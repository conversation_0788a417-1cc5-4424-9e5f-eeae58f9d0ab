/**
 * 🚀 Enhanced Dashboard Routes with Elasticsearch Integration
 * 
 * Phase 3: Elasticsearch Optimization - Dashboard Integration
 * Provides Elasticsearch-powered endpoints with automatic fallback to existing APIs
 * Ensures seamless integration with Production and Arrets dashboards
 */

import express from 'express';
import elasticsearchProductionService from '../services/ElasticsearchProductionService.js';
import elasticsearchStopsService from '../services/ElasticsearchStopsService.js';
import auth from '../middleware/auth.js';
import rateLimiter from '../middleware/rateLimiter.js';
import { executeQuery } from '../utils/dbUtils.js';
import { db } from '../server.js';

const router = express.Router();

// Apply authentication and rate limiting
router.use(auth);
router.use(rateLimiter.analyticsLimiter());

/**
 * 🏭 PRODUCTION DASHBOARD INTEGRATION
 * Enhanced endpoints that match existing API structure with Elasticsearch optimization
 */

/**
 * GET /api/enhanced/production/dashboard
 * Production Dashboard data with Elasticsearch primary + fallback
 * Matches existing /api/DailyTableMould structure
 */
router.get('/production/dashboard', async (req, res) => {
  try {
    const { date, dateRangeType, model, machine } = req.query;
    
    // Try Elasticsearch first
    try {
      const elasticsearchData = await getProductionDataFromElasticsearch(req.query);
      
      if (elasticsearchData && elasticsearchData.length > 0) {
        console.log('✅ Using Elasticsearch for production dashboard');
        return res.json({
          success: true,
          data: elasticsearchData,
          source: 'elasticsearch',
          response_time_ms: elasticsearchData.query_time_ms || 0
        });
      }
    } catch (elasticsearchError) {
      console.warn('⚠️ Elasticsearch failed, falling back to MySQL:', elasticsearchError.message);
    }
    
    // Fallback to existing MySQL query
    console.log('📊 Using MySQL fallback for production dashboard');
    const fallbackData = await getProductionDataFromMySQL(req.query);
    
    res.json({
      success: true,
      data: fallbackData,
      source: 'mysql_fallback',
      response_time_ms: 0
    });
    
  } catch (error) {
    console.error('❌ Production dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch production dashboard data',
      error: error.message
    });
  }
});

/**
 * GET /api/enhanced/production/chart
 * Production chart data with Elasticsearch optimization
 * Matches existing /api/chart-production structure
 */
router.get('/production/chart', async (req, res) => {
  try {
    // Try Elasticsearch first
    try {
      const chartData = await getProductionChartFromElasticsearch(req.query);
      
      if (chartData && chartData.length > 0) {
        console.log('✅ Using Elasticsearch for production chart');
        return res.json({
          success: true,
          data: chartData,
          source: 'elasticsearch'
        });
      }
    } catch (elasticsearchError) {
      console.warn('⚠️ Elasticsearch failed, falling back to MySQL:', elasticsearchError.message);
    }
    
    // Fallback to existing MySQL query
    console.log('📊 Using MySQL fallback for production chart');
    const fallbackData = await getProductionChartFromMySQL();
    
    res.json({
      success: true,
      data: fallbackData,
      source: 'mysql_fallback'
    });
    
  } catch (error) {
    console.error('❌ Production chart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch production chart data',
      error: error.message
    });
  }
});

/**
 * GET /api/enhanced/production/sidecards
 * Production sidecards with Elasticsearch optimization
 * Matches existing /api/sidecards-prod structure
 */
router.get('/production/sidecards', async (req, res) => {
  try {
    // Try Elasticsearch first
    try {
      const sidecardsData = await getProductionSidecardsFromElasticsearch(req.query);
      
      if (sidecardsData) {
        console.log('✅ Using Elasticsearch for production sidecards');
        return res.json({
          success: true,
          data: sidecardsData,
          source: 'elasticsearch'
        });
      }
    } catch (elasticsearchError) {
      console.warn('⚠️ Elasticsearch failed, falling back to MySQL:', elasticsearchError.message);
    }
    
    // Fallback to existing MySQL query
    console.log('📊 Using MySQL fallback for production sidecards');
    const fallbackData = await getProductionSidecardsFromMySQL(req.query);
    
    res.json({
      success: true,
      data: fallbackData,
      source: 'mysql_fallback'
    });
    
  } catch (error) {
    console.error('❌ Production sidecards error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch production sidecards data',
      error: error.message
    });
  }
});

/**
 * 🛑 ARRETS DASHBOARD INTEGRATION
 * Enhanced endpoints for stops/downtime analysis with Elasticsearch optimization
 */

/**
 * GET /api/enhanced/arrets/dashboard
 * Arrets Dashboard data with Elasticsearch primary + fallback
 */
router.get('/arrets/dashboard', async (req, res) => {
  try {
    const { machine_ids, start_date, end_date, category } = req.query;
    
    // Try Elasticsearch first
    try {
      const elasticsearchData = await getArretsDashboardFromElasticsearch(req.query);
      
      if (elasticsearchData) {
        console.log('✅ Using Elasticsearch for arrets dashboard');
        return res.json({
          success: true,
          data: elasticsearchData,
          source: 'elasticsearch',
          response_time_ms: elasticsearchData.query_time_ms || 0
        });
      }
    } catch (elasticsearchError) {
      console.warn('⚠️ Elasticsearch failed, falling back to MySQL:', elasticsearchError.message);
    }
    
    // Fallback to existing MySQL query
    console.log('📊 Using MySQL fallback for arrets dashboard');
    const fallbackData = await getArretsDashboardFromMySQL(req.query);
    
    res.json({
      success: true,
      data: fallbackData,
      source: 'mysql_fallback'
    });
    
  } catch (error) {
    console.error('❌ Arrets dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch arrets dashboard data',
      error: error.message
    });
  }
});

/**
 * GET /api/enhanced/arrets/analytics
 * Advanced stops analytics with Elasticsearch optimization
 */
router.get('/arrets/analytics', async (req, res) => {
  try {
    // Try Elasticsearch first
    try {
      const analyticsData = await getArretsAnalyticsFromElasticsearch(req.query);
      
      if (analyticsData) {
        console.log('✅ Using Elasticsearch for arrets analytics');
        return res.json({
          success: true,
          data: analyticsData,
          source: 'elasticsearch'
        });
      }
    } catch (elasticsearchError) {
      console.warn('⚠️ Elasticsearch failed, falling back to MySQL:', elasticsearchError.message);
    }
    
    // Fallback to existing MySQL query
    console.log('📊 Using MySQL fallback for arrets analytics');
    const fallbackData = await getArretsAnalyticsFromMySQL(req.query);
    
    res.json({
      success: true,
      data: fallbackData,
      source: 'mysql_fallback'
    });
    
  } catch (error) {
    console.error('❌ Arrets analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch arrets analytics data',
      error: error.message
    });
  }
});

/**
 * 🔧 HELPER FUNCTIONS - ELASTICSEARCH DATA RETRIEVAL
 */

/**
 * Get production data from Elasticsearch
 */
async function getProductionDataFromElasticsearch(queryParams) {
  const { date, dateRangeType, model, machine } = queryParams;
  
  const filters = {};
  
  // Date filtering
  if (date) {
    filters.dateRange = {
      start: date,
      end: date
    };
  }
  
  // Machine filtering
  if (machine) {
    filters.machineName = machine;
  }
  
  if (model) {
    filters.machineModel = model;
  }
  
  const result = await elasticsearchProductionService.getProductionAnalytics(filters, { size: 1000 });
  
  // Transform to match existing API structure
  return result.records.map(record => ({
    id: record.id,
    Machine_Name: record.machineName,
    Date_Insert_Day: record.date,
    Good_QTY_Day: record.production?.good || 0,
    Rejects_QTY_Day: record.production?.rejects || 0,
    TRS: record.performance?.oee || 0,
    Disponibilite: record.performance?.availability || 0,
    Rendement: record.performance?.performance || 0,
    Qualite: record.performance?.quality || 0,
    Shift: record.shift,
    Regleur_Prenom: record.operator
  }));
}

/**
 * Get production chart data from Elasticsearch
 */
async function getProductionChartFromElasticsearch(queryParams) {
  const dashboardData = await elasticsearchProductionService.getDashboardData([], {
    start: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  
  // Transform to match existing chart structure
  return dashboardData.dailyTrends.map(trend => ({
    Date_Insert_Day: trend.date,
    Total_Good_Qty_Day: trend.production,
    Total_Rejects_Qty_Day: trend.rejects
  }));
}

/**
 * Get production sidecards from Elasticsearch
 */
async function getProductionSidecardsFromElasticsearch(queryParams) {
  const dashboardData = await elasticsearchProductionService.getDashboardData([], {});
  
  return {
    goodqty: dashboardData.totalProduction || 0,
    rejetqty: dashboardData.totalRejects || 0
  };
}

/**
 * Get arrets dashboard data from Elasticsearch
 */
async function getArretsDashboardFromElasticsearch(queryParams) {
  const { machine_ids, start_date, end_date } = queryParams;
  
  const machineIds = machine_ids ? machine_ids.split(',').map(id => parseInt(id)) : [];
  const dateRange = {};
  
  if (start_date) dateRange.start = start_date;
  if (end_date) dateRange.end = end_date;
  
  return await elasticsearchStopsService.getDowntimeDashboard(machineIds, dateRange);
}

/**
 * Get arrets analytics from Elasticsearch
 */
async function getArretsAnalyticsFromElasticsearch(queryParams) {
  const filters = {};
  
  if (queryParams.machine_ids) {
    filters.machineIds = queryParams.machine_ids.split(',').map(id => parseInt(id));
  }
  
  if (queryParams.category) {
    filters.category = queryParams.category;
  }
  
  if (queryParams.start_date || queryParams.end_date) {
    filters.dateRange = {};
    if (queryParams.start_date) filters.dateRange.start = queryParams.start_date;
    if (queryParams.end_date) filters.dateRange.end = queryParams.end_date;
  }
  
  return await elasticsearchStopsService.getStopsAnalytics(filters, { size: 1000 });
}

/**
 * 🔧 HELPER FUNCTIONS - MYSQL FALLBACK DATA RETRIEVAL
 */

/**
 * Get production data from MySQL (fallback)
 */
async function getProductionDataFromMySQL(queryParams) {
  const { date, dateRangeType, model, machine } = queryParams;

  let query = 'SELECT * FROM machine_daily_table_mould WHERE 1=1';
  const params = [];

  // Date filtering
  if (date) {
    if (dateRangeType === 'week') {
      query += ' AND STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") >= DATE_SUB(?, INTERVAL 1 WEEK)';
      params.push(date);
    } else if (dateRangeType === 'month') {
      query += ' AND STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") >= DATE_SUB(?, INTERVAL 1 MONTH)';
      params.push(date);
    } else {
      query += ' AND STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") = STR_TO_DATE(?, "%Y-%m-%d")';
      params.push(date);
    }
  }

  // Machine filtering
  if (machine) {
    query += ' AND Machine_Name = ?';
    params.push(machine);
  }

  if (model) {
    query += ' AND Machine_Name LIKE ?';
    params.push(`%${model}%`);
  }

  query += ' ORDER BY STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") DESC';

  const [rows] = await db.execute(query, params);
  return rows;
}

/**
 * Get production chart data from MySQL (fallback)
 */
async function getProductionChartFromMySQL() {
  const query = `
    SELECT
      DATE_FORMAT(
          STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'),
          '%Y-%m-%d'
      ) AS Date_Insert_Day,
      SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS Total_Good_Qty_Day,
      SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS Total_Rejects_Qty_Day
    FROM machine_daily_table_mould
    WHERE
        STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
        DATE_SUB(CURDATE(), INTERVAL 5 DAY)
    GROUP BY Date_Insert_Day
    ORDER BY Date_Insert_Day ASC
  `;

  const [rows] = await db.execute(query);
  return rows;
}

/**
 * Get production sidecards from MySQL (fallback)
 */
async function getProductionSidecardsFromMySQL(queryParams) {
  const { date, dateRangeType, model, machine } = queryParams;

  let query = 'SELECT SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS goodqty FROM machine_daily_table_mould WHERE 1=1';
  const params = [];

  // Apply same filtering logic as production data
  if (date) {
    if (dateRangeType === 'week') {
      query += ' AND STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") >= DATE_SUB(?, INTERVAL 1 WEEK)';
      params.push(date);
    } else if (dateRangeType === 'month') {
      query += ' AND STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") >= DATE_SUB(?, INTERVAL 1 MONTH)';
      params.push(date);
    } else {
      query += ' AND STR_TO_DATE(Date_Insert_Day, "%d/%m/%Y") = STR_TO_DATE(?, "%Y-%m-%d")';
      params.push(date);
    }
  }

  if (machine) {
    query += ' AND Machine_Name = ?';
    params.push(machine);
  }

  if (model) {
    query += ' AND Machine_Name LIKE ?';
    params.push(`%${model}%`);
  }

  const [goodRows] = await db.execute(query, params);

  // Get rejects with same filters
  const rejectQuery = query.replace('Good_QTY_Day', 'Rejects_QTY_Day').replace('goodqty', 'rejetqty');
  const [rejectRows] = await db.execute(rejectQuery, params);

  return {
    goodqty: goodRows[0]?.goodqty || 0,
    rejetqty: rejectRows[0]?.rejetqty || 0
  };
}

/**
 * Get arrets dashboard data from MySQL (fallback)
 */
async function getArretsDashboardFromMySQL(queryParams) {
  const { machine_ids, start_date, end_date, category } = queryParams;

  let query = 'SELECT * FROM machine_stop_table_mould WHERE 1=1';
  const params = [];

  // Machine filtering
  if (machine_ids) {
    const machineIdArray = machine_ids.split(',').map(id => parseInt(id));
    query += ` AND machine_id IN (${machineIdArray.map(() => '?').join(',')})`;
    params.push(...machineIdArray);
  }

  // Date filtering
  if (start_date) {
    query += ' AND DATE(date_insert) >= ?';
    params.push(start_date);
  }

  if (end_date) {
    query += ' AND DATE(date_insert) <= ?';
    params.push(end_date);
  }

  // Category filtering
  if (category) {
    query += ' AND stop_category = ?';
    params.push(category);
  }

  query += ' ORDER BY date_insert DESC';

  const [rows] = await db.execute(query, params);

  // Transform to match Elasticsearch structure
  return {
    stops: rows,
    totalStops: rows.length,
    totalDowntime: rows.reduce((sum, stop) => {
      const start = new Date(stop.start_time);
      const end = new Date(stop.end_time || new Date());
      return sum + (end - start) / (1000 * 60); // minutes
    }, 0),
    categories: [...new Set(rows.map(stop => stop.stop_category))],
    machines: [...new Set(rows.map(stop => ({ id: stop.machine_id, name: stop.machine_name })))]
  };
}

/**
 * Get arrets analytics from MySQL (fallback)
 */
async function getArretsAnalyticsFromMySQL(queryParams) {
  const { machine_ids, start_date, end_date, category } = queryParams;

  let query = `
    SELECT
      stop_category,
      COUNT(*) as stop_count,
      AVG(TIMESTAMPDIFF(MINUTE, start_time, COALESCE(end_time, NOW()))) as avg_duration,
      SUM(TIMESTAMPDIFF(MINUTE, start_time, COALESCE(end_time, NOW()))) as total_duration,
      machine_name
    FROM machine_stop_table_mould
    WHERE 1=1
  `;
  const params = [];

  // Apply same filtering as dashboard
  if (machine_ids) {
    const machineIdArray = machine_ids.split(',').map(id => parseInt(id));
    query += ` AND machine_id IN (${machineIdArray.map(() => '?').join(',')})`;
    params.push(...machineIdArray);
  }

  if (start_date) {
    query += ' AND DATE(date_insert) >= ?';
    params.push(start_date);
  }

  if (end_date) {
    query += ' AND DATE(date_insert) <= ?';
    params.push(end_date);
  }

  if (category) {
    query += ' AND stop_category = ?';
    params.push(category);
  }

  query += ' GROUP BY stop_category, machine_name ORDER BY total_duration DESC';

  const [rows] = await db.execute(query, params);

  return {
    analytics: rows,
    total: rows.length,
    query_time_ms: 0
  };
}

export default router;
