import nodemailer from 'nodemailer';
import dayjs from 'dayjs';
import 'dayjs/locale/fr.js';
import dotenv from 'dotenv';
import emailSettingsIntegration from './EmailSettingsIntegration.js';

// Load environment variables
dotenv.config();

dayjs.locale('fr');

/**
 * EmailNotificationService - SMTP email integration for LOCQL notification system
 * Integrates with existing SSE notification infrastructure to provide email delivery
 * capabilities for critical alerts, machine notifications, and automated reports.
 */
class EmailNotificationService {
  constructor() {
    this.transporter = null;
    this.isConfigured = false;
    this.initializationPromise = null;
    this.deliveryStats = {
      totalSent: 0,
      totalFailed: 0,
      lastDeliveryTime: null,
      averageDeliveryTime: 0
    };

    this.config = {
      host: process.env.SMTP_HOST || 'sicaf.com.tn',
      port: parseInt(process.env.SMTP_PORT) || 25,
      secure: process.env.SMTP_SECURE === 'true', // false for port 25 with STARTTLS
      requireTLS: process.env.SMTP_USE_STARTTLS === 'true', // Enable STARTTLS
      auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || '5vx7*Ok48'
      },
      from: {
        name: process.env.SMTP_FROM_NAME || 'LOCQL Performance System',
        email: process.env.SMTP_FROM_EMAIL || '<EMAIL>'
      }
    };

    // Initialize asynchronously but don't wait for it in constructor
    this.initializationPromise = this.initializeTransporter();
  }

  /**
   * Initialize nodemailer transporter with SMTP configuration
   */
  async initializeTransporter() {
    try {
      console.log('📧 Initializing SMTP transporter...');
      
      this.transporter = nodemailer.createTransport({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        requireTLS: this.config.requireTLS,
        auth: this.config.auth,
        // TLS configuration for self-signed certificates
        tls: {
          rejectUnauthorized: false, // Accept self-signed certificates
          ciphers: 'SSLv3'
        },
        // Authentication method configuration
        authMethod: 'PLAIN', // Try PLAIN authentication first
        // Additional configuration for better reliability
        connectionTimeout: 60000, // 60 seconds
        greetingTimeout: 30000,   // 30 seconds
        socketTimeout: 60000,     // 60 seconds
        logger: process.env.NODE_ENV === 'development',
        debug: process.env.NODE_ENV === 'development'
      });

      // Verify SMTP connection
      await this.verifyConnection();
      this.isConfigured = true;
      
      console.log('✅ SMTP transporter initialized successfully');
      console.log(`📧 Email service configured: ${this.config.from.email}`);
      
    } catch (error) {
      console.error('❌ Failed to initialize SMTP transporter:', error);
      this.isConfigured = false;
      throw error;
    }
  }

  /**
   * Verify SMTP connection
   */
  async verifyConnection() {
    if (!this.transporter) {
      throw new Error('SMTP transporter not initialized');
    }

    try {
      console.log('🔍 Verifying SMTP connection...');
      await this.transporter.verify();
      console.log('✅ SMTP connection verified successfully');
      return true;
    } catch (error) {
      console.error('❌ SMTP connection verification failed:', error);
      throw error;
    }
  }

  /**
   * Send machine alert email notification with settings integration
   * @param {Object} alertData - Machine alert data
   * @param {Array} recipients - Array of email addresses
   * @param {number} userId - User ID for settings (default: 1)
   * @returns {Promise<Object>} - Delivery result
   */
  async sendMachineAlert(alertData, recipients, userId = 1) {
    const startTime = Date.now();

    try {
      // Ensure initialization is complete
      if (this.initializationPromise) {
        await this.initializationPromise;
      }

      if (!this.isConfigured) {
        throw new Error('SMTP service not configured');
      }

      // Convert alert data to notification format for settings check
      const notification = {
        title: `ALERTE MACHINE - ${alertData.machine_name || 'Machine Non Spécifiée'}`,
        message: alertData.message || 'Machine alert triggered',
        category: 'machine_alert',
        priority: alertData.priority || 'medium',
        machine_id: alertData.machine_id
      };

      // Check if email should be sent based on user settings
      const settingsCheck = await emailSettingsIntegration.shouldSendEmail(notification, userId);
      if (!settingsCheck.shouldSend) {
        console.log(`🚨 Machine alert email not sent: ${settingsCheck.reason}`);
        return { success: false, reason: settingsCheck.reason };
      }

      console.log(`🚨 Sending machine alert email to ${recipients.length} recipients with settings applied`);

      // Get email configuration from settings
      const emailConfig = await emailSettingsIntegration.getEmailConfiguration(notification, userId);

      // Generate base content
      const baseSubject = `🚨 ALERTE MACHINE - ${alertData.machine_name || 'Machine Non Spécifiée'}`;
      const baseHtml = this.generateMachineAlertHTML(alertData);
      const baseText = this.generateMachineAlertText(alertData);

      // Apply settings to content
      const enhancedContent = await emailSettingsIntegration.applySettingsToEmailContent({
        subject: baseSubject,
        html: baseHtml,
        text: baseText
      }, notification, userId);

      const mailOptions = {
        from: `"${this.config.from.name}" <${this.config.from.email}>`,
        to: recipients.join(', '),
        subject: enhancedContent.subject,
        text: enhancedContent.text,
        html: enhancedContent.html,
        priority: alertData.priority === 'critical' ? 'high' : 'normal',
        headers: {
          ...enhancedContent.headers,
          'X-Machine-ID': alertData.machine_id?.toString() || 'unknown',
          'X-Alert-Priority': alertData.priority || 'medium'
        }
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      // Update delivery statistics
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryStats(true, deliveryTime);
      
      console.log(`✅ Machine alert email sent successfully in ${deliveryTime}ms`);
      console.log(`📧 Message ID: ${result.messageId}`);
      
      return {
        success: true,
        messageId: result.messageId,
        recipients: recipients.length,
        deliveryTime,
        type: 'machine_alert'
      };
      
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryStats(false, deliveryTime);
      
      console.error('❌ Failed to send machine alert email:', error);
      throw error;
    }
  }

  /**
   * Send shift report email notification
   * @param {Object} reportData - Shift report data
   * @param {Array} recipients - Array of email addresses
   * @returns {Promise<Object>} - Delivery result
   */
  async sendShiftReport(reportData, recipients) {
    const startTime = Date.now();
    
    try {
      console.log(`📊 Sending shift report email to ${recipients.length} recipients`);
      
      const subject = `📊 Rapport de Quart - ${reportData.machine?.name || 'Machine'} - ${dayjs(reportData.date).format('DD/MM/YYYY')}`;
      const htmlContent = this.generateShiftReportHTML(reportData);
      const textContent = this.generateShiftReportText(reportData);

      const mailOptions = {
        from: `"${this.config.from.name}" <${this.config.from.email}>`,
        to: recipients.join(', '),
        subject: subject,
        text: textContent,
        html: htmlContent,
        headers: {
          'X-Notification-Type': 'shift_report',
          'X-Machine-ID': reportData.machine?.id?.toString() || 'unknown',
          'X-Report-Date': reportData.date || new Date().toISOString()
        }
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      // Update delivery statistics
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryStats(true, deliveryTime);
      
      console.log(`✅ Shift report email sent successfully in ${deliveryTime}ms`);
      console.log(`📧 Message ID: ${result.messageId}`);
      
      return {
        success: true,
        messageId: result.messageId,
        recipients: recipients.length,
        deliveryTime,
        type: 'shift_report'
      };
      
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryStats(false, deliveryTime);
      
      console.error('❌ Failed to send shift report email:', error);
      throw error;
    }
  }

  /**
   * Send general notification email with settings integration
   * @param {Object} notification - Notification data
   * @param {Array} recipients - Array of email addresses
   * @param {number} userId - User ID for settings (default: 1)
   * @returns {Promise<Object>} - Delivery result
   */
  async sendNotificationEmail(notification, recipients, userId = 1) {
    const startTime = Date.now();

    try {
      // Check if email should be sent based on user settings
      const settingsCheck = await emailSettingsIntegration.shouldSendEmail(notification, userId);
      if (!settingsCheck.shouldSend) {
        console.log(`📧 Email not sent: ${settingsCheck.reason}`);
        return { success: false, reason: settingsCheck.reason };
      }

      console.log(`📧 Sending notification email to ${recipients.length} recipients with settings applied`);

      // Get email configuration from settings
      const emailConfig = await emailSettingsIntegration.getEmailConfiguration(notification, userId);

      // Generate base content
      const baseSubject = `🔔 ${notification.title}`;
      const baseHtml = this.generateNotificationHTML(notification);
      const baseText = this.generateNotificationText(notification);

      // Apply settings to content
      const enhancedContent = await emailSettingsIntegration.applySettingsToEmailContent({
        subject: baseSubject,
        html: baseHtml,
        text: baseText
      }, notification, userId);

      const mailOptions = {
        from: `"${this.config.from.name}" <${this.config.from.email}>`,
        to: recipients.join(', '),
        subject: enhancedContent.subject,
        text: enhancedContent.text,
        html: enhancedContent.html,
        priority: notification.priority === 'critical' ? 'high' : 'normal',
        headers: {
          ...enhancedContent.headers,
          'X-Notification-ID': notification.id?.toString() || 'unknown'
        }
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      // Update delivery statistics
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryStats(true, deliveryTime);
      
      console.log(`✅ Notification email sent successfully in ${deliveryTime}ms`);
      console.log(`📧 Message ID: ${result.messageId}`);
      
      return {
        success: true,
        messageId: result.messageId,
        recipients: recipients.length,
        deliveryTime,
        type: 'notification'
      };
      
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      this.updateDeliveryStats(false, deliveryTime);
      
      console.error('❌ Failed to send notification email:', error);
      throw error;
    }
  }

  /**
   * Generate HTML content for machine alert emails
   * @param {Object} alertData - Alert data
   * @returns {string} - HTML content
   */
  generateMachineAlertHTML(alertData) {
    const timestamp = dayjs().format('dddd DD MMMM YYYY à HH:mm');
    const priorityColor = this.getPriorityColor(alertData.priority);
    const priorityIcon = this.getPriorityIcon(alertData.priority);
    
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Alerte Machine - LOCQL</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">${priorityIcon} ALERTE MACHINE</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">Système de Performance LOCQL</p>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0; border-top: none;">
        <div style="background: ${priorityColor}; color: white; padding: 10px; border-radius: 4px; margin-bottom: 20px;">
          <strong>Priorité: ${(alertData.priority || 'medium').toUpperCase()}</strong>
        </div>
        
        <h2 style="color: #1e40af; margin-top: 0;">${alertData.title || 'Alerte Machine'}</h2>
        <p style="font-size: 16px; margin-bottom: 20px;">${alertData.message || 'Aucun message spécifié'}</p>
        
        <div style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #3b82f6;">
          <h3 style="margin-top: 0; color: #1e40af;">Détails de l'Alerte</h3>
          <p><strong>Machine:</strong> ${alertData.machine_name || 'Non spécifiée'}</p>
          <p><strong>Date et Heure:</strong> ${timestamp}</p>
          <p><strong>Source:</strong> ${alertData.source || 'Système de monitoring'}</p>
          ${alertData.machine_id ? `<p><strong>ID Machine:</strong> ${alertData.machine_id}</p>` : ''}
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #fef3c7; border-radius: 4px; border-left: 4px solid #f59e0b;">
          <p style="margin: 0;"><strong>⚠️ Action Requise:</strong> Veuillez vérifier l'état de la machine et prendre les mesures appropriées.</p>
        </div>
      </div>
      
      <div style="background: #1f2937; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center;">
        <p style="margin: 0; font-size: 12px; opacity: 0.8;">
          Cet email a été envoyé automatiquement par le système LOCQL Performance.<br>
          Ne pas répondre à cet email.
        </p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate text content for machine alert emails
   * @param {Object} alertData - Alert data
   * @returns {string} - Text content
   */
  generateMachineAlertText(alertData) {
    const timestamp = dayjs().format('dddd DD MMMM YYYY à HH:mm');

    return `
🚨 ALERTE MACHINE - LOCQL Performance System

PRIORITÉ: ${(alertData.priority || 'medium').toUpperCase()}

${alertData.title || 'Alerte Machine'}

${alertData.message || 'Aucun message spécifié'}

DÉTAILS:
- Machine: ${alertData.machine_name || 'Non spécifiée'}
- Date et Heure: ${timestamp}
- Source: ${alertData.source || 'Système de monitoring'}
${alertData.machine_id ? `- ID Machine: ${alertData.machine_id}` : ''}

⚠️ ACTION REQUISE: Veuillez vérifier l'état de la machine et prendre les mesures appropriées.

---
Cet email a été envoyé automatiquement par le système LOCQL Performance.
Ne pas répondre à cet email.
    `.trim();
  }

  /**
   * Generate HTML content for shift report emails
   * @param {Object} reportData - Report data
   * @returns {string} - HTML content
   */
  generateShiftReportHTML(reportData) {
    const timestamp = dayjs(reportData.date).format('dddd DD MMMM YYYY');
    const generatedTime = dayjs().format('dddd DD MMMM YYYY à HH:mm');

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport de Quart - LOCQL</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">📊 RAPPORT DE QUART</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">Système de Performance LOCQL</p>
      </div>

      <div style="background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0; border-top: none;">
        <h2 style="color: #059669; margin-top: 0;">${reportData.machine?.name || 'Machine'} - ${timestamp}</h2>

        <div style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #10b981; margin-bottom: 20px;">
          <h3 style="margin-top: 0; color: #059669;">Informations Générales</h3>
          <p><strong>Machine:</strong> ${reportData.machine?.name || 'Non spécifiée'}</p>
          <p><strong>Équipe:</strong> ${reportData.shift || 'Non spécifiée'}</p>
          <p><strong>Date:</strong> ${timestamp}</p>
          <p><strong>Généré le:</strong> ${generatedTime}</p>
        </div>

        ${reportData.production ? `
        <div style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #3b82f6; margin-bottom: 20px;">
          <h3 style="margin-top: 0; color: #1e40af;">Production</h3>
          <p><strong>Production Totale:</strong> ${reportData.production.total || 0} unités</p>
          <p><strong>Taux de Performance:</strong> ${reportData.production.performance || 0}%</p>
          <p><strong>Qualité:</strong> ${reportData.production.quality || 0}%</p>
        </div>
        ` : ''}

        <div style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #f59e0b;">
          <h3 style="margin-top: 0; color: #d97706;">Résumé</h3>
          <p>Le rapport de quart complet est disponible dans l'interface LOCQL pour plus de détails et d'analyses.</p>
        </div>
      </div>

      <div style="background: #1f2937; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center;">
        <p style="margin: 0; font-size: 12px; opacity: 0.8;">
          Cet email a été envoyé automatiquement par le système LOCQL Performance.<br>
          Ne pas répondre à cet email.
        </p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate text content for shift report emails
   * @param {Object} reportData - Report data
   * @returns {string} - Text content
   */
  generateShiftReportText(reportData) {
    const timestamp = dayjs(reportData.date).format('dddd DD MMMM YYYY');
    const generatedTime = dayjs().format('dddd DD MMMM YYYY à HH:mm');

    return `
📊 RAPPORT DE QUART - LOCQL Performance System

${reportData.machine?.name || 'Machine'} - ${timestamp}

INFORMATIONS GÉNÉRALES:
- Machine: ${reportData.machine?.name || 'Non spécifiée'}
- Équipe: ${reportData.shift || 'Non spécifiée'}
- Date: ${timestamp}
- Généré le: ${generatedTime}

${reportData.production ? `
PRODUCTION:
- Production Totale: ${reportData.production.total || 0} unités
- Taux de Performance: ${reportData.production.performance || 0}%
- Qualité: ${reportData.production.quality || 0}%
` : ''}

RÉSUMÉ:
Le rapport de quart complet est disponible dans l'interface LOCQL pour plus de détails et d'analyses.

---
Cet email a été envoyé automatiquement par le système LOCQL Performance.
Ne pas répondre à cet email.
    `.trim();
  }

  /**
   * Generate HTML content for general notification emails
   * @param {Object} notification - Notification data
   * @returns {string} - HTML content
   */
  generateNotificationHTML(notification) {
    const timestamp = dayjs().format('dddd DD MMMM YYYY à HH:mm');
    const priorityColor = this.getPriorityColor(notification.priority);
    const categoryIcon = this.getCategoryIcon(notification.category);

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Notification - LOCQL</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">${categoryIcon} NOTIFICATION</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">Système de Performance LOCQL</p>
      </div>

      <div style="background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0; border-top: none;">
        <div style="background: ${priorityColor}; color: white; padding: 10px; border-radius: 4px; margin-bottom: 20px;">
          <strong>Priorité: ${(notification.priority || 'medium').toUpperCase()}</strong>
        </div>

        <h2 style="color: #7c3aed; margin-top: 0;">${notification.title || 'Notification'}</h2>
        <p style="font-size: 16px; margin-bottom: 20px;">${notification.message || 'Aucun message spécifié'}</p>

        <div style="background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #a855f7;">
          <h3 style="margin-top: 0; color: #7c3aed;">Détails</h3>
          <p><strong>Catégorie:</strong> ${notification.category || 'Général'}</p>
          <p><strong>Date et Heure:</strong> ${timestamp}</p>
          <p><strong>Source:</strong> ${notification.source || 'Système'}</p>
          ${notification.machine_id ? `<p><strong>Machine:</strong> ${notification.machine_id}</p>` : ''}
        </div>
      </div>

      <div style="background: #1f2937; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center;">
        <p style="margin: 0; font-size: 12px; opacity: 0.8;">
          Cet email a été envoyé automatiquement par le système LOCQL Performance.<br>
          Ne pas répondre à cet email.
        </p>
      </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate text content for general notification emails
   * @param {Object} notification - Notification data
   * @returns {string} - Text content
   */
  generateNotificationText(notification) {
    const timestamp = dayjs().format('dddd DD MMMM YYYY à HH:mm');

    return `
🔔 NOTIFICATION - LOCQL Performance System

PRIORITÉ: ${(notification.priority || 'medium').toUpperCase()}

${notification.title || 'Notification'}

${notification.message || 'Aucun message spécifié'}

DÉTAILS:
- Catégorie: ${notification.category || 'Général'}
- Date et Heure: ${timestamp}
- Source: ${notification.source || 'Système'}
${notification.machine_id ? `- Machine: ${notification.machine_id}` : ''}

---
Cet email a été envoyé automatiquement par le système LOCQL Performance.
Ne pas répondre à cet email.
    `.trim();
  }

  /**
   * Get priority color for email styling
   * @param {string} priority - Priority level
   * @returns {string} - Color code
   */
  getPriorityColor(priority) {
    const colors = {
      low: '#10b981',      // Green
      medium: '#f59e0b',   // Yellow
      high: '#f97316',     // Orange
      critical: '#ef4444'  // Red
    };
    return colors[priority] || colors.medium;
  }

  /**
   * Get priority icon for email content
   * @param {string} priority - Priority level
   * @returns {string} - Icon emoji
   */
  getPriorityIcon(priority) {
    const icons = {
      low: '🟢',
      medium: '🟡',
      high: '🟠',
      critical: '🔴'
    };
    return icons[priority] || icons.medium;
  }

  /**
   * Get category icon for email content
   * @param {string} category - Notification category
   * @returns {string} - Icon emoji
   */
  getCategoryIcon(category) {
    const icons = {
      alert: '⚠️',
      maintenance: '🔧',
      update: '🔄',
      info: 'ℹ️',
      machine_alert: '🚨',
      production: '📊',
      quality: '✅'
    };
    return icons[category] || icons.info;
  }

  /**
   * Update delivery statistics
   * @param {boolean} success - Whether delivery was successful
   * @param {number} deliveryTime - Delivery time in milliseconds
   */
  updateDeliveryStats(success, deliveryTime) {
    if (success) {
      this.deliveryStats.totalSent++;
    } else {
      this.deliveryStats.totalFailed++;
    }

    this.deliveryStats.lastDeliveryTime = new Date();

    // Update average delivery time (simple moving average)
    const totalDeliveries = this.deliveryStats.totalSent + this.deliveryStats.totalFailed;
    this.deliveryStats.averageDeliveryTime =
      (this.deliveryStats.averageDeliveryTime * (totalDeliveries - 1) + deliveryTime) / totalDeliveries;
  }

  /**
   * Get delivery statistics
   * @returns {Object} - Delivery statistics
   */
  getDeliveryStats() {
    const totalDeliveries = this.deliveryStats.totalSent + this.deliveryStats.totalFailed;
    const successRate = totalDeliveries > 0 ? (this.deliveryStats.totalSent / totalDeliveries) * 100 : 0;

    return {
      ...this.deliveryStats,
      totalDeliveries,
      successRate: Math.round(successRate * 100) / 100,
      isConfigured: this.isConfigured
    };
  }

  /**
   * Test email configuration by sending a test email
   * @param {string} testEmail - Test email address
   * @returns {Promise<Object>} - Test result
   */
  async sendTestEmail(testEmail) {
    try {
      // Ensure initialization is complete
      if (this.initializationPromise) {
        await this.initializationPromise;
      }

      if (!this.isConfigured) {
        throw new Error('SMTP service not configured');
      }

      console.log(`🧪 Sending test email to: ${testEmail}`);

      const testNotification = {
        title: 'Test de Configuration Email LOCQL',
        message: 'Ceci est un email de test pour vérifier la configuration SMTP du système LOCQL Performance.',
        category: 'info',
        priority: 'medium',
        source: 'email_service_test'
      };

      const result = await this.sendNotificationEmail(testNotification, [testEmail]);

      console.log('✅ Test email sent successfully');
      return {
        success: true,
        message: 'Test email sent successfully',
        ...result
      };

    } catch (error) {
      console.error('❌ Test email failed:', error);
      return {
        success: false,
        message: 'Test email failed',
        error: error.message
      };
    }
  }

  /**
   * Health check for email service
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    try {
      // Wait for initialization to complete if it's still in progress
      if (this.initializationPromise) {
        try {
          await this.initializationPromise;
        } catch (initError) {
          return {
            status: 'unhealthy',
            message: 'SMTP initialization failed',
            error: initError.message,
            isConfigured: false
          };
        }
      }

      if (!this.isConfigured) {
        return {
          status: 'unhealthy',
          message: 'SMTP transporter not configured',
          isConfigured: false
        };
      }

      await this.verifyConnection();

      return {
        status: 'healthy',
        message: 'SMTP service is operational',
        isConfigured: true,
        config: {
          host: this.config.host,
          port: this.config.port,
          secure: this.config.secure,
          requireTLS: this.config.requireTLS,
          from: this.config.from.email
        },
        stats: this.getDeliveryStats()
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'SMTP connection failed',
        error: error.message,
        isConfigured: this.isConfigured
      };
    }
  }

  /**
   * Alias for healthCheck method for compatibility
   * @returns {Promise<Object>} - Health status
   */
  async checkHealth() {
    const healthStatus = await this.healthCheck();
    return {
      healthy: healthStatus.status === 'healthy',
      stats: {
        totalEmailsSent: this.deliveryStats.totalSent,
        averageDeliveryTime: this.deliveryStats.averageDeliveryTime
      },
      ...healthStatus
    };
  }
}

// Create and export singleton instance
const emailNotificationService = new EmailNotificationService();
export default emailNotificationService;
