import express from "express";
import db from "../db.js";
import auth from "../middleware/auth.js";
import bypassPermission from "../middleware/bypassPermission.js";
import PDFDocument from "pdfkit";
import fs from "fs";
import path from "path";
import dayjs from "dayjs";
import { createCanvas } from "canvas";
import Chart from "chart.js/auto";

/**
 * Execute a database query with timeout protection, retry mechanism, and error handling
 * @param {string} queryString - The SQL query to execute
 * @param {Array} params - The parameters for the query
 * @param {Object} options - Additional options
 * @param {number} options.timeout - Timeout in milliseconds
 * @param {number} options.maxResults - Maximum number of results to return
 * @param {string} options.queryName - Name of the query for logging
 * @param {number} options.maxRetries - Maximum number of retry attempts
 * @param {number} options.retryDelay - Delay between retries in milliseconds
 * @returns {Promise<Array>} - The query results
 */
async function safeDbQuery(queryString, params = [], options = {}) {
  const {
    timeout = 10000,
    maxResults = null,
    queryName = "database query",
    maxRetries = 2,
    retryDelay = 1000
  } = options;

  // Add LIMIT clause if maxResults is specified and not already in the query
  let finalQueryString = queryString;
  let finalParams = [...params];

  if (maxResults && !finalQueryString.toLowerCase().includes('limit')) {
    finalQueryString = `${finalQueryString} LIMIT ?`;
    finalParams = [...finalParams, maxResults];
  }

  let lastError = null;
  let retryCount = 0;

  // Retry loop
  while (retryCount <= maxRetries) {
    try {
      // If this is a retry, log it
      if (retryCount > 0) {
        console.log(`Retry attempt ${retryCount}/${maxRetries} for query '${queryName}'`);
      }

      // Create a promise that rejects after the timeout
      const timeoutPromise = new Promise((_, reject) => {
        const timeoutId = setTimeout(() => {
          clearTimeout(timeoutId); // Clean up
          reject(new Error(`Query timeout after ${timeout}ms: ${queryName}`));
        }, timeout);
      });

      // Execute the query
      const queryPromise = db.execute(finalQueryString, finalParams);

      // Race the query against the timeout
      const [results] = await Promise.race([queryPromise, timeoutPromise]);

      // Validate the response
      if (!Array.isArray(results)) {
        throw new Error(`Invalid response format from database for ${queryName}: expected array`);
      }

      // Check if results might be truncated
      if (maxResults && results.length === maxResults) {
        console.warn(`Query '${queryName}' returned maximum allowed results (${maxResults}). Data may be truncated.`);
      }

      // Success! Return the results
      return results;
    } catch (error) {
      lastError = error;

      // Determine if we should retry based on the error type
      const isRetryableError = (
        error.code === 'PROTOCOL_CONNECTION_LOST' ||
        error.code === 'ER_LOCK_DEADLOCK' ||
        error.code === 'ER_LOCK_WAIT_TIMEOUT' ||
        error.message.includes('timeout') ||
        error.message.includes('connection') ||
        error.code === 'ECONNRESET'
      );

      // If we've reached max retries or it's not a retryable error, stop trying
      if (retryCount >= maxRetries || !isRetryableError) {
        break;
      }

      // Log the retry
      console.warn(`Database error in query '${queryName}', will retry (${retryCount + 1}/${maxRetries}):`, error.message);

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay * (retryCount + 1)));
      retryCount++;
    }
  }

  // If we got here, all retries failed
  console.error(`Error executing ${queryName} after ${retryCount} retries:`, lastError);
  console.error("Query parameters:", params);

  // Re-throw the last error to be handled by the caller
  throw lastError;
}

// Register Chart.js plugins for proper rendering
Chart.register({
  id: 'datalabels',
  beforeDraw: (chart) => {
    const ctx = chart.ctx;
    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = 'bold 12px Arial';

    if (chart.config.type === 'pie' || chart.config.type === 'doughnut') {
      const meta = chart.getDatasetMeta(0);
      const total = meta.data.reduce((sum, element) => sum + element.parsed, 0);

      meta.data.forEach((element, index) => {
        const value = chart.data.datasets[0].data[index];
        if (value === 0 || total === 0) return;

        const percentage = Math.round((value / total) * 100) + '%';

        // For pie charts, position text in the center of each slice
        if (chart.config.type === 'pie') {
          const radius = element.outerRadius - (element.outerRadius - element.innerRadius) / 2;
          const angle = element.startAngle + (element.endAngle - element.startAngle) / 2;
          const x = element.x + Math.cos(angle) * radius * 0.7;
          const y = element.y + Math.sin(angle) * radius * 0.7;

          ctx.fillStyle = '#ffffff';
          ctx.fillText(percentage, x, y);
        }
      });
    }

    ctx.restore();
  }
});

const router = express.Router();

// Helper function to create a pie chart
const createPieChart = async (data, labels, title, width = 350, height = 250) => {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  new Chart(ctx, {
    type: 'pie',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: [
          '#0a3d62', // Dark blue
          '#ff3f34', // Red
          '#ffa801', // Orange
          '#05c46b', // Green
          '#3c40c6', // Blue
          '#f53b57'  // Pink
        ],
        borderColor: '#ffffff',
        borderWidth: 1
      }]
    },
    options: {
      responsive: false,
      animation: false, // Disable animation for PDF rendering
      plugins: {
        title: {
          display: true,
          text: title,
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 10
        },
        legend: {
          position: 'bottom',
          labels: {
            boxWidth: 15,
            font: {
              size: 12
            },
            padding: 15
          }
        },
        datalabels: {
          display: data.length <= 5, // Only show data labels if we have 5 or fewer items
          color: '#ffffff',
          font: {
            weight: 'bold'
          },
          formatter: (value) => {
            const sum = data.reduce((a, b) => a + b, 0);
            if (sum === 0) return '';
            const percentage = Math.round((value / sum) * 100) + '%';
            return percentage;
          }
        }
      }
    }
  });

  // Return the canvas as a buffer
  return canvas.toBuffer('image/png');
};

// Helper function to create a bar chart
const createBarChart = async (data, labels, title, width = 450, height = 250) => {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  new Chart(ctx, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        label: title,
        data: data,
        backgroundColor: [
          '#0a3d62', // Dark blue
          '#3c40c6', // Blue
          '#05c46b', // Green
          '#ffa801'  // Orange
        ],
        borderColor: '#0a3d62',
        borderWidth: 1
      }]
    },
    options: {
      responsive: false,
      animation: false, // Disable animation for PDF rendering
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: '#d2dae2'
          },
          ticks: {
            font: {
              size: 10
            },
            padding: 5
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 10
            },
            padding: 5
          }
        }
      },
      plugins: {
        title: {
          display: true,
          text: title,
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 10
        },
        legend: {
          display: false
        },
        datalabels: {
          display: true,
          color: '#1e272e',
          anchor: 'end',
          align: 'top',
          font: {
            weight: 'bold',
            size: 10
          },
          formatter: (value) => {
            return value + '%';
          }
        }
      }
    }
  });

  // Return the canvas as a buffer
  return canvas.toBuffer('image/png');
};

// Helper function to create a line chart
const createLineChart = async (data, labels, title, width = 450, height = 250) => {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: title,
        data: data,
        backgroundColor: 'rgba(10, 61, 98, 0.2)', // Dark blue with transparency
        borderColor: '#0a3d62',
        borderWidth: 2,
        tension: 0.3,
        fill: true,
        pointBackgroundColor: '#0a3d62',
        pointRadius: 4,
        pointHoverRadius: 4
      }]
    },
    options: {
      responsive: false,
      animation: false, // Disable animation for PDF rendering
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: '#d2dae2'
          },
          ticks: {
            font: {
              size: 10
            },
            padding: 5
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 10
            },
            padding: 5
          }
        }
      },
      plugins: {
        title: {
          display: true,
          text: title,
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 10
        },
        legend: {
          display: false
        },
        datalabels: {
          display: true,
          color: '#1e272e',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 4,
          font: {
            weight: 'bold',
            size: 10
          }
        }
      }
    }
  });

  // Return the canvas as a buffer
  return canvas.toBuffer('image/png');
};

// Helper function to create a gauge chart (donut chart with one value)
const createGaugeChart = async (value, maxValue, title, width = 250, height = 250) => {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Calculate percentage
  const percentage = (value / maxValue) * 100;

  // Determine color based on percentage
  let color = '#ff3f34'; // Red
  if (percentage >= 85) {
    color = '#05c46b'; // Green
  } else if (percentage >= 65) {
    color = '#ffa801'; // Orange
  }

  // Create the chart
  new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['Value', 'Remaining'],
      datasets: [{
        data: [value, maxValue - value],
        backgroundColor: [
          color,
          '#d2dae2' // Gray
        ],
        borderWidth: 0,
        cutout: '70%'
      }]
    },
    options: {
      responsive: false,
      animation: false, // Disable animation for PDF rendering
      circumference: 180,
      rotation: -90,
      layout: {
        padding: {
          top: 10,
          bottom: 10
        }
      },
      plugins: {
        title: {
          display: true,
          text: title,
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 10
        },
        legend: {
          display: false
        },
        tooltip: {
          enabled: false
        }
      }
    }
  });

  // Add text in the center with better positioning and visibility
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.font = 'bold 24px Arial';
  ctx.fillStyle = color;

  // Draw percentage text with white background for better visibility
  const percentageText = `${Math.round(percentage)}%`;
  const textWidth = ctx.measureText(percentageText).width;

  // Draw a small white background behind the text
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
  ctx.fillRect(width / 2 - textWidth / 2 - 5, height / 2 + 5, textWidth + 10, 30);

  // Draw the text
  ctx.fillStyle = color;
  ctx.fillText(percentageText, width / 2, height / 2 + 20);

  // Add a small label
  ctx.font = 'bold 12px Arial';
  ctx.fillText('Performance', width / 2, height / 2 - 15);

  // Return the canvas as a buffer
  return canvas.toBuffer('image/png');
};

/**
 * Test endpoint to generate a simple PDF without database queries
 */
router.post("/test-generate", auth, bypassPermission, async (_, res) => {
  console.log("Received test report generation request");
  try {
    // Create a simple PDF document
    console.log("Creating test PDF document");
    const doc = new PDFDocument({ margin: 50 });
    const reportDir = path.join(process.cwd(), "reports");

    // Ensure reports directory exists
    if (!fs.existsSync(reportDir)) {
      console.log(`Creating reports directory: ${reportDir}`);
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const filename = `test_report_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.pdf`;
    const filePath = path.join(reportDir, filename);
    console.log(`Test PDF will be saved to: ${filePath}`);

    // Create write stream
    console.log("Creating file write stream");
    const writeStream = fs.createWriteStream(filePath);

    // Handle write stream errors
    writeStream.on('error', (err) => {
      console.error("Error with write stream:", err);
      return res.status(500).json({ error: "Error creating PDF file: " + err.message });
    });

    // Pipe PDF to file
    doc.pipe(writeStream);

    // Add enhanced content to PDF with better styling
    try {
      console.log("Adding content to test PDF with enhanced styling");

      // Define colors
      const darkBlue = '#0a3d62';
      const black = '#1e272e';
      const lightBlue = '#c7ecee';
      const gray = '#d2dae2';
      const green = '#05c46b';

      // Add company logo or header
      doc.rect(0, 0, doc.page.width, 60).fill(darkBlue);
      doc.fontSize(24).fillColor('white').text("Rapport de Test", 50, 20, { align: "center" });

      // Add date and time in header
      doc.fontSize(10).fillColor('white').text(`Généré le: ${dayjs().format("DD/MM/YYYY HH:mm")}`, 50, 45, { align: "center" });

      // Add a horizontal line
      doc.moveTo(50, 70).lineTo(doc.page.width - 50, 70).strokeColor(darkBlue).lineWidth(2).stroke();

      // Test information section with styled box
      doc.rect(50, 80, doc.page.width - 100, 100).fillColor(lightBlue).fill();
      doc.rect(50, 80, doc.page.width - 100, 30).fillColor(darkBlue).fill();
      doc.fillColor('white').fontSize(16).text("Information de Test", 60, 90);

      doc.fillColor(black).fontSize(12);
      doc.text("Ce rapport est généré pour tester le système de génération de PDF.", 60, 120);
      doc.text("Si vous voyez ce rapport, cela signifie que le système fonctionne correctement.", 60, 140);
      doc.text(`Date et heure: ${dayjs().format("DD/MM/YYYY HH:mm:ss")}`, 60, 160);

      // Test results section with styled box
      doc.rect(50, 190, doc.page.width - 100, 100).fillColor(gray).fill();
      doc.rect(50, 190, doc.page.width - 100, 30).fillColor(darkBlue).fill();
      doc.fillColor('white').fontSize(16).text("Résultats du Test", 60, 200);

      doc.fillColor(black).fontSize(12);
      doc.text("✓ Génération de PDF", 60, 240);
      doc.text("✓ Formatage du contenu", 60, 260);
      doc.text("✓ Téléchargement du fichier", 300, 240);
      doc.text("✓ Affichage des couleurs", 300, 260);

      // Add a success message
      doc.rect(50, 300, doc.page.width - 100, 60).fillColor(green).fill();
      doc.fillColor('white').fontSize(16).text("Test Réussi!", 60, 320, { align: "center" });

      // Add footer
      doc.rect(0, doc.page.height - 40, doc.page.width, 40).fill(darkBlue);
      doc.fillColor('white').fontSize(10).text(
        `Rapport de test - Page 1/1`,
        50,
        doc.page.height - 25,
        { align: "center" }
      );

      // Finalize PDF
      console.log("Finalizing enhanced test PDF document");
      doc.end();
    } catch (err) {
      console.error("Error generating test PDF content:", err);
      return res.status(500).json({ error: "Error generating PDF content: " + err.message });
    }

    writeStream.on("finish", () => {
      console.log("Test PDF file write completed");

      // Send success response with dynamic URL based on environment
      const baseURL = process.env.NODE_ENV === 'production'
        ? 'https://eternal-friendly-chigger.ngrok-free.app'
        : 'http://localhost:5000';

      res.json({
        success: true,
        message: "Test report generated successfully",
        filename,
        filePath: `${baseURL}/api/shift-reports/view-test/${filename}`,
        downloadPath: `${baseURL}/api/shift-reports/download-test/${filename}`,
      });

      console.log("Test response sent to client");
    });

    writeStream.on("error", (err) => {
      console.error("Error writing test PDF:", err);
      res.status(500).json({ error: "Error generating test PDF report: " + err.message });
    });
  } catch (err) {
    console.error("Error in test report generation:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

/**
 * Download a test report PDF
 */
router.get("/download-test/:filename", (req, res) => {
  try {
    console.log("Download test report request received for:", req.params.filename);
    const filename = req.params.filename;
    const filePath = path.join(process.cwd(), "reports", filename);

    console.log("Looking for file at:", filePath);
    if (!fs.existsSync(filePath)) {
      console.error("File not found:", filePath);
      return res.status(404).json({ error: "Test report file not found" });
    }

    console.log("File found, sending for download");
    res.download(filePath);
  } catch (err) {
    console.error("Error downloading test report:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

/**
 * View a test report PDF in the browser
 */
router.get("/view-test/:filename", (req, res) => {
  try {
    console.log("View test report request received for:", req.params.filename);
    const filename = req.params.filename;
    const filePath = path.join(process.cwd(), "reports", filename);

    console.log("Looking for file at:", filePath);
    if (!fs.existsSync(filePath)) {
      console.error("File not found:", filePath);
      return res.status(404).json({ error: "Test report file not found" });
    }

    // Set headers for inline display (not download)
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename="' + filename + '"');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    console.log("File found, sending for viewing in browser");
  } catch (err) {
    console.error("Error viewing test report:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

/**
 * Generate a shift report with real data from database
 */
router.post("/generate", auth, bypassPermission, async (req, res) => {
  console.log("Received shift report generation request:", req.body);
  try {
    const { machineId, date, shift } = req.body;

    if (!machineId) {
      console.error("Missing machineId in request");
      return res.status(400).json({ error: "Machine ID is required" });
    }

    console.log(`Processing report for machine: ${machineId}, date: ${date}, shift: ${shift}`);

    // Calculate shift time window (8 hours)
    const currentDate = date ? dayjs(date) : dayjs();
    let startTime, endTime;

    switch(shift) {
      case "Matin":
        startTime = currentDate.format("YYYY-MM-DD 06:00:00");
        endTime = currentDate.format("YYYY-MM-DD 14:00:00");
        break;
      case "Après-midi":
        startTime = currentDate.format("YYYY-MM-DD 14:00:00");
        endTime = currentDate.format("YYYY-MM-DD 22:00:00");
        break;
      case "Nuit":
        startTime = currentDate.format("YYYY-MM-DD 22:00:00");
        endTime = currentDate.add(1, 'day').format("YYYY-MM-DD 06:00:00");
        break;
      default:
        // Default to current 8-hour window
        endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
        startTime = dayjs().subtract(8, 'hour').format("YYYY-MM-DD HH:mm:ss");
    }

    console.log(`Time window calculated: ${startTime} to ${endTime}`);

    // Query data from machine_daily_table_mould with improved error handling and timeout protection
    console.log("Querying data from machine_daily_table_mould");
    let dailyData = null;
    let dailyDataError = null;
    try {
      // First get the column names from the table to understand its structure
      const columns = await safeDbQuery(
        "SHOW COLUMNS FROM machine_daily_table_mould",
        [],
        { queryName: "machine_daily_table_mould columns", timeout: 8000 }
      );

      console.log("machine_daily_table_mould columns:", columns.map(col => col.Field));

      // Now query the actual data with the safe query function
      const dailyRows = await safeDbQuery(
        `SELECT * FROM machine_daily_table_mould
         WHERE Machine_Name = ?`,
        [machineId],
        { queryName: "machine_daily_table_mould data", timeout: 10000 }
      );

      if (dailyRows && dailyRows.length > 0) {
        console.log("Found daily data:", dailyRows[0]);
        dailyData = dailyRows[0];
      } else {
        console.log("No daily data found for the specified date and machine");
        dailyDataError = "No daily data found for this machine";
      }
    } catch (dbErr) {
      console.error("Database error when querying machine_daily_table_mould:", dbErr);
      // Log additional diagnostic information
      console.error("Query parameters:", { machineId, startTime, endTime });
      dailyDataError = `Error retrieving daily data: ${dbErr.message}`;
      // Continue with report generation even if query fails
    }

    // Query data from machine_sessions with improved error handling and timeout protection
    console.log("Querying data from machine_sessions");
    let sessionData = [];
    let sessionDataError = null;
    try {
      // Use the safeDbQuery helper with appropriate timeout and result limits
      const sessionRows = await safeDbQuery(
        `SELECT * FROM machine_sessions
         WHERE Machine_Name = ?
         AND session_start >= ?
         AND session_start < ?
         ORDER BY session_start DESC`,
        [machineId, startTime, endTime],
        {
          queryName: "machine_sessions",
          timeout: 15000, // 15 seconds timeout for potentially larger dataset
          maxResults: 1000 // Limit the maximum number of results to prevent memory issues
        }
      );

      if (sessionRows.length > 0) {
        console.log(`Found ${sessionRows.length} session records`);
        console.log("Machine sessions table structure:", Object.keys(sessionRows[0] || {}).join(', '));
        sessionData = sessionRows;
      } else {
        console.log("No session data found for the specified time window");
        sessionDataError = "No session data found for the specified time window";

        // Create placeholder session data to ensure PDF generation can complete
        sessionData = [{
          session_start: startTime,
          session_end: endTime,
          Quantite_Bon: 0,
          Quantite_Rejet: 0,
          Etat: "N/A",
          Code_arret: "N/A",
          Regleur_Prenom: "N/A",
          Article: "N/A",
          Ordre_Fabrication: "N/A",
          empreint: "N/A",
          cycle: 0,
          TRS: 0
        }];
        console.log("Created placeholder session data to ensure PDF generation can complete");
      }
    } catch (dbErr) {
      console.error("Database error when querying machine_sessions:", dbErr);
      // The safeDbQuery function already logs the parameters, so we don't need to do it again

      // Add fallback behavior
      console.log("Using empty dataset for machine_sessions due to query error");
      sessionDataError = `Error retrieving session data: ${dbErr.message}`;

      // Create placeholder session data to ensure PDF generation can complete
      sessionData = [{
        session_start: startTime,
        session_end: endTime,
        Quantite_Bon: 0,
        Quantite_Rejet: 0,
        Etat: "N/A",
        Code_arret: "N/A",
        Regleur_Prenom: "N/A",
        Article: "N/A",
        Ordre_Fabrication: "N/A",
        empreint: "N/A",
        cycle: 0,
        TRS: 0
      }];
      console.log("Created placeholder session data after error to ensure PDF generation can complete");
      // Continue with report generation even if query fails
    }

    // Create PDF document
    console.log("Creating PDF document with real data");
    // Add these settings when creating the PDF document to improve rendering:
    const doc = new PDFDocument({
      margin: 50,
      autoFirstPage: true,
      bufferPages: true,  // Important for correct page numbering
      layout: 'portrait',
      size: 'A4'
    });
    const reportDir = path.join(process.cwd(), "reports");

    // Initialize yPos variable for use throughout the document
    let yPos = 0;

    // Initialize machine name variable at a higher scope
    let machineName = machineId;
    const addPageFooter = () => {
      // remove bottom margin to avoid flow into new page
      const oldBottom = doc.page.margins.bottom;
      doc.page.margins.bottom = 0;

      // draw footer background
      doc
        .rect(0, doc.page.height - 40, doc.page.width, 40)
        .fill('#0a3d62');

      // draw footer text without wrapping
      doc
        .fillColor('white')
        .fontSize(10)
        .text(
          `Rapport de performance pour ${machineName || machineId} - Quart: ${shift || "Current"} `,
          50,
          doc.page.height - 30,
          {
            align: 'center',
            width: doc.page.width - 100,
            lineBreak: false
          }
        );

      // restore bottom margin
      doc.page.margins.bottom = oldBottom;
    };

    // Ensure reports directory exists
    if (!fs.existsSync(reportDir)) {
      console.log(`Creating reports directory: ${reportDir}`);
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const filename = `shift_report_${machineId}_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.pdf`;
    const filePath = path.join(reportDir, filename);
    console.log(`PDF will be saved to: ${filePath}`);

    // Create write stream
    console.log("Creating file write stream");
    const writeStream = fs.createWriteStream(filePath);

    // Handle write stream errors
    writeStream.on('error', (err) => {
      console.error("Error with write stream:", err);
      return res.status(500).json({ error: "Error creating PDF file: " + err.message });
    });

    // Pipe PDF to file
    doc.pipe(writeStream);
    // Footer helper

    // Add enhanced content to PDF with better styling and real data
    try {
      console.log("Adding content to PDF with enhanced styling and real data");

      // Register error handler for writeStream
      writeStream.on("error", (err) => {
        console.error("Error with write stream:", err);
        return res.status(500).json({ error: "Error creating PDF file: " + err.message });
      });

      // Register completion handler for writeStream
      writeStream.on("finish", function() {
        console.log("PDF file write completed");

        // Clear the timeout if it exists
        if (global.pdfTimeout) {
          clearTimeout(global.pdfTimeout);
          console.log("PDF generation timeout cleared");
          global.pdfTimeout = null;
        }

        // Send success response with dynamic URL based on environment
        const baseURL = process.env.NODE_ENV === 'production'
          ? 'https://eternal-friendly-chigger.ngrok-free.app'
          : 'http://localhost:5000';

        // Check if headers have already been sent
        if (!res.headersSent) {
          console.log("Sending success response to client");
          res.json({
            success: true,
            message: "Rapport généré avec succès (données statiques)",
            filename,
            filePath: `${baseURL}/api/shift-reports/view-static/${filename}`,
            downloadPath: `${baseURL}/api/shift-reports/download-static/${filename}`,
          });
          console.log("Response sent to client successfully");
        } else {
          console.warn("Headers already sent, cannot send success response");
        }
      });

      // Define colors
      const darkBlue = '#0a3d62';
      const black = '#1e272e';
      const lightBlue = '#c7ecee';
      const gray = '#d2dae2';
      const green = '#05c46b';
      const red = '#ff3f34';
      const orange = '#ffa801';

      // Calculate metrics from the data
      // Default values in case no data is found
      let goodQty = 0;
      let rejectQty = 0;
      let totalQty = 0;
      let qualityRate = "0.00";
      let availabilityRate = "0.00";
      let performanceRate = "0.00";
      let oeeRate = "0.00";
      let cycleMoyen = "0.00";
      let avgCycleTime = 0;
      // machineName is already defined at a higher scope
      let articleInfo = "N/A";
      let orderInfo = "N/A";
      let colorInfo = "N/A";
      let empreintesInfo = "N/A";
      let regleurInfo = "N/A";

      // Define latestSession at a higher scope so it's available throughout the code
      let latestSession = null;

      // Add warning message to the report if no data was found
      let noDataWarning = null;
      if (dailyDataError && sessionDataError) {
        noDataWarning = "Aucune donnée n'a été trouvée pour cette machine et cette période. Le rapport contient des valeurs par défaut.";
        console.log("No data available for report: " + noDataWarning);
      } else if (dailyDataError) {
        noDataWarning = "Données quotidiennes non disponibles: " + dailyDataError;
        console.log("Daily data warning: " + noDataWarning);
      } else if (sessionDataError) {
        noDataWarning = "Données de session non disponibles: " + sessionDataError;
        console.log("Session data warning: " + noDataWarning);
      }

      // Process daily data if available
      if (dailyData) {
        console.log("Using daily data for metrics:", dailyData);

        // Parse numeric values from text fields, handling commas and percentage signs
        const parseNumeric = (value) => {
          if (!value) return 0;
          // Remove any percentage sign and convert commas to dots for decimal
          const cleanValue = value.toString().replace('%', '').replace(',', '.');
          return isNaN(parseFloat(cleanValue)) ? 0 : parseFloat(cleanValue);
        };

        // Parse quantities
        goodQty = parseNumeric(dailyData.Good_QTY_Day);
        rejectQty = parseNumeric(dailyData.Rejects_QTY_Day);
        totalQty = goodQty + rejectQty;

        if (totalQty > 0) {
          qualityRate = ((goodQty / totalQty) * 100).toFixed(2);
        }

        // Parse rates, removing percentage signs if present
        availabilityRate = parseNumeric(dailyData.Availability_Rate_Day).toFixed(2);
        performanceRate = parseNumeric(dailyData.Performance_Rate_Day).toFixed(2);
        oeeRate = parseNumeric(dailyData.OEE_Day).toFixed(2);

        // Parse cycle time
        cycleMoyen = parseNumeric(dailyData.Speed_Day).toFixed(2);
      }

      // Process session data if available
      if (sessionData.length > 0) {
        console.log("Using session data for additional information");

        // Use the most recent session for machine details
        latestSession = sessionData[0];

        // Override with session data if daily data wasn't available
        if (!dailyData) {
          console.log("No daily data found, using session data for metrics");

          // Sum up quantities from all sessions
          goodQty = sessionData.reduce((sum, session) => sum + parseInt(session.Quantite_Bon || 0), 0);
          rejectQty = sessionData.reduce((sum, session) => sum + parseInt(session.Quantite_Rejet || 0), 0);
          totalQty = goodQty + rejectQty;

          if (totalQty > 0) {
            qualityRate = ((goodQty / totalQty) * 100).toFixed(2);
          }

          // Use average TRS from sessions
          const validTrsSessions = sessionData.filter(s => s.TRS && !isNaN(parseFloat(s.TRS)));
          if (validTrsSessions.length > 0) {
            const avgTrs = validTrsSessions.reduce((sum, s) => sum + parseFloat(s.TRS), 0) / validTrsSessions.length;
            oeeRate = avgTrs.toFixed(2);
          }

          // Use average cycle time
          const validCycleSessions = sessionData.filter(s => s.cycle && !isNaN(parseFloat(s.cycle)));
          if (validCycleSessions.length > 0) {
            const avgCycle = validCycleSessions.reduce((sum, s) => sum + parseFloat(s.cycle), 0) / validCycleSessions.length;
            cycleMoyen = avgCycle.toFixed(2);
          }
        }

        // Get additional information from the latest session
        machineName = latestSession.Machine_Name || machineId;
        articleInfo = latestSession.Article || "N/A";
        orderInfo = latestSession.Ordre_Fabrication || "N/A";
        colorInfo = latestSession.Code_arret || "N/A";
        empreintesInfo = latestSession.empreint || "N/A"; // Updated to use new field name
        regleurInfo = latestSession.Regleur_Prenom || "N/A";
        // Additional information is available in latestSession if needed
      }

      // Add company logo or header
      doc.rect(0, 0, doc.page.width, 60).fill(darkBlue);
      doc.fontSize(24).fillColor('white').text("Rapport de Performance de Quart", 50, 20, { align: "center" });

      // Add date and time in header
      doc.fontSize(10).fillColor('white').text(`Généré le: ${dayjs().format("DD/MM/YYYY HH:mm")}`, 50, 45, { align: "center" });

      // Add a horizontal line
      doc.moveTo(50, 70).lineTo(doc.page.width - 50, 70).strokeColor(darkBlue).lineWidth(2).stroke();

      // Display warning message if no data was found
      if (noDataWarning) {
        doc.rect(50, 80, doc.page.width - 100, 50).fillColor(orange).fill();
        doc.fillColor('white').fontSize(14).text("Attention: Données Manquantes", 60, 90, { align: "center" });
        doc.fillColor('white').fontSize(12).text(noDataWarning, 60, 110, { align: "center" });
        yPos = 140; // Update position for next content
      }

      // Machine information section with styled box - updated with aggregated data from machine_sessions
      doc.rect(50, 80, doc.page.width - 100, 160).fillColor(lightBlue).fill();
      doc.rect(50, 80, doc.page.width - 100, 30).fillColor(darkBlue).fill();
      doc.fillColor('white').fontSize(16).text("Information Machine", 60, 90);

      // Calculate aggregated data from machine_sessions for the 8-hour shift period
      let totalRunTime = 0;
      let totalCycles = 0;
      let mostCommonState = "N/A";
      let mostCommonStopCode = "N/A";

      if (sessionData.length > 0) {
        // Calculate total run time and stop time
        sessionData.forEach(session => {
          const startTime = session.session_start ? new Date(session.session_start) : null;
          const endTime = session.session_end ? new Date(session.session_end) : new Date();

          if (startTime) {
            const sessionDuration = (endTime - startTime) / (1000 * 60); // in minutes
            totalRunTime += sessionDuration;

            // Count cycles if available
            if (session.Quantite_Bon) {
              totalCycles += parseInt(session.Quantite_Bon) || 0;
            }
          }
        });

        // Calculate average cycle time from sessions with valid cycle data
        const validCycleSessions = sessionData.filter(s => s.cycle && !isNaN(parseFloat(s.cycle)));
        if (validCycleSessions.length > 0) {
          avgCycleTime = validCycleSessions.reduce((sum, s) => sum + parseFloat(s.cycle), 0) / validCycleSessions.length;
        }

        // Find most common state
        const stateCount = {};
        sessionData.forEach(session => {
          if (session.Etat) {
            stateCount[session.Etat] = (stateCount[session.Etat] || 0) + 1;
          }
        });
        mostCommonState = Object.entries(stateCount).sort((a, b) => b[1] - a[1])[0]?.[0] || "N/A";

        // Find most common stop code
        const stopCodeCount = {};
        sessionData.forEach(session => {
          if (session.Code_arret) {
            stopCodeCount[session.Code_arret] = (stopCodeCount[session.Code_arret] || 0) + 1;
          }
        });
        mostCommonStopCode = Object.entries(stopCodeCount).sort((a, b) => b[1] - a[1])[0]?.[0] || "N/A";
      }

      doc.fillColor(black).fontSize(12);
      doc.text(`Machine: ${machineName}`, 60, 120);
      doc.text(`Article: ${articleInfo}`, 60, 140);
      doc.text(`OF: ${orderInfo}`, 60, 160);
      doc.text(`Quart: ${shift || "Current"}`, 300, 120);
      doc.text(`Date: ${date || dayjs().format("YYYY-MM-DD")}`, 300, 140);
      doc.text(`Code arret: ${colorInfo}`, 300, 160);

      // Display aggregated data from machine_sessions
      doc.text(`Empreintes: ${empreintesInfo}`, 60, 180);
      doc.text(`Régleur: ${regleurInfo}`, 300, 180);
      doc.text(`Temps de Production: ${Math.round(totalRunTime)} min`, 60, 200);
      doc.text(`État Principal: ${mostCommonState}`, 300, 200);
      doc.text(`Cycles Totaux: ${totalCycles}`, 60, 220);
      doc.text(`Code Arrêt Principal: ${mostCommonStopCode}`, 300, 220);

      // Period information section with styled box - adjusted position
      doc.rect(50, 250, doc.page.width - 100, 100).fillColor(gray).fill();
      doc.rect(50, 250, doc.page.width - 100, 30).fillColor(darkBlue).fill();
      doc.fillColor('white').fontSize(16).text("Période", 60, 260);

      doc.fillColor(black).fontSize(12);
      doc.text(`Début: ${startTime}`, 60, 290);
      doc.text(`Fin: ${endTime}`, 60, 310);
      doc.text(`Durée: 8 heures`, 300, 290);
      if (req.user) {
        doc.text(`Généré par: ${req.user.username}`, 300, 310);
      } else {
        doc.text(`Généré par: Système`, 300, 310);
      }

      // Performance metrics section with styled box - adjusted position
      doc.rect(50, 360, doc.page.width - 100, 180).fillColor(lightBlue).fill();
      doc.rect(50, 360, doc.page.width - 100, 30).fillColor(darkBlue).fill();
      doc.fillColor('white').fontSize(16).text("Métriques de Performance", 60, 370);

      // Create a table-like structure for metrics
      doc.fillColor(black).fontSize(12);

      // First row
      doc.text("Quantité Bonne:", 60, 410);
      doc.text(goodQty.toLocaleString(), 200, 410);
      doc.text("Taux de Disponibilité:", 300, 410);
      doc.text(`${availabilityRate}%`, 450, 410);

      // Second row
      doc.text("Quantité Rejetée:", 60, 440);
      doc.text(rejectQty.toLocaleString(), 200, 440);
      doc.text("Taux de Performance:", 300, 440);
      doc.text(`${performanceRate}%`, 450, 440);

      // Third row
      doc.text("Production Totale:", 60, 470);
      doc.text(totalQty.toLocaleString(), 200, 470);
      doc.text("Taux de Qualité:", 300, 470);
      doc.text(`${qualityRate}%`, 450, 470);

      // Fourth row
      doc.text("Temps de Cycle Moyen:", 60, 500);
      doc.text(`${cycleMoyen} sec`, 200, 500);
      doc.text("TRS (OEE):", 300, 500);
      doc.text(`${oeeRate}%`, 450, 500);

      // Session data section on the same page - adjusted position
      if (sessionData.length > 0) {
        // Add session summary section
        doc.rect(50, 550, doc.page.width - 100, 120).fillColor(gray).fill();
        doc.rect(50, 550, doc.page.width - 100, 30).fillColor(darkBlue).fill();
        doc.fillColor('white').fontSize(16).text("Résumé des Sessions", 60, 560);

        // Show summary of sessions
        doc.fillColor(black).fontSize(12);
        doc.text(`Nombre de sessions: ${sessionData.length}`, 60, 590);

        // Calculate total quantities
        const totalGoodQty = sessionData.reduce((sum, session) => sum + parseInt(session.Quantite_Bon || 0), 0);
        const totalRejectQty = sessionData.reduce((sum, session) => sum + parseInt(session.Quantite_Rejet || 0), 0);

        // Show the latest session details
        const latestSession = sessionData[0];
        const startDate = latestSession.session_start ? dayjs(latestSession.session_start).format("HH:mm:ss") : "N/A";
        const endDate = latestSession.session_end ? dayjs(latestSession.session_end).format("HH:mm:ss") : "En cours";

        doc.text(`Dernière session: ${startDate} à ${endDate}`, 60, 610);
        doc.text(`Article: ${latestSession.Article || "N/A"}`, 60, 630);
        doc.text(`OF: ${latestSession.Ordre_Fabrication || "N/A"}`, 300, 630);
        doc.text(`Total Bon: ${totalGoodQty}`, 60, 650);
        doc.text(`Total Rejet: ${totalRejectQty}`, 300, 650);
        doc.addPage();


        // --- Historique des Sessions Section ---
        doc.rect(0, 0, doc.page.width, 60).fill(darkBlue);
        doc.fontSize(24).fillColor('white').text("Historique des Sessions", 50, 20, { align: "center" });
        doc.fontSize(10).fillColor('white').text(`Machine: ${machineName} - Date: ${date || dayjs().format("YYYY-MM-DD") } - Quart: ${shift || "Current"}`, 50, 45, { align: "center" });
        doc.moveTo(50, 70).lineTo(doc.page.width - 50, 70).strokeColor(darkBlue).lineWidth(2).stroke();
        doc.moveDown(1);
        // Draw the table header and session rows as before
        const sessionTableHeaders = [
          { label: "Début", width: 70 },
          { label: "Fin", width: 70 },
          { label: "Article", width: 50 },
          { label: "OF", width: 40 },
          { label: "Bon", width: 60 },
          { label: "Rejet", width: 50 },
          { label: "Cycle", width: 60 },
          { label: "TRS", width: 50 }
        ];
        let tableY = doc.y + 10;
        let tableX = 50;
        let rowHeight = 20;
        let tableWidth = sessionTableHeaders.reduce((sum, h) => sum + h.width, 0);
        // Draw header background
        let headerY = tableY;
        doc.rect(tableX, headerY, tableWidth, rowHeight).fill(darkBlue);
        doc.fillColor('white').fontSize(10);
        let colX = tableX;
        sessionTableHeaders.forEach(h => {
          doc.text(h.label, colX + 2, headerY + 5, { width: h.width - 4, align: "center" });
          colX += h.width;
        });
        // Draw session rows
        let rowY = headerY + rowHeight;
        sessionData.forEach((session, idx) => {
          colX = tableX;
          // Alternate row color
          if (idx % 2 === 0) {
            doc.rect(tableX, rowY, tableWidth, rowHeight).fillColor(lightBlue).fillOpacity(0.15).fill();
            doc.fillOpacity(1);
          }
          doc.fillColor(black).fontSize(9);
          const values = [
            session.session_start ? dayjs(session.session_start).format("HH:mm:ss") : "N/A",
            session.session_end ? dayjs(session.session_end).format("HH:mm:ss") : "En cours",
            session.Article || "N/A",
            session.Ordre_Fabrication || "N/A",
            session.Quantite_Bon != null ? session.Quantite_Bon : "0",
            session.Quantite_Rejet != null ? session.Quantite_Rejet : "0",
            session.cycle != null ? `${session.cycle}s` : "N/A",
            session.TRS != null ? `${session.TRS}%` : "N/A"
          ];
          values.forEach((val, i) => {
            doc.text(val, colX + 2, rowY + 5, { width: sessionTableHeaders[i].width - 4, align: "center" });
            colX += sessionTableHeaders[i].width;
          });
          rowY += rowHeight;
          if (rowY + rowHeight > doc.page.height - 60) {
            addPageFooter(doc.bufferedPageRange().start + doc.bufferedPageRange().count);
           doc.addPage();
            rowY = 60;
            doc.rect(tableX, rowY, tableWidth, rowHeight).fill(darkBlue);
            doc.fillColor('white').fontSize(10);
            colX = tableX;
            sessionTableHeaders.forEach(h => {
              doc.text(h.label, colX + 2, rowY + 5, { width: h.width - 4, align: "center" });
              colX += h.width;
            });
            rowY += rowHeight;
          }
        });
        doc.moveDown(2);
       addPageFooter();

      // Update yPos variable for the charts section
      yPos = doc.y || rowY + 20;

      // Add a new page for charts and visualizations
      // Add footer to current page
      addPageFooter();

      // Add a new page
     doc.addPage();
     addPageFooter();

      // Add header to charts page
      doc.rect(0, 0, doc.page.width, 60).fill(darkBlue);
      doc.fontSize(24).fillColor('white').text("Visualisations et Graphiques", 50, 20, { align: "center" });
      doc.fontSize(10).fillColor('white').text(`Machine: ${machineName} - Date: ${date || dayjs().format("YYYY-MM-DD")} - Quart: ${shift || "Current"}`, 50, 45, { align: "center" });

      // Add a horizontal line
      doc.moveTo(50, 70).lineTo(doc.page.width - 50, 70).strokeColor(darkBlue).lineWidth(2).stroke();

      // Reset Y position for new page
      yPos = 80;

      // Add this before creating any charts:
      // Fix chart rendering by ensuring enough space
      const ensureSpaceForChart = (height) => {
        if (doc.y + height > doc.page.height - 60) {
          doc.addPage();

          // Add header to the new page
          doc.rect(0, 0, doc.page.width, 60).fill(darkBlue);
          doc.fontSize(24).fillColor('white').text("Visualisations et Graphiques", 50, 20, { align: "center" });
          doc.fontSize(10).fillColor('white').text(`Machine: ${machineName} - Date: ${date || dayjs().format("YYYY-MM-DD")} - Quart: ${shift || "Current"}`, 50, 45, { align: "center" });

          // Add a horizontal line
          doc.moveTo(50, 70).lineTo(doc.page.width - 50, 70).strokeColor(darkBlue).lineWidth(2).stroke();

          return true;
        }
        return false;
      };

      // Reduce chart spacing to fit more on a page
      const chartSpacing = 20; // Reduced from 30

      // Create a two-column layout for the first row of charts
      // First column: OEE Gauge Chart
      try {
        console.log("Starting OEE gauge chart generation");

        // Create a gauge chart for OEE
        let oeeValue = parseFloat(oeeRate);

        // Ensure we have a valid value for the gauge chart
        if (isNaN(oeeValue) || oeeValue <= 0) {
          console.log("No valid OEE data available, using placeholder value");
          oeeValue = 0.1; // Use minimal placeholder value to avoid empty chart
        }

        console.log("Creating gauge chart with OEE value:", oeeValue);
        const oeeGaugeBuffer = await createGaugeChart(oeeValue, 100, "Performance Globale (TRS/OEE)");
        console.log("Gauge chart buffer created successfully");

        // Add the gauge chart to the PDF with reduced width
        const chartWidth = 200; // Reduced from 220
        console.log("Adding gauge chart to PDF at position:", yPos);
        doc.image(oeeGaugeBuffer, 50, yPos, { width: chartWidth });
        console.log("Gauge chart added to PDF successfully");

        // OEE interpretation with more compact text
        let oeeComment = "";
        if (oeeValue >= 85) {
          oeeComment = "Excellent - Performance de classe mondiale";
        } else if (oeeValue >= 75) {
          oeeComment = "Très bon - Continuez l'amélioration";
        } else if (oeeValue >= 65) {
          oeeComment = "Bon - Des améliorations sont possibles";
        } else if (oeeValue >= 55) {
          oeeComment = "Acceptable - Améliorations nécessaires";
        } else {
          oeeComment = "Faible - Actions correctives urgentes requises";
        }

        // If we're using placeholder data, add a note
        if (isNaN(parseFloat(oeeRate)) || parseFloat(oeeRate) <= 0) {
          oeeComment = "Données insuffisantes pour évaluer la performance";
        }

        // Add comment below the gauge chart with reduced spacing
        const commentY = yPos + chartWidth * 0.85; // Reduced spacing
        console.log("Adding OEE comment to PDF at position:", commentY);
        doc.fontSize(9).text(oeeComment, 50, commentY, { width: chartWidth, align: "center" }); // Smaller font
        console.log("OEE comment added to PDF successfully");
      } catch (chartErr) {
        console.error("Error creating OEE gauge chart:", chartErr);
        console.error("Chart error stack trace:", chartErr.stack);
        doc.fontSize(12).text("Erreur lors de la création du graphique OEE", 50, yPos);
      }

      // Second column: Quality Pie Chart
      try {
        console.log("Starting quality pie chart generation");

        // Create data for the pie chart
        let qualityData = [goodQty, rejectQty];
        const qualityLabels = ['Quantité Bonne', 'Quantité Rejetée'];
        console.log("Quality data:", qualityData);

        // Ensure we have some data for the chart - if both values are 0, add placeholder data
        if (qualityData[0] === 0 && qualityData[1] === 0) {
          console.log("No quality data available for chart, using placeholder data");
          qualityData = [1, 0]; // Use placeholder data to avoid empty chart

          // Add a note about missing data
          console.log("Adding missing data note to PDF");
          doc.fontSize(9).fillColor(orange).text("Aucune donnée de qualité disponible",
            doc.page.width - 250, yPos - 15, { align: "center" });
        }

        // Create the pie chart
        console.log("Creating quality pie chart");
        const qualityPieBuffer = await createPieChart(qualityData, qualityLabels, "Répartition de la Qualité");
        console.log("Quality pie chart buffer created successfully");

        // Add the pie chart to the PDF (in the second column) with reduced width
        const chartWidth = 200; // Reduced from 220
        const xPos = doc.page.width - chartWidth - 50;
        console.log("Adding quality pie chart to PDF at position:", xPos, yPos);
        doc.image(qualityPieBuffer, xPos, yPos, { width: chartWidth });
        console.log("Quality pie chart added to PDF successfully");
      } catch (chartErr) {
        console.error("Error creating quality pie chart:", chartErr);
        console.error("Chart error stack trace:", chartErr.stack);
        doc.fontSize(12).text("Erreur lors de la création du graphique de qualité", doc.page.width - 270, yPos);
      }

      // Move Y position down for the next chart (bar chart) with reduced spacing
      yPos += 230; // Reduced from 250
      ensureSpaceForChart(250); // Reduced from 280

      // Create Bar Chart for Performance Metrics (full width) with optimized dimensions
      try {
        console.log("Starting metrics bar chart generation");

        // Create data for the bar chart
        let metricsData = [
          parseFloat(availabilityRate),
          parseFloat(performanceRate),
          parseFloat(qualityRate),
          parseFloat(oeeRate)
        ];
        const metricsLabels = ['Disponibilité', 'Performance', 'Qualité', 'OEE/TRS'];
        console.log("Metrics data:", metricsData);

        // Check if we have valid data for the chart
        const hasValidData = metricsData.some(value => !isNaN(value) && value > 0);

        // If no valid data, use placeholder values
        if (!hasValidData) {
          console.log("No valid metrics data available for chart, using placeholder data");
          metricsData = [0.1, 0.1, 0.1, 0.1]; // Use minimal placeholder values

          // Add a note about missing data
          console.log("Adding missing metrics data note to PDF");
          doc.fontSize(10).fillColor(orange).text("Aucune donnée de performance valide disponible",
            50, yPos - 15, { align: "center", width: doc.page.width - 100 });
        }

        // Create the bar chart
        console.log("Creating metrics bar chart");
        const metricsBarBuffer = await createBarChart(metricsData, metricsLabels, "Indicateurs de Performance (%)");
        console.log("Metrics bar chart buffer created successfully");

        // Add the bar chart to the PDF (centered) with reduced width
        const chartWidth = 400; // Reduced from 450
        const xPos = (doc.page.width - chartWidth) / 2;
        console.log("Adding metrics bar chart to PDF at position:", xPos, yPos);
        doc.image(metricsBarBuffer, xPos, yPos, { width: chartWidth });
        console.log("Metrics bar chart added to PDF successfully");

        // Update Y position with reduced spacing
        yPos += 220 + chartSpacing; // Reduced from 250
        console.log("Updated Y position to:", yPos);
      } catch (chartErr) {
        console.error("Error creating metrics bar chart:", chartErr);
        console.error("Chart error stack trace:", chartErr.stack);
        doc.fontSize(12).text("Erreur lors de la création du graphique des indicateurs", 50, yPos);
        yPos += 30; // Still move down even if chart failed
        console.log("Updated Y position after error to:", yPos);
      }

      // If we have session data, create a line chart for production over time
      ensureSpaceForChart(230); // Reduced from 280

      // Always attempt to create the production chart section, even with no data
      try {
        console.log("Starting production line chart generation");

        if (sessionData.length > 0) {
          console.log("Session data available, processing for line chart");

          // Sort sessions by start time
          const sortedSessions = [...sessionData].sort((a, b) =>
            new Date(a.session_start) - new Date(b.session_start)
          );
          console.log("Sorted sessions for chart:", sortedSessions.length);

          // Extract data for the line chart (up to 10 sessions for readability)
          const productionData = sortedSessions.slice(0, 10).map(s => parseInt(s.Quantite_Bon || 0));
          const timeLabels = sortedSessions.slice(0, 10).map(s =>
            s.session_start ? dayjs(s.session_start).format("HH:mm") : "N/A"
          );
          console.log("Production data for chart:", productionData);
          console.log("Time labels for chart:", timeLabels);

          // Only create chart if we have valid data
          if (productionData.some(value => value > 0)) {
            console.log("Valid production data found, creating line chart");

            // Create the line chart
            const productionLineBuffer = await createLineChart(
              productionData,
              timeLabels,
              "Production par Session"
            );
            console.log("Production line chart buffer created successfully");

            // Add the line chart to the PDF (centered) with reduced width
            const chartWidth = 400; // Reduced from 450
            const xPos = (doc.page.width - chartWidth) / 2;
            console.log("Adding production line chart to PDF at position:", xPos, yPos);
            doc.image(productionLineBuffer, xPos, yPos, { width: chartWidth });
            console.log("Production line chart added to PDF successfully");

            // Update Y position with reduced spacing
            yPos += 220 + chartSpacing; // Reduced from 250
            console.log("Updated Y position to:", yPos);
          } else {
            // No valid data for chart
            console.log("No valid production data for chart, adding note");
            doc.fontSize(12).fillColor(orange).text("Pas de données de production suffisantes pour générer un graphique", 50, yPos, { align: "center", width: doc.page.width - 100 });
            yPos += 30;
            console.log("Updated Y position after note to:", yPos);
          }
        } else {
          // No session data at all
          console.log("No session data available, adding note");
          doc.fontSize(12).fillColor(orange).text("Aucune donnée de session disponible pour générer un graphique de production", 50, yPos, { align: "center", width: doc.page.width - 100 });
          yPos += 30;
          console.log("Updated Y position after note to:", yPos);
        }
      } catch (chartErr) {
        console.error("Error creating production line chart:", chartErr);
        console.error("Chart error stack trace:", chartErr.stack);
        doc.fontSize(12).text("Erreur lors de la création du graphique de production", 50, yPos, { align: "center", width: doc.page.width - 100 });
        yPos += 30; // Still move down even if chart failed
        console.log("Updated Y position after error to:", yPos);
      }

        // --- Conclusion Section ---
      console.log("Starting conclusion section");
      //  addPageFooter(doc.bufferedPageRange().start + doc.bufferedPageRange().count);
      console.log("Adding new page for conclusion");
      doc.addPage();
      console.log("New page added successfully");

      console.log("Adding page footer");
      addPageFooter();
      console.log("Page footer added successfully");

      console.log("Adding conclusion header");
      doc.rect(0, 0, doc.page.width, 60).fill(darkBlue);
      doc.fontSize(24).fillColor('white').text("Rapport de Performance - Conclusion", 50, 20, { align: "center" });
      doc.fontSize(10).fillColor('white').text(`Machine: ${machineName} - Date: ${date || dayjs().format("YYYY-MM-DD") } - Quart: ${shift || "Current"}`, 50, 45, { align: "center" });
      doc.moveTo(50, 70).lineTo(doc.page.width - 50, 70).strokeColor(darkBlue).lineWidth(2).stroke();
      console.log("Conclusion header added successfully");

      doc.moveDown(1);
      doc.moveDown(0.5);
      const yPosConclusion = doc.y;
      console.log("Conclusion Y position:", yPosConclusion);

      console.log("Adding conclusion box");
      doc.rect(50, yPosConclusion, doc.page.width - 100, 70).fillColor(lightBlue).fill();
      doc.rect(50, yPosConclusion, doc.page.width - 100, 20).fillColor(darkBlue).fill();
      doc.fillColor('white').fontSize(14).text("Conclusion", 60, yPosConclusion + 4);
      console.log("Conclusion box added successfully");

      // Add data availability warning in conclusion if needed
      if (noDataWarning) {
        console.log("Adding data warning to conclusion:", noDataWarning);
        doc.fillColor(orange).fontSize(11).text(noDataWarning, 60, yPosConclusion + 30, { width: doc.page.width - 120, align: "center" });
      }

      console.log("Adding performance comment");
      doc.fillColor(black).fontSize(10);
      let performanceComment = "Performance excellente. Continuez les pratiques actuelles.";
      doc.text(performanceComment, 60, yPosConclusion + 25, { width: doc.page.width - 120 });
      doc.text(`Rapport généré pour ${machineName} le ${dayjs().format("DD/MM/YYYY à HH:mm")}.`, 60, yPosConclusion + 45);
      console.log("Performance comment added successfully");

      // Finalize PDF
      console.log("Finalizing enhanced PDF document with real data");

      // Add event listener for errors on the document
      doc.on('error', (err) => {
        console.error("Error in PDF document:", err);
        if (!res.headersSent) {
          return res.status(500).json({ error: "Error in PDF document: " + err.message });
        }
      });

      // Store the timeout reference in a variable accessible to the writeStream.on("finish") handler
      let pdfTimeoutRef;

      // Add a timeout to detect if the PDF generation is taking too long
      pdfTimeoutRef = setTimeout(() => {
        console.error("PDF generation timeout - no 'finish' event received");

        // Try to force close the document and stream
        try {
          if (doc && typeof doc.end === 'function' && !doc.ended) {
            console.log("Forcing document end");
            doc.end();
          }

          if (writeStream && typeof writeStream.end === 'function' && !writeStream.ended) {
            console.log("Forcing write stream end");
            writeStream.end();
          }
        } catch (forceCloseErr) {
          console.error("Error while trying to force close:", forceCloseErr);
        }

        if (!res.headersSent) {
          return res.status(500).json({ error: "PDF generation timeout - operation took too long" });
        }
      }, 30000); // 30 seconds timeout

      // Store the timeout reference in a global variable that can be accessed by the writeStream.on("finish") handler
      global.pdfTimeout = pdfTimeoutRef;

      // End the document
      console.log("Calling doc.end() to finalize the PDF");
      doc.end();

      // Log that we've reached the end of the try block
      console.log("End of PDF generation try block");
    }
  } catch (err) {
    console.error("Error generating shift report:", err);
    console.error("Error stack trace:", err.stack);

    // Try to force close resources if they exist
    try {
      if (typeof doc !== 'undefined' && doc && typeof doc.end === 'function' && !doc.ended) {
        console.log("Forcing document end in catch block");
        doc.end();
      }

      if (typeof writeStream !== 'undefined' && writeStream && typeof writeStream.end === 'function' && !writeStream.ended) {
        console.log("Forcing write stream end in catch block");
        writeStream.end();
      }
    } catch (forceCloseErr) {
      console.error("Error while trying to force close in catch block:", forceCloseErr);
    }

    if (!res.headersSent) {
      res.status(500).json({ error: "Server error: " + err.message });
    } else {
      console.warn("Headers already sent, cannot send error response");
    }
  }
} catch (err) {
  console.error("Error generating PDF content (outer catch):", err);
  console.error("Error stack trace (outer catch):", err.stack);

  // Try to force close resources if they exist
  try {
    if (typeof doc !== 'undefined' && doc && typeof doc.end === 'function' && !doc.ended) {
      console.log("Forcing document end in outer catch block");
      doc.end();
    }

    if (typeof writeStream !== 'undefined' && writeStream && typeof writeStream.end === 'function' && !writeStream.ended) {
      console.log("Forcing write stream end in outer catch block");
      writeStream.end();
    }
  } catch (forceCloseErr) {
    console.error("Error while trying to force close in outer catch block:", forceCloseErr);
  }

  if (!res.headersSent) {
    return res.status(500).json({ error: "Error generating PDF content: " + err.message });
  } else {
    console.warn("Headers already sent, cannot send error response from outer catch");
  }
}
});

/**
 * Download a static shift report PDF
 */
router.get("/download-static/:filename", (req, res) => {
  try {
    console.log("Download static report request received for:", req.params.filename);
    const filename = req.params.filename;
    const filePath = path.join(process.cwd(), "reports", filename);

    console.log("Looking for file at:", filePath);
    if (!fs.existsSync(filePath)) {
      console.error("File not found:", filePath);
      return res.status(404).json({ error: "Report file not found" });
    }

    console.log("File found, sending for download");
    res.download(filePath);
  } catch (err) {
    console.error("Error downloading shift report:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

/**
 * View a static shift report PDF in the browser
 */
router.get("/view-static/:filename", (req, res) => {
  try {
    console.log("View static report request received for:", req.params.filename);
    const filename = req.params.filename;
    const filePath = path.join(process.cwd(), "reports", filename);

    console.log("Looking for file at:", filePath);
    if (!fs.existsSync(filePath)) {
      console.error("File not found:", filePath);
      return res.status(404).json({ error: "Report file not found" });
    }

    // Set headers for inline display (not download)
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename="' + filename + '"');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    console.log("File found, sending for viewing in browser");
  } catch (err) {
    console.error("Error viewing shift report:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

export default router;
