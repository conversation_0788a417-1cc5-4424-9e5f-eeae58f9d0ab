import React, { Component } from 'react';
import { Result, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

/**
 * Error boundary component to catch errors in child components
 * @extends {Component}
 */
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  /**
   * Update state when an error occurs
   * @param {Error} error - The error that was thrown
   * @returns {Object} - New state
   */
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  /**
   * Capture error information
   * @param {Error} error - The error that was thrown
   * @param {Object} errorInfo - Information about the error
   */
  componentDidCatch(error, errorInfo) {
    // Log error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
    
    // You could send this to an error reporting service
    // reportError(error, errorInfo);
  }

  /**
   * Reset the error state
   */
  resetErrorBoundary = () => {
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
    
    // Call onReset callback if provided
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  render() {
    const { hasError, error } = this.state;
    const { children, fallback, showReset = true } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return typeof fallback === 'function' 
          ? fallback({ error, resetErrorBoundary: this.resetErrorBoundary })
          : fallback;
      }

      // Default error UI
      return (
        <Result
          status="error"
          title="Une erreur est survenue"
          subTitle={error?.message || "Quelque chose s'est mal passé lors du chargement de ce composant."}
          extra={showReset && (
            <Button 
              type="primary" 
              icon={<ReloadOutlined />} 
              onClick={this.resetErrorBoundary}
            >
              Réessayer
            </Button>
          )}
        />
      );
    }

    return children;
  }
}

export default ErrorBoundary;
