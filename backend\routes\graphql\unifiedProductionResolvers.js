/**
 * Unified Production GraphQL Resolvers
 * 
 * Provides GraphQL resolvers that can work with both Elasticsearch (primary)
 * and MySQL (fallback) data sources with automatic switching for production data.
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

import { executeQuery } from '../../utils/dbUtils.js';
import { buildDateFilter } from '../../utils/dateUtils.js';
import redisConfig from '../../config/redisConfig.js';
import elasticsearchProductionService from '../../services/ElasticsearchProductionService.js';
import { checkElasticsearchHealth } from '../../config/elasticsearch.js';
import crypto from 'crypto';

class UnifiedProductionResolvers {
  constructor() {
    this.cacheTimeout = 300; // 5 minutes cache
  }

  /**
   * Select appropriate data source (Elasticsearch primary, MySQL fallback)
   */
  async selectDataSource() {
    try {
      const isElasticsearchAvailable = await checkElasticsearchHealth();
      
      if (isElasticsearchAvailable) {
        // Check if production index exists and has data
        const hasProductionData = await elasticsearchProductionService.isAvailable();
        if (hasProductionData) {
          console.log('Using Elasticsearch as primary data source for production');
          return 'elasticsearch';
        } else {
          console.warn('Elasticsearch available but no production data indexed, using MySQL');
          return 'mysql';
        }
      } else {
        console.warn('Elasticsearch unavailable, falling back to MySQL for production');
        return 'mysql';
      }
    } catch (error) {
      console.error('Error selecting production data source:', error);
      return 'mysql';
    }
  }

  /**
   * Cache key generator
   */
  generateCacheKey(operation, filters = {}) {
    const filtersStr = JSON.stringify(filters);
    const hash = crypto.createHash('md5').update(filtersStr).digest('hex');
    return `production:${operation}:${hash}`;
  }

  /**
   * Get data with caching
   */
  async getCachedData(cacheKey, dataFetcher) {
    try {
      // Try to get from cache first
      if (redisConfig.isConnected) {
        const client = redisConfig.getClient();
        const cached = await client.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }
      
      // Fetch fresh data
      const data = await dataFetcher();
      
      // Cache the result
      if (redisConfig.isConnected && data) {
        const client = redisConfig.getClient();
        await client.setex(cacheKey, this.cacheTimeout, JSON.stringify(data));
      }
      
      return data;
    } catch (error) {
      console.error('Error with cached production data operation:', error);
      // Return fresh data without caching on error
      return await dataFetcher();
    }
  }

  /**
   * Direct MySQL query for production chart data (fallback)
   */
  async getProductionChartFromMySQL(filters = {}) {
    try {
      const { dateRangeType = "day", model, machine, page = 1, limit = 100 } = filters;
      
      let query = `
        SELECT
          DATE_FORMAT(
            CASE 
              WHEN Date_Insert_Day LIKE '%/%' THEN
                STR_TO_DATE(
                  SUBSTRING_INDEX(Date_Insert_Day, ' ', 1), 
                  '%d/%m/%Y'
                )
              ELSE
                STR_TO_DATE(Date_Insert_Day, '%Y-%m-%d')
            END,
            '%Y-%m-%d'
          ) AS Date_Insert_Day,
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS Total_Good_Qty_Day,
          SUM(
            CASE 
              WHEN Rejects_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Rejects_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS Total_Rejects_Qty_Day,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS OEE_Day,
          AVG(
            CASE 
              WHEN Speed_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Speed_Day, ',', '.') AS DECIMAL(10,2))
              ELSE 0 
            END
          ) AS Speed_Day,
          AVG(
            CASE 
              WHEN Availability_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Availability_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS Availability_Rate_Day,
          AVG(
            CASE 
              WHEN Performance_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Performance_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Performance_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS Performance_Rate_Day,
          AVG(
            CASE 
              WHEN Quality_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Quality_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Quality_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS Quality_Rate_Day
        FROM machine_daily_table_mould
        WHERE 1=1`;

      let queryParams = [];

      // Apply date filter if provided
      if (filters.date) {
        const dateFilter = buildDateFilter(filters.date, dateRangeType);
        query += dateFilter.condition;
        queryParams = queryParams.concat(dateFilter.params);
      }

      // Apply machine filters
      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(machine);
      }

      query += ` GROUP BY Date_Insert_Day ORDER BY Date_Insert_Day DESC LIMIT ${parseInt(limit)}`;

      const result = await executeQuery(query, queryParams);
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching production chart from MySQL:', error);
      return [];
    }
  }

  /**
   * Get production sidecards from MySQL (fallback)
   */
  async getProductionSidecardsFromMySQL(filters = {}) {
    try {
      let query = `
        SELECT 
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS goodqty,
          SUM(
            CASE 
              WHEN Rejects_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Rejects_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS rejetqty
        FROM machine_daily_table_mould 
        WHERE 1=1`;

      let queryParams = [];

      // Apply filters
      if (filters.model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${filters.model}%`);
      } else if (filters.machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(filters.machine);
      }

      if (filters.date) {
        const dateFilter = buildDateFilter(filters.date, filters.dateRangeType || 'day');
        query += dateFilter.condition;
        queryParams = queryParams.concat(dateFilter.params);
      }

      const result = await executeQuery(query, queryParams);
      const data = result.success ? result.data : [];
      
      return data[0] || { goodqty: 0, rejetqty: 0 };
    } catch (error) {
      console.error('Error fetching production sidecards from MySQL:', error);
      return { goodqty: 0, rejetqty: 0 };
    }
  }

  /**
   * Get shift performance from MySQL (for team comparison charts)
   */
  async getShiftPerformanceFromMySQL(filters = {}) {
    try {
      let query = `
        SELECT 
          Shift AS shift_name,
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS production,
          SUM(
            CASE 
              WHEN Rejects_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Rejects_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS rejects,
          AVG(
            CASE 
              WHEN Availability_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Availability_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS availability,
          AVG(
            CASE 
              WHEN Performance_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Performance_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Performance_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS performance,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS oee,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS trs,
          AVG(
            CASE 
              WHEN Quality_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Quality_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Quality_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS quality,
          AVG(
            CASE 
              WHEN Availability_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Availability_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS disponibilite,
          AVG(
            CASE 
              WHEN Down_Hours_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Down_Hours_Day, ',', '.') AS DECIMAL(10,2))
              ELSE 0 
            END
          ) AS downtime
        FROM machine_daily_table_mould 
        WHERE 1=1`;

      let queryParams = [];

      // Apply filters
      if (filters.model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${filters.model}%`);
      } else if (filters.machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(filters.machine);
      }

      if (filters.date) {
        const dateFilter = buildDateFilter(filters.date, filters.dateRangeType || 'day');
        query += dateFilter.condition;
        queryParams = queryParams.concat(dateFilter.params);
      }

      query += ` GROUP BY Shift ORDER BY Shift`;

      console.log('🔍 [SHIFT PERFORMANCE DEBUG] Final query:', query);
      console.log('🔍 [SHIFT PERFORMANCE DEBUG] Query params:', queryParams);

      const result = await executeQuery(query, queryParams);
      
      if (result.success && result.data.length > 0) {
        console.log('🔍 [SHIFT PERFORMANCE DEBUG] Sample result:', result.data[0]);
        console.log('🔍 [SHIFT PERFORMANCE DEBUG] Total shifts returned:', result.data.length);
        
        // Map shift_name to Shift to match GraphQL schema expectations
        const mappedData = result.data.map(item => ({
          ...item,
          Shift: item.shift_name,
          // Remove the shift_name field to avoid confusion
          shift_name: undefined
        }));
        
        return mappedData;
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching shift performance from MySQL:', error);
      return [];
    }
  }

  /**
   * Get machine performance from MySQL (fallback)
   */
  async getMachinePerformanceFromMySQL(filters = {}) {
    try {
      let query = `
        SELECT 
          Machine_Name,
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS production,
          SUM(
            CASE 
              WHEN Rejects_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Rejects_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS rejects,
          AVG(
            CASE 
              WHEN Availability_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Availability_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS availability,
          AVG(
            CASE 
              WHEN Performance_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Performance_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Performance_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS performance,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS oee,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS trs,
          AVG(
            CASE 
              WHEN Quality_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Quality_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Quality_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS quality,
          AVG(
            CASE 
              WHEN Availability_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN Availability_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                -- For decimal values (0-1 range), convert to percentage (0-100 range)
                CASE 
                  WHEN CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4)) * 100
                  ELSE
                    CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4))
                END
              ELSE 0 
            END
          ) AS disponibilite,
          AVG(
            CASE 
              WHEN Down_Hours_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Down_Hours_Day, ',', '.') AS DECIMAL(10,2))
              ELSE 0 
            END
          ) AS downtime
        FROM machine_daily_table_mould 
        WHERE 1=1`;

      let queryParams = [];

      // Apply filters
      if (filters.model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${filters.model}%`);
      } else if (filters.machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(filters.machine);
      }

      if (filters.date) {
        const dateFilter = buildDateFilter(filters.date, filters.dateRangeType || 'day');
        query += dateFilter.condition;
        queryParams = queryParams.concat(dateFilter.params);
      }

      query += ` GROUP BY Machine_Name ORDER BY Machine_Name`;

      console.log('🔍 [MACHINE PERFORMANCE DEBUG] Final query:', query);
      console.log('🔍 [MACHINE PERFORMANCE DEBUG] Query params:', queryParams);

      const result = await executeQuery(query, queryParams);
      
      if (result.success && result.data.length > 0) {
        console.log('🔍 [MACHINE PERFORMANCE DEBUG] Sample result:', result.data[0]);
        console.log('🔍 [MACHINE PERFORMANCE DEBUG] Total machines returned:', result.data.length);
      }
      
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching machine performance from MySQL:', error);
      return [];
    }
  }

  /**
   * Get production chart data with unified data source
   */
  async getProductionChart(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('productionChart', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const result = await elasticsearchProductionService.getProductionChart(filters);
          return {
            data: result,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback to MySQL
          const mysqlData = await this.getProductionChartFromMySQL(filters);
          return {
            data: mysqlData,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source for production chart:`, error);
        
        // Try fallback if primary fails
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, trying MySQL fallback for production chart...');
          const mysqlData = await this.getProductionChartFromMySQL(filters);
          return {
            data: mysqlData,
            dataSource: 'mysql-fallback'
          };
        }
        throw error;
      }
    });
  }

  /**
   * Get production sidecards with unified data source
   */
  async getProductionSidecards(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('productionSidecards', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const result = await elasticsearchProductionService.getProductionSidecards(filters);
          return {
            ...result,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback to MySQL
          const mysqlData = await this.getProductionSidecardsFromMySQL(filters);
          return {
            ...mysqlData,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source for production sidecards:`, error);
        
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, trying MySQL fallback for production sidecards...');
          const mysqlData = await this.getProductionSidecardsFromMySQL(filters);
          return {
            ...mysqlData,
            dataSource: 'mysql-fallback'
          };
        }
        throw error;
      }
    });
  }

  /**
   * Get shift performance with unified data source (for team comparison)
   */
  async getShiftPerformance(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('shiftPerformance', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          // For now, fallback to MySQL for shift performance
          // In future, Elasticsearch could be enhanced to support shift-based queries
          console.log('Shift performance not yet implemented for Elasticsearch, using MySQL...');
          const mysqlData = await this.getShiftPerformanceFromMySQL(filters);
          return {
            data: mysqlData,
            dataSource: 'mysql'
          };
        } else {
          // Use MySQL for shift performance
          const mysqlData = await this.getShiftPerformanceFromMySQL(filters);
          return {
            data: mysqlData,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source for shift performance:`, error);
        
        // Always fallback to MySQL for shift performance
        console.log('Error occurred, falling back to MySQL for shift performance...');
        const mysqlData = await this.getShiftPerformanceFromMySQL(filters);
        return {
          data: mysqlData,
          dataSource: 'mysql-fallback'
        };
      }
    });
  }

  /**
   * Get machine performance with unified data source
   */
  async getMachinePerformance(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('machinePerformance', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const result = await elasticsearchProductionService.getMachinePerformance(filters);
          return {
            data: result,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback to MySQL
          const mysqlData = await this.getMachinePerformanceFromMySQL(filters);
          return {
            data: mysqlData,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source for machine performance:`, error);
        
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, trying MySQL fallback for machine performance...');
          const mysqlData = await this.getMachinePerformanceFromMySQL(filters);
          return {
            data: mysqlData,
            dataSource: 'mysql-fallback'
          };
        }
        throw error;
      }
    });
  }

  /**
   * Get data source status for monitoring
   */
  async getDataSourceStatus() {
    try {
      const isElasticsearchAvailable = await checkElasticsearchHealth();
      let elasticsearchStats = null;
      
      if (isElasticsearchAvailable) {
        try {
          elasticsearchStats = await elasticsearchProductionService.getIndexStats();
        } catch (error) {
          console.warn('Failed to get Elasticsearch production stats:', error);
        }
      }
      
      return {
        primarySource: isElasticsearchAvailable ? 'elasticsearch' : 'mysql',
        elasticsearch: {
          available: isElasticsearchAvailable,
          stats: elasticsearchStats
        },
        mysql: {
          available: true, // Assume MySQL is always available if we reach here
          stats: null // Could add MySQL stats here
        }
      };
    } catch (error) {
      console.error('Error getting production data source status:', error);
      return {
        primarySource: 'mysql',
        elasticsearch: {
          available: false,
          error: error.message
        },
        mysql: {
          available: true
        }
      };
    }
  }
}

// Create instance and export resolvers
const unifiedProductionResolvers = new UnifiedProductionResolvers();

const resolvers = {
  getProductionChart: (parent, args, context) => unifiedProductionResolvers.getProductionChart(parent, args, context),
  getProductionSidecards: (parent, args, context) => unifiedProductionResolvers.getProductionSidecards(parent, args, context),
  getMachinePerformance: (parent, args, context) => unifiedProductionResolvers.getMachinePerformance(parent, args, context),
  getShiftPerformance: (parent, args, context) => unifiedProductionResolvers.getShiftPerformance(parent, args, context),
  getDataSourceStatus: (parent, args, context) => unifiedProductionResolvers.getDataSourceStatus(parent, args, context),
  selectDataSource: () => unifiedProductionResolvers.selectDataSource()
};

export default resolvers;
