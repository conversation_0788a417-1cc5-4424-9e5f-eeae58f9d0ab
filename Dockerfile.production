# Production-Ready Unified LOCQL Container
# Frontend (Vite) + Backend (Express) on Port 5000
# Optimized for Docker Hub deployment with unified architecture

FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    make \
    g++ \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set Puppeteer to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# ===========================
# STAGE 1: Dependencies
# ===========================
FROM base AS dependencies

# Copy package files for dependency installation
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

# Install root dependencies
RUN npm ci  --no-audit --no-fund

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm ci  --no-audit --no-fund

# Install backend dependencies  
WORKDIR /app/backend
RUN npm ci  --no-audit --no-fund

# ===========================
# STAGE 2: Build Frontend
# ===========================
FROM dependencies AS frontend-build

WORKDIR /app/frontend

# Copy frontend source
COPY frontend/ .

# Build frontend for production
RUN npm run build

# ===========================
# STAGE 3: Production Image
# ===========================
FROM base AS production

# Set production environment
ENV NODE_ENV=production
ENV PORT=5000

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/

# Copy node_modules from dependencies stage
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/backend/node_modules ./backend/node_modules

# Copy backend source
COPY backend/ ./backend/

# Copy built frontend from build stage
COPY --from=frontend-build /app/frontend/dist ./frontend/dist

# Copy environment configuration files
COPY config.env ./
COPY docker.env ./

# Set ownership
RUN chown -R nodejs:nodejs /app

USER nodejs

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start the unified server (backend serves API + frontend)
CMD ["npm", "run", "start"]
