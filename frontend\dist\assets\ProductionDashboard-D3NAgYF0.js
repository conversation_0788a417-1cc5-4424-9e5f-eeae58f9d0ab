import{r as d,a as Lt,at as O,R as e,s as Tt,aj as Ct,d as j,T as dt,f as v,D as At,g as Ye,P as ca,e as he,B as $t,S as R,ad as ma,l as da,E as X,h as at,c as I,b as be,F as Kt,au as ua,A as fa,G as pa,C as fe,O as Rt,m as Ht,i as ha}from"./index-O2xm1U_Z.js";import{t as ga,n as ne,R as ya,S as Ea}from"./dataUtils-p4cY5Ek4.js";import{d as M}from"./dayjs.min-CgAD4wBe.js";import"./fr-D5QIIXkH.js";import{i as Da,G as Sa}from"./GlobalSearchModal-CB8P5fP4.js";import{w as ba,c as _a,R as xt}from"./DownloadOutlined-ClmkhSDC.js";import{u as Ra}from"./useDailyTableGraphQL-CKkW_3L0.js";import{F as Ca,S as xa}from"./SearchResultsDisplay-CTnAspg6.js";import{U as pe,u as Pa,g as Ma}from"./UnifiedChartExpansion-D6afXd9w.js";import{R as tt}from"./ThunderboltOutlined-xe_XoJ1p.js";import{R as ut}from"./ToolOutlined-BRYzAkU2.js";import{P as Ee}from"./progress-CVvjjq5H.js";import{R as zt}from"./CheckCircleOutlined-BmO9kYsr.js";import{R as Ta}from"./ReloadOutlined-EeD9QNgc.js";import{c as ze,d as et,a as Pt,f as _e}from"./numberFormatter-CKFvf91F.js";import{R as Aa,a as Na}from"./RiseOutlined-BR87aXMV.js";import{R as qt}from"./DashboardOutlined-CC3QTJ5W.js";import{R as va}from"./ClockCircleOutlined-D-iaV6k8.js";import{R as ka}from"./CloseCircleOutlined-CKZmJH6e.js";import{R as Jt}from"./LineChartOutlined-CdWMpnra.js";import{R as Ia}from"./CalendarOutlined-ry8TLVWh.js";import{u as Re,g as Ya,a as Xt}from"./chartColors-CyE5IZmc.js";import{R as ge,B as qe,C as oe,X as le,Y as ie,T as Z,a as ee,g as je,P as wa,e as Fa,f as La,L as rt,d as nt,h as $a,i as Y}from"./PieChart-CJMXTNKV.js";import{R as jt}from"./InfoCircleOutlined-BSC7vzM8.js";import{R as za}from"./WarningOutlined-BiatBBB-.js";import{R as Nt}from"./BarChartOutlined-CX9KDBHm.js";import{S as Ze}from"./index-Dc91-n-S.js";import{R as qa}from"./TableOutlined-DH1zLx-t.js";import{R as ja}from"./SearchOutlined-CzaKf_7S.js";import"./FilePdfOutlined-Cew2Jbhk.js";import"./index-fv7kzFnJ.js";import"./FileTextOutlined-ZE845-EP.js";import"./ExperimentOutlined-D3OB_L_Y.js";import"./index-DcuuzGl6.js";import"./FilterOutlined-B3wNZQVB.js";import"./index-fmer6zpJ.js";import"./EyeOutlined-DDj6D5vZ.js";import"./FullscreenOutlined-DMf8_5Nq.js";import"./CloseOutlined-CLsA06b-.js";import"./ZoomOutOutlined-CdPjzMfa.js";const Qa=()=>{const[n,i]=d.useState([]),[a,t]=d.useState([]),[r,s]=d.useState([]),[h,f]=d.useState("IPS"),[l,C]=d.useState(""),[b,x]=d.useState(!1),u=d.useCallback(async()=>{try{console.log("Fetching machine models..."),x(!0);const y=await Lt.get("/api/machine-models").retry(2);if(y.body){const D=O(y),g=Array.isArray(D)?D.map(E=>E.model||E):[];console.log("Machine models fetched:",g),i(g.length>0?g:["IPS","CCM24"])}else console.log("No machine models returned from API, using defaults"),i(["IPS","CCM24"])}catch(y){console.error("Error loading machine models:",y),i(["IPS","CCM24"])}finally{x(!1)}},[]),p=d.useCallback(async()=>{try{console.log("Fetching machine names..."),x(!0);const y=await Lt.get("/api/machine-names").retry(2);if(y.body){const D=O(y);console.log("Machine names fetched:",D),Array.isArray(D)&&D.length>0?(t(D),D.find(E=>E.Machine_Name==="IPS01")&&h==="IPS"&&console.log("Confirmed IPS as default machine model")):(console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}else console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(y){console.error("Error loading machine names:",y),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}finally{x(!1)}},[h]),m=y=>{f(y)},_=y=>{C(y)};return d.useEffect(()=>{if(h){const y=a.filter(D=>D.Machine_Name&&D.Machine_Name.startsWith(h));s(y),l&&!y.some(D=>D.Machine_Name===l)&&C("")}else s([]),C("")},[h,a,l]),d.useEffect(()=>{u()},[u]),d.useEffect(()=>{p()},[p]),{machineModels:n,machineNames:a,filteredMachineNames:r,selectedMachineModel:h,selectedMachine:l,loading:b,handleMachineModelChange:m,handleMachineChange:_,fetchMachineModels:u,fetchMachineNames:p,setSelectedMachineModel:f,setSelectedMachine:C}},Ba=n=>n?new Date(n).toISOString().split("T")[0]:null;M.extend(Da);M.extend(ba);M.extend(_a);M.locale("fr");const Va=()=>{const[n,i]=d.useState(null),[a,t]=d.useState("day"),[r,s]=d.useState(""),[h,f]=d.useState(!1),l=d.useCallback((p,m)=>{if(!p)return{short:"",full:""};try{const _=M(p);if(!_.isValid())return console.error("Invalid date in formatDateRange:",p),{short:"Date invalide",full:"Date invalide"};if(m==="day")return{short:_.format("DD/MM/YYYY"),full:`le ${_.format("DD MMMM YYYY")}`};if(m==="week"){const y=_.startOf("isoWeek"),D=_.endOf("isoWeek"),g=_.isoWeek();return{short:`S${g} ${_.format("YYYY")}`,full:`Semaine ${g} (du ${y.format("DD MMMM")} au ${D.format("DD MMMM YYYY")})`}}else if(m==="month"){const y=_.format("MMMM"),D=y.charAt(0).toUpperCase()+y.slice(1);return{short:`${D} ${_.format("YYYY")}`,full:`${D} ${_.format("YYYY")}`}}return{short:"",full:""}}catch(_){return console.error("Error in formatDateRange:",_),{short:"Erreur de date",full:"Erreur de date"}}},[]),C=p=>{if(!p){x();return}try{let m=M(p);const _=m.toDate();i(_);const{full:y}=l(_,a);s(y),f(!0),console.log(`Date selected: ${m.format("YYYY-MM-DD")}, Range type: ${a}`)}catch(m){console.error("Error handling date change:",m),i(p);const{full:_}=l(p,a);s(_),f(!0)}},b=p=>{if(t(p),n){const m=M(n);let _=m;p==="week"?_=m.startOf("isoWeek"):p==="month"&&(_=m.startOf("month"));const y=_.toDate();i(y);const{full:D}=l(y,p);s(D),console.log(`Date range type changed to: ${p}, Adjusted date: ${_.format("YYYY-MM-DD")}`)}},x=()=>{i(null),s(""),f(!1)},u=d.useCallback(()=>{const p=new URLSearchParams;if(n)try{const m=Ba(n);m?(p.append("date",m),p.append("dateRangeType",a),console.log(`API request params: date=${m}, dateRangeType=${a}`)):console.error("Failed to format date for API request:",n)}catch(m){console.error("Error building date query params:",m)}return p},[n,a]);return{dateFilter:n,dateRangeType:a,dateRangeDescription:r,dateFilterActive:h,handleDateChange:C,handleDateRangeTypeChange:b,resetDateFilter:x,buildDateQueryParams:u,formatDateRange:l}},Ua=({selectedMachineModel:n,selectedMachine:i,dateFilter:a,dateRangeType:t,buildDateQueryParams:r})=>{const s=(w,B)=>{const F=(()=>{if(typeof window<"u"){const $=window.location.origin;return $.includes("ngrok-free.app")||$.includes("ngrok.io")?$:"http://localhost:5000"}return"http://localhost:5000"})();return Lt[w](`${F}${B}`).retry(2).withCredentials().timeout(3e4)},[h,f]=d.useState(!1),[l,C]=d.useState([]),[b,x]=d.useState([]),[u,p]=d.useState([]),[m,_]=d.useState([]),[y,D]=d.useState(0),[g,E]=d.useState(0),[c,L]=d.useState([]),[Q,te]=d.useState([]),[se,ce]=d.useState([]),[U,me]=d.useState([]),De=d.useCallback(()=>{const w=new URLSearchParams;n&&!i?w.append("model",n):i&&w.append("machine",i),w.append("limit","100"),w.append("chartLimit","200"),w.append("page","1");const B=r();if(Object.entries(B).forEach(([$,K])=>{w.append($,K)}),!B.date&&!B.dateRangeType){const $=M().subtract(7,"days").format("YYYY-MM-DD");w.append("date",$),w.append("dateRangeType","week"),w.append("defaultFilter","true")}const F=B.dateRangeType;return F==="month"?w.append("aggregateBy","day"):F==="year"&&w.append("aggregateBy","week"),w.toString()?`?${w.toString()}`:""},[n,i,r]),Fe=d.useCallback(()=>{const w=M(),B=[];for(let F=9;F>=0;F--){const $=w.subtract(F,"day").format("YYYY-MM-DD");if(!M($).isValid()){console.error("Invalid date generated:",$);continue}B.push({date:$,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.floor(Math.random()*30)+70,speed:Math.floor(Math.random()*5)+5,Machine_Name:i||(n?`${n}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(Math.random()*3)]})}return B},[i,n]),G=d.useCallback(async()=>{var w,B;if(!n){console.log("No machine model selected, skipping data fetch");return}f(!0);try{const F=De();console.log("API query string:",F);const $=await Promise.allSettled([s("get",`/api/testing-chart-production${F}`),s("get","/api/unique-dates-production").catch(()=>({body:[]})),s("get",`/api/sidecards-prod${F}`),s("get",`/api/sidecards-prod-rejet${F}`),s("get",`/api/machine-performance${F}`),s("get",`/api/hourly-trends${F}`),s("get",`/api/machine-oee-trends${F}`),s("get",`/api/speed-trends${F}`),s("get",`/api/shift-comparison${F}`),s("get",`/api/machine-daily-mould${F}`)]),[K,de,Ne,J,H,z,ve,Le,ot,Ce]=$;if(K.status==="fulfilled"&&K.value.body){const T=O(K.value),ae=(Array.isArray(T)?T:[]).map(ga);C(ae)}else console.log("No chart data available"),C([]);if(de.status==="fulfilled"){const T=O(de.value);_(T||[])}if(Ne.status==="fulfilled"){const T=O(Ne.value);D(((w=T[0])==null?void 0:w.goodqty)||0)}else D(0);if(J.status==="fulfilled"){const T=O(J.value);E(((B=T[0])==null?void 0:B.rejetqty)||0)}else E(0);if(H.status==="fulfilled"&&H.value.body){const T=O(H.value);x(T||[])}else console.log("No machine performance data available"),x([]);if(z.status==="fulfilled"){const T=O(z.value);ce(T||[])}const ft=ve.status==="fulfilled"&&ve.value.body?O(ve.value).reduce((T,q)=>(T[q.date]=parseFloat(q.oee)||0,T),{}):{},pt=Le.status==="fulfilled"&&Le.value.body?O(Le.value).reduce((T,q)=>{const ae=parseFloat(q.speed);return!isNaN(ae)&&ae>0&&(T[q.date]=ae),T},{}):{},xe=[...[...new Set([...Object.keys(ft),...Object.keys(pt)])]].sort((T,q)=>M(T).diff(M(q)));let Qe=xe;if(xe.length>0){const T=M(xe[xe.length-1]);Qe=xe.filter(q=>T.diff(M(q),"day")<=60)}const Be=Qe.map(T=>({date:T,oee:ft[T]||0,speed:pt[T]||null})).sort((T,q)=>M(T.date).diff(M(q.date)));if(Ce&&Ce.status==="fulfilled"&&Ce.value.body){const T=O(Ce.value);if(T.length>0)try{const q=T.map(N=>{const Ve=parseFloat(N.Good_QTY_Day||N.good||0),lt=parseFloat(N.Rejects_QTY_Day||N.reject||0),gt=parseFloat(N.OEE_Day||N.oee||0),it=parseFloat(N.Speed_Day||N.speed||0),Se=parseFloat(N.Availability_Rate_Day||N.availability||0),st=parseFloat(N.Performance_Rate_Day||N.performance||0),ct=parseFloat(N.Quality_Rate_Day||N.quality||0);let ke=null;try{const Pe=N.Date_Insert_Day||N.date;if(Pe)if(M(Pe).isValid())ke=M(Pe).format("YYYY-MM-DD");else{const Dt=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const St of Dt){const o=M(Pe,St);if(o.isValid()){ke=o.format("YYYY-MM-DD");break}}}ke||(console.warn(`Invalid date found: ${N.Date_Insert_Day||N.date}, using today's date instead`),ke=M().format("YYYY-MM-DD"))}catch(Pe){console.error("Error parsing date:",Pe),ke=M().format("YYYY-MM-DD")}const mt=ne(gt),Ue=ne(Se),yt=ne(st),Et=ne(ct);return{date:ke,oee:mt,speed:isNaN(it)?0:it,good:isNaN(Ve)?0:Ve,reject:isNaN(lt)?0:lt,Machine_Name:N.Machine_Name||"N/A",Shift:N.Shift||"N/A",availability:Ue,performance:yt,quality:Et}}).sort((N,Ve)=>M(N.date).diff(M(Ve.date)));if(q.some(N=>N.good>0||N.reject>0||N.oee>0||N.speed>0))p(q);else{console.warn("No valid data points found in processed mould data");const N=Fe();p(N)}}catch(q){console.error("Error processing mould data:",q),p(Be)}else if(console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback"),Be.length>0)p(Be);else{const q=Fe();p(q)}}else if(console.log("Machine daily mould API request failed or returned invalid data"),Ce&&Ce.status==="rejected"&&console.error("API error:",Ce.reason),Be.length>0)p(Be);else{const T=Fe();p(T),console.log("Using sample data for default dashboard state (IPS model)")}ve.status==="fulfilled"&&L(O(ve.value)||[]),Le.status==="fulfilled"&&te(O(Le.value)||[]),ot.status==="fulfilled"&&me(O(ot.value)||[])}catch(F){console.error("Error loading data:",F),D(0),E(0),C([]),x([])}finally{f(!1)}},[n,i,a,t,De,Fe]),W=d.useCallback(async()=>{var w,B;try{f(!0);const F=await Promise.allSettled([s("get","/api/sidecards-prod"),s("get","/api/sidecards-prod-rejet")]),[$,K]=F;if($.status==="fulfilled"){const de=O($.value);D(((w=de[0])==null?void 0:w.goodqty)||15e3)}else console.error("Failed to fetch good quantity:",$.reason),D(15e3);if(K.status==="fulfilled"){const de=O(K.value);E(((B=de[0])==null?void 0:B.rejetqty)||750)}else console.error("Failed to fetch rejected quantity:",K.reason),E(750)}catch(F){console.error("Error loading general data:",F),D(15e3),E(750)}finally{f(!1)}},[]),Mt=d.useCallback(()=>{let w=0;l.length>0&&(w=l.reduce((J,H)=>{let z=parseFloat(H.oee||0);return z=ne(z),J+z},0)/l.length);const B=y+g>0?g/(y+g)*100:0,F=y+g>0?y/(y+g)*100:0;let $=0;l.length>0&&($=l.reduce((J,H)=>{let z=parseFloat(H.availability||0);return z=ne(z),J+z},0)/l.length);let K=0;l.length>0&&(K=l.reduce((J,H)=>{let z=parseFloat(H.performance||0);return z=ne(z),J+z},0)/l.length);let de=0;return l.length>0&&(de=l.reduce((J,H)=>{let z=parseFloat(H.quality||0);return z=ne(z),J+z},0)/l.length),{avgTRS:w,rejectRate:B,qualityRate:F,avgAvailability:$,avgPerformance:K,avgQuality:de}},[l,y,g]);return d.useEffect(()=>{console.log("🔄 Data fetch effect triggered:",{selectedMachineModel:n,selectedMachine:i,dateFilter:a,dateRangeType:t}),n?(console.log("📊 Fetching production data for model:",n),G()):(console.log("📊 Fetching general data (no machine model selected)"),W())},[n,i,a,t,G,W]),{loading:h,chartData:l,machinePerformance:b,mergedData:u,uniqueDates:m,goodQty:y,rejetQty:g,oeeTrends:c,speedTrends:Q,hourlyTrends:se,shiftComparison:U,fetchData:G,fetchGeneralData:W,calculateStatistics:Mt}},Zt=d.createContext(),Ga=({children:n})=>{const i=Qa(),a=Va(),t=Ua({selectedMachineModel:i.selectedMachineModel,selectedMachine:i.selectedMachine,dateFilter:a.dateFilter,dateRangeType:a.dateRangeType,buildDateQueryParams:a.buildDateQueryParams}),r=t.calculateStatistics(),s={...i,...a,...t,...r,resetFilters:()=>{a.resetDateFilter(),a.setDateRangeType("day"),i.setSelectedMachineModel(""),i.setSelectedMachine("")},handleRefresh:()=>{t.fetchData()}};return e.createElement(Zt.Provider,{value:s},n)},Oa=()=>{const n=d.useContext(Zt);if(n===void 0)throw new Error("useProduction must be used within a ProductionProvider");return n},Ha=()=>{const[n,i]=d.useState({elasticsearch:!1,mysql:!0,primary:"mysql",lastCheck:null,retryCount:0}),[a,t]=d.useState(!1),r=d.useRef(null),s=d.useRef(null),h=d.useCallback(async()=>{var u,p;try{const _=await(await fetch("/api/graphql",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:`
            query TestDataSources {
              enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) {
                dataSource
                goodqty
              }
            }
          `})})).json();if(_.errors)return console.warn("GraphQL errors during data source test:",_.errors),{elasticsearch:!1,mysql:!0,primary:"mysql"};const y=((p=(u=_.data)==null?void 0:u.enhancedGetProductionSidecards)==null?void 0:p.dataSource)||"mysql";return{elasticsearch:y==="elasticsearch",mysql:y==="mysql"||y==="unknown",primary:y}}catch(m){return console.error("Error testing data sources:",m),{elasticsearch:!1,mysql:!0,primary:"mysql"}}},[]),f=d.useCallback(u=>{const p=n;i(m=>({...m,...u,lastCheck:new Date().toISOString(),retryCount:u.elasticsearch?0:m.retryCount+1})),p.primary!==u.primary&&(u.elasticsearch&&!p.elasticsearch?Tt.success({message:"Elasticsearch Restored",description:"Primary data source is now available. Performance improved.",icon:React.createElement(tt,{style:{color:"#52c41a"}}),duration:4}):!u.elasticsearch&&p.elasticsearch&&Tt.warning({message:"Elasticsearch Unavailable",description:"Switched to MySQL fallback. Data still available.",icon:React.createElement(ut,{style:{color:"#faad14"}}),duration:6}))},[n]),l=d.useCallback(()=>{if(a)return;t(!0);const u=async()=>{const p=await h();f(p)};u(),s.current=setInterval(u,3e4),console.log("🔍 Data source monitoring started")},[a,h,f]),C=d.useCallback(()=>{a&&(t(!1),s.current&&(clearInterval(s.current),s.current=null),r.current&&(clearTimeout(r.current),r.current=null),console.log("🔍 Data source monitoring stopped"))},[a]),b=d.useCallback(async()=>{if(n.retryCount>=5){Tt.error({message:"Elasticsearch Connection Failed",description:"Maximum retry attempts reached. Please check the service.",icon:React.createElement(Ct,{style:{color:"#ff4d4f"}}),duration:8});return}console.log(`🔄 Retrying Elasticsearch connection (attempt ${n.retryCount+1}/5)`);const u=await h();if(f(u),!u.elasticsearch){const p=Math.min(1e3*Math.pow(2,n.retryCount),3e4);r.current=setTimeout(b,p)}},[n.retryCount,h,f]),x=d.useCallback(async()=>{const u=await h();return f(u),u},[h,f]);return d.useEffect(()=>(l(),()=>C()),[l,C]),d.useEffect(()=>{if(!n.elasticsearch&&a&&n.retryCount<5){const u=Math.min(5e3*Math.pow(1.5,n.retryCount),3e4);r.current=setTimeout(b,u)}return()=>{r.current&&clearTimeout(r.current)}},[n.elasticsearch,n.retryCount,a,b]),{status:n,isMonitoring:a,startMonitoring:l,stopMonitoring:C,refreshStatus:x,retryElasticsearch:b}},{Text:re,Paragraph:Wa}=dt,Ka=({status:n,onRefresh:i,onRetry:a,isMonitoring:t=!0})=>{const r=()=>n.elasticsearch?"#52c41a":n.mysql?"#faad14":"#ff4d4f",s=()=>n.elasticsearch?"Elasticsearch":n.mysql?"MySQL Fallback":"Disconnected",h=()=>n.elasticsearch?e.createElement(tt,null):n.mysql?e.createElement(ut,null):e.createElement(Ct,null),l=n.elasticsearch?{level:"high",percent:95,text:"Optimal"}:n.mysql?{level:"medium",percent:75,text:"Backup"}:{level:"low",percent:25,text:"Limited"},C=e.createElement("div",{style:{width:300}},e.createElement(j,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(re,{strong:!0},"Data Source Status"),e.createElement("div",{style:{marginTop:8}},e.createElement(j,null,h(),e.createElement(re,null,s()),e.createElement(v,{color:n.elasticsearch?"green":n.mysql?"orange":"red"},n.primary.toUpperCase())))),e.createElement(At,{style:{margin:"12px 0"}}),e.createElement("div",null,e.createElement(re,{strong:!0},"Performance Level"),e.createElement("div",{style:{marginTop:8}},e.createElement(Ee,{percent:l.percent,strokeColor:r(),size:"small",format:()=>l.text}))),e.createElement("div",null,e.createElement(re,{strong:!0},"Service Status"),e.createElement("div",{style:{marginTop:8}},e.createElement(j,{direction:"vertical",size:"small",style:{width:"100%"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},e.createElement(re,null,"Elasticsearch:"),e.createElement(j,null,n.elasticsearch?e.createElement(zt,{style:{color:"#52c41a"}}):e.createElement(Ct,{style:{color:"#ff4d4f"}}),e.createElement(re,{type:n.elasticsearch?"success":"danger"},n.elasticsearch?"Active":"Unavailable"))),e.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},e.createElement(re,null,"MySQL:"),e.createElement(j,null,n.mysql?e.createElement(zt,{style:{color:"#52c41a"}}):e.createElement(Ct,{style:{color:"#ff4d4f"}}),e.createElement(re,{type:n.mysql?"success":"danger"},n.mysql?"Active":"Unavailable")))))),n.lastCheck&&e.createElement(e.Fragment,null,e.createElement(At,{style:{margin:"12px 0"}}),e.createElement("div",null,e.createElement(re,{strong:!0},"Last Check"),e.createElement("div",{style:{marginTop:4}},e.createElement(re,{type:"secondary",style:{fontSize:"12px"}},M(n.lastCheck).format("HH:mm:ss"))))),n.retryCount>0&&e.createElement("div",null,e.createElement(re,{strong:!0},"Retry Attempts"),e.createElement("div",{style:{marginTop:4}},e.createElement(re,{type:"warning"},n.retryCount,"/5"))),e.createElement(At,{style:{margin:"12px 0"}}),e.createElement(j,null,e.createElement(Ye,{size:"small",icon:e.createElement(Ta,null),onClick:i,type:"primary"},"Refresh"),!n.elasticsearch&&e.createElement(Ye,{size:"small",icon:e.createElement(tt,null),onClick:a,disabled:n.retryCount>=5},"Retry ES")),e.createElement(Wa,{style:{margin:0,fontSize:"11px"},type:"secondary"},n.elasticsearch?"Using high-performance Elasticsearch for optimal data retrieval.":"Using MySQL fallback to ensure continuous data availability.")));return e.createElement(ca,{content:C,title:"Data Source Monitor",trigger:"hover",placement:"bottomLeft"},e.createElement(he,{title:`Data source: ${n.primary} • Click for details`},e.createElement($t,{count:e.createElement(j,{size:4},h(),e.createElement(re,{style:{color:"white",fontSize:"11px"}},s()),t&&e.createElement("div",{style:{width:6,height:6,borderRadius:"50%",backgroundColor:"#52c41a",animation:"pulse 2s infinite"}})),style:{backgroundColor:r(),borderRadius:"12px",height:"24px",lineHeight:"24px",cursor:"pointer",paddingLeft:"8px",paddingRight:"8px"}})))},Ja=(n,i,a,t,r,s,h)=>[{title:"Production Totale",value:ze(n,"Pcs"),rawValue:n,suffix:"Pcs",icon:e.createElement(Aa,null),color:R.PRIMARY_BLUE,description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:ze(i,"Kg"),rawValue:i,suffix:"Kg",icon:e.createElement(Na,null),color:R.PRIMARY_BLUE,description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:ze(a,"%"),rawValue:a,suffix:"%",icon:e.createElement(qt,null),color:R.PRIMARY_BLUE,description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:ze(t,"%"),rawValue:t,suffix:"%",icon:e.createElement(va,null),color:R.PRIMARY_BLUE,description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:ze(r,"%"),rawValue:r,suffix:"%",icon:e.createElement(tt,null),color:R.PRIMARY_BLUE,description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:ze(s,"%"),rawValue:s,suffix:"%",icon:e.createElement(ka,null),color:R.PRIMARY_BLUE,description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:ze(h,"%"),rawValue:h,suffix:"%",icon:e.createElement(zt,null),color:R.PRIMARY_BLUE,description:"Pourcentage de pièces bonnes sur la production totale"}],{Text:vt}=dt,Xa=(n,i)=>[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>e.createElement(j,null,e.createElement(ut,{style:{color:n[0]}}),e.createElement(vt,{strong:!0},a||"N/A")),sorter:(a,t)=>(a.Machine_Name||"").localeCompare(t.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:a=>{if(!a)return e.createElement(vt,null,"N/A");const t=new Date(a);return e.createElement(vt,null,t.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}))},sorter:(a,t)=>{const r=new Date(a.Date_Insert_Day||0),s=new Date(t.Date_Insert_Day||0);return r-s}},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:a=>e.createElement(v,{color:"green"},et(parseFloat(a)||0)," h"),sorter:(a,t)=>(parseFloat(a.Run_Hours_Day)||0)-(parseFloat(t.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:a=>e.createElement(v,{color:"orange"},et(parseFloat(a)||0)," h"),sorter:(a,t)=>(parseFloat(a.Down_Hours_Day)||0)-(parseFloat(t.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:a=>e.createElement(v,{color:"green"},Pt(parseInt(a)||0)," pcs"),sorter:(a,t)=>(parseInt(a.Good_QTY_Day)||0)-(parseInt(t.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:a=>e.createElement(v,{color:"red"},Pt(parseInt(a)||0)," pcs"),sorter:(a,t)=>(parseInt(a.Rejects_QTY_Day)||0)-(parseInt(t.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:a=>e.createElement(v,{color:"blue"},et(parseFloat(a)||0)),sorter:(a,t)=>(parseFloat(a.Speed_Day)||0)-(parseFloat(t.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:a=>{const t=i(a);return e.createElement(he,{title:`${_e(t,1)}% de disponibilité`},e.createElement(Ee,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${_e(r,1)}%`:"0,0%"}))},sorter:(a,t)=>i(a.Availability_Rate_Day)-i(t.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:a=>{const t=i(a);return e.createElement(he,{title:`${t.toFixed(1)}% de performance`},e.createElement(Ee,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>i(a.Performance_Rate_Day)-i(t.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:a=>{const t=i(a);return e.createElement(he,{title:`${t.toFixed(1)}% de qualité`},e.createElement(Ee,{percent:t,size:"small",status:t>90?"success":t>80?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>i(a.Quality_Rate_Day)-i(t.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:a=>{const t=i(a);return e.createElement(he,{title:`${t.toFixed(1)}% de TRS`},e.createElement(Ee,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>i(a.OEE_Day)-i(t.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:a=>e.createElement(v,{color:"blue"},a||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(a,t)=>t.Shift===a},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:a=>e.createElement(v,{color:"purple"},a||"N/A")},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:a=>e.createElement(v,{color:"cyan"},a||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:a=>e.createElement(v,{color:"magenta"},a||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:a=>e.createElement(v,{color:"gold"},a||"N/A")},{title:"Actions",key:"actions",fixed:"right",width:80,render:()=>e.createElement(ma,{menu:{items:[{key:"1",icon:e.createElement(Jt,null),label:"Voir tendances"},{key:"2",icon:e.createElement(da,null),label:"Paramètres"},{key:"3",icon:e.createElement(xt,null),label:"Exporter données"}]},trigger:["click"]},e.createElement(Ye,{type:"text",icon:e.createElement(ya,null)}))}],{Text:kt}=dt,Za=(n,i=[])=>{const a=t=>{if(t==null||t==="")return 0;if(typeof t=="number"&&!isNaN(t))return t<=1&&t>0?t*100:t;if(typeof t=="string"){const r=parseFloat(t.replace(",","."));if(!isNaN(r))return r<=1&&r>0?r*100:r}return 0};return[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:t=>e.createElement(j,null,e.createElement(ut,{style:{color:n[0]}}),e.createElement(kt,{strong:!0},t||"N/A")),filters:Array.from(new Set(i.map(t=>t.Machine_Name||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,r)=>r.Machine_Name===t||t==="N/A"&&!r.Machine_Name,sorter:(t,r)=>(t.Machine_Name||"").localeCompare(r.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:t=>{if(!t)return e.createElement(kt,null,"N/A");const r=new Date(t);return e.createElement(j,null,e.createElement(Ia,{style:{color:n[1]}}),e.createElement(kt,null,r.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})))},sorter:(t,r)=>{const s=new Date(t.Date_Insert_Day||0),h=new Date(r.Date_Insert_Day||0);return s-h},defaultSortOrder:"descend"},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:t=>e.createElement(v,{color:"green"},et(parseFloat(t)||0)," h"),sorter:(t,r)=>(parseFloat(t.Run_Hours_Day)||0)-(parseFloat(r.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:t=>e.createElement(v,{color:"orange"},et(parseFloat(t)||0)," h"),sorter:(t,r)=>(parseFloat(t.Down_Hours_Day)||0)-(parseFloat(r.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:t=>e.createElement(v,{color:"green"},Pt(parseInt(t)||0)," pcs"),sorter:(t,r)=>(parseInt(t.Good_QTY_Day)||0)-(parseInt(r.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:t=>e.createElement(v,{color:"red"},Pt(parseInt(t)||0)," pcs"),sorter:(t,r)=>(parseInt(t.Rejects_QTY_Day)||0)-(parseInt(r.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:t=>e.createElement(v,{color:"blue"},et(parseFloat(t)||0)),sorter:(t,r)=>(parseFloat(t.Speed_Day)||0)-(parseFloat(r.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:t=>{const r=a(t);return e.createElement(he,{title:`${_e(r,1)}% de disponibilité`},e.createElement(Ee,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${_e(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Availability_Rate_Day)-a(r.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:t=>{const r=a(t);return e.createElement(he,{title:`${_e(r,1)}% de performance`},e.createElement(Ee,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${_e(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Performance_Rate_Day)-a(r.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:t=>{const r=a(t);return e.createElement(he,{title:`${_e(r,1)}% de qualité`},e.createElement(Ee,{percent:r,size:"small",status:r>90?"success":r>80?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${_e(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Quality_Rate_Day)-a(r.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:t=>{const r=a(t);return e.createElement(he,{title:`${_e(r,1)}% de TRS`},e.createElement(Ee,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${_e(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.OEE_Day)-a(r.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:t=>e.createElement(v,{color:"blue"},t||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(t,r)=>r.Shift===t},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:t=>e.createElement(v,{color:"purple"},t||"N/A"),filters:Array.from(new Set(i.map(t=>t.Part_Number||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,r)=>r.Part_Number===t||t==="N/A"&&!r.Part_Number},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:t=>e.createElement(v,{color:"cyan"},t||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:t=>e.createElement(v,{color:"magenta"},t||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:t=>e.createElement(v,{color:"gold"},t||"N/A")}]},Vt=(n,i=!1,a=0)=>{if(!n)return"N/A";try{const t=M(n);return t.isValid()?i?a>100?t.format("MM/DD"):a>50?t.format("MM/DD/YY"):t.format("DD/MM/YYYY"):a>30?t.format("MM/DD"):t.format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",t),"N/A"}},we=[R.PRIMARY_BLUE,R.SECONDARY_BLUE,R.CHART_TERTIARY,R.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],Qt=d.memo(({data:n,title:i,dataKey:a,color:t,label:r="Quantité",tooltipLabel:s="Quantité",isKg:h=!1,height:f=300,enhanced:l=!1,expanded:C=!1,zoom:b=1,selectedDataPoints:x=[],chartConfig:u={},dimensions:p={},isModal:m=!1})=>{const{charts:_,theme:y}=at(),D=Re({charts:_,theme:y});if(!n||n.length===0)return e.createElement("div",{style:{height:f,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée disponible"}));const g=D.responsiveContainerProps,E=D.gridConfig,c=D.axisConfig,L=D.tooltipConfig,Q=D.legendConfig,te=D.displayConfig,se=D.height||f,ce=D.margins,U=p.margin||ce,me=p.fontSize||(l?14:12),De=p.labelAngle||(l?-45:0),Fe=p.labelHeight||(l?100:60);return e.createElement(ge,{...g,height:se},e.createElement(qe,{data:n,margin:U},e.createElement(oe,{...E}),e.createElement(le,{dataKey:"date",...c,tick:{fill:"#666",fontSize:me},tickFormatter:G=>Vt(G,C||l,n.length),interval:u.labelInterval!==void 0?u.labelInterval:l?0:"preserveStartEnd",angle:De,textAnchor:De!==0?"end":"middle",height:Fe,minTickGap:C?5:10}),e.createElement(ie,{...c,tick:{fontSize:me},tickFormatter:G=>G.toLocaleString(),label:{value:h?`${r} (kg)`:r,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:me}}}),e.createElement(Z,{...L,formatter:G=>{const W=parseFloat(G);return[isNaN(W)?"N/A":W.toLocaleString(),h?`${s} (kg)`:s]},labelFormatter:G=>{try{return G&&M(G).isValid()?`Date: ${M(G).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),(l||_.showLegend)&&e.createElement(ee,{...Q}),e.createElement(je,{dataKey:a,name:s,fill:t,maxBarSize:u.maxBarSize||(l?60:40),radius:l||C?[4,4,0,0]:[0,0,0,0],...D.getBarElementConfig(t)},te.showDataLabels&&e.createElement($a,{dataKey:a,position:"top",formatter:G=>{const W=parseFloat(G);return isNaN(W)?"":W.toLocaleString()},style:{fill:"#666",fontSize:"10px"}}))))}),er=d.memo(({data:n,title:i,dataKey:a,color:t,label:r="Quantité",tooltipLabel:s="Quantité",isKg:h=!1,height:f=300,enhanced:l=!1,expanded:C=!1,zoom:b=1,selectedDataPoints:x=[],chartConfig:u={},dimensions:p={},isModal:m=!1})=>{var E;if(i&&i.includes("Temps d'arrêt")&&(n==null?void 0:n.length)>0&&console.log("EnhancedShiftBarChart - Downtime data received:",n.map(c=>({Shift:c.Shift,[a]:c[a]}))),!n||n.length===0)return e.createElement("div",{style:{height:f,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée disponible"}));const _=p.margin||(l?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),y=p.fontSize||(l?14:12),D=p.labelAngle||(l?-45:0),g=p.labelHeight||(l?100:60);return e.createElement(ge,{width:"100%",height:f},e.createElement(qe,{data:n,margin:_},e.createElement(oe,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(le,{dataKey:"Shift",tick:{fill:"#666",fontSize:y},tickFormatter:c=>c||"N/A",interval:u.labelInterval!==void 0?u.labelInterval:l?0:"preserveStartEnd",angle:D,textAnchor:D!==0?"end":"middle",height:g,minTickGap:C?5:10}),e.createElement(ie,{tick:{fontSize:y},tickFormatter:c=>c.toLocaleString(),domain:i&&i.includes("Temps d'arrêt")?[0,"dataMax"]:["auto","auto"],label:{value:h?`${r} (kg)`:r,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:y}}}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:l?14:12},formatter:c=>{const L=parseFloat(c);return[isNaN(L)?"N/A":L.toLocaleString(),h?`${s} (kg)`:s]},labelFormatter:c=>`Équipe: ${c}`}),l&&e.createElement(ee,null),e.createElement(je,{dataKey:a,name:s,fill:t,maxBarSize:u.maxBarSize||(l?60:40),radius:l||C?[4,4,0,0]:[0,0,0,0],label:(E=enhancedChartConfig.displayConfig)!=null&&E.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),ea=d.memo(({data:n,color:i=we[0],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:h=[],chartConfig:f={},dimensions:l={},isModal:C=!1})=>{var D;const{charts:b,theme:x}=at(),u=Re({charts:b,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée TRS disponible"}));const p=l.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=l.fontSize||(t?14:12),_=l.labelAngle||(t?-45:0),y=l.labelHeight||(t?100:60);return e.createElement(ge,{width:"100%",height:a},e.createElement(rt,{data:n,margin:p},e.createElement(oe,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(le,{dataKey:"date",tick:{fill:"#666",fontSize:m},tickFormatter:g=>Vt(g,r||t,n.length),interval:f.labelInterval!==void 0?f.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(ie,{tick:{fontSize:m},tickFormatter:g=>`${g}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>{let E=parseFloat(g);const c=!isNaN(E);return c&&E<=1&&E>0&&(E=E*100),[c?`${E.toFixed(2)}%`:`${g}%`,"TRS"]},labelFormatter:g=>{try{return g&&M(g).isValid()?`Date: ${M(g).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(ee,null),e.createElement(nt,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:f.strokeWidth||(t||r?3:2),dot:{r:f.dotSize||(t||r?6:4),fill:i},activeDot:{r:(f.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(D=u.displayConfig)!=null&&D.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),ta=d.memo(({data:n,color:i=we[0],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:h=[],chartConfig:f={},dimensions:l={},isModal:C=!1})=>{var D;const{charts:b,theme:x}=at(),u=Re({charts:b,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée TRS disponible"}));const p=l.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=l.fontSize||(t?14:12),_=l.labelAngle||(t?-45:0),y=l.labelHeight||(t?100:60);return e.createElement(ge,{width:"100%",height:a},e.createElement(rt,{data:n,margin:p},e.createElement(oe,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(le,{dataKey:"Shift",tick:{fill:"#666",fontSize:m},tickFormatter:g=>g||"N/A",interval:f.labelInterval!==void 0?f.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(ie,{tick:{fontSize:m},tickFormatter:g=>`${g}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>{let E=parseFloat(g);const c=!isNaN(E);return c&&E<=1&&E>0&&(E=E*100),[c?`${E.toFixed(2)}%`:`${g}%`,"TRS"]},labelFormatter:g=>`Équipe: ${g}`}),t&&e.createElement(ee,null),e.createElement(nt,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:f.strokeWidth||(t||r?3:2),dot:{r:f.dotSize||(t||r?6:4),fill:i},activeDot:{r:(f.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(D=u.displayConfig)!=null&&D.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),aa=d.memo(({data:n,color:i=we[5],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:h=[],chartConfig:f={},dimensions:l={},isModal:C=!1})=>{var D;const{charts:b,theme:x}=at(),u=Re({charts:b,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée de performance disponible"}));const p=l.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=l.fontSize||(t?14:12),_=l.labelAngle||(t?-45:0),y=l.labelHeight||(t?100:60);return e.createElement(ge,{width:"100%",height:a},e.createElement(rt,{data:n,margin:p},e.createElement(oe,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(le,{dataKey:"Shift",tick:{fill:"#666",fontSize:m},tickFormatter:g=>g||"N/A",interval:f.labelInterval!==void 0?f.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(ie,{tick:{fontSize:m},tickFormatter:g=>`${g}%`,domain:[0,100],label:{value:"Performance (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>{let E=parseFloat(g);const c=!isNaN(E);return c&&E<=1&&E>0&&(E=E*100),[c?`${E.toFixed(2)}%`:`${g}%`,"Performance"]},labelFormatter:g=>`Équipe: ${g}`}),t&&e.createElement(ee,null),e.createElement(nt,{type:"monotone",dataKey:"performance",name:"Performance",stroke:i,strokeWidth:f.strokeWidth||(t||r?3:2),dot:{r:f.dotSize||(t||r?6:4),fill:i},activeDot:{r:(f.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(D=u.displayConfig)!=null&&D.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),ra=d.memo(({data:n,color:i=we[1],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:h=[],chartConfig:f={},dimensions:l={},isModal:C=!1})=>{var D;const{charts:b,theme:x}=at(),u=Re({charts:b,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée de cycle disponible"}));const p=l.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),m=l.fontSize||(t?14:12),_=l.labelAngle||(t?-45:0),y=l.labelHeight||(t?100:60);return e.createElement(ge,{width:"100%",height:a},e.createElement(rt,{data:n,margin:p},e.createElement(oe,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(le,{dataKey:"date",tick:{fill:"#666",fontSize:m},tickFormatter:g=>Vt(g,r||t,n.length),interval:f.labelInterval!==void 0?f.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(ie,{tick:{fontSize:m},tickFormatter:g=>`${g}s`,label:{value:"Cycle De Temps (s)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:m}}}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>[typeof g=="number"&&!isNaN(g)?`${g.toFixed(2)}s`:`${g}s`,"Cycle De Temps"],labelFormatter:g=>{try{return g&&M(g).isValid()?`Date: ${M(g).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(ee,null),e.createElement(nt,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:i,strokeWidth:f.strokeWidth||(t||r?3:2),dot:{r:f.dotSize||(t||r?6:4),fill:i},activeDot:{r:(f.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(D=u.displayConfig)!=null&&D.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),na=d.memo(({data:n,dataKey:i="value",nameKey:a="name",colors:t=we,height:r,enhanced:s=!1,zoom:h=1,selectedDataPoints:f=[]})=>{var b;const l=Re({chartType:"pie",allowedTypes:["pie"]});if(!n||n.length===0)return e.createElement("div",{style:{height:r||l.height,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée disponible"}));const C=r||l.height;return e.createElement(ge,{width:"100%",height:C},e.createElement(wa,{margin:l.margins},e.createElement(Fa,{data:n,dataKey:i,nameKey:a,cx:"50%",cy:"50%",innerRadius:s?80:60,outerRadius:s?120:80,paddingAngle:s?8:5,label:s?({name:x,percent:u})=>`${x}: ${(u*100).toFixed(1)}%`:!1,labelLine:s},n.map((x,u)=>e.createElement(La,{key:`cell-${u}`,fill:t[u%t.length]}))),e.createElement(Z,{...l.tooltipConfig,formatter:(x,u)=>[typeof x=="number"?x.toLocaleString():x,u]}),((b=l.charts)==null?void 0:b.showLegend)&&e.createElement(ee,{...l.legendConfig,layout:s?"horizontal":"vertical",verticalAlign:s?"bottom":"middle",align:s?"center":"right",wrapperStyle:{paddingLeft:s?0:24,paddingTop:s?20:0,fontSize:s?14:12}})))}),tr=d.memo(({data:n,color:i=we[2],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:s=[]})=>{var x;const h=Re({chartType:"bar",allowedTypes:["bar"]}),f=a||h.height;if(!n||n.length===0)return e.createElement("div",{style:{height:f,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée de production disponible"}));const l=n.reduce((u,p)=>{const m=p.Machine_Name;return u[m]||(u[m]={Machine_Name:m,production:0}),u[m].production+=Number(p.production)||0,u},{}),C=Object.values(l),b=t?14:12;return e.createElement(ge,{width:"100%",height:f},e.createElement(qe,{data:C,margin:h.margins},e.createElement(oe,{...h.gridConfig}),e.createElement(le,{dataKey:"Machine_Name",...h.axisConfig,tick:{fontSize:b},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(ie,{...h.axisConfig,tick:{fontSize:b},tickFormatter:u=>u.toLocaleString(),label:{value:"Production (pcs)",angle:-90,position:"insideLeft",style:{fontSize:b}}}),e.createElement(Z,{...h.tooltipConfig,formatter:u=>[typeof u=="number"&&!isNaN(u)?Number.isInteger(u)?u.toLocaleString():u.toFixed(2):u,"Production"]}),((x=h.charts)==null?void 0:x.showLegend)&&e.createElement(ee,{...h.legendConfig}),e.createElement(je,{dataKey:"production",name:"Production",fill:i,radius:t?[4,4,0,0]:[0,0,0,0]})))}),oa=d.memo(({data:n,color:i=we[4],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:s=[]})=>{var x;const h=Re({chartType:"bar",allowedTypes:["bar"]}),f=a||h.height;if(!n||n.length===0)return e.createElement("div",{style:{height:f,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée de rejets disponible"}));const l=n.reduce((u,p)=>{const m=p.Machine_Name;return u[m]||(u[m]={Machine_Name:m,rejects:0}),u[m].rejects+=Number(p.rejects)||0,u},{}),C=Object.values(l),b=t?14:12;return e.createElement(ge,{width:"100%",height:f},e.createElement(qe,{data:C,margin:h.margins},e.createElement(oe,{...h.gridConfig}),e.createElement(le,{dataKey:"Machine_Name",...h.axisConfig,tick:{fontSize:b},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(ie,{...h.axisConfig,tick:{fontSize:b},tickFormatter:u=>u.toLocaleString(),label:{value:"Rejets (kg)",angle:-90,position:"insideLeft",style:{fontSize:b}}}),e.createElement(Z,{...h.tooltipConfig,formatter:u=>[typeof u=="number"&&!isNaN(u)?Number.isInteger(u)?u.toLocaleString():u.toFixed(2):u,"Rejets"]}),((x=h.charts)==null?void 0:x.showLegend)&&e.createElement(ee,{...h.legendConfig}),e.createElement(je,{dataKey:"rejects",name:"Rejets",fill:i,radius:t?[4,4,0,0]:[0,0,0,0]})))}),la=d.memo(({data:n,color:i=we[5],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:s=[]})=>{if(Re({chartType:"bar",allowedTypes:["bar"]}),!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(X,{description:"Aucune donnée TRS disponible"}));const h=n.reduce((b,x)=>{const u=x.Machine_Name;b[u]||(b[u]={Machine_Name:u,trs:0,count:0});let p=Number(x.oee)||0;return p>0&&p<=1&&(p=p*100),b[u].trs+=p,b[u].count+=1,b},{}),f=Object.values(h).map(b=>({Machine_Name:b.Machine_Name,trs:b.count>0?b.trs/b.count:0})),l=t?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},C=t?14:12;return e.createElement(ge,{width:"100%",height:a},e.createElement(qe,{data:f,margin:l},e.createElement(oe,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(le,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:C},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(ie,{tick:{fontSize:C},tickFormatter:b=>`${b.toFixed(1)}%`,label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:C}},domain:[0,100]}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:b=>{let x=parseFloat(b);return isNaN(x)?["N/A","TRS"]:[`${x.toFixed(1)}%`,"TRS"]}}),t&&e.createElement(ee,null),e.createElement(je,{dataKey:"trs",name:"TRS",fill:i,radius:t?[4,4,0,0]:[0,0,0,0]})))});Qt.displayName="EnhancedQuantityBarChart";er.displayName="EnhancedShiftBarChart";ea.displayName="EnhancedTRSLineChart";ta.displayName="EnhancedShiftTRSLineChart";aa.displayName="EnhancedPerformanceLineChart";ra.displayName="EnhancedCycleTimeLineChart";na.displayName="EnhancedPieChart";tr.displayName="EnhancedMachineProductionChart";oa.displayName="EnhancedMachineRejectsChart";la.displayName="EnhancedMachineTRSChart";const{Text:Gr}=dt,ar=({data:n,colors:i,dateRangeType:a,dateFilter:t,formatDateRange:r})=>{const{settings:s}=at();Re({chartType:"bar",allowedTypes:["bar","line"]});const h=d.useMemo(()=>{const C=Ya(s);return C===null?i||["#1890ff","#52c41a","#faad14","#f5222d","#722ed1"]:C},[s,i]),f=C=>{console.log(`Chart expanded: ${C}`)},l=C=>{console.log(`Chart collapsed: ${C}`)};return e.createElement(e.Fragment,null,e.createElement(I,{span:24},e.createElement(be,{gutter:[24,24]},e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:`Quantité Bonne - ${a==="day"?"Journalière":a==="week"?"Hebdomadaire":"Mensuelle"}`,data:n,chartType:"bar",expandMode:"modal",onExpand:()=>f("quantity-good"),onCollapse:()=>l("quantity-good"),exportEnabled:!0,cardProps:{extra:e.createElement(v,{color:t?"blue":"green"},r(t,a))}},e.createElement(Qt,{data:n,title:"Quantité Bonne",dataKey:"good",color:h[2],tooltipLabel:"Quantité bonne"}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:`Quantité Rejetée - ${a==="day"?"Journalière":a==="week"?"Hebdomadaire":"Mensuelle"}`,data:n,chartType:"bar",expandMode:"modal",onExpand:()=>f("quantity-reject"),onCollapse:()=>l("quantity-reject"),exportEnabled:!0,cardProps:{extra:e.createElement(v,{color:t?"blue":"green"},r(t,a))}},e.createElement(Qt,{data:n,title:"Quantité Rejetée",dataKey:"reject",color:h[4],label:"Quantité",tooltipLabel:"Quantité rejetée",isKg:!0}))))),e.createElement(I,{xs:24,md:24},e.createElement(be,{gutter:[24,24]},e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Tendances TRS (Taux de Rendement Synthétique)",data:n,chartType:"line",expandMode:"modal",onExpand:()=>f("trs-trends"),onCollapse:()=>l("trs-trends"),exportEnabled:!0,cardProps:{extra:e.createElement(v,{color:"cyan"},"Évolution TRS")}},e.createElement(ea,{data:n,color:h[0]}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Tendances Cycle De Temps",data:n,chartType:"line",expandMode:"modal",onExpand:()=>f("cycle-time-trends"),onCollapse:()=>l("cycle-time-trends"),exportEnabled:!0,cardProps:{extra:e.createElement(v,{color:"orange"},"Évolution Cycle")}},e.createElement(ra,{data:n,color:h[1]}))))))},Bt=d.memo(({data:n,title:i,dataKey:a,color:t,label:r="Valeur",tooltipLabel:s,isKg:h=!1,chartType:f,allowedTypes:l=["bar","line"],enhanced:C=!1,expanded:b=!1,isModal:x=!1,height:u=300,...p})=>{const m=Xt({fallbackType:"bar",allowedTypes:l,propChartType:f}),_=m.chartType,y=d.useMemo(()=>!Array.isArray(n)||n.length===0?[]:n.map(c=>{const L=c.Shift||c.shift||c.name||c.label||"N/A",Q=c[a]||0;return{...c,shift:L,[a]:Number(Q)||0,originalData:c}}).filter(c=>c[a]!==void 0&&c[a]!==null),[n,a]),D=({active:c,payload:L,label:Q})=>{if(c&&L&&L.length){const se=L[0].value,ce=h?`${se.toLocaleString()} kg`:se.toLocaleString(),U=m.tooltipConfig;return e.createElement("div",{style:U.contentStyle},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:m.getTextColor()}},`Équipe: ${Q}`),e.createElement("p",{style:{margin:0,color:t||m.getPrimaryColor()}},`${s||Q}: ${ce}`))}return null},g={data:y,margin:m.margins,...p},E=()=>{const c=t||m.getPrimaryColor();switch(_){case"line":return e.createElement(rt,{...g},e.createElement(oe,{...m.gridConfig}),e.createElement(le,{dataKey:"shift",...m.axisConfig,tick:{fontSize:11}}),e.createElement(ie,{...m.axisConfig,tick:{fontSize:11},label:{value:h?"Quantité (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(Z,{content:e.createElement(D,null)}),m.displayConfig.showLegend&&e.createElement(ee,{...m.legendConfig}),e.createElement(nt,{type:"monotone",dataKey:a,name:s||i,...m.getLineElementConfig(c)}));case"bar":default:return e.createElement(qe,{...g},e.createElement(oe,{...m.gridConfig}),e.createElement(le,{dataKey:"shift",...m.axisConfig,tick:{fontSize:11}}),e.createElement(ie,{...m.axisConfig,tick:{fontSize:11},label:{value:h?"Quantité (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(Z,{content:e.createElement(D,null)}),m.displayConfig.showLegend&&e.createElement(ee,{...m.legendConfig}),e.createElement(je,{dataKey:a,name:s||i,...m.getBarElementConfig(c),maxBarSize:C||b?60:40}))}};return!y||y.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:u,width:"100%"}},e.createElement(X,{description:"Aucune donnée disponible pour les équipes",image:X.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{width:"100%",height:u}},e.createElement(ge,{...m.responsiveContainerProps},E()))});Bt.displayName="EnhancedShiftChart";const ia=d.memo(({data:n,title:i,dataKey:a="production",color:t,label:r="Production",tooltipLabel:s,isKg:h=!0,chartType:f,allowedTypes:l=["bar","line"],enhanced:C=!1,expanded:b=!1,isModal:x=!1,height:u=300,...p})=>{const m=Xt({fallbackType:"bar",allowedTypes:l,propChartType:f}),_=m.chartType,y=d.useMemo(()=>!Array.isArray(n)||n.length===0?[]:n.map(c=>{const L=c.Machine||c.machine||c.name||c.label||"N/A",Q=c[a]||0;return{...c,machine:L,[a]:Number(Q)||0,originalData:c}}).filter(c=>c[a]!==void 0&&c[a]!==null),[n,a]),D=({active:c,payload:L,label:Q})=>{const te=m.theme||{};if(c&&L&&L.length){const ce=L[0].value,U=h?`${ce.toLocaleString()} kg`:ce.toLocaleString();return e.createElement("div",{style:{backgroundColor:te.darkMode?"#1f1f1f":"#ffffff",border:`1px solid ${t||R.PRIMARY_BLUE}`,borderRadius:"8px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:te.darkMode?"#ffffff":"#000000"}},`Machine: ${Q}`),e.createElement("p",{style:{margin:0,color:t||R.PRIMARY_BLUE}},`${s||Q}: ${U}`))}return null},g={data:y,margin:m.margins,...p},E=()=>{var L,Q;const c=t||R.PRIMARY_BLUE;switch(_){case"line":return e.createElement(rt,{...g},e.createElement(oe,{...m.gridConfig}),e.createElement(le,{dataKey:"machine",...m.axisConfig,tick:{fontSize:11},angle:-45,textAnchor:"end",height:80}),e.createElement(ie,{...m.axisConfig,tick:{fontSize:11},label:{value:h?"Production (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(Z,{content:e.createElement(D,null)}),((L=m.charts)==null?void 0:L.showLegend)&&e.createElement(ee,{...m.legendConfig}),e.createElement(nt,{type:"monotone",dataKey:a,name:s||i,...m.getLineElementConfig(c)}));case"bar":default:return e.createElement(qe,{...g},e.createElement(oe,{...m.gridConfig}),e.createElement(le,{dataKey:"machine",...m.axisConfig,tick:{fontSize:11},angle:-45,textAnchor:"end",height:80}),e.createElement(ie,{...m.axisConfig,tick:{fontSize:11},label:{value:h?"Production (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(Z,{content:e.createElement(D,null)}),((Q=m.charts)==null?void 0:Q.showLegend)&&e.createElement(ee,{...m.legendConfig}),e.createElement(je,{dataKey:a,name:s||i,...m.getBarElementConfig(c),maxBarSize:C||b?60:40}))}};return!y||y.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:u,width:"100%"}},e.createElement(X,{description:"Aucune donnée disponible pour les machines",image:X.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{width:"100%",height:u}},e.createElement(ge,{...m.responsiveContainerProps},E()))});ia.displayName="EnhancedMachineChart";const sa=({dataSource:n=[],columns:i=[],loading:a=!1,title:t,totalRecords:r=0,pageSize:s=100,currentPage:h=1,onPageChange:f,onPageSizeChange:l,exportEnabled:C=!1,onExport:b,maxRecordsWarning:x=1e3,performanceMode:u=!1,rowKey:p="id",scroll:m={x:1300},expandable:_,...y})=>{const[D,g]=d.useState(!1),E=d.useMemo(()=>{const U=n.length,me=U*.1,De=U>x;return{recordCount:U,estimatedRenderTime:me,isLargeDataset:De,performanceLevel:U>2e3?"poor":U>1e3?"warning":"good"}},[n.length,x]),c=d.useCallback(async()=>{if(b){g(!0);try{await b({data:n,totalRecords:r,currentPage:h,pageSize:s})}catch(U){console.error("Export failed:",U)}finally{g(!1)}}},[b,n,r,h,s]),L=d.useMemo(()=>({current:h,pageSize:s,total:r,showSizeChanger:!0,showQuickJumper:r>1e3,pageSizeOptions:["50","100","200","500"],showTotal:(U,me)=>`${me[0]}-${me[1]} sur ${U} enregistrements`,onChange:f,onShowSizeChange:l,size:"default"}),[h,s,r,f,l]),Q=()=>E.isLargeDataset?e.createElement(fa,{message:`Attention: ${E.recordCount} enregistrements`,description:`Le chargement peut prendre ${Math.ceil(E.estimatedRenderTime/1e3)}s. Considérez l'utilisation de filtres pour améliorer les performances.`,type:"warning",showIcon:!0,icon:e.createElement(za,null),style:{marginBottom:16},action:e.createElement(j,null,e.createElement(Ye,{size:"small",type:"link"},"Optimiser les filtres"))}):null,te=d.useMemo(()=>t?e.createElement(j,null,e.createElement("span",null,t),e.createElement(v,{color:E.performanceLevel==="good"?"green":E.performanceLevel==="warning"?"orange":"red"},E.recordCount," enregistrements"),u&&e.createElement(he,{title:`Temps de rendu estimé: ${E.estimatedRenderTime.toFixed(1)}ms`},e.createElement(jt,{style:{color:"#1890ff"}}))):null,[t,E,u]),se=d.useMemo(()=>e.createElement(j,null,C&&e.createElement(Ye,{icon:e.createElement(xt,null),onClick:c,loading:D,disabled:n.length===0},"Exporter"),u&&e.createElement(v,{color:"blue"},"Mode Performance")),[C,c,D,n.length,u]),ce=d.useMemo(()=>({...y,dataSource:n,columns:i,loading:a,rowKey:p,scroll:E.isLargeDataset?{...m,y:400}:m,pagination:r>s?L:!1,size:E.isLargeDataset?"small":"middle",expandable:_,title:te?()=>te:void 0,extra:se,virtual:E.isLargeDataset,sticky:!0,showSorterTooltip:!1,rowSelection:y.rowSelection?{...y.rowSelection,preserveSelectedRowKeys:!0}:void 0}),[y,n,i,a,p,m,E.isLargeDataset,r,s,L,_,te,se]);return e.createElement("div",null,e.createElement(Q,null),e.createElement(Kt,{...ce}),r>1e3&&e.createElement("div",{style:{marginTop:16,textAlign:"center"}},e.createElement(ua,{...L,simple:!1,showLessItems:!1})))};sa.propTypes={dataSource:Y.array,columns:Y.array,loading:Y.bool,title:Y.string,totalRecords:Y.number,pageSize:Y.number,currentPage:Y.number,onPageChange:Y.func,onPageSizeChange:Y.func,exportEnabled:Y.bool,onExport:Y.func,maxRecordsWarning:Y.number,performanceMode:Y.bool,rowKey:Y.oneOfType([Y.string,Y.func]),scroll:Y.object,expandable:Y.object};Y.array,Y.string,Y.node,Y.number,Y.bool,Y.bool,Y.bool,Y.bool,Y.bool,Y.number,Y.func,Y.func,Y.node;const It=n=>n?new Date(n).toISOString().split("T")[0]:null,Wt=(n,i)=>{if(!(n!=null&&n.start))return"Toutes les dates";const a=M(n.start),t=n.end?M(n.end):a;return i==="day"?a.format("DD/MM/YYYY"):i==="week"?`${a.format("DD/MM")} - ${t.format("DD/MM/YYYY")}`:i==="month"?a.format("MM/YYYY"):`${a.format("DD/MM/YYYY")} - ${t.format("DD/MM/YYYY")}`};M.locale("fr");const{Title:Yt,Text:wt,Paragraph:Ft}=dt,{useBreakpoint:rr}=pa,nr=()=>{var Pe,Dt,St;const n=Pa(),{getChartColor:i,getChartColors:a,charts:t}=n,{dateFilter:r,dateRangeType:s,dateRangeDescription:h,selectedMachineModel:f,selectedMachine:l,machineModels:C,filteredMachineNames:b,handleMachineModelChange:x,handleMachineChange:u,handleDateChange:p,handleDateRangeTypeChange:m,resetFilters:_,handleRefresh:y}=Oa();d.useCallback(()=>{const o={};return f&&(o.model=f),l&&(o.machine=l),r!=null&&r.start&&(r!=null&&r.end)&&(o.startDate=r.start.format("YYYY-MM-DD"),o.endDate=r.end.format("YYYY-MM-DD")),o.dateRangeType=s,o},[f,l,r,s]);const{getDashboardData:D,getAllDailyProduction:g,loading:E}=Ra(),[c,L]=d.useState({allDailyProduction:[],productionChart:{data:[],dataSource:"unknown"},sidecards:{goodqty:0,rejetqty:0,dataSource:"unknown"},machinePerformance:{data:[],dataSource:"unknown"},availabilityTrend:[]}),[Q,te]=d.useState({elasticsearch:!1,mysql:!0,primary:"mysql"}),{status:se,isMonitoring:ce,refreshStatus:U,retryElasticsearch:me}=Ha(),De=d.useCallback(async()=>{var o,S,A,V,ue,Ge,Me,Te,bt,_t,Ae,$e,ye,Oe,He,We,Ke,Ut,Gt,Ot;try{const Ie={dateRangeType:s,model:f||void 0,machine:l||void 0,date:r?It(r):void 0};Object.keys(Ie).forEach(k=>{Ie[k]===void 0&&delete Ie[k]}),console.log("🔍 [FILTER DEBUG] Date filter value:",r),console.log("🔍 [FILTER DEBUG] Formatted date:",It(r)),console.log("🔍 [FILTER DEBUG] Complete filters object:",Ie);const[Je,P]=await Promise.all([g(Ie),D(Ie)]);if(P){const k=((o=P.productionChart)==null?void 0:o.dataSource)||((S=P.sidecards)==null?void 0:S.dataSource)||((A=P.machinePerformance)==null?void 0:A.dataSource)||"mysql";te({elasticsearch:k==="elasticsearch",mysql:k==="mysql"||k==="unknown",primary:k})}console.log("Raw machinePerformance from GraphQL:",(ue=(V=P==null?void 0:P.machinePerformance)==null?void 0:V.data)==null?void 0:ue.map(k=>({Shift:k.Shift,downtime:k.downtime}))),console.log("🔍 [DATA SOURCE DEBUG] Data sources:",{productionChart:(Ge=P==null?void 0:P.productionChart)==null?void 0:Ge.dataSource,sidecards:(Me=P==null?void 0:P.sidecards)==null?void 0:Me.dataSource,machinePerformance:(Te=P==null?void 0:P.machinePerformance)==null?void 0:Te.dataSource}),console.log("🔍 [DATA DEBUG] Dashboard result:",{chartLength:(_t=(bt=P==null?void 0:P.productionChart)==null?void 0:bt.data)==null?void 0:_t.length,sidecards:P==null?void 0:P.sidecards,firstChartItem:($e=(Ae=P==null?void 0:P.productionChart)==null?void 0:Ae.data)==null?void 0:$e[0],allProductionLength:(ye=Je==null?void 0:Je.getAllDailyProduction)==null?void 0:ye.length}),L({allDailyProduction:(Je==null?void 0:Je.getAllDailyProduction)||[],productionChart:{data:(((Oe=P==null?void 0:P.productionChart)==null?void 0:Oe.data)||[]).map(k=>{console.log("Processing chart item:",k);const Xe={date:k.Date_Insert_Day,good:parseInt(k.Total_Good_Qty_Day)||0,reject:parseInt(k.Total_Rejects_Qty_Day)||0,oee:(parseFloat(k.OEE_Day)||0)*100,speed:parseFloat(k.Speed_Day)||0,availability:(parseFloat(k.Availability_Rate_Day)||0)*100,performance:(parseFloat(k.Performance_Rate_Day)||0)*100,quality:(parseFloat(k.Quality_Rate_Day)||0)*100,OEE_Day:(parseFloat(k.OEE_Day)||0)*100,Availability_Rate_Day:(parseFloat(k.Availability_Rate_Day)||0)*100,Performance_Rate_Day:(parseFloat(k.Performance_Rate_Day)||0)*100,Quality_Rate_Day:(parseFloat(k.Quality_Rate_Day)||0)*100};return console.log("Transformed chart item:",Xe),Xe}),dataSource:((He=P==null?void 0:P.productionChart)==null?void 0:He.dataSource)||"unknown"},sidecards:{goodqty:parseInt((We=P==null?void 0:P.sidecards)==null?void 0:We.goodqty)||0,rejetqty:parseInt((Ke=P==null?void 0:P.sidecards)==null?void 0:Ke.rejetqty)||0,dataSource:((Ut=P==null?void 0:P.sidecards)==null?void 0:Ut.dataSource)||"unknown"},machinePerformance:{data:(((Gt=P==null?void 0:P.machinePerformance)==null?void 0:Gt.data)||[]).map(k=>{console.log("Processing machinePerformance item:",k);const Xe={...k,availability:(parseFloat(k.availability)||0)*100,performance:(parseFloat(k.performance)||0)*100,oee:(parseFloat(k.oee)||0)*100,quality:(parseFloat(k.quality)||0)*100,disponibilite:parseFloat(k.disponibilite)||0,downtime:parseFloat(k.downtime)||0};return console.log("Transformed machinePerformance item:",Xe),Xe}),dataSource:((Ot=P==null?void 0:P.machinePerformance)==null?void 0:Ot.dataSource)||"unknown"},availabilityTrend:(P==null?void 0:P.availabilityTrend)||[]})}catch(Ie){console.error("Error fetching GraphQL data:",Ie)}},[s,f,l,r,g,D]);d.useEffect(()=>{De()},[De]),d.useEffect(()=>{console.log("ProductionData updated:",c),c.productionChart.length>0&&(console.log("First chart item:",c.productionChart[0]),console.log("Chart data length:",c.productionChart.length))},[c]);const[Fe,G]=d.useState("1"),[W,Mt]=d.useState(0),[w,B]=d.useState(null),[F,$]=d.useState(""),[K,de]=d.useState(!1),[Ne,J]=d.useState(!1),H=rr(),[z,ve]=d.useState(!1),Le=d.useCallback(o=>{p(o),ve(!!o)},[p]);d.useEffect(()=>{var S,A;const o=c.allDailyProduction.length+(((S=c.productionChart.data)==null?void 0:S.length)||0)+(((A=c.machinePerformance.data)==null?void 0:A.length)||0);Mt(o)},[c]);const ot=d.useCallback(async o=>{try{console.log("Exporting data:",o)}catch(S){console.error("Export failed:",S)}},[]),Ce=d.useCallback((o,S)=>{B(o),$(S),de(!!o),o&&G("3")},[]),ft=d.useCallback(o=>{console.log("Global search result selected:",o),J(!1),o.type==="production-data"&&G("3")},[]);d.useCallback(()=>{B(null),$(""),de(!1)},[]);const pt=d.useMemo(()=>{let o=0,S=0,A=0,V=0;const ue=c.productionChart.data||[],Ge=c.sidecards;if(console.log("🔍 [STATS DEBUG] Chart data for statistics:",{length:ue.length,firstItem:ue[0],dates:ue.map(Ae=>Ae.date)}),console.log("🔍 [STATS DEBUG] Sidecards data:",Ge),ue.length>0){const Ae=ue.reduce(($e,ye)=>{let Oe=parseFloat(ye.oee||ye.OEE_Day||0),He=parseFloat(ye.availability||ye.Availability_Rate_Day||0),We=parseFloat(ye.performance||ye.Performance_Rate_Day||0),Ke=parseFloat(ye.quality||ye.Quality_Rate_Day||0);return Oe=ne(Oe),He=ne(He),We=ne(We),Ke=ne(Ke),{oee:$e.oee+Oe,availability:$e.availability+He,performance:$e.performance+We,quality:$e.quality+Ke}},{oee:0,availability:0,performance:0,quality:0});o=Ae.oee/ue.length,S=Ae.availability/ue.length,A=Ae.performance/ue.length,V=Ae.quality/ue.length}const Me=parseInt(Ge.goodqty)||0,Te=parseInt(Ge.rejetqty)||0,bt=Me+Te>0?Te/(Me+Te)*100:0,_t=Me+Te>0?Me/(Me+Te)*100:0;return{avgTRS:o,avgAvailability:S,avgPerformance:A,avgQuality:V,rejectRate:bt,qualityRate:_t,totalGood:Me,totalRejects:Te}},[c]),{avgTRS:ht,avgAvailability:xe,avgPerformance:Qe,avgQuality:Be,rejectRate:T,qualityRate:q,totalGood:ae,totalRejects:N}=pt,Ve=d.useCallback(()=>{const S=new Date().getHours();return S>=6&&S<14?"Matin":S>=14&&S<22?"Après-midi":"Nuit"},[]),lt=d.useMemo(()=>(console.log("🔍 [STATS DEBUG] Values for statistics cards:",{totalGood:ae,totalRejects:N,avgTRS:ht,avgAvailability:xe,avgPerformance:Qe,rejectRate:T,qualityRate:q}),Ja(ae,N,ht,xe,Qe,T,q)),[ae,N,ht,xe,Qe,T,q]),gt=d.useMemo(()=>Xa(a([R.PRIMARY_BLUE,R.SECONDARY_BLUE,R.CHART_TERTIARY,R.SUCCESS_GREEN,R.WARNING_ORANGE]),ne),[a]),it=d.useMemo(()=>Za(a([R.PRIMARY_BLUE,R.SECONDARY_BLUE,R.CHART_TERTIARY,R.SUCCESS_GREEN,R.WARNING_ORANGE]),c.allDailyProduction),[a,c.allDailyProduction]),Se=d.useCallback((o,S=0)=>{if(o==null||o==="")return S;if(typeof o=="number"&&!isNaN(o))return o;const A=String(o).trim().replace(",","."),V=parseFloat(A);return isNaN(V)?S:V},[]),st=d.useCallback((o,S=0)=>{if(o==null||o==="")return S;if(typeof o=="number"&&!isNaN(o))return Math.round(o);const A=String(o).trim(),V=parseInt(A,10);return isNaN(V)?S:V},[]),ct=d.useMemo(()=>c.allDailyProduction.map(o=>({...o,date:(()=>{try{const S=o.Date_Insert_Day||o.date;if(S){if(S.includes("/")){let V=M(S,"DD/MM/YYYY HH:mm:ss");if(V.isValid()||(V=M(S,"DD/MM/YYYY")),V.isValid())return V.format("YYYY-MM-DD")}const A=M(S);if(A.isValid())return A.format("YYYY-MM-DD")}return console.warn(`Invalid date found in table data: ${S}, using today's date`),M().format("YYYY-MM-DD")}catch(S){return console.error("Error parsing date for table:",S),M().format("YYYY-MM-DD")}})(),Machine_Name:o.Machine_Name||"N/A",Shift:o.Shift||"N/A",good:st(o.Good_QTY_Day),reject:st(o.Rejects_QTY_Day),oee:(()=>{const S=Se(o.OEE_Day);return S>0&&S<=1?S*100:S})(),speed:Se(o.Speed_Day,null),mould_number:o.Part_Number||"N/A",poid_unitaire:o.Poid_Unitaire||"N/A",cycle_theorique:o.Cycle_Theorique||"N/A",poid_purge:o.Poid_Purge||"N/A",availability:(()=>{const S=Se(o.Availability_Rate_Day);return S>0&&S<=1?S*100:S})(),performance:(()=>{const S=Se(o.Performance_Rate_Day);return S>0&&S<=1?S*100:S})(),quality:(()=>{const S=Se(o.Quality_Rate_Day);return S>0&&S<=1?S*100:S})(),run_hours:Se(o.Run_Hours_Day),down_hours:Se(o.Down_Hours_Day)})),[c.allDailyProduction,Se,st]),ke=d.useMemo(()=>{var o,S;return[{key:"1",label:e.createElement("span",null,e.createElement(Jt,null),"Tendances"),children:e.createElement(be,{gutter:[24,24]},E?e.createElement(I,{span:24},e.createElement(fe,null,e.createElement(Rt,{active:!0,paragraph:{rows:8}}))):e.createElement(ar,{data:c.productionChart.data,colors:a([R.PRIMARY_BLUE,R.SECONDARY_BLUE,R.CHART_TERTIARY,R.SUCCESS_GREEN,R.WARNING_ORANGE]),dateRangeType:s,dateFilter:r,formatDateRange:Wt}))},{key:"2",label:e.createElement("span",null,e.createElement(Nt,null),"Performance"),children:e.createElement(be,{gutter:[24,24]},E?e.createElement(I,{span:24},e.createElement(fe,null,e.createElement(Rt,{active:!0,paragraph:{rows:8}}))):e.createElement(e.Fragment,null,e.createElement(I,{span:24},e.createElement(fe,{title:e.createElement(j,null,e.createElement(Nt,{style:{fontSize:20,color:i(R.SECONDARY_BLUE,1)}}),e.createElement(wt,{strong:!0},"Performance des Machines")),variant:"borderless",extra:e.createElement($t,{count:((o=c.machinePerformance.data)==null?void 0:o.length)||0,style:{backgroundColor:i(R.SECONDARY_BLUE,1)}})},e.createElement(be,{gutter:[24,24]},e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Production par Machine",data:c.machinePerformance.data,chartType:"bar",expandMode:"modal"},e.createElement(ia,{data:c.machinePerformance.data,title:"Production par Machine",dataKey:"production",tooltipLabel:"Production",isKg:!0,color:i(R.CHART_TERTIARY,2)}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Rejets par Machine",data:c.machinePerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(oa,{data:c.machinePerformance.data,color:i(R.WARNING_ORANGE,4)})))))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{...Ma("TRS par Machine",c.machinePerformance.data,"bar")},e.createElement(la,{data:c.machinePerformance.data,color:i("#60A5FA",5)}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Répartition Production - Qualité",data:[{name:"Bonnes Pièces",value:Number(ae)||0},{name:"Rejets",value:Number(N)||0}].filter(A=>A.value>0),chartType:"pie",expandMode:"modal",exportEnabled:!0,cardProps:{extra:e.createElement(v,{color:"red"},"Qualité"),loading:E}},e.createElement(na,{data:[{name:"Bonnes Pièces",value:Number(ae)||0},{name:"Rejets",value:Number(N)||0}].filter(A=>A.value>0),colors:a([R.CHART_TERTIARY,R.WARNING_ORANGE]),height:((S=t==null?void 0:t.layout)==null?void 0:S.defaultHeight)||300}))),e.createElement(I,{xs:24,md:24},e.createElement(fe,{title:e.createElement(j,null,e.createElement(Nt,{style:{fontSize:20,color:i(R.CHART_QUATERNARY,3)}}),e.createElement(wt,{strong:!0},"Comparaison des Équipes")),variant:"borderless",extra:e.createElement(v,{color:"orange"},"Par équipe")},e.createElement(be,{gutter:[24,24]},e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Production par Équipe",data:c.machinePerformance,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(Bt,{data:c.machinePerformance,title:"Production par Équipe",dataKey:"production",color:i(R.CHART_TERTIARY,2),label:"Production",tooltipLabel:"Production",isKg:!1}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Temps d'arrêt par Équipe",data:c.machinePerformance,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(Bt,{data:c.machinePerformance.data,title:"Temps d'arrêt par Équipe",dataKey:"downtime",color:i(R.WARNING_ORANGE,4),label:"Temps d'arrêt (heures)",tooltipLabel:"Temps d'arrêt (heures)",isKg:!1}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"TRS par Équipe",data:c.machinePerformance.data,chartType:"line",expandMode:"modal",exportEnabled:!0},e.createElement(ta,{data:c.machinePerformance.data,color:i(R.PRIMARY_BLUE,0)}))),e.createElement(I,{xs:24,md:12},e.createElement(pe,{title:"Performance par Équipe",data:c.machinePerformance.data,chartType:"line",expandMode:"modal",exportEnabled:!0},e.createElement(aa,{data:c.machinePerformance.data,color:i("#60A5FA",5)}))))))))},{key:"3",label:e.createElement("span",null,e.createElement(qa,null),"Détails"),children:e.createElement(be,{gutter:[24,24]},e.createElement(I,{span:24},e.createElement(fe,{title:e.createElement(j,null,e.createElement(ut,{style:{fontSize:20,color:i(R.SECONDARY_BLUE,1)}}),e.createElement(wt,{strong:!0},"Données Journalières par Machine")),variant:"borderless",extra:e.createElement(j,null,e.createElement($t,{count:c.allDailyProduction.length,style:{backgroundColor:i(R.SECONDARY_BLUE,1)}}),e.createElement(Ye,{type:"link",icon:e.createElement(xt,null),disabled:!0},"Exporter"))},e.createElement(Kt,{dataSource:c.allDailyProduction,columns:gt,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:A=>`Total ${A} enregistrements`},scroll:{x:1800},rowKey:(A,V)=>`${A.Machine_Name}-${A.Date_Insert_Day}-${V}`}))),e.createElement(I,{span:24},e.createElement(sa,{title:"Données Détaillées de Production",dataSource:ct,columns:it,totalRecords:ct.length,pageSize:50,currentPage:1,onExport:ot,maxRecordsWarning:500,loading:E,scroll:{x:2200},rowKey:(A,V)=>`${A.Date_Insert_Day}-${A.Machine_Name||"unknown"}-${A.Part_Number||"unknown"}-${V}`,expandable:{expandedRowRender:A=>e.createElement(fe,{size:"small",title:"Informations du moule"},e.createElement(be,{gutter:[16,16]},e.createElement(I,{span:6},e.createElement(Ze,{title:"Numéro de Pièce",value:A.Part_Number||"N/A",valueStyle:{fontSize:16}})),e.createElement(I,{span:6},e.createElement(Ze,{title:"Poids Unitaire",value:A.Poid_Unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})),e.createElement(I,{span:6},e.createElement(Ze,{title:"Cycle Théorique",value:A.Cycle_Theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})),e.createElement(I,{span:6},e.createElement(Ze,{title:"Poids Purge",value:A.Poid_Purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})))),expandRowByClick:!0,rowExpandable:A=>A.Part_Number&&A.Part_Number!=="N/A"}})))}]},[c.allDailyProduction,c.machinePerformance.data,c.sidecards,s,r,Wt,ct,it,ot,E,gt]),mt=E,Ue=E,yt=(((Pe=c.productionChart.data)==null?void 0:Pe.length)||0)>0||c.sidecards.goodqty>0,Et=(((Dt=c.productionChart.data)==null?void 0:Dt.length)||0)>0||c.sidecards.goodqty>0;return console.log("🔍 [DATA AVAILABILITY DEBUG]",{hasData:yt,hasGraphQLData:Et,chartDataLength:((St=c.productionChart.data)==null?void 0:St.length)||0,sidecardsGoodQty:c.sidecards.goodqty,allDailyProductionLength:c.allDailyProduction.length,isLoading:mt,filters:{selectedMachineModel:f,selectedMachine:l,dateFilter:r}}),e.createElement("div",{style:{padding:H.md?24:16}},e.createElement(Ht,{spinning:mt,tip:"Chargement des données...",size:"large"},e.createElement(be,{gutter:[24,24]},e.createElement(I,{span:24},e.createElement(fe,{variant:"borderless",styles:{body:{padding:H.md?24:16}}},e.createElement(be,{gutter:[24,24],align:"middle"},e.createElement(I,{xs:24,md:12},e.createElement(Yt,{level:3,style:{marginBottom:8}},e.createElement(qt,{style:{marginRight:12,color:i(R.PRIMARY_BLUE,0)}}),"Tableau de Bord de Production")),e.createElement(I,{xs:24,md:12,style:{textAlign:H.md?"right":"left"}},e.createElement(j,{direction:"vertical",style:{width:"100%"}},e.createElement(Ca,{selectedMachineModel:f,selectedMachine:l,machineModels:C,filteredMachineNames:b,dateRangeType:s,dateFilter:r,dateFilterActive:z,handleMachineModelChange:x,handleMachineChange:u,handleDateRangeTypeChange:m,handleDateChange:Le,resetFilters:_,handleRefresh:y,loading:E,dataSize:W,pageType:"production",onSearchResults:Ce,enableElasticsearch:!0}),W>500&&e.createElement(v,{color:"blue",icon:e.createElement(tt,null)},W," enregistrements"),(f||z)&&e.createElement(j,{wrap:!0,style:{marginTop:8}},f&&e.createElement(v,{color:"blue",closable:!0,onClose:()=>x("")},"Modèle: ",f),l&&e.createElement(v,{color:"green",closable:!0,onClose:()=>u("")},"Machine: ",l),z&&e.createElement(v,{color:"purple",closable:!0,onClose:()=>p(null)},"Période: ",h)),!(f||z)&&Et&&e.createElement(j,{wrap:!0,style:{marginTop:8}},e.createElement(v,{color:"green",icon:e.createElement(tt,null)},"Powered by GraphQL"),e.createElement(Ka,{status:{...Q,...se},onRefresh:U,onRetry:me,isMonitoring:ce}))))))),lt.slice(0,4).map(o=>e.createElement(I,{key:o.title,xs:24,sm:12,md:6},e.createElement(fe,{hoverable:!0,loading:Ue,style:{backgroundColor:"#FFFFFF",border:`1px solid ${R.PRIMARY_BLUE}`,borderTop:`3px solid ${R.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},Ue?e.createElement(Rt,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ze,{title:e.createElement(he,{title:o.description},e.createElement(j,null,e.cloneElement(o.icon,{style:{color:R.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:R.DARK_GRAY,fontWeight:600}},o.title),e.createElement(jt,{style:{color:R.LIGHT_GRAY,fontSize:14}}))),value:o.rawValue||o.value,precision:o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité")?1:0,suffix:o.suffix,valueStyle:{fontSize:24,color:R.PRIMARY_BLUE,fontWeight:700},formatter:S=>o.suffix==="%"?S.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):o.suffix==="Pcs"||o.suffix==="Kg"?S.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):S.toLocaleString("fr-FR")}),(o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité"))&&e.createElement(Ee,{percent:o.rawValue||o.value,strokeColor:R.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),lt.slice(4).map(o=>e.createElement(I,{key:o.title,xs:24,sm:12,md:6},e.createElement(fe,{hoverable:!0,loading:Ue,style:{backgroundColor:"#FFFFFF",border:`1px solid ${R.PRIMARY_BLUE}`,borderTop:`3px solid ${R.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},Ue?e.createElement(Rt,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ze,{title:e.createElement(he,{title:o.description},e.createElement(j,null,e.cloneElement(o.icon,{style:{color:R.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:R.DARK_GRAY,fontWeight:600}},o.title),e.createElement(jt,{style:{color:R.LIGHT_GRAY,fontSize:14}}))),value:o.rawValue||o.value,precision:o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité")?1:0,suffix:o.suffix,valueStyle:{fontSize:24,color:R.PRIMARY_BLUE,fontWeight:700},formatter:S=>o.suffix==="%"?S.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):o.suffix==="Pcs"||o.suffix==="Kg"?S.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):S.toLocaleString("fr-FR")}),(o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité"))&&e.createElement(Ee,{percent:o.rawValue||o.value,strokeColor:R.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),mt?e.createElement(I,{span:24},e.createElement(fe,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(Ht,{size:"large",style:{marginBottom:24}}),e.createElement(Yt,{level:3},"Chargement des données..."),e.createElement(Ft,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},f?`Chargement des données pour ${f}...`:"Chargement des données de production pour tous les modèles de machines...")))):yt?e.createElement(e.Fragment,null,e.createElement(I,{span:24},e.createElement(fe,{variant:"borderless"},e.createElement(ha,{defaultActiveKey:"1",onChange:G,items:ke,tabBarExtraContent:e.createElement(j,null,e.createElement(Ye,{type:"link",icon:e.createElement(ja,null),onClick:()=>J(!0)},"Recherche globale"),e.createElement(Ye,{type:"link",icon:e.createElement(xt,null),disabled:!0},"Exporter"),l&&e.createElement(Ea,{machineId:l,machineName:l,shift:Ve()}))})))):e.createElement(I,{span:24},e.createElement(fe,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(qt,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),e.createElement(Yt,{level:3},"Aucune donnée disponible"),e.createElement(Ft,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},f||r?"Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.":"Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."),(f||r)&&e.createElement(Ft,{style:{fontSize:14,color:"#999",textAlign:"center",marginTop:16}},"Filtres actifs:",f&&` Modèle: ${f}`,l&&` Machine: ${l}`,r&&` Période: ${It(r)}`)))))),K&&w&&e.createElement("div",{style:{marginTop:24}},e.createElement(xa,{results:w,searchQuery:F,pageType:"production",loading:E,onResultSelect:o=>{console.log("Production result selected:",o)},onPageChange:o=>{console.log("Page changed:",o)}})),e.createElement(Sa,{visible:Ne,onClose:()=>J(!1),onResultSelect:ft}))},or=d.memo(nr),Or=d.memo(()=>e.createElement(Ga,null,e.createElement(or,null)));export{Or as default};
