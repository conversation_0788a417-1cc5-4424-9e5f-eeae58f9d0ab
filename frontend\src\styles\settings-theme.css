/* Settings Theme CSS - Comprehensive Immediate Visual Effects */

/* Dark Theme Styles - Complete Coverage with Accessibility Compliance */
.dark-theme {
  background-color: #141414 !important;
  color: #ffffff !important;
}

/* High Contrast Text for Accessibility (WCAG AA Compliance - 4.5:1 ratio) */
.dark-theme,
.dark-theme * {
  color: #ffffff !important;
}

.dark-theme .ant-typography,
.dark-theme .ant-typography-title,
.dark-theme .ant-typography-paragraph,
.dark-theme .ant-typography-text {
  color: #ffffff !important;
}

.dark-theme h1,
.dark-theme h2,
.dark-theme h3,
.dark-theme h4,
.dark-theme h5,
.dark-theme h6 {
  color: #ffffff !important;
}

.dark-theme p,
.dark-theme span,
.dark-theme div,
.dark-theme label {
  color: #ffffff !important;
}

/* Secondary text with high contrast */
.dark-theme .ant-typography-caption,
.dark-theme .ant-typography-secondary {
  color: #e0e0e0 !important; /* High contrast secondary text */
}

/* Ensure all text elements have sufficient contrast */
.dark-theme .ant-descriptions-item-label,
.dark-theme .ant-descriptions-item-content {
  color: #ffffff !important;
}

/* Layout Components */
.dark-theme .ant-layout {
  background-color: #141414 !important;
}

.dark-theme .ant-layout-content {
  background-color: #141414 !important;
}

.dark-theme .ant-layout-header {
  background-color: #001529 !important;
  border-bottom: 1px solid #303030 !important;
}

.dark-theme .ant-layout-sider {
  background-color: #001529 !important;
}

/* Card Components */
.dark-theme .ant-card {
  background-color: #1f1f1f !important;
  border-color: #303030 !important;
  color: #ffffff !important;
}

.dark-theme .ant-card-head {
  background-color: #1f1f1f !important;
  border-bottom-color: #303030 !important;
  color: #ffffff !important;
}

.dark-theme .ant-card-head-title {
  color: #ffffff !important;
}

.dark-theme .ant-card-body {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

/* Table Components */
.dark-theme .ant-table {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

.dark-theme .ant-table-thead > tr > th {
  background-color: #262626 !important;
  border-bottom-color: #303030 !important;
  color: #ffffff !important;
}

.dark-theme .ant-table-tbody > tr > td {
  border-bottom-color: #303030 !important;
  color: #ffffff !important;
  background-color: #1f1f1f !important;
}

.dark-theme .ant-table-tbody > tr:hover > td {
  background-color: #262626 !important;
}

.dark-theme .ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #1890ff20 !important;
}

.dark-theme .ant-table-pagination {
  background-color: #1f1f1f !important;
}

/* Menu Components */
.dark-theme .ant-menu {
  background-color: #001529 !important;
  color: #ffffff !important;
}

.dark-theme .ant-menu-item {
  color: #ffffff !important;
}

.dark-theme .ant-menu-item:hover {
  background-color: #1890ff20 !important;
  color: #1890ff !important;
}

.dark-theme .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

.dark-theme .ant-menu-submenu-title {
  color: #ffffff !important;
}

/* Form Components */
.dark-theme .ant-form-item-label > label {
  color: #ffffff !important;
}

.dark-theme .ant-input {
  background-color: #262626 !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.dark-theme .ant-input:hover {
  border-color: #1890ff !important;
}

.dark-theme .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.dark-theme .ant-select {
  color: #ffffff !important;
}

.dark-theme .ant-select-selector {
  background-color: #262626 !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.dark-theme .ant-select-selector:hover {
  border-color: #1890ff !important;
}

.dark-theme .ant-select-selection-item {
  color: #ffffff !important;
}

.dark-theme .ant-select-arrow {
  color: #ffffff !important;
}

/* Button Components */
.dark-theme .ant-btn {
  border-color: #404040 !important;
  color: #ffffff !important;
}

.dark-theme .ant-btn:not(.ant-btn-primary) {
  background-color: #262626 !important;
}

.dark-theme .ant-btn:hover:not(.ant-btn-primary) {
  background-color: #404040 !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
}

.dark-theme .ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
}

.dark-theme .ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* Switch Components */
.dark-theme .ant-switch {
  background-color: #404040 !important;
}

.dark-theme .ant-switch-checked {
  background-color: #1890ff !important;
}

/* Slider Components */
.dark-theme .ant-slider-rail {
  background-color: #404040 !important;
}

.dark-theme .ant-slider-track {
  background-color: #1890ff !important;
}

.dark-theme .ant-slider-handle {
  border-color: #1890ff !important;
  background-color: #1890ff !important;
}

/* Tabs Components */
.dark-theme .ant-tabs {
  color: #ffffff !important;
}

.dark-theme .ant-tabs-tab {
  color: #ffffff !important;
}

.dark-theme .ant-tabs-tab:hover {
  color: #1890ff !important;
}

.dark-theme .ant-tabs-tab-active {
  color: #1890ff !important;
}

.dark-theme .ant-tabs-ink-bar {
  background-color: #1890ff !important;
}

.dark-theme .ant-tabs-content-holder {
  background-color: #141414 !important;
}

/* Modal Components */
.dark-theme .ant-modal-content {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

.dark-theme .ant-modal-header {
  background-color: #1f1f1f !important;
  border-bottom-color: #303030 !important;
}

.dark-theme .ant-modal-title {
  color: #ffffff !important;
}

.dark-theme .ant-modal-footer {
  background-color: #1f1f1f !important;
  border-top-color: #303030 !important;
}

/* Dropdown Components */
.dark-theme .ant-dropdown-menu {
  background-color: #262626 !important;
  border-color: #404040 !important;
}

.dark-theme .ant-dropdown-menu-item {
  color: #ffffff !important;
}

.dark-theme .ant-dropdown-menu-item:hover {
  background-color: #1890ff20 !important;
}

/* Tooltip Components */
.dark-theme .ant-tooltip-inner {
  background-color: #262626 !important;
  color: #ffffff !important;
}

.dark-theme .ant-tooltip-arrow-content {
  background-color: #262626 !important;
}

/* Alert Components */
.dark-theme .ant-alert {
  border-color: #404040 !important;
}

.dark-theme .ant-alert-info {
  background-color: #1890ff20 !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.dark-theme .ant-alert-success {
  background-color: #52c41a20 !important;
  border-color: #52c41a !important;
  color: #ffffff !important;
}

.dark-theme .ant-alert-warning {
  background-color: #faad1420 !important;
  border-color: #faad14 !important;
  color: #ffffff !important;
}

.dark-theme .ant-alert-error {
  background-color: #ff4d4f20 !important;
  border-color: #ff4d4f !important;
  color: #ffffff !important;
}

/* Tag Components */
.dark-theme .ant-tag {
  background-color: #262626 !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

/* Progress Components */
.dark-theme .ant-progress-text {
  color: #ffffff !important;
}

/* Statistic Components */
.dark-theme .ant-statistic-title {
  color: #ffffff !important;
}

.dark-theme .ant-statistic-content {
  color: #ffffff !important;
}

/* Divider Components */
.dark-theme .ant-divider {
  border-color: #303030 !important;
}

.dark-theme .ant-divider-horizontal.ant-divider-with-text::before,
.dark-theme .ant-divider-horizontal.ant-divider-with-text::after {
  border-top-color: #303030 !important;
}

/* List Components */
.dark-theme .ant-list {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

.dark-theme .ant-list-item {
  border-bottom-color: #303030 !important;
  color: #ffffff !important;
}

.dark-theme .ant-list-item-meta-title {
  color: #ffffff !important;
}

.dark-theme .ant-list-item-meta-description {
  color: #cccccc !important;
}

/* Additional High Contrast Text Elements */
.dark-theme .ant-typography-title,
.dark-theme .ant-typography-paragraph,
.dark-theme .ant-typography-text {
  color: #ffffff !important;
}

.dark-theme .ant-form-item-label > label {
  color: #ffffff !important;
}

.dark-theme .ant-form-item-explain,
.dark-theme .ant-form-item-extra {
  color: #e0e0e0 !important;
}

/* Breadcrumb Components */
.dark-theme .ant-breadcrumb {
  color: #ffffff !important;
}

.dark-theme .ant-breadcrumb a {
  color: #1890ff !important;
}

.dark-theme .ant-breadcrumb-separator {
  color: #cccccc !important;
}

/* Steps Components */
.dark-theme .ant-steps-item-title {
  color: #ffffff !important;
}

.dark-theme .ant-steps-item-description {
  color: #cccccc !important;
}

/* Collapse Components */
.dark-theme .ant-collapse {
  background-color: #1f1f1f !important;
  border-color: #303030 !important;
}

.dark-theme .ant-collapse-header {
  color: #ffffff !important;
  background-color: #262626 !important;
}

.dark-theme .ant-collapse-content {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

/* Drawer Components */
.dark-theme .ant-drawer-content {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

.dark-theme .ant-drawer-header {
  background-color: #1f1f1f !important;
  border-bottom-color: #303030 !important;
}

.dark-theme .ant-drawer-title {
  color: #ffffff !important;
}

/* Spin Components */
.dark-theme .ant-spin-text {
  color: #ffffff !important;
}

/* Empty Components */
.dark-theme .ant-empty-description {
  color: #cccccc !important;
}

/* Result Components */
.dark-theme .ant-result-title {
  color: #ffffff !important;
}

.dark-theme .ant-result-subtitle {
  color: #cccccc !important;
}

/* Anchor Components */
.dark-theme .ant-anchor-link-title {
  color: #ffffff !important;
}

.dark-theme .ant-anchor-link-title:hover {
  color: #1890ff !important;
}

/* Calendar Components */
.dark-theme .ant-picker-calendar {
  background-color: #1f1f1f !important;
}

.dark-theme .ant-picker-calendar-header {
  color: #ffffff !important;
}

/* Tree Components */
.dark-theme .ant-tree {
  background-color: #1f1f1f !important;
  color: #ffffff !important;
}

.dark-theme .ant-tree-title {
  color: #ffffff !important;
}

/* Transfer Components */
.dark-theme .ant-transfer {
  color: #ffffff !important;
}

.dark-theme .ant-transfer-list {
  background-color: #1f1f1f !important;
  border-color: #303030 !important;
}

.dark-theme .ant-transfer-list-header {
  background-color: #262626 !important;
  color: #ffffff !important;
}

/* Upload Components */
.dark-theme .ant-upload {
  background-color: #1f1f1f !important;
  border-color: #303030 !important;
}

.dark-theme .ant-upload-text {
  color: #ffffff !important;
}

.dark-theme .ant-upload-hint {
  color: #cccccc !important;
}

/* Compact Theme Styles */
.compact-theme .ant-card {
  padding: 12px !important;
}

.compact-theme .ant-card-body {
  padding: 12px !important;
}

.compact-theme .ant-table {
  font-size: 12px !important;
}

.compact-theme .ant-table-thead > tr > th {
  padding: 8px !important;
  font-size: 12px !important;
}

.compact-theme .ant-table-tbody > tr > td {
  padding: 8px !important;
  font-size: 12px !important;
}

.compact-theme .ant-btn {
  padding: 4px 12px !important;
  font-size: 12px !important;
  height: 28px !important;
}

.compact-theme .ant-input {
  padding: 4px 8px !important;
  font-size: 12px !important;
}

.compact-theme .ant-select {
  font-size: 12px !important;
}

.compact-theme .ant-select-selector {
  padding: 0 8px !important;
}

.compact-theme .ant-statistic {
  margin-bottom: 8px !important;
}

.compact-theme .ant-statistic-title {
  font-size: 12px !important;
  margin-bottom: 4px !important;
}

.compact-theme .ant-statistic-content {
  font-size: 18px !important;
}

/* No Animations Styles */
.no-animations * {
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  transition-duration: 0s !important;
  transition-delay: 0s !important;
}

.no-animations .ant-spin {
  animation: none !important;
}

.no-animations .ant-spin-dot {
  animation: none !important;
}

.no-animations .ant-spin-dot-item {
  animation: none !important;
}

.no-animations .recharts-wrapper {
  animation: none !important;
}

.no-animations .recharts-surface {
  animation: none !important;
}

/* Settings Page Specific Styles */
.settings-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.settings-preview-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.settings-preview-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark-theme .settings-preview-card:hover {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

/* Settings Form Styles */
.settings-form-item {
  margin-bottom: 16px;
}

.compact-theme .settings-form-item {
  margin-bottom: 12px;
}

.settings-form-label {
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}

.settings-form-description {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
  display: block;
}

.dark-theme .settings-form-description {
  color: #999;
}

/* Settings Tabs Styles */
.settings-tabs .ant-tabs-tab {
  padding: 12px 16px;
  font-weight: 500;
}

.compact-theme .settings-tabs .ant-tabs-tab {
  padding: 8px 12px;
  font-size: 12px;
}

.settings-tabs .ant-tabs-content-holder {
  padding-top: 16px;
}

/* Settings Action Bar */
.settings-action-bar {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.dark-theme .settings-action-bar {
  background-color: #262626;
  border-color: #404040;
}

.compact-theme .settings-action-bar {
  padding: 8px;
  margin-bottom: 12px;
}

/* Settings Grid Layout */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.compact-theme .settings-grid {
  gap: 12px;
}

/* Settings Preview Animations */
.settings-preview-enter {
  opacity: 0;
  transform: translateY(20px);
}

.settings-preview-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.no-animations .settings-preview-enter,
.no-animations .settings-preview-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-page {
    padding: 16px;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-tabs .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .settings-page {
    padding: 12px;
  }
  
  .settings-action-bar {
    padding: 8px;
  }
  
  .settings-form-item {
    margin-bottom: 12px;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .settings-form-label {
    font-weight: 700;
  }
  
  .ant-card {
    border-width: 2px !important;
  }
  
  .ant-btn {
    border-width: 2px !important;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
