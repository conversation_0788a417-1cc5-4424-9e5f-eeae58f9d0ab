import{r as g,a2 as w,R as e,d as n,T as re,e as b,g as u,l as _,B as m,C as p,b as E,c as o,al as z,aa as W,E as K,f as I,i as le,a9 as ue}from"./index-O2xm1U_Z.js";import{R as ie}from"./ReloadOutlined-EeD9QNgc.js";import{R as D}from"./FullscreenOutlined-DMf8_5Nq.js";import{R as C}from"./ThunderboltOutlined-xe_XoJ1p.js";import{R as k}from"./ExperimentOutlined-D3OB_L_Y.js";import{R as Ee}from"./FilterOutlined-B3wNZQVB.js";import{R as ye}from"./HistoryOutlined-kCKI-dwE.js";import{R as xe}from"./SaveOutlined-CxNNW5G5.js";import{b as be,D as he,R as te}from"./DownloadOutlined-ClmkhSDC.js";import{R as ve}from"./CalendarOutlined-ry8TLVWh.js";import{R as J}from"./ClockCircleOutlined-D-iaV6k8.js";import{S as h}from"./index-Dc91-n-S.js";import{R as H}from"./CheckCircleOutlined-BmO9kYsr.js";import{P as S}from"./progress-CVvjjq5H.js";import{R as A}from"./LineChartOutlined-CdWMpnra.js";import{R as X}from"./PieChartOutlined-zW9kEC3-.js";import{R as P}from"./BarChartOutlined-CX9KDBHm.js";import{R as $,a as ne}from"./TrophyOutlined-COlt6cAY.js";import{R as ce}from"./AlertOutlined-BsPkpdqt.js";import{R}from"./EyeOutlined-DDj6D5vZ.js";import{a as oe,R as F}from"./RiseOutlined-BR87aXMV.js";import{R as Z}from"./BulbOutlined-CGAT06dO.js";import{R as se}from"./ToolOutlined-BRYzAkU2.js";import{L as de}from"./index-CTwwobLF.js";import"./dayjs.min-CgAD4wBe.js";var ze={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"},Se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},Re={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"},we={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},ke={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 328a60 60 0 10120 0 60 60 0 10-120 0zM852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-32 660H204V128h616v596zM604 328a60 60 0 10120 0 60 60 0 10-120 0zm250.2 556H169.8c-16.5 0-29.8 14.3-29.8 32v36c0 4.4 3.3 8 7.4 8h729.1c4.1 0 7.4-3.6 7.4-8v-36c.1-17.7-13.2-32-29.7-32zM664 508H360c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"robot",theme:"outlined"},Ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 736c0-111.6-65.4-208-160-252.9V317.3c0-15.1-5.3-29.7-15.1-41.2L536.5 95.4C530.1 87.8 521 84 512 84s-18.1 3.8-24.5 11.4L335.1 276.1a63.97 63.97 0 00-15.1 41.2v165.8C225.4 528 160 624.4 160 736h156.5c-2.3 7.2-3.5 15-3.5 23.8 0 22.1 7.6 43.7 21.4 60.8a97.2 97.2 0 0043.1 30.6c23.1 54 75.6 88.8 134.5 88.8 29.1 0 57.3-8.6 81.4-24.8 23.6-15.8 41.9-37.9 53-64a97 97 0 0043.1-30.5 97.52 97.52 0 0021.4-60.8c0-8.4-1.1-16.4-3.1-23.8H864zM762.3 621.4c9.4 14.6 17 30.3 22.5 46.6H700V558.7a211.6 211.6 0 0162.3 62.7zM388 483.1V318.8l124-147 124 147V668H388V483.1zM239.2 668c5.5-16.3 13.1-32 22.5-46.6 16.3-25.2 37.5-46.5 62.3-62.7V668h-84.8zm388.9 116.2c-5.2 3-11.2 4.2-17.1 3.4l-19.5-2.4-2.8 19.4c-5.4 37.9-38.4 66.5-76.7 66.5-38.3 0-71.3-28.6-76.7-66.5l-2.8-19.5-19.5 2.5a27.7 27.7 0 01-17.1-3.5c-8.7-5-14.1-14.3-14.1-24.4 0-10.6 5.9-19.4 14.6-23.8h231.3c8.8 4.5 14.6 13.3 14.6 23.8-.1 10.2-5.5 19.6-14.2 24.5zM464 400a48 48 0 1096 0 48 48 0 10-96 0z"}}]},name:"rocket",theme:"outlined"},Ce={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"};function V(){return V=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},V.apply(this,arguments)}const Ae=(r,a)=>g.createElement(w,V({},r,{ref:a,icon:ze})),Oe=g.forwardRef(Ae);function Y(){return Y=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},Y.apply(this,arguments)}const Me=(r,a)=>g.createElement(w,Y({},r,{ref:a,icon:Se})),Pe=g.forwardRef(Me);function N(){return N=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},N.apply(this,arguments)}const $e=(r,a)=>g.createElement(w,N({},r,{ref:a,icon:Re})),j=g.forwardRef($e);function q(){return q=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},q.apply(this,arguments)}const Be=(r,a)=>g.createElement(w,q({},r,{ref:a,icon:we})),We=g.forwardRef(Be);function G(){return G=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},G.apply(this,arguments)}const je=(r,a)=>g.createElement(w,G({},r,{ref:a,icon:ke})),v=g.forwardRef(je);function Q(){return Q=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},Q.apply(this,arguments)}const Le=(r,a)=>g.createElement(w,Q({},r,{ref:a,icon:Ie})),ee=g.forwardRef(Le);function U(){return U=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var c in l)Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c])}return r},U.apply(this,arguments)}const Te=(r,a)=>g.createElement(w,U({},r,{ref:a,icon:Ce})),De=g.forwardRef(Te),{Title:He,Text:ae}=re,Fe=()=>e.createElement("div",{style:{background:"linear-gradient(135deg, #1890ff 0%, #722ed1 100%)",borderRadius:"16px",padding:"32px",marginBottom:"24px",color:"white",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{position:"absolute",top:"-50%",right:"-10%",width:"200px",height:"200px",background:"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",borderRadius:"50%",animation:"pulse 4s ease-in-out infinite"}}),e.createElement("div",{style:{position:"absolute",bottom:"-30%",left:"-5%",width:"150px",height:"150px",background:"radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%)",borderRadius:"50%",animation:"pulse 6s ease-in-out infinite reverse"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"16px"}},e.createElement("div",null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"}},e.createElement(v,{style:{fontSize:"32px",color:"white"}})),e.createElement("div",null,e.createElement(He,{level:1,style:{color:"white",margin:0,fontSize:"36px",fontWeight:"700",textShadow:"0 2px 4px rgba(0,0,0,0.3)"}},"AI Analytics Dashboard"),e.createElement(ae,{style:{color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"500"}},"Advanced Intelligence & Performance Optimization Platform")))),e.createElement(n,{size:"middle"},e.createElement(b,{title:"Refresh All Data",placement:"bottom"},e.createElement(u,{type:"text",icon:e.createElement(ie,null),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})),e.createElement(b,{title:"Analytics Settings",placement:"bottom"},e.createElement(u,{type:"text",icon:e.createElement(_,null),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})),e.createElement(b,{title:"Fullscreen Mode",placement:"bottom"},e.createElement(u,{type:"text",icon:e.createElement(D,null),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})))),e.createElement(n,{wrap:!0,size:"middle"},e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(C,{style:{fontSize:"10px"}}),e.createElement("span",null,"Real-time AI")),style:{background:"linear-gradient(135deg, #52c41a, #389e0d)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(k,{style:{fontSize:"10px"}}),e.createElement("span",null,"96 Analytics")),style:{background:"linear-gradient(135deg, #fa8c16, #d46b08)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(De,{style:{fontSize:"10px"}}),e.createElement("span",null,"13 Intelligence Categories")),style:{background:"linear-gradient(135deg, #eb2f96, #c41d7f)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.createElement(m,{count:e.createElement(n,{size:4},e.createElement(v,{style:{fontSize:"10px"}}),e.createElement("span",null,"Predictive ML")),style:{background:"linear-gradient(135deg, #722ed1, #531dab)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}})),e.createElement("div",{style:{position:"absolute",top:"16px",right:"16px",background:"rgba(255,255,255,0.2)",borderRadius:"20px",padding:"8px 12px",backdropFilter:"blur(10px)"}},e.createElement(n,{size:8},e.createElement("div",{style:{width:"8px",height:"8px",borderRadius:"50%",background:"#52c41a",animation:"pulse 2s ease-in-out infinite"}}),e.createElement(ae,{style:{color:"white",fontSize:"12px",fontWeight:"500"}},"AI Systems Online")))),e.createElement("style",{jsx:!0},`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.1);
          }
        }
      `)),{RangePicker:Ve}=he,{Option:B}=z,Ye=({filters:r,onFilterChange:a,loading:l})=>{const[c,i]=g.useState(!1),f=[{id:"M001",name:"Machine 001 - Line A"},{id:"M002",name:"Machine 002 - Line A"},{id:"M003",name:"Machine 003 - Line B"},{id:"M004",name:"Machine 004 - Line B"},{id:"M005",name:"Machine 005 - Line C"}],y=[{id:"P001",name:"Part ABC-123",weight:"50g"},{id:"P002",name:"Part DEF-456",weight:"75g"},{id:"P003",name:"Part GHI-789",weight:"100g"},{id:"P004",name:"Part JKL-012",weight:"125g"}],t=[{id:"OP001",name:"John Smith",shift:"Day"},{id:"OP002",name:"Maria Garcia",shift:"Night"},{id:"OP003",name:"Ahmed Hassan",shift:"Day"},{id:"OP004",name:"Lisa Chen",shift:"Evening"}],s=["Day","Evening","Night"],x=d=>{a({dateRange:d})},O=d=>{a({machine:d})},me=d=>{a({partNumber:d})},pe=d=>{a({operator:d})},fe=d=>{a({shift:d})},ge=()=>{a({dateRange:null,machine:null,partNumber:null,operator:null,shift:null})},L=()=>Object.values(r).filter(d=>d!=null).length;return e.createElement(p,{style:{marginBottom:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"},bodyStyle:{padding:"24px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"}},e.createElement(n,{align:"center",size:"middle"},e.createElement("div",{style:{background:"linear-gradient(135deg, #1890ff, #40a9ff)",borderRadius:"10px",padding:"8px",color:"white"}},e.createElement(Ee,{style:{fontSize:"16px"}})),e.createElement("div",null,e.createElement("h3",{style:{margin:0,color:"#1890ff",fontWeight:"600"}},"Smart Filters"),e.createElement("span",{style:{color:"#8c8c8c",fontSize:"12px"}},"Apply intelligent filters to focus your analytics")),L()>0&&e.createElement(m,{count:L(),style:{backgroundColor:"#52c41a",borderRadius:"10px"}})),e.createElement(n,null,e.createElement(b,{title:"Load Saved Filter Preset"},e.createElement(u,{icon:e.createElement(ye,null),style:{borderRadius:"8px"}},"Presets")),e.createElement(b,{title:"Save Current Filters"},e.createElement(u,{icon:e.createElement(xe,null),type:"dashed",style:{borderRadius:"8px"}},"Save")),e.createElement(b,{title:"Clear All Filters"},e.createElement(u,{icon:e.createElement(be,null),onClick:ge,disabled:L()===0,style:{borderRadius:"8px"}},"Clear")))),e.createElement(E,{gutter:[16,16]},e.createElement(o,{xs:24,sm:12,lg:6},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(ve,{style:{color:"#1890ff"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Date Range"))),e.createElement(Ve,{value:r.dateRange,onChange:x,style:{width:"100%",borderRadius:"8px",border:"2px solid #e8f4fd"},placeholder:["Start Date","End Date"]})),e.createElement(o,{xs:24,sm:12,lg:6},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(j,{style:{color:"#52c41a"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Machine"))),e.createElement(z,{value:r.machine,onChange:O,placeholder:"Select Machine",allowClear:!0,showSearch:!0,filterOption:(d,M)=>M.children.toLowerCase().indexOf(d.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"}},f.map(d=>e.createElement(B,{key:d.id,value:d.id},e.createElement(n,null,e.createElement(m,{status:"success"}),d.name))))),e.createElement(o,{xs:24,sm:12,lg:6},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(j,{style:{color:"#fa8c16"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Part Number"))),e.createElement(z,{value:r.partNumber,onChange:me,placeholder:"Select Part",allowClear:!0,showSearch:!0,filterOption:(d,M)=>M.children.toLowerCase().indexOf(d.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"}},y.map(d=>e.createElement(B,{key:d.id,value:d.id},e.createElement("div",null,e.createElement("div",null,d.name),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Weight: ",d.weight)))))),e.createElement(o,{xs:24,sm:12,lg:3},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(W,{style:{color:"#722ed1"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Operator"))),e.createElement(z,{value:r.operator,onChange:pe,placeholder:"Select Operator",allowClear:!0,showSearch:!0,filterOption:(d,M)=>M.children.toLowerCase().indexOf(d.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"}},t.map(d=>e.createElement(B,{key:d.id,value:d.id},e.createElement("div",null,e.createElement("div",null,d.name),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},d.shift," Shift")))))),e.createElement(o,{xs:24,sm:12,lg:3},e.createElement("div",{style:{marginBottom:"8px"}},e.createElement(n,{size:4},e.createElement(J,{style:{color:"#eb2f96"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"Shift"))),e.createElement(z,{value:r.shift,onChange:fe,placeholder:"Select Shift",allowClear:!0,style:{width:"100%",borderRadius:"8px"}},s.map(d=>e.createElement(B,{key:d,value:d},e.createElement(n,null,e.createElement(m,{status:d==="Day"?"success":d==="Evening"?"warning":"error"}),d," Shift")))))),e.createElement("div",{style:{marginTop:"20px",padding:"16px",background:"rgba(255,255,255,0.6)",borderRadius:"12px"}},e.createElement("div",{style:{marginBottom:"12px"}},e.createElement("span",{style:{fontWeight:"500",color:"#595959",fontSize:"13px"}},"Quick Presets:")),e.createElement(n,{wrap:!0,size:"small"},e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Today"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Yesterday"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Last 7 Days"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Last 30 Days"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"This Month"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Current Shift"),e.createElement(u,{size:"small",style:{borderRadius:"6px"}},"Peak Hours"))))},Ne=({loading:r,filters:a})=>{const l=[{title:"AI Performance Score",value:94.2,suffix:"%",precision:1,trend:"up",change:"+5.2%",color:"#52c41a",icon:e.createElement(v,null),description:"Overall AI system efficiency",gradient:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)"},{title:"Production Efficiency",value:87.5,suffix:"%",precision:1,trend:"up",change:"+3.1%",color:"#1890ff",icon:e.createElement(P,null),description:"Real-time production optimization",gradient:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)"},{title:"Quality Index",value:98.7,suffix:"%",precision:1,trend:"up",change:"+1.8%",color:"#722ed1",icon:e.createElement($,null),description:"AI-powered quality assurance",gradient:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)"},{title:"Predicted Savings",value:15847,prefix:"$",precision:0,trend:"up",change:"+12.4%",color:"#fa8c16",icon:e.createElement(We,null),description:"Monthly cost optimization",gradient:"linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)"},{title:"Real-time Alerts",value:23,precision:0,trend:"down",change:"-15%",color:"#f5222d",icon:e.createElement(ce,null),description:"Active system notifications",gradient:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)"},{title:"Operator Efficiency",value:91.3,suffix:"%",precision:1,trend:"up",change:"+4.7%",color:"#eb2f96",icon:e.createElement(C,null),description:"AI-enhanced performance",gradient:"linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)"}],c=[{title:"ML Model Accuracy",value:96.8,target:95,color:"#52c41a"},{title:"Prediction Confidence",value:92.1,target:90,color:"#1890ff"},{title:"Data Quality Score",value:89.4,target:85,color:"#722ed1"},{title:"System Uptime",value:99.7,target:99,color:"#fa8c16"}];return e.createElement(e.Fragment,null,e.createElement(E,{gutter:[16,16],style:{marginBottom:"24px"}},l.map((i,f)=>e.createElement(o,{xs:24,sm:12,md:8,lg:4,key:f},e.createElement(p,{loading:r,style:{borderRadius:"16px",border:"none",background:i.gradient,color:"white",boxShadow:"0 8px 24px rgba(0,0,0,0.12)",position:"relative",overflow:"hidden"},bodyStyle:{padding:"20px",position:"relative",zIndex:2}},e.createElement("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",zIndex:1}}),e.createElement("div",{style:{position:"absolute",bottom:"-30px",left:"-30px",width:"100px",height:"100px",background:"rgba(255,255,255,0.05)",borderRadius:"50%",zIndex:1}}),e.createElement(n,{direction:"vertical",size:"small",style:{width:"100%"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"8px",padding:"6px",fontSize:"16px"}},i.icon),e.createElement(m,{count:e.createElement(n,{size:2},i.trend==="up"?e.createElement(Pe,{style:{fontSize:"10px"}}):e.createElement(Oe,{style:{fontSize:"10px"}}),e.createElement("span",{style:{fontSize:"10px"}},i.change)),style:{background:i.trend==="up"?"rgba(82, 196, 26, 0.9)":"rgba(245, 34, 45, 0.9)",color:"white",border:"none",borderRadius:"10px",fontSize:"10px"}})),e.createElement(h,{value:i.value,precision:i.precision,prefix:i.prefix,suffix:i.suffix,valueStyle:{color:"white",fontSize:"24px",fontWeight:"700",lineHeight:"1.2"}}),e.createElement("div",null,e.createElement("div",{style:{fontSize:"12px",fontWeight:"600",marginBottom:"2px"}},i.title),e.createElement("div",{style:{fontSize:"10px",opacity:.8,fontWeight:"400"}},i.description))))))),e.createElement(p,{title:e.createElement(n,null,e.createElement(v,{style:{color:"#1890ff"}}),e.createElement("span",null,"AI System Health Metrics"),e.createElement(m,{count:"Live",style:{backgroundColor:"#52c41a"}})),style:{marginBottom:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"}},e.createElement(E,{gutter:[24,16]},c.map((i,f)=>e.createElement(o,{xs:24,sm:12,md:6,key:f},e.createElement("div",{style:{background:"linear-gradient(135deg, #f8faff 0%, #e6f7ff 100%)",borderRadius:"12px",padding:"16px",border:`2px solid ${i.color}20`}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},e.createElement("span",{style:{fontSize:"13px",fontWeight:"600",color:"#595959"}},i.title),e.createElement(b,{title:`Target: ${i.target}%`},e.createElement(H,{style:{color:i.value>=i.target?"#52c41a":"#faad14",fontSize:"14px"}}))),e.createElement("div",{style:{marginBottom:"8px"}},e.createElement("span",{style:{fontSize:"20px",fontWeight:"700",color:i.color}},i.value,"%")),e.createElement(S,{percent:i.value,strokeColor:{"0%":i.color,"100%":i.color+"80"},trailColor:"#f0f0f0",strokeWidth:6,showInfo:!1,style:{marginBottom:"4px"}}),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",textAlign:"center"}},"Target: ",i.target,"%")))))),e.createElement(E,{gutter:[16,16]},e.createElement(o,{xs:24,sm:8},e.createElement(p,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)",boxShadow:"0 4px 16px rgba(250, 140, 22, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"}},e.createElement(n,{direction:"vertical",size:"small"},e.createElement(J,{style:{fontSize:"24px",color:"#fa8c16"}}),e.createElement("div",{style:{fontWeight:"600",color:"#fa8c16"}},"Real-time Monitoring"),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"24/7 AI surveillance active")))),e.createElement(o,{xs:24,sm:8},e.createElement(p,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",boxShadow:"0 4px 16px rgba(82, 196, 26, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"}},e.createElement(n,{direction:"vertical",size:"small"},e.createElement(A,{style:{fontSize:"24px",color:"#52c41a"}}),e.createElement("div",{style:{fontWeight:"600",color:"#52c41a"}},"Predictive Analytics"),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"ML models optimizing performance")))),e.createElement(o,{xs:24,sm:8},e.createElement(p,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",boxShadow:"0 4px 16px rgba(114, 46, 209, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"}},e.createElement(n,{direction:"vertical",size:"small"},e.createElement(X,{style:{fontSize:"24px",color:"#722ed1"}}),e.createElement("div",{style:{fontWeight:"600",color:"#722ed1"}},"Smart Insights"),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"AI-generated recommendations"))))))},qe=({loading:r,filters:a})=>{const[l,c]=g.useState("performance"),i=[{partNumber:"ABC-123",aiScore:94.2,efficiency:87.5,qualityIndex:98.3,predictedYield:92.1,trend:"improving",status:"optimal",mlConfidence:96.8,recommendations:["Maintain current parameters","Consider 2% speed increase","Quality metrics excellent"]},{partNumber:"DEF-456",aiScore:78.4,efficiency:71.2,qualityIndex:89.7,predictedYield:85.3,trend:"declining",status:"attention",mlConfidence:89.2,recommendations:["Optimize cycle time","Check material consistency","Review operator training"]},{partNumber:"GHI-789",aiScore:91.7,efficiency:89.3,qualityIndex:95.1,predictedYield:88.9,trend:"stable",status:"good",mlConfidence:94.5,recommendations:["Performance stable","Minor temperature adjustment","Continue monitoring"]}],f=t=>{switch(t){case"optimal":return"#52c41a";case"good":return"#1890ff";case"attention":return"#fa8c16";case"critical":return"#f5222d";default:return"#d9d9d9"}},y=t=>{switch(t){case"improving":return e.createElement(F,{style:{color:"#52c41a"}});case"declining":return e.createElement(oe,{style:{color:"#f5222d"}});case"stable":return e.createElement(R,{style:{color:"#1890ff"}});default:return null}};return r?e.createElement("div",{style:{height:"400px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{description:"Loading AI Analysis..."})):e.createElement("div",{style:{minHeight:"400px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"8px",padding:"12px",marginBottom:"16px"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(o,null,e.createElement(n,null,e.createElement(k,{style:{color:"#1890ff"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"AI Model: Production Optimizer v2.1"),e.createElement(I,{color:"green",style:{borderRadius:"6px"}},"Active"))),e.createElement(o,null,e.createElement(n,{size:"small"},e.createElement(u,{size:"small",type:l==="performance"?"primary":"default",onClick:()=>c("performance"),style:{borderRadius:"6px"}},"Performance"),e.createElement(u,{size:"small",type:l==="predictions"?"primary":"default",onClick:()=>c("predictions"),style:{borderRadius:"6px"}},"Predictions"),e.createElement(u,{size:"small",type:l==="insights"?"primary":"default",onClick:()=>c("insights"),style:{borderRadius:"6px"}},"AI Insights"))))),l==="performance"&&e.createElement(E,{gutter:[16,16]},i.map((t,s)=>e.createElement(o,{xs:24,key:s},e.createElement(p,{size:"small",style:{borderRadius:"12px",border:`2px solid ${f(t.status)}20`,background:`linear-gradient(135deg, ${f(t.status)}05 0%, ${f(t.status)}10 100%)`}},e.createElement(E,{gutter:16,align:"middle"},e.createElement(o,{flex:"auto"},e.createElement(n,{align:"center",size:"middle"},e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600",fontSize:"14px",marginBottom:"4px"}},t.partNumber),e.createElement(n,{size:"small"},y(t.trend),e.createElement("span",{style:{fontSize:"12px",color:"#8c8c8c",textTransform:"capitalize"}},t.trend),e.createElement(I,{color:f(t.status),style:{borderRadius:"4px",fontSize:"10px",textTransform:"uppercase"}},t.status))))),e.createElement(o,null,e.createElement(h,{title:"AI Score",value:t.aiScore,suffix:"%",precision:1,valueStyle:{fontSize:"18px",color:f(t.status),fontWeight:"700"}})),e.createElement(o,null,e.createElement("div",{style:{width:"120px"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Efficiency"),e.createElement(S,{percent:t.efficiency,size:"small",strokeColor:f(t.status),showInfo:!0,format:x=>`${x}%`}))),e.createElement(o,null,e.createElement("div",{style:{width:"120px"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Quality Index"),e.createElement(S,{percent:t.qualityIndex,size:"small",strokeColor:"#722ed1",showInfo:!0,format:x=>`${x}%`}))),e.createElement(o,null,e.createElement(b,{title:"ML Model Confidence"},e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",marginBottom:"2px"}},"Confidence"),e.createElement("div",{style:{fontSize:"14px",fontWeight:"600",color:t.mlConfidence>90?"#52c41a":"#fa8c16"}},t.mlConfidence,"%"))))))))),l==="predictions"&&e.createElement("div",{style:{background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"12px",padding:"20px",textAlign:"center"}},e.createElement($,{style:{fontSize:"48px",color:"#fa8c16",marginBottom:"16px"}}),e.createElement("h3",{style:{color:"#fa8c16",marginBottom:"8px"}},"AI Prediction Engine"),e.createElement("p",{style:{color:"#8c8c8c",marginBottom:"16px"}},"Real-time yield predictions and optimization recommendations"),e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},i.map((t,s)=>e.createElement("div",{key:s,style:{background:"white",borderRadius:"8px",padding:"12px",display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("span",{style:{fontWeight:"500"}},t.partNumber),e.createElement(n,null,e.createElement("span",{style:{color:"#8c8c8c"}},"Predicted Yield:"),e.createElement("span",{style:{fontWeight:"600",color:t.predictedYield>90?"#52c41a":"#fa8c16"}},t.predictedYield,"%")))))),l==="insights"&&e.createElement("div",null,e.createElement("h4",{style:{marginBottom:"16px",color:"#1890ff"}},"AI-Generated Recommendations"),e.createElement(E,{gutter:[16,16]},i.map((t,s)=>e.createElement(o,{xs:24,key:s},e.createElement(p,{title:t.partNumber,size:"small",style:{borderRadius:"12px",border:`2px solid ${f(t.status)}20`},extra:e.createElement(I,{color:f(t.status)},t.status)},e.createElement(n,{direction:"vertical",size:"small",style:{width:"100%"}},t.recommendations.map((x,O)=>e.createElement("div",{key:O,style:{background:"#f8faff",borderRadius:"6px",padding:"8px 12px",fontSize:"13px",color:"#595959"}},"• ",x)))))))))},Ge=({loading:r,filters:a})=>{const[l,c]=g.useState(null),i={currentPerformance:87.5,optimizedPerformance:94.2,estimatedSavings:12450,activeOptimizations:[{id:1,title:"Cycle Time Optimization",impact:"High",status:"active",progress:78,description:"AI-recommended cycle time adjustments",estimatedGain:"+3.2%",timeToComplete:"6 hours",confidence:92.5},{id:2,title:"Temperature Profile Tuning",impact:"Medium",status:"pending",progress:0,description:"Optimal temperature curve for quality improvement",estimatedGain:"+2.1%",timeToComplete:"12 hours",confidence:87.3},{id:3,title:"Material Feed Rate",impact:"Medium",status:"completed",progress:100,description:"Optimized material injection parameters",estimatedGain:"+1.4%",timeToComplete:"Completed",confidence:94.8}],realTimeRecommendations:[{priority:"high",message:"Increase injection speed by 5% on Machine M003",impact:"+2.3% efficiency",confidence:89.2},{priority:"medium",message:"Adjust cooling time by -10% for Part ABC-123",impact:"+1.8% cycle time",confidence:84.7},{priority:"low",message:"Consider material preheating for next shift",impact:"+0.9% quality",confidence:76.4}]},f=t=>{switch(t){case"active":return"#1890ff";case"completed":return"#52c41a";case"pending":return"#fa8c16";default:return"#d9d9d9"}},y=t=>{switch(t){case"high":return"#f5222d";case"medium":return"#fa8c16";case"low":return"#52c41a";default:return"#d9d9d9"}};return r?e.createElement("div",{style:{height:"400px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{description:"Loading Optimization Engine..."})):e.createElement("div",{style:{minHeight:"400px"}},e.createElement(E,{gutter:16,style:{marginBottom:"20px"}},e.createElement(o,{span:8},e.createElement(p,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",border:"2px solid #1890ff20",borderRadius:"12px"}},e.createElement(h,{title:"Current Performance",value:i.currentPerformance,suffix:"%",precision:1,valueStyle:{color:"#1890ff",fontSize:"20px",fontWeight:"700"}}))),e.createElement(o,{span:8},e.createElement(p,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",border:"2px solid #52c41a20",borderRadius:"12px"}},e.createElement(h,{title:"AI Optimized Target",value:i.optimizedPerformance,suffix:"%",precision:1,valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"700"}}))),e.createElement(o,{span:8},e.createElement(p,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",border:"2px solid #fa8c1620",borderRadius:"12px"}},e.createElement(h,{title:"Potential Savings",value:i.estimatedSavings,prefix:"$",precision:0,valueStyle:{color:"#fa8c16",fontSize:"20px",fontWeight:"700"}})))),e.createElement("div",{style:{marginBottom:"20px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},e.createElement(n,null,e.createElement(ee,{style:{color:"#1890ff"}}),e.createElement("span",{style:{fontWeight:"600",color:"#595959"}},"Active Optimizations"),e.createElement(m,{count:i.activeOptimizations.filter(t=>t.status==="active").length,style:{backgroundColor:"#1890ff"}}))),e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},i.activeOptimizations.map(t=>e.createElement(p,{key:t.id,size:"small",style:{borderRadius:"10px",border:`2px solid ${f(t.status)}20`,background:`linear-gradient(135deg, ${f(t.status)}05 0%, ${f(t.status)}10 100%)`}},e.createElement(E,{gutter:16,align:"middle"},e.createElement(o,{flex:"auto"},e.createElement("div",null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"}},e.createElement("span",{style:{fontWeight:"600",fontSize:"14px"}},t.title),e.createElement(n,{size:"small"},e.createElement(I,{color:f(t.status)},t.status),e.createElement(I,{color:t.impact==="High"?"red":t.impact==="Medium"?"orange":"green"},t.impact," Impact"))),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",marginBottom:"8px"}},t.description),e.createElement(E,{gutter:16,align:"middle"},e.createElement(o,{flex:"auto"},e.createElement(S,{percent:t.progress,size:"small",strokeColor:f(t.status),showInfo:!1})),e.createElement(o,null,e.createElement(n,{size:"large"},e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Gain"),e.createElement("div",{style:{fontSize:"12px",fontWeight:"600",color:"#52c41a"}},t.estimatedGain)),e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Time"),e.createElement("div",{style:{fontSize:"12px",fontWeight:"500"}},t.timeToComplete)),e.createElement("div",{style:{textAlign:"center"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Confidence"),e.createElement("div",{style:{fontSize:"12px",fontWeight:"600",color:t.confidence>90?"#52c41a":"#fa8c16"}},t.confidence,"%")))))))))))),e.createElement("div",null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"}},e.createElement(n,null,e.createElement(Z,{style:{color:"#fa8c16"}}),e.createElement("span",{style:{fontWeight:"600",color:"#595959"}},"Real-time AI Recommendations"),e.createElement(m,{count:"Live",style:{backgroundColor:"#52c41a"}})),e.createElement(u,{type:"primary",size:"small",icon:e.createElement(H,null),style:{borderRadius:"6px"}},"Apply All")),e.createElement(n,{direction:"vertical",size:"small",style:{width:"100%"}},i.realTimeRecommendations.map((t,s)=>e.createElement(p,{key:s,size:"small",style:{borderRadius:"8px",border:`2px solid ${y(t.priority)}20`,background:`linear-gradient(135deg, ${y(t.priority)}05 0%, ${y(t.priority)}10 100%)`}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(o,{flex:"auto"},e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"4px"}},t.priority==="high"&&e.createElement(ce,{style:{color:"#f5222d"}}),t.priority==="medium"&&e.createElement(J,{style:{color:"#fa8c16"}}),t.priority==="low"&&e.createElement(H,{style:{color:"#52c41a"}}),e.createElement("span",{style:{fontSize:"13px",fontWeight:"500"}},t.message)),e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Expected impact: ",t.impact," • Confidence: ",t.confidence,"%")),e.createElement(o,null,e.createElement(n,{size:"small"},e.createElement(I,{color:y(t.priority),style:{textTransform:"uppercase",fontSize:"10px"}},t.priority),e.createElement(u,{type:"primary",size:"small",style:{borderRadius:"4px"}},"Apply")))))))))},{Option:T}=z,Qe=({loading:r,filters:a})=>{const[l,c]=g.useState("overview"),[i,f]=g.useState("all"),y={currentUtilization:78.5,plannedCapacity:92,forecastAccuracy:94.2,optimizationPotential:15.8,machines:[{id:"M001",name:"Machine 001",currentLoad:85.2,maxCapacity:100,forecastLoad:91.5,efficiency:87.3,status:"optimal"},{id:"M002",name:"Machine 002",currentLoad:72.1,maxCapacity:100,forecastLoad:88.9,efficiency:91.8,status:"good"},{id:"M003",name:"Machine 003",currentLoad:94.7,maxCapacity:100,forecastLoad:98.2,efficiency:76.4,status:"attention"}],weeklyForecast:[{week:"Week 1",demand:85,capacity:95,utilization:89.5},{week:"Week 2",demand:92,capacity:95,utilization:96.8},{week:"Week 3",demand:88,capacity:95,utilization:92.6},{week:"Week 4",demand:96,capacity:95,utilization:101.1}]},t=s=>{switch(s){case"optimal":return"#52c41a";case"good":return"#1890ff";case"attention":return"#fa8c16";case"critical":return"#f5222d";default:return"#d9d9d9"}};return r?e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{description:"Loading Capacity AI..."})):e.createElement("div",{style:{minHeight:"400px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",borderRadius:"8px",padding:"12px",marginBottom:"16px"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(o,null,e.createElement(n,null,e.createElement(P,{style:{color:"#722ed1"}}),e.createElement("span",{style:{fontWeight:"500",color:"#595959"}},"AI Capacity Planner v3.2"),e.createElement("div",{style:{background:"#52c41a",color:"white",padding:"2px 8px",borderRadius:"10px",fontSize:"10px",fontWeight:"600"}},"PREDICTIVE"))),e.createElement(o,null,e.createElement(n,{size:"small"},e.createElement(z,{value:l,onChange:c,size:"small",style:{width:120,borderRadius:"6px"}},e.createElement(T,{value:"overview"},"Overview"),e.createElement(T,{value:"machines"},"Machines"),e.createElement(T,{value:"forecast"},"Forecast")),e.createElement(u,{size:"small",icon:e.createElement(ie,null),style:{borderRadius:"6px"}},"Refresh"))))),l==="overview"&&e.createElement("div",null,e.createElement(E,{gutter:[16,16],style:{marginBottom:"20px"}},e.createElement(o,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Current Utilization",value:y.currentUtilization,suffix:"%",precision:1,valueStyle:{color:"#722ed1",fontSize:"18px",fontWeight:"700"}}))),e.createElement(o,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Planned Capacity",value:y.plannedCapacity,suffix:"%",precision:1,valueStyle:{color:"#1890ff",fontSize:"18px",fontWeight:"700"}}))),e.createElement(o,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Forecast Accuracy",value:y.forecastAccuracy,suffix:"%",precision:1,valueStyle:{color:"#52c41a",fontSize:"18px",fontWeight:"700"}}))),e.createElement(o,{span:6},e.createElement(p,{size:"small",style:{textAlign:"center",borderRadius:"8px"}},e.createElement(h,{title:"Optimization Potential",value:y.optimizationPotential,suffix:"%",precision:1,valueStyle:{color:"#fa8c16",fontSize:"18px",fontWeight:"700"}})))),e.createElement(p,{title:"AI Capacity Insights",size:"small",style:{borderRadius:"10px",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"}},e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},e.createElement("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #52c41a20"}},e.createElement(n,null,e.createElement(F,{style:{color:"#52c41a"}}),e.createElement("span",{style:{fontWeight:"500",color:"#52c41a"}},"Opportunity:"),e.createElement("span",null,"Machine M002 can handle 16% more load during peak hours"))),e.createElement("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #fa8c1620"}},e.createElement(n,null,e.createElement(R,{style:{color:"#fa8c16"}}),e.createElement("span",{style:{fontWeight:"500",color:"#fa8c16"}},"Alert:"),e.createElement("span",null,"Week 4 shows 101% utilization - consider capacity adjustment"))),e.createElement("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #722ed120"}},e.createElement(n,null,e.createElement(C,{style:{color:"#722ed1"}}),e.createElement("span",{style:{fontWeight:"500",color:"#722ed1"}},"Recommendation:"),e.createElement("span",null,"Redistribute 8% load from M003 to M002 for optimal balance")))))),l==="machines"&&e.createElement("div",null,e.createElement("h4",{style:{marginBottom:"16px",color:"#722ed1"}},"Machine Capacity Analysis"),e.createElement(E,{gutter:[16,16]},y.machines.map(s=>e.createElement(o,{xs:24,md:8,key:s.id},e.createElement(p,{title:s.name,size:"small",style:{borderRadius:"12px",border:`2px solid ${t(s.status)}20`,background:`linear-gradient(135deg, ${t(s.status)}05 0%, ${t(s.status)}10 100%)`},extra:e.createElement("div",{style:{background:t(s.status),color:"white",padding:"2px 8px",borderRadius:"10px",fontSize:"10px",fontWeight:"600",textTransform:"uppercase"}},s.status)},e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},e.createElement("div",null,e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Current Load"),e.createElement(S,{percent:s.currentLoad,strokeColor:t(s.status),showInfo:!0,format:x=>`${x}%`})),e.createElement("div",null,e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Forecast Load"),e.createElement(S,{percent:s.forecastLoad,strokeColor:"#1890ff",showInfo:!0,format:x=>`${x}%`})),e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",null,e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c"}},"Efficiency"),e.createElement("div",{style:{fontSize:"14px",fontWeight:"600",color:s.efficiency>85?"#52c41a":"#fa8c16"}},s.efficiency,"%")),e.createElement(u,{size:"small",type:"primary",style:{borderRadius:"6px"}},"Optimize")))))))),l==="forecast"&&e.createElement("div",null,e.createElement("h4",{style:{marginBottom:"16px",color:"#722ed1"}},"Weekly Capacity Forecast"),e.createElement(n,{direction:"vertical",size:"middle",style:{width:"100%"}},y.weeklyForecast.map((s,x)=>e.createElement(p,{key:x,size:"small",style:{borderRadius:"10px",border:s.utilization>100?"2px solid #f5222d20":"2px solid #52c41a20",background:s.utilization>100?"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)":"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)"}},e.createElement(E,{gutter:16,align:"middle"},e.createElement(o,{flex:"auto"},e.createElement(n,{size:"large"},e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600",fontSize:"14px"}},s.week),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c"}},"Demand: ",s.demand,"% | Capacity: ",s.capacity,"%")))),e.createElement(o,null,e.createElement("div",{style:{width:"200px"}},e.createElement("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"}},"Utilization: ",s.utilization,"%"),e.createElement(S,{percent:Math.min(s.utilization,100),strokeColor:s.utilization>100?"#f5222d":s.utilization>95?"#fa8c16":"#52c41a",showInfo:!1}))),e.createElement(o,null,s.utilization>100?e.createElement(oe,{style:{color:"#f5222d",fontSize:"16px"}}):s.utilization>95?e.createElement(R,{style:{color:"#fa8c16",fontSize:"16px"}}):e.createElement(F,{style:{color:"#52c41a",fontSize:"16px"}}))))))))},Ue=({loading:r,filters:a})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(A,{style:{fontSize:"48px",color:"#fa8c16"}}),e.createElement("h3",{style:{color:"#fa8c16",margin:0}},"Efficiency Trends AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Deep learning trend analysis and predictive forecasting"))),_e=({loading:r,filters:a})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(k,{style:{fontSize:"48px",color:"#eb2f96"}}),e.createElement("h3",{style:{color:"#eb2f96",margin:0}},"Bottleneck Detection AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Automated bottleneck identification and resolution"))),Ke=({loading:r,filters:a})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(_,{style:{fontSize:"48px",color:"#13c2c2"}}),e.createElement("h3",{style:{color:"#13c2c2",margin:0}},"Predictive Maintenance AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Machine failure prediction and preventive maintenance"))),Je=({loading:r,filters:a})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(X,{style:{fontSize:"48px",color:"#f5222d"}}),e.createElement("h3",{style:{color:"#f5222d",margin:0}},"Cost Optimization AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"AI-powered cost reduction and ROI optimization"))),Xe=({loading:r,filters:a})=>e.createElement("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)",borderRadius:"12px"}},e.createElement(n,{direction:"vertical",align:"center"},e.createElement(R,{style:{fontSize:"48px",color:"#a0d911"}}),e.createElement("h3",{style:{color:"#a0d911",margin:0}},"Yield Optimization AI"),e.createElement("p",{style:{color:"#8c8c8c",textAlign:"center"}},"Maximize production yield through AI insights"))),Ze=({loading:r,filters:a})=>{const[l,c]=g.useState("overview"),i=[{key:"ai-part-performance",title:"AI Part Performance Analyzer",description:"Machine learning models analyzing part production patterns",component:e.createElement(qe,{loading:r,filters:a}),icon:e.createElement(v,null),badge:"ML",color:"#1890ff"},{key:"production-optimization",title:"Production Optimization Engine",description:"Real-time optimization recommendations",component:e.createElement(Ge,{loading:r,filters:a}),icon:e.createElement(C,null),badge:"AI",color:"#52c41a"},{key:"capacity-planning",title:"Intelligent Capacity Planning",description:"AI-driven capacity forecasting and planning",component:e.createElement(Qe,{loading:r,filters:a}),icon:e.createElement(P,null),badge:"Predictive",color:"#722ed1"},{key:"efficiency-trends",title:"Efficiency Trend Analysis",description:"Deep learning trend analysis and forecasting",component:e.createElement(Ue,{loading:r,filters:a}),icon:e.createElement(A,null),badge:"Trend",color:"#fa8c16"}],f=[{key:"bottleneck-analysis",title:"AI Bottleneck Detection",description:"Automated bottleneck identification and resolution",component:e.createElement(_e,{loading:r,filters:a}),icon:e.createElement(k,null),badge:"Auto",color:"#eb2f96"},{key:"predictive-maintenance",title:"Predictive Maintenance AI",description:"Machine failure prediction and prevention",component:e.createElement(Ke,{loading:r,filters:a}),icon:e.createElement(_,null),badge:"Predict",color:"#13c2c2"},{key:"cost-optimization",title:"Cost Optimization Engine",description:"AI-powered cost reduction strategies",component:e.createElement(Je,{loading:r,filters:a}),icon:e.createElement(X,null),badge:"$$",color:"#f5222d"},{key:"yield-optimization",title:"Yield Optimization AI",description:"Maximize production yield through AI insights",component:e.createElement(Xe,{loading:r,filters:a}),icon:e.createElement(R,null),badge:"Yield",color:"#a0d911"}],y=[{key:"overview",label:e.createElement(n,null,e.createElement(P,null),e.createElement("span",null,"Overview Analytics"),e.createElement(m,{count:i.length,style:{backgroundColor:"#1890ff"}})),children:e.createElement(E,{gutter:[24,24]},i.map(t=>e.createElement(o,{xs:24,lg:12,key:t.key},e.createElement(p,{title:e.createElement(n,null,e.createElement("div",{style:{background:`linear-gradient(135deg, ${t.color}, ${t.color}80)`,borderRadius:"8px",padding:"6px",color:"white"}},t.icon),e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600"}},t.title),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",fontWeight:"normal"}},t.description))),extra:e.createElement(n,null,e.createElement(m,{count:t.badge,style:{backgroundColor:t.color}}),e.createElement(b,{title:"Expand"},e.createElement(u,{type:"text",icon:e.createElement(D,null),size:"small"})),e.createElement(b,{title:"Export Data"},e.createElement(u,{type:"text",icon:e.createElement(te,null),size:"small"}))),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"}},t.component))))},{key:"advanced",label:e.createElement(n,null,e.createElement(v,null),e.createElement("span",null,"Advanced AI"),e.createElement(m,{count:f.length,style:{backgroundColor:"#722ed1"}})),children:e.createElement(E,{gutter:[24,24]},f.map(t=>e.createElement(o,{xs:24,lg:12,key:t.key},e.createElement(p,{title:e.createElement(n,null,e.createElement("div",{style:{background:`linear-gradient(135deg, ${t.color}, ${t.color}80)`,borderRadius:"8px",padding:"6px",color:"white"}},t.icon),e.createElement("div",null,e.createElement("div",{style:{fontWeight:"600"}},t.title),e.createElement("div",{style:{fontSize:"12px",color:"#8c8c8c",fontWeight:"normal"}},t.description))),extra:e.createElement(n,null,e.createElement(m,{count:t.badge,style:{backgroundColor:t.color}}),e.createElement(b,{title:"Expand"},e.createElement(u,{type:"text",icon:e.createElement(D,null),size:"small"})),e.createElement(b,{title:"Export Data"},e.createElement(u,{type:"text",icon:e.createElement(te,null),size:"small"}))),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"}},t.component))))}];return e.createElement("div",{style:{background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(o,null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"}},e.createElement(v,{style:{fontSize:"24px"}})),e.createElement("div",null,e.createElement("h2",{style:{color:"white",margin:0,fontSize:"24px"}},"Production Intelligence"),e.createElement("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"}},"AI-powered production analytics and optimization")))),e.createElement(o,null,e.createElement(n,null,e.createElement(m,{count:"8 AI Models",style:{backgroundColor:"rgba(255,255,255,0.2)"}}),e.createElement(m,{count:"Real-time",style:{backgroundColor:"#52c41a"}}),e.createElement(m,{count:"ML Enabled",style:{backgroundColor:"#722ed1"}}))))),e.createElement(le,{activeKey:l,onChange:c,items:y,type:"card",tabBarStyle:{background:"white",borderRadius:"12px",padding:"8px",marginBottom:"20px",border:"none",boxShadow:"0 4px 16px rgba(0,0,0,0.06)"}}))},et=({loading:r,filters:a})=>{const l=[{title:"Operator Performance 360°",description:"Comprehensive operator performance analytics",icon:e.createElement($,null),color:"#1890ff"},{title:"Skills Assessment AI",description:"AI-powered skills gap analysis",icon:e.createElement(W,null),color:"#52c41a"},{title:"Training Optimization",description:"Personalized training recommendations",icon:e.createElement(ue,null),color:"#722ed1"},{title:"Productivity Insights",description:"Real-time productivity monitoring",icon:e.createElement(P,null),color:"#fa8c16"}];return e.createElement("div",{style:{background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(o,null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"}},e.createElement(W,{style:{fontSize:"24px"}})),e.createElement("div",null,e.createElement("h2",{style:{color:"white",margin:0,fontSize:"24px"}},"Operator Intelligence 360°"),e.createElement("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"}},"AI-driven operator performance and optimization analytics")))),e.createElement(o,null,e.createElement(n,null,e.createElement(m,{count:"12 AI Models",style:{backgroundColor:"rgba(255,255,255,0.2)"}}),e.createElement(m,{count:"360° View",style:{backgroundColor:"#eb2f96"}}))))),e.createElement(E,{gutter:[24,24]},l.map((c,i)=>e.createElement(o,{xs:24,md:12,key:i},e.createElement(p,{style:{height:"200px",borderRadius:"16px",border:"none",background:`linear-gradient(135deg, ${c.color}10 0%, ${c.color}05 100%)`,boxShadow:"0 8px 24px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement("div",{style:{background:c.color,borderRadius:"50%",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px"}},c.icon),e.createElement("div",null,e.createElement("h3",{style:{margin:0,color:c.color}},c.title),e.createElement("p",{style:{margin:"8px 0 0 0",color:"#8c8c8c"}},c.description))))))))},tt=({loading:r,filters:a})=>{const l=[{title:"Weight Pattern ML",description:"Machine learning weight pattern analysis",icon:e.createElement(j,null),color:"#fa8c16"},{title:"Variance Prediction",description:"AI-powered weight variance prediction",icon:e.createElement(A,null),color:"#52c41a"},{title:"Quality Correlation",description:"Weight-quality correlation analysis",icon:e.createElement(k,null),color:"#722ed1"},{title:"Optimization Engine",description:"Weight optimization recommendations",icon:e.createElement(ne,null),color:"#1890ff"}];return e.createElement("div",{style:{background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"}},e.createElement("div",{style:{background:"linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"}},e.createElement(E,{justify:"space-between",align:"middle"},e.createElement(o,null,e.createElement(n,{align:"center",size:"large"},e.createElement("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"}},e.createElement(ne,{style:{fontSize:"24px"}})),e.createElement("div",null,e.createElement("h2",{style:{color:"white",margin:0,fontSize:"24px"}},"Weight Analytics Intelligence"),e.createElement("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"}},"Advanced weight analysis and optimization using machine learning")))),e.createElement(o,null,e.createElement(n,null,e.createElement(m,{count:"ML Powered",style:{backgroundColor:"#faad14"}}),e.createElement(m,{count:"Precision+",style:{backgroundColor:"#13c2c2"}}))))),e.createElement(E,{gutter:[24,24]},l.map((c,i)=>e.createElement(o,{xs:24,md:12,key:i},e.createElement(p,{style:{height:"200px",borderRadius:"16px",border:"none",background:`linear-gradient(135deg, ${c.color}10 0%, ${c.color}05 100%)`,boxShadow:"0 8px 24px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement("div",{style:{background:c.color,borderRadius:"50%",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px"}},c.icon),e.createElement("div",null,e.createElement("h3",{style:{margin:0,color:c.color}},c.title),e.createElement("p",{style:{margin:"8px 0 0 0",color:"#8c8c8c"}},c.description))))))))},nt=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(C,{style:{fontSize:"64px",color:"#13c2c2"}}),e.createElement("h2",{style:{color:"#13c2c2",margin:0}},"Filter Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Smart filtering and data intelligence coming soon")))),at=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(A,{style:{fontSize:"64px",color:"#52c41a"}}),e.createElement("h2",{style:{color:"#52c41a",margin:0}},"Business Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Financial analytics and ROI optimization coming soon")))),rt=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(se,{style:{fontSize:"64px",color:"#fa541c"}}),e.createElement("h2",{style:{color:"#fa541c",margin:0}},"Maintenance Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Predictive maintenance and equipment optimization coming soon")))),lt=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement($,{style:{fontSize:"64px",color:"#eb2f96"}}),e.createElement("h2",{style:{color:"#eb2f96",margin:0}},"Quality Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"AI-powered quality assurance and optimization coming soon")))),it=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(ee,{style:{fontSize:"64px",color:"#f5222d"}}),e.createElement("h2",{style:{color:"#f5222d",margin:0}},"Performance Optimization"),e.createElement("p",{style:{color:"#8c8c8c"}},"Automated performance enhancement and optimization coming soon")))),ct=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(R,{style:{fontSize:"64px",color:"#faad14"}}),e.createElement("h2",{style:{color:"#faad14",margin:0}},"Real-time Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Live monitoring and real-time analytics coming soon")))),ot=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(k,{style:{fontSize:"64px",color:"#096dd9"}}),e.createElement("h2",{style:{color:"#096dd9",margin:0}},"Strategic Intelligence"),e.createElement("p",{style:{color:"#8c8c8c"}},"Long-term strategic planning and future insights coming soon")))),st=({loading:r,filters:a})=>e.createElement("div",{style:{background:"linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(p,{style:{textAlign:"center",border:"none",background:"transparent"}},e.createElement(n,{direction:"vertical",align:"center",size:"large"},e.createElement(Z,{style:{fontSize:"64px",color:"#a0d911"}}),e.createElement("h2",{style:{color:"#a0d911",margin:0}},"Continuous Improvement"),e.createElement("p",{style:{color:"#8c8c8c"}},"Self-learning systems and continuous optimization coming soon")))),{Content:dt}=de,{Title:jt,Text:Lt}=re,Tt=()=>{const[r,a]=g.useState("production"),[l,c]=g.useState(!1),[i,f]=g.useState({dateRange:null,machine:null,partNumber:null,operator:null,shift:null}),y=g.useCallback(x=>{f(O=>({...O,...x}))},[]),t=g.useCallback(x=>{a(x)},[]),s=g.useMemo(()=>[{key:"production",label:e.createElement(n,null,e.createElement(v,{style:{color:"#1890ff"}}),e.createElement("span",null,"Production Intelligence"),e.createElement(m,{count:"AI",style:{backgroundColor:"#52c41a"}})),children:e.createElement(Ze,{loading:l,filters:i})},{key:"operator",label:e.createElement(n,null,e.createElement(W,{style:{color:"#722ed1"}}),e.createElement("span",null,"Operator Intelligence"),e.createElement(m,{count:"360°",style:{backgroundColor:"#eb2f96"}})),children:e.createElement(et,{loading:l,filters:i})},{key:"weight",label:e.createElement(n,null,e.createElement(j,{style:{color:"#fa8c16"}}),e.createElement("span",null,"Weight Analytics"),e.createElement(m,{count:"ML",style:{backgroundColor:"#faad14"}})),children:e.createElement(tt,{loading:l,filters:i})},{key:"filters",label:e.createElement(n,null,e.createElement(C,{style:{color:"#13c2c2"}}),e.createElement("span",null,"Filter Intelligence"),e.createElement(m,{count:"Smart",style:{backgroundColor:"#1890ff"}})),children:e.createElement(nt,{loading:l,filters:i})},{key:"business",label:e.createElement(n,null,e.createElement(A,{style:{color:"#52c41a"}}),e.createElement("span",null,"Business Intelligence"),e.createElement(m,{count:"ROI",style:{backgroundColor:"#f5222d"}})),children:e.createElement(at,{loading:l,filters:i})},{key:"maintenance",label:e.createElement(n,null,e.createElement(se,{style:{color:"#fa541c"}}),e.createElement("span",null,"Maintenance Intelligence"),e.createElement(m,{count:"Predictive",style:{backgroundColor:"#722ed1"}})),children:e.createElement(rt,{loading:l,filters:i})},{key:"quality",label:e.createElement(n,null,e.createElement($,{style:{color:"#eb2f96"}}),e.createElement("span",null,"Quality Intelligence"),e.createElement(m,{count:"ML",style:{backgroundColor:"#13c2c2"}})),children:e.createElement(lt,{loading:l,filters:i})},{key:"performance",label:e.createElement(n,null,e.createElement(ee,{style:{color:"#f5222d"}}),e.createElement("span",null,"Performance Optimization"),e.createElement(m,{count:"Auto",style:{backgroundColor:"#fa8c16"}})),children:e.createElement(it,{loading:l,filters:i})},{key:"realtime",label:e.createElement(n,null,e.createElement(R,{style:{color:"#faad14"}}),e.createElement("span",null,"Real-time Intelligence"),e.createElement(m,{count:"Live",style:{backgroundColor:"#f5222d"}})),children:e.createElement(ct,{loading:l,filters:i})},{key:"strategic",label:e.createElement(n,null,e.createElement(k,{style:{color:"#096dd9"}}),e.createElement("span",null,"Strategic Intelligence"),e.createElement(m,{count:"Future",style:{backgroundColor:"#722ed1"}})),children:e.createElement(ot,{loading:l,filters:i})},{key:"improvement",label:e.createElement(n,null,e.createElement(Z,{style:{color:"#a0d911"}}),e.createElement("span",null,"Continuous Improvement"),e.createElement(m,{count:"Learning",style:{backgroundColor:"#52c41a"}})),children:e.createElement(st,{loading:l,filters:i})}],[l,i]);return e.createElement(de,{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:`
          radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%)
        `,animation:"float 20s ease-in-out infinite",zIndex:0}}),e.createElement(dt,{style:{padding:"24px",position:"relative",zIndex:1}},e.createElement("div",{style:{maxWidth:"1600px",margin:"0 auto",background:"rgba(255,255,255,0.95)",borderRadius:"20px",padding:"32px",backdropFilter:"blur(10px)",boxShadow:"0 20px 40px rgba(0,0,0,0.1)"}},e.createElement(Fe,null),e.createElement(Ye,{filters:i,onFilterChange:y,loading:l}),e.createElement(Ne,{loading:l,filters:i}),e.createElement(p,{style:{marginTop:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"}},e.createElement(le,{activeKey:r,onChange:t,type:"card",size:"large",items:s,tabBarStyle:{borderBottom:"2px solid #f0f0f0",marginBottom:"24px"},tabBarGutter:8})))),e.createElement("style",{jsx:!0,global:!0},`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          33% {
            transform: translateY(-10px) rotate(1deg);
          }
          66% {
            transform: translateY(5px) rotate(-1deg);
          }
        }
        
        @keyframes glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
          }
          50% {
            box-shadow: 0 0 30px rgba(24, 144, 255, 0.5);
          }
        }
        
        .ant-tabs-tab {
          border-radius: 12px !important;
          transition: all 0.3s ease !important;
        }
        
        .ant-tabs-tab:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        }
        
        .ant-tabs-tab-active {
          background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
          color: white !important;
          border-color: transparent !important;
        }
        
        .ant-tabs-tab-active .anticon,
        .ant-tabs-tab-active span {
          color: white !important;
        }
        
        .ant-card {
          transition: all 0.3s ease !important;
        }
        
        .ant-card:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 12px 28px rgba(0,0,0,0.12) !important;
        }
        
        .ant-badge-count {
          border-radius: 10px !important;
          font-size: 10px !important;
          line-height: 18px !important;
          min-width: 18px !important;
          height: 18px !important;
        }
      `))};export{Tt as default};
