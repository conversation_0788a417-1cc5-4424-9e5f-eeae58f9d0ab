/**
 * Filter Coordinator Hook
 * Manages coordination between frontend filters and backend data processing
 * Replaces the problematic useDataSourceMonitor with better filter-aware coordination
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { notification } from 'antd';
import { ThunderboltOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const useFilterCoordinator = (filterState) => {
  const [coordinationStatus, setCoordinationStatus] = useState({
    isCoordinated: false,
    lastSync: null,
    pendingFilters: 0,
    dataSourceHealth: 'unknown',
    filterQueue: []
  });

  const [isProcessingFilters, setIsProcessingFilters] = useState(false);
  const filterTimeoutRef = useRef(null);
  const healthCheckIntervalRef = useRef(null);

  // Extract filter dependencies for coordination
  const {
    selectedMachineModel,
    selectedMachine,
    dateFilter,
    dateRangeType,
    loading
  } = filterState || {};

  // Debounced filter coordination
  const coordinateFilters = useCallback(async () => {
    if (isProcessingFilters) {
      console.log('🔄 [FILTER COORDINATOR] Filter processing already in progress, queuing request');
      return;
    }

    setIsProcessingFilters(true);
    console.log('🔄 [FILTER COORDINATOR] Starting filter coordination:', {
      model: selectedMachineModel,
      machine: selectedMachine,
      date: dateFilter ? (typeof dateFilter === 'object' ? dateFilter.format?.('YYYY-MM-DD') : dateFilter) : null,
      dateRangeType
    });

    try {
      // Test backend coordination with current filters
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query TestFilterCoordination($filters: ProductionFilters) {
              enhancedGetProductionSidecards(filters: $filters) {
                dataSource
                goodqty
                rejetqty
              }
            }
          `,
          variables: {
            filters: {
              dateRangeType,
              model: selectedMachineModel || undefined,
              machine: selectedMachine || undefined,
              date: dateFilter ? (typeof dateFilter === 'object' ? dateFilter.format?.('YYYY-MM-DD') : dateFilter) : undefined
            }
          }
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        console.warn('🔄 [FILTER COORDINATOR] GraphQL errors during coordination test:', result.errors);
        setCoordinationStatus(prev => ({
          ...prev,
          isCoordinated: false,
          lastSync: new Date().toISOString(),
          dataSourceHealth: 'error'
        }));
        return false;
      }

      const dataSource = result.data?.enhancedGetProductionSidecards?.dataSource || 'unknown';
      const hasData = (result.data?.enhancedGetProductionSidecards?.goodqty || 0) > 0 || 
                     (result.data?.enhancedGetProductionSidecards?.rejetqty || 0) > 0;

      setCoordinationStatus(prev => ({
        ...prev,
        isCoordinated: true,
        lastSync: new Date().toISOString(),
        pendingFilters: 0,
        dataSourceHealth: hasData ? 'healthy' : 'no-data',
        filterQueue: []
      }));

      console.log('✅ [FILTER COORDINATOR] Coordination successful:', {
        dataSource,
        hasData,
        timestamp: new Date().toISOString()
      });

      // Show success notification only for significant filter changes
      if (selectedMachineModel || selectedMachine || dateFilter) {
        notification.success({
          message: 'Filters Applied',
          description: `Data updated using ${dataSource} source`,
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
          duration: 2,
          placement: 'bottomRight'
        });
      }

      return true;
    } catch (error) {
      console.error('❌ [FILTER COORDINATOR] Coordination failed:', error);
      
      setCoordinationStatus(prev => ({
        ...prev,
        isCoordinated: false,
        lastSync: new Date().toISOString(),
        dataSourceHealth: 'error'
      }));

      // Show error notification for coordination failures
      notification.error({
        message: 'Filter Coordination Failed',
        description: 'Unable to sync filters with backend. Data may be stale.',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        duration: 4,
        placement: 'bottomRight'
      });

      return false;
    } finally {
      setIsProcessingFilters(false);
    }
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType, isProcessingFilters]);

  // Debounced filter coordination trigger
  useEffect(() => {
    if (loading) {
      console.log('🔄 [FILTER COORDINATOR] Skipping coordination - data is loading');
      return;
    }

    // Clear existing timeout
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }

    // Queue filter coordination with debounce
    setCoordinationStatus(prev => ({
      ...prev,
      pendingFilters: prev.pendingFilters + 1
    }));

    filterTimeoutRef.current = setTimeout(() => {
      coordinateFilters();
    }, 500); // 500ms debounce for filter changes

    return () => {
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType, loading, coordinateFilters]);

  // Periodic health check (less frequent than the old useDataSourceMonitor)
  useEffect(() => {
    const performHealthCheck = async () => {
      if (isProcessingFilters) {
        console.log('🏥 [FILTER COORDINATOR] Skipping health check - filters are processing');
        return;
      }

      try {
        const response = await fetch('/api/graphql', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            query: `
              query HealthCheck {
                enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) {
                  dataSource
                }
              }
            `
          })
        });

        const result = await response.json();
        const dataSource = result.data?.enhancedGetProductionSidecards?.dataSource || 'unknown';
        
        setCoordinationStatus(prev => ({
          ...prev,
          dataSourceHealth: dataSource === 'elasticsearch' ? 'elasticsearch' : 'mysql'
        }));

        console.log('🏥 [FILTER COORDINATOR] Health check completed:', { dataSource });
      } catch (error) {
        console.warn('🏥 [FILTER COORDINATOR] Health check failed:', error);
        setCoordinationStatus(prev => ({
          ...prev,
          dataSourceHealth: 'error'
        }));
      }
    };

    // Initial health check
    performHealthCheck();

    // Set up periodic health checks every 60 seconds (less frequent than before)
    healthCheckIntervalRef.current = setInterval(performHealthCheck, 60000);

    return () => {
      if (healthCheckIntervalRef.current) {
        clearInterval(healthCheckIntervalRef.current);
      }
    };
  }, [isProcessingFilters]);

  // Manual coordination trigger
  const triggerCoordination = useCallback(async () => {
    console.log('🔄 [FILTER COORDINATOR] Manual coordination triggered');
    return await coordinateFilters();
  }, [coordinateFilters]);

  // Get coordination status for UI display
  const getCoordinationStatus = useCallback(() => {
    const { isCoordinated, lastSync, pendingFilters, dataSourceHealth } = coordinationStatus;
    
    let status = 'unknown';
    let message = 'Filter coordination status unknown';
    let color = 'default';

    if (isProcessingFilters) {
      status = 'processing';
      message = `Processing ${pendingFilters} filter(s)...`;
      color = 'processing';
    } else if (isCoordinated && dataSourceHealth !== 'error') {
      status = 'coordinated';
      message = `Filters synchronized (${dataSourceHealth})`;
      color = 'success';
    } else if (pendingFilters > 0) {
      status = 'pending';
      message = `${pendingFilters} filter(s) pending coordination`;
      color = 'warning';
    } else if (dataSourceHealth === 'error') {
      status = 'error';
      message = 'Coordination error - check connection';
      color = 'error';
    }

    return {
      status,
      message,
      color,
      lastSync,
      dataSourceHealth,
      isProcessing: isProcessingFilters
    };
  }, [coordinationStatus, isProcessingFilters]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
      if (healthCheckIntervalRef.current) {
        clearInterval(healthCheckIntervalRef.current);
      }
    };
  }, []);

  return {
    coordinationStatus: getCoordinationStatus(),
    triggerCoordination,
    isProcessingFilters
  };
};

export default useFilterCoordinator;
