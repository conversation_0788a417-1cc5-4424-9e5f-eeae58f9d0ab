import React from "react";
import { createContext, useState, useEffect, useCallback, useContext } from "react"
import ky from 'ky';

// 🔒 SECURITY: Using Ky HTTP client for superior cookie management
// Ky provides automatic cookie handling, retry logic, and better error handling
import { message, Modal } from "antd"
import { extractResponseData, isResponseSuccessful, getErrorMessage } from "../utils/apiUtils"
import sessionManager from "../utils/sessionManager"
import { getRedirectPathByPermissions } from "../utils/redirectUtils"
import { usePermission } from "../hooks/usePermission"

// Create the context
const AuthContext = createContext()

// Provider component
const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true) // Start with loading true
  const [sessionWarningVisible, setSessionWarningVisible] = useState(false)
  const [redirectPath, setRedirectPath] = useState('/profile') // Default redirect path
  const baseURL = (() => {
    // Check if we're in a browser environment first
    if (typeof window !== 'undefined') {
      const currentOrigin = window.location.origin;
      console.log('🔌 AuthContext current origin:', currentOrigin);

      // If running on ngrok domain, use the same origin (unified architecture)
      if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
        console.log('🔌 AuthContext detected ngrok deployment - using same origin:', currentOrigin);
        return currentOrigin;
      }

      // For local development, check environment variable first
      if (import.meta.env.VITE_API_URL) {
        console.log('🔌 AuthContext using VITE_API_URL for local development:', import.meta.env.VITE_API_URL);
        return import.meta.env.VITE_API_URL;
      }

      // Fallback to current origin for local development
      console.log('🔌 AuthContext using current origin for local development:', currentOrigin);
      return currentOrigin;
    }

    // Fallback for server-side rendering
    return "http://localhost:5000";
  })();

  // Session timeout duration (30 minutes)
  const SESSION_TIMEOUT = 30 * 60 * 1000

  // 🔒 SECURITY: Create Ky instance with optimal configuration for authentication
  // Ky provides superior cookie management, automatic retries, and better error handling
  const api = ky.create({
    prefixUrl: baseURL,
    credentials: 'include', // ✅ CRITICAL: Automatic cookie handling
    timeout: 30000,         // 30 second timeout
    retry: {
      limit: 2,             // Retry failed requests twice
      methods: ['get', 'post', 'put', 'delete'],
      statusCodes: [408, 413, 429, 500, 502, 503, 504]
    },
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    hooks: {
      beforeRequest: [
        request => {
          console.log(`🔍 [AuthContext] ${request.method} request to: ${request.url}`);
          console.log(`🔍 [AuthContext] Request configured with credentials: 'include'`);
        }
      ],
      afterResponse: [
        (request, options, response) => {
          console.log(`✅ [AuthContext] Response ${response.status} from: ${request.url}`);
          return response;
        }
      ]
    }
  });

  // 🔒 SECURITY: Helper function to create authenticated requests using Ky
  // Uses HTTP-only cookies exclusively for security
  const createAuthRequest = async (method, url, body = null) => {
    try {
      // Remove leading slash when using prefixUrl
      const cleanUrl = url.startsWith('/') ? url.substring(1) : url;

      if (body) {
        return await api[method](cleanUrl, { json: body }).json();
      } else {
        return await api[method](cleanUrl).json();
      }
    } catch (error) {
      // Ky throws HTTPError for non-2xx responses
      if (error.name === 'HTTPError') {
        throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
      }
      throw error;
    }
    // Note: No Authorization header needed - backend uses HTTP-only cookies
    // Ky automatically includes cookies with credentials: 'include'
  };

  // Configure message component to use dynamic theme
  useEffect(() => {
    message.config({
      top: 24,
      duration: 3,
      maxCount: 3
    })
  }, [])

  // Handle session timeout warning
  const handleSessionWarning = useCallback(() => {
    setSessionWarningVisible(true)
  }, [])

  // Handle session timeout
  const handleSessionTimeout = useCallback(() => {
    // Force logout when session expires
    logout()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  // Extend session
  const extendSession = useCallback(async () => {
    try {
      // Call the backend to refresh the session
      await createAuthRequest('get', '/api/refresh-session')

      // Reset the session timeout
      sessionManager.extendSession()

      // Hide the warning modal
      setSessionWarningVisible(false)
    } catch (error) {
      console.error("Failed to extend session:", error)
      // If we can't extend the session, log the user out
      logout()
    }
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  // Check if user is already logged in
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        console.log('🔍 [AuthContext] Checking authentication status...');
        console.log('🔍 [AuthContext] Base URL:', baseURL);
        console.log('🔍 [AuthContext] Current origin:', typeof window !== 'undefined' ? window.location.origin : 'N/A');

        // Make request with credentials to check if user is logged in via HTTP-only cookie
        const response = await createAuthRequest('get', '/api/me')
        console.log('🔍 [AuthContext] Auth check response success:', response.success);
        console.log('🔍 [AuthContext] Auth check response data:', response.data ? 'present' : 'missing');

        if (response.success) {
          // Extract user data from response (handles both old and new formats)
          const userData = response.data || response;
          console.log('🔍 [AuthContext] User data:', userData);
          setUser(userData)
          setIsAuthenticated(true)

          // Initialize session manager when user is authenticated
          sessionManager.init({
            timeoutDuration: SESSION_TIMEOUT,
            logoutCallback: handleSessionTimeout,
            warningCallback: handleSessionWarning,
            warningThreshold: 0.8 // Show warning when 80% of timeout has elapsed
          })

          // Determine the best redirect path based on user permissions
          const hasPermission = (requiredPermissions) => {
            // Admin users have all permissions
            if (userData.role === 'admin') {
              return true;
            }

            // Convert single permission to array
            const permissions = Array.isArray(requiredPermissions)
              ? requiredPermissions
              : [requiredPermissions];

            // Get user permissions from all sources
            const userPermissions = userData.all_permissions
              ? userData.all_permissions
              : [
                  ...(Array.isArray(userData.permissions) ? userData.permissions : []),
                  ...(Array.isArray(userData.role_permissions) ? userData.role_permissions : []),
                  ...(Array.isArray(userData.hierarchy_permissions) ? userData.hierarchy_permissions : [])
                ];

            // Check if user has any of the required permissions
            return permissions.some(permission => userPermissions.includes(permission));
          };

          const hasRole = (roles) => {
            // Convert single role to array
            const roleList = Array.isArray(roles) ? roles : [roles];
            // Check if user's role is in the list
            return roleList.includes(userData.role);
          };

          // Get the best redirect path
          const bestPath = getRedirectPathByPermissions(userData, hasPermission, hasRole);
          setRedirectPath(bestPath);
        }
      } catch (error) {
        console.error("🔍 [AuthContext] Auth check failed:", error)
        console.error("🔍 [AuthContext] Error details:", {
          message: error.message,
          status: error.status,
          response: error.response?.body || error.response,
          url: `${baseURL}/api/me`
        });

        // Provide specific error messages based on error type
        if (error.code === 'ECONNREFUSED') {
          console.error("🔍 [AuthContext] Connection refused - backend server may not be running");
        } else if (error.status === 401) {
          console.log("🔍 [AuthContext] User not authenticated (401) - this is normal for logged out users");
        } else if (error.status === 404) {
          console.error("🔍 [AuthContext] /api/me endpoint not found (404) - check backend routes");
        } else if (error.status >= 500) {
          console.error("🔍 [AuthContext] Server error (5xx) - check backend logs");
        }

        // Authentication failed, but we don't need to do anything special
        // since we're not using localStorage anymore
      } finally {
        // Set loading to false after we've checked authentication
        console.log('🔍 [AuthContext] Setting loading to false');
        setLoading(false)
      }
    }

    checkAuthStatus()

    // Cleanup session manager on unmount
    return () => {
      sessionManager.cleanup()
    }
  }, [])

  // Login function
  const login = async (credentials) => {
    try {
      // Send login request with credentials
      // The backend will set the HTTP-only cookie
      const response = await createAuthRequest('post', '/api/login', credentials)

      if (response.success) {
        // Extract data from response (handles both old and new formats)
        const responseData = response.data || response;
        const userData = responseData.user || response.user;

        // 🔒 SECURITY: No localStorage usage for sensitive data
        // Authentication token is securely stored in HTTP-only cookie by backend
        console.log('🔑 Authentication successful - using secure HTTP-only cookies');

        // Update state
        setUser(userData)
        setIsAuthenticated(true)

        // 🔍 VERIFICATION: Test authentication immediately after login
        console.log('🔍 [AuthContext] Login successful, verifying authentication...');
        try {
          // Small delay to ensure cookie is set
          await new Promise(resolve => setTimeout(resolve, 100));

          const verifyResponse = await createAuthRequest('get', '/api/me');
          console.log('✅ [AuthContext] Authentication verification successful:', verifyResponse.success);
        } catch (verifyError) {
          console.error('❌ [AuthContext] Authentication verification failed:', verifyError);
          console.error('❌ [AuthContext] This indicates a cookie handling issue');
          // Don't fail the login, but log the issue
        }

        // Determine the best redirect path based on user permissions
        // We need to create temporary permission checking functions since usePermission hook
        // can't be used directly here (it depends on the updated user state)
        const hasPermission = (requiredPermissions) => {
          // Admin users have all permissions
          if (userData.role === 'admin') {
            return true;
          }

          // Convert single permission to array
          const permissions = Array.isArray(requiredPermissions)
            ? requiredPermissions
            : [requiredPermissions];

          // Get user permissions from all sources
          const userPermissions = userData.all_permissions
            ? userData.all_permissions
            : [
                ...(Array.isArray(userData.permissions) ? userData.permissions : []),
                ...(Array.isArray(userData.role_permissions) ? userData.role_permissions : []),
                ...(Array.isArray(userData.hierarchy_permissions) ? userData.hierarchy_permissions : [])
              ];

          // Check if user has any of the required permissions
          return permissions.some(permission => userPermissions.includes(permission));
        };

        const hasRole = (roles) => {
          // Convert single role to array
          const roleList = Array.isArray(roles) ? roles : [roles];
          // Check if user's role is in the list
          return roleList.includes(userData.role);
        };

        // Get the best redirect path
        const bestPath = getRedirectPathByPermissions(userData, hasPermission, hasRole);
        setRedirectPath(bestPath);

        return { success: true, redirectPath: bestPath }
      } else {
        const errorMessage = response.message || "Login failed"
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Login failed. Please try again."
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      const response = await createAuthRequest('put', '/api/users/update-profile', profileData)

      if (response.success) {
        // Extract user data from response (handles both old and new formats)
        const userData = response.data || response;
        setUser(userData)

        const successMessage = response.message || "Profil mis à jour avec succès";
        message.success(successMessage)
        return { success: true }
      } else {
        const errorMessage = response.message || "Échec de la mise à jour du profil";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Échec de la mise à jour du profil";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  const logout = async () => {
    try {
      // Call the logout endpoint to clear the HTTP-only cookie on the server
      await createAuthRequest('get', '/api/logout')
      console.log('✅ Server logout successful')
    } catch (error) {
      console.error("❌ Logout error:", error)
      // Continue with client-side logout even if server call fails
    } finally {
      // 🔒 SECURITY: No localStorage cleanup needed for auth data
      // HTTP-only cookie is securely cleared by the backend logout endpoint
      console.log('🔑 Logout complete - secure HTTP-only cookie cleared by server');

      // Update auth state regardless of server response
      setUser(null)
      setIsAuthenticated(false)

      // Clean up session manager
      sessionManager.cleanup()
    }
  }

  // Change password function
  const changePassword = async (passwordData) => {
    try {
      const response = await createAuthRequest('put', '/api/users/change-password', passwordData)

      if (response.success) {
        const successMessage = response.message || "Mot de passe mis à jour avec succès";
        message.success(successMessage)
        return { success: true }
      } else {
        const errorMessage = response.message || "Échec de la mise à jour du mot de passe";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Échec de la mise à jour du mot de passe";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Forgot password function
  const forgotPassword = async (email) => {
    try {
      const response = await createAuthRequest('post', '/api/forgot-password', { email })

      if (response.success) {
        // Always show the same success message regardless of email existence
        message.success(
          "Si votre email est enregistré, vous recevrez des instructions de réinitialisation dans quelques minutes.",
          5,
        )
        return { success: true }
      } else {
        const errorMessage = response.message || "Échec de la demande de réinitialisation";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Échec de la demande de réinitialisation";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Reset password function
  const resetPassword = async (token, password) => {
    try {
        if (!password || password.length < 8) {
            message.error("Le mot de passe doit contenir au moins 8 caractères")
            return { success: false }
        }

        const response = await createAuthRequest('post', '/api/reset-password', {
            token,
            password,
        });

        if (response.success) {
            const successMessage = response.message || "Mot de passe réinitialisé avec succès!";
            message.success(successMessage + " Redirection...")
            setTimeout(() => (window.location.href = "/login"), 2000)
            return { success: true }
        }

        const errorMessage = response.message || "Échec de la réinitialisation";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
    } catch (error) {
        console.error("Reset password error:", error);

        let errorMessage = getErrorMessage(error) || "Erreur de connexion au serveur";

        // Handle validation errors
        if (error.response && error.response.body.errors && error.response.body.errors.length > 0) {
            errorMessage = error.response.body.errors[0].msg;
        }

        message.error(errorMessage);
        return { success: false, message: errorMessage };
    }
};


  // Verify reset token function
  const verifyResetToken = async (token) => {
    try {
      const response = await createAuthRequest('get', `/api/verify-reset-token/${token}`)

      if (response.success) {
        // Check if token has expiration info
        if (response.expiresAt) {
          const expiresAt = new Date(response.expiresAt)
          if (expiresAt < new Date()) {
            return {
              success: false,
              message: "Le lien de réinitialisation a expiré",
            }
          }
        }

        return { success: true }
      }

      return {
        success: false,
        message: response.message || "Token invalide ou expiré"
      }
    } catch (error) {
      return {
        success: false,
        message: getErrorMessage(error) || "Token invalide ou expiré",
      }
    }
  }

  // Get all users function (admin only)
  const getAllUsers = async () => {
    try {
      // Make request with credentials to use the HTTP-only cookie
      const response = await createAuthRequest('get', '/api/users');

      if (response.success) {
        return { success: true, data: response.data || [] };
      }

      return {
        success: false,
        message: response.message || "Erreur lors de la récupération des utilisateurs",
        data: []
      };
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Erreur lors de la récupération des utilisateurs";
      console.error("Error fetching users:", error);

      // If unauthorized, handle gracefully
      if (error.response?.status === 401 || error.response?.status === 403) {
        return {
          success: false,
          message: "Vous n'avez pas les droits nécessaires pour accéder à cette ressource",
          data: []
        };
      }

      return { success: false, message: errorMessage, data: [] };
    }
  }

  // Create user function (admin only)
  const createUser = async (userData) => {
    try {
      const response = await createAuthRequest('post', '/api/users', userData)

      if (response.success) {
        const successMessage = response.message || "Utilisateur créé avec succès";
        message.success(successMessage)
        return { success: true, data: response.data }
      } else {
        const errorMessage = response.message || "Erreur lors de la création de l'utilisateur";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Erreur lors de la création de l'utilisateur";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Update user function (admin only)
  const updateUser = async (userId, userData) => {
    try {
      const response = await createAuthRequest('put', `/api/users/${userId}`, userData)

      if (response.success) {
        const successMessage = response.message || "Utilisateur mis à jour avec succès";
        message.success(successMessage)
        return { success: true, data: response.data }
      } else {
        const errorMessage = response.message || "Erreur lors de la mise à jour de l'utilisateur";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Erreur lors de la mise à jour de l'utilisateur";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Delete user function (admin only)
  const deleteUser = async (userId) => {
    try {
      const response = await createAuthRequest('delete', `/api/users/${userId}`)

      if (isResponseSuccessful(response)) {
        const successMessage = response.body.message || "Utilisateur supprimé avec succès";
        message.success(successMessage)
        return { success: true }
      } else {
        const errorMessage = response.body.message || "Erreur lors de la suppression de l'utilisateur";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Erreur lors de la suppression de l'utilisateur";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Reset user password function (admin only)
  const resetUserPassword = async (userId, newPassword) => {
    try {
      const response = await createAuthRequest('post', `/api/users/${userId}/reset-password`, { newPassword })

      if (response.success) {
        const successMessage = response.message || "Mot de passe réinitialisé avec succès";
        message.success(successMessage)
        return { success: true }
      } else {
        const errorMessage = response.message || "Erreur lors de la réinitialisation du mot de passe";
        message.error(errorMessage)
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error) || "Erreur lors de la réinitialisation du mot de passe";
      message.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Calculate remaining session time in minutes
  const getRemainingSessionTime = useCallback(() => {
    const remainingMs = sessionManager.getRemainingTime();
    return Math.ceil(remainingMs / 60000); // Convert to minutes and round up
  }, []);

  // Make sure these functions are included in the context value
  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        loading,
        login,
        logout,
        updateProfile,
        changePassword,
        forgotPassword,
        verifyResetToken,
        resetPassword,
        getAllUsers,
        createUser,
        updateUser,
        deleteUser,
        resetUserPassword,
        extendSession,
        getRemainingSessionTime,
        redirectPath, // Add the redirect path to the context
        createAuthRequest, // Export the createAuthRequest function
      }}
    >
      {/* Session timeout warning modal */}
      <Modal
        title="Session Expiration Warning"
        open={sessionWarningVisible}
        onOk={extendSession}
        onCancel={logout}
        okText="Extend Session"
        cancelText="Logout"
        cancelButtonProps={{ danger: true }}
        closable={false}
        maskClosable={false}
        keyboard={false}
      >
        <p>Your session is about to expire due to inactivity.</p>
        <p>You will be automatically logged out in approximately {getRemainingSessionTime()} minutes.</p>
        <p>Do you want to extend your session?</p>
      </Modal>

      {children}
    </AuthContext.Provider>
  )
}

// Export the context and provider
export { AuthContext, AuthProvider }

// Export a custom hook for using the auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
