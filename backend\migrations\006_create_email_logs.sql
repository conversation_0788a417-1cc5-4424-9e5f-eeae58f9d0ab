-- Create email_logs table for tracking email notifications
CREATE TABLE IF NOT EXISTS email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    notification_id VARCHAR(255) NULL,
    message_id VARCHAR(255) NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject TEXT NOT NULL,
    status ENUM('sent', 'failed', 'pending', 'retry') DEFAULT 'pending',
    sent_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    retry_count INT DEFAULT 0,
    error_message TEXT NULL,
    settings_snapshot JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_notification_id (notification_id),
    INDEX idx_message_id (message_id),
    INDEX idx_recipient (recipient),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_created_at (created_at)
);

-- Create email_templates table for storing email templates
CREATE TABLE IF NOT EXISTS email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('minimal', 'standard', 'detailed') DEFAULT 'standard',
    language ENUM('fr', 'en', 'es') DEFAULT 'fr',
    subject_template TEXT NOT NULL,
    body_template_html TEXT NULL,
    body_template_text TEXT NULL,
    variables JSON NULL COMMENT 'Available template variables',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_language (language),
    INDEX idx_is_active (is_active)
);

-- Create email_queue table for batch processing and scheduling
CREATE TABLE IF NOT EXISTS email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    notification_data JSON NOT NULL,
    email_settings JSON NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    scheduled_for TIMESTAMP NULL,
    batch_id VARCHAR(100) NULL,
    status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    last_attempt_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_recipient (recipient),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_for (scheduled_for),
    INDEX idx_batch_id (batch_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create email_statistics table for tracking email metrics
CREATE TABLE IF NOT EXISTS email_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    total_sent INT DEFAULT 0,
    total_failed INT DEFAULT 0,
    total_pending INT DEFAULT 0,
    by_priority JSON NULL COMMENT 'Statistics by priority level',
    by_type JSON NULL COMMENT 'Statistics by notification type',
    by_template JSON NULL COMMENT 'Statistics by template type',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date (date),
    INDEX idx_date (date)
);

-- Insert default email templates
INSERT INTO email_templates (name, type, language, subject_template, body_template_html, body_template_text, variables) VALUES
('notification_minimal_fr', 'minimal', 'fr', '[SOMIPEM] {{type}}: {{title}}', 
'<p>{{message}}</p>', 
'{{message}}',
'{"type": "Notification type", "title": "Notification title", "message": "Notification message"}'),

('notification_standard_fr', 'standard', 'fr', '[SOMIPEM] {{type}}: {{title}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <div style="background-color: #0a3d62; color: white; padding: 20px; text-align: center;">
    <h1 style="margin: 0;">SOMIPEM</h1>
    <p style="margin: 5px 0 0 0;">Système de Production</p>
  </div>
  <div style="padding: 20px; background-color: #f8f9fa;">
    <h2 style="color: #0a3d62; margin-top: 0;">{{title}}</h2>
    <p><strong>Type:</strong> {{type}}</p>
    <p><strong>Priorité:</strong> {{priority}}</p>
    <p><strong>Date:</strong> {{timestamp}}</p>
    <div style="margin: 20px 0; padding: 15px; background-color: white; border-left: 4px solid #0a3d62;">
      <p style="margin: 0;">{{message}}</p>
    </div>
  </div>
  <div style="background-color: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
    <p style="margin: 0;">Cet email a été généré automatiquement par le système SOMIPEM.</p>
  </div>
</div>',
'SOMIPEM - Système de Production\n\n{{title}}\n\nType: {{type}}\nPriorité: {{priority}}\nDate: {{timestamp}}\n\nMessage:\n{{message}}\n\n---\nCet email a été généré automatiquement par le système SOMIPEM.',
'{"type": "Notification type", "title": "Notification title", "message": "Notification message", "priority": "Priority level", "timestamp": "Notification timestamp"}'),

('notification_detailed_fr', 'detailed', 'fr', '[SOMIPEM] {{type}}: {{title}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <div style="background-color: #0a3d62; color: white; padding: 20px; text-align: center;">
    <h1 style="margin: 0;">SOMIPEM</h1>
    <p style="margin: 5px 0 0 0;">Système de Production</p>
  </div>
  <div style="padding: 20px; background-color: #f8f9fa;">
    <h2 style="color: #0a3d62; margin-top: 0;">{{title}}</h2>
    <p><strong>Type:</strong> {{type}}</p>
    <p><strong>Priorité:</strong> {{priority}}</p>
    <p><strong>Date:</strong> {{timestamp}}</p>
    <div style="margin: 20px 0; padding: 15px; background-color: white; border-left: 4px solid #0a3d62;">
      <p style="margin: 0;">{{message}}</p>
    </div>
    <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border: 1px solid #dee2e6;">
      <h4>Informations détaillées:</h4>
      <p><strong>ID Notification:</strong> {{id}}</p>
      <p><strong>Système:</strong> SOMIPEM v1.0</p>
      <p><strong>Données:</strong> {{data}}</p>
    </div>
  </div>
  <div style="background-color: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
    <p style="margin: 0;">Cet email a été généré automatiquement par le système SOMIPEM.</p>
  </div>
</div>',
'SOMIPEM - Système de Production\n\n{{title}}\n\nType: {{type}}\nPriorité: {{priority}}\nDate: {{timestamp}}\n\nMessage:\n{{message}}\n\nInformations détaillées:\nID Notification: {{id}}\nSystème: SOMIPEM v1.0\nDonnées: {{data}}\n\n---\nCet email a été généré automatiquement par le système SOMIPEM.',
'{"type": "Notification type", "title": "Notification title", "message": "Notification message", "priority": "Priority level", "timestamp": "Notification timestamp", "id": "Notification ID", "data": "Additional data"}');

-- Initialize email statistics for current date
INSERT IGNORE INTO email_statistics (date, total_sent, total_failed, total_pending) 
VALUES (CURDATE(), 0, 0, 0);
