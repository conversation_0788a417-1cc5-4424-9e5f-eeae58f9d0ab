/**
 * SOMIPEM Brand Utility Classes
 * Global CSS utilities for consistent SOMIPEM branding
 */

/* Primary Brand Colors */
.somipem-primary {
  color: var(--somipem-primary-blue) !important;
}

.somipem-primary-bg {
  background-color: var(--somipem-primary-blue) !important;
}

.somipem-secondary {
  color: var(--somipem-secondary-blue) !important;
}

.somipem-secondary-bg {
  background-color: var(--somipem-secondary-blue) !important;
}

/* Text Colors */
.somipem-text-primary {
  color: var(--somipem-dark-gray) !important;
}

.somipem-text-secondary {
  color: var(--somipem-light-gray) !important;
}

/* Border Colors */
.somipem-border {
  border-color: var(--somipem-accent-border) !important;
}

.somipem-border-primary {
  border-color: var(--somipem-primary-blue) !important;
}

/* Status Colors */
.somipem-success {
  color: var(--somipem-success) !important;
}

.somipem-warning {
  color: var(--somipem-warning) !important;
}

.somipem-error {
  color: var(--somipem-error) !important;
}

.somipem-success-bg {
  background-color: var(--somipem-success) !important;
}

.somipem-warning-bg {
  background-color: var(--somipem-warning) !important;
}

.somipem-error-bg {
  background-color: var(--somipem-error) !important;
}

/* Interactive States */
.somipem-hover:hover {
  background-color: var(--somipem-hover-blue) !important;
  color: var(--somipem-primary-blue) !important;
}

.somipem-selected {
  background-color: var(--somipem-selected-bg) !important;
  color: var(--somipem-primary-blue) !important;
}

/* Buttons */
.somipem-btn-primary {
  background-color: var(--somipem-primary-blue) !important;
  border-color: var(--somipem-primary-blue) !important;
  color: white !important;
}

.somipem-btn-primary:hover {
  background-color: var(--somipem-secondary-blue) !important;
  border-color: var(--somipem-secondary-blue) !important;
}

.somipem-btn-secondary {
  background-color: transparent !important;
  border-color: var(--somipem-primary-blue) !important;
  color: var(--somipem-primary-blue) !important;
}

.somipem-btn-secondary:hover {
  background-color: var(--somipem-hover-blue) !important;
  border-color: var(--somipem-secondary-blue) !important;
  color: var(--somipem-secondary-blue) !important;
}

/* Cards and Surfaces */
.somipem-card {
  background-color: var(--somipem-white) !important;
  border: 1px solid var(--somipem-accent-border) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(30, 58, 138, 0.08) !important;
}

[data-theme="dark"] .somipem-card {
  background-color: #1F2937 !important;
  border-color: var(--somipem-accent-border) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Accent Elements */
.somipem-accent-border-left {
  border-left: 3px solid var(--somipem-primary-blue) !important;
}

.somipem-accent-border-bottom {
  border-bottom: 2px solid var(--somipem-primary-blue) !important;
}

/* Navigation and Menu Items */
.somipem-nav-item {
  transition: all 0.3s ease !important;
}

.somipem-nav-item:hover {
  background-color: var(--somipem-hover-blue) !important;
  color: var(--somipem-primary-blue) !important;
}

.somipem-nav-item-selected {
  background-color: var(--somipem-selected-bg) !important;
  color: var(--somipem-primary-blue) !important;
  border-right: 3px solid var(--somipem-primary-blue) !important;
}

/* Progress and Status Indicators */
.somipem-progress-bg {
  background-color: var(--somipem-primary-blue) !important;
}

.somipem-badge {
  background-color: var(--somipem-primary-blue) !important;
  color: white !important;
}

/* Form Elements */
.somipem-input:focus {
  border-color: var(--somipem-primary-blue) !important;
  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.2) !important;
}

/* Headers and Titles */
.somipem-header {
  color: var(--somipem-dark-gray) !important;
  font-weight: 600 !important;
}

.somipem-subheader {
  color: var(--somipem-light-gray) !important;
  font-weight: 500 !important;
}

/* Chart and Data Visualization */
.somipem-chart-container {
  background: var(--somipem-white) !important;
  border: 1px solid var(--somipem-accent-border) !important;
  border-radius: 8px !important;
}

[data-theme="dark"] .somipem-chart-container {
  background: #1F2937 !important;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .somipem-mobile-spacing {
    padding: 12px !important;
    margin: 8px 0 !important;
  }
}

/* Animation utilities */
.somipem-transition {
  transition: all 0.3s ease !important;
}

.somipem-hover-lift:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.15) !important;
}
