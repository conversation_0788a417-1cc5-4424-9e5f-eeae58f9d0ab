/**
 * Quick Test - Check MySQL result contains TRS field
 */

import mysql from 'mysql2/promise';

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'testingarea51',
 
};

const testQuery = async () => {
  console.log('🔍 Testing MySQL query directly...\n');

  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    const query = `
      SELECT 
        Machine_Name,
        AVG(
          CASE 
            WHEN OEE_Day LIKE '%\\%%' THEN
              CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
            WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
              CASE 
                WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                  CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                ELSE
                  CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
              END
            ELSE 0 
          END
        ) AS oee,
        AVG(
          CASE 
            WHEN OEE_Day LIKE '%\\%%' THEN
              CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
            WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
              CASE 
                WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                  CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                ELSE
                  CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
              END
            ELSE 0 
          END
        ) AS trs
      FROM machine_daily_table_mould 
      WHERE Machine_Name LIKE 'IPS%'
      GROUP BY Machine_Name 
      ORDER BY Machine_Name
      LIMIT 1
    `;

    const [rows] = await connection.execute(query);
    console.log('✅ Query successful!');
    console.log('📊 Result:', JSON.stringify(rows[0], null, 2));
    
    if (rows[0]) {
      console.log('\n🔍 Field Analysis:');
      console.log('  - Machine:', rows[0].Machine_Name);
      console.log('  - OEE value:', rows[0].oee);
      console.log('  - TRS value:', rows[0].trs);
      console.log('  - Values equal:', rows[0].oee === rows[0].trs);
    }

    await connection.end();
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

testQuery();
