import React from 'react';
import { Space } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

const PredictiveMaintenanceCard = ({ loading, filters }) => {
  return (
    <div style={{ 
      height: '300px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)',
      borderRadius: '12px'
    }}>
      <Space direction="vertical" align="center">
        <SettingOutlined style={{ fontSize: '48px', color: '#13c2c2' }} />
        <h3 style={{ color: '#13c2c2', margin: 0 }}>Predictive Maintenance AI</h3>
        <p style={{ color: '#8c8c8c', textAlign: 'center' }}>
          Machine failure prediction and preventive maintenance
        </p>
      </Space>
    </div>
  );
};

export default PredictiveMaintenanceCard;
