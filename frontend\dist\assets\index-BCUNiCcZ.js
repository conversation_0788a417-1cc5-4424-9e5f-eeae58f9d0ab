import{r as e,Q as se,U as ne,ap as ae,V as ie,aA as le,a1 as q,aF as ce,aH as de,b3 as ge,aZ as Q,q as T,b4 as ue,av as pe,b5 as me,P as ve}from"./index-y9W4UQPd.js";const V=e.createContext({}),fe=t=>{const{antCls:s,componentCls:n,iconCls:r,avatarBg:a,avatarColor:d,containerSize:C,containerSizeLG:g,containerSizeSM:b,textFontSize:y,textFontSizeLG:v,textFontSizeSM:w,borderRadius:k,borderRadiusLG:f,borderRadiusSM:j,lineWidth:S,lineType:x}=t,z=(o,h,O)=>({width:o,height:o,borderRadius:"50%",[`&${n}-square`]:{borderRadius:O},[`&${n}-icon`]:{fontSize:h,[`> ${r}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},ae(t)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:d,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:a,border:`${ie(S)} ${x} transparent`,"&-image":{background:"transparent"},[`${s}-image-img`]:{display:"block"}}),z(C,y,k)),{"&-lg":Object.assign({},z(g,v,f)),"&-sm":Object.assign({},z(b,w,j)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},Se=t=>{const{componentCls:s,groupBorderColor:n,groupOverlapping:r,groupSpace:a}=t;return{[`${s}-group`]:{display:"inline-flex",[s]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},[`${s}-group-popover`]:{[`${s} + ${s}`]:{marginInlineStart:a}}}},he=t=>{const{controlHeight:s,controlHeightLG:n,controlHeightSM:r,fontSize:a,fontSizeLG:d,fontSizeXL:C,fontSizeHeading3:g,marginXS:b,marginXXS:y,colorBorderBg:v}=t;return{containerSize:s,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((d+C)/2),textFontSizeLG:g,textFontSizeSM:a,groupSpace:y,groupOverlapping:-b,groupBorderColor:v}},U=se("Avatar",t=>{const{colorTextLightSolid:s,colorTextPlaceholder:n}=t,r=ne(t,{avatarBg:n,avatarColor:s});return[fe(r),Se(r)]},he);var Ce=function(t,s){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&s.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)s.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(n[r[a]]=t[r[a]]);return n};const Z=e.forwardRef((t,s)=>{const{prefixCls:n,shape:r,size:a,src:d,srcSet:C,icon:g,className:b,rootClassName:y,style:v,alt:w,draggable:k,children:f,crossOrigin:j,gap:S=4,onError:x}=t,z=Ce(t,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[o,h]=e.useState(1),[O,F]=e.useState(!1),[A,G]=e.useState(!0),I=e.useRef(null),E=e.useRef(null),P=le(s,I),{getPrefixCls:$,avatar:p}=e.useContext(q),u=e.useContext(V),N=()=>{if(!E.current||!I.current)return;const l=E.current.offsetWidth,i=I.current.offsetWidth;l!==0&&i!==0&&S*2<i&&h(i-S*2<l?(i-S*2)/l:1)};e.useEffect(()=>{F(!0)},[]),e.useEffect(()=>{G(!0),h(1)},[d]),e.useEffect(N,[S]);const B=()=>{(x==null?void 0:x())!==!1&&G(!1)},c=ce(l=>{var i,M;return(M=(i=a??(u==null?void 0:u.size))!==null&&i!==void 0?i:l)!==null&&M!==void 0?M:"default"}),H=Object.keys(typeof c=="object"?c||{}:{}).some(l=>["xs","sm","md","lg","xl","xxl"].includes(l)),L=de(H),D=e.useMemo(()=>{if(typeof c!="object")return{};const l=ge.find(M=>L[M]),i=c[l];return i?{width:i,height:i,fontSize:i&&(g||f)?i/2:18}:{}},[L,c]),m=$("avatar",n),_=Q(m),[J,K,Y]=U(m,_),ee=T({[`${m}-lg`]:c==="large",[`${m}-sm`]:c==="small"}),W=e.isValidElement(d),te=r||(u==null?void 0:u.shape)||"circle",re=T(m,ee,p==null?void 0:p.className,`${m}-${te}`,{[`${m}-image`]:W||d&&A,[`${m}-icon`]:!!g},Y,_,b,y,K),oe=typeof c=="number"?{width:c,height:c,fontSize:g?c/2:18}:{};let R;if(typeof d=="string"&&A)R=e.createElement("img",{src:d,draggable:k,srcSet:C,onError:B,alt:w,crossOrigin:j});else if(W)R=d;else if(g)R=g;else if(O||o!==1){const l=`scale(${o})`,i={msTransform:l,WebkitTransform:l,transform:l};R=e.createElement(ue,{onResize:N},e.createElement("span",{className:`${m}-string`,ref:E,style:Object.assign({},i)},f))}else R=e.createElement("span",{className:`${m}-string`,style:{opacity:0},ref:E},f);return J(e.createElement("span",Object.assign({},z,{style:Object.assign(Object.assign(Object.assign(Object.assign({},oe),D),p==null?void 0:p.style),v),className:re,ref:P}),R))}),X=t=>{const{size:s,shape:n}=e.useContext(V),r=e.useMemo(()=>({size:t.size||s,shape:t.shape||n}),[t.size,t.shape,s,n]);return e.createElement(V.Provider,{value:r},t.children)},be=t=>{var s,n,r,a;const{getPrefixCls:d,direction:C}=e.useContext(q),{prefixCls:g,className:b,rootClassName:y,style:v,maxCount:w,maxStyle:k,size:f,shape:j,maxPopoverPlacement:S,maxPopoverTrigger:x,children:z,max:o}=t,h=d("avatar",g),O=`${h}-group`,F=Q(h),[A,G,I]=U(h,F),E=T(O,{[`${O}-rtl`]:C==="rtl"},I,F,b,y,G),P=pe(z).map((u,N)=>me(u,{key:`avatar-key-${N}`})),$=(o==null?void 0:o.count)||w,p=P.length;if($&&$<p){const u=P.slice(0,$),N=P.slice($,p),B=(o==null?void 0:o.style)||k,c=((s=o==null?void 0:o.popover)===null||s===void 0?void 0:s.trigger)||x||"hover",H=((n=o==null?void 0:o.popover)===null||n===void 0?void 0:n.placement)||S||"top",L=Object.assign(Object.assign({content:N},o==null?void 0:o.popover),{classNames:{root:T(`${O}-popover`,(a=(r=o==null?void 0:o.popover)===null||r===void 0?void 0:r.classNames)===null||a===void 0?void 0:a.root)},placement:H,trigger:c});return u.push(e.createElement(ve,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},L),e.createElement(Z,{style:B},`+${p-$}`))),A(e.createElement(X,{shape:j,size:f},e.createElement("div",{className:E,style:v},u)))}return A(e.createElement(X,{shape:j,size:f},e.createElement("div",{className:E,style:v},P)))},ye=Z;ye.Group=be;export{ye as A};
