#!/usr/bin/env node

/**
 * Quick GraphQL Test for Machine Performance Fix
 */

const SERVER_URL = process.env.SERVER_URL || 'http://localhost:5000';

async function testMachinePerformance() {
  console.log('🔧 MACHINE PERFORMANCE TEST');
  console.log('===========================\n');

  try {
    const query = `
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          data {
            Machine_Name
            production
            rejects
            oee
            availability
            performance
            quality
          }
          dataSource
        }
      }
    `;

    console.log('📊 Testing Machine Performance with IPS model filter...');
    
    const response = await fetch(`${SERVER_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query,
        variables: {
          filters: {
            model: "IPS",
            dateRangeType: "day"
          }
        }
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return;
    }

    const data = result.data.enhancedGetMachinePerformance;
    console.log(`📦 Data source: ${data.dataSource}`);
    console.log(`🏭 Total machines: ${data.data.length}`);
    console.log('');

    console.log('📋 Machine Performance Results:');
    data.data.forEach((machine, index) => {
      console.log(`   ${index + 1}. ${machine.Machine_Name}`);
      console.log(`      Production: ${machine.production} pieces`);
      console.log(`      Rejects: ${machine.rejects} pieces`);
      console.log(`      OEE: ${machine.oee}%`);
      console.log(`      Availability: ${machine.availability}%`);
      console.log(`      Performance: ${machine.performance}%`);
      console.log(`      Quality: ${machine.quality}%`);
      console.log('');
    });

    // Check if we're getting unique machines (not duplicated by shifts)
    const uniqueMachines = [...new Set(data.data.map(m => m.Machine_Name))];
    console.log('🔍 VERIFICATION:');
    console.log(`   Total records: ${data.data.length}`);
    console.log(`   Unique machines: ${uniqueMachines.length}`);
    console.log(`   Status: ${data.data.length === uniqueMachines.length ? '✅ PASS - No duplicates' : '❌ FAIL - Duplicates found'}`);
    
    if (data.data.length !== uniqueMachines.length) {
      console.log('🚨 Machine duplicates detected:');
      const duplicates = data.data.filter((machine, index, array) => 
        array.findIndex(m => m.Machine_Name === machine.Machine_Name) !== index
      );
      duplicates.forEach(dup => console.log(`   • ${dup.Machine_Name}`));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testMachinePerformance();
