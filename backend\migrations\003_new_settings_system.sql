-- Migration: New Settings System Database Schema
-- Description: Complete settings system reconstruction with JSON storage
-- Date: 2025-08-03
-- Version: 003
-- Purpose: Implement comprehensive settings system with immediate effect guarantee

-- Drop existing settings tables for clean slate approach
DROP TABLE IF EXISTS user_notification_preferences;
DROP TABLE IF EXISTS user_report_subscriptions;
DROP TABLE IF EXISTS system_settings;

-- Drop existing user_settings table to recreate with new structure
DROP TABLE IF EXISTS user_settings;

-- Create new comprehensive user_settings table with JSON storage
-- This table stores all user preferences in a flexible JSON format
-- allowing for easy extension and immediate effect implementation
CREATE TABLE user_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  settings_json JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign key constraint to ensure data integrity
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  
  -- Unique constraint to ensure one settings record per user
  UNIQUE KEY unique_user_settings (user_id),
  
  -- Indexes for performance optimization
  INDEX idx_user_settings_user_id (user_id),
  INDEX idx_user_settings_updated (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert comprehensive default settings for all existing users
-- These defaults provide immediate functionality while allowing customization
INSERT INTO user_settings (user_id, settings_json)
SELECT 
  id as user_id,
  JSON_OBJECT(
    -- Theme & Display Settings - Controls visual appearance
    'theme', JSON_OBJECT(
      'darkMode', false,
      'compactMode', false,
      'animationsEnabled', true,
      'chartAnimations', true
    ),
    
    -- Table & Data Display Settings - Controls data presentation
    'tables', JSON_OBJECT(
      'defaultPageSize', 20,
      'pageSizeOptions', JSON_ARRAY(10, 20, 50, 100),
      'virtualizationThreshold', 100,
      'showQuickJumper', true
    ),
    
    -- Chart & Visualization Settings - Controls chart behavior
    'charts', JSON_OBJECT(
      'animationsEnabled', true,
      'defaultType', 'bar',
      'showLegend', true,
      'colorScheme', 'somipem',
      'performanceMode', false
    ),
    
    -- Refresh & Timing Settings - Controls update frequencies
    'refresh', JSON_OBJECT(
      'dashboardInterval', 300,
      'realtimeInterval', 60,
      'autoRefreshEnabled', true,
      'backgroundRefresh', true
    ),
    
    -- Comprehensive Notification Settings - Controls real-time notifications
    'notifications', JSON_OBJECT(
      'categories', JSON_OBJECT(
        'machine_alert', true,
        'production', true,
        'quality', true,
        'maintenance', true,
        'alert', true,
        'info', true,
        'updates', true
      ),
      'priorities', JSON_OBJECT(
        'critical', true,
        'high', true,
        'medium', true,
        'low', true
      ),
      'delivery', JSON_OBJECT(
        'sse', true,
        'email', false,
        'browser', true
      ),
      'behavior', JSON_OBJECT(
        'sound', true,
        'autoClose', false,
        'autoCloseDelay', 5000,
        'maxVisible', 5
      )
    ),
    
    -- Comprehensive Email Settings - Controls email notifications
    'email', JSON_OBJECT(
      'enabled', false,
      'frequency', 'immediate',
      'template', 'standard',
      'notifications', JSON_OBJECT(
        'categories', JSON_OBJECT(
          'machine_alert', true,
          'production', false,
          'quality', true,
          'maintenance', false,
          'alert', true,
          'info', false,
          'updates', false
        ),
        'priorities', JSON_OBJECT(
          'critical', true,
          'high', true,
          'medium', false,
          'low', false
        )
      ),
      'quietHours', JSON_OBJECT(
        'enabled', false,
        'start', '22:00',
        'end', '06:00',
        'timezone', 'Africa/Tunis'
      ),
      'batchSettings', JSON_OBJECT(
        'hourlyBatch', JSON_OBJECT(
          'enabled', false,
          'maxNotifications', 10
        ),
        'dailyDigest', JSON_OBJECT(
          'enabled', false,
          'time', '08:00',
          'includeCharts', true,
          'includeTables', false
        )
      )
    ),
    
    -- Comprehensive Report Settings - Controls report generation and delivery
    'reports', JSON_OBJECT(
      'generation', JSON_OBJECT(
        'autoGenerate', false,
        'format', 'pdf',
        'quality', 'standard',
        'includeCharts', true,
        'includeTables', true
      ),
      'schedules', JSON_OBJECT(
        'daily', JSON_OBJECT(
          'enabled', false,
          'time', '07:00',
          'machines', JSON_ARRAY()
        ),
        'weekly', JSON_OBJECT(
          'enabled', false,
          'day', 'monday',
          'time', '08:00',
          'machines', JSON_ARRAY()
        ),
        'monthly', JSON_OBJECT(
          'enabled', false,
          'day', 1,
          'time', '09:00',
          'machines', JSON_ARRAY()
        )
      ),
      'subscriptions', JSON_OBJECT(
        'enabled', false,
        'types', JSON_ARRAY('shift', 'daily', 'weekly'),
        'delivery', 'email'
      ),
      'content', JSON_OBJECT(
        'sections', JSON_OBJECT(
          'summary', true,
          'production', true,
          'quality', true,
          'maintenance', false,
          'charts', true,
          'tables', false
        ),
        'template', 'standard',
        'branding', true
      ),
      'delivery', JSON_OBJECT(
        'email', true,
        'download', false,
        'storage', true,
        'retention', 30
      )
    ),
    
    -- Performance Settings - Controls application performance
    'performance', JSON_OBJECT(
      'caching', JSON_OBJECT(
        'enabled', true,
        'duration', 300,
        'strategy', 'smart'
      ),
      'optimization', JSON_OBJECT(
        'lazyLoading', true,
        'virtualization', true,
        'compression', false
      )
    )
  ) as settings_json
FROM users
WHERE NOT EXISTS (
  SELECT 1 FROM user_settings WHERE user_settings.user_id = users.id
);

-- Create trigger to automatically create default settings for new users
-- This ensures every new user gets comprehensive default settings immediately
DELIMITER $$

CREATE TRIGGER create_default_user_settings
AFTER INSERT ON users
FOR EACH ROW
BEGIN
  INSERT INTO user_settings (user_id, settings_json)
  VALUES (
    NEW.id,
    JSON_OBJECT(
      'theme', JSON_OBJECT(
        'darkMode', false,
        'compactMode', false,
        'animationsEnabled', true,
        'chartAnimations', true
      ),
      'tables', JSON_OBJECT(
        'defaultPageSize', 20,
        'pageSizeOptions', JSON_ARRAY(10, 20, 50, 100),
        'virtualizationThreshold', 100,
        'showQuickJumper', true
      ),
      'charts', JSON_OBJECT(
        'animationsEnabled', true,
        'defaultType', 'bar',
        'showLegend', true,
        'colorScheme', 'somipem',
        'performanceMode', false
      ),
      'refresh', JSON_OBJECT(
        'dashboardInterval', 300,
        'realtimeInterval', 60,
        'autoRefreshEnabled', true,
        'backgroundRefresh', true
      ),
      'notifications', JSON_OBJECT(
        'categories', JSON_OBJECT(
          'machine_alert', true,
          'production', true,
          'quality', true,
          'maintenance', true,
          'alert', true,
          'info', true,
          'updates', true
        ),
        'priorities', JSON_OBJECT(
          'critical', true,
          'high', true,
          'medium', true,
          'low', true
        ),
        'delivery', JSON_OBJECT(
          'sse', true,
          'email', false,
          'browser', true
        ),
        'behavior', JSON_OBJECT(
          'sound', true,
          'autoClose', false,
          'autoCloseDelay', 5000,
          'maxVisible', 5
        )
      ),
      'email', JSON_OBJECT(
        'enabled', false,
        'frequency', 'immediate',
        'template', 'standard',
        'notifications', JSON_OBJECT(
          'categories', JSON_OBJECT(
            'machine_alert', true,
            'production', false,
            'quality', true,
            'maintenance', false,
            'alert', true,
            'info', false,
            'updates', false
          ),
          'priorities', JSON_OBJECT(
            'critical', true,
            'high', true,
            'medium', false,
            'low', false
          )
        ),
        'quietHours', JSON_OBJECT(
          'enabled', false,
          'start', '22:00',
          'end', '06:00',
          'timezone', 'Africa/Tunis'
        ),
        'batchSettings', JSON_OBJECT(
          'hourlyBatch', JSON_OBJECT(
            'enabled', false,
            'maxNotifications', 10
          ),
          'dailyDigest', JSON_OBJECT(
            'enabled', false,
            'time', '08:00',
            'includeCharts', true,
            'includeTables', false
          )
        )
      ),
      'reports', JSON_OBJECT(
        'generation', JSON_OBJECT(
          'autoGenerate', false,
          'format', 'pdf',
          'quality', 'standard',
          'includeCharts', true,
          'includeTables', true
        ),
        'schedules', JSON_OBJECT(
          'daily', JSON_OBJECT(
            'enabled', false,
            'time', '07:00',
            'machines', JSON_ARRAY()
          ),
          'weekly', JSON_OBJECT(
            'enabled', false,
            'day', 'monday',
            'time', '08:00',
            'machines', JSON_ARRAY()
          ),
          'monthly', JSON_OBJECT(
            'enabled', false,
            'day', 1,
            'time', '09:00',
            'machines', JSON_ARRAY()
          )
        ),
        'subscriptions', JSON_OBJECT(
          'enabled', false,
          'types', JSON_ARRAY('shift', 'daily', 'weekly'),
          'delivery', 'email'
        ),
        'content', JSON_OBJECT(
          'sections', JSON_OBJECT(
            'summary', true,
            'production', true,
            'quality', true,
            'maintenance', false,
            'charts', true,
            'tables', false
          ),
          'template', 'standard',
          'branding', true
        ),
        'delivery', JSON_OBJECT(
          'email', true,
          'download', false,
          'storage', true,
          'retention', 30
        )
      ),
      'performance', JSON_OBJECT(
        'caching', JSON_OBJECT(
          'enabled', true,
          'duration', 300,
          'strategy', 'smart'
        ),
        'optimization', JSON_OBJECT(
          'lazyLoading', true,
          'virtualization', true,
          'compression', false
        )
      )
    )
  );
END$$

DELIMITER ;
