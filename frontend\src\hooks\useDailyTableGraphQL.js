/**
 * Custom hook for Daily Table GraphQL queries
 * Provides access to all daily production data via GraphQL endpoint
 */
import React from "react" ;
import { useState, useCallback } from 'react';

const GRAPHQL_ENDPOINT = '/api/graphql';

const useDailyTableGraphQL = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Generic GraphQL query executor
  const executeQuery = useCallback(async (query, variables = {}) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          query,
          variables 
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors.map(e => e.message).join(', '));
      }

      setLoading(false);
      return result.data;
    } catch (err) {
      setError(err.message);
      setLoading(false);
      throw err;
    }
  }, []);

  // Get all daily production data - FIXED: Now accepts filters
  const getAllDailyProduction = useCallback(async (filters = {}) => {
    const query = `
      query GetAllDailyProduction($filters: FilterInput) {
        getAllDailyProduction(filters: $filters) {
          Machine_Name
          Date_Insert_Day
          Run_Hours_Day
          Down_Hours_Day
          Good_QTY_Day
          Rejects_QTY_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
          OEE_Day
          Shift
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get production chart data with filters
  const getProductionChart = useCallback(async (filters = {}) => {
    const query = `
      query GetProductionChart($filters: EnhancedFilterInput) {
        enhancedGetProductionChart(filters: $filters) {
          data {
            Date_Insert_Day
            Total_Good_Qty_Day
            Total_Rejects_Qty_Day
            OEE_Day
            Speed_Day
            Availability_Rate_Day
            Performance_Rate_Day
            Quality_Rate_Day
          }
          dataSource
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get production sidecards with data source indicator
  const getProductionSidecards = useCallback(async (filters = {}) => {
    const query = `
      query GetProductionSidecards($filters: EnhancedFilterInput) {
        enhancedGetProductionSidecards(filters: $filters) {
          goodqty
          rejetqty
          dataSource
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get unique dates
  const getUniqueDates = useCallback(async () => {
    const query = `
      query {
        getUniqueDates
      }
    `;
    return executeQuery(query);
  }, [executeQuery]);

  // Get machine models
  const getMachineModels = useCallback(async () => {
    const query = `
      query {
        getMachineModels {
          model
        }
      }
    `;
    return executeQuery(query);
  }, [executeQuery]);

  // Get machine names
  const getMachineNames = useCallback(async () => {
    const query = `
      query {
        getMachineNames {
          Machine_Name
        }
      }
    `;
    return executeQuery(query);
  }, [executeQuery]);

  // Get machine performance  // Get machine performance
  const getMachinePerformance = useCallback(async (filters = {}) => {
    const query = `
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          data {
            Machine_Name
            Shift
            production
            rejects
            downtime
            availability
            performance
            oee
            quality
            disponibilite
          }
          dataSource
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get shift performance (for team comparison charts)
  const getShiftPerformance = useCallback(async (filters = {}) => {
    const query = `
      query GetShiftPerformance($filters: EnhancedFilterInput) {
        enhancedGetShiftPerformance(filters: $filters) {
          data {
            Shift
            production
            rejects
            downtime
            availability
            performance
            oee
            quality
            disponibilite
          }
          dataSource
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get availability trends
  const getAvailabilityTrend = useCallback(async (filters = {}) => {
    const query = `
      query GetAvailabilityTrend($filters: FilterInput) {
        getAvailabilityTrend(filters: $filters) {
          date
          machine
          disponibilite
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Get performance metrics
  const getPerformanceMetrics = useCallback(async (filters = {}) => {
    const query = `
      query GetPerformanceMetrics($filters: FilterInput) {
        getPerformanceMetrics(filters: $filters) {
          machine
          model
          disponibilite
          stops
          mttr
          mtbf
        }
      }
    `;
    return executeQuery(query, { filters });
  }, [executeQuery]);

  // Composite function to get dashboard data with data source tracking
  const getDashboardData = useCallback(async (filters = {}) => {
    try {
      // Execute queries directly using executeQuery to avoid dependency issues
      const productionChartQuery = `
        query GetProductionChart($filters: EnhancedFilterInput) {
          enhancedGetProductionChart(filters: $filters) {
            data {
              Date_Insert_Day
              Total_Good_Qty_Day
              Total_Rejects_Qty_Day
              OEE_Day
              Speed_Day
              Availability_Rate_Day
              Performance_Rate_Day
              Quality_Rate_Day
            }
            dataSource
          }
        }
      `;

      const sidecardsQuery = `
        query GetProductionSidecards($filters: EnhancedFilterInput) {
          enhancedGetProductionSidecards(filters: $filters) {
            goodqty
            rejetqty
            dataSource
          }
        }
      `;

      const machinePerformanceQuery = `
        query GetMachinePerformance($filters: EnhancedFilterInput) {
          enhancedGetMachinePerformance(filters: $filters) {
            data {
              Machine_Name
              Shift
              production
              rejects
              downtime
              availability
              performance
              oee
              quality
              disponibilite
            }
            dataSource
          }
        }
      `;

      const shiftPerformanceQuery = `
        query GetShiftPerformance($filters: EnhancedFilterInput) {
          enhancedGetShiftPerformance(filters: $filters) {
            data {
              Shift
              production
              rejects
              downtime
              availability
              performance
              oee
              quality
              disponibilite
            }
            dataSource
          }
        }
      `;

      const availabilityTrendQuery = `
        query GetAvailabilityTrend($filters: FilterInput) {
          getAvailabilityTrend(filters: $filters) {
            date
            machine
            disponibilite
          }
        }
      `;

      const [
        productionChart,
        sidecards,
        machinePerformance,
        shiftPerformance,
        availabilityTrend
      ] = await Promise.all([
        executeQuery(productionChartQuery, { filters }),
        executeQuery(sidecardsQuery, { filters }),
        executeQuery(machinePerformanceQuery, { filters }),
        executeQuery(shiftPerformanceQuery, { filters }),
        executeQuery(availabilityTrendQuery, { filters })
      ]);

      return {
        productionChart: {
          data: productionChart?.enhancedGetProductionChart?.data || [],
          dataSource: productionChart?.enhancedGetProductionChart?.dataSource || 'unknown'
        },
        sidecards: {
          goodqty: sidecards?.enhancedGetProductionSidecards?.goodqty || 0,
          rejetqty: sidecards?.enhancedGetProductionSidecards?.rejetqty || 0,
          dataSource: sidecards?.enhancedGetProductionSidecards?.dataSource || 'unknown'
        },
        machinePerformance: {
          data: machinePerformance?.enhancedGetMachinePerformance?.data || [],
          dataSource: machinePerformance?.enhancedGetMachinePerformance?.dataSource || 'unknown'
        },
        shiftPerformance: {
          data: shiftPerformance?.enhancedGetShiftPerformance?.data || [],
          dataSource: shiftPerformance?.enhancedGetShiftPerformance?.dataSource || 'unknown'
        },
        availabilityTrend: availabilityTrend?.getAvailabilityTrend || []
      };
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      throw err;
    }
  }, [executeQuery]);

  return {
    // State
    loading,
    error,
    
    // Individual query functions
    getAllDailyProduction,
    getProductionChart,
    getProductionSidecards,
    getUniqueDates,
    getMachineModels,
    getMachineNames,
    getMachinePerformance,
    getShiftPerformance,
    getAvailabilityTrend,
    getPerformanceMetrics,
    
    // Composite functions
    getDashboardData,
    
    // Utility
    executeQuery
  };
};

export default useDailyTableGraphQL;
