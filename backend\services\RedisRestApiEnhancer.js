/**
 * Redis-Enhanced REST API Service for Manufacturing Intelligence Platform
 * High-performance caching layer for REST API endpoints
 * 
 * Performance Targets:
 * - 80% database query load reduction for REST APIs
 * - Sub-100ms cache hit response times
 * - Consistent caching strategy between GraphQL and REST
 */

import redisService from './RedisService.js';
import crypto from 'crypto';

class RedisRestApiEnhancer {
  constructor() {
    this.initialized = false;
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      avgCacheResponseTime: 0,
      avgDbResponseTime: 0,
      endpointMetrics: new Map()
    };

    // TTL configurations for different endpoint types
    this.TTL_CONFIG = {
      realtime: 45,      // 45 seconds for real-time data
      short: 120,        // 2 minutes for frequently changing data
      medium: 300,       // 5 minutes for dashboard data
      long: 900,         // 15 minutes for aggregated data
      veryLong: 1800     // 30 minutes for static data
    };

    // Endpoint categorization for TTL assignment
    this.ENDPOINT_CATEGORIES = {
      realtime: ['/api/RealTimeTable'],
      short: ['/api/MachineCard', '/api/dailyStats'],
      medium: [
        '/api/DailyTableMould',
        '/api/chart-production',
        '/api/sidecards',
        '/api/sidecards-prod',
        '/api/sidecards-prod-rejet'
      ],
      long: ['/api/top-5-stops'],
      veryLong: ['/api/unique-dates', '/api/unique-dates-production']
    };
  }

  /**
   * Initialize the REST API enhancer
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    try {
      // Ensure Redis service is initialized
      const redisInitialized = await redisService.initialize();
      if (!redisInitialized) {
        console.warn('⚠️ Redis not available - REST API enhancer running in fallback mode');
        return false;
      }

      this.initialized = true;
      console.log('✅ Redis REST API enhancer initialized');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize Redis REST API enhancer:', error);
      return false;
    }
  }

  /**
   * Generate cache key for REST API endpoint
   */
  generateCacheKey(endpoint, query = {}, body = {}) {
    const keyData = {
      endpoint,
      query: this.sanitizeQueryParams(query),
      body: body || {}
    };

    const keyString = JSON.stringify(keyData);
    const hash = crypto.createHash('md5').update(keyString).digest('hex');
    
    return `rest:${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}:${hash}`;
  }

  /**
   * Sanitize query parameters for consistent caching
   */
  sanitizeQueryParams(query) {
    const sanitized = {};
    
    // Sort keys for consistent hashing
    const sortedKeys = Object.keys(query).sort();
    
    for (const key of sortedKeys) {
      const value = query[key];
      
      // Skip undefined/null values
      if (value !== undefined && value !== null) {
        sanitized[key] = String(value).trim();
      }
    }
    
    return sanitized;
  }

  /**
   * Determine TTL for endpoint
   */
  getTTLForEndpoint(endpoint) {
    for (const [category, endpoints] of Object.entries(this.ENDPOINT_CATEGORIES)) {
      if (endpoints.some(pattern => endpoint.includes(pattern.replace('/api', '')))) {
        return this.TTL_CONFIG[category];
      }
    }
    
    // Default to medium TTL
    return this.TTL_CONFIG.medium;
  }

  /**
   * Create cached middleware for REST API endpoints
   */
  createCachedEndpoint(endpoint, options = {}) {
    const {
      ttl = this.getTTLForEndpoint(endpoint),
      keyPrefix = 'api',
      skipCache = false,
      cacheCondition = null
    } = options;

    return (originalHandler) => {
      return async (req, res, next) => {
        const startTime = Date.now();
        const endpointName = `${req.method} ${endpoint}`;

        try {
          // Initialize if needed
          if (!this.initialized) {
            await this.initialize();
          }

          // Skip cache if disabled or condition not met
          if (skipCache || process.env.DISABLE_CACHE === 'true') {
            console.log(`🔄 Cache SKIP for ${endpointName} (disabled)`);
            return await originalHandler(req, res, next);
          }

          // Check cache condition
          if (cacheCondition && !cacheCondition(req)) {
            console.log(`🔄 Cache SKIP for ${endpointName} (condition not met)`);
            return await originalHandler(req, res, next);
          }

          // Generate cache key
          const cacheKey = this.generateCacheKey(endpoint, req.query, req.body);

          // Try to get from cache
          const cached = await redisService.getCache(cacheKey);

          if (cached) {
            const responseTime = Date.now() - startTime;
            this.updateMetrics(endpointName, 'hit', responseTime);

            console.log(`📦 Cache HIT for ${endpointName} (${responseTime}ms)`);
            
            // Send cached response
            return res.json(cached);
          }

          // Cache miss - execute original handler
          console.log(`🔄 Cache MISS for ${endpointName} - executing query`);
          const dbStartTime = Date.now();

          // Capture response data
          const originalJson = res.json;
          let responseData = null;

          res.json = function(data) {
            responseData = data;
            return originalJson.call(this, data);
          };

          // Execute original handler
          await originalHandler(req, res, next);

          const dbResponseTime = Date.now() - dbStartTime;
          const totalResponseTime = Date.now() - startTime;

          // Cache the response if successful
          if (responseData && res.statusCode >= 200 && res.statusCode < 300) {
            await redisService.setCache(cacheKey, responseData, ttl);
            console.log(`💾 Cached ${endpointName} result (DB: ${dbResponseTime}ms, Total: ${totalResponseTime}ms)`);
          }

          this.updateMetrics(endpointName, 'miss', totalResponseTime, dbResponseTime);

        } catch (error) {
          console.error(`❌ Cache error for ${endpointName}:`, error);
          // Fallback to original handler
          return await originalHandler(req, res, next);
        }
      };
    };
  }

  /**
   * Create cached middleware for heavy aggregation endpoints
   */
  createHeavyAggregationEndpoint(endpoint, options = {}) {
    const {
      ttl = this.TTL_CONFIG.long,
      keyPrefix = 'aggregation',
      performanceThreshold = 1000
    } = options;

    return this.createCachedEndpoint(endpoint, {
      ...options,
      ttl,
      keyPrefix,
      cacheCondition: (req) => {
        // Cache aggregation queries more aggressively
        return true;
      }
    });
  }

  /**
   * Create cached middleware for dashboard endpoints
   */
  createDashboardEndpoint(endpoint, options = {}) {
    const {
      ttl = this.TTL_CONFIG.medium,
      keyPrefix = 'dashboard'
    } = options;

    return this.createCachedEndpoint(endpoint, {
      ...options,
      ttl,
      keyPrefix
    });
  }

  /**
   * Update performance metrics
   */
  updateMetrics(endpoint, type, responseTime, dbResponseTime = 0) {
    this.metrics.totalRequests++;
    
    if (type === 'hit') {
      this.metrics.cacheHits++;
      this.metrics.avgCacheResponseTime = 
        (this.metrics.avgCacheResponseTime * (this.metrics.cacheHits - 1) + responseTime) / this.metrics.cacheHits;
    } else {
      this.metrics.cacheMisses++;
      this.metrics.avgDbResponseTime = 
        (this.metrics.avgDbResponseTime * (this.metrics.cacheMisses - 1) + dbResponseTime) / this.metrics.cacheMisses;
    }

    // Track per-endpoint metrics
    if (!this.metrics.endpointMetrics.has(endpoint)) {
      this.metrics.endpointMetrics.set(endpoint, {
        hits: 0,
        misses: 0,
        avgResponseTime: 0,
        totalRequests: 0
      });
    }

    const endpointMetric = this.metrics.endpointMetrics.get(endpoint);
    endpointMetric.totalRequests++;
    endpointMetric.avgResponseTime = 
      (endpointMetric.avgResponseTime * (endpointMetric.totalRequests - 1) + responseTime) / endpointMetric.totalRequests;

    if (type === 'hit') {
      endpointMetric.hits++;
    } else {
      endpointMetric.misses++;
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const hitRate = this.metrics.totalRequests > 0 
      ? ((this.metrics.cacheHits / this.metrics.totalRequests) * 100).toFixed(2)
      : 0;

    const cacheEfficiency = this.metrics.avgDbResponseTime > 0 && this.metrics.avgCacheResponseTime > 0
      ? (this.metrics.avgDbResponseTime / this.metrics.avgCacheResponseTime).toFixed(2)
      : 0;

    return {
      ...this.metrics,
      hitRate: parseFloat(hitRate),
      cacheEfficiency: parseFloat(cacheEfficiency),
      endpointMetrics: Object.fromEntries(this.metrics.endpointMetrics)
    };
  }

  /**
   * Invalidate cache for specific endpoint pattern
   */
  async invalidateEndpointCache(endpointPattern) {
    try {
      const pattern = `rest:${endpointPattern.replace(/[^a-zA-Z0-9]/g, '_')}:*`;
      const count = await redisService.invalidateCache(pattern);
      console.log(`🗑️ Invalidated ${count} cache entries for ${endpointPattern}`);
      return count;
    } catch (error) {
      console.error('❌ Cache invalidation failed:', error);
      return 0;
    }
  }

  /**
   * Warm up cache for common endpoints
   */
  async warmUpCache(endpoints = []) {
    console.log('🔥 Starting REST API cache warm-up...');
    
    const commonEndpoints = endpoints.length > 0 ? endpoints : [
      '/api/DailyTableMould',
      '/api/chart-production',
      '/api/sidecards',
      '/api/sidecards-prod'
    ];

    // This would typically make requests to these endpoints
    // For now, we'll just log the warm-up intention
    console.log(`🔥 Would warm up ${commonEndpoints.length} endpoints`);
    console.log('🔥 REST API cache warm-up completed');
  }
}

// Create singleton instance
const redisRestApiEnhancer = new RedisRestApiEnhancer();

export default redisRestApiEnhancer;
