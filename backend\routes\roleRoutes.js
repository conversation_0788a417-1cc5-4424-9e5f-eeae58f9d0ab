/**
 * Role management routes
 */
import express from 'express';
import auth from '../middleware/auth.js';
import { checkPermission } from '../middleware/permission.js';
import { corsOptions } from '../middleware/cors.js';
import cors from 'cors';
import { executeQuery } from '../utils/dbUtils.js';
import { sendSuccess, sendError, asyncHandler } from '../utils/responseUtils.js';

const router = express.Router();

/**
 * @route   GET api/roles
 * @desc    Get all roles
 * @access  Private (with permission)
 */
router.get('/', cors(corsOptions), auth, checkPermission(['manage_roles', 'view_roles']), asyncHandler(async (_, res) => {
  const query = 'SELECT * FROM roles ORDER BY name';
  const { success, data, error } = await executeQuery(query);

  if (!success) {
    return sendError(res, 'Server error', 500, error);
  }

  return sendSuccess(res, data);
}));

/**
 * @route   GET api/roles/:id
 * @desc    Get role by ID
 * @access  Private (with permission)
 */
router.get('/:id', cors(corsOptions), auth, checkPermission('manage_roles'), asyncHandler(async (req, res) => {
  const query = 'SELECT * FROM roles WHERE id = ?';
  const { success, data, error } = await executeQuery(query, [req.params.id]);

  if (!success) {
    return sendError(res, 'Server error', 500, error);
  }

  if (data.length === 0) {
    return sendError(res, 'Role not found', 404);
  }

  return sendSuccess(res, data[0]);
}));

/**
 * @route   POST api/roles
 * @desc    Create a role
 * @access  Private (with permission)
 */
router.post('/', cors(corsOptions), auth, checkPermission('manage_roles'), asyncHandler(async (req, res) => {
  const { name, description, permissions } = req.body;

  if (!name) {
    return sendError(res, 'Role name is required', 400);
  }

  // Convert permissions array to JSON string
  const permissionsJson = JSON.stringify(permissions || []);

  // Insert new role
  const insertQuery = 'INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)';
  const insertResult = await executeQuery(insertQuery, [name, description || null, permissionsJson]);

  if (!insertResult.success) {
    return sendError(res, 'Server error', 500, insertResult.error);
  }

  // Get the newly created role
  const selectQuery = 'SELECT * FROM roles WHERE id = ?';
  const { success, data, error } = await executeQuery(selectQuery, [insertResult.data.insertId], true);

  if (!success) {
    return sendError(res, 'Server error', 500, error);
  }

  // Parse permissions JSON for response
  if (data.permissions) {
    try {
      data.permissions = JSON.parse(data.permissions);
    } catch (e) {
      data.permissions = [];
    }
  }

  return sendSuccess(res, data, 'Role created successfully', 201);
}));

/**
 * @route   PUT api/roles/:id
 * @desc    Update a role
 * @access  Private (with permission)
 */
router.put('/:id', cors(corsOptions), auth, checkPermission('manage_roles'), asyncHandler(async (req, res) => {
  const { name, description, permissions } = req.body;

  if (!name) {
    return sendError(res, 'Role name is required', 400);
  }

  // Check if trying to modify admin role
  if (req.params.id === '1' && name !== 'admin') {
    return sendError(res, 'Cannot change admin role name', 400);
  }

  // Convert permissions array to JSON string
  const permissionsJson = JSON.stringify(permissions || []);

  // Update role
  const updateQuery = 'UPDATE roles SET name = ?, description = ?, permissions = ? WHERE id = ?';
  const updateResult = await executeQuery(updateQuery, [name, description || null, permissionsJson, req.params.id]);

  if (!updateResult.success) {
    return sendError(res, 'Server error', 500, updateResult.error);
  }

  if (updateResult.data.affectedRows === 0) {
    return sendError(res, 'Role not found', 404);
  }

  // Get updated role
  const selectQuery = 'SELECT * FROM roles WHERE id = ?';
  const { success, data, error } = await executeQuery(selectQuery, [req.params.id], true);

  if (!success) {
    return sendError(res, 'Server error', 500, error);
  }

  // Parse permissions JSON for response
  if (data.permissions) {
    try {
      data.permissions = JSON.parse(data.permissions);
    } catch (e) {
      data.permissions = [];
    }
  }

  return sendSuccess(res, data, 'Role updated successfully');
}));

/**
 * @route   DELETE api/roles/:id
 * @desc    Delete a role
 * @access  Private (with permission)
 */
router.delete('/:id', cors(corsOptions), auth, checkPermission('manage_roles'), asyncHandler(async (req, res) => {
  // Prevent deletion of admin role
  const roleCheckQuery = 'SELECT name FROM roles WHERE id = ?';
  const roleCheckResult = await executeQuery(roleCheckQuery, [req.params.id]);

  if (!roleCheckResult.success) {
    return sendError(res, 'Server error', 500, roleCheckResult.error);
  }

  if (roleCheckResult.data.length === 0) {
    return sendError(res, 'Role not found', 404);
  }

  if (roleCheckResult.data[0].name === 'admin') {
    return sendError(res, 'Cannot delete admin role', 400);
  }

  // Check if role is in use
  const usersCheckQuery = 'SELECT COUNT(*) as count FROM users WHERE role_id = ?';
  const usersCheckResult = await executeQuery(usersCheckQuery, [req.params.id]);

  if (!usersCheckResult.success) {
    return sendError(res, 'Server error', 500, usersCheckResult.error);
  }

  if (usersCheckResult.data[0].count > 0) {
    return sendError(res, 'Cannot delete role that has users assigned to it', 400);
  }

  // Delete role
  const deleteQuery = 'DELETE FROM roles WHERE id = ?';
  const deleteResult = await executeQuery(deleteQuery, [req.params.id]);

  if (!deleteResult.success) {
    return sendError(res, 'Server error', 500, deleteResult.error);
  }

  if (deleteResult.data.affectedRows === 0) {
    return sendError(res, 'Role not found', 404);
  }

  return sendSuccess(res, null, 'Role deleted successfully');
}));

/**
 * @route   GET api/permissions
 * @desc    Get all permissions
 * @access  Private (with permission)
 */
router.get('/permissions/all', cors(corsOptions), auth, checkPermission('manage_roles'), asyncHandler(async (_, res) => {
  const query = 'SELECT * FROM permissions ORDER BY name';
  const { success, data, error } = await executeQuery(query);

  if (!success) {
    return sendError(res, 'Server error', 500, error);
  }

  return sendSuccess(res, data);
}));

export default router;