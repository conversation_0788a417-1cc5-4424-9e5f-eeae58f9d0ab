/**
 * In-Memory Redis Fallback System
 * 
 * Provides a complete fallback implementation when Redis is unavailable.
 * Maintains core functionality using in-memory storage with graceful degradation.
 */

import { EventEmitter } from 'events';

class InMemoryRedisFallback extends EventEmitter {
  constructor() {
    super();
    this.storage = new Map();
    this.pubSubChannels = new Map();
    this.subscribers = new Map();
    this.keyExpiration = new Map();
    this.isConnected = true; // Always "connected" for fallback
    this.metrics = {
      operations: 0,
      hits: 0,
      misses: 0,
      sets: 0,
      gets: 0,
      publishes: 0,
      subscriptions: 0
    };

    console.log('🔄 Redis Fallback: In-memory cache system initialized');
    this.startExpirationCleanup();
  }

  /**
   * Start periodic cleanup of expired keys
   */
  startExpirationCleanup() {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      for (const [key, expiry] of this.keyExpiration.entries()) {
        if (expiry <= now) {
          this.storage.delete(key);
          this.keyExpiration.delete(key);
        }
      }
    }, 10000); // Clean up every 10 seconds
  }

  /**
   * Initialize the fallback system (Redis compatibility)
   */
  async initialize() {
    // Already initialized in constructor
    console.log('✅ Redis Fallback: System ready');
    return true;
  }

  /**
   * Redis-compatible connection simulation
   */
  async connect() {
    console.log('🔄 Redis Fallback: Connection simulated (in-memory mode)');
    this.emit('connect');
    this.emit('ready');
    return Promise.resolve();
  }

  /**
   * Redis-compatible ping
   */
  async ping() {
    this.metrics.operations++;
    return 'PONG';
  }

  /**
   * Set key-value with optional TTL
   */
  async set(key, value) {
    this.metrics.operations++;
    this.metrics.sets++;
    this.storage.set(key, value);
    console.log(`📦 Redis Fallback: SET ${key}`);
    return 'OK';
  }

  /**
   * Set key-value with expiration time in seconds
   */
  async setex(key, seconds, value) {
    this.metrics.operations++;
    this.metrics.sets++;
    this.storage.set(key, value);
    this.keyExpiration.set(key, Date.now() + (seconds * 1000));
    console.log(`📦 Redis Fallback: SETEX ${key} (TTL: ${seconds}s)`);
    return 'OK';
  }

  /**
   * Get value by key
   */
  async get(key) {
    this.metrics.operations++;
    this.metrics.gets++;

    // Check if key is expired
    const expiry = this.keyExpiration.get(key);
    if (expiry && expiry <= Date.now()) {
      this.storage.delete(key);
      this.keyExpiration.delete(key);
      this.metrics.misses++;
      console.log(`📭 Redis Fallback: GET ${key} (expired)`);
      return null;
    }

    const value = this.storage.get(key);
    if (value !== undefined) {
      this.metrics.hits++;
      console.log(`📦 Redis Fallback: GET ${key} (hit)`);
      return value;
    } else {
      this.metrics.misses++;
      console.log(`📭 Redis Fallback: GET ${key} (miss)`);
      return null;
    }
  }

  /**
   * Delete keys
   */
  async del(...keys) {
    this.metrics.operations++;
    let deleted = 0;
    for (const key of keys) {
      if (this.storage.delete(key)) {
        deleted++;
        this.keyExpiration.delete(key);
      }
    }
    console.log(`🗑️ Redis Fallback: DEL ${keys.join(', ')} (${deleted} deleted)`);
    return deleted;
  }

  /**
   * Get keys matching pattern
   */
  async keys(pattern) {
    this.metrics.operations++;
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const matchingKeys = [];
    
    for (const key of this.storage.keys()) {
      if (regex.test(key)) {
        matchingKeys.push(key);
      }
    }
    
    console.log(`🔍 Redis Fallback: KEYS ${pattern} (${matchingKeys.length} found)`);
    return matchingKeys;
  }

  /**
   * Get database size
   */
  async dbsize() {
    this.metrics.operations++;
    return this.storage.size;
  }

  /**
   * Publish message to channel
   */
  async publish(channel, message) {
    this.metrics.operations++;
    this.metrics.publishes++;

    const subscribers = this.subscribers.get(channel) || [];
    console.log(`📡 Redis Fallback: PUBLISH ${channel} to ${subscribers.length} subscribers`);

    // Emit to all subscribers
    for (const subscriber of subscribers) {
      try {
        subscriber.emit('message', channel, message);
      } catch (error) {
        console.error(`❌ Redis Fallback: Error publishing to subscriber:`, error);
      }
    }

    return subscribers.length;
  }

  /**
   * Subscribe to channels
   */
  async subscribe(...channels) {
    this.metrics.operations++;
    this.metrics.subscriptions++;

    for (const channel of channels) {
      if (!this.subscribers.has(channel)) {
        this.subscribers.set(channel, []);
      }
      this.subscribers.get(channel).push(this);
    }

    console.log(`📡 Redis Fallback: SUBSCRIBE ${channels.join(', ')}`);
    this.emit('subscribe', channels[0], this.subscribers.get(channels[0]).length);
    return 'OK';
  }

  /**
   * Unsubscribe from channels
   */
  async unsubscribe(...channels) {
    this.metrics.operations++;

    for (const channel of channels) {
      const subscribers = this.subscribers.get(channel);
      if (subscribers) {
        const index = subscribers.indexOf(this);
        if (index > -1) {
          subscribers.splice(index, 1);
        }
        if (subscribers.length === 0) {
          this.subscribers.delete(channel);
        }
      }
    }

    console.log(`📡 Redis Fallback: UNSUBSCRIBE ${channels.join(', ')}`);
    return 'OK';
  }

  /**
   * Get server info (simplified)
   */
  async info(section) {
    this.metrics.operations++;
    
    if (section === 'memory') {
      return `used_memory:${this.storage.size * 100}
used_memory_human:${(this.storage.size * 100 / 1024).toFixed(2)}K
used_memory_peak:${this.storage.size * 100}
mem_fragmentation_ratio:1.00`;
    }
    
    return `redis_version:7.0.0-fallback
redis_mode:standalone-fallback
process_id:${process.pid}
uptime_in_seconds:${Math.floor(Date.now() / 1000)}
connected_clients:1`;
  }

  /**
   * Quit connection (cleanup)
   */
  async quit() {
    console.log('🔄 Redis Fallback: Cleaning up in-memory cache');
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.storage.clear();
    this.keyExpiration.clear();
    this.pubSubChannels.clear();
    this.subscribers.clear();
    
    this.emit('end');
    return 'OK';
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    const hitRate = this.metrics.gets > 0 
      ? (this.metrics.hits / this.metrics.gets) * 100 
      : 0;

    return {
      ...this.metrics,
      hitRate: parseFloat(hitRate.toFixed(2)),
      storageSize: this.storage.size,
      mode: 'in-memory-fallback',
      isConnected: this.isConnected
    };
  }

  /**
   * Health check
   */
  async healthCheck() {
    return {
      status: 'healthy-fallback',
      mode: 'in-memory',
      responseTime: 1, // Always fast for in-memory
      isConnected: true,
      metrics: this.getMetrics()
    };
  }

  /**
   * Event handlers for compatibility
   */
  on(event, handler) {
    super.on(event, handler);
    return this;
  }

  emit(event, ...args) {
    super.emit(event, ...args);
    return this;
  }
}

export default InMemoryRedisFallback;
