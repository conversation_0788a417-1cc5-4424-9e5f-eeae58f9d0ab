import db from './db.js';

console.log('=== Checking current database dates ===');

// Check machine_stop_table_mould dates
const stopQuery = `
  SELECT 
    Machine_Name,
    Date_Insert,
    DATE_FORMAT(DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')), '%Y-%m-%d') AS formatted_date
  FROM machine_stop_table_mould 
  ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC 
  LIMIT 10
`;

db.query(stopQuery, (err, stopResults) => {
  if (err) {
    console.error('Error querying stop table:', err);
    return;
  }
  
  console.log('\n🔹 Recent machine_stop_table_mould dates:');
  stopResults.forEach(row => {
    console.log(`Machine: ${row.Machine_Name}, Raw Date: ${row.Date_Insert}, Formatted: ${row.formatted_date}`);
  });

  // Check machine_daily_table_mould dates
  const dailyQuery = `
    SELECT 
      Machine_Name,
      Date_Insert_Day,
      DATE_FORMAT(DATE(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')), '%Y-%m-%d') AS formatted_date
    FROM machine_daily_table_mould 
    ORDER BY STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') DESC 
    LIMIT 10
  `;
  
  db.query(dailyQuery, (err, dailyResults) => {
    if (err) {
      console.error('Error querying daily table:', err);
      return;
    }
    
    console.log('\n🔹 Recent machine_daily_table_mould dates:');
    dailyResults.forEach(row => {
      console.log(`Machine: ${row.Machine_Name}, Raw Date: ${row.Date_Insert_Day}, Formatted: ${row.formatted_date}`);
    });
    
    console.log('\n=== Date range analysis ===');
    
    // Get date range from stop table
    const rangeQuery = `
      SELECT 
        MIN(DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'))) AS min_date,
        MAX(DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'))) AS max_date,
        COUNT(*) as total_records
      FROM machine_stop_table_mould
    `;
    
    db.query(rangeQuery, (err, rangeResults) => {
      if (err) {
        console.error('Error getting date range:', err);
        process.exit(1);
      }
      
      console.log('Stop table date range:', rangeResults[0]);
      
      const dailyRangeQuery = `
        SELECT 
          MIN(DATE(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'))) AS min_date,
          MAX(DATE(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'))) AS max_date,
          COUNT(*) as total_records
        FROM machine_daily_table_mould
      `;
      
      db.query(dailyRangeQuery, (err, dailyRangeResults) => {
        if (err) {
          console.error('Error getting daily date range:', err);
          process.exit(1);
        }
        
        console.log('Daily table date range:', dailyRangeResults[0]);
        process.exit(0);
      });
    });
  });
});
