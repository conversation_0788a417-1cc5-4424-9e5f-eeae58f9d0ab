import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: './config.env' });

/**
 * Middleware to protect routes that require authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const protect = async (req, res, next) => {
  // Prioritize token from HTTP-only cookie
  // Fall back to headers for backward compatibility during transition
  const token = (req.cookies && req.cookies.token) ||
                (req.headers.authorization && req.headers.authorization.startsWith('Bearer')
                  ? req.headers.authorization.split(' ')[1]
                  : null);

  // If no token found, return unauthorized
  if (!token) {
    return res.status(401).json({
      status: 'fail',
      message: 'You are not logged in. Please log in to get access.',
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // Set user on request object
    req.user = decoded;

    next();
  } catch (error) {
    return res.status(401).json({
      status: 'fail',
      message: 'Invalid token or token expired. Please log in again.',
    });
  }
};

/**
 * Middleware to restrict access to specific roles
 * @param  {...String} roles - Roles allowed to access the route
 * @returns {Function} - Express middleware function
 */
export const restrictTo = (...roles) => {
  return (req, res, next) => {
    // Check if user role is in the allowed roles
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        status: 'fail',
        message: 'You do not have permission to perform this action',
      });
    }

    next();
  };
};

/**
 * Middleware to check if user is authenticated
 * Sets req.isAuthenticated to true if user is authenticated
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const isAuthenticated = (req, res, next) => {
  // Prioritize token from HTTP-only cookie
  // Fall back to headers for backward compatibility during transition
  const token = (req.cookies && req.cookies.token) ||
                (req.headers.authorization && req.headers.authorization.startsWith('Bearer')
                  ? req.headers.authorization.split(' ')[1]
                  : null);

  if (!token) {
    req.isAuthenticated = false;
    return next();
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Set user on request object
    req.user = decoded;
    req.isAuthenticated = true;
    next();
  } catch (error) {
    req.isAuthenticated = false;
    next();
  }
};