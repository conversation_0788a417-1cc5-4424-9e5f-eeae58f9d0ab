import express from "express";
import auth from "../middleware/auth.js";
import bypassPermission from "../middleware/bypassPermission.js";
import elasticsearchService from "../services/elasticsearchService.js";
import { body, query, validationResult } from "express-validator";
import redisRestApiEnhancer from '../services/RedisRestApiEnhancer.js';

const router = express.Router();

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Global search endpoint - searches across all indices - Enhanced with Redis caching
router.get("/global", auth, bypassPermission, [
  query("q").notEmpty().withMessage("Search query is required"),
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("size").optional().isInt({ min: 1, max: 100 }).withMessage("Size must be between 1 and 100")
], validateRequest, redisRestApiEnhancer.createCachedEndpoint('/api/search/global', {
  ttl: 300, // 5 minutes TTL for search results
  keyPrefix: 'search'
})(async (req, res) => {
  try {
    const { q, page = 1, size = 20 } = req.query;
    
    // Search across multiple indices
    const searchPromises = [
      elasticsearchService.searchMachineSessions({
        query: q,
        page: parseInt(page),
        size: Math.ceil(parseInt(size) / 4) // Distribute results across indices
      }),
      elasticsearchService.searchReports({
        query: q,
        page: parseInt(page),
        size: Math.ceil(parseInt(size) / 4)
      }),
      elasticsearchService.searchProductionData({
        query: q,
        page: parseInt(page),
        size: Math.ceil(parseInt(size) / 4)
      }),
      elasticsearchService.searchMachineStops({
        query: q,
        page: parseInt(page),
        size: Math.ceil(parseInt(size) / 4)
      })
    ];

    const [sessionResults, reportResults, productionResults, stopsResults] = await Promise.all(searchPromises);

    // Combine and format results
    const combinedResults = {
      total: sessionResults.total + reportResults.total + productionResults.total + stopsResults.total,
      results: [
        ...sessionResults.hits.map(hit => ({
          type: 'machine-session',
          id: hit._id,
          score: hit._score,
          data: hit._source,
          highlight: hit.highlight
        })),
        ...reportResults.hits.map(hit => ({
          type: 'report',
          id: hit._id,
          score: hit._score,
          data: hit._source,
          highlight: hit.highlight
        })),
        ...productionResults.hits.map(hit => ({
          type: 'production-data',
          id: hit._id,
          score: hit._score,
          data: hit._source,
          highlight: hit.highlight
        })),
        ...stopsResults.hits.map(hit => ({
          type: 'machine-stop',
          id: hit._id,
          score: hit._score,
          data: hit._source,
          highlight: hit.highlight
        }))
      ].sort((a, b) => b.score - a.score).slice(0, parseInt(size))
    };

    res.json(combinedResults);
  } catch (error) {
    console.error("Global search error:", error);
    res.status(500).json({ error: "Search failed", details: error.message });
  }
}));

// Search machine sessions
router.get("/sessions", auth, bypassPermission, [
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("size").optional().isInt({ min: 1, max: 100 }).withMessage("Size must be between 1 and 100")
], validateRequest, async (req, res) => {
  try {
    const {
      q: query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      status,
      shift,
      page = 1,
      size = 20
    } = req.query;

    const searchParams = {
      query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      status,
      shift,
      page: parseInt(page),
      size: parseInt(size)
    };

    const results = await elasticsearchService.searchMachineSessions(searchParams);

    res.json({
      sessions: results.hits.map(hit => ({
        id: hit._id,
        score: hit._score,
        ...hit._source,
        highlight: hit.highlight
      })),
      total: results.total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(results.total / parseInt(size))
    });
  } catch (error) {
    console.error("Session search error:", error);
    res.status(500).json({ error: "Session search failed", details: error.message });
  }
});

// Search reports with advanced filtering
router.get("/reports", auth, bypassPermission, [
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("size").optional().isInt({ min: 1, max: 100 }).withMessage("Size must be between 1 and 100")
], validateRequest, async (req, res) => {
  try {
    const {
      q: query,
      type,
      machineId,
      dateFrom,
      dateTo,
      generatedBy,
      page = 1,
      size = 20
    } = req.query;

    const searchParams = {
      query,
      type,
      machineId,
      dateFrom,
      dateTo,
      generatedBy,
      page: parseInt(page),
      size: parseInt(size)
    };

    const results = await elasticsearchService.searchReports(searchParams);

    res.json({
      reports: results.hits.map(hit => ({
        id: hit._id,
        score: hit._score,
        ...hit._source,
        highlight: hit.highlight
      })),
      total: results.total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(results.total / parseInt(size))
    });
  } catch (error) {
    console.error("Report search error:", error);
    res.status(500).json({ error: "Report search failed", details: error.message });
  }
});

// Search production data with advanced filtering and SQL fallback
router.get("/production", auth, bypassPermission, [
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("size").optional().isInt({ min: 1, max: 100 }).withMessage("Size must be between 1 and 100")
], validateRequest, async (req, res) => {
  try {
    const {
      q: query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      shift,
      operator,
      minOEE,
      maxOEE,
      page = 1,
      size = 20
    } = req.query;

    const searchParams = {
      query,
      machineId,
      machineModel,
      dateFrom,
      dateTo,
      shift,
      operator,
      minOEE: minOEE ? parseFloat(minOEE) : undefined,
      maxOEE: maxOEE ? parseFloat(maxOEE) : undefined,
      page: parseInt(page),
      size: parseInt(size)
    };

    let results;
    let searchMethod = 'elasticsearch';

    try {
      // Try Elasticsearch first
      const esResults = await elasticsearchService.searchProductionData(searchParams);
      results = {
        production: esResults.hits.map(hit => ({
          id: hit._id,
          score: hit._score,
          ...hit._source,
          highlight: hit.highlight
        })),
        total: esResults.total,
        page: parseInt(page),
        size: parseInt(size),
        totalPages: Math.ceil(esResults.total / parseInt(size))
      };
    } catch (esError) {
      console.warn("Elasticsearch search failed, falling back to SQL:", esError.message);
      searchMethod = 'sql_fallback';

      // Import database connection
      const db = await import("../db.js");
      results = await elasticsearchService.searchProductionDataSQL(searchParams, db.default);

      // Add metadata for SQL results
      results.production = results.production.map(item => ({
        id: `${item.Machine_Name}_${item.Date_Insert_Day}`,
        score: 1.0, // Default score for SQL results
        ...item
      }));
    }

    // Add search metadata
    results.searchMethod = searchMethod;
    results.timestamp = new Date().toISOString();

    res.json(results);
  } catch (error) {
    console.error("Production search error:", error);
    res.status(500).json({
      error: "Production search failed",
      details: error.message,
      searchMethod: 'failed'
    });
  }
});

// Search machine stops/arrets with advanced filtering and SQL fallback
router.get("/stops", auth, bypassPermission, [
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("size").optional().isInt({ min: 1, max: 100 }).withMessage("Size must be between 1 and 100")
], validateRequest, async (req, res) => {
  try {
    const {
      q: query,
      machineId,
      machineModel,
      stopCode,
      stopCategory,
      severity,
      dateFrom,
      dateTo,
      resolved,
      maintenanceRequired,
      page = 1,
      size = 20
    } = req.query;

    const searchParams = {
      query,
      machineId,
      machineModel,
      stopCode,
      stopCategory,
      severity,
      dateFrom,
      dateTo,
      resolved: resolved !== undefined ? resolved === 'true' : undefined,
      maintenanceRequired: maintenanceRequired !== undefined ? maintenanceRequired === 'true' : undefined,
      page: parseInt(page),
      size: parseInt(size)
    };

    let results;
    let searchMethod = 'elasticsearch';

    try {
      // Try Elasticsearch first
      const esResults = await elasticsearchService.searchMachineStops(searchParams);
      results = {
        stops: esResults.hits.map(hit => ({
          id: hit._id,
          score: hit._score,
          ...hit._source,
          highlight: hit.highlight
        })),
        total: esResults.total,
        page: parseInt(page),
        size: parseInt(size),
        totalPages: Math.ceil(esResults.total / parseInt(size))
      };
    } catch (esError) {
      console.warn("Elasticsearch search failed, falling back to SQL:", esError.message);
      searchMethod = 'sql_fallback';

      // Import database connection
      const db = await import("../db.js");
      results = await elasticsearchService.searchMachineStopsSQL(searchParams, db.default);

      // Add metadata for SQL results
      results.stops = results.stops.map(item => ({
        id: `${item.Machine_Name}_${item.Date_Insert}_${item.Debut_Stop}`,
        score: 1.0, // Default score for SQL results
        ...item
      }));
    }

    // Add search metadata
    results.searchMethod = searchMethod;
    results.timestamp = new Date().toISOString();

    res.json(results);
  } catch (error) {
    console.error("Stops search error:", error);
    res.status(500).json({
      error: "Stops search failed",
      details: error.message,
      searchMethod: 'failed'
    });
  }
});

// Analytics endpoint for machine performance aggregations
router.get("/analytics/machine-performance", auth, bypassPermission, [
  query("dateFrom").notEmpty().withMessage("Start date is required"),
  query("dateTo").notEmpty().withMessage("End date is required")
], validateRequest, async (req, res) => {
  try {
    const { dateFrom, dateTo } = req.query;

    const results = await elasticsearchService.getMachinePerformanceAggregation(dateFrom, dateTo);

    // Format aggregation results
    const machinePerformance = results.aggregations.machines.buckets.map(bucket => ({
      machineId: bucket.key,
      sessionCount: bucket.session_count.value,
      averageTRS: Math.round(bucket.avg_trs.value * 100) / 100,
      averageEfficiency: Math.round(bucket.avg_efficiency.value * 100) / 100,
      totalProduction: bucket.total_production.value
    }));

    res.json({
      dateRange: { from: dateFrom, to: dateTo },
      machines: machinePerformance,
      summary: {
        totalMachines: machinePerformance.length,
        totalSessions: machinePerformance.reduce((sum, m) => sum + m.sessionCount, 0),
        averageTRS: machinePerformance.reduce((sum, m) => sum + m.averageTRS, 0) / machinePerformance.length,
        totalProduction: machinePerformance.reduce((sum, m) => sum + m.totalProduction, 0)
      }
    });
  } catch (error) {
    console.error("Analytics error:", error);
    res.status(500).json({ error: "Analytics query failed", details: error.message });
  }
});

// Autocomplete/suggestions endpoint
router.get("/suggest", auth, bypassPermission, [
  query("q").notEmpty().withMessage("Query is required"),
  query("field").optional().isIn(['machineName', 'operator', 'title', 'description']).withMessage("Invalid field")
], validateRequest, async (req, res) => {
  try {
    const { q, field = 'machineName', size = 10 } = req.query;

    // Create a simple prefix query for suggestions
    const searchQuery = {
      query: {
        bool: {
          should: [
            {
              match_phrase_prefix: {
                [field]: {
                  query: q,
                  max_expansions: 10
                }
              }
            },
            {
              wildcard: {
                [`${field}.keyword`]: `*${q}*`
              }
            }
          ]
        }
      },
      _source: [field],
      size: parseInt(size)
    };

    // Search across relevant indices
    const indices = field === 'title' || field === 'description' ? ['reports'] : ['machine-sessions', 'reports'];
    
    const suggestions = new Set();
    
    for (const index of indices) {
      try {
        const results = await elasticsearchService.search(index, searchQuery);
        results.hits.forEach(hit => {
          const value = hit._source[field];
          if (value && typeof value === 'string') {
            suggestions.add(value);
          }
        });
      } catch (indexError) {
        console.warn(`Error searching in index ${index}:`, indexError.message);
      }
    }

    res.json({
      suggestions: Array.from(suggestions).slice(0, parseInt(size))
    });
  } catch (error) {
    console.error("Suggestion error:", error);
    res.status(500).json({ error: "Suggestion query failed", details: error.message });
  }
});

// Enhanced health check endpoint using SuperAgent service
router.get("/health", async (req, res) => {
  try {
    const healthMonitoringService = (await import("../services/HealthMonitoringService.js")).default;
    
    // Get comprehensive health status
    const healthStatus = await healthMonitoringService.performHealthCheck();
    
    // Include additional context for search service specifically
    const searchServiceHealth = {
      ...healthStatus,
      search_service: {
        elasticsearch_available: healthStatus.services.elasticsearch?.status === 'healthy',
        sql_fallback_available: healthStatus.services.database?.status === 'healthy',
        current_mode: healthStatus.services.elasticsearch?.status === 'healthy' ? 'elasticsearch' : 'sql_fallback'
      },
      service_info: {
        service: 'search-service',
        version: process.env.APP_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime()
      }
    };

    // Set appropriate HTTP status based on health
    const httpStatus = healthStatus.overall === 'healthy' ? 200 : 
                      healthStatus.overall === 'degraded' ? 200 : 503;

    res.status(httpStatus).json(searchServiceHealth);
  } catch (error) {
    console.error("Health check error:", error);
    res.status(500).json({
      overall: 'error',
      error: error.message,
      timestamp: new Date().toISOString(),
      search_service: {
        elasticsearch_available: false,
        sql_fallback_available: false,
        current_mode: 'unknown'
      }
    });
  }
});

// Reindex endpoint for manual data synchronization
router.post("/reindex", auth, bypassPermission, [
  body("index").isIn(['machine-sessions', 'reports', 'all']).withMessage("Invalid index name"),
  body("dateFrom").optional().isISO8601().withMessage("Invalid date format"),
  body("dateTo").optional().isISO8601().withMessage("Invalid date format")
], validateRequest, async (req, res) => {
  try {
    const { index, dateFrom, dateTo, force = false } = req.body;

    // This would be implemented to reindex data from MySQL to Elasticsearch
    // For now, return a placeholder response
    res.json({
      message: "Reindexing initiated",
      index,
      dateRange: dateFrom && dateTo ? { from: dateFrom, to: dateTo } : null,
      status: "pending",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Reindex error:", error);
    res.status(500).json({ error: "Reindexing failed", details: error.message });
  }
});

export default router;
