import React from "react";
import {
  RiseOutlined,
  FallOutlined,
  DashboardOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import SOMIPEM_COLORS from "../../styles/brand-colors";
import { formatStatValue } from "../../utils/numberFormatter";

/**
 * Generate statistics configuration for the dashboard
 * @param {number} goodQty - Total good quantity
 * @param {number} rejetQty - Total reject quantity
 * @param {number} avgTRS - Average TRS
 * @param {number} avgAvailability - Average availability
 * @param {number} avgPerformance - Average performance
 * @param {number} rejectRate - Reject rate
 * @param {number} qualityRate - Quality rate
 * @returns {Array} Array of statistics configuration objects
 */
const getStatisticsConfig = (
  goodQty,
  rejetQty,
  avgTRS,
  avgAvailability,
  avgPerformance,
  rejectRate,
  qualityRate
) => [
  {
    title: "Production Totale",
    value: formatStatValue(goodQty, "Pcs"),
    rawValue: goodQty,
    suffix: "Pcs",
    icon: <RiseOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Nombre total de pièces bonnes produites",
  },
  {
    title: "Rejet Total",
    value: formatStatValue(rejetQty, "Kg"),
    rawValue: rejetQty,
    suffix: "Kg",
    icon: <FallOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Nombre total de pièces rejetées",
  },
  {
    title: "TRS Moyen",
    value: formatStatValue(avgTRS, "%"),
    rawValue: avgTRS,
    suffix: "%",
    icon: <DashboardOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Taux de Rendement Synthétique moyen (OEE_Day)",
  },
  {
    title: "Disponibilité",
    value: formatStatValue(avgAvailability, "%"),
    rawValue: avgAvailability,
    suffix: "%",
    icon: <ClockCircleOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Taux de disponibilité moyen (Availability_Rate_Day)",
  },
  {
    title: "Performance",
    value: formatStatValue(avgPerformance, "%"),
    rawValue: avgPerformance,
    suffix: "%",
    icon: <ThunderboltOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Taux de performance moyen (Performance_Rate_Day)",
  },
  {
    title: "Taux de Rejet",
    value: formatStatValue(rejectRate, "%"),
    rawValue: rejectRate,
    suffix: "%",
    icon: <CloseCircleOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Pourcentage de pièces rejetées sur la production totale",
  },
  {
    title: "Taux de Qualité",
    value: formatStatValue(qualityRate, "%"),
    rawValue: qualityRate,
    suffix: "%",
    icon: <CheckCircleOutlined />,
    color: SOMIPEM_COLORS.PRIMARY_BLUE, // #1E3A8A
    description: "Pourcentage de pièces bonnes sur la production totale",
  },
];

export default getStatisticsConfig;
