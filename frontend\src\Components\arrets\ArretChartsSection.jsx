import React from 'react';
import { <PERSON>, <PERSON>, Card, Tabs, Button, Space, Empty } from 'antd';
import { 
  Bar<PERSON>hartOutlined, 
  PieChartOutlined, 
  LineChartOutlined, 
  FullscreenOutlined, 
  AreaChartOutlined,  DashboardOutlined,
  TableOutlined,
  TrophyOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FundOutlined,
  AppstoreOutlined,
  HomeOutlined 
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import ArretDualBarChart from './charts/ArretDualBarChart';
import ArretPieChart from './charts/ArretPieChart';
import ArretTrendChart from './charts/ArretTrendChart';
import ArretHeatmapChart from './charts/ArretHeatmapChart';
import ArretHorizontalBarChart from './charts/ArretHorizontalBarChart';
import ArretAreaChart from './charts/ArretAreaChart';

import ArretOperatorStatsTable from './ArretOperatorStatsTable';
import ArretDisponibiliteChart from './charts/ArretDisponibiliteChart';
import ArretParetoChart from './charts/ArretParetoChart';
import ArretPerformanceGauge from './charts/ArretPerformanceGauge';
import ArretProductionOrderChart from './charts/ArretProductionOrderChart';
import ArretOperatorChart from './charts/ArretOperatorChart';
import ArretMachineEfficiencyChart from './charts/ArretMachineEfficiencyChart';
import ArretTimePatternChart from './charts/ArretTimePatternChart';
import ArretDurationDistributionChart from './charts/ArretDurationDistributionChart';
// New separated metric components
import AvailabilityTrendChart from './charts/AvailabilityTrendChart';
import MTTRTrendChart from './charts/MTTRTrendChart';
import DowntimeDurationChart from './charts/DowntimeDurationChart';
import CumulativeImpactChart from './charts/CumulativeImpactChart';
// Import the working components from the original Arrets2 structure
import PerformanceMetricsGauge from '../charts/performance-metrics-gauge';
import DowntimeParetoChart from '../charts/downtime-pareto-chart';
import DisponibiliteByMachineChart from '../charts/disponibilite-by-machine-chart';
import MttrHeatCalendar from '../charts/mttr-heat-calendar';

import SOMIPEM_COLORS from '../../styles/brand-colors';
import { useSettings } from '../../hooks/useSettings';
import { useUnifiedDashboardCharts } from '../../utils/unifiedChartUtils';
import UnifiedChartExpansion from '../charts/UnifiedChartExpansion/UnifiedChartExpansion';

import ArretErrorBoundary from './ArretErrorBoundary';

const { TabPane } = Tabs;

const ArretChartsSection = () => {
  // Initialize unified chart configuration
  const chartConfig = useUnifiedDashboardCharts();
  
  // For backward compatibility, maintain the old interface
  const { getChartColor, getChartColors, charts } = chartConfig;
  
  // Get settings for enhanced chart configuration
  const { charts: settingsCharts } = useSettings();
  const clickToExpandEnabled = settingsCharts.interactions?.clickToExpand !== false;

  const context = useArretQueuedContext();
  
  // Extra defensive programming
  if (!context) {
    return <div>Context not available</div>;
  }  const { 
    chartData = [], 
    topStopsData = [],
    durationTrend = [],
    machineComparison = [],
    stopReasons = [],
    stopsData = [],
    filteredStopsData = [],
    operatorStats = [],
    disponibiliteTrendData = [],
    downtimeParetoData = [],
    mttrCalendarData = [],
    disponibiliteByMachineData = [],    selectedMachine = "",
    selectedMachineModel = "",
    selectedDate = null,
    dateRangeType = "day",
    mttr = 0,
    mtbf = 0,
    doper = 0,
    loading = true, 
    chartOptions = { activeTab: 'bar' }, 
    setChartOptions,

    dateFilterActive = false,
    dateRangeDescription = ""  } = context;

  // Debug logging for filter state with enhanced triple filter detection
  React.useEffect(() => {
    const hasAllFilters = selectedMachineModel && selectedMachine && selectedDate;
    
    console.log('🎯 ArretChartsSection - Context Data Debug (Updated):', {
      topStopsData: topStopsData?.length || 0,
      machineComparison: machineComparison?.length || 0,
      stopReasons: stopReasons?.length || 0,
      chartData: chartData?.length || 0,
      filteredStopsData: filteredStopsData?.length || 0,
      stopsData: stopsData?.length || 0,
      disponibiliteTrendData: disponibiliteTrendData?.length || 0,
      mttrCalendarData: mttrCalendarData?.length || 0,
      downtimeParetoData: downtimeParetoData?.length || 0,
      operatorStats: operatorStats?.length || 0,
      disponibiliteByMachineData: disponibiliteByMachineData?.length || 0,
      loading,
      activeTab: chartOptions?.activeTab,
      selectedMachine,
      selectedMachineModel,
      dateFilterActive,
      dateRangeType,
      sampleStopsData: stopsData?.slice(0, 2) || []
    });

    // Enhanced debug for specific chart data mapping
    const currentData = getChartData(chartOptions?.activeTab);
    console.log(`🔍 ArretChartsSection - Active Tab "${chartOptions?.activeTab}" Data:`, {
      dataLength: currentData?.length || 0,
      dataType: typeof currentData,
      isArray: Array.isArray(currentData),
      sampleData: currentData?.slice(0, 2) || 'No data',
      chartTitle: getChartTitle(chartOptions?.activeTab),
      chartType: getChartType(chartOptions?.activeTab)
    });

    console.log("🔍 ArretChartsSection - stopsData sample:", stopsData?.slice(0, 2));
    console.log("🔍 ArretChartsSection - disponibiliteTrendData sample:", disponibiliteTrendData?.slice(0, 2));
    console.log("🔍 ArretChartsSection - downtimeParetoData sample:", downtimeParetoData?.slice(0, 2));
    console.log("🔍 ArretChartsSection - operatorStats sample:", operatorStats?.slice(0, 2));
    console.log("🔍 ArretChartsSection - disponibiliteByMachineData sample:", disponibiliteByMachineData?.slice(0, 2));
  }, [
    topStopsData, 
    machineComparison, 
    stopReasons, 
    chartData, 
    filteredStopsData, 
    stopsData, 
    disponibiliteTrendData, 
    mttrCalendarData, 
    downtimeParetoData, 
    operatorStats,
    disponibiliteByMachineData,
    loading, 
    chartOptions, 
    selectedMachine, 
    selectedMachineModel,
    dateFilterActive,
    dateRangeType
  ]);
  // Data validation helper
  const validateChartData = (data, chartType) => {
    if (!data) return { isValid: false, reason: 'Data is null or undefined' };
    if (!Array.isArray(data)) return { isValid: false, reason: 'Data is not an array' };
    if (data.length === 0) return { isValid: false, reason: 'Data array is empty' };

    // Basic validation for different chart types
    const firstItem = data[0];
    if (!firstItem || typeof firstItem !== 'object') {
      return { isValid: false, reason: 'First data item is not a valid object' };
    }

    return { isValid: true, reason: 'Data is valid' };
  };

  const handleChartTypeChange = (key) => {
    console.log('🎯 Chart type change requested:', key, 'Current:', chartOptions?.activeTab);
    if (setChartOptions) {
      setChartOptions(prev => ({
        ...prev,
        activeTab: key
      }));
    } else {
      console.warn('⚠️ setChartOptions function not available');
    }
  };

  // Helper function to get chart data based on active tab with fallback logic
  const getChartData = (activeTab) => {
    let primaryData, fallbackData;

    switch (activeTab) {
      case 'bar':
        primaryData = machineComparison;
        fallbackData = stopsData;
        break;
      case 'pie':
        primaryData = topStopsData;
        fallbackData = stopsData;
        break;
      case 'trend':
        primaryData = chartData;
        fallbackData = stopsData;
        break;
      case 'heatmap':
        primaryData = filteredStopsData;
        fallbackData = stopsData;
        break;
      case 'horizontalBar':
        primaryData = stopReasons;
        fallbackData = stopsData;
        break;
      case 'area':
        primaryData = durationTrend;
        fallbackData = stopsData;
        break;
      case 'productionOrders':
        primaryData = downtimeParetoData;
        fallbackData = stopsData;
        break;
      case 'operators':
        primaryData = operatorStats;
        fallbackData = stopsData;
        break;
      case 'machineEfficiency':
        primaryData = disponibiliteByMachineData;
        fallbackData = stopsData;
        break;
      case 'timePatterns':
        primaryData = mttrCalendarData;
        fallbackData = stopsData;
        break;
      case 'durationDistribution':
        primaryData = disponibiliteTrendData;
        fallbackData = stopsData;
        break;
      default:
        return [];
    }

    // Return primary data if it exists and has content, otherwise fallback
    const hasValidPrimaryData = Array.isArray(primaryData) && primaryData.length > 0;
    const hasValidFallbackData = Array.isArray(fallbackData) && fallbackData.length > 0;

    if (hasValidPrimaryData) {
      return primaryData;
    } else if (hasValidFallbackData) {
      console.log(`🔄 ArretChartsSection - Using fallback data for ${activeTab}:`, {
        primaryDataLength: primaryData?.length || 0,
        fallbackDataLength: fallbackData?.length || 0
      });
      return fallbackData;
    } else {
      console.warn(`⚠️ ArretChartsSection - No valid data for ${activeTab}:`, {
        primaryData: primaryData?.length || 0,
        fallbackData: fallbackData?.length || 0,
        hasValidPrimaryData,
        hasValidFallbackData,
        primaryDataType: typeof primaryData,
        fallbackDataType: typeof fallbackData
      });
      return [];
    }
  };

  // Helper function to get chart type based on active tab
  const getChartType = (activeTab) => {
    switch (activeTab) {
      case 'bar': return 'bar';
      case 'pie': return 'pie';
      case 'trend': return 'line';
      case 'heatmap': return 'heatmap';
      case 'horizontalBar': return 'bar';
      case 'area': return 'area';
      default: return 'bar';
    }
  };

  // Helper function to get chart title based on active tab
  const getChartTitle = (activeTab) => {
    switch (activeTab) {
      case 'bar': return 'Comparaison Machines';
      case 'pie': return 'Top 5 Causes';
      case 'trend': return 'Evolution Arrêts';
      case 'heatmap': return 'Durée par Heure';
      case 'horizontalBar': return 'Causes d\'Arrêt';
      case 'area': return 'Tendance Durée';
      case 'productionOrders': return 'Analyse Production';
      case 'operators': return 'Performance Opérateurs';
      case 'machineEfficiency': return 'Efficacité Machines';
      case 'timePatterns': return 'Motifs Temporels';
      case 'durationDistribution': return 'Distribution Durées';
      default: return 'Graphique';
    }
  };

  const chartTabs = [    {
      key: 'bar',
      tab: (
        <span>
          <BarChartOutlined />
          Comparaison Machines
        </span>
      ),
      content: <ArretDualBarChart
        data={machineComparison}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },
    {
      key: 'pie',
      tab: (
        <span>
          <PieChartOutlined />
          Top 5 Causes
        </span>
      ),
      content: <ArretPieChart
        data={topStopsData}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },
    {
      key: 'trend',
      tab: (
        <span>
          <LineChartOutlined />
          Evolution Arrêts
        </span>
      ),
      content: <ArretTrendChart
        data={chartData}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY
        ])}
      />
    },  
      {
      key: 'heatmap',
      tab: (
        <span>
          <AreaChartOutlined />
          Durée par Heure
        </span>
      ),
      content: <ArretHeatmapChart
        data={filteredStopsData.length > 0 ? filteredStopsData : stopsData}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },{
      key: 'horizontalBar',
      tab: (
        <span>
          <BarChartOutlined />
          Causes d'Arrêt
        </span>
      ),
      content: <ArretHorizontalBarChart
        data={stopReasons}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },    {
      key: 'productionOrders',
      tab: (
        <span>
          <DashboardOutlined />
          Analyse Production
        </span>
      ),
      content: <ArretProductionOrderChart
        data={downtimeParetoData.length > 0 ? downtimeParetoData : stopsData}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },
    {
      key: 'operators',
      tab: (
        <span>
          <UserOutlined />
          Performance Opérateurs
        </span>
      ),
      content: <ArretOperatorChart
        data={operatorStats.length > 0 ? operatorStats : stopsData}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },
    {
      key: 'machineEfficiency',
      tab: (
        <span>
          <FundOutlined />
          Efficacité Machines
        </span>
      ),
      content: <ArretMachineEfficiencyChart
        data={disponibiliteByMachineData.length > 0 ? disponibiliteByMachineData : (stopsData.length > 0 ? stopsData : [])}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },
    {
      key: 'timePatterns',
      tab: (
        <span>
          <ClockCircleOutlined />
          Motifs Temporels
        </span>
      ),
      content: <ArretTimePatternChart
        data={mttrCalendarData.length > 0 ? mttrCalendarData : (stopsData.length > 0 ? stopsData : [])}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    },
    {
      key: 'durationDistribution',
      tab: (
        <span>
          <AppstoreOutlined />
          Distribution Durées
        </span>
      ),
      content: <ArretDurationDistributionChart
        data={disponibiliteTrendData.length > 0 ? disponibiliteTrendData : (stopsData.length > 0 ? stopsData : [])}
        loading={loading}
        colors={getChartColors([
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ])}
      />
    }
  ];

  return (
    <div style={{ marginBottom: '24px' }}>
      {/* Chart Selection Buttons */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col span={24}>          <Card size="small" style={{ background: '#fafafa' }}>
            <Space wrap size="middle" style={{ width: '100%', justifyContent: 'center' }}>
              {chartTabs.map(tab => (
                <Button
                  key={tab.key}
                  type={chartOptions.activeTab === tab.key ? "primary" : "default"}
                  icon={tab.tab.props.children[0]}
                  onClick={() => handleChartTypeChange(tab.key)}
                  size="large"
                  style={{
                    height: '52px',
                    minWidth: '180px',
                    borderRadius: '10px',
                    fontWeight: chartOptions.activeTab === tab.key ? 'bold' : 'normal',
                    fontSize: '14px',
                    boxShadow: chartOptions.activeTab === tab.key 
                      ? `0 4px 12px ${SOMIPEM_COLORS.PRIMARY_BLUE}40` // SOMIPEM Primary Blue shadow with transparency
                      : '0 2px 6px rgba(0,0,0,0.1)',
                    border: chartOptions.activeTab === tab.key 
                      ? `2px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}` // SOMIPEM Primary Blue border
                      : '1px solid #d9d9d9',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {tab.tab.props.children[1]}
                </Button>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Active Chart Display with Unified Expansion */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          {/* Debug Info Panel (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div style={{
              background: '#f0f0f0',
              padding: '8px',
              marginBottom: '8px',
              borderRadius: '4px',
              fontSize: '12px',
              fontFamily: 'monospace'
            }}>
              <strong>Debug Info:</strong> {chartOptions.activeTab} |
              Data Length: {getChartData(chartOptions.activeTab)?.length || 0} |
              Chart Type: {getChartType(chartOptions.activeTab)} |
              Loading: {loading ? 'Yes' : 'No'} |
              Click to Expand: {clickToExpandEnabled ? 'Enabled' : 'Disabled'}
            </div>
          )}

          <ArretErrorBoundary>
            <UnifiedChartExpansion
              title={getChartTitle(chartOptions.activeTab)}
              data={getChartData(chartOptions.activeTab)}
              chartType={getChartType(chartOptions.activeTab)}
              expandMode="modal"
              exportEnabled={true}
              style={{
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                border: '1px solid #e8e8e8'
              }}
              cardProps={{
                styles: {
                  body: { padding: '24px' }
                }
              }}
            >
              {chartTabs.find(tab => tab.key === chartOptions.activeTab)?.content}
            </UnifiedChartExpansion>
          </ArretErrorBoundary>
        </Col>
      </Row>

      {/* Advanced Analytics Section */}
      <div style={{ marginTop: '48px' }}>
        {/* Section Header */}
        <div style={{ 
          textAlign: 'center',
          marginBottom: '40px',
          padding: '32px',
          background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE} 0%, ${SOMIPEM_COLORS.SECONDARY_BLUE} 100%)`, // SOMIPEM gradient
          borderRadius: '24px',
          color: 'white',
          boxShadow: `0 20px 60px ${SOMIPEM_COLORS.PRIMARY_BLUE}40`, // SOMIPEM Primary Blue shadow
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Background decoration */}
          <div style={{
            position: 'absolute',
            top: '-50%',
            right: '-10%',
            width: '300px',
            height: '300px',
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '50%',
            filter: 'blur(40px)'
          }}></div>
          <div style={{
            position: 'absolute',
            bottom: '-30%',
            left: '-5%',
            width: '200px',
            height: '200px',
            background: 'rgba(255,255,255,0.08)',
            borderRadius: '50%',
            filter: 'blur(30px)'
          }}></div>
          
          <div style={{ position: 'relative', zIndex: 1 }}>
            <TrophyOutlined style={{ fontSize: '36px', marginBottom: '16px', color: '#fff' }} />
            <h2 style={{ 
              margin: 0, 
              fontSize: '32px', 
              fontWeight: '700',
              color: 'white',
              letterSpacing: '0.5px'
            }}>
              Analyses Avancées
            </h2>
            <p style={{ 
              margin: '12px 0 0 0', 
              opacity: 0.95, 
              fontSize: '18px',
              color: 'white',
              fontWeight: '300'
            }}>
              Indicateurs clés de performance et analyses prédictives en temps réel
            </p>          </div>
        </div>        {/* Main Analytics Grid */}
        {selectedMachine ? (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(800px, 1fr))',          
            gap: '40px',
            marginBottom: '50px'
          }}>
            {/* Availability Trend Card */}
            <div style={{
              background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
              borderRadius: '24px',
              padding: '0',
              boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)',
              border: '1px solid rgba(255,255,255,0.2)',
              position: 'relative',
              overflow: 'hidden',
              minHeight: '750px'
            }}>
              {/* Card Header */}
              <div style={{
                background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE})`, // SOMIPEM gradient
                padding: '24px 32px',
                color: 'white',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  top: '-50px',
                  right: '-50px',
                  width: '150px',
                  height: '150px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '50%',
                  filter: 'blur(20px)'
                }}></div>
                
                <div style={{ position: 'relative', zIndex: 1 }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <div style={{
                      width: '56px',
                      height: '56px',
                      borderRadius: '16px',
                      background: 'rgba(255,255,255,0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '20px',
                      backdropFilter: 'blur(10px)'
                    }}>
                      <span style={{ fontSize: '24px' }}>📈</span>
                    </div>
                    <div>
                      <h3 style={{ 
                        margin: 0, 
                        fontSize: '24px', 
                        fontWeight: '700', 
                        color: 'white'
                      }}>
                        Tendance de Disponibilité
                      </h3>
                      <p style={{ 
                        margin: '4px 0 0 0', 
                        color: 'rgba(255,255,255,0.9)', 
                        fontSize: '16px',
                        fontWeight: '300'
                      }}>
                        Évolution des performances de {selectedMachine}
                      </p>
                    </div>
                  </div>
                </div>
              </div>{/* Card Content */}
            <div style={{ padding: '32px' }}>
              {disponibiliteTrendData.length > 0 ? (
                <div style={{ 
                  display: 'grid',
                  gridTemplateRows: '1fr 1fr',
                  gap: '30px',
                  height: '600px'
                }}>
                  {/* Availability Trend Subchart */}
                  <div style={{ 
                    background: `linear-gradient(145deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}10, ${SOMIPEM_COLORS.SECONDARY_BLUE}10)`, // SOMIPEM light gradient
                    borderRadius: '16px',
                    padding: '20px',
                    border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}20`, // SOMIPEM border with transparency
                    position: 'relative'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '16px'
                    }}>
                      <div style={{
                        width: '4px',
                        height: '28px',
                        background: `linear-gradient(to bottom, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE})`, // SOMIPEM gradient
                        borderRadius: '2px',
                        marginRight: '12px'
                      }}></div>
                      <h4 style={{ 
                        margin: 0, 
                        color: SOMIPEM_COLORS.PRIMARY_BLUE, // SOMIPEM Primary Blue
                        fontSize: '18px',
                        fontWeight: '600'
                      }}>
                        Disponibilité (%)
                      </h4>
                    </div>
                    <div style={{ height: 'calc(100% - 50px)' }}>
                      <AvailabilityTrendChart data={disponibiliteTrendData} loading={loading} />
                    </div>
                  </div>

                  {/* MTTR Trend Subchart */}
                  <div style={{ 
                    background: `linear-gradient(145deg, ${SOMIPEM_COLORS.LIGHT_GRAY}10, ${SOMIPEM_COLORS.DARK_GRAY}05)`, // SOMIPEM light gradient
                    borderRadius: '16px',
                    padding: '20px',
                    border: `1px solid ${SOMIPEM_COLORS.LIGHT_GRAY}20`, // SOMIPEM border with transparency
                    position: 'relative'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '16px'
                    }}>
                      <div style={{
                        width: '4px',
                        height: '28px',
                        background: `linear-gradient(to bottom, ${SOMIPEM_COLORS.SECONDARY_BLUE}, ${SOMIPEM_COLORS.PRIMARY_BLUE})`, // SOMIPEM gradient
                        borderRadius: '2px',
                        marginRight: '12px'
                      }}></div>
                      <h4 style={{ 
                        margin: 0, 
                        color: SOMIPEM_COLORS.SECONDARY_BLUE, // SOMIPEM Secondary Blue
                        fontSize: '18px',
                        fontWeight: '600'
                      }}>
                        Temps Moyen de Réparation (MTTR)
                      </h4>
                    </div>                    <div style={{ height: 'calc(100% - 50px)' }}>
                      <MTTRTrendChart data={mttrCalendarData} loading={loading} />
                    </div>
                  </div>
                </div>              ) : (
                <div style={{ 
                  height: '600px', 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  background: `linear-gradient(145deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}10, ${SOMIPEM_COLORS.SECONDARY_BLUE}10)`, // SOMIPEM light gradient
                  borderRadius: '16px'
                }}>
                  <Empty 
                    description="Aucune donnée de disponibilité disponible" 
                    style={{ color: '#8c8c8c' }}
                  />
                </div>
              )}
            </div>
          </div>          {/* Pareto Analysis Card */}
          <div style={{
            background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
            borderRadius: '24px',
            padding: '0',
            boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)',
            border: '1px solid rgba(255,255,255,0.2)',
            position: 'relative',
            overflow: 'hidden',
            minHeight: '750px'
          }}>
            {/* Card Header */}
            <div style={{
              background: `linear-gradient(135deg, ${SOMIPEM_COLORS.DARK_GRAY}, ${SOMIPEM_COLORS.LIGHT_GRAY})`, // SOMIPEM gradient
              padding: '24px 32px',
              color: 'white',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '-50px',
                right: '-50px',
                width: '150px',
                height: '150px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}></div>
              
              <div style={{ position: 'relative', zIndex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    width: '56px',
                    height: '56px',
                    borderRadius: '16px',
                    background: 'rgba(255,255,255,0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '20px',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <span style={{ fontSize: '24px' }}>📊</span>
                  </div>
                  <div>
                    <h3 style={{ 
                      margin: 0, 
                      fontSize: '24px', 
                      fontWeight: '700', 
                      color: 'white'
                    }}>
                      Analyse Pareto des Temps d'Arrêt
                    </h3>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      color: 'rgba(255,255,255,0.9)', 
                      fontSize: '16px',
                      fontWeight: '300'
                    }}>
                      Identification des causes principales
                    </p>
                  </div>
                </div>
              </div>
            </div>            {/* Card Content */}
            <div style={{ padding: '32px' }}>              {downtimeParetoData.length > 0 ? (                <div style={{ 
                  display: 'grid',
                  gridTemplateRows: '1fr 1fr',
                  gap: '12px',
                  height: '780px'
                }}>
                  {/* Downtime Duration Subchart */}                  <div style={{ 
                    background: `linear-gradient(145deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}10, ${SOMIPEM_COLORS.SECONDARY_BLUE}10)`, // SOMIPEM light gradient
                    borderRadius: '16px',
                    padding: '12px',
                    border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}20`, // SOMIPEM border with transparency
                    position: 'relative'
                  }}>                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '8px'
                    }}>                      <div style={{
                        width: '4px',
                        height: '18px',
                        background: `linear-gradient(to bottom, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE})`, // SOMIPEM gradient
                        borderRadius: '2px',
                        marginRight: '8px'
                      }}></div>
                      <h4 style={{ 
                        margin: 0, 
                        color: SOMIPEM_COLORS.PRIMARY_BLUE, // SOMIPEM Primary Blue
                        fontSize: '14px',
                        fontWeight: '600'
                      }}>
                        Durée des Temps d'Arrêt (min)
                      </h4>                    </div>                    <div style={{ height: 'calc(100% - 36px)' }}>
                      <DowntimeDurationChart data={downtimeParetoData} loading={loading} />
                    </div>
                  </div>

                  {/* Cumulative Impact Subchart */}                  <div style={{ 
                    background: `linear-gradient(145deg, ${SOMIPEM_COLORS.SECONDARY_BLUE}10, ${SOMIPEM_COLORS.PRIMARY_BLUE}10)`, // SOMIPEM light gradient
                    borderRadius: '16px',
                    padding: '12px',
                    border: `1px solid ${SOMIPEM_COLORS.SECONDARY_BLUE}20`, // SOMIPEM border with transparency
                    position: 'relative'
                  }}>                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '8px'
                    }}>                      <div style={{
                        width: '4px',
                        height: '24px',
                        background: `linear-gradient(to bottom, ${SOMIPEM_COLORS.SECONDARY_BLUE}, ${SOMIPEM_COLORS.PRIMARY_BLUE})`, // SOMIPEM gradient
                        borderRadius: '2px',
                        marginRight: '10px'
                      }}></div>
                      <h4 style={{ 
                        margin: 0, 
                        color: SOMIPEM_COLORS.SECONDARY_BLUE, // SOMIPEM Secondary Blue
                        fontSize: '16px',
                        fontWeight: '600'
                      }}>
                        Impact Cumulé (Pareto %)
                      </h4>                    </div>                    <div style={{ height: 'calc(100% - 36px)' }}>
                      <CumulativeImpactChart data={downtimeParetoData} loading={loading} />
                    </div>
                  </div>
                </div>
              ) : (                <div style={{ 
                  height: '600px', 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  background: `linear-gradient(145deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}10, ${SOMIPEM_COLORS.SECONDARY_BLUE}10)`, // SOMIPEM light gradient
                  borderRadius: '16px'
                }}>
                  <Empty 
                    description="Aucune donnée de temps d'arrêt disponible" 
                    style={{ color: '#8c8c8c' }}
                  />
                </div>
              )}
            </div>          </div>
        </div>
        ) : (
          /* Machine Selection Required Message */
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '80px 40px',
            marginBottom: '50px'
          }}>
            <div style={{
              background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
              borderRadius: '24px',
              padding: '60px 80px',
              boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)',
              border: '2px dashed #d9d9d9',
              textAlign: 'center',
              maxWidth: '600px',
              width: '100%'
            }}>
              <div style={{
                fontSize: '64px',
                marginBottom: '24px',
                opacity: 0.6
              }}>
                🏭
              </div>
              <h3 style={{
                margin: '0 0 16px 0',
                fontSize: '24px',
                fontWeight: '600',
                color: '#595959'
              }}>
                Sélectionnez une Machine
              </h3>
              <p style={{
                margin: 0,
                fontSize: '16px',
                color: '#8c8c8c',
                lineHeight: '1.6'
              }}>
                Pour afficher les analyses avancées de disponibilité, MTTR et métriques de performance,
                <br />
                veuillez sélectionner une machine spécifique dans les filtres ci-dessus.
              </p>
            </div>
          </div>
        )}

        {/* Supporting Analytics Grid */}
        {selectedMachine ? (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(700px, 1fr))',
          gap: '40px'
        }}>{/* Performance Metrics Card */}
          <div style={{
            background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
            borderRadius: '24px',
            padding: '0',
            boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)',
            border: '1px solid rgba(255,255,255,0.2)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* Card Header */}
            <div style={{
              background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE})`, // SOMIPEM gradient
              padding: '24px 32px',
              color: 'white',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '-50px',
                right: '-50px',
                width: '150px',
                height: '150px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}></div>
              
              <div style={{ position: 'relative', zIndex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    width: '56px',
                    height: '56px',
                    borderRadius: '16px',
                    background: 'rgba(255,255,255,0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '20px',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <span style={{ fontSize: '24px' }}>🎯</span>
                  </div>
                  <div>
                    <h3 style={{ 
                      margin: 0, 
                      fontSize: '24px', 
                      fontWeight: '700', 
                      color: 'white'
                    }}>
                      Métriques de Performance
                    </h3>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      color: 'rgba(255,255,255,0.9)', 
                      fontSize: '16px',
                      fontWeight: '300'
                    }}>
                      Indicateurs clés temps réel
                    </p>
                  </div>
                </div>
              </div>
            </div>            {/* Card Content */}
            <div style={{ 
              padding: '32px',
              height: '500px'
            }}>
              <div style={{ 
                height: '100%',
                background: `linear-gradient(145deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}10, ${SOMIPEM_COLORS.SECONDARY_BLUE}10)`, // SOMIPEM light gradient
                borderRadius: '16px',
                padding: '20px',
                border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}20` // SOMIPEM border with transparency
              }}>
                <ArretPerformanceGauge 
                  mttr={mttr} 
                  mtbf={mtbf} 
                  doper={doper} 
                  loading={loading} 
                />
              </div>
            </div>
          </div>          {/* MTTR Calendar Card */}
          <div style={{
            background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
            borderRadius: '24px',
            padding: '0',
            boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)',
            border: '1px solid rgba(255,255,255,0.2)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* Card Header */}
            <div style={{
              background: `linear-gradient(135deg, ${SOMIPEM_COLORS.SECONDARY_BLUE}, ${SOMIPEM_COLORS.PRIMARY_BLUE})`, // SOMIPEM gradient
              padding: '24px 32px',
              color: 'white',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '-50px',
                right: '-50px',
                width: '150px',
                height: '150px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '50%',
                filter: 'blur(20px)'
              }}></div>
              
              <div style={{ position: 'relative', zIndex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    width: '56px',
                    height: '56px',
                    borderRadius: '16px',
                    background: 'rgba(255,255,255,0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '20px',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <span style={{ fontSize: '24px' }}>📅</span>
                  </div>
                  <div>
                    <h3 style={{ 
                      margin: 0, 
                      fontSize: '24px', 
                      fontWeight: '700', 
                      color: 'white'
                    }}>
                      Calendrier MTTR
                    </h3>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      color: 'rgba(255,255,255,0.9)', 
                      fontSize: '16px',
                      fontWeight: '300'
                    }}>
                      Vue mensuelle des réparations
                    </p>
                  </div>
                </div>
              </div>
            </div>            {/* Card Content */}
            <div style={{ 
              padding: '24px',
              height: '650px'  // Increased from 500px for better visibility
            }}>              <div style={{ 
                height: '100%',
                background: 'linear-gradient(145deg, #e6fffb, #e0f7fa)',
                borderRadius: '16px',
                padding: '20px',  // Increased padding
                border: '1px solid rgba(19, 194, 194, 0.1)',
                overflow: 'hidden'
              }}>
                <MttrHeatCalendar 
                  data={mttrCalendarData} 
                  loading={loading} 
                  selectedDate={selectedDate}
                  selectedMachine={selectedMachine}
                  dateRangeType={dateRangeType}                />
              </div>
            </div>
          </div>
        </div>
        ) : (
          /* Machine Selection Required Message for Supporting Analytics */
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '40px'
          }}>
            <div style={{
              background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
              borderRadius: '16px',
              padding: '40px 60px',
              boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
              border: '1px dashed #d9d9d9',
              textAlign: 'center',
              maxWidth: '500px'
            }}>
              <div style={{
                fontSize: '48px',
                marginBottom: '16px',
                opacity: 0.5
              }}>
                📊
              </div>
              <h4 style={{
                margin: '0 0 12px 0',
                fontSize: '18px',
                fontWeight: '600',
                color: '#595959'
              }}>
                Métriques de Performance Indisponibles
              </h4>
              <p style={{
                margin: 0,
                fontSize: '14px',
                color: '#8c8c8c',
                lineHeight: '1.5'
              }}>
                Les métriques de performance et le calendrier MTTR nécessitent la sélection d'une machine.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArretChartsSection;
