const s=(e,t=null,r=!1)=>{if(e==null||e==="")return"0";const o=typeof e=="string"?parseFloat(e):e;if(isNaN(o))return"0";let n=t;n===null&&(Number.isInteger(o)?n=0:n=o<10?2:1);const f=(Math.round(o*Math.pow(10,n))/Math.pow(10,n)).toFixed(n).split(".");let i=f[0];const a=f[1];if(i=i.replace(/\B(?=(\d{3})+(?!\d))/g,"."),n>0&&(r||a!=="00")){const l=r?a:a.replace(/0+$/,"");if(l.length>0)return`${i},${l}`}return i},u=e=>s(e,0,!1),m=(e,t=1)=>s(e,t,!1),h=(e,t=2)=>s(e,t,!1),d=e=>{const t=typeof e=="string"?parseFloat(e):e;if(isNaN(t)||t===0)return"0";const r=Number.isInteger(t)?0:1;return s(e,r,!1)},c=(e,t="h")=>{const r=typeof e=="string"?parseFloat(e):e;return isNaN(r)?"0":s(e,2,!1)},g=(e,t="")=>{const r=t.toLowerCase();return r==="%"?m(e,1):r==="kg"?d(e):r==="pcs"||r==="pieces"?u(e):r==="h"||r==="hours"||r==="min"||r==="minutes"||r==="sec"||r==="seconds"?c(e):s(e)};export{u as a,h as b,g as c,c as d,s as e,m as f};
