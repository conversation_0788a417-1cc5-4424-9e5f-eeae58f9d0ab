#!/usr/bin/env node

/**
 * Filter Functionality Test
 * Tests the Production Dashboard filter behavior
 */

const SERVER_URL = process.env.SERVER_URL || 'http://localhost:5000';

async function testFilters() {
  console.log('🔍 FILTER FUNCTIONALITY TEST');
  console.log('============================\n');

  try {
    // Test 1: Default state (no filters)
    console.log('📊 TEST 1: Default state (no filters)');
    const defaultQuery = `
      query GetAllDailyProduction {
        getAllDailyProduction {
          success
          data {
            Machine_Name
            Date_Insert_Day
            OEE_Day
            Good_QTY_Day
            Shift
          }
          dataSource
        }
      }
    `;

    const defaultResponse = await fetch(`${SERVER_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: defaultQuery })
    });

    const defaultResult = await defaultResponse.json();
    console.log(`✅ Default query returned ${defaultResult.data.getAllDailyProduction.data.length} records`);
    console.log(`📦 Data source: ${defaultResult.data.getAllDailyProduction.dataSource}`);
    
    // Show sample data
    const sampleDefault = defaultResult.data.getAllDailyProduction.data.slice(0, 3);
    console.log('📋 Sample data (first 3 records):');
    sampleDefault.forEach((record, index) => {
      console.log(`   ${index + 1}. Machine: ${record.Machine_Name}, Date: ${record.Date_Insert_Day}, OEE: ${record.OEE_Day}, Shift: ${record.Shift}`);
    });
    console.log('');

    // Test 2: Machine model filter (IPS)
    console.log('📊 TEST 2: Machine model filter (IPS)');
    const modelFilterQuery = `
      query GetAllDailyProduction($filters: EnhancedFilterInput) {
        getAllDailyProduction(filters: $filters) {
          success
          data {
            Machine_Name
            Date_Insert_Day
            OEE_Day
            Good_QTY_Day
            Shift
          }
          dataSource
        }
      }
    `;

    const modelFilterResponse = await fetch(`${SERVER_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: modelFilterQuery,
        variables: {
          filters: {
            model: "IPS",
            dateRangeType: "day"
          }
        }
      })
    });

    const modelFilterResult = await modelFilterResponse.json();
    console.log(`✅ Model filter (IPS) returned ${modelFilterResult.data.getAllDailyProduction.data.length} records`);
    console.log(`📦 Data source: ${modelFilterResult.data.getAllDailyProduction.dataSource}`);
    
    // Check if all results are IPS machines
    const ipsRecords = modelFilterResult.data.getAllDailyProduction.data.filter(r => r.Machine_Name.startsWith('IPS'));
    console.log(`🔍 IPS machines in results: ${ipsRecords.length}/${modelFilterResult.data.getAllDailyProduction.data.length} (${ipsRecords.length === modelFilterResult.data.getAllDailyProduction.data.length ? 'PASS' : 'FAIL'})`);
    console.log('');

    // Test 3: Specific machine filter (IPS01)
    console.log('📊 TEST 3: Specific machine filter (IPS01)');
    const machineFilterResponse = await fetch(`${SERVER_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: modelFilterQuery,
        variables: {
          filters: {
            model: "IPS",
            machine: "IPS01",
            dateRangeType: "day"
          }
        }
      })
    });

    const machineFilterResult = await machineFilterResponse.json();
    console.log(`✅ Machine filter (IPS01) returned ${machineFilterResult.data.getAllDailyProduction.data.length} records`);
    console.log(`📦 Data source: ${machineFilterResult.data.getAllDailyProduction.dataSource}`);
    
    // Check if all results are IPS01
    const ips01Records = machineFilterResult.data.getAllDailyProduction.data.filter(r => r.Machine_Name === 'IPS01');
    console.log(`🔍 IPS01 machines in results: ${ips01Records.length}/${machineFilterResult.data.getAllDailyProduction.data.length} (${ips01Records.length === machineFilterResult.data.getAllDailyProduction.data.length ? 'PASS' : 'FAIL'})`);
    console.log('');

    // Test 4: Machine Performance GraphQL (this is what the charts use)
    console.log('📊 TEST 4: Machine Performance GraphQL (chart data)');
    const performanceQuery = `
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          success
          data {
            Machine_Name
            production
            rejects
            trs
            availability
            performance
            quality
          }
          dataSource
        }
      }
    `;

    const performanceResponse = await fetch(`${SERVER_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: performanceQuery,
        variables: {
          filters: {
            model: "IPS",
            dateRangeType: "day"
          }
        }
      })
    });

    const performanceResult = await performanceResponse.json();
    console.log(`✅ Machine Performance returned ${performanceResult.data.enhancedGetMachinePerformance.data.length} machines`);
    console.log(`📦 Data source: ${performanceResult.data.enhancedGetMachinePerformance.dataSource}`);
    
    // Show the machine performance data
    console.log('📋 Machine Performance data:');
    performanceResult.data.enhancedGetMachinePerformance.data.forEach((machine, index) => {
      console.log(`   ${index + 1}. ${machine.Machine_Name}: Production=${machine.production}, TRS=${machine.trs}%, Availability=${machine.availability}%`);
    });
    console.log('');

    // Test 5: Test with Date Filter
    console.log('📊 TEST 5: Date filter test');
    const dateFilterResponse = await fetch(`${SERVER_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: modelFilterQuery,
        variables: {
          filters: {
            model: "IPS",
            date: "2024-10-31",
            dateRangeType: "day"
          }
        }
      })
    });

    const dateFilterResult = await dateFilterResponse.json();
    console.log(`✅ Date filter (2024-10-31) returned ${dateFilterResult.data.getAllDailyProduction.data.length} records`);
    console.log(`📦 Data source: ${dateFilterResult.data.getAllDailyProduction.dataSource}`);

    // Check date filtering
    const correctDateRecords = dateFilterResult.data.getAllDailyProduction.data.filter(r => 
      r.Date_Insert_Day && r.Date_Insert_Day.includes('31/10/2024')
    );
    console.log(`🔍 Records with correct date: ${correctDateRecords.length}/${dateFilterResult.data.getAllDailyProduction.data.length}`);
    console.log('');

    console.log('🏁 FILTER TEST COMPLETED');
    console.log('========================');
    console.log('📊 Summary:');
    console.log(`   • Default query: ${defaultResult.data.getAllDailyProduction.data.length} records`);
    console.log(`   • Model filter (IPS): ${modelFilterResult.data.getAllDailyProduction.data.length} records`);
    console.log(`   • Machine filter (IPS01): ${machineFilterResult.data.getAllDailyProduction.data.length} records`);
    console.log(`   • Machine performance: ${performanceResult.data.enhancedGetMachinePerformance.data.length} machines`);
    console.log(`   • Date filter: ${dateFilterResult.data.getAllDailyProduction.data.length} records`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testFilters();
