import React, { useState } from 'react';
import { Card, Row, Col, Tabs, Space, Badge, Button, Tooltip, Empty } from 'antd';
import { 
  RobotOutlined,
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined,
  Pie<PERSON><PERSON>Outlined,
  <PERSON>boltOutlined,
  ExperimentOutlined,
  EyeOutlined,
  SettingOutlined,
  FullscreenOutlined,
  DownloadOutlined
} from '@ant-design/icons';

// Import sub-components (will create these next)
import AIPartPerformanceCard from '../cards/AIPartPerformanceCard';
import ProductionOptimizationCard from '../cards/ProductionOptimizationCard';
import CapacityPlanningCard from '../cards/CapacityPlanningCard';
import EfficiencyTrendsCard from '../cards/EfficiencyTrendsCard';
import BottleneckAnalysisCard from '../cards/BottleneckAnalysisCard';
import PredictiveMaintenanceCard from '../cards/PredictiveMaintenanceCard';
import CostOptimizationCard from '../cards/CostOptimizationCard';
import YieldOptimizationCard from '../cards/YieldOptimizationCard';

const ProductionIntelligenceSection = ({ loading, filters }) => {
  const [activeSubTab, setActiveSubTab] = useState("overview");

  const overviewCards = [
    {
      key: "ai-part-performance",
      title: "AI Part Performance Analyzer",
      description: "Machine learning models analyzing part production patterns",
      component: <AIPartPerformanceCard loading={loading} filters={filters} />,
      icon: <RobotOutlined />,
      badge: "ML",
      color: "#1890ff"
    },
    {
      key: "production-optimization",
      title: "Production Optimization Engine",
      description: "Real-time optimization recommendations",
      component: <ProductionOptimizationCard loading={loading} filters={filters} />,
      icon: <ThunderboltOutlined />,
      badge: "AI",
      color: "#52c41a"
    },
    {
      key: "capacity-planning",
      title: "Intelligent Capacity Planning",
      description: "AI-driven capacity forecasting and planning",
      component: <CapacityPlanningCard loading={loading} filters={filters} />,
      icon: <BarChartOutlined />,
      badge: "Predictive",
      color: "#722ed1"
    },
    {
      key: "efficiency-trends",
      title: "Efficiency Trend Analysis",
      description: "Deep learning trend analysis and forecasting",
      component: <EfficiencyTrendsCard loading={loading} filters={filters} />,
      icon: <LineChartOutlined />,
      badge: "Trend",
      color: "#fa8c16"
    }
  ];

  const advancedCards = [
    {
      key: "bottleneck-analysis",
      title: "AI Bottleneck Detection",
      description: "Automated bottleneck identification and resolution",
      component: <BottleneckAnalysisCard loading={loading} filters={filters} />,
      icon: <ExperimentOutlined />,
      badge: "Auto",
      color: "#eb2f96"
    },
    {
      key: "predictive-maintenance",
      title: "Predictive Maintenance AI",
      description: "Machine failure prediction and prevention",
      component: <PredictiveMaintenanceCard loading={loading} filters={filters} />,
      icon: <SettingOutlined />,
      badge: "Predict",
      color: "#13c2c2"
    },
    {
      key: "cost-optimization",
      title: "Cost Optimization Engine",
      description: "AI-powered cost reduction strategies",
      component: <CostOptimizationCard loading={loading} filters={filters} />,
      icon: <PieChartOutlined />,
      badge: "$$",
      color: "#f5222d"
    },
    {
      key: "yield-optimization",
      title: "Yield Optimization AI",
      description: "Maximize production yield through AI insights",
      component: <YieldOptimizationCard loading={loading} filters={filters} />,
      icon: <EyeOutlined />,
      badge: "Yield",
      color: "#a0d911"
    }
  ];

  const subTabs = [
    {
      key: "overview",
      label: (
        <Space>
          <BarChartOutlined />
          <span>Overview Analytics</span>
          <Badge count={overviewCards.length} style={{ backgroundColor: '#1890ff' }} />
        </Space>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {overviewCards.map((card) => (
            <Col xs={24} lg={12} key={card.key}>
              <Card
                title={
                  <Space>
                    <div style={{
                      background: `linear-gradient(135deg, ${card.color}, ${card.color}80)`,
                      borderRadius: '8px',
                      padding: '6px',
                      color: 'white'
                    }}>
                      {card.icon}
                    </div>
                    <div>
                      <div style={{ fontWeight: '600' }}>{card.title}</div>
                      <div style={{ fontSize: '12px', color: '#8c8c8c', fontWeight: 'normal' }}>
                        {card.description}
                      </div>
                    </div>
                  </Space>
                }
                extra={
                  <Space>
                    <Badge count={card.badge} style={{ backgroundColor: card.color }} />
                    <Tooltip title="Expand">
                      <Button type="text" icon={<FullscreenOutlined />} size="small" />
                    </Tooltip>
                    <Tooltip title="Export Data">
                      <Button type="text" icon={<DownloadOutlined />} size="small" />
                    </Tooltip>
                  </Space>
                }
                style={{
                  borderRadius: '16px',
                  border: 'none',
                  boxShadow: '0 8px 24px rgba(0,0,0,0.06)'
                }}
                bodyStyle={{ padding: '20px' }}
              >
                {card.component}
              </Card>
            </Col>
          ))}
        </Row>
      )
    },
    {
      key: "advanced",
      label: (
        <Space>
          <RobotOutlined />
          <span>Advanced AI</span>
          <Badge count={advancedCards.length} style={{ backgroundColor: '#722ed1' }} />
        </Space>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {advancedCards.map((card) => (
            <Col xs={24} lg={12} key={card.key}>
              <Card
                title={
                  <Space>
                    <div style={{
                      background: `linear-gradient(135deg, ${card.color}, ${card.color}80)`,
                      borderRadius: '8px',
                      padding: '6px',
                      color: 'white'
                    }}>
                      {card.icon}
                    </div>
                    <div>
                      <div style={{ fontWeight: '600' }}>{card.title}</div>
                      <div style={{ fontSize: '12px', color: '#8c8c8c', fontWeight: 'normal' }}>
                        {card.description}
                      </div>
                    </div>
                  </Space>
                }
                extra={
                  <Space>
                    <Badge count={card.badge} style={{ backgroundColor: card.color }} />
                    <Tooltip title="Expand">
                      <Button type="text" icon={<FullscreenOutlined />} size="small" />
                    </Tooltip>
                    <Tooltip title="Export Data">
                      <Button type="text" icon={<DownloadOutlined />} size="small" />
                    </Tooltip>
                  </Space>
                }
                style={{
                  borderRadius: '16px',
                  border: 'none',
                  boxShadow: '0 8px 24px rgba(0,0,0,0.06)'
                }}
                bodyStyle={{ padding: '20px' }}
              >
                {card.component}
              </Card>
            </Col>
          ))}
        </Row>
      )
    }
  ];

  return (
    <div style={{
      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px'
    }}>
      {/* Section Header */}
      <div style={{
        background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '24px',
        color: 'white'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space align="center" size="large">
              <div style={{
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '10px',
                padding: '10px'
              }}>
                <RobotOutlined style={{ fontSize: '24px' }} />
              </div>
              <div>
                <h2 style={{ color: 'white', margin: 0, fontSize: '24px' }}>
                  Production Intelligence
                </h2>
                <p style={{ color: 'rgba(255,255,255,0.9)', margin: 0, fontSize: '14px' }}>
                  AI-powered production analytics and optimization
                </p>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Badge count="8 AI Models" style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
              <Badge count="Real-time" style={{ backgroundColor: '#52c41a' }} />
              <Badge count="ML Enabled" style={{ backgroundColor: '#722ed1' }} />
            </Space>
          </Col>
        </Row>
      </div>

      {/* Sub-tabs */}
      <Tabs
        activeKey={activeSubTab}
        onChange={setActiveSubTab}
        items={subTabs}
        type="card"
        tabBarStyle={{
          background: 'white',
          borderRadius: '12px',
          padding: '8px',
          marginBottom: '20px',
          border: 'none',
          boxShadow: '0 4px 16px rgba(0,0,0,0.06)'
        }}
      />
    </div>
  );
};

export default ProductionIntelligenceSection;
