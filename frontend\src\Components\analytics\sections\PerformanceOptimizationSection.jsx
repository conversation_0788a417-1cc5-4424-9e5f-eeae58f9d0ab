import React from 'react';
import { Card, Space } from 'antd';
import { RocketOutlined } from '@ant-design/icons';

const PerformanceOptimizationSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <RocketOutlined style={{ fontSize: '64px', color: '#f5222d' }} />
          <h2 style={{ color: '#f5222d', margin: 0 }}>Performance Optimization</h2>
          <p style={{ color: '#8c8c8c' }}>Automated performance enhancement and optimization coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default PerformanceOptimizationSection;
