#!/bin/bash

# Build and Push LOCQL Unified Container to Docker Hub
# This script builds the production-ready unified container and pushes it to Docker Hub

set -e  # Exit on any error

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-your-dockerhub-username}"
IMAGE_NAME="locql-unified"
VERSION="${VERSION:-1.0.0}"
LATEST_TAG="latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    print_success "Docker is running"
}

# Check if user is logged into Docker Hub
check_docker_login() {
    print_status "Checking Docker Hub authentication..."
    if ! docker info | grep -q "Username"; then
        print_warning "Not logged into Docker Hub. Please run: docker login"
        read -p "Do you want to login now? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker login
        else
            print_error "Docker Hub login required to push images"
            exit 1
        fi
    fi
    print_success "Docker Hub authentication verified"
}

# Build the Docker image
build_image() {
    print_status "Building LOCQL Unified Container..."
    print_status "Image: ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    # Build with version tag
    docker build \
        -f Dockerfile.production \
        -t "${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}" \
        -t "${DOCKER_USERNAME}/${IMAGE_NAME}:${LATEST_TAG}" \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VERSION="${VERSION}" \
        .
    
    print_success "Docker image built successfully"
}

# Test database connectivity
test_database_connectivity() {
    if [[ "$SKIP_DB_TEST" == true ]]; then
        print_warning "Skipping database connectivity test as requested"
        return 0
    fi


    local db_host="localhost"  # For host testing, use localhost
    local db_user="root"
    local db_pass="root"
    local db_name="Testingarea51"
    local db_port="3306"

    print_status "Testing connection to MySQL database:"
    print_status "  Host: $db_host"
    print_status "  Port: $db_port"
    print_status "  User: $db_user"
    print_status "  Database: $db_name"

    # Test if MySQL service is running
    if ! nc -z "$db_host" "$db_port" 2>/dev/null; then
        print_error "MySQL server is not running on ${db_host}:${db_port}"
        print_status "Please start MySQL server before running the test"
        return 1
    fi
    print_success "MySQL server is running on ${db_host}:${db_port}"

    # Test MySQL connection with credentials if mysql client is available
    if command -v mysql &> /dev/null; then
        print_status "Testing MySQL authentication..."
        if mysql -h "$db_host" -P "$db_port" -u "$db_user" -p"$db_pass" -e "SELECT 1;" &> /dev/null; then
            print_success "MySQL authentication successful"

            # Test if database exists
            print_status "Checking if database '$db_name' exists..."
            if mysql -h "$db_host" -P "$db_port" -u "$db_user" -p"$db_pass" -e "SHOW DATABASES LIKE '$db_name';" 2>/dev/null | grep -q "$db_name"; then
                print_success "Database '$db_name' exists and is accessible"
                return 0
            else
                print_warning "Database '$db_name' does not exist"
                print_status "Creating database '$db_name'..."
                if mysql -h "$db_host" -P "$db_port" -u "$db_user" -p"$db_pass" -e "CREATE DATABASE IF NOT EXISTS \`$db_name\`;" &> /dev/null; then
                    print_success "Database '$db_name' created successfully"
                    return 0
                else
                    print_error "Failed to create database '$db_name'"
                    return 1
                fi
            fi
        else
            print_error "MySQL authentication failed with user '$db_user'"
            print_status "Please verify MySQL credentials: user='$db_user', password='$db_pass'"
            return 1
        fi
    else
        print_warning "MySQL client not found - cannot test database connectivity"
        print_status "The container test will proceed, but may fail if database is not accessible"
        return 0
    fi
}

# Test the built image
test_image() {
    print_status "Testing the built image with working ngrok database configuration..."

    # Database configuration matching ngrok.env and config.env
    local db_host="host.docker.internal"  # For Docker container to host communication
    local db_user="root"
    local db_pass="root"  # Note: Using DB_PASS not DB_PASSWORD
    local db_name="Testingarea51"  # Correct database name from working config
    local jwt_secret="dadfba2957b57678f0bdf5bb2f27b1938b9a0b472cd95a49a7f01397af5db005b9072f3d5b0a42268571b735187e2a7aedc532ae79797206fe5789718711d719"

    print_status "Container will use database configuration:"
    print_status "  DB_HOST: $db_host"
    print_status "  DB_USER: $db_user"
    print_status "  DB_NAME: $db_name"
    print_status "  DB_PASS: [REDACTED]"

    # Run container with correct environment variables matching working Pomerium setup
    CONTAINER_ID=$(docker run -d -p 5001:5000 \
        -e NODE_ENV=production \
        -e DB_HOST="$db_host" \
        -e DB_USER="$db_user" \
        -e DB_PASS="$db_pass" \
        -e DB_NAME="$db_name" \
        -e JWT_SECRET="$jwt_secret" \
        -e JWT_EXPIRE=8h \
        -e JWT_COOKIE_EXPIRE=1 \
        -e DISABLE_CACHE=false \
        -e DEBUG=true \
        -e LOG_LEVEL=debug \
        -e TRUST_PROXY=true \
        "${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}")

    if [ $? -ne 0 ]; then
        print_error "Failed to start test container"
        exit 1
    fi

    print_status "Container started with ID: ${CONTAINER_ID}"
    print_status "Waiting for container to initialize and connect to database..."

    # Wait longer for the container to start, initialize, and connect to database
    sleep 20

    # Check if the container is still running
    if docker ps | grep -q "${CONTAINER_ID}"; then
        print_success "Container started successfully"

        # Show container logs for debugging
        print_status "Container logs (last 10 lines):"
        docker logs --tail 10 "${CONTAINER_ID}"

        # Test health endpoint with retries
        print_status "Testing health endpoint..."
        for i in {1..5}; do
            if curl -f http://localhost:5001/health &> /dev/null; then
                print_success "Health check passed on attempt $i"
                break
            else
                print_warning "Health check attempt $i failed, retrying in 5 seconds..."
                sleep 5
            fi

            if [ $i -eq 5 ]; then
                print_warning "Health check failed after 5 attempts, but container is running"
                print_status "This may be due to database connectivity issues in the test environment"
                print_status "Container logs:"
                docker logs "${CONTAINER_ID}"
            fi
        done
    else
        print_error "Container failed to start or exited"
        print_status "Container logs:"
        docker logs "${CONTAINER_ID}"
        docker rm "${CONTAINER_ID}" &> /dev/null
        exit 1
    fi

    # Clean up test container
    print_status "Cleaning up test container..."
    docker stop "${CONTAINER_ID}" &> /dev/null
    docker rm "${CONTAINER_ID}" &> /dev/null
    print_success "Test completed successfully"
}

# Push the image to Docker Hub
push_image() {
    print_status "Pushing image to Docker Hub..."
    
    # Push version tag
    docker push "${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    print_success "Pushed ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    
    # Push latest tag
    docker push "${DOCKER_USERNAME}/${IMAGE_NAME}:${LATEST_TAG}"
    print_success "Pushed ${DOCKER_USERNAME}/${IMAGE_NAME}:${LATEST_TAG}"
}

# Display usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --username USERNAME    Docker Hub username (default: your-dockerhub-username)"
    echo "  -v, --version VERSION      Image version tag (default: 1.0.0)"
    echo "  -t, --test-only           Build and test only, don't push"
    echo "  --skip-db-test           Skip database connectivity tests"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DOCKER_USERNAME           Docker Hub username"
    echo "  VERSION                   Image version tag"
    echo ""
    echo "Examples:"
    echo "  $0 -u myusername -v 1.2.0"
    echo "  $0 --test-only"
    echo "  $0 --test-only --skip-db-test"
    echo "  DOCKER_USERNAME=myuser VERSION=2.0.0 $0"
}

# Parse command line arguments
TEST_ONLY=false
SKIP_DB_TEST=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--username)
            DOCKER_USERNAME="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -t|--test-only)
            TEST_ONLY=true
            shift
            ;;
        --skip-db-test)
            SKIP_DB_TEST=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate Docker username
if [[ "$DOCKER_USERNAME" == "your-dockerhub-username" ]]; then
    print_error "Please set your Docker Hub username using -u flag or DOCKER_USERNAME environment variable"
    show_usage
    exit 1
fi

# Main execution
main() {
    print_status "Starting LOCQL Unified Container build process..."
    print_status "Docker Username: ${DOCKER_USERNAME}"
    print_status "Image Name: ${IMAGE_NAME}"
    print_status "Version: ${VERSION}"
    print_status "Test Only: ${TEST_ONLY}"
    print_status "Skip DB Test: ${SKIP_DB_TEST}"
    echo ""

    check_docker

    # Test database connectivity before building (unless skipped)
    if ! test_database_connectivity; then
        print_error "Database connectivity test failed"
        print_status "You can skip database tests with --skip-db-test parameter"
        print_status "Example: $0 -u ${DOCKER_USERNAME} --test-only --skip-db-test"
        exit 1
    fi

    if [[ "$TEST_ONLY" == false ]]; then
        check_docker_login
    fi

    build_image
    test_image
    
    if [[ "$TEST_ONLY" == false ]]; then
        push_image
        print_success "🎉 Image successfully pushed to Docker Hub!"
        echo ""
        print_status "You can now pull and run the image with:"
        echo "  docker pull ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
        echo "  docker run -p 5000:5000 ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    else
        print_success "🧪 Build and test completed successfully!"
        echo ""
        print_status "To push the image, run without --test-only flag"
    fi
}

# Run main function
main
