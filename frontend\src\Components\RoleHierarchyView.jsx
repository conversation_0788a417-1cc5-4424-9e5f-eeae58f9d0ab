import React, { useState, useEffect } from 'react';
import { Card, Tree, Table, Tag, Tabs, Tooltip, Typography, Spin, Alert } from 'antd';
import { useAuth } from '../hooks/useAuth';
import request from 'superagent';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * Component to visualize the role hierarchy and permissions
 */
const RoleHierarchyView = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [roleHierarchy, setRoleHierarchy] = useState({});
  const [permissions, setPermissions] = useState({});
  const [rolePermissions, setRolePermissions] = useState({});

  // Fetch role hierarchy data
  useEffect(() => {
    const fetchRoleHierarchy = async () => {
      try {
        setLoading(true);
        const response = await request.get('/api/role-hierarchy/hierarchy')
          .withCredentials() // ✅ FIXED: Correct SuperAgent syntax
          .timeout(30000)    // ✅ ADDED: Consistent timeout
          .retry(2);

        if (response.body.success) {
          setRoleHierarchy(response.body.data.hierarchy || {});
          setPermissions(response.body.data.permissions || {});
          setRolePermissions(response.body.data.rolePermissions || {});
        } else {
          setError(response.body.message || 'Failed to load role hierarchy');
        }
      } catch (error) {
        setError(error.response?.body?.message || 'Error loading role hierarchy');
        console.error('Error fetching role hierarchy:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRoleHierarchy();
  }, []);

  // Convert role hierarchy to Tree data structure
  const buildTreeData = () => {
    // Clear visited roles for each build
    const visitedRoles = new Set();

    // Find the root nodes (highest level roles)
    // In this case, we're looking for admin (level 100)
    const rootNodes = Object.entries(roleHierarchy)
      .filter(([, role]) => role.level === 100)
      .map(([roleName]) => ({
        title: formatRoleName(roleName),
        key: `role-${roleName}`, // ✅ FIXED: Add prefix to ensure unique keys
        roleName: roleName, // Store original role name for reference
        children: buildChildren(roleName, visitedRoles)
      }));

    return rootNodes;
  };

  // Build children nodes recursively
  const buildChildren = (parentRoleName, visitedRoles) => {
    // Prevent infinite recursion
    if (visitedRoles.has(parentRoleName)) {
      return [];
    }

    // Mark this role as visited
    visitedRoles.add(parentRoleName);

    // Get the parent role's inherits array
    const parentRole = roleHierarchy[parentRoleName];
    if (!parentRole || !parentRole.inherits || parentRole.inherits.length === 0) {
      return [];
    }

    // Map the inherited roles to tree nodes
    const children = parentRole.inherits
      .filter(childRoleName =>
        // Make sure the child role exists and is not the parent itself
        roleHierarchy[childRoleName] && childRoleName !== parentRoleName && !visitedRoles.has(childRoleName)
      )
      .map((childRoleName, index) => ({
        title: formatRoleName(childRoleName),
        key: `role-${parentRoleName}-${childRoleName}-${index}`, // ✅ FIXED: Ensure unique keys with parent context
        roleName: childRoleName, // Store original role name for reference
        children: buildChildren(childRoleName, new Set(visitedRoles)) // Create new Set to avoid conflicts
      }));

    return children;
  };

  // Format role name for display
  const formatRoleName = (roleName) => {
    return roleName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get role level name for display
  const getRoleLevelName = (level) => {
    switch (level) {
      case 100:
        return 'Administrator';
      case 80:
        return 'Head Manager';
      case 60:
        return 'Department Manager';
      case 40:
        return 'Staff';
      case 10:
        return 'Base User';
      default:
        return `Level ${level}`;
    }
  };

  // Build permission table data
  const buildPermissionTableData = () => {
    const permissionData = [];
    let index = 0;

    Object.entries(permissions).forEach(([namespace, namespaceData]) => {
      Object.entries(namespaceData.permissions).forEach(([permName, description]) => {
        permissionData.push({
          key: `perm-${namespace}-${permName}-${index}`, // ✅ FIXED: Ensure unique keys with index
          namespace,
          permission: permName,
          fullPermission: `${namespace}:${permName}`,
          description,
          roles: Object.entries(rolePermissions)
            .filter(([, perms]) =>
              perms.includes(`${namespace}:${permName}`) ||
              perms.includes(`${namespace}:*`) ||
              perms.includes('system:admin')
            )
            .map(([roleName]) => roleName)
        });
        index++;
      });
    });

    return permissionData;
  };

  // Permission table columns
  const permissionColumns = [
    {
      title: 'Namespace',
      dataIndex: 'namespace',
      key: 'namespace',
      render: (namespace) => <Tag color="blue">{namespace}</Tag>,
      filters: Object.keys(permissions).map(ns => ({ text: ns, value: ns })),
      onFilter: (value, record) => record.namespace === value,
    },
    {
      title: 'Permission',
      dataIndex: 'permission',
      key: 'permission',
    },
    {
      title: 'Full Permission',
      dataIndex: 'fullPermission',
      key: 'fullPermission',
      render: (text) => <code>{text}</code>,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles, record) => (
        <>
          {roles.map((role, index) => (
            <Tag color="green" key={`${record.key}-role-${role}-${index}`}>
              {formatRoleName(role)}
            </Tag>
          ))}
        </>
      ),
      filters: Object.keys(roleHierarchy).map(role => ({ text: formatRoleName(role), value: role })),
      onFilter: (value, record) => record.roles.includes(value),
    },
  ];

  if (loading) {
    return <Spin tip="Loading role hierarchy..." />;
  }

  if (error) {
    return <Alert type="error" message="Error" description={error} />;
  }

  return (
    <Card title="Role Hierarchy and Permissions">
      <Tabs defaultActiveKey="hierarchy">
        <TabPane tab="Role Hierarchy" key="hierarchy">
          <Title level={4}>Role Hierarchy Visualization</Title>
          <Text type="secondary">
            This visualization shows the role hierarchy structure. Each role inherits permissions from the roles it connects to below it.
          </Text>
          <div style={{ marginTop: 20, background: '#f5f5f5', padding: 20, borderRadius: 5 }}>
            {/* Legend */}
            <div style={{ marginBottom: 15, padding: 10, background: '#fff', borderRadius: 4, border: '1px solid #eee' }}>
              <Text strong>Legend:</Text>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginTop: 5 }}>
                <Tag color="blue">Admin (Level 100)</Tag>
                <Tag color="cyan">Head Manager (Level 80)</Tag>
                <Tag color="green">Department Managers (Level 60)</Tag>
                <Tag color="orange">Staff (Level 40)</Tag>
                <Tag color="purple">Base User (Level 10)</Tag>
              </div>
            </div>

            <Tree
              showLine={{ showLeafIcon: false }}
              defaultExpandAll
              treeData={buildTreeData()}
              blockNode={true}
              style={{ fontSize: '14px' }}
              switcherIcon={<span style={{ color: '#1890ff' }}>▼</span>}
              titleRender={(nodeData) => {
                // Get the actual role name from the nodeData
                const actualRoleName = nodeData.roleName || nodeData.key.replace(/^role-/, '').split('-')[0];

                // Determine color based on role level
                let color = 'inherit';
                let fontWeight = 'normal';
                const roleData = roleHierarchy[actualRoleName];

                if (roleData) {
                  if (roleData.level === 100) {
                    color = '#1890ff'; // blue
                    fontWeight = 'bold';
                  } else if (roleData.level === 80) {
                    color = '#13c2c2'; // cyan
                  } else if (roleData.level === 60) {
                    color = '#52c41a'; // green
                  } else if (roleData.level === 40) {
                    color = '#fa8c16'; // orange
                  } else if (roleData.level === 10) {
                    color = '#722ed1'; // purple
                  }
                }

                // Create a more detailed tooltip
                const tooltipContent = roleData ? (
                  <div>
                    <p><strong>Role:</strong> {formatRoleName(actualRoleName)}</p>
                    <p><strong>Level:</strong> {roleData.level} ({getRoleLevelName(roleData.level)})</p>
                    <p><strong>Description:</strong> {roleData.description}</p>
                    <p><strong>Inherits from:</strong> {roleData.inherits && roleData.inherits.length > 0
                      ? roleData.inherits.map(r => formatRoleName(r)).join(', ')
                      : 'None'}</p>
                  </div>
                ) : nodeData.title;

                return (
                  <Tooltip title={tooltipContent} placement="right">
                    <div style={{
                      padding: '8px 0',
                      fontWeight: fontWeight,
                      color: color
                    }}>
                      {formatRoleName(actualRoleName)}
                    </div>
                  </Tooltip>
                );
              }}
            />
          </div>
        </TabPane>
        <TabPane tab="Permissions" key="permissions">
          <Title level={4}>Permission List</Title>
          <Text type="secondary">
            This table shows all available permissions and which roles have access to them.
          </Text>
          <Table
            columns={permissionColumns}
            dataSource={buildPermissionTableData()}
            style={{ marginTop: 20 }}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default RoleHierarchyView;
