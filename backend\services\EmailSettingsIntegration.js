import { executeQuery } from '../utils/dbUtils.js';
import { logger } from '../utils/logger.js';

/**
 * Email Settings Integration Service
 * Bridges the gap between UI settings and existing email services
 * Makes all 17+ email settings functional with immediate effects
 */
class EmailSettingsIntegration {
  constructor() {
    this.settingsCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get user email settings with caching
   */
  async getUserEmailSettings(userId = 1) {
    try {
      const cacheKey = `email_settings_${userId}`;
      const cached = this.settingsCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        return cached.settings;
      }

      const query = `
        SELECT settings 
        FROM user_settings 
        WHERE user_id = ? 
        LIMIT 1
      `;
      
      const result = await executeQuery(query, [userId]);
      
      if (result.length > 0) {
        const allSettings = JSON.parse(result[0].settings);
        const emailSettings = allSettings.email || this.getDefaultEmailSettings();
        
        // Cache the settings
        this.settingsCache.set(cacheKey, {
          settings: emailSettings,
          timestamp: Date.now()
        });
        
        return emailSettings;
      }
      
      return this.getDefaultEmailSettings();
      
    } catch (error) {
      logger.error('Failed to get user email settings:', error);
      return this.getDefaultEmailSettings();
    }
  }

  /**
   * Get default email settings matching UI structure
   */
  getDefaultEmailSettings() {
    return {
      // Basic Settings
      enabled: false,
      frequency: 'immediate', // immediate, hourly, daily
      template: 'standard', // minimal, standard, detailed
      
      // Advanced Settings
      format: 'html', // html, text, both
      language: 'fr', // fr, en, es
      attachments: {
        enabled: false
      },
      
      // Signature Settings
      signature: {
        enabled: false,
        text: 'SOMIPEM Production System'
      },
      
      // Filtering & Rules
      filtering: {
        minPriority: 'medium', // low, medium, high, critical
        maxPerDay: 50,
        preventDuplicates: true,
        smartGrouping: false
      },
      
      // Delivery Options
      delivery: {
        method: 'immediate', // immediate, scheduled, batch
        retryFailed: true,
        readReceipts: false,
        tracking: false
      },
      
      // Batch Settings
      batchSettings: {
        hourlyBatch: {
          enabled: false,
          maxNotifications: 20
        },
        dailyDigest: {
          enabled: false,
          digestTime: '08:00'
        }
      }
    };
  }

  /**
   * Check if email should be sent based on settings
   */
  async shouldSendEmail(notification, userId = 1) {
    const settings = await this.getUserEmailSettings(userId);
    
    // Check if email notifications are enabled
    if (!settings.enabled) {
      return { shouldSend: false, reason: 'Email notifications disabled' };
    }

    // Check priority filtering
    if (!this.checkPriorityFilter(notification.priority, settings.filtering.minPriority)) {
      return { shouldSend: false, reason: 'Priority below threshold' };
    }

    // Check daily limit
    if (!(await this.checkDailyLimit(settings.filtering.maxPerDay, userId))) {
      return { shouldSend: false, reason: 'Daily email limit reached' };
    }

    // Check for duplicates
    if (settings.filtering.preventDuplicates && (await this.isDuplicate(notification, userId))) {
      return { shouldSend: false, reason: 'Duplicate notification prevented' };
    }

    return { shouldSend: true, settings };
  }

  /**
   * Get email configuration for existing services
   */
  async getEmailConfiguration(notification, userId = 1) {
    const settings = await this.getUserEmailSettings(userId);
    
    return {
      // Template configuration
      template: settings.template || 'standard',
      format: settings.format || 'html',
      language: settings.language || 'fr',
      
      // Signature configuration
      includeSignature: settings.signature?.enabled || false,
      signatureText: settings.signature?.text || 'SOMIPEM Production System',
      
      // Delivery configuration
      deliveryMethod: settings.delivery?.method || 'immediate',
      retryOnFailure: settings.delivery?.retryFailed !== false,
      requestReadReceipts: settings.delivery?.readReceipts || false,
      enableTracking: settings.delivery?.tracking || false,
      
      // Batch configuration
      batchEnabled: settings.batchSettings?.hourlyBatch?.enabled || false,
      batchMaxNotifications: settings.batchSettings?.hourlyBatch?.maxNotifications || 20,
      digestEnabled: settings.batchSettings?.dailyDigest?.enabled || false,
      digestTime: settings.batchSettings?.dailyDigest?.digestTime || '08:00'
    };
  }

  /**
   * Apply settings to email content
   */
  async applySettingsToEmailContent(baseContent, notification, userId = 1) {
    const settings = await this.getUserEmailSettings(userId);
    const config = await this.getEmailConfiguration(notification, userId);
    
    let { subject, html, text } = baseContent;
    
    // Apply language settings
    if (config.language === 'en') {
      subject = subject.replace('[SOMIPEM]', '[SOMIPEM]').replace('ALERTE', 'ALERT').replace('Rapport', 'Report');
    }
    
    // Apply template settings
    if (config.template === 'minimal') {
      html = this.generateMinimalTemplate(notification, config.language);
      text = notification.message;
    } else if (config.template === 'detailed') {
      html = this.enhanceWithDetailedTemplate(html, notification);
    }
    
    // Apply signature settings
    if (config.includeSignature) {
      const signature = this.generateSignature(config.signatureText, config.format);
      html += signature.html;
      text += signature.text;
    }
    
    // Apply format settings
    const finalContent = {};
    if (config.format === 'html' || config.format === 'both') {
      finalContent.html = html;
    }
    if (config.format === 'text' || config.format === 'both') {
      finalContent.text = text;
    }
    
    return {
      subject,
      ...finalContent,
      headers: this.generateHeaders(config, notification)
    };
  }

  /**
   * Check priority filter
   */
  checkPriorityFilter(notificationPriority, minPriority) {
    const priorities = ['low', 'medium', 'high', 'critical'];
    const notificationLevel = priorities.indexOf(notificationPriority);
    const minLevel = priorities.indexOf(minPriority);
    return notificationLevel >= minLevel;
  }

  /**
   * Check daily email limit
   */
  async checkDailyLimit(maxPerDay, userId) {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const query = `
        SELECT COUNT(*) as count 
        FROM email_logs 
        WHERE DATE(sent_at) = ? 
        AND recipient LIKE '%@%'
      `;
      
      const result = await executeQuery(query, [today]);
      const todayCount = result[0]?.count || 0;
      
      return todayCount < maxPerDay;
      
    } catch (error) {
      logger.error('Failed to check daily limit:', error);
      return true; // Allow sending if check fails
    }
  }

  /**
   * Check for duplicate notifications
   */
  async isDuplicate(notification, userId) {
    try {
      const query = `
        SELECT COUNT(*) as count 
        FROM email_logs 
        WHERE subject = ? 
        AND sent_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `;
      
      const result = await executeQuery(query, [notification.title]);
      return (result[0]?.count || 0) > 0;
      
    } catch (error) {
      logger.error('Failed to check duplicates:', error);
      return false; // Allow sending if check fails
    }
  }

  /**
   * Generate minimal template
   */
  generateMinimalTemplate(notification, language = 'fr') {
    return `<p>${notification.message}</p>`;
  }

  /**
   * Enhance with detailed template
   */
  enhanceWithDetailedTemplate(baseHtml, notification) {
    const detailedInfo = `
      <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border: 1px solid #dee2e6;">
        <h4>Informations détaillées:</h4>
        <p><strong>ID Notification:</strong> ${notification.id}</p>
        <p><strong>Système:</strong> SOMIPEM v1.0</p>
        <p><strong>Serveur:</strong> ${process.env.NODE_ENV || 'production'}</p>
        <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
      </div>
    `;
    
    return baseHtml + detailedInfo;
  }

  /**
   * Generate signature
   */
  generateSignature(signatureText, format) {
    const html = `
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <p style="margin: 0; font-size: 12px; color: #6c757d;">${signatureText}</p>
      </div>
    `;
    
    const text = `\n\n---\n${signatureText}`;
    
    return { html, text };
  }

  /**
   * Generate email headers based on settings
   */
  generateHeaders(config, notification) {
    const headers = {
      'X-Notification-Type': notification.category || 'general',
      'X-Notification-Priority': notification.priority || 'medium',
      'X-SOMIPEM-Settings-Applied': 'true'
    };
    
    if (config.requestReadReceipts) {
      headers['Disposition-Notification-To'] = process.env.SMTP_FROM_EMAIL;
    }
    
    if (config.enableTracking) {
      headers['X-Email-Tracking'] = 'enabled';
    }
    
    return headers;
  }

  /**
   * Clear settings cache for user
   */
  clearSettingsCache(userId = 1) {
    const cacheKey = `email_settings_${userId}`;
    this.settingsCache.delete(cacheKey);
  }

  /**
   * Clear all settings cache
   */
  clearAllCache() {
    this.settingsCache.clear();
  }

  /**
   * Get settings summary for validation
   */
  async getSettingsSummary(userId = 1) {
    const settings = await this.getUserEmailSettings(userId);
    
    return {
      enabled: settings.enabled,
      template: settings.template,
      format: settings.format,
      language: settings.language,
      signatureEnabled: settings.signature?.enabled,
      dailyLimit: settings.filtering?.maxPerDay,
      minPriority: settings.filtering?.minPriority,
      batchEnabled: settings.batchSettings?.hourlyBatch?.enabled,
      digestEnabled: settings.batchSettings?.dailyDigest?.enabled
    };
  }
}

// Create singleton instance
const emailSettingsIntegration = new EmailSettingsIntegration();

export default emailSettingsIntegration;
