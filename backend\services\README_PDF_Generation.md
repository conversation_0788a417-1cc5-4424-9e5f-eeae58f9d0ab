# Modern PDF Generation Service

## Overview

This service replaces the legacy PDFDocument-based PDF generation with a modern React + Tailwind + Puppeteer solution for the SOMIPEM industrial dashboard application.

## Architecture

### Components

1. **Backend Service** (`pdfGenerationService.js`)
   - Manages Puppeteer browser instances
   - Handles PDF generation from React templates
   - Provides health checks and cleanup

2. **React PDF Template** (`PDFReportTemplate.jsx`)
   - PDF-optimized React component
   - Uses Tailwind CSS for consistent styling
   - Includes Chart.js for data visualization
   - Print-specific CSS for proper pagination

3. **PDF Preview Route** (`pdf-preview.jsx`)
   - Dedicated route for Puppeteer access
   - Receives report data via URL parameters
   - Renders without navigation or UI chrome

## Advantages Over PDFDocument

- **Better Visual Design**: HTML/CSS layout vs programmatic construction
- **Chart Rendering**: Perfect Chart.js integration with existing components
- **Maintainability**: Easier to modify layouts and styling
- **Consistency**: Matches web interface design exactly
- **Typography**: Better font rendering and responsive design

## Usage

### Generate PDF Report

```javascript
import pdfGenerationService from '../services/pdfGenerationService.js';

const pdfBuffer = await pdfGenerationService.generateShiftReportPDF(reportData, {
  format: 'A4',
  margin: {
    top: '20mm',
    right: '15mm',
    bottom: '20mm',
    left: '15mm'
  }
});
```

### Environment Variables

Required in `config.env`:
```
FRONTEND_URL=http://localhost:3000
```

## Technical Details

### PDF Generation Flow

1. **Data Encoding**: Report data is base64-encoded and passed via URL
2. **Navigation**: Puppeteer navigates to `/reports/pdf-preview?data=...`
3. **Rendering**: React component renders with Chart.js charts
4. **Readiness**: Component signals readiness via `data-pdf-ready` attribute
5. **PDF Creation**: Puppeteer generates PDF with print-optimized settings

### Print Styling

- Uses CSS `@page` rules for headers/footers
- Implements `page-break-*` properties for content flow
- Print media queries hide unnecessary elements
- High DPI rendering for crisp charts

### Performance Optimizations

- Browser instance reuse
- Optimized viewport settings
- Disabled animations for faster rendering
- Proper cleanup and error handling

## Error Handling

- Timeout protection (30 seconds navigation, 15 seconds chart rendering)
- Graceful browser cleanup
- Specific error messages for different failure types
- Health check endpoint for monitoring

## Deployment Notes

### Dependencies

```json
{
  "puppeteer": "^21.5.0",
  "puppeteer-core": "^21.5.0"
}
```

### Server Requirements

- Node.js 18+
- Sufficient memory for Puppeteer browser instances
- Network access to frontend URL

### Docker Considerations

If deploying in Docker, add these args to Puppeteer launch:
```javascript
args: [
  '--no-sandbox',
  '--disable-setuid-sandbox',
  '--disable-dev-shm-usage'
]
```

## Monitoring

Use the health check endpoint:
```javascript
const health = await pdfGenerationService.healthCheck();
console.log(health); // { status: 'healthy', timestamp: '...' }
```

## Troubleshooting

### Common Issues

1. **Frontend URL not accessible**: Check FRONTEND_URL in config.env
2. **Charts not rendering**: Ensure Chart.js dependencies are installed
3. **PDF timeout**: Increase timeout values in service configuration
4. **Memory issues**: Monitor browser instance cleanup

### Debug Mode

Enable debug logging:
```javascript
process.env.DEBUG = 'puppeteer:*';
```

## Migration from PDFDocument

The new service maintains the same API interface as the old system:
- Same report data structure
- Same file naming conventions
- Same database metadata storage
- Backward compatible error handling

## Future Enhancements

- Template caching for better performance
- Multiple PDF formats support
- Batch PDF generation
- Real-time progress tracking
- Custom branding options
