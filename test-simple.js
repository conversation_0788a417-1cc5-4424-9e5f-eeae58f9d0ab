#!/usr/bin/env node

/**
 * Simple test to check if the issue is with GraphQL or database
 */

import http from 'http';

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000
    };

    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function runTests() {
  console.log('🔍 Testing basic connectivity...\n');

  // Test 1: Health check
  try {
    console.log('1. Testing health endpoint...');
    const health = await testEndpoint('/api/health/ping');
    console.log('✅ Health check:', health.status, JSON.stringify(health.data));
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  // Test 2: REST API
  try {
    console.log('\n2. Testing REST API...');
    const rest = await testEndpoint('/api/sidecards-arret');
    console.log('✅ REST API:', rest.status, JSON.stringify(rest.data));
  } catch (error) {
    console.log('❌ REST API failed:', error.message);
  }

  // Test 3: GraphQL endpoint
  try {
    console.log('\n3. Testing GraphQL endpoint...');
    const graphql = await testEndpoint('/api/graphql', 'POST', {
      query: 'query { __typename }'
    });
    console.log('✅ GraphQL:', graphql.status, JSON.stringify(graphql.data));
  } catch (error) {
    console.log('❌ GraphQL failed:', error.message);
  }

  console.log('\n🏁 Basic tests completed!');
}

runTests().catch(console.error);
