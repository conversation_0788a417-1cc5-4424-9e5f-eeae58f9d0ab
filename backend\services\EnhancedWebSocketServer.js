/**
 * Enhanced WebSocket Server for Multi-Client State Management
 * Integrates with MultiClientStateManager for real-time data synchronization
 * 
 * Features:
 * - User authentication and session management
 * - Room-based subscriptions for targeted updates
 * - Automatic reconnection handling
 * - Performance monitoring and metrics
 * - Integration with existing machine data WebSocket
 */

import { WebSocketServer } from 'ws';
import jwt from 'jsonwebtoken';
import multiClientStateManager from './MultiClientStateManager.js';
import { executeQuery } from '../utils/dbUtils.js';

class EnhancedWebSocketServer {
  constructor() {
    this.wss = null;
    this.server = null;
    this.initialized = false;
    
    // WebSocket paths for different services
    this.WS_PATHS = {
      STATE_SYNC: '/api/state-sync-ws',
      MACHINE_DATA: '/api/machine-data-ws',
      NOTIFICATIONS: '/api/notifications-ws'
    };

    // Performance metrics
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      authenticatedConnections: 0,
      messagesProcessed: 0,
      errorsHandled: 0,
      avgConnectionDuration: 0,
      lastActivity: null
    };
  }

  /**
   * Initialize the enhanced WebSocket server
   */
  async initialize(server) {
    if (this.initialized) {
      return true;
    }

    try {
      this.server = server;
      
      // Initialize multi-client state manager
      await multiClientStateManager.initialize();

      // Set up WebSocket server with no server mode
      this.wss = new WebSocketServer({ noServer: true });

      // Handle server upgrade events
      server.on('upgrade', (request, socket, head) => {
        this.handleUpgrade(request, socket, head);
      });

      // Set up WebSocket connection handler
      this.wss.on('connection', (ws, request) => {
        this.handleConnection(ws, request);
      });

      this.initialized = true;
      console.log('✅ Enhanced WebSocket Server initialized');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize Enhanced WebSocket Server:', error);
      return false;
    }
  }

  /**
   * Handle WebSocket upgrade requests
   */
  handleUpgrade(request, socket, head) {
    const url = new URL(request.url, `http://${request.headers.host}`);
    
    // Route to appropriate WebSocket handler based on path
    if (url.pathname === this.WS_PATHS.STATE_SYNC) {
      this.wss.handleUpgrade(request, socket, head, (ws) => {
        this.wss.emit('connection', ws, request);
      });
    }
    // Let other WebSocket handlers manage their own paths
  }

  /**
   * Handle new WebSocket connections
   */
  async handleConnection(ws, request) {
    console.log('🔌 New WebSocket connection attempt');
    
    try {
      // Extract authentication information
      const authInfo = await this.authenticateConnection(request);
      
      if (!authInfo.success) {
        console.log('❌ WebSocket authentication failed');
        ws.close(1008, 'Authentication failed');
        return;
      }

      const { userId, user } = authInfo;
      
      // Extract session information
      const sessionInfo = this.extractSessionInfo(request);
      
      // Register client with state manager
      const clientId = await multiClientStateManager.registerClient(
        ws, 
        userId, 
        sessionInfo
      );

      // Store client metadata
      ws.clientId = clientId;
      ws.userId = userId;
      ws.user = user;
      ws.connectedAt = Date.now();

      // Update metrics
      this.metrics.totalConnections++;
      this.metrics.activeConnections++;
      this.metrics.authenticatedConnections++;
      this.metrics.lastActivity = new Date().toISOString();

      console.log(`✅ WebSocket client authenticated: ${clientId} for user ${userId}`);

      // Send welcome message
      this.sendWelcomeMessage(ws, user);

      // Set up connection monitoring
      this.setupConnectionMonitoring(ws);

    } catch (error) {
      console.error('❌ Error handling WebSocket connection:', error);
      ws.close(1011, 'Internal server error');
      this.metrics.errorsHandled++;
    }
  }

  /**
   * Authenticate WebSocket connection
   */
  async authenticateConnection(request) {
    try {
      // Extract token from query parameters or headers
      const url = new URL(request.url, `http://${request.headers.host}`);
      const token = url.searchParams.get('token') || 
                   this.extractTokenFromCookie(request.headers.cookie) ||
                   this.extractTokenFromHeader(request.headers.authorization);

      if (!token) {
        return { success: false, error: 'No authentication token provided' };
      }

      // Verify JWT token
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        return { success: false, error: 'JWT secret not configured' };
      }

      const decoded = jwt.verify(token, jwtSecret);
      
      // Get user information from database
      const { success, data: users, error } = await executeQuery(
        `SELECT u.id, u.username, u.email, u.role, u.department_id,
                r.name as role_name, d.name as department_name
         FROM users u
         LEFT JOIN roles r ON u.role_id = r.id
         LEFT JOIN departments d ON u.department_id = d.id
         WHERE u.id = ?`,
        [decoded.id]
      );

      if (!success || users.length === 0) {
        return { success: false, error: 'User not found' };
      }

      return { 
        success: true, 
        userId: decoded.id, 
        user: users[0],
        token 
      };

    } catch (error) {
      console.error('❌ WebSocket authentication error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Extract session information from request
   */
  extractSessionInfo(request) {
    const userAgent = request.headers['user-agent'] || 'Unknown';
    const ipAddress = request.socket.remoteAddress || 
                     request.headers['x-forwarded-for'] || 
                     request.headers['x-real-ip'] || 
                     'Unknown';

    return {
      userAgent,
      ipAddress,
      timestamp: Date.now()
    };
  }

  /**
   * Extract token from cookie header
   */
  extractTokenFromCookie(cookieHeader) {
    if (!cookieHeader) return null;
    
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {});

    return cookies.token;
  }

  /**
   * Extract token from authorization header
   */
  extractTokenFromHeader(authHeader) {
    if (!authHeader) return null;
    
    const parts = authHeader.split(' ');
    if (parts.length === 2 && parts[0] === 'Bearer') {
      return parts[1];
    }
    
    return null;
  }

  /**
   * Send welcome message to newly connected client
   */
  sendWelcomeMessage(ws, user) {
    const welcomeMessage = {
      type: 'WELCOME',
      user: {
        id: user.id,
        username: user.username,
        role: user.role_name,
        department: user.department_name
      },
      server: {
        version: '2.0.0',
        features: ['real_time_sync', 'multi_device', 'collaborative_editing'],
        timestamp: Date.now()
      }
    };

    this.sendMessage(ws, welcomeMessage);
  }

  /**
   * Set up connection monitoring for a WebSocket
   */
  setupConnectionMonitoring(ws) {
    // Ping interval for connection health
    const pingInterval = setInterval(() => {
      if (ws.readyState === ws.OPEN) {
        ws.ping();
      } else {
        clearInterval(pingInterval);
      }
    }, 30000);

    // Handle connection close
    ws.on('close', (code, reason) => {
      clearInterval(pingInterval);
      this.handleConnectionClose(ws, code, reason);
    });

    // Handle connection errors
    ws.on('error', (error) => {
      clearInterval(pingInterval);
      this.handleConnectionError(ws, error);
    });

    // Store interval reference
    ws.pingInterval = pingInterval;
  }

  /**
   * Handle connection close
   */
  handleConnectionClose(ws, code, reason) {
    const duration = Date.now() - ws.connectedAt;
    
    // Update metrics
    this.metrics.activeConnections--;
    this.metrics.avgConnectionDuration = 
      (this.metrics.avgConnectionDuration + duration) / 2;

    console.log(`🔌 WebSocket connection closed: ${ws.clientId} (code: ${code}, duration: ${duration}ms)`);
  }

  /**
   * Handle connection errors
   */
  handleConnectionError(ws, error) {
    console.error(`❌ WebSocket connection error for ${ws.clientId}:`, error);
    this.metrics.errorsHandled++;
  }

  /**
   * Send message to WebSocket client
   */
  sendMessage(ws, message) {
    if (ws.readyState === ws.OPEN) {
      try {
        ws.send(JSON.stringify(message));
        this.metrics.messagesProcessed++;
        return true;
      } catch (error) {
        console.error('❌ Error sending WebSocket message:', error);
        this.metrics.errorsHandled++;
        return false;
      }
    }
    return false;
  }

  /**
   * Broadcast message to all connected clients
   */
  broadcastMessage(message, filter = null) {
    let sentCount = 0;
    
    this.wss.clients.forEach((ws) => {
      if (ws.readyState === ws.OPEN) {
        // Apply filter if provided
        if (!filter || filter(ws)) {
          if (this.sendMessage(ws, message)) {
            sentCount++;
          }
        }
      }
    });

    console.log(`📡 Broadcasted message to ${sentCount} clients`);
    return sentCount;
  }

  /**
   * Get server metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      currentActiveConnections: this.wss ? this.wss.clients.size : 0,
      stateManagerMetrics: multiClientStateManager.getMetrics()
    };
  }

  /**
   * Health check for WebSocket server
   */
  getHealthStatus() {
    return {
      initialized: this.initialized,
      activeConnections: this.wss ? this.wss.clients.size : 0,
      stateManagerHealthy: multiClientStateManager.initialized,
      lastActivity: this.metrics.lastActivity,
      uptime: this.initialized ? Date.now() - this.metrics.totalConnections : 0
    };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🔄 Shutting down Enhanced WebSocket Server...');

    try {
      if (this.wss) {
        // Close all client connections
        this.wss.clients.forEach((ws) => {
          ws.close(1001, 'Server shutting down');
        });

        // Close WebSocket server
        this.wss.close();
      }

      this.initialized = false;
      console.log('✅ Enhanced WebSocket Server shut down gracefully');

    } catch (error) {
      console.error('❌ Error during WebSocket server shutdown:', error);
    }
  }
}

// Create singleton instance
const enhancedWebSocketServer = new EnhancedWebSocketServer();

export default enhancedWebSocketServer;
