import express from 'express';
import { check, validationResult } from 'express-validator';
import auth from '../middleware/auth.js';
import { executeQuery } from '../utils/dbUtils.js';
import rateLimiter from '../middleware/rateLimiter.js';

const router = express.Router();

/**
 * GET /api/settings
 * Get user settings
 */
router.get('/', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`🔍 Getting settings for user ${userId}`);

    const result = await executeQuery(
      'SELECT settings_json FROM user_settings WHERE user_id = ?',
      [userId]
    );

    if (!result.success) {
      console.error('Database error getting settings:', result.error);
      return res.status(500).json({
        error: 'Database error',
        message: 'Failed to retrieve settings'
      });
    }

    if (result.data.length === 0) {
      // User has no settings yet, create default settings
      console.log(`📝 Creating default settings for user ${userId}`);
      
      const defaultSettings = {
        theme: {
          darkMode: false,
          compactMode: false,
          animationsEnabled: true,
          chartAnimations: true
        },
        tables: {
          defaultPageSize: 20,
          pageSizeOptions: [10, 20, 50, 100],
          virtualizationThreshold: 100,
          showQuickJumper: true
        },
        charts: {
          animationsEnabled: true,
          showLegend: true,
          colorScheme: 'brand',
          performanceMode: false,
          layout: {
            defaultHeight: 300,
            compactMode: false,
            aspectRatio: '16:9',
            margins: { top: 20, right: 30, left: 20, bottom: 5 }
          },
          dataDisplay: {
            showDataLabels: false,
            showGridLines: true,
            showDataPoints: true,
            zeroBasedAxis: false
          },
          interactions: {
            hoverEffects: true,
            clickToExpand: true,
            tooltipStyle: 'standard'
          }
        },
        refresh: {
          dashboardInterval: 300,
          realtimeInterval: 60,
          autoRefreshEnabled: true,
          backgroundRefresh: true
        },
        notifications: {
          categories: {
            machine_alert: true,
            production: true,
            quality: true,
            maintenance: true,
            alert: true,
            info: true,
            updates: true
          },
          priorities: {
            critical: true,
            high: true,
            medium: true,
            low: true
          },
          delivery: {
            sse: true,
            email: false,
            browser: true
          },
          behavior: {
            sound: true,
            autoClose: false,
            autoCloseDelay: 5000,
            maxVisible: 5
          }
        },
        email: {
          enabled: false,
          frequency: 'immediate',
          template: 'standard',
          notifications: {
            categories: {
              machine_alert: true,
              production: false,
              quality: true,
              maintenance: false,
              alert: true,
              info: false,
              updates: false
            },
            priorities: {
              critical: true,
              high: true,
              medium: false,
              low: false
            }
          },
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '06:00',
            timezone: 'Africa/Tunis'
          },
          batchSettings: {
            hourlyBatch: {
              enabled: false,
              maxNotifications: 10
            },
            dailyDigest: {
              enabled: false,
              time: '08:00',
              includeCharts: true,
              includeTables: false
            }
          }
        },
        reports: {
          generation: {
            autoGenerate: false,
            format: 'pdf',
            quality: 'standard',
            includeCharts: true,
            includeTables: true
          },
          schedules: {
            daily: {
              enabled: false,
              time: '07:00',
              machines: []
            },
            weekly: {
              enabled: false,
              day: 'monday',
              time: '08:00',
              machines: []
            },
            monthly: {
              enabled: false,
              day: 1,
              time: '09:00',
              machines: []
            }
          },
          subscriptions: {
            enabled: false,
            types: ['shift', 'daily', 'weekly'],
            delivery: 'email'
          },
          content: {
            sections: {
              summary: true,
              production: true,
              quality: true,
              maintenance: false,
              charts: true,
              tables: false
            },
            template: 'standard',
            branding: true
          },
          delivery: {
            email: true,
            download: false,
            storage: true,
            retention: 30
          }
        },
        performance: {
          caching: {
            enabled: true,
            duration: 300,
            strategy: 'smart'
          },
          optimization: {
            lazyLoading: true,
            virtualization: true,
            compression: false
          }
        }
      };

      // Insert default settings
      const insertResult = await executeQuery(
        'INSERT INTO user_settings (user_id, settings_json) VALUES (?, ?)',
        [userId, JSON.stringify(defaultSettings)]
      );

      if (!insertResult.success) {
        console.error('Failed to create default settings:', insertResult.error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create default settings'
        });
      }

      console.log(`✅ Default settings created for user ${userId}`);
      return res.json({
        success: true,
        settings: defaultSettings,
        message: 'Default settings created'
      });
    }

    const settings = result.data[0].settings_json;
    console.log(`✅ Settings retrieved for user ${userId}`);

    res.json({
      success: true,
      settings: settings
    });

  } catch (error) {
    console.error('Error getting settings:', error);
    res.status(500).json({
      error: 'Server error',
      message: error.message
    });
  }
});

/**
 * PUT /api/settings
 * Update user settings
 */
router.put('/',
  auth,
  rateLimiter.settingsUpdateLimiter(),
  [
    check('settings')
      .isObject()
      .withMessage('Settings must be an object')
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const userId = req.user.id;
      const { settings } = req.body;

      console.log(`🔄 Updating settings for user ${userId}`);

      // Update settings in database
      const result = await executeQuery(
        'UPDATE user_settings SET settings_json = ?, updated_at = NOW() WHERE user_id = ?',
        [JSON.stringify(settings), userId]
      );

      if (!result.success) {
        console.error('Database error updating settings:', result.error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to update settings'
        });
      }

      if (result.data.affectedRows === 0) {
        // User doesn't have settings yet, create them
        const insertResult = await executeQuery(
          'INSERT INTO user_settings (user_id, settings_json) VALUES (?, ?)',
          [userId, JSON.stringify(settings)]
        );

        if (!insertResult.success) {
          console.error('Failed to create settings:', insertResult.error);
          return res.status(500).json({
            error: 'Database error',
            message: 'Failed to create settings'
          });
        }

        console.log(`✅ Settings created for user ${userId}`);
      } else {
        console.log(`✅ Settings updated for user ${userId}`);
      }

      res.json({
        success: true,
        settings: settings,
        message: 'Settings updated successfully'
      });

    } catch (error) {
      console.error('Error updating settings:', error);
      res.status(500).json({
        error: 'Server error',
        message: error.message
      });
    }
  }
);

/**
 * POST /api/settings/reset
 * Reset user settings to defaults
 */
router.post('/reset', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`🔄 Resetting settings for user ${userId}`);

    // Get default settings (same as in GET endpoint)
    const defaultSettings = {
      theme: {
        darkMode: false,
        compactMode: false,
        animationsEnabled: true,
        chartAnimations: true
      },
      tables: {
        defaultPageSize: 20,
        pageSizeOptions: [10, 20, 50, 100],
        virtualizationThreshold: 100,
        showQuickJumper: true
      },
      charts: {
        animationsEnabled: true,
        showLegend: true,
        colorScheme: 'brand',
        performanceMode: false,
        layout: {
          defaultHeight: 300,
          compactMode: false,
          aspectRatio: '16:9',
          margins: { top: 20, right: 30, left: 20, bottom: 5 }
        },
        dataDisplay: {
          showDataLabels: false,
          showGridLines: true,
          showDataPoints: true,
          zeroBasedAxis: false
        },
        interactions: {
          hoverEffects: true,
          clickToExpand: true,
          tooltipStyle: 'standard'
        }
      },
      refresh: {
        dashboardInterval: 300,
        realtimeInterval: 60,
        autoRefreshEnabled: true,
        backgroundRefresh: true
      },
      notifications: {
        categories: {
          machine_alert: true,
          production: true,
          quality: true,
          maintenance: true,
          alert: true,
          info: true,
          updates: true
        },
        priorities: {
          critical: true,
          high: true,
          medium: true,
          low: true
        },
        delivery: {
          sse: true,
          email: false,
          browser: true
        },
        behavior: {
          sound: true,
          autoClose: false,
          autoCloseDelay: 5000,
          maxVisible: 5
        }
      },
      email: {
        enabled: false,
        frequency: 'immediate',
        template: 'standard',
        notifications: {
          categories: {
            machine_alert: true,
            production: false,
            quality: true,
            maintenance: false,
            alert: true,
            info: false,
            updates: false
          },
          priorities: {
            critical: true,
            high: true,
            medium: false,
            low: false
          }
        },
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '06:00',
          timezone: 'Africa/Tunis'
        },
        batchSettings: {
          hourlyBatch: {
            enabled: false,
            maxNotifications: 10
          },
          dailyDigest: {
            enabled: false,
            time: '08:00',
            includeCharts: true,
            includeTables: false
          }
        }
      },
      reports: {
        generation: {
          autoGenerate: false,
          format: 'pdf',
          quality: 'standard',
          includeCharts: true,
          includeTables: true
        },
        schedules: {
          daily: {
            enabled: false,
            time: '07:00',
            machines: []
          },
          weekly: {
            enabled: false,
            day: 'monday',
            time: '08:00',
            machines: []
          },
          monthly: {
            enabled: false,
            day: 1,
            time: '09:00',
            machines: []
          }
        },
        subscriptions: {
          enabled: false,
          types: ['shift', 'daily', 'weekly'],
          delivery: 'email'
        },
        content: {
          sections: {
            summary: true,
            production: true,
            quality: true,
            maintenance: false,
            charts: true,
            tables: false
          },
          template: 'standard',
          branding: true
        },
        delivery: {
          email: true,
          download: false,
          storage: true,
          retention: 30
        }
      },
      performance: {
        caching: {
          enabled: true,
          duration: 300,
          strategy: 'smart'
        },
        optimization: {
          lazyLoading: true,
          virtualization: true,
          compression: false
        }
      }
    };

    // Update settings with defaults
    const result = await executeQuery(
      'UPDATE user_settings SET settings_json = ?, updated_at = NOW() WHERE user_id = ?',
      [JSON.stringify(defaultSettings), userId]
    );

    if (!result.success) {
      console.error('Database error resetting settings:', result.error);
      return res.status(500).json({
        error: 'Database error',
        message: 'Failed to reset settings'
      });
    }

    if (result.data.affectedRows === 0) {
      // User doesn't have settings yet, create them
      const insertResult = await executeQuery(
        'INSERT INTO user_settings (user_id, settings_json) VALUES (?, ?)',
        [userId, JSON.stringify(defaultSettings)]
      );

      if (!insertResult.success) {
        console.error('Failed to create default settings:', insertResult.error);
        return res.status(500).json({
          error: 'Database error',
          message: 'Failed to create default settings'
        });
      }
    }

    console.log(`✅ Settings reset to defaults for user ${userId}`);

    res.json({
      success: true,
      settings: defaultSettings,
      message: 'Settings reset to defaults successfully'
    });

  } catch (error) {
    console.error('Error resetting settings:', error);
    res.status(500).json({
      error: 'Server error',
      message: error.message
    });
  }
});

/**
 * GET /api/settings/schema
 * Get settings validation schema
 */
router.get('/schema', auth, async (req, res) => {
  try {
    const schema = {
      theme: {
        darkMode: { type: 'boolean', default: false },
        compactMode: { type: 'boolean', default: false },
        animationsEnabled: { type: 'boolean', default: true },
        chartAnimations: { type: 'boolean', default: true }
      },
      tables: {
        defaultPageSize: {
          type: 'number',
          default: 20,
          options: [10, 20, 50, 100]
        },
        pageSizeOptions: {
          type: 'array',
          default: [10, 20, 50, 100]
        },
        virtualizationThreshold: { type: 'number', default: 100 },
        showQuickJumper: { type: 'boolean', default: true }
      },
      charts: {
        animationsEnabled: { type: 'boolean', default: true },
        showLegend: { type: 'boolean', default: true },
        colorScheme: {
          type: 'string',
          default: 'brand',
          options: ['default', 'brand', 'blue', 'green', 'red']
        },
        performanceMode: { type: 'boolean', default: false }
      },
      refresh: {
        dashboardInterval: {
          type: 'number',
          default: 300,
          min: 30,
          max: 3600
        },
        realtimeInterval: {
          type: 'number',
          default: 60,
          min: 10,
          max: 600
        },
        autoRefreshEnabled: { type: 'boolean', default: true },
        backgroundRefresh: { type: 'boolean', default: true }
      },
      notifications: {
        categories: {
          machine_alert: { type: 'boolean', default: true },
          production: { type: 'boolean', default: true },
          quality: { type: 'boolean', default: true },
          maintenance: { type: 'boolean', default: true },
          alert: { type: 'boolean', default: true },
          info: { type: 'boolean', default: true },
          updates: { type: 'boolean', default: true }
        },
        priorities: {
          critical: { type: 'boolean', default: true },
          high: { type: 'boolean', default: true },
          medium: { type: 'boolean', default: true },
          low: { type: 'boolean', default: true }
        },
        delivery: {
          sse: { type: 'boolean', default: true },
          email: { type: 'boolean', default: false },
          browser: { type: 'boolean', default: true }
        },
        behavior: {
          sound: { type: 'boolean', default: true },
          autoClose: { type: 'boolean', default: false },
          autoCloseDelay: {
            type: 'number',
            default: 5000,
            min: 1000,
            max: 30000
          },
          maxVisible: {
            type: 'number',
            default: 5,
            min: 1,
            max: 20
          }
        }
      },
      email: {
        enabled: { type: 'boolean', default: false },
        frequency: {
          type: 'string',
          default: 'immediate',
          options: ['immediate', 'hourly_batch', 'daily_digest']
        },
        template: {
          type: 'string',
          default: 'standard',
          options: ['minimal', 'standard', 'detailed']
        }
      },
      reports: {
        generation: {
          autoGenerate: { type: 'boolean', default: false },
          format: {
            type: 'string',
            default: 'pdf',
            options: ['pdf', 'html', 'excel']
          },
          quality: {
            type: 'string',
            default: 'standard',
            options: ['low', 'standard', 'high']
          },
          includeCharts: { type: 'boolean', default: true },
          includeTables: { type: 'boolean', default: true }
        }
      },
      performance: {
        caching: {
          enabled: { type: 'boolean', default: true },
          duration: {
            type: 'number',
            default: 300,
            min: 60,
            max: 3600
          },
          strategy: {
            type: 'string',
            default: 'smart',
            options: ['none', 'basic', 'smart', 'aggressive']
          }
        },
        optimization: {
          lazyLoading: { type: 'boolean', default: true },
          virtualization: { type: 'boolean', default: true },
          compression: { type: 'boolean', default: false }
        }
      }
    };

    res.json({
      success: true,
      schema: schema
    });

  } catch (error) {
    console.error('Error getting settings schema:', error);
    res.status(500).json({
      error: 'Server error',
      message: error.message
    });
  }
});

/**
 * GET /api/settings/diagnostic
 * Settings system diagnostic endpoint
 */
router.get('/diagnostic', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const diagnosticStartTime = Date.now();

    console.log(`🔍 Running settings diagnostic for user ${userId}`);

    const diagnostic = {
      timestamp: new Date().toISOString(),
      userId,
      tests: []
    };

    // Test 1: Database connectivity
    const dbTestStart = Date.now();
    try {
      const dbTest = await executeQuery('SELECT 1 as test');
      diagnostic.tests.push({
        name: 'Database Connectivity',
        status: dbTest.success ? 'passed' : 'failed',
        duration: Date.now() - dbTestStart,
        details: dbTest.success ? 'Database connection successful' : dbTest.error
      });
    } catch (dbError) {
      diagnostic.tests.push({
        name: 'Database Connectivity',
        status: 'failed',
        duration: Date.now() - dbTestStart,
        details: dbError.message
      });
    }

    // Test 2: Settings table structure
    const tableTestStart = Date.now();
    try {
      const tableTest = await executeQuery('DESCRIBE user_settings');
      diagnostic.tests.push({
        name: 'Settings Table Structure',
        status: tableTest.success ? 'passed' : 'failed',
        duration: Date.now() - tableTestStart,
        details: tableTest.success ? `Table has ${tableTest.data.length} columns` : tableTest.error
      });
    } catch (tableError) {
      diagnostic.tests.push({
        name: 'Settings Table Structure',
        status: 'failed',
        duration: Date.now() - tableTestStart,
        details: tableError.message
      });
    }

    // Test 3: User settings retrieval
    const retrievalTestStart = Date.now();
    try {
      const retrievalTest = await executeQuery(
        'SELECT settings_json FROM user_settings WHERE user_id = ?',
        [userId]
      );
      diagnostic.tests.push({
        name: 'User Settings Retrieval',
        status: retrievalTest.success ? 'passed' : 'failed',
        duration: Date.now() - retrievalTestStart,
        details: retrievalTest.success ?
          `Found ${retrievalTest.data.length} settings record(s)` :
          retrievalTest.error
      });
    } catch (retrievalError) {
      diagnostic.tests.push({
        name: 'User Settings Retrieval',
        status: 'failed',
        duration: Date.now() - retrievalTestStart,
        details: retrievalError.message
      });
    }

    // Test 4: Settings update performance
    const updateTestStart = Date.now();
    try {
      const testSettings = { diagnostic: { timestamp: Date.now() } };
      const updateTest = await executeQuery(
        'UPDATE user_settings SET settings_json = ?, updated_at = NOW() WHERE user_id = ?',
        [JSON.stringify(testSettings), userId]
      );
      diagnostic.tests.push({
        name: 'Settings Update Performance',
        status: updateTest.success ? 'passed' : 'failed',
        duration: Date.now() - updateTestStart,
        details: updateTest.success ?
          `Updated ${updateTest.data.affectedRows} record(s)` :
          updateTest.error
      });
    } catch (updateError) {
      diagnostic.tests.push({
        name: 'Settings Update Performance',
        status: 'failed',
        duration: Date.now() - updateTestStart,
        details: updateError.message
      });
    }

    diagnostic.totalDuration = Date.now() - diagnosticStartTime;
    diagnostic.overall = diagnostic.tests.every(test => test.status === 'passed') ? 'healthy' : 'issues_detected';

    console.log(`✅ Settings diagnostic completed in ${diagnostic.totalDuration}ms`);

    res.json({
      success: true,
      diagnostic
    });

  } catch (error) {
    console.error('Settings diagnostic failed:', error);
    res.status(500).json({
      success: false,
      error: 'Diagnostic failed',
      message: error.message
    });
  }
});

export default router;
