// Constants and configuration for the Arret Context system

// Chart colors matching original design
export const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2", 
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
}

// Skeleton loading sections for different components
export const SKELETON_SECTIONS = {
  // Basic sections
  STATS: 'stats',
  CHARTS: 'charts',
  TABLE: 'table',
  PERFORMANCE: 'performance',
  
  // Granular chart skeletons
  TOP_STOPS_CHART: 'topStopsChart',
  DURATION_TREND_CHART: 'durationTrendChart',
  MACHINE_COMPARISON_CHART: 'machineComparisonChart',
  PARETO_CHART: 'paretoChart',
  MTTR_HEATMAP: 'mttrHeatmap',
  AVAILABILITY_CHART: 'availabilityChart',
  
  // Component-specific skeletons
  FILTERS: 'filters',
  HEADER: 'header',
  SIDEBAR: 'sidebar',
  PAGINATION: 'pagination',
  
  // Loading phases
  INITIAL_LOAD: 'initialLoad',
  DATA_REFRESH: 'dataRefresh',
  FILTER_CHANGE: 'filterChange'
}

// Default skeleton phases for progressive loading
export const DEFAULT_SKELETON_PHASES = [
  { sections: [SKELETON_SECTIONS.STATS], delay: 200 },
  { sections: [SKELETON_SECTIONS.PERFORMANCE], delay: 400 },
  { sections: [SKELETON_SECTIONS.TOP_STOPS_CHART, SKELETON_SECTIONS.DURATION_TREND_CHART], delay: 600 },
  { sections: [SKELETON_SECTIONS.MACHINE_COMPARISON_CHART, SKELETON_SECTIONS.PARETO_CHART], delay: 800 },
  { sections: [SKELETON_SECTIONS.TABLE, SKELETON_SECTIONS.PAGINATION], delay: 1000 },
  { sections: [SKELETON_SECTIONS.MTTR_HEATMAP, SKELETON_SECTIONS.AVAILABILITY_CHART], delay: 1200 },
  { sections: [SKELETON_SECTIONS.INITIAL_LOAD, SKELETON_SECTIONS.DATA_REFRESH, SKELETON_SECTIONS.FILTER_CHANGE], delay: 1400 }
]

// Performance calculation constants
export const PERFORMANCE_CONSTANTS = {
  MINUTES_PER_HOUR: 60,
  HOURS_PER_DAY: 24,
  DAYS_PER_WEEK: 7,
  APPROX_DAYS_PER_MONTH: 30
}

// Cache configuration
export const CACHE_CONFIG = {
  DURATION: 5 * 60 * 1000, // 5 minutes
  MAX_ENTRIES: 100
}

// Debounce delays based on filter complexity
export const DEBOUNCE_DELAYS = {
  SIMPLE: 200,    // No machine or date filters
  MEDIUM: 400,    // Either machine or date filter
  COMPLEX: 800    // Both machine and date filters
}

// Circuit breaker configuration
export const CIRCUIT_BREAKER = {
  THRESHOLD: 5,
  MAX_RETRIES: 3
}

// Initial state templates
export const INITIAL_SKELETON_STATE = {
  [SKELETON_SECTIONS.STATS]: false,
  [SKELETON_SECTIONS.CHARTS]: false,
  [SKELETON_SECTIONS.TABLE]: false,
  [SKELETON_SECTIONS.PERFORMANCE]: false,
  [SKELETON_SECTIONS.TOP_STOPS_CHART]: false,
  [SKELETON_SECTIONS.DURATION_TREND_CHART]: false,
  [SKELETON_SECTIONS.MACHINE_COMPARISON_CHART]: false,
  [SKELETON_SECTIONS.PARETO_CHART]: false,
  [SKELETON_SECTIONS.MTTR_HEATMAP]: false,
  [SKELETON_SECTIONS.AVAILABILITY_CHART]: false,
  [SKELETON_SECTIONS.FILTERS]: false,
  [SKELETON_SECTIONS.HEADER]: false,
  [SKELETON_SECTIONS.SIDEBAR]: false,
  [SKELETON_SECTIONS.PAGINATION]: false,
  [SKELETON_SECTIONS.INITIAL_LOAD]: false,
  [SKELETON_SECTIONS.DATA_REFRESH]: false,
  [SKELETON_SECTIONS.FILTER_CHANGE]: false
}

export const INITIAL_DATA_STATE = {
  // Machine and filter state
  machineModels: [],
  machineNames: [],
  selectedMachineModel: "IPS",
  selectedMachine: "",
  filteredMachineNames: [],
  
  // Date filtering state
  dateRangeType: "month", // Changed from "day" to "month" to match UI default
  selectedDate: null,
  dateRangeDescription: "",
  dateFilterActive: false,
  dateOptions: [],
  
  // Loading states
  loading: false,
  error: null,
  essentialLoading: false,
  detailedLoading: false,
  complexFilterLoading: false, // For triple filter scenarios
  
  // Data arrays
  arretStats: [],
  topStopsData: [],
  arretsByRange: [],
  arretNonDeclareStats: [],
  filteredArretsByRange: [],
  stopsData: [],
  rawChartData: [],
  durationTrend: [],
  machineComparison: [],
  operatorStats: [],
  stopReasons: [],
  
  // Performance metrics
  mttr: 0,
  mtbf: 0,
  doper: 0,
  showPerformanceMetrics: false,
  
  // UI state
  isSearchModalVisible: false,
  isAutoMode: true,
  searchResults: [],
  searchLoading: false
}
