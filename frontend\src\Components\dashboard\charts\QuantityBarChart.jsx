import React from "react";
import {
  <PERSON><PERSON><PERSON>ive<PERSON><PERSON>r,
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON> as RechartsTooltip,
} from "recharts";
import { Card, Empty } from "antd";
import dayjs from "dayjs";

/**
 * Reusable bar chart component for displaying quantity data
 * @param {Object} props - Component props
 * @param {Array} props.data - The data to display in the chart
 * @param {string} props.title - The title of the chart
 * @param {string} props.dataKey - The key in the data objects to use for the bar values
 * @param {string} props.color - The color to use for the bars
 * @param {string} props.label - The label to use for the Y-axis
 * @param {string} props.tooltipLabel - The label to use in the tooltip
 * @param {boolean} props.isKg - Whether the values are in kg (affects formatting)
 * @returns {JSX.Element} The rendered chart
 */
const QuantityBarChart = ({
  data,
  title,
  dataKey,
  color,
  label = "Quantité",
  tooltipLabel = "Quantité",
  isKg = false,
}) => {
  return (
    <Card title={title} type="inner">
      {data && data.length > 0 ? (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
            <XAxis
              dataKey="date"
              tick={{ fill: "#666" }}
              tickFormatter={(date) => {
                // Safely format the date, handling invalid dates
                try {
                  if (date && dayjs(date).isValid()) {
                    // Format as DD/MM with year included in tooltip
                    return dayjs(date).format("DD/MM");
                  }
                  return "N/A";
                } catch (e) {
                  console.error("Error formatting date:", date, e);
                  return "N/A";
                }
              }}
            />
            <YAxis
              tickFormatter={(value) => value.toLocaleString()}
              label={{
                value: isKg ? `${label} (kg)` : label,
                angle: -90,
                position: "insideLeft",
                style: { fill: "#666" },
              }}
            />
            <RechartsTooltip
              contentStyle={{
                backgroundColor: "#fff",
                border: "1px solid #f0f0f0",
                borderRadius: 8,
                boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
              }}
              formatter={(value) => {
                // Ensure value is a number and properly formatted
                const numValue = parseFloat(value);
                return [
                  !isNaN(numValue) ? numValue.toLocaleString() : "N/A",
                  isKg ? `${tooltipLabel} (kg)` : tooltipLabel
                ];
              }}
              labelFormatter={(label) => {
                try {
                  if (label && dayjs(label).isValid()) {
                    return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                  }
                  return "Date: N/A";
                } catch (e) {
                  console.error("Error formatting tooltip date:", label, e);
                  return "Date: N/A";
                }
              }}
            />

            <Bar
              dataKey={dataKey}
              name={tooltipLabel}
              fill={color}
              maxBarSize={40}
            />
          </BarChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
          <Empty description="Aucune donnée disponible" />
        </div>
      )}
    </Card>
  );
};

export default QuantityBarChart;
