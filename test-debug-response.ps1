# Test Shift Performance GraphQL Query
# PowerShell script to test the new shift performance implementation

$headers = @{
    'Content-Type' = 'application/json'
}

$query = @{
    query = @"
    {
        enhancedGetShiftPerformance(filters: { model: "IPS" }) {
            data {
                Shift
                production
                rejects
                availability
                performance
                oee
                quality
                disponibilite
                downtime
            }
            dataSource
        }
    }
"@
} | ConvertTo-Json

Write-Host "Testing Shift Performance Query..."
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/graphql" -Method POST -Headers $headers -Body $query
    
    if ($response.errors) {
        Write-Host "GraphQL Errors:"
        $response.errors | ConvertTo-Json -Depth 10 | Write-Host
        return
    }
    
    $data = $response.data.enhancedGetShiftPerformance
    Write-Host "Data source: $($data.dataSource)"
    Write-Host "Total shifts: $($data.data.Count)"
    Write-Host ""
    
    Write-Host "Shift Performance Results (Team Comparison):"
    for ($i = 0; $i -lt $data.data.Count; $i++) {
        $shift = $data.data[$i]
        Write-Host "   $($i + 1). $($shift.shift)"
        Write-Host "      Production: $($shift.production) pieces"
        Write-Host "      Rejects: $($shift.rejects) pieces"
        Write-Host "      OEE: $($shift.oee)%"
        Write-Host "      Availability: $($shift.availability)%"
        Write-Host "      Performance: $($shift.performance)%"
        Write-Host "      Quality: $($shift.quality)%"
        Write-Host "      Downtime: $($shift.downtime) hours"
        Write-Host ""
    }
    
    # Verify we're getting shift data (not machine data)
    $uniqueShifts = $data.data | Select-Object -ExpandProperty shift | Sort-Object -Unique
    Write-Host "Unique shifts found: $($uniqueShifts -join ', ')"
    
    # Check if we have machine names instead of shifts
    $hasMachineNames = $uniqueShifts | Where-Object { $_ -like '*IPS*' -or $_ -like '*MACHINE*' }
    if ($hasMachineNames.Count -eq 0) {
        Write-Host "SUCCESS: Data is properly aggregated by shifts (not machines)"
    } else {
        Write-Host "ISSUE: Data might still be showing machine names instead of shifts"
    }
    
} catch {
    Write-Host "Test failed: " + $_.Exception.Message
}