/**
 * Custom hook for handling dashboard data fetching and WebSocket connections
 */
import React from 'react';
import { useState, useEffect, useCallback } from 'react';
import request from 'superagent';
import { notification } from 'antd';
import { CloseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import websocketService from '../utils/websocketService';
import { useAuth } from './useAuth';

/**
 * Safely parses float values, returning 0 for NaN results
 * @param {string|number} value - The value to parse
 * @returns {number} The parsed float or 0 if NaN
 */
const safeParseFloat = (value) => {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Custom hook for managing dashboard data with WebSocket support
 * @returns {Object} Dashboard data and related functions
 */
export const useDashboardData = () => {
  // Get authentication state from useAuth hook
  const { isAuthenticated } = useAuth();

  // 🔒 SECURITY: Helper function to create authenticated requests
  // Uses HTTP-only cookies exclusively for security
  const createAuthRequest = useCallback((method, url) => {
    const baseURL = (() => {
      // Check if we're in a browser environment first
      if (typeof window !== 'undefined') {
        const currentOrigin = window.location.origin;

        // If running on ngrok domain, use the same origin (unified architecture)
        if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
          return currentOrigin;
        }

        // For local development, check environment variable first
        if (import.meta.env.VITE_API_URL) {
          return import.meta.env.VITE_API_URL;
        }

        // Fallback to current origin for local development
        return currentOrigin;
      }

      // Fallback for server-side rendering
      return "http://localhost:5000";
    })();

    return request[method](`${baseURL}${url}`)
      .retry(2)
      .withCredentials() // ✅ FIXED: Correct SuperAgent syntax
      .timeout(30000);   // ✅ ADDED: Consistent timeout
    // Note: No Authorization header needed - backend uses HTTP-only cookies
  }, []);

  // State for machine data and related information
  const [machineData, setMachineData] = useState([]);
  const [previousMachineData, setPreviousMachineData] = useState([]);
  const [sideCardData, setSideCardData] = useState({});
  const [dailyStats, setDailyStats] = useState([]);
  const [selectedMachine, setSelectedMachine] = useState(null);
  const [machineHistory, setMachineHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [historyLoading, setHistoryLoading] = useState(false);
  const [historyError, setHistoryError] = useState(null);
  const [isHistoricalView, setIsHistoricalView] = useState(false);

  // State for WebSocket connection and status
  const [wsStatus, setWsStatus] = useState({
    connected: false,
    connecting: false,
    updating: false,
    reconnecting: false
  });

  // State to track machine session status
  const [sessionStatus, setSessionStatus] = useState({});

  // State for additional statistics
  const [operatorStats, setOperatorStats] = useState([]);
  const [productionStats, setProductionStats] = useState(null);

  // State for filters
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedShift, setSelectedShift] = useState("all");
  const [selectedView, setSelectedView] = useState("machines");

  /**
   * Formats machine data with calculated fields
   * @param {Array} machines - Raw machine data
   * @returns {Array} Formatted machine data
   */
  const formatMachineData = useCallback((machines) => {
    return machines.map((m) => {
      // Calculate status based on TRS
      const trs = safeParseFloat(m.TRS || "0");
      const status = trs > 80 ? "success" : trs > 60 ? "warning" : "error";

      // Calculate progress
      const quantiteBon = safeParseFloat(m.Quantite_Bon || "0");
      const quantitePlanifier = safeParseFloat(m.Quantite_Planifier || "0");
      const progress = (quantiteBon / (quantitePlanifier || 1)) * 100;

      return {
        ...m,
        status,
        progress,
      };
    });
  }, []);

  /**
   * Handles machine session updates
   * @param {Array} currentMachines - Current machine data
   * @param {Array} previousMachines - Previous machine data
   */
  const handleMachineSessions = useCallback(async (currentMachines, previousMachines) => {
    try {
      // Check authentication before making requests
      if (!isAuthenticated) {
        console.log('🔐 Skipping machine sessions update - user not authenticated');
        return;
      }

      // Get current active sessions
      const response = await createAuthRequest('get', '/api/activeSessions');
      const activeSessions = response.body;
      const activeSessionMap = {};

      // Create a map of active sessions by machine ID for quick lookup
      activeSessions.forEach((session) => {
        activeSessionMap[session.machine_id] = session;
      });

      // Create a map of previous machine states for comparison
      const prevMachineMap = {};
      previousMachines.forEach((machine) => {
        if (machine.id) {
          prevMachineMap[machine.id] = machine;
        }
      });

      // Process each machine to determine if we need to create, update, or stop a session
      for (const machine of currentMachines) {
        if (!machine.id) continue; // Skip machines without ID

        // Ensure the critical fields are present
        const machineData = {
          ...machine,
          // Ensure these fields are always present with at least a "0" value
          Regleur_Prenom: machine.Regleur_Prenom || "0",
          Quantite_Planifier: machine.Quantite_Planifier || "0",
          Quantite_Bon: machine.Quantite_Bon || "0",
          Quantite_Rejet: machine.Quantite_Rejet || "0",
          TRS: machine.TRS || "0",
          // Add new fields from real_time_table
          Poid_unitaire: machine.Poid_unitaire || "0",
          cycle_theorique: machine.cycle_theorique || "0",
          empreint: machine.empreint || "0",
          Etat: machine.Etat || "off",
          Code_arret: machine.Code_arret || "",
        };
        // We have the previous machine state available if needed
        // const prevMachine = prevMachineMap[machine.id];
        const hasActiveSession = !!activeSessionMap[machine.id];

        // Case 1: Machine is ON and has no active session (new session needed)
        if (machine.Etat === "on" && !hasActiveSession) {
          await createAuthRequest('post', "/api/createSession").send({
            machineId: machine.id,
            machineData: machineData,
          });

          // Update session status in state
          setSessionStatus((prev) => ({
            ...prev,
            [machine.id]: {
              active: true,
              startTime: new Date(),
              lastUpdate: new Date(),
            },
          }));

          // Don't show notification for new session
          console.log(`New session started for ${machine.Machine_Name}`);
        }
        // Case 2: Machine is ON and has an active session (update needed)
        else if (machine.Etat === "on" && hasActiveSession) {
          await createAuthRequest('post', "/api/updateSession").send({
            machineId: machine.id,
            machineData: machineData,
          });

          // Update session status in state
          setSessionStatus((prev) => ({
            ...prev,
            [machine.id]: {
              ...prev[machine.id],
              lastUpdate: new Date(),
            },
          }));
        }
        // Case 3: Machine was ON but is now OFF (stop session)
        else if (machine.Etat === "off" && hasActiveSession) {
          await createAuthRequest('post', "/api/stopSession").send({
            machineId: machine.id,
          });

          // Update session status in state
          setSessionStatus((prev) => {
            const newStatus = { ...prev };
            delete newStatus[machine.id];
            return newStatus;
          });

          // Don't show notification for stopped session
          console.log(`Session ended for ${machine.Machine_Name}`);
        }
        // Case 4: Machine is OFF and has no active session (no action needed)
      }
    } catch (error) {
      console.error("Error handling machine sessions:", error);
    }
  }, [isAuthenticated, createAuthRequest]);

  /**
   * Fetches data via REST API (fallback if WebSocket fails)
   */
  const fetchDataFallback = useCallback(async () => {
    try {
      // Check authentication before making requests
      if (!isAuthenticated) {
        console.log('🔐 Skipping data fetch - user not authenticated');
        setLoading(false);
        setError('Authentication required');
        return;
      }

      setLoading(true);

      const responses = await Promise.all([
        createAuthRequest('get', "/api/RealTimeTable"),
        createAuthRequest('get', "/api/MachineCard"),
        createAuthRequest('get', "/api/sidecards"),
        createAuthRequest('get', "/api/dailyStats"),
      ]);

      const [_, machineCards, sideCards, daily] = responses; // First response is unused

      // Save the previous state before updating
      setPreviousMachineData([...machineData]);

      const formattedMachineData = formatMachineData(machineCards.body);

      setMachineData(formattedMachineData);
      setSideCardData(sideCards.body[0] || {});
      setDailyStats(daily.body);
      setError(null);
      setLoading(false);
      setLastUpdate(new Date());

      // Pass the previous state to handleMachineSessions
      await handleMachineSessions(formattedMachineData, machineData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setError(error.message || "Failed to fetch data");
      setLoading(false);
      setLastUpdate(new Date());
    }
  }, [isAuthenticated, createAuthRequest, machineData, formatMachineData, handleMachineSessions]);

  /**
   * Fetches machine history data
   * @param {string|number} machineId - ID of the machine
   */
  const fetchMachineHistory = useCallback(async (machineId) => {
    if (!machineId) return;

    try {
      // Check authentication before making requests
      if (!isAuthenticated) {
        console.log('🔐 Skipping machine history fetch - user not authenticated');
        setHistoryError('Authentication required');
        return;
      }

      setHistoryLoading(true);
      setHistoryError(null);

      // Only use the machine sessions endpoint
      const sessionsResponse = await createAuthRequest('get', `/api/machineSessions/${machineId}`);

      if (Array.isArray(sessionsResponse.body) && sessionsResponse.body.length > 0) {
        // Process sessions data
        const processedData = sessionsResponse.body.map(session => ({
          ...session,
          timestamp: new Date(session.session_start),
          isActive: !session.session_end,
          highlight: !session.session_end
        }));

        setMachineHistory(processedData);
      } else {
        // No sessions found
        console.log("No sessions found for this machine");
        setMachineHistory([]);
        setHistoryError("No sessions found for this machine");
      }

      setHistoryLoading(false);
    } catch (error) {
      console.error("Error fetching machine history:", error);
      setHistoryError("No history data available for this machine");
      setHistoryLoading(false);
      setMachineHistory([]);
    }
  }, [isAuthenticated, createAuthRequest]);

  /**
   * Refreshes dashboard data or attempts to reconnect WebSocket
   */
  const handleRefresh = useCallback(() => {
    if (wsStatus.connected) {
      // If WebSocket is connected, request an update
      websocketService.requestUpdate();
      console.log("Requesting data update from server");
    } else if (wsStatus.connecting || wsStatus.reconnecting) {
      // If already connecting or reconnecting, just log it
      console.log("Connection already in progress");
    } else {
      // If disconnected and not connecting/reconnecting, attempt to reconnect
      setWsStatus(prev => ({ ...prev, connecting: true }));

      // Show notification for reconnection attempt
      notification.info({
        message: "Reconnecting",
        description: "Attempting to establish WebSocket connection",
        icon: React.createElement(ReloadOutlined, { spin: true, style: { color: "#1890ff" } }),
        placement: "bottomRight",
        duration: 2,
        key: "websocket-reconnecting"
      });

      // Attempt to reconnect
      websocketService.connect();
    }
  }, [wsStatus.connected, wsStatus.connecting, wsStatus.reconnecting]);

  /**
   * Fetches operator statistics
   */
  const fetchOperatorStats = useCallback(async () => {
    try {
      // Check authentication before making requests
      if (!isAuthenticated) {
        console.log('🔐 Skipping operator stats fetch - user not authenticated');
        return;
      }

      const response = await createAuthRequest('get', "/api/operator-stats");
      setOperatorStats(response.body);
    } catch (error) {
      console.error("Error fetching operator stats:", error);
    }
  }, [isAuthenticated, createAuthRequest]);

  /**
   * Fetches production statistics
   */
  const fetchProductionStats = useCallback(async () => {
    try {
      // Check authentication before making requests
      if (!isAuthenticated) {
        console.log('🔐 Skipping production stats fetch - user not authenticated');
        return;
      }

      const response = await createAuthRequest('get', "/api/production-stats");
      setProductionStats(response.body);
    } catch (error) {
      console.error("Error fetching production stats:", error);
    }
  }, [isAuthenticated, createAuthRequest]);

  // Setup WebSocket connection and event listeners
  useEffect(() => {
    // Only connect if not already connected
    if (!websocketService.isConnected) {
      // Update WebSocket status to connecting
      setWsStatus(prev => ({ ...prev, connecting: true }));

      // Connect to WebSocket server
      websocketService.connect();
    } else {
      // Already connected, just update the status and request data
      setWsStatus(prev => ({ ...prev, connected: true, connecting: false }));
      websocketService.requestUpdate();
    }

    // Set up event listeners for different types of messages
    const initialDataListener = (data) => {
      console.log("Received initial data from WebSocket");

      // Update WebSocket status - no longer loading
      setWsStatus(prev => ({ ...prev, connecting: false, updating: false }));

      // Format machine data with calculated fields
      const formattedMachineData = formatMachineData(data.machineData);

      // Update session status state
      const newSessionStatus = {};
      data.activeSessions.forEach((session) => {
        newSessionStatus[session.machine_id] = {
          active: true,
          startTime: new Date(session.session_start),
          lastUpdate: new Date(session.last_updated),
          sessionId: session.id
        };
      });
      setSessionStatus(newSessionStatus);

      // Update state with all fetched data
      setMachineData(formattedMachineData);
      setPreviousMachineData([...formattedMachineData]); // Save a copy for comparison
      setSideCardData(data.sideCardData || {});
      setDailyStats(data.dailyStats || []);
      setError(null);
      setLoading(false);
      setLastUpdate(new Date());

      // Don't show notification for initial data load
      console.log("Initial data loaded successfully");
    };

    const updateListener = (data) => {
      console.log("Received update from WebSocket", data);

      // Use a subtle indicator instead of a loading spinner
      setWsStatus(prev => ({ ...prev, updating: true }));

      // Set a timeout to reset the updating status after a short delay
      setTimeout(() => {
        setWsStatus(prev => ({ ...prev, updating: false }));
      }, 500);

      // Save the previous state before updating
      setPreviousMachineData([...machineData]);

      // Process the changed machines
      const changedMachines = data.data.changedMachines || [];
      const fullData = data.data.fullData || [];

      // Format the full data with calculated fields
      const formattedMachineData = formatMachineData(fullData);

      // Update the machine data in state WITHOUT setting loading: true
      setMachineData(formattedMachineData);
      setLastUpdate(new Date());

      // Process machine sessions after data is updated
      handleMachineSessions(formattedMachineData, machineData);

      // Don't show notifications for updates
      console.log(`${changedMachines.length} machine(s) updated`);
    };

    const sessionUpdateListener = (data) => {
      console.log("Received session update from WebSocket", data);

      const { sessionData, updateType } = data;
      const machineId = sessionData.machine_id;

      // Update the session status based on the update type
      if (updateType === 'created' || updateType === 'updated') {
        setSessionStatus(prev => ({
          ...prev,
          [machineId]: {
            active: true,
            startTime: new Date(sessionData.session_start),
            lastUpdate: new Date(sessionData.last_updated),
            sessionId: sessionData.id
          }
        }));

        // Don't show notifications for session updates
        console.log(`Session ${updateType} for ${sessionData.Machine_Name || 'machine ' + machineId}`);
      } else if (updateType === 'stopped') {
        // For stopped sessions, remove from active sessions
        setSessionStatus(prev => {
          const newStatus = { ...prev };
          delete newStatus[machineId];
          return newStatus;
        });

        // Don't show notifications for stopped sessions
        console.log(`Session stopped for ${sessionData.Machine_Name || 'machine ' + machineId}`);
      }
    };

    const connectListener = () => {
      console.log("WebSocket connected");
      // Update WebSocket status
      setWsStatus(prev => ({ ...prev, connected: true, connecting: false }));

      // Request initial data when connection is established
      websocketService.requestUpdate();

      // Don't show notification for connection established
      console.log("WebSocket connection established successfully");
    };

    const disconnectListener = () => {
      console.log("WebSocket disconnected");
      // Only update the status, don't trigger reconnection
      setWsStatus(prev => ({ ...prev, connected: false, connecting: false }));

      // Only show notification if it wasn't a clean disconnect (e.g., page unload)
      if (document.visibilityState === 'visible') {
        notification.warning({
          message: "Connexion perdue",
          description: "La connexion WebSocket a été interrompue",
          icon: React.createElement(ReloadOutlined, { style: { color: "#faad14" } }),
          placement: "bottomRight",
          duration: 4,
          key: "websocket-disconnected"
        });
      }

      // Log the disconnect but don't attempt to reconnect automatically
      console.log("WebSocket disconnected - No automatic reconnection");
    };

    const errorListener = (error) => {
      console.error("WebSocket error:", error);

      // Update WebSocket status
      setWsStatus(prev => ({ ...prev, connected: false, connecting: false }));

      notification.error({
        message: "Erreur de connexion",
        description: "Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",
        icon: React.createElement(CloseCircleOutlined, { style: { color: "#ff4d4f" } }),
        placement: "bottomRight",
        duration: 4,
        key: "websocket-error"
      });

      // Update state with error
      setError("Erreur de connexion WebSocket");

      // Do not fall back to HTTP polling for testing
      console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)");
    };

    // Register all event listeners
    const unsubscribeInitialData = websocketService.addEventListener('initialData', initialDataListener);
    const unsubscribeUpdate = websocketService.addEventListener('update', updateListener);
    const unsubscribeSessionUpdate = websocketService.addEventListener('sessionUpdate', sessionUpdateListener);
    const unsubscribeConnect = websocketService.addEventListener('connect', connectListener);
    const unsubscribeDisconnect = websocketService.addEventListener('disconnect', disconnectListener);
    const unsubscribeError = websocketService.addEventListener('error', errorListener);

    // Don't show connecting notification, just log it
    console.log("Establishing WebSocket connection...");

    // Fallback timeout is deactivated for testing WebSocket connection
    const fallbackTimeout = setTimeout(() => {
      if (!websocketService.isConnected) {
        console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)");

        // Only reset connection status to allow future reconnection attempts
        setWsStatus(prev => ({ ...prev, connecting: false }));

        // Don't show timeout notification, just log it
        console.log("WebSocket connection timeout - still attempting to connect");
      }
    }, 10000); // 10 second timeout

    // Cleanup function to remove event listeners when component unmounts
    return () => {
      clearTimeout(fallbackTimeout);
      unsubscribeInitialData();
      unsubscribeUpdate();
      unsubscribeSessionUpdate();
      unsubscribeConnect();
      unsubscribeDisconnect();
      unsubscribeError();

      // Don't disconnect the WebSocket when the component unmounts
      // This allows the connection to persist between page navigations
      console.log('useDashboardData unmounting - keeping WebSocket connection alive');

      // Only disconnect if the page is actually being unloaded
      window.addEventListener('beforeunload', () => {
        console.log('Page unloading, disconnecting WebSocket');
        websocketService.disconnect();
      }, { once: true });
    };
  }, [formatMachineData, handleMachineSessions, fetchDataFallback, machineData]);

  // Initial data fetch for operator and production stats
  useEffect(() => {
    fetchOperatorStats();
    fetchProductionStats();
  }, [fetchOperatorStats, fetchProductionStats]);

  return {
    // Data
    machineData,
    previousMachineData,
    sideCardData,
    dailyStats,
    selectedMachine,
    machineHistory,
    operatorStats,
    productionStats,
    sessionStatus,

    // Status
    loading,
    error,
    lastUpdate,
    historyLoading,
    historyError,
    wsStatus,
    isHistoricalView,

    // Filters
    selectedDate,
    selectedShift,
    selectedView,

    // Actions
    setSelectedMachine,
    fetchMachineHistory,
    handleRefresh,
    setSelectedDate,
    setSelectedShift,
    setSelectedView,
    formatMachineData
  };
};

export default useDashboardData;