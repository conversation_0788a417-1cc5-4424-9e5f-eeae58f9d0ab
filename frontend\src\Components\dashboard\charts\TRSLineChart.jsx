import React from "react";
import {
  <PERSON>sponsive<PERSON><PERSON>r,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip as RechartsTooltip,
} from "recharts";
import { Card, Empty } from "antd";
import dayjs from "dayjs";
import { normalizePercentage } from "../../../utils/dataUtils";

/**
 * Line chart component for displaying TRS (OEE) data
 * @param {Object} props - Component props
 * @param {Array} props.data - The data to display in the chart
 * @param {string} props.title - The title of the chart
 * @param {string} props.color - The color to use for the line
 * @returns {JSX.Element} The rendered chart
 */
const TRSLineChart = ({ data, title = "TRS", color = "#1890ff" }) => {
  return (
    <Card title={title} type="inner">
      {data && data.length > 0 ? (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tick={{ fill: "#666" }}
              tickFormatter={(date) => {
                // Safely format the date, handling invalid dates
                try {
                  if (date && dayjs(date).isValid()) {
                    // Format as DD/MM with year included in tooltip
                    return dayjs(date).format("DD/MM");
                  }
                  return "N/A";
                } catch (e) {
                  console.error("Error formatting date:", date, e);
                  return "N/A";
                }
              }}
            />
            <YAxis tickFormatter={(value) => `${value}%`} domain={[0, 100]} />
            <RechartsTooltip
              contentStyle={{
                backgroundColor: "#fff",
                border: "1px solid #f0f0f0",
                borderRadius: 8,
                boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
              }}
              formatter={(value) => {
                let parsed = parseFloat(value);
                const isValidNumber = !isNaN(parsed);

                // Handle decimal percentages (0-1 range)
                if (isValidNumber && parsed <= 1 && parsed > 0) {
                  parsed = normalizePercentage(parsed);
                }

                return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "TRS"];
              }}
              labelFormatter={(label) => {
                try {
                  if (label && dayjs(label).isValid()) {
                    return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                  }
                  return "Date: N/A";
                } catch (e) {
                  console.error("Error formatting tooltip date:", label, e);
                  return "Date: N/A";
                }
              }}
            />

            <Line
              type="monotone"
              dataKey="oee"
              name="TRS"
              stroke={color}
              strokeWidth={2}
              dot={{ r: 4, fill: color }}
              activeDot={{ r: 6, fill: "#fff", stroke: color, strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
          <Empty description="Aucune donnée disponible" />
        </div>
      )}
    </Card>
  );
};

export default TRSLineChart;
