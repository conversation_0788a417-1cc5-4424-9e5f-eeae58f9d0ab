#!/usr/bin/env node

/**
 * COMPREHENSIVE ELASTIC<PERSON>ARCH AND REDIS INTEGRATION AUDIT
 * 
 * Purpose: Perform complete audit of Elasticsearch and Redis integration
 * with fallback system verification in both dashboard pages
 */

import superagent from 'superagent';

const baseURL = 'http://localhost:5000';

/**
 * PHASE 1: ELASTICSEARCH INTEGRATION AUDIT
 */
async function auditElasticsearchIntegration() {
  console.log('🔍 PHASE 1: ELASTICSEARCH INTEGRATION AUDIT\n');
  
  const results = {
    healthStatus: null,
    productionDashboard: {
      elasticsearchUsage: false,
      fallbackMechanism: false,
      dataSourceTracking: false,
      searchCapabilities: false
    },
    arretsDashboard: {
      elasticsearchUsage: false,
      fallbackMechanism: false,
      dataSourceTracking: false,
      searchCapabilities: false
    },
    backendIntegration: {
      unifiedResolvers: false,
      elasticsearchServices: false,
      enhancedRoutes: false,
      healthChecks: false
    }
  };

  try {
    // Test Elasticsearch health endpoint
    console.log('1️⃣ Testing Elasticsearch health endpoint...');
    try {
      const healthResponse = await superagent
        .get(`${baseURL}/api/elasticsearch/health`)
        .timeout(5000);
      
      results.healthStatus = healthResponse.body;
      console.log(`   ✅ Elasticsearch Status: ${results.healthStatus.cluster_health || 'Unknown'}`);
    } catch (error) {
      console.log(`   ❌ Elasticsearch health check failed: ${error.message}`);
      results.healthStatus = { error: error.message };
    }

    // Test ProductionDashboard GraphQL queries with Elasticsearch
    console.log('\n2️⃣ Testing ProductionDashboard Elasticsearch integration...');
    try {
      const query = `
        query TestProductionElasticsearch {
          getDashboardData {
            productionChart {
              data {
                Date_Insert_Day
                Total_Good_Qty_Day
              }
              dataSource
            }
            machinePerformance {
              data {
                Machine_Name
                oee
              }
              dataSource
            }
          }
        }
      `;

      const graphqlResponse = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({ query })
        .timeout(10000);

      const data = graphqlResponse.body.data?.getDashboardData;
      
      if (data) {
        results.productionDashboard.dataSourceTracking = !!(data.productionChart?.dataSource || data.machinePerformance?.dataSource);
        results.productionDashboard.fallbackMechanism = data.productionChart?.dataSource === 'mysql' || data.machinePerformance?.dataSource === 'mysql';
        
        console.log(`   ✅ Production Chart Data Source: ${data.productionChart?.dataSource || 'unknown'}`);
        console.log(`   ✅ Machine Performance Data Source: ${data.machinePerformance?.dataSource || 'unknown'}`);
        console.log(`   ✅ Data Source Tracking: ${results.productionDashboard.dataSourceTracking ? 'Enabled' : 'Disabled'}`);
      }
    } catch (error) {
      console.log(`   ❌ ProductionDashboard GraphQL test failed: ${error.message}`);
    }

    // Test ArretsDashboard Elasticsearch integration
    console.log('\n3️⃣ Testing ArretsDashboard Elasticsearch integration...');
    try {
      const arretsQuery = `
        query TestArretsElasticsearch {
          getArretsDashboard {
            essentialStats {
              totalStops
              averageDuration
            }
            stopsData {
              id
              machineId
              stopReason
            }
          }
        }
      `;

      const arretsResponse = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({ query: arretsQuery })
        .timeout(10000);

      const arretsData = arretsResponse.body.data?.getArretsDashboard;
      
      if (arretsData) {
        results.arretsDashboard.elasticsearchUsage = true;
        console.log(`   ✅ Arrets Essential Stats: ${arretsData.essentialStats?.totalStops || 0} stops`);
        console.log(`   ✅ Stops Data Records: ${arretsData.stopsData?.length || 0}`);
      }
    } catch (error) {
      console.log(`   ❌ ArretsDashboard GraphQL test failed: ${error.message}`);
    }

    // Test backend integration endpoints
    console.log('\n4️⃣ Testing backend Elasticsearch integration...');
    try {
      // Test enhanced dashboard route
      const enhancedResponse = await superagent
        .get(`${baseURL}/api/enhanced/production/dashboard`)
        .timeout(5000);
      
      results.backendIntegration.enhancedRoutes = enhancedResponse.status === 200;
      console.log(`   ✅ Enhanced production route: ${enhancedResponse.body.source || 'Working'}`);
    } catch (error) {
      console.log(`   ❌ Enhanced production route failed: ${error.message}`);
    }

    // Test search service
    console.log('\n5️⃣ Testing search service integration...');
    try {
      const searchResponse = await superagent
        .get(`${baseURL}/api/search/health`)
        .timeout(5000);
      
      results.productionDashboard.searchCapabilities = searchResponse.body.elasticsearch?.status === 'healthy';
      results.arretsDashboard.searchCapabilities = searchResponse.body.elasticsearch?.status === 'healthy';
      
      console.log(`   ✅ Search service status: ${searchResponse.body.elasticsearch?.status || 'unknown'}`);
    } catch (error) {
      console.log(`   ❌ Search service test failed: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Elasticsearch audit failed:', error.message);
  }

  return results;
}

/**
 * PHASE 2: REDIS INTEGRATION AUDIT
 */
async function auditRedisIntegration() {
  console.log('\n🔍 PHASE 2: REDIS INTEGRATION AUDIT\n');
  
  const results = {
    healthStatus: null,
    caching: {
      graphqlCaching: false,
      restApiCaching: false,
      sessionManagement: false,
      pubSubMessaging: false
    },
    performance: {
      cacheHitRate: 0,
      avgResponseTime: 0,
      totalQueries: 0
    },
    fallbackBehavior: {
      redisDownHandling: false,
      gracefulDegradation: false,
      errorHandling: false
    }
  };

  try {
    // Test Redis health
    console.log('1️⃣ Testing Redis health status...');
    try {
      const redisHealthResponse = await superagent
        .get(`${baseURL}/api/health/redis`)
        .timeout(5000);
      
      results.healthStatus = redisHealthResponse.body;
      console.log(`   ✅ Redis Status: ${results.healthStatus.status || 'Unknown'}`);
      
      if (results.healthStatus.metrics) {
        results.performance = {
          cacheHitRate: results.healthStatus.metrics.cacheHitRate || 0,
          avgResponseTime: results.healthStatus.metrics.avgResponseTime || 0,
          totalQueries: results.healthStatus.metrics.totalQueries || 0
        };
        
        console.log(`   ✅ Cache Hit Rate: ${(results.performance.cacheHitRate * 100).toFixed(1)}%`);
        console.log(`   ✅ Avg Response Time: ${results.performance.avgResponseTime}ms`);
      }
    } catch (error) {
      console.log(`   ❌ Redis health check failed: ${error.message}`);
      results.healthStatus = { error: error.message };
    }

    // Test GraphQL caching
    console.log('\n2️⃣ Testing GraphQL Redis caching...');
    try {
      const startTime = Date.now();
      
      // First request (should cache)
      const firstRequest = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({
          query: `{ getAllDailyProduction { Machine_Name Good_Qty_Day } }`
        })
        .timeout(10000);
      
      const firstResponseTime = Date.now() - startTime;
      
      // Second request (should hit cache)
      const secondStartTime = Date.now();
      const secondRequest = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({
          query: `{ getAllDailyProduction { Machine_Name Good_Qty_Day } }`
        })
        .timeout(10000);
      
      const secondResponseTime = Date.now() - secondStartTime;
      
      results.caching.graphqlCaching = secondResponseTime < firstResponseTime;
      
      console.log(`   ✅ First request: ${firstResponseTime}ms`);
      console.log(`   ✅ Second request: ${secondResponseTime}ms`);
      console.log(`   ✅ Caching effective: ${results.caching.graphqlCaching ? 'Yes' : 'No'}`);
      
    } catch (error) {
      console.log(`   ❌ GraphQL caching test failed: ${error.message}`);
    }

    // Test session management
    console.log('\n3️⃣ Testing Redis session management...');
    try {
      const sessionResponse = await superagent
        .get(`${baseURL}/api/auth/profile`)
        .set('Cookie', 'test-session=active')
        .timeout(5000);
      
      results.caching.sessionManagement = sessionResponse.status === 200;
      console.log(`   ✅ Session management: ${results.caching.sessionManagement ? 'Working' : 'Failed'}`);
    } catch (error) {
      console.log(`   ⚠️  Session test: ${error.status === 401 ? 'Expected unauthorized' : 'Failed'}`);
      results.caching.sessionManagement = error.status === 401; // Expected for no valid session
    }

    // Test pub/sub capabilities
    console.log('\n4️⃣ Testing Redis pub/sub capabilities...');
    try {
      const pubsubResponse = await superagent
        .get(`${baseURL}/api/health/pubsub`)
        .timeout(5000);
      
      results.caching.pubSubMessaging = pubsubResponse.body.healthy;
      console.log(`   ✅ Pub/Sub messaging: ${results.caching.pubSubMessaging ? 'Healthy' : 'Unavailable'}`);
    } catch (error) {
      console.log(`   ❌ Pub/Sub test failed: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Redis audit failed:', error.message);
  }

  return results;
}

/**
 * PHASE 3: FALLBACK SYSTEM TESTING
 */
async function testFallbackSystems() {
  console.log('\n🔍 PHASE 3: FALLBACK SYSTEM TESTING\n');
  
  const results = {
    elasticsearchFallback: {
      dataAvailable: false,
      performanceImpact: 0,
      userExperienceIntact: false
    },
    redisFallback: {
      dataAvailable: false,
      performanceImpact: 0,
      userExperienceIntact: false
    },
    combinedFailure: {
      dataAvailable: false,
      gracefulDegradation: false
    }
  };

  try {
    // Test with simulated Elasticsearch failure
    console.log('1️⃣ Testing Elasticsearch fallback system...');
    try {
      // Force MySQL fallback by using specific parameter
      const fallbackQuery = `
        query TestElasticsearchFallback {
          getDashboardData(forceDataSource: "mysql") {
            productionChart {
              data {
                Date_Insert_Day
                Total_Good_Qty_Day
              }
              dataSource
            }
          }
        }
      `;

      const fallbackResponse = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({ query: fallbackQuery })
        .timeout(10000);

      const fallbackData = fallbackResponse.body.data?.getDashboardData;
      
      if (fallbackData && fallbackData.productionChart?.dataSource === 'mysql') {
        results.elasticsearchFallback.dataAvailable = true;
        results.elasticsearchFallback.userExperienceIntact = true;
        
        console.log(`   ✅ Fallback data source: ${fallbackData.productionChart.dataSource}`);
        console.log(`   ✅ Records available: ${fallbackData.productionChart.data?.length || 0}`);
      }
    } catch (error) {
      console.log(`   ❌ Elasticsearch fallback test failed: ${error.message}`);
    }

    // Test without Redis caching
    console.log('\n2️⃣ Testing Redis fallback behavior...');
    try {
      const startTime = Date.now();
      
      const nonCachedResponse = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({
          query: `{ getAllDailyProduction(bypassCache: true) { Machine_Name Good_Qty_Day } }`
        })
        .timeout(15000);

      const responseTime = Date.now() - startTime;
      
      if (nonCachedResponse.status === 200) {
        results.redisFallback.dataAvailable = true;
        results.redisFallback.userExperienceIntact = true;
        results.redisFallback.performanceImpact = responseTime;
        
        console.log(`   ✅ Data available without Redis: Yes`);
        console.log(`   ✅ Response time without cache: ${responseTime}ms`);
        console.log(`   ✅ Performance impact: ${responseTime > 3000 ? 'High' : responseTime > 1000 ? 'Medium' : 'Low'}`);
      }
    } catch (error) {
      console.log(`   ❌ Redis fallback test failed: ${error.message}`);
    }

    // Test combined service failure scenario
    console.log('\n3️⃣ Testing combined service failure scenario...');
    try {
      const combinedFailureQuery = `
        query TestCombinedFailure {
          getDashboardData(forceDataSource: "mysql", bypassCache: true) {
            productionChart {
              data {
                Date_Insert_Day
                Total_Good_Qty_Day
              }
              dataSource
            }
            sidecards {
              goodqty
              rejetqty
              dataSource
            }
          }
        }
      `;

      const combinedResponse = await superagent
        .post(`${baseURL}/api/graphql`)
        .send({ query: combinedFailureQuery })
        .timeout(15000);

      const combinedData = combinedResponse.body.data?.getDashboardData;
      
      if (combinedData) {
        results.combinedFailure.dataAvailable = true;
        results.combinedFailure.gracefulDegradation = 
          combinedData.productionChart?.dataSource === 'mysql' && 
          combinedData.sidecards?.dataSource === 'mysql';
        
        console.log(`   ✅ Data available with both services down: Yes`);
        console.log(`   ✅ Graceful degradation: ${results.combinedFailure.gracefulDegradation ? 'Working' : 'Failed'}`);
      }
    } catch (error) {
      console.log(`   ❌ Combined failure test failed: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Fallback system testing failed:', error.message);
  }

  return results;
}

/**
 * PHASE 4: GENERATE COMPREHENSIVE REPORT
 */
function generateAuditReport(elasticsearchResults, redisResults, fallbackResults) {
  console.log('\n📊 COMPREHENSIVE AUDIT REPORT');
  console.log('='.repeat(60));
  
  // Elasticsearch Status Summary
  console.log('\n🔍 ELASTICSEARCH INTEGRATION STATUS:');
  console.log(`   Health Status: ${elasticsearchResults.healthStatus?.cluster_health || 'Unknown'}`);
  console.log(`   ProductionDashboard Integration: ${elasticsearchResults.productionDashboard.dataSourceTracking ? '✅ Active' : '❌ Inactive'}`);
  console.log(`   ArretsDashboard Integration: ${elasticsearchResults.arretsDashboard.elasticsearchUsage ? '✅ Active' : '❌ Inactive'}`);
  console.log(`   Fallback Mechanism: ${elasticsearchResults.productionDashboard.fallbackMechanism ? '✅ Working' : '❌ Not Working'}`);
  console.log(`   Search Capabilities: ${elasticsearchResults.productionDashboard.searchCapabilities ? '✅ Available' : '❌ Unavailable'}`);

  // Redis Status Summary
  console.log('\n💾 REDIS INTEGRATION STATUS:');
  console.log(`   Health Status: ${redisResults.healthStatus?.status || 'Unknown'}`);
  console.log(`   GraphQL Caching: ${redisResults.caching.graphqlCaching ? '✅ Active' : '❌ Inactive'}`);
  console.log(`   Session Management: ${redisResults.caching.sessionManagement ? '✅ Working' : '❌ Not Working'}`);
  console.log(`   Pub/Sub Messaging: ${redisResults.caching.pubSubMessaging ? '✅ Available' : '❌ Unavailable'}`);
  console.log(`   Cache Hit Rate: ${(redisResults.performance.cacheHitRate * 100).toFixed(1)}%`);

  // Fallback Systems Summary
  console.log('\n🛡️ FALLBACK SYSTEMS STATUS:');
  console.log(`   Elasticsearch Fallback: ${fallbackResults.elasticsearchFallback.dataAvailable ? '✅ Functional' : '❌ Failed'}`);
  console.log(`   Redis Fallback: ${fallbackResults.redisFallback.dataAvailable ? '✅ Functional' : '❌ Failed'}`);
  console.log(`   Combined Failure Handling: ${fallbackResults.combinedFailure.gracefulDegradation ? '✅ Graceful' : '❌ Failed'}`);

  // Overall System Resilience Score
  const resilienceFactors = [
    elasticsearchResults.productionDashboard.fallbackMechanism,
    redisResults.caching.graphqlCaching,
    fallbackResults.elasticsearchFallback.dataAvailable,
    fallbackResults.redisFallback.dataAvailable,
    fallbackResults.combinedFailure.gracefulDegradation
  ];

  const resilienceScore = (resilienceFactors.filter(Boolean).length / resilienceFactors.length) * 100;

  console.log('\n🎯 OVERALL SYSTEM RESILIENCE:');
  console.log(`   Resilience Score: ${resilienceScore.toFixed(1)}%`);
  
  if (resilienceScore >= 90) {
    console.log('   Status: 🟢 EXCELLENT - System highly resilient to service failures');
  } else if (resilienceScore >= 70) {
    console.log('   Status: 🟡 GOOD - System moderately resilient, some improvements needed');
  } else if (resilienceScore >= 50) {
    console.log('   Status: 🟠 FAIR - System has basic resilience, significant improvements needed');
  } else {
    console.log('   Status: 🔴 POOR - System vulnerable to service failures, immediate action required');
  }

  return {
    elasticsearch: elasticsearchResults,
    redis: redisResults,
    fallback: fallbackResults,
    resilienceScore,
    recommendations: generateRecommendations(elasticsearchResults, redisResults, fallbackResults)
  };
}

/**
 * Generate recommendations based on audit results
 */
function generateRecommendations(elasticsearchResults, redisResults, fallbackResults) {
  const recommendations = [];

  if (!elasticsearchResults.productionDashboard.dataSourceTracking) {
    recommendations.push('🔧 Implement data source tracking for ProductionDashboard');
  }

  if (!elasticsearchResults.productionDashboard.fallbackMechanism) {
    recommendations.push('🔧 Implement Elasticsearch → MySQL fallback mechanism');
  }

  if (!redisResults.caching.graphqlCaching) {
    recommendations.push('🔧 Enable Redis caching for GraphQL queries');
  }

  if (redisResults.performance.cacheHitRate < 0.7) {
    recommendations.push('🔧 Optimize Redis cache strategies to improve hit rate');
  }

  if (!fallbackResults.elasticsearchFallback.dataAvailable) {
    recommendations.push('🔧 Fix Elasticsearch fallback system implementation');
  }

  if (!fallbackResults.redisFallback.dataAvailable) {
    recommendations.push('🔧 Ensure system works without Redis caching');
  }

  if (!fallbackResults.combinedFailure.gracefulDegradation) {
    recommendations.push('🔧 Implement graceful degradation for combined service failures');
  }

  if (recommendations.length === 0) {
    recommendations.push('✅ No critical issues found - system is well architected');
  }

  return recommendations;
}

/**
 * Main execution function
 */
(async () => {
  console.log('🚀 STARTING COMPREHENSIVE ELASTICSEARCH AND REDIS INTEGRATION AUDIT\n');
  
  try {
    const elasticsearchResults = await auditElasticsearchIntegration();
    const redisResults = await auditRedisIntegration();
    const fallbackResults = await testFallbackSystems();
    
    const finalReport = generateAuditReport(elasticsearchResults, redisResults, fallbackResults);
    
    console.log('\n📋 RECOMMENDATIONS:');
    finalReport.recommendations.forEach(rec => console.log(`   ${rec}`));
    
    console.log('\n✅ AUDIT COMPLETED SUCCESSFULLY');
    
    // Exit with appropriate code based on resilience score
    process.exit(finalReport.resilienceScore >= 70 ? 0 : 1);
    
  } catch (error) {
    console.error('\n❌ AUDIT FAILED:', error.message);
    process.exit(1);
  }
})();
