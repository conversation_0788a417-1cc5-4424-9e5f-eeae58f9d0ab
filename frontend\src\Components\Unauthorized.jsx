import React from "react";
import { Result, But<PERSON> } from "antd";
import { useNavigate } from "react-router-dom";

const Unauthorized = () => {
  const navigate = useNavigate();

  return (
    <Result
      status="403"
      title="403"
      subTitle="<PERSON><PERSON><PERSON><PERSON>, vous n'êtes pas autorisé à accéder à cette page."
      extra={
        <Button type="primary" onClick={() => navigate("/home")}>
          Retour à l'accueil
        </Button>
      }
    />
  );
};

export default Unauthorized;