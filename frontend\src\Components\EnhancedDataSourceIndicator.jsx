/**
 * Enhanced Data Source Status Component
 * Displays real-time data source status with interactive controls
 */

import React from 'react';
import { 
  Badge, 
  Tooltip, 
  Space, 
  Button, 
  Popover, 
  Typography, 
  Progress,
  Divider,
  Tag
} from 'antd';
import { 
  ThunderboltOutlined, 
  ToolOutlined, 
  InfoCircleOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text, Paragraph } = Typography;

const EnhancedDataSourceIndicator = ({ 
  status, 
  onRefresh, 
  onRetry, 
  isMonitoring = true 
}) => {
  const getStatusColor = () => {
    if (status.elasticsearch) return '#52c41a'; // Green for Elasticsearch
    if (status.mysql) return '#faad14'; // Orange for MySQL fallback
    return '#ff4d4f'; // Red for unknown/error
  };

  const getStatusText = () => {
    if (status.elasticsearch) return 'Elasticsearch';
    if (status.mysql) return 'MySQL Fallback';
    return 'Disconnected';
  };

  const getStatusIcon = () => {
    if (status.elasticsearch) return <ThunderboltOutlined />;
    if (status.mysql) return <ToolOutlined />;
    return <ExclamationCircleOutlined />;
  };

  const getPerformanceLevel = () => {
    if (status.elasticsearch) return { level: 'high', percent: 95, text: 'Optimal' };
    if (status.mysql) return { level: 'medium', percent: 75, text: 'Backup' };
    return { level: 'low', percent: 25, text: 'Limited' };
  };

  const performance = getPerformanceLevel();

  const detailsContent = (
    <div style={{ width: 300 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Data Source Status</Text>
          <div style={{ marginTop: 8 }}>
            <Space>
              {getStatusIcon()}
              <Text>{getStatusText()}</Text>
              <Tag color={status.elasticsearch ? 'green' : status.mysql ? 'orange' : 'red'}>
                {status.primary.toUpperCase()}
              </Tag>
            </Space>
          </div>
        </div>

        <Divider style={{ margin: '12px 0' }} />

        <div>
          <Text strong>Performance Level</Text>
          <div style={{ marginTop: 8 }}>
            <Progress
              percent={performance.percent}
              strokeColor={getStatusColor()}
              size="small"
              format={() => performance.text}
            />
          </div>
        </div>

        <div>
          <Text strong>Service Status</Text>
          <div style={{ marginTop: 8 }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>Elasticsearch:</Text>
                <Space>
                  {status.elasticsearch ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                  )}
                  <Text type={status.elasticsearch ? 'success' : 'danger'}>
                    {status.elasticsearch ? 'Active' : 'Unavailable'}
                  </Text>
                </Space>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>MySQL:</Text>
                <Space>
                  {status.mysql ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                  )}
                  <Text type={status.mysql ? 'success' : 'danger'}>
                    {status.mysql ? 'Active' : 'Unavailable'}
                  </Text>
                </Space>
              </div>
            </Space>
          </div>
        </div>

        {status.lastCheck && (
          <>
            <Divider style={{ margin: '12px 0' }} />
            <div>
              <Text strong>Last Check</Text>
              <div style={{ marginTop: 4 }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {dayjs(status.lastCheck).format('HH:mm:ss')}
                </Text>
              </div>
            </div>
          </>
        )}

        {status.retryCount > 0 && (
          <div>
            <Text strong>Retry Attempts</Text>
            <div style={{ marginTop: 4 }}>
              <Text type="warning">{status.retryCount}/5</Text>
            </div>
          </div>
        )}

        <Divider style={{ margin: '12px 0' }} />

        <Space>
          <Button 
            size="small" 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            type="primary"
          >
            Refresh
          </Button>
          {!status.elasticsearch && (
            <Button 
              size="small" 
              icon={<ThunderboltOutlined />} 
              onClick={onRetry}
              disabled={status.retryCount >= 5}
            >
              Retry ES
            </Button>
          )}
        </Space>

        <Paragraph style={{ margin: 0, fontSize: '11px' }} type="secondary">
          {status.elasticsearch 
            ? 'Using high-performance Elasticsearch for optimal data retrieval.'
            : 'Using MySQL fallback to ensure continuous data availability.'
          }
        </Paragraph>
      </Space>
    </div>
  );

  return (
    <Popover 
      content={detailsContent} 
      title="Data Source Monitor"
      trigger="hover"
      placement="bottomLeft"
    >
      <Tooltip title={`Data source: ${status.primary} • Click for details`}>
        <Badge
          count={
            <Space size={4}>
              {getStatusIcon()}
              <Text style={{ color: 'white', fontSize: '11px' }}>
                {getStatusText()}
              </Text>
              {isMonitoring && (
                <div 
                  style={{
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    backgroundColor: '#52c41a',
                    animation: 'pulse 2s infinite'
                  }}
                />
              )}
            </Space>
          }
          style={{ 
            backgroundColor: getStatusColor(),
            borderRadius: '12px',
            height: '24px',
            lineHeight: '24px',
            cursor: 'pointer',
            paddingLeft: '8px',
            paddingRight: '8px'
          }}
        />
      </Tooltip>
    </Popover>
  );
};

export default EnhancedDataSourceIndicator;
