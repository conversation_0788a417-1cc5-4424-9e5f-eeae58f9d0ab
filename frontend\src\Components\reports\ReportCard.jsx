
import React from "react" ;
import { Card, Typography, Space, Tag, Button, <PERSON>lt<PERSON>, Dropdown, Divider, Statistic } from "antd"
import {
  FileTextOutlined,
  DownloadOutlined,
  EyeOutlined,
  PrinterOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  Bar<PERSON>hartOutlined,
  <PERSON><PERSON>hartOutlined,
  ToolOutlined,
  DashboardOutlined,
  CheckCircleOutlined,
  AreaChartOutlined,
  SettingOutlined,
} from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text } = Typography

// Formats d'exportation disponibles
const exportFormats = [
  { key: "pdf", label: "PDF", icon: <FilePdfOutlined /> },
  { key: "excel", label: "Excel", icon: <FileExcelOutlined /> },
  { key: "csv", label: "CSV", icon: <FileTextOutlined /> },
]

const ReportCard = ({ report, onView, onExport, onPrint }) => {
  // Obtenir l'icône et la couleur en fonction du type de rapport
  const getReportTypeInfo = (type) => {
    const types = {
      shift: { icon: <ClockCircleOutlined />, color: "blue", label: "Rapport de quart" },
      daily: { icon: <CalendarOutlined />, color: "green", label: "Rapport journalier" },
      weekly: { icon: <BarChartOutlined />, color: "purple", label: "Rapport hebdomadaire" },
      monthly: { icon: <LineChartOutlined />, color: "cyan", label: "Rapport mensuel" },
      machine: { icon: <ToolOutlined />, color: "orange", label: "Rapport de machine" },
      production: { icon: <DashboardOutlined />, color: "geekblue", label: "Rapport de production" },
      maintenance: { icon: <ToolOutlined />, color: "gold", label: "Rapport de maintenance" },
      quality: { icon: <CheckCircleOutlined />, color: "lime", label: "Rapport de qualité" },
      financial: { icon: <AreaChartOutlined />, color: "magenta", label: "Rapport financier" },
      custom: { icon: <SettingOutlined />, color: "volcano", label: "Rapport personnalisé" },
    }

    return types[type] || { icon: <FileTextOutlined />, color: "default", label: type }
  }

  // Obtenir le statut du rapport
  const getStatusInfo = (status) => {
    const statuses = {
      completed: { color: "success", text: "Complété" },
      pending: { color: "processing", text: "En attente" },
      error: { color: "error", text: "Erreur" },
    }

    return statuses[status] || { color: "default", text: "Inconnu" }
  }

  const typeInfo = getReportTypeInfo(report.type)
  const statusInfo = getStatusInfo(report.status)

  return (
    <Card
      hoverable
      style={{ marginBottom: 16 }}
      actions={[
        <Tooltip title="Voir" key="view">
          <Button type="text" icon={<EyeOutlined />} onClick={() => onView(report)} />
        </Tooltip>,
        <Dropdown
          menu={{
            items: exportFormats.map((format) => ({
              key: format.key,
              icon: format.icon,
              label: format.label,
              onClick: () => onExport(report, format.key),
            })),
          }}
          trigger={["click"]}
          key="export"
        >
          <Tooltip title="Exporter">
            <Button type="text" icon={<DownloadOutlined />} />
          </Tooltip>
        </Dropdown>,
        <Tooltip title="Imprimer" key="print">
          <Button type="text" icon={<PrinterOutlined />} onClick={() => onPrint(report)} />
        </Tooltip>,
      ]}
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <Space>
          {typeInfo.icon}
          <Title level={4} style={{ margin: 0 }}>
            {report.title || typeInfo.label}
          </Title>
          <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
        </Space>

        <Text type="secondary">
          {report.description || `Rapport généré le ${dayjs(report.generatedAt).format("DD/MM/YYYY à HH:mm")}`}
        </Text>

        <Divider style={{ margin: "12px 0" }} />

        <Space wrap>
          <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} valueStyle={{ fontSize: "14px" }} />

          {report.shift && <Statistic title="Quart" value={report.shift} valueStyle={{ fontSize: "14px" }} />}

          {report.machineName && (
            <Statistic title="Machine" value={report.machineName} valueStyle={{ fontSize: "14px" }} />
          )}
        </Space>
      </Space>
    </Card>
  )
}

export default ReportCard

