/**
 * French Number Formatting Utilities
 * Formats numbers according to French conventions:
 * - Thousands separator: . (dot)
 * - Decimal separator: , (comma)
 * Examples: 12.909.438 Pcs, 84.872 Kg, 25,5 %
 */

/**
 * Format a number according to French conventions
 * @param {number|string} value - The number to format
 * @param {number} decimals - Number of decimal places (default: 0 for integers, 1 for percentages, 2 for precise values)
 * @param {boolean} forceDecimals - Whether to show decimals even if they are .00
 * @returns {string} Formatted number string
 */
export const formatFrenchNumber = (value, decimals = null, forceDecimals = false) => {
  // Handle invalid values
  if (value === null || value === undefined || value === '') return '0';
  
  // Convert to number
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '0';
  
  // Auto-determine decimal places if not specified
  let decimalPlaces = decimals;
  if (decimalPlaces === null) {
    // Check if it's a whole number or has decimals
    if (Number.isInteger(numValue)) {
      decimalPlaces = 0;
    } else {
      // For non-integers, use appropriate precision
      decimalPlaces = numValue < 10 ? 2 : 1;
    }
  }
  
  // Round to specified decimal places
  const roundedValue = Math.round(numValue * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
  
  // Split into integer and decimal parts
  const parts = roundedValue.toFixed(decimalPlaces).split('.');
  let integerPart = parts[0];
  const decimalPart = parts[1];
  
  // Add thousands separators (dots) to integer part
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  
  // Combine parts with French decimal separator (comma)
  if (decimalPlaces > 0 && (forceDecimals || decimalPart !== '00')) {
    // Remove trailing zeros unless forced
    const cleanDecimal = forceDecimals ? decimalPart : decimalPart.replace(/0+$/, '');
    if (cleanDecimal.length > 0) {
      return `${integerPart},${cleanDecimal}`;
    }
  }
  
  return integerPart;
};

/**
 * Format integer values (no decimals)
 * @param {number|string} value - The number to format
 * @returns {string} Formatted integer string
 */
export const formatFrenchInteger = (value) => {
  return formatFrenchNumber(value, 0, false);
};

/**
 * Format percentage values (1 decimal place)
 * @param {number|string} value - The percentage value to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} Formatted percentage string
 */
export const formatFrenchPercentage = (value, decimals = 1) => {
  return formatFrenchNumber(value, decimals, false);
};

/**
 * Format decimal values (2 decimal places by default)
 * @param {number|string} value - The number to format
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted decimal string
 */
export const formatFrenchDecimal = (value, decimals = 2) => {
  return formatFrenchNumber(value, decimals, false);
};

/**
 * Format weight values (typically in kg)
 * @param {number|string} value - The weight value to format
 * @returns {string} Formatted weight string
 */
export const formatFrenchWeight = (value) => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue) || numValue === 0) return '0';
  
  // Use 1 decimal place for weights unless it's a whole number
  const decimals = Number.isInteger(numValue) ? 0 : 1;
  return formatFrenchNumber(value, decimals, false);
};

/**
 * Format time duration values (hours, minutes, seconds)
 * @param {number|string} value - The time value to format
 * @param {string} unit - The time unit ('h', 'min', 'sec')
 * @returns {string} Formatted time string
 */
export const formatFrenchTime = (value, unit = 'h') => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '0';
  
  // Use 2 decimal places for time values
  return formatFrenchNumber(value, 2, false);
};

/**
 * Format value with suffix (like Pcs, Kg, %, etc.)
 * @param {number|string} value - The value to format
 * @param {string} suffix - The suffix to append
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted value with suffix
 */
export const formatWithSuffix = (value, suffix = '', decimals = null) => {
  const formattedValue = formatFrenchNumber(value, decimals);
  return suffix ? `${formattedValue} ${suffix}` : formattedValue;
};

/**
 * Format statistics card values based on suffix type
 * @param {number|string} value - The value to format
 * @param {string} suffix - The suffix (Pcs, Kg, %, etc.)
 * @returns {string} Formatted value
 */
export const formatStatValue = (value, suffix = '') => {
  const lowerSuffix = suffix.toLowerCase();
  
  if (lowerSuffix === '%') {
    return formatFrenchPercentage(value, 1);
  } else if (lowerSuffix === 'kg') {
    return formatFrenchWeight(value);
  } else if (lowerSuffix === 'pcs' || lowerSuffix === 'pieces') {
    return formatFrenchInteger(value);
  } else if (lowerSuffix === 'h' || lowerSuffix === 'hours') {
    return formatFrenchTime(value);
  } else if (lowerSuffix === 'min' || lowerSuffix === 'minutes') {
    return formatFrenchTime(value);
  } else if (lowerSuffix === 'sec' || lowerSuffix === 'seconds') {
    return formatFrenchTime(value);
  } else {
    // Default formatting for other units
    return formatFrenchNumber(value);
  }
};

// Export all functions
export default {
  formatFrenchNumber,
  formatFrenchInteger,
  formatFrenchPercentage,
  formatFrenchDecimal,
  formatFrenchWeight,
  formatFrenchTime,
  formatWithSuffix,
  formatStatValue
};
