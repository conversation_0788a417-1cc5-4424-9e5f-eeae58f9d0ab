import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { Grid } from 'antd';

const { useBreakpoint } = Grid;

/**
 * Layout Context for managing application layout state
 * Provides sidebar state, dimensions, and z-index management
 */
const LayoutContext = createContext();

/**
 * Hook to access layout context
 * @returns {Object} Layout context value
 */
export const useLayout = () => {
  const context = useContext(LayoutContext);
  
  if (context === undefined) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  
  return context;
};

/**
 * Layout Provider Component
 * Manages global layout state and provides it to child components
 */
export const LayoutProvider = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false); // false = sidebar open by default
  const [broken, setBroken] = useState(false);
  const screens = useBreakpoint();

  // Layout constants
  const LAYOUT_CONSTANTS = {
    SIDEBAR_WIDTH: 260,
    COLLAPSED_WIDTH: 80,
    HEADER_HEIGHT: 64,
    Z_INDEX: {
      SIDEBAR: 1001,
      HEADER: 1000,
      MOBILE_OVERLAY: 999,
      MODAL_MASK: 10000,
      MODAL: 10001,
      MODAL_CONTENT: 10002,
      FULLSCREEN_MODAL: 10010 // Higher than everything for fullscreen mode
    }
  };

  // Responsive state
  const isMobile = broken || !screens.md;
  const isTablet = screens.md && !screens.lg;
  const isDesktop = screens.lg;

  // Calculate layout dimensions
  const layoutDimensions = useMemo(() => {
    const sidebarWidth = isMobile ? 0 : (collapsed ? LAYOUT_CONSTANTS.COLLAPSED_WIDTH : LAYOUT_CONSTANTS.SIDEBAR_WIDTH);
    
    return {
      sidebarWidth,
      contentWidth: `calc(100vw - ${sidebarWidth}px)`,
      contentLeft: sidebarWidth,
      availableWidth: window.innerWidth - sidebarWidth,
      availableHeight: window.innerHeight - LAYOUT_CONSTANTS.HEADER_HEIGHT,
      headerHeight: LAYOUT_CONSTANTS.HEADER_HEIGHT
    };
  }, [collapsed, isMobile, LAYOUT_CONSTANTS.COLLAPSED_WIDTH, LAYOUT_CONSTANTS.SIDEBAR_WIDTH, LAYOUT_CONSTANTS.HEADER_HEIGHT]);

  // Modal positioning utilities
  const getModalDimensions = useMemo(() => {
    return (isFullscreen = false) => {
      if (isFullscreen) {
        return {
          width: '100vw',
          height: '100vh',
          top: 0,
          left: 0,
          padding: 0,
          borderRadius: 0
        };
      }

      if (isMobile) {
        return {
          width: '100vw',
          height: '100vh',
          top: 0,
          left: 0,
          padding: 0,
          borderRadius: 0
        };
      }

      // For desktop and tablet, account for sidebar
      const availableWidth = layoutDimensions.availableWidth;
      const modalWidth = Math.min(availableWidth * 0.9, 1200); // Max 90% of available width or 1200px
      const modalHeight = Math.min(window.innerHeight * 0.85, 800); // Max 85% of viewport height or 800px

      return {
        width: modalWidth,
        height: modalHeight,
        top: Math.max(20, (window.innerHeight - modalHeight) / 2),
        left: layoutDimensions.contentLeft + Math.max(20, (availableWidth - modalWidth) / 2),
        padding: isTablet ? '8px' : '16px',
        borderRadius: isTablet ? '12px' : '16px'
      };
    };
  }, [isMobile, isTablet, layoutDimensions]);

  // Z-index management
  const getZIndex = useMemo(() => {
    return (layer) => {
      return LAYOUT_CONSTANTS.Z_INDEX[layer] || 1000;
    };
  }, [LAYOUT_CONSTANTS.Z_INDEX]);

  // Update broken state based on screen size
  useEffect(() => {
    setBroken(!screens.lg);
    // Only auto-collapse on mobile (md and below), not on tablet (lg)
    // This preserves user preference on desktop/tablet sizes
    if (!screens.md) {
      setCollapsed(true);
    }
  }, [screens.md]);

  // Context value
  const value = useMemo(() => ({
    // Layout state
    collapsed,
    setCollapsed,
    broken,
    setBroken,
    
    // Responsive state
    isMobile,
    isTablet,
    isDesktop,
    screens,
    
    // Layout dimensions
    layoutDimensions,
    
    // Constants
    LAYOUT_CONSTANTS,
    
    // Utilities
    getModalDimensions,
    getZIndex,
    
    // Convenience methods
    getSidebarWidth: () => layoutDimensions.sidebarWidth,
    getContentWidth: () => layoutDimensions.contentWidth,
    getAvailableWidth: () => layoutDimensions.availableWidth,
    getAvailableHeight: () => layoutDimensions.availableHeight,
    
    // Modal helpers
    getModalZIndex: () => getZIndex('MODAL'),
    getModalMaskZIndex: () => getZIndex('MODAL_MASK'),
    getModalContentZIndex: () => getZIndex('MODAL_CONTENT'),
    
    // Layout integration helpers
    isLayoutReady: () => !broken || collapsed !== undefined,
    shouldUseFixedPositioning: () => !isMobile,
    getModalContainer: () => document.body, // Always use body for proper z-index stacking
    
  }), [
    collapsed,
    broken,
    isMobile,
    isTablet,
    isDesktop,
    screens,
    layoutDimensions,
    LAYOUT_CONSTANTS,
    getModalDimensions,
    getZIndex
  ]);

  return (
    <LayoutContext.Provider value={value}>
      {children}
    </LayoutContext.Provider>
  );
};

export default LayoutContext;
