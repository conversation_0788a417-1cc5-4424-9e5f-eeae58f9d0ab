/**
 * GraphQL resolvers for machine stop table data  
 * Recreates functionality from stopTable.js routes as GraphQL endpoints
 */

import { GraphQLObjectType, GraphQLString, GraphQLInt, GraphQLFloat, GraphQLList, GraphQLSchema, GraphQLInputObjectType, GraphQLBoolean } from 'graphql';
import { executeQuery } from '../../utils/dbUtils.js';
import redisEnhancedResolvers from '../../services/RedisEnhancedResolvers.js';

// Helper function to handle machine filtering consistently - SIMPLIFIED LIKE REST API
const addMachineFilters = (conditions, queryParams, filters) => {
  const { model, machine } = filters;

  // Enhanced debug logging for filter application
  console.log('🔧 MACHINE FILTER DETAIL:', {
    receivedModel: model,
    receivedMachine: machine,
    modelTruthy: !!model,
    machineTruthy: !!machine,
    willUseExactMatch: !!machine,
    willUseLikeMatch: !machine && !!model
  });

  if (machine) {
    // If a specific machine is selected, use exact match
    conditions.push(`Machine_Name = ?`);
    queryParams.push(machine);
    console.log(`✅ Added EXACT match filter for machine: ${machine}`);
  } else if (model) {
    // If only model is selected, use pattern matching
    conditions.push(`Machine_Name LIKE ?`);
    queryParams.push(`${model}%`);
    console.log(`✅ Added LIKE match filter for model: ${model}%`);
  }
  
  console.log('🔧 Final machine filter conditions:', {
    conditionsAdded: conditions.length > 0,
    lastCondition: conditions[conditions.length - 1] || 'None',
    lastParam: queryParams[queryParams.length - 1] || 'None'
  });
  // No default filter - show all machines if no filter is provided
};

// Helper function to handle date range filtering consistently with robust date parsing
const addDateRangeFilter = (conditions, queryParams, filters) => {
  const { date, startDate, endDate, dateRangeType = "day" } = filters;

  // Enhanced debugging for date filters
  console.log('� DATE FILTER DEBUG:', {
    receivedDate: date,
    receivedStartDate: startDate,
    receivedEndDate: endDate,
    receivedDateRangeType: dateRangeType,
    hasDate: !!date,
    hasDateRange: !!(startDate && endDate),
    startDateTruthy: !!startDate,
    endDateTruthy: !!endDate
  });

  // Robust date parsing that handles various formats in the database
  // Handles formats like: "07/09/2024 12:16", " 3/12/2024 09:55:38", "29/04/2025  19:47:37"
  const parseDateColumn = `
    COALESCE(
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i'), '%Y-%m-%d')
    )
  `;

  // If startDate and endDate are provided, use them directly
  if (startDate && endDate && startDate !== 'null' && endDate !== 'null') {
    conditions.push(`${parseDateColumn} BETWEEN ? AND ?`);
    queryParams.push(startDate, endDate);
    console.log('✅ Applied date range filter:', { startDate, endDate });
    return;
  }

  // Fall back to original date logic
  if (date && date !== 'null') {
    if (dateRangeType === "day") {
      conditions.push(`${parseDateColumn} = ?`);
      queryParams.push(date);
      console.log('✅ Applied single day filter:', { date });
    } else if (dateRangeType === "week") {
      // SIMPLIFIED week logic
      conditions.push(`${parseDateColumn} >= ?`);
      queryParams.push(date); // Just use the date as is for startDate
      
      // Calculate end date (7 days later)
      const startDateObj = new Date(date);
      const endDateObj = new Date(startDateObj);
      endDateObj.setDate(startDateObj.getDate() + 6);
      const endDateStr = endDateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD
      
      conditions.push(`${parseDateColumn} <= ?`);
      queryParams.push(endDateStr);
      
      console.log('✅ Applied week filter:', { startDate: date, endDate: endDateStr });
    } else if (dateRangeType === "month") {
      // SIMPLIFIED month logic - use year/month extraction
      // Extract year and month parts
      const yearMonth = date.substring(0, 7); // Gets "YYYY-MM" part
      
      conditions.push(`${parseDateColumn} LIKE ?`);
      queryParams.push(`${yearMonth}%`);
      
      console.log('✅ Applied month filter:', { yearMonthPattern: `${yearMonth}%` });
    }
  }
  
  // If no date filters were applied, log it
  if (!date && !startDate && !endDate) {
    console.log('� No date filters applied');
  }
  
  console.log('📅 Final date filter conditions:', {
    conditionsAdded: conditions.length > 0,
    latestCondition: conditions[conditions.length - 1] || 'None',
    latestParams: queryParams.slice(-2) || 'None'
  });
};

// GraphQL Types
const MachineStopType = new GraphQLObjectType({
  name: 'MachineStop',
  fields: {
    Machine_Name: { type: GraphQLString },
    Date_Insert: { type: GraphQLString },
    Part_NO: { type: GraphQLString },
    Code_Stop: { type: GraphQLString },
    Debut_Stop: { type: GraphQLString },
    Fin_Stop_Time: { type: GraphQLString },
    Regleur_Prenom: { type: GraphQLString },
    duration_minutes: { type: GraphQLInt },
    // Add calculated/derived fields that the frontend expects
    Cause: { 
      type: GraphQLString,
      resolve: (parent) => parent.Code_Stop || parent.Cause // Map Code_Stop to Cause
    },
    Raison_Arret: { 
      type: GraphQLString,
      resolve: (parent) => parent.Code_Stop || parent.Raison_Arret // Map Code_Stop to Raison_Arret
    },
    Operateur: { 
      type: GraphQLString,
      resolve: (parent) => parent.Regleur_Prenom || parent.Operateur // Map Regleur_Prenom to Operateur
    }
  }
});

const TopStopType = new GraphQLObjectType({
  name: 'TopStop',
  fields: {
    stopName: { type: GraphQLString },
    count: { type: GraphQLInt }
  }
});

const StopStatsType = new GraphQLObjectType({
  name: 'StopStats',
  fields: {
    Stop_Date: { type: GraphQLString },
    Total_Stops: { type: GraphQLInt }
  }
});

const StopReasonType = new GraphQLObjectType({
  name: 'StopReason',
  fields: {
    reason: { type: GraphQLString },
    count: { type: GraphQLInt }
  }
});

const StopDurationTrendType = new GraphQLObjectType({
  name: 'StopDurationTrend',
  fields: {
    hour: { type: GraphQLInt },
    avgDuration: { type: GraphQLFloat }
  }
});

const MachineStopComparisonType = new GraphQLObjectType({
  name: 'MachineStopComparison',
  fields: {
    Machine_Name: { type: GraphQLString },
    stops: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat }
  }
});

const OperatorStopStatsType = new GraphQLObjectType({
  name: 'OperatorStopStats',
  fields: {
    operator: { type: GraphQLString },
    interventions: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat }
  }
});

const StopSideCardType = new GraphQLObjectType({
  name: 'StopSideCard',
  fields: {
    Arret_Totale: { type: GraphQLInt },
    Arret_Totale_nondeclare: { type: GraphQLInt }
  }
});

const MachineModelType = new GraphQLObjectType({
  name: 'StopMachineModel',
  fields: {
    model: { type: GraphQLString }
  }
});

const MachineNameType = new GraphQLObjectType({
  name: 'StopMachineName',
  fields: {
    Machine_Name: { type: GraphQLString }
  }
});

// Input Types
const StopFilterInputType = new GraphQLInputObjectType({
  name: 'StopFilterInput',
  fields: {
    date: { type: GraphQLString },
    startDate: { type: GraphQLString },
    endDate: { type: GraphQLString },
    dateRangeType: { type: GraphQLString },
    model: { type: GraphQLString },
    machine: { type: GraphQLString },
    dateRange: { type: GraphQLString },
    page: { type: GraphQLInt },
    limit: { type: GraphQLInt }
  }
});

// Resolvers
const stopTableResolvers = {
  // Get all machine stops - Enhanced with Redis caching
  getAllMachineStops: redisEnhancedResolvers.createCachedResolver(
    'getAllMachineStops',
    {
      type: new GraphQLList(MachineStopType),
      args: {
        filters: { type: StopFilterInputType }
      },
      resolve: async (_, { filters = {} }) => {
      console.log('🔍 getAllMachineStops called with filters:', JSON.stringify(filters, null, 2));
      
      // DEBUG: Enhanced filter logging for machine selection debug
      if (filters.machine) {
        console.log('🔍🔍 MACHINE FILTER DETECTED:', {
          machine: filters.machine,
          exactMatchWillBeUsed: true
        });
      } else if (filters.model) {
        console.log('🔍🔍 MODEL FILTER DETECTED:', {
          model: filters.model,
          likeMatchWillBeUsed: true
        });
      }
      
      // Performance optimization: Detect complex queries that might cause freezing
      const hasAllFilters = filters.model && filters.machine && (filters.startDate || filters.date);
      const isComplexQuery = hasAllFilters || Object.keys(filters).length > 2;
      
      // Adaptive limits based on query complexity
      const getQueryLimit = () => {
        if (hasAllFilters) return 500; // Very restrictive for triple filters
        if (isComplexQuery) return 1000; // Moderate for complex queries
        return 2000; // Normal limit for simple queries
      };
      
      const queryLimit = filters.limit || getQueryLimit();
      console.log(`🎯 Query analysis: hasAllFilters=${hasAllFilters}, isComplex=${isComplexQuery}, limit=${queryLimit}`);
      
      try {
        // Reduced timeout for complex queries to prevent hanging
        const timeoutDuration = isComplexQuery ? 15000 : 30000;
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error(`Database query timeout after ${timeoutDuration}ms`)), timeoutDuration)
        );
        
        // First, test if we can query the database at all
        const testQuery = `SELECT COUNT(*) as total FROM machine_stop_table_mould LIMIT 1`;
        console.log('🧪 Testing database connection...');
        const testResult = await Promise.race([
          executeQuery(testQuery, []),
          timeoutPromise
        ]);
        console.log('🧪 Test result:', testResult);
        
        const { date } = filters;

        // SIMPLIFIED QUERY - LIKE REST API
        // Remove complex date parsing and duration calculations to prevent freezing
        let query = `SELECT * FROM machine_stop_table_mould`;
        const queryParams = [];
        const conditions = [];

        // SIMPLIFIED MACHINE FILTERING - LIKE REST API
        addMachineFilters(conditions, queryParams, filters);
        console.log('🏭 After machine filters - conditions:', conditions, 'params:', queryParams);

        // ENHANCED DATE FILTERING - USE THE HELPER FUNCTION CONSISTENTLY
        console.log('📅📅 Date filter details:', {
          date: filters.date,
          startDate: filters.startDate,
          endDate: filters.endDate,
          dateRangeType: filters.dateRangeType,
          hasAnyDateFilter: !!(filters.date || filters.startDate || filters.endDate)
        });
        
        // Use the consistent helper function for date filtering
        addDateRangeFilter(conditions, queryParams, filters);
        console.log('📅 After date filters - conditions:', conditions, 'params:', queryParams);
        
        // Additional check: if we have date filters but no conditions were added, something went wrong
        if ((filters.date || filters.startDate || filters.endDate) && !conditions.some(c => c.includes('Date_Insert'))) {
          console.error('⚠️ Date filter was provided but no date condition was added to the query!');
        }

        // Add WHERE clause if there are conditions
        if (conditions.length > 0) {
          query += ` WHERE ` + conditions.join(" AND ");
        }

        // Simple ORDER BY and LIMIT - like REST API
        query += ` ORDER BY Date_Insert DESC LIMIT ${queryLimit}`;

        console.log('📝 SIMPLIFIED Final query:', query);
        console.log('📝 Query params:', queryParams);

        const startTime = Date.now();
        const { success, data, error } = await Promise.race([
          executeQuery(query, queryParams),
          timeoutPromise
        ]);
        const executionTime = Date.now() - startTime;

        console.log(`💾 Query completed in ${executionTime}ms:`, { success, dataCount: data?.length || 0, error });
        
        // Performance warnings
        if (executionTime > 5000) {
          console.warn(`⚠️ Slow query detected: ${executionTime}ms for ${data?.length || 0} records`);
        }
        
        if (data && data.length > 400 && hasAllFilters) {
          console.warn(`⚠️ Large dataset with all filters: ${data.length} records - potential performance impact`);
        }
        
        if (data && data.length > 0) {
          console.log('📋 Sample record:', data[0]);
        }

        if (!success) {
          throw new Error(`Database query failed: ${error}`);
        }

        // Calculate duration_minutes for each record
        const dataWithDuration = data.map(record => {
          let duration_minutes = 0;
          
          try {
            if (record.Debut_Stop && record.Fin_Stop_Time) {
              // Parse the different date formats in the database
              const parseDateTime = (dateStr) => {
                if (!dateStr) return null;
                
                const str = dateStr.toString().trim();
                
                // Format: "03/12/2024 09:55" or " 3/12/2024 09:55:38"
                const match1 = str.match(/^\s*(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?/);
                if (match1) {
                  const [_, day, month, year, hours, minutes, seconds = 0] = match1;
                  return new Date(year, month - 1, day, hours, minutes, seconds);
                }
                
                // Format: "2024-12-03 09:55:38"
                if (str.match(/^\d{4}-\d{2}-\d{2}\s+\d{1,2}:\d{1,2}/)) {
                  return new Date(str);
                }
                
                return null;
              };
              
              const startTime = parseDateTime(record.Debut_Stop);
              const endTime = parseDateTime(record.Fin_Stop_Time);
              
              if (startTime && endTime && !isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                duration_minutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
              }
            }
          } catch (error) {
            console.warn('⚠️ Error calculating duration for record:', record.ID_Duree, error.message);
          }
          
          return {
            ...record,
            duration_minutes
          };
        });

        console.log('⏱️ Duration calculation completed:', {
          totalRecords: dataWithDuration.length,
          recordsWithDuration: dataWithDuration.filter(r => r.duration_minutes > 0).length,
          sampleDurations: dataWithDuration.slice(0, 3).map(r => ({ 
            id: r.ID_Duree, 
            start: r.Debut_Stop, 
            end: r.Fin_Stop_Time, 
            duration: r.duration_minutes 
          }))
        });

        return dataWithDuration;
      } catch (error) {
        console.error('❌ getAllMachineStops error:', error);
        throw new Error(`Failed to fetch machine stops: ${error.message}`);
      }
    }
  },
  { ttl: 300, keyPrefix: 'stops' }
  ),

  // Get top 5 stops - Enhanced with Redis caching
  getTop5Stops: redisEnhancedResolvers.createCachedResolver(
    'getTop5Stops',
    {
      type: new GraphQLList(TopStopType),
      args: {
        filters: { type: StopFilterInputType }
      },
      resolve: async (_, { filters = {} }) => {
      let query = `
        SELECT
          Code_Stop AS stopName,
          COUNT(*) AS count
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add date range filter if provided
      if (filters.date) {
        addDateRangeFilter(conditions, queryParams, filters);
      }

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY Code_Stop
        ORDER BY count DESC
        LIMIT 5
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },
  { ttl: 300, keyPrefix: 'stops' }
  ),

  // Get stop statistics - Enhanced with Redis caching
  getStopStats: redisEnhancedResolvers.createCachedResolver(
    'getStopStats',
    {
      type: new GraphQLList(StopStatsType),
      args: {
        filters: { type: StopFilterInputType }
      },
      resolve: async (_, { filters = {} }) => {
      const { dateRange } = filters;

      let query = `
        SELECT
          DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS Stop_Date,
          COUNT(*) AS Total_Stops
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add date range filter if provided
      if (dateRange) {
        const [startDate, endDate] = dateRange.split(",");
        if (startDate && endDate) {
          conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') BETWEEN ? AND ?`);
          queryParams.push(startDate, endDate);
        } else if (startDate) {
          conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') >= ?`);
          queryParams.push(startDate);
        }
      }

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY Stop_Date
        ORDER BY Stop_Date DESC
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },
  { ttl: 300, keyPrefix: 'stops' }
  ),

  // Get unique dates - Enhanced with Redis caching
  getUniqueStopDates: redisEnhancedResolvers.createCachedResolver(
    'getUniqueStopDates',
    {
      type: new GraphQLList(GraphQLString),
      args: {
        filters: { type: StopFilterInputType }
      },
      resolve: async (_, { filters = {} }) => {
      let query = `
        SELECT DISTINCT
          DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS date
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += ` ORDER BY date DESC`;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data.map((row) => row.date);
    }
  },
  { ttl: 1800, keyPrefix: 'stops' }
  ),

  // Get stop details by date
  getStopDetailsByDate: {
    type: new GraphQLList(MachineStopType),
    args: {
      date: { type: GraphQLString },
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { date, filters = {} }) => {
      let query = `
        SELECT
          Machine_Name,
          Regleur_Prenom,
          Code_Stop,
          Date_Insert,
          Debut_Stop,
          Fin_Stop_Time,
          Part_NO,
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ) AS duration_minutes
        FROM machine_stop_table_mould
        WHERE
          DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?
      `;
      const queryParams = [date];
      const conditions = [`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`];

      // Apply machine filtering with default
      const { model, machine } = filters;

      if (model) {
        conditions.push(`Machine_Name LIKE ?`);
        queryParams.push(`${model}%`);
      } else if (machine) {
        conditions.push(`Machine_Name = ?`);
        queryParams.push(machine);
      } else {
        conditions.push(`Machine_Name = ?`);
        queryParams.push("IPS01");
      }

      // Rebuild query with all conditions
      query = `
        SELECT
          Machine_Name,
          Regleur_Prenom,
          Code_Stop,
          Date_Insert,
          Debut_Stop,
          Fin_Stop_Time,
          Part_NO,
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ) AS duration_minutes
        FROM machine_stop_table_mould
        WHERE ${conditions.join(" AND ")}
        ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },
  // Get stop sidecards
  getStopSidecards: {
    type: StopSideCardType,
    args: {
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      console.log('📊 getStopSidecards called with filters:', {
        filters,
        filtersJSON: JSON.stringify(filters, null, 2)
      });

      // Total stops query
      let totalQuery = `SELECT COUNT(*) AS Arret_Totale FROM machine_stop_table_mould`;
      let totalQueryParams = [];
      let totalConditions = [];

      // Apply machine filtering (without default)
      addMachineFilters(totalConditions, totalQueryParams, filters);

      // FIXED: Use proper date range filtering instead of just date
      addDateRangeFilter(totalConditions, totalQueryParams, filters);

      // Add WHERE clause if there are conditions
      if (totalConditions.length > 0) {
        totalQuery += ` WHERE ` + totalConditions.join(" AND ");
      }

      console.log('📊 Total stops query:', {
        query: totalQuery,
        params: totalQueryParams,
        conditionsCount: totalConditions.length
      });

      // Non-declared stops query with correct spelling
      const nonDeclaredQueryParams = [];
      const nonDeclaredConditions = [`Code_Stop = 'Arrêt non déclaré'`];

      // Apply machine filtering (without default)
      addMachineFilters(nonDeclaredConditions, nonDeclaredQueryParams, filters);

      // FIXED: Use proper date range filtering instead of just date
      addDateRangeFilter(nonDeclaredConditions, nonDeclaredQueryParams, filters);

      const nonDeclaredQuery = `
        SELECT COUNT(*) AS Arret_Totale_nondeclare
        FROM machine_stop_table_mould
        WHERE ${nonDeclaredConditions.join(" AND ")}
      `;

      console.log('📊 Non-declared stops query:', {
        query: nonDeclaredQuery,
        params: nonDeclaredQueryParams,
        conditionsCount: nonDeclaredConditions.length
      });

      const [totalResult, nonDeclaredResult] = await Promise.all([
        executeQuery(totalQuery, totalQueryParams),
        executeQuery(nonDeclaredQuery, nonDeclaredQueryParams)
      ]);

      if (!totalResult.success || !nonDeclaredResult.success) {
        throw new Error(`Database query failed`);
      }

      return {
        Arret_Totale: totalResult.data[0]?.Arret_Totale || 0,
        Arret_Totale_nondeclare: nonDeclaredResult.data[0]?.Arret_Totale_nondeclare || 0
      };
    }
  },

  // Get stop reasons
  getStopReasons: {
    type: new GraphQLList(StopReasonType),
    args: {
      date: { type: GraphQLString },
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { date, filters = {} }) => {
      let query = `
        SELECT
          Code_Stop AS reason,
          COUNT(*) AS count
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Add date filter if provided
      if (date) {
        addDateRangeFilter(conditions, queryParams, { ...filters, date });
      }

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY Code_Stop
        ORDER BY count DESC
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Get stop duration trend
  getStopDurationTrend: {
    type: new GraphQLList(StopDurationTrendType),
    args: {
      date: { type: GraphQLString },
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { date, filters = {} }) => {
      let query = `
        SELECT
          HOUR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) AS hour,
          AVG(TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          )) AS avgDuration
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Add date filter if provided
      if (date) {
        addDateRangeFilter(conditions, queryParams, { ...filters, date });
      }

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY hour
        ORDER BY hour
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Get machine stop comparison
  getMachineStopComparison: {
    type: new GraphQLList(MachineStopComparisonType),
    args: {
      date: { type: GraphQLString },
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { date, filters = {} }) => {
      let query = `
        SELECT
          Machine_Name,
          COUNT(*) AS stops,
          SUM(TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          )) AS totalDuration
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Add date filter if provided
      if (date) {
        addDateRangeFilter(conditions, queryParams, { ...filters, date });
      }      // Apply machine filtering - for comparison, handle differently
      const { model, machine } = filters;

      if (model) {
        conditions.push(`Machine_Name LIKE ?`);
        queryParams.push(`${model}%`);
      } else if (machine) {
        conditions.push(`Machine_Name = ?`);
        queryParams.push(machine);
      } else {
        // For comparison, default to showing all IPS machines if no filter
        conditions.push(`Machine_Name LIKE ?`);
        queryParams.push(`IPS%`);
      }

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY Machine_Name
        ORDER BY stops DESC
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Get operator stop stats
  getOperatorStopStats: {
    type: new GraphQLList(OperatorStopStatsType),
    args: {
      date: { type: GraphQLString },
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { date, filters = {} }) => {
      let query = `
        SELECT
          Regleur_Prenom AS operator,
          COUNT(*) AS interventions,
          SUM(TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          )) AS totalDuration
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Add date filter if provided
      if (date) {
        addDateRangeFilter(conditions, queryParams, { ...filters, date });
      }

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY Regleur_Prenom
        ORDER BY interventions DESC
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Get machine models for stops
  getStopMachineModels: {
    type: new GraphQLList(MachineModelType),
    resolve: async () => {
      const query = `
        SELECT DISTINCT
          CASE
            WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
            THEN REGEXP_REPLACE(Machine_Name, '[0-9].*$', '')
            ELSE Machine_Name
          END AS model
        FROM machine_stop_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
        ORDER BY model
      `;

      const { success, data, error } = await executeQuery(query);

      // If no results, return default models
      if (!success || !data || data.length === 0) {
        return [{ model: "IPS" }, { model: "CCM24" }];
      }

      return data;
    }
  },

  // Get machine names for stops
  getStopMachineNames: {
    type: new GraphQLList(MachineNameType),
    args: {
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { model } = filters;
      
      let query = `
        SELECT DISTINCT Machine_Name
        FROM machine_stop_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
      `;
      
      const queryParams = [];

      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      }

      query += ` ORDER BY Machine_Name`;      const { success, data, error } = await executeQuery(query, queryParams);

      // Return default machines based on the model if the query fails or returns empty
      if (!success || !data || data.length === 0) {
        if (model === "CCM24") {
          return [
            { Machine_Name: "CCM2401" },
            { Machine_Name: "CCM2402" },
            { Machine_Name: "CCM2403" },
            { Machine_Name: "CCM2404" },
          ];
        } else {
          // Default to IPS machines
          return [
            { Machine_Name: "IPS01" },
            { Machine_Name: "IPS02" },
            { Machine_Name: "IPS03" },
            { Machine_Name: "IPS04" },
          ];
        }
      }

      return data;
    }
  },

  // Get stop details for production (equivalent to arrets-production/:date)
  getStopProductionDetails: {
    type: new GraphQLList(MachineStopType),
    args: {
      date: { type: GraphQLString },
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { date, filters = {} }) => {
      let query = `
        SELECT
          Machine_Name,
          Regleur_Prenom,
          Code_Stop,
          Date_Insert,
          Debut_Stop,
          Fin_Stop_Time,
          Part_NO,
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ) AS duration_minutes
        FROM machine_stop_table_mould
        WHERE
          DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?
      `;
      const queryParams = [date];
      const conditions = [`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`];

      // Apply machine filtering with default
      const { model, machine } = filters;

      if (model) {
        conditions.push(`Machine_Name LIKE ?`);
        queryParams.push(`${model}%`);
      } else if (machine) {
        conditions.push(`Machine_Name = ?`);
        queryParams.push(machine);
      } else {
        conditions.push(`Machine_Name = ?`);
        queryParams.push("IPS01");
      }

      // Rebuild query with all conditions
      query = `
        SELECT
          Machine_Name,
          Regleur_Prenom,
          Code_Stop,
          Date_Insert,
          Debut_Stop,
          Fin_Stop_Time,
          Part_NO,
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ) AS duration_minutes
        FROM machine_stop_table_mould
        WHERE ${conditions.join(" AND ")}
        ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Get stops by date range (equivalent to arrets-by-range) 
  getStopsByRange: {
    type: new GraphQLList(StopStatsType),
    args: {
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { date, dateRangeType = "day" } = filters;

      // Determine the appropriate date grouping based on the range type
      let dateGrouping = "DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d')";

      if (dateRangeType === "week") {
        // Group by week number for week view
        dateGrouping =
          "CONCAT(YEAR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')), '-', WEEK(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), 1))";
      } else if (dateRangeType === "month") {
        // Group by month for month view
        dateGrouping = "DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-01')";
      }

      let query = `
        SELECT
          ${dateGrouping} AS Stop_Date,
          COUNT(*) AS Total_Stops
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Apply machine filtering with default
      addMachineFilters(conditions, queryParams, filters);

      // Add date range filter if provided
      if (date) {
        addDateRangeFilter(conditions, queryParams, filters);
      }

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }

      query += `
        GROUP BY Stop_Date
        ORDER BY Stop_Date DESC
        LIMIT 30
      `;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      // For week and month views, convert the grouped date format back to a standard date
      if (dateRangeType === "week" || dateRangeType === "month") {
        const processedResults = data.map((item) => {
          if (dateRangeType === "week") {
            // Convert year-week format to a date (first day of that week)
            const [year, week] = item.Stop_Date.split("-");
            const firstDayOfWeek = new Date(year, 0, 1 + (week - 1) * 7);
            item.Stop_Date = firstDayOfWeek.toISOString().split("T")[0];
          }
          // For month view, the date is already in YYYY-MM-01 format
          return item;
        });
        return processedResults;
      }

      return data;
    }
  },
  // Get stops table data with date range support (equivalent to arrets-table-range)
  getStopsTableRange: {
    type: new GraphQLList(MachineStopType),
    args: {
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { date, limit, dateRangeType = "day" } = filters;

      let query = `
        SELECT
          Machine_Name,
          Regleur_Prenom,
          Code_Stop,
          Date_Insert,
          Debut_Stop,
          Fin_Stop_Time,
          Part_NO,
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ) AS duration_minutes
        FROM machine_stop_table_mould
      `;
      const queryParams = [];
      const conditions = [];

      // Add date range filter if provided
      if (date) {
        addDateRangeFilter(conditions, queryParams, filters);
      }

      // Apply machine filtering - only if machine/model is specified
      if (filters.machine || filters.model) {
        addMachineFilters(conditions, queryParams, filters);
      }

      // Add WHERE clause if there are conditions
      if (conditions.length > 0) {
        query += ` WHERE ` + conditions.join(" AND ");
      }      query += ` ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC`;

      // Add limit if specified (using string interpolation to avoid parameter binding issues)
      if (limit) {
        const limitValue = parseInt(limit);
        if (limitValue > 0 && limitValue <= 1000) { // Safety check
          query += ` LIMIT ${limitValue}`;
        }
      }

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Composite resolver for dashboard data (matches frontend expectation)
  getStopDashboardData: {
    type: new GraphQLObjectType({
      name: 'StopDashboardData',
      fields: {
        allStops: { type: new GraphQLList(MachineStopType) },
        topStops: { type: new GraphQLList(TopStopType) },
        sidecards: { type: StopSideCardType },
        stopComparison: { type: new GraphQLList(MachineStopComparisonType) },
        stopReasons: { type: new GraphQLList(StopReasonType) }
      }
    }),
    args: {
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      try {
        console.log('🔍 getStopDashboardData called with filters:', filters);
        
        // Execute all queries in parallel for better performance
        const [
          allStops,
          topStops,
          sidecards,
          stopComparison,
          stopReasons
        ] = await Promise.all([
          stopTableResolvers.getAllMachineStops.resolve(_, { filters }),
          stopTableResolvers.getTop5Stops.resolve(_, { filters }),
          stopTableResolvers.getStopSidecards.resolve(_, { filters }),
          stopTableResolvers.getMachineStopComparison.resolve(_, { date: filters.startDate, filters }),
          stopTableResolvers.getStopReasons.resolve(_, { filters })
        ]);

        console.log('📊 Dashboard data assembled:', {
          allStopsCount: allStops?.length || 0,
          topStopsCount: topStops?.length || 0,
          sidecardsData: sidecards,
          comparisonCount: stopComparison?.length || 0,
          reasonsCount: stopReasons?.length || 0
        });

        return {
          allStops: allStops || [],
          topStops: topStops || [],
          sidecards: sidecards || { Arret_Totale: 0, Arret_Totale_nondeclare: 0 },
          stopComparison: stopComparison || [],
          stopReasons: stopReasons || []
        };
      } catch (error) {
        console.error('❌ Error in getStopDashboardData:', error);
        throw new Error(`Failed to fetch dashboard data: ${error.message}`);
      }
    }
  },
  // Composite resolver for analysis data (matches frontend expectation)
  getStopsAnalysisData: {
    type: new GraphQLObjectType({
      name: 'StopsAnalysisData',
      fields: {
        durationTrend: { type: new GraphQLList(StopDurationTrendType) },
        operatorStats: { type: new GraphQLList(OperatorStopStatsType) },
        stopReasons: { type: new GraphQLList(StopReasonType) },
        stopStats: { type: new GraphQLList(StopStatsType) }
      }
    }),
    args: {
      filters: { type: StopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      try {
        console.log('🔍 getStopsAnalysisData called with filters:', filters);
        
        // Execute all analysis queries in parallel
        const [
          durationTrend,
          operatorStats,
          stopReasons,
          stopStats
        ] = await Promise.all([
          stopTableResolvers.getStopDurationTrend.resolve(_, { date: filters.startDate, filters }),
          stopTableResolvers.getOperatorStopStats.resolve(_, { date: filters.startDate, filters }),
          stopTableResolvers.getStopReasons.resolve(_, { date: filters.startDate, filters }),
          stopTableResolvers.getStopStats.resolve(_, { filters })
        ]);

        console.log('📈 Analysis data assembled:', {
          durationTrendCount: durationTrend?.length || 0,
          operatorStatsCount: operatorStats?.length || 0,
          stopReasonsCount: stopReasons?.length || 0,
          stopStatsCount: stopStats?.length || 0
        });

        return {
          durationTrend: durationTrend || [],
          operatorStats: operatorStats || [],
          stopReasons: stopReasons || [],
          stopStats: stopStats || []
        };
      } catch (error) {
        console.error('❌ Error in getStopsAnalysisData:', error);
        throw new Error(`Failed to fetch analysis data: ${error.message}`);
      }
    }
  },
};

// Export types for schema
export const stopTableTypes = {
  MachineStopType,
  TopStopType,
  StopStatsType,
  StopReasonType,
  StopDurationTrendType,
  MachineStopComparisonType,
  OperatorStopStatsType,
  StopSideCardType,
  MachineModelType,
  MachineNameType,
  StopFilterInputType
};

// Export queries for schema
export const stopTableQueries = stopTableResolvers;

export default stopTableResolvers;
