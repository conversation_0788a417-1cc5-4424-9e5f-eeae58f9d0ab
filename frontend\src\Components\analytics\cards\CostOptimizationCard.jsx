import React from 'react';
import { Space } from 'antd';
import { PieChartOutlined } from '@ant-design/icons';

const CostOptimizationCard = ({ loading, filters }) => {
  return (
    <div style={{ 
      height: '300px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)',
      borderRadius: '12px'
    }}>
      <Space direction="vertical" align="center">
        <PieChartOutlined style={{ fontSize: '48px', color: '#f5222d' }} />
        <h3 style={{ color: '#f5222d', margin: 0 }}>Cost Optimization AI</h3>
        <p style={{ color: '#8c8c8c', textAlign: 'center' }}>
          AI-powered cost reduction and ROI optimization
        </p>
      </Space>
    </div>
  );
};

export default CostOptimizationCard;
