/**
 * Data Source Status Hook
 * 
 * React hook for monitoring data source status and switching between
 * Elasticsearch and MySQL data sources automatically.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { ENHANCED_DASHBOARD_QUERIES } from '../modules/enhancedGraphQLInterface';

export const useDataSourceStatus = (fetchGraphQL, options = {}) => {
  const {
    checkInterval = 30000, // 30 seconds
    retryDelay = 5000, // 5 seconds
    maxRetries = 3,
    enableAutoRetry = true
  } = options;

  const [dataSourceStatus, setDataSourceStatus] = useState({
    primarySource: 'unknown',
    elasticsearch: {
      available: false,
      stats: null,
      error: null
    },
    mysql: {
      available: true, // Assume MySQL is always available
      error: null
    },
    lastChecked: null,
    isChecking: false,
    error: null
  });

  const intervalRef = useRef(null);
  const retryTimeoutRef = useRef(null);
  const retryCountRef = useRef(0);

  /**
   * Check data source status
   */
  const checkStatus = useCallback(async () => {
    setDataSourceStatus(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      const result = await fetchGraphQL(ENHANCED_DASHBOARD_QUERIES.GET_DATA_SOURCE_STATUS);
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      const status = result.data?.getDataSourceStatus;
      if (status) {
        setDataSourceStatus({
          ...status,
          lastChecked: new Date(),
          isChecking: false,
          error: null
        });
        retryCountRef.current = 0; // Reset retry count on success
      } else {
        throw new Error('No data source status returned');
      }
    } catch (error) {
      console.error('Error checking data source status:', error);
      setDataSourceStatus(prev => ({
        ...prev,
        isChecking: false,
        error: error.message,
        lastChecked: new Date()
      }));

      // Implement retry logic
      if (enableAutoRetry && retryCountRef.current < maxRetries) {
        retryCountRef.current++;
        retryTimeoutRef.current = setTimeout(() => {
          checkStatus();
        }, retryDelay * retryCountRef.current);
      }
    }
  }, [fetchGraphQL, enableAutoRetry, maxRetries, retryDelay]);

  /**
   * Start monitoring
   */
  const startMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Initial check
    checkStatus();

    // Set up periodic checks
    intervalRef.current = setInterval(checkStatus, checkInterval);
  }, [checkStatus, checkInterval]);

  /**
   * Stop monitoring
   */
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  /**
   * Force refresh status
   */
  const refreshStatus = useCallback(() => {
    retryCountRef.current = 0;
    checkStatus();
  }, [checkStatus]);

  // Start monitoring on mount
  useEffect(() => {
    startMonitoring();
    return stopMonitoring;
  }, [startMonitoring, stopMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  return {
    dataSourceStatus,
    isElasticsearchPrimary: dataSourceStatus.primarySource === 'elasticsearch',
    isMySQLFallback: dataSourceStatus.primarySource === 'mysql',
    isHealthy: dataSourceStatus.elasticsearch.available || dataSourceStatus.mysql.available,
    refreshStatus,
    startMonitoring,
    stopMonitoring
  };
};

/**
 * Data Source Status Context Provider
 */
import React, { createContext, useContext } from 'react';

const DataSourceStatusContext = createContext();

export const DataSourceStatusProvider = ({ children, fetchGraphQL, options = {} }) => {
  const dataSourceStatus = useDataSourceStatus(fetchGraphQL, options);

  return (
    <DataSourceStatusContext.Provider value={dataSourceStatus}>
      {children}
    </DataSourceStatusContext.Provider>
  );
};

/**
 * Hook to use data source status context
 */
export const useDataSourceStatusContext = () => {
  const context = useContext(DataSourceStatusContext);
  if (!context) {
    throw new Error('useDataSourceStatusContext must be used within a DataSourceStatusProvider');
  }
  return context;
};

/**
 * Data Source Status Display Component
 */
export const DataSourceStatusDisplay = ({ className = '', showDetails = false }) => {
  const { dataSourceStatus, isElasticsearchPrimary, refreshStatus } = useDataSourceStatusContext();

  const getStatusColor = () => {
    if (dataSourceStatus.error) return 'text-red-500';
    if (isElasticsearchPrimary) return 'text-green-500';
    return 'text-yellow-500';
  };

  const getStatusText = () => {
    if (dataSourceStatus.error) return 'Error';
    if (isElasticsearchPrimary) return 'Elasticsearch (Primary)';
    return 'MySQL (Fallback)';
  };

  const getStatusIcon = () => {
    if (dataSourceStatus.isChecking) return '🔄';
    if (dataSourceStatus.error) return '❌';
    if (isElasticsearchPrimary) return '🚀';
    return '⚠️';
  };

  return (
    <div className={`data-source-status ${className}`}>
      <div className="flex items-center space-x-2">
        <span className="text-sm">{getStatusIcon()}</span>
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        <button
          onClick={refreshStatus}
          className="text-xs text-blue-500 hover:text-blue-700"
          title="Refresh status"
        >
          🔄
        </button>
      </div>
      
      {showDetails && (
        <div className="mt-2 text-xs text-gray-600">
          {dataSourceStatus.elasticsearch.available && (
            <div>
              ES Documents: {dataSourceStatus.elasticsearch.stats?.documentCount || 0}
            </div>
          )}
          {dataSourceStatus.lastChecked && (
            <div>
              Last checked: {dataSourceStatus.lastChecked.toLocaleTimeString()}
            </div>
          )}
          {dataSourceStatus.error && (
            <div className="text-red-500">
              Error: {dataSourceStatus.error}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default useDataSourceStatus;
