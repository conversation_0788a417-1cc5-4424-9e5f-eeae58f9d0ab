import React from "react"
import { Row, Col, Card, Progress, Typography, Statistic, Space, Empty } from "antd"
import SOM<PERSON>EM_COLORS from "../../styles/brand-colors";

const { Text, Title } = Typography

// SOMIPEM Chart Colors (Blue Theme Only)
const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,    // #1E3A8A
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE, // #3B82F6
  success: SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Use blue instead of green
  warning: SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Use blue instead of amber
  danger: SOMIPEM_COLORS.CHART_TERTIARY,    // #93C5FD - Use light blue instead of red
  purple: "#6366F1", // Indigo blue
  pink: "#60A5FA",   // Light blue
  orange: "#1D4ED8", // Dark blue
  cyan: SOMIPEM_COLORS.CHART_QUATERNARY,    // #DBEAFE
  lime: "#3730A3",   // Darker blue
}

// Helper function to format percentage values
const formatPercentage = (value) => {
  if (value === undefined || value === null) return 0;

  // Convert to number
  let numValue = Number(value);

  // Check if value is between 0 and 1, and convert to percentage if needed
  if (!isNaN(numValue) && numValue > 0 && numValue < 1) {
    numValue = numValue * 100;
  }

  return numValue;
};

const PerformanceMetricsGauge = ({
  data = null,
  selectedMachine = "",
  loading = false,
  thresholds = {
    disponibilite: { low: 70, medium: 85 },
    mttr: { low: 45, medium: 20 },
    mtbf: { low: 120, medium: 240 },
  },
}) => {
  // Helper function to determine color based on value and thresholds
  const getColorForMetric = (value, metric) => {
    const metricThresholds = thresholds[metric]
    if (!metricThresholds) return CHART_COLORS.primary

    if (metric === "mttr") {
      // For MTTR, lower is better
      if (value > metricThresholds.low) return CHART_COLORS.danger
      if (value > metricThresholds.medium) return CHART_COLORS.warning
      return CHART_COLORS.success
    } else {
      // For disponibilite and mtbf, higher is better
      if (value < metricThresholds.low) return CHART_COLORS.danger
      if (value < metricThresholds.medium) return CHART_COLORS.warning
      return CHART_COLORS.success
    }
  }

  if (!data) {
    return <Empty description="Aucune donnée disponible pour les indicateurs de performance" />
  }

  return (
    <Row gutter={[16, 16]}>
      {/* Disponibilité Gauge */}
      <Col xs={24} md={8}>
        <Card bordered={false} style={{ height: "100%" }}>
          <Statistic
            title={
              <Space>
                <span>Disponibilité</span>
                {selectedMachine && <Text type="secondary">({selectedMachine})</Text>}
              </Space>
            }
            value={formatPercentage(data.disponibilite)}
            precision={1}
            suffix="%"
            valueStyle={{ color: getColorForMetric(formatPercentage(data.disponibilite), "disponibilite") }}
          />
          <Progress
            type="dashboard"
            percent={formatPercentage(data.disponibilite)}
            strokeColor={getColorForMetric(formatPercentage(data.disponibilite), "disponibilite")}
            format={(percent) => `${percent?.toFixed(1)}%`}
            width={120}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              {formatPercentage(data.disponibilite) >= thresholds.disponibilite.medium
                ? "Excellente disponibilité opérationnelle"
                : formatPercentage(data.disponibilite) >= thresholds.disponibilite.low
                  ? "Disponibilité acceptable, des améliorations possibles"
                  : "Disponibilité insuffisante, action requise"}
            </Text>
          </div>
        </Card>
      </Col>

      {/* MTTR Gauge */}
      <Col xs={24} md={8}>
        <Card bordered={false} style={{ height: "100%" }}>
          <Statistic
            title={
              <Space>
                <span>MTTR</span>
                {selectedMachine && <Text type="secondary">({selectedMachine})</Text>}
              </Space>
            }
            value={data.mttr}
            precision={1}
            suffix="min"
            valueStyle={{ color: getColorForMetric(data.mttr, "mttr") }}
          />
          <Progress
            type="dashboard"
            percent={Math.min(100, (data.mttr / 60) * 100)}
            strokeColor={getColorForMetric(data.mttr, "mttr")}
            format={(percent) => `${data.mttr.toFixed(1)} min`}
            width={120}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              {data.mttr <= thresholds.mttr.medium
                ? "Excellent temps de réparation"
                : data.mttr <= thresholds.mttr.low
                  ? "Temps de réparation acceptable"
                  : "Temps de réparation trop long, action requise"}
            </Text>
          </div>
        </Card>
      </Col>

      {/* MTBF Gauge */}
      <Col xs={24} md={8}>
        <Card bordered={false} style={{ height: "100%" }}>
          <Statistic
            title={
              <Space>
                <span>MTBF</span>
                {selectedMachine && <Text type="secondary">({selectedMachine})</Text>}
              </Space>
            }
            value={data.mtbf}
            precision={1}
            suffix="min"
            valueStyle={{ color: getColorForMetric(data.mtbf, "mtbf") }}
          />
          <Progress
            type="dashboard"
            percent={Math.min(100, Math.max(0, (data.mtbf / 1440) * 100))} // 1440 = 24h in minutes
            strokeColor={getColorForMetric(data.mtbf, "mtbf")}
            format={(percent) => `${data.mtbf.toFixed(1)} min`}
            width={120}
          />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              {data.mtbf >= thresholds.mtbf.medium
                ? "Excellente fiabilité entre pannes"
                : data.mtbf >= thresholds.mtbf.low
                  ? "Fiabilité acceptable entre pannes"
                  : "Fiabilité insuffisante, action requise"}
            </Text>
          </div>
        </Card>
      </Col>
    </Row>
  )
}

export default React.memo(PerformanceMetricsGauge)
