/**
 * Enhanced SSE Notification Bell Component for Somipem
 * Replaces WebSocket-based notifications with Server-Sent Events
 * Maintains full backward compatibility with existing notification system
 */

import SOMIPEM_COLORS from '../styles/brand-colors';
import React, { useState } from 'react';
import { 
  Badge, 
  Dropdown, 
  List, 
  Button, 
  Typography, 
  Tag, 
  Space, 
  Tooltip,
  Divider,
  Empty,
  Spin
} from 'antd';
import { 
  BellOutlined, 
  CheckOutlined, 
  WifiOutlined,
  DisconnectOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
  ToolOutlined,
  AppstoreOutlined,
  InfoCircleOutlined,
  BuildOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/fr';
import { useSSE } from '../context/SSEContext';

dayjs.extend(relativeTime);
dayjs.locale('fr');

const { Text } = Typography;

const SSENotificationBell = () => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  
  // Use shared SSE connection from context
  const {
    notifications,
    unreadCount,
    connectionStatus,
    connectionStats,
    markAsRead,
    acknowledgeNotification,
    connect,
    isConnected,
    isConnecting,
    hasError
  } = useSSE();

  // Debug: Log connection status changes
  React.useEffect(() => {
    console.log('🔔 SSENotificationBell - Connection status changed:', {
      connectionStatus,
      isConnected,
      isConnecting,
      hasError,
      notificationsCount: notifications.length,
      unreadCount
    });
  }, [connectionStatus, isConnected, isConnecting, hasError, notifications.length, unreadCount]);

  /**
   * Get notification icon based on category
   */
  const getNotificationIcon = (category, priority) => {
    const iconStyle = getPriorityIconStyle(priority);
    
    switch (category) {
      case 'alert':
      case 'machine_alert':
        return <AlertOutlined style={iconStyle} />;
      case 'maintenance':
        return <ToolOutlined style={iconStyle} />;
      case 'update':
        return <AppstoreOutlined style={iconStyle} />;
      case 'production':
        return <BuildOutlined style={iconStyle} />;
      case 'quality':
        return <AlertOutlined style={iconStyle} />;
      case 'info':
      default:
        return <InfoCircleOutlined style={iconStyle} />;
    }
  };

  /**
   * Get priority-based icon styling with SOMIPEM colors
   */
  const getPriorityIconStyle = (priority) => {
    switch (priority) {
      case 'critical':
        return { color: SOMIPEM_COLORS.ERROR, fontSize: '16px' };
      case 'high':
        return { color: SOMIPEM_COLORS.WARNING, fontSize: '15px' };
      case 'medium':
        return { color: SOMIPEM_COLORS.PRIMARY_BLUE, fontSize: '14px' };
      case 'low':
        return { color: SOMIPEM_COLORS.SUCCESS, fontSize: '14px' };
      default:
        return { color: SOMIPEM_COLORS.PRIMARY_BLUE, fontSize: '14px' };
    }
  };

  /**
   * Get priority color for tags
   */
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'blue';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  /**
   * Get priority text in French
   */
  const getPriorityText = (priority) => {
    switch (priority) {
      case 'critical': return 'Critique';
      case 'high': return 'Élevée';
      case 'medium': return 'Moyenne';
      case 'low': return 'Faible';
      default: return 'Moyenne';
    }
  };

  /**
   * Get category text in French
   */
  const getCategoryText = (category) => {
    switch (category) {
      case 'alert': return 'Alerte';
      case 'machine_alert': return 'Alerte Machine';
      case 'maintenance': return 'Maintenance';
      case 'update': return 'Mise à jour';
      case 'production': return 'Production';
      case 'quality': return 'Qualité';
      case 'info':
      default: return 'Information';
    }
  };

  /**
   * Get connection status icon and color
   */
  const getConnectionStatusDisplay = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: <WifiOutlined />,
          color: '#52c41a',
          text: 'Connecté',
          status: 'success'
        };
      case 'connecting':
        return {
          icon: <LoadingOutlined spin />,
          color: '#1890ff',
          text: 'Connexion...',
          status: 'processing'
        };
      case 'error':
        return {
          icon: <ExclamationCircleOutlined />,
          color: '#faad14',
          text: 'Erreur',
          status: 'warning'
        };
      case 'failed':
        return {
          icon: <DisconnectOutlined />,
          color: '#ff4d4f',
          text: 'Échec',
          status: 'error'
        };
      default:
        return {
          icon: <DisconnectOutlined />,
          color: '#d9d9d9',
          text: 'Déconnecté',
          status: 'default'
        };
    }
  };

  /**
   * Get notification background color based on priority and read status
   */
  const getNotificationBackground = (notification) => {
    if (notification.read_at) return 'transparent';
    
    switch (notification.priority) {
      case 'critical': return '#fff2f0';
      case 'high': return '#fff7e6';
      case 'medium': return '#f0f7ff';
      case 'low': return '#f6ffed';
      default: return '#f0f7ff';
    }
  };

  /**
   * Get notification border for critical items
   */
  const getNotificationBorder = (notification) => {
    return notification.priority === 'critical' ? '2px solid #ff7875' : 'none';
  };

  /**
   * Handle notification click (mark as read)
   */
  const handleNotificationClick = async (notification) => {
    if (notification.isUnread) {
      await markAsRead(notification.id);
    }
  };

  /**
   * Handle acknowledge button click
   */
  const handleAcknowledge = async (e, notification) => {
    e.stopPropagation();
    await acknowledgeNotification(notification.id);
  };

  const connectionDisplay = getConnectionStatusDisplay();

  /**
   * Notification dropdown menu
   */
  const notificationMenu = (
    <div style={{ width: 380, maxHeight: 500, overflow: 'hidden' }}>
      {/* Header with connection status */}
      <div style={{ 
        padding: '12px 16px', 
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Text strong>Notifications</Text>
          <Space>
            <Tooltip title={`SSE: ${connectionDisplay.text}`}>
              <Badge 
                status={connectionDisplay.status} 
                text={
                  <span style={{ color: connectionDisplay.color, fontSize: '12px' }}>
                    {connectionDisplay.icon} {connectionDisplay.text}
                  </span>
                }
              />
            </Tooltip>
            {hasError && (
              <Button 
                size="small" 
                type="link" 
                icon={<ReloadOutlined />}
                onClick={connect}
                style={{ padding: 0 }}
              >
                Reconnecter
              </Button>
            )}
          </Space>
        </Space>
        
        {/* Connection stats for debugging */}
        {connectionStats.connectedAt && (
          <Text type="secondary" style={{ fontSize: '11px' }}>
            Connecté: {dayjs(connectionStats.connectedAt).format('HH:mm:ss')} • 
            Messages: {connectionStats.messagesReceived}
          </Text>
        )}
      </div>
      
      {/* Notifications list */}
      <div style={{ maxHeight: 400, overflow: 'auto' }}>
        {notifications.length === 0 ? (
          <div style={{ padding: 20, textAlign: 'center' }}>
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="Aucune notification"
            />
          </div>
        ) : (
          <List
            dataSource={notifications.slice(0, 15)} // Show last 15 notifications
            renderItem={(notification) => (
              <List.Item
                style={{
                  padding: '12px 16px',
                  backgroundColor: getNotificationBackground(notification),
                  borderLeft: getNotificationBorder(notification),
                  cursor: 'pointer',
                  transition: 'background-color 0.2s'
                }}
                onClick={() => handleNotificationClick(notification)}
                actions={[
                  !notification.read_at && (
                    <Tooltip title="Marquer comme lu">
                      <Button
                        type="text"
                        size="small"
                        icon={<CheckOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          markAsRead(notification.id);
                        }}
                      />
                    </Tooltip>
                  ),
                  (notification.priority === 'critical' || notification.priority === 'high') && 
                  !notification.acknowledged_at && (
                    <Tooltip title="Acquitter">
                      <Button
                        type="text"
                        size="small"
                        onClick={(e) => handleAcknowledge(e, notification)}
                        style={{ color: '#fa8c16' }}
                      >
                        Acquitter
                      </Button>
                    </Tooltip>
                  )
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(notification.category, notification.priority)}
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Text 
                        strong={notification.isUnread}
                        style={{ 
                          color: notification.priority === 'critical' ? '#ff4d4f' : 'inherit',
                          fontSize: '14px',
                          lineHeight: '1.4'
                        }}
                      >
                        {notification.title}
                      </Text>
                      <div style={{ display: 'flex', gap: '4px', flexShrink: 0, marginLeft: '8px' }}>
                        <Tag 
                          color={getPriorityColor(notification.priority)} 
                          size="small"
                          style={{ 
                            fontSize: '10px',
                            fontWeight: notification.priority === 'critical' ? 'bold' : 'normal'
                          }}
                        >
                          {getPriorityText(notification.priority)}
                        </Tag>
                      </div>
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ 
                        marginBottom: '6px', 
                        fontSize: '13px',
                        color: '#666'
                      }}>
                        {notification.message}
                      </div>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        fontSize: '11px'
                      }}>
                        <Space size={4}>
                          <Text type="secondary">
                            {notification.timeAgo || dayjs(notification.created_at).fromNow()}
                          </Text>
                          {notification.machine_id && (
                            <>
                              <span style={{ color: '#d9d9d9' }}>•</span>
                              <Text type="secondary">
                                Machine {notification.machine_id}
                              </Text>
                            </>
                          )}
                        </Space>
                        <Space size={4}>
                          <Tag size="small" style={{ fontSize: '10px' }}>
                            {getCategoryText(notification.category)}
                          </Tag>
                          {notification.acknowledged_at && (
                            <Tag color="green" size="small" style={{ fontSize: '10px' }}>
                              Acquittée
                            </Tag>
                          )}
                        </Space>
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>
      
      {/* Footer */}
      {notifications.length > 15 && (
        <div style={{ 
          padding: '8px 16px', 
          borderTop: '1px solid #f0f0f0',
          textAlign: 'center'
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {notifications.length - 15} notifications supplémentaires...
          </Text>
        </div>
      )}
    </div>
  );

  return (
    <Dropdown 
      overlay={notificationMenu} 
      trigger={['click']} 
      placement="bottomRight"
      visible={dropdownVisible}
      onVisibleChange={setDropdownVisible}
    >
      <Badge 
        count={unreadCount} 
        size="small"
        offset={[-2, 2]}
      >
        <Button 
          type="text" 
          icon={<BellOutlined />} 
          style={{ 
            color: isConnected ? 'inherit' : '#ff4d4f',
            fontSize: '16px'
          }}
        />
      </Badge>
    </Dropdown>
  );
};

export default SSENotificationBell;
