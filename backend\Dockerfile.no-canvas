# Dockerfile without canvas for testing apicache
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Create a temporary package.json without canvas
RUN echo "=== Creating package.json without canvas ===" && \
    node -e "const pkg = require('./package.json'); delete pkg.dependencies.canvas; delete pkg.dependencies['chartjs-node-canvas']; delete pkg.dependencies.puppeteer; delete pkg.dependencies['puppeteer-core']; require('fs').writeFileSync('package-temp.json', JSON.stringify(pkg, null, 2));" && \
    mv package-temp.json package.json

# Install dependencies
RUN echo "=== Installing Node.js dependencies (without canvas) ===" && \
    npm install --verbose && \
    echo "=== Verifying apicache installation ===" && \
    npm list apicache && \
    echo "=== Testing apicache require ===" && \
    node -e "try { require('apicache'); console.log('SUCCESS: apicache found and working'); } catch(e) { console.error('ERROR: apicache missing:', e.message); process.exit(1); }" && \
    echo "=== Testing other key dependencies ===" && \
    node -e "try { require('express'); console.log('SUCCESS: express works'); } catch(e) { console.error('ERROR: express missing:', e.message); }" && \
    node -e "try { require('mysql2'); console.log('SUCCESS: mysql2 works'); } catch(e) { console.error('ERROR: mysql2 missing:', e.message); }" && \
    npm cache clean --force

# Copy source code
COPY . .

# Expose port
EXPOSE 5000

# Start the application
CMD ["node", "server.js"]
