

import { GraphQLObjectType, GraphQLSchema, GraphQLString, GraphQLInt, GraphQLBoolean, GraphQLList, GraphQLInputObjectType, GraphQLNonNull } from 'graphql';
import { dailyTableQueries, dailyTableTypes } from './dailyTableResolvers.js';
import { stopTableQueries, stopTableTypes } from './stopTableResolvers.js';
import { externalApiResolvers } from './externalApiResolvers.js';
import { enhancedTypes } from './enhancedDashboardTypes.js';
import unifiedDashboardResolvers from './unifiedDashboardResolvers.js';
import unifiedProductionResolvers from './unifiedProductionResolvers.js';
import dataSyncResolvers from './dataSyncResolvers.js';

// External API Types
const ExternalServiceType = new GraphQLObjectType({
  name: 'ExternalService',
  fields: {
    name: { type: GraphQLString },
    status: { type: GraphQLString },
    responseTime: { type: GraphQLInt },
    details: { type: GraphQLString }
  }
});

const HealthStatusType = new GraphQLObjectType({
  name: 'HealthStatus',
  fields: {
    overall: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    services: { type: new GraphQLList(ExternalServiceType) },
    error: { type: GraphQLString }
  }
});

const WebhookType = new GraphQLObjectType({
  name: 'Webhook',
  fields: {
    name: { type: GraphQLString },
    url: { type: GraphQLString },
    active: { type: GraphQLBoolean },
    format: { type: GraphQLString },
    timeout: { type: GraphQLInt }
  }
});

const ConnectivityTestType = new GraphQLObjectType({
  name: 'ConnectivityTest',
  fields: {
    connected: { type: GraphQLBoolean },
    url: { type: GraphQLString },
    responseTime: { type: GraphQLInt },
    testDuration: { type: GraphQLInt },
    timestamp: { type: GraphQLString },
    error: { type: GraphQLString }
  }
});

const EndpointMonitorResultType = new GraphQLObjectType({
  name: 'EndpointMonitorResult',
  fields: {
    name: { type: GraphQLString },
    healthy: { type: GraphQLBoolean },
    url: { type: GraphQLString },
    responseTime: { type: GraphQLInt },
    status: { type: GraphQLInt },
    timestamp: { type: GraphQLString },
    error: { type: GraphQLString },
    metrics: { type: GraphQLString }
  }
});

const EndpointMonitorType = new GraphQLObjectType({
  name: 'EndpointMonitor',
  fields: {
    timestamp: { type: GraphQLString },
    totalEndpoints: { type: GraphQLInt },
    results: { type: new GraphQLList(EndpointMonitorResultType) }
  }
});

const NotificationResultType = new GraphQLObjectType({
  name: 'NotificationResult',
  fields: {
    success: { type: GraphQLBoolean },
    platform: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    response: { type: GraphQLString },
    error: { type: GraphQLString }
  }
});

const MultiPlatformResultType = new GraphQLObjectType({
  name: 'MultiPlatformResult',
  fields: {
    platform: { type: GraphQLString },
    success: { type: GraphQLBoolean },
    timestamp: { type: GraphQLString },
    response: { type: GraphQLString },
    error: { type: GraphQLString }
  }
});

const MultiPlatformNotificationResultType = new GraphQLObjectType({
  name: 'MultiPlatformNotificationResult',
  fields: {
    success: { type: GraphQLBoolean },
    totalPlatforms: { type: GraphQLInt },
    successfulSends: { type: GraphQLInt },
    failedSends: { type: GraphQLInt },
    totalTime: { type: GraphQLInt },
    timestamp: { type: GraphQLString },
    results: { type: new GraphQLList(MultiPlatformResultType) }
  }
});

const WebhookRegistrationResultType = new GraphQLObjectType({
  name: 'WebhookRegistrationResult',
  fields: {
    success: { type: GraphQLBoolean },
    name: { type: GraphQLString },
    url: { type: GraphQLString },
    format: { type: GraphQLString },
    timeout: { type: GraphQLInt },
    message: { type: GraphQLString }
  }
});

const WebhookTestResultType = new GraphQLObjectType({
  name: 'WebhookTestResult',
  fields: {
    success: { type: GraphQLBoolean },
    webhook: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    response: { type: GraphQLString },
    error: { type: GraphQLString },
    config: { type: GraphQLString }
  }
});

const WebhookStatusResultType = new GraphQLObjectType({
  name: 'WebhookStatusResult',
  fields: {
    success: { type: GraphQLBoolean },
    webhook: { type: GraphQLString },
    active: { type: GraphQLBoolean },
    message: { type: GraphQLString },
    timestamp: { type: GraphQLString }
  }
});

// Input Types
const EndpointInputType = new GraphQLInputObjectType({
  name: 'EndpointInput',
  fields: {
    name: { type: new GraphQLNonNull(GraphQLString) },
    url: { type: new GraphQLNonNull(GraphQLString) },
    headers: { type: GraphQLString }
  }
});

const NotificationInputType = new GraphQLInputObjectType({
  name: 'NotificationInput',
  fields: {
    platform: { type: new GraphQLNonNull(GraphQLString) },
    webhook_url: { type: new GraphQLNonNull(GraphQLString) },
    message: { type: new GraphQLNonNull(GraphQLString) },
    type: { type: GraphQLString },
    title: { type: GraphQLString }
  }
});

const PlatformInputType = new GraphQLInputObjectType({
  name: 'PlatformInput',
  fields: {
    platform: { type: new GraphQLNonNull(GraphQLString) },
    webhook_url: { type: new GraphQLNonNull(GraphQLString) },
    name: { type: GraphQLString }
  }
});

const MultiPlatformNotificationInputType = new GraphQLInputObjectType({
  name: 'MultiPlatformNotificationInput',
  fields: {
    message: { type: new GraphQLNonNull(GraphQLString) },
    platforms: { type: new GraphQLNonNull(new GraphQLList(PlatformInputType)) },
    type: { type: GraphQLString },
    title: { type: GraphQLString }
  }
});

const WebhookRegistrationInputType = new GraphQLInputObjectType({
  name: 'WebhookRegistrationInput',
  fields: {
    name: { type: new GraphQLNonNull(GraphQLString) },
    url: { type: new GraphQLNonNull(GraphQLString) },
    headers: { type: GraphQLString },
    timeout: { type: GraphQLInt },
    format: { type: GraphQLString }
  }
});

// Root Query combining all resolver types
const RootQuery = new GraphQLObjectType({
  name: 'RootQueryType',
  fields: {
    ...dailyTableQueries,
    ...stopTableQueries,
    
    // Enhanced Production Queries with Elasticsearch + MySQL fallback
    enhancedGetProductionChart: {
      type: enhancedTypes.EnhancedProductionChartResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedProductionResolvers.getProductionChart
    },
    enhancedGetProductionSidecards: {
      type: enhancedTypes.EnhancedProductionSidecardsType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedProductionResolvers.getProductionSidecards
    },
    enhancedGetMachinePerformance: {
      type: enhancedTypes.EnhancedMachinePerformanceResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedProductionResolvers.getMachinePerformance
    },
    enhancedGetShiftPerformance: {
      type: enhancedTypes.EnhancedMachinePerformanceResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedProductionResolvers.getShiftPerformance
    },
    
    // Enhanced Dashboard Queries with Elasticsearch support
    enhancedGetAllMachineStops: {
      type: enhancedTypes.EnhancedStopsResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType },
        pagination: { type: enhancedTypes.PaginationInputType }
      },
      resolve: unifiedDashboardResolvers.getAllMachineStops
    },
    enhancedGetTop5Stops: {
      type: enhancedTypes.EnhancedTopStopsResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedDashboardResolvers.getTop5Stops
    },
    enhancedGetMachineStopComparison: {
      type: enhancedTypes.EnhancedMachineComparisonResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedDashboardResolvers.getMachineStopComparison
    },
    getDashboardStats: {
      type: enhancedTypes.DashboardStatsType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: unifiedDashboardResolvers.getDashboardStats
    },
    getStopEvolution: {
      type: enhancedTypes.StopEvolutionResultType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType },
        interval: { type: GraphQLString } // day, week, hour
      },
      resolve: unifiedDashboardResolvers.getStopEvolution
    },

    // Data Synchronization Queries
    getDataSyncStats: dataSyncResolvers.getDataSyncStats,
    triggerManualSync: dataSyncResolvers.triggerManualSync,
    getPerformanceMetrics: {
      type: enhancedTypes.PerformanceMetricsType,
      args: {
        filters: { type: enhancedTypes.EnhancedFilterInputType }
      },
      resolve: async (_, args) => {
        try {
          const dataSource = await unifiedDashboardResolvers.selectDataSource();
          if (dataSource === 'elasticsearch') {
            const elasticsearchDashboardService = require('../../services/elasticsearchDashboardService');
            const metrics = await elasticsearchDashboardService.getPerformanceMetrics(args.filters);
            return { ...metrics, dataSource: 'elasticsearch' };
          } else {
            // Could implement MySQL performance metrics here
            return {
              mttr: 0,
              mtbf: 0,
              availability: 0,
              totalDowntime: 0,
              averageStopsPerDay: 0,
              dataSource: 'mysql-not-implemented'
            };
          }
        } catch (error) {
          console.error('Error getting performance metrics:', error);
          return {
            mttr: 0,
            mtbf: 0,
            availability: 0,
            totalDowntime: 0,
            averageStopsPerDay: 0,
            dataSource: 'error'
          };
        }
      }
    },
    getDataSourceStatus: {
      type: enhancedTypes.DataSourceStatusType,
      resolve: unifiedDashboardResolvers.getDataSourceStatus
    },
    
    // External API queries
    externalHealthStatus: {
      type: HealthStatusType,
      resolve: externalApiResolvers.Query.externalHealthStatus
    },
    registeredWebhooks: {
      type: new GraphQLList(WebhookType),
      resolve: externalApiResolvers.Query.registeredWebhooks
    },
    testExternalService: {
      type: ConnectivityTestType,
      args: {
        url: { type: new GraphQLNonNull(GraphQLString) },
        timeout: { type: GraphQLInt }
      },
      resolve: externalApiResolvers.Query.testExternalService
    },
    monitorEndpoints: {
      type: EndpointMonitorType,
      args: {
        endpoints: { type: new GraphQLNonNull(new GraphQLList(EndpointInputType)) },
        timeout: { type: GraphQLInt }
      },
      resolve: externalApiResolvers.Query.monitorEndpoints
    }
  }
});

// Root Mutation for external API operations and dashboard management
const RootMutation = new GraphQLObjectType({
  name: 'RootMutationType',
  fields: {
    // Dashboard Management Mutations
    indexDashboardData: {
      type: new GraphQLObjectType({
        name: 'IndexResult',
        fields: {
          success: { type: GraphQLBoolean },
          indexed: { type: GraphQLInt },
          errors: { type: GraphQLInt },
          message: { type: GraphQLString }
        }
      }),
      resolve: async () => {
        try {
          const dashboardDataIndexer = require('../../services/dashboardDataIndexer');
          await dashboardDataIndexer.createIndexMapping();
          const result = await dashboardDataIndexer.indexAllStopData();
          return { 
            success: result.success, 
            indexed: result.indexed, 
            errors: result.errors,
            message: `Indexed ${result.indexed} records with ${result.errors} errors`
          };
        } catch (error) {
          return { 
            success: false, 
            indexed: 0, 
            errors: 1,
            message: error.message 
          };
        }
      }
    },
    
    // External API Mutations
    sendExternalNotification: {
      type: NotificationResultType,
      args: {
        input: { type: new GraphQLNonNull(NotificationInputType) }
      },
      resolve: externalApiResolvers.Mutation.sendExternalNotification
    },
    sendMultiPlatformNotification: {
      type: MultiPlatformNotificationResultType,
      args: {
        input: { type: new GraphQLNonNull(MultiPlatformNotificationInputType) }
      },
      resolve: externalApiResolvers.Mutation.sendMultiPlatformNotification
    },
    registerWebhook: {
      type: WebhookRegistrationResultType,
      args: {
        input: { type: new GraphQLNonNull(WebhookRegistrationInputType) }
      },
      resolve: externalApiResolvers.Mutation.registerWebhook
    },
    testWebhook: {
      type: WebhookTestResultType,
      args: {
        webhookName: { type: new GraphQLNonNull(GraphQLString) }
      },
      resolve: externalApiResolvers.Mutation.testWebhook
    },
    updateWebhookStatus: {
      type: WebhookStatusResultType,
      args: {
        webhookName: { type: new GraphQLNonNull(GraphQLString) },
        active: { type: new GraphQLNonNull(GraphQLBoolean) }
      },
      resolve: externalApiResolvers.Mutation.updateWebhookStatus
    },
    performHealthCheck: {
      type: HealthStatusType,
      resolve: externalApiResolvers.Mutation.performHealthCheck
    }
  }
});

// Export the complete GraphQL schema
export const schema = new GraphQLSchema({
  query: RootQuery,
  mutation: RootMutation
});

// Export all types for potential reuse
export const types = {
  ...dailyTableTypes,
  ...stopTableTypes,
  ...enhancedTypes,
  ExternalServiceType,
  HealthStatusType,
  WebhookType,
  ConnectivityTestType,
  EndpointMonitorType,
  NotificationResultType,
  MultiPlatformNotificationResultType,
  WebhookRegistrationResultType,
  WebhookTestResultType,
  WebhookStatusResultType
};
