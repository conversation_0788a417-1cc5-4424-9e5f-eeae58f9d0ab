import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON> as RechartsTooltip,
} from "recharts";
import { Card, Empty } from "antd";
import dayjs from "dayjs";

/**
 * Line chart component for displaying cycle time data
 * @param {Object} props - Component props
 * @param {Array} props.data - The data to display in the chart
 * @param {string} props.title - The title of the chart
 * @param {string} props.color - The color to use for the line
 * @returns {JSX.Element} The rendered chart
 */
const CycleTimeLineChart = ({ data, title = "Cycle De Temps", color = "#13c2c2" }) => {
  return (
    <Card title={title} type="inner">
      {data && data.length > 0 ? (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tick={{ fill: "#666" }}
              tickFormatter={(date) => {
                // Safely format the date, handling invalid dates
                try {
                  if (date && dayjs(date).isValid()) {
                    // Format as DD/MM with year included in tooltip
                    return dayjs(date).format("DD/MM");
                  }
                  return "N/A";
                } catch (e) {
                  console.error("Error formatting date:", date, e);
                  return "N/A";
                }
              }}
              type="category"
              allowDuplicatedCategory={false}
              interval="preserveStartEnd"
            />
            <YAxis
              domain={[0, 'dataMax + 1']}
              tickFormatter={(value) => `${value.toFixed(2)}`}
            />
            <RechartsTooltip
              contentStyle={{
                backgroundColor: "#fff",
                border: "1px solid #f0f0f0",
                borderRadius: 8,
                boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
              }}
              formatter={(value) => {
                const parsed = parseFloat(value);
                const isValidNumber = !isNaN(parsed);
                return [
                  isValidNumber ? parsed.toFixed(2) : value,
                  "Cycle De Temps"
                ];
              }}
              labelFormatter={(label) => {
                try {
                  if (label && dayjs(label).isValid()) {
                    return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                  }
                  return "Date: N/A";
                } catch (e) {
                  console.error("Error formatting tooltip date:", label, e);
                  return "Date: N/A";
                }
              }}
            />

            <Line
              type="monotone"
              dataKey="speed"
              name="Cycle De Temps"
              stroke={color}
              strokeWidth={2}
              dot={{ r: 4, fill: color }}
              activeDot={{ r: 6, fill: "#fff", stroke: color, strokeWidth: 2 }}
              connectNulls={true}
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
          <Empty description="Aucune donnée disponible" />
        </div>
      )}
    </Card>
  );
};

export default CycleTimeLineChart;
