import db from "../db.js"
import emailNotificationService from "../services/EmailNotificationService.js"

import emailRetryService from "../services/EmailRetryService.js"
import notificationEscalationService from "../services/NotificationEscalationService.js"

/**
 * NotificationService - Enhanced notification system with database persistence
 * Integrates with SSE for real-time delivery and email notifications
 * Provides comprehensive notification management with multi-channel delivery
 */
class NotificationService {
  /**
   * Create a new notification
   * @param {Object} notification - The notification object
   * @param {string} notification.title - Notification title
   * @param {string} notification.message - Notification message
   * @param {string} notification.category - Category (alert, maintenance, update, info, machine_alert, production, quality)
   * @param {string} notification.priority - Priority (low, medium, high, critical)
   * @param {string} notification.severity - Severity (info, warning, error, critical)
   * @param {string} notification.source - Source of notification (system, machine_monitoring, user, etc.)
   * @param {number|null} notification.machine_id - Machine ID (for machine-related notifications)
   * @param {number|null} notification.userId - User ID (null for all users)
   * @returns {Promise<Object>} - The created notification
   */
  static async createNotification(notification) {
    const {
      title,
      message,
      category,
      priority = 'medium',
      severity = 'info',
      source = 'system',
      machine_id = null,
      userId
    } = notification

    // Validate category
    const validCategories = ['alert', 'maintenance', 'update', 'info', 'machine_alert', 'production', 'quality'];
    if (!validCategories.includes(category)) {
      throw new Error(`Invalid category: ${category}`);
    }

    // Validate priority
    const validPriorities = ['low', 'medium', 'high', 'critical'];
    if (!validPriorities.includes(priority)) {
      throw new Error(`Invalid priority: ${priority}`);
    }

    // Validate severity
    const validSeverities = ['info', 'warning', 'error', 'critical'];
    if (!validSeverities.includes(severity)) {
      throw new Error(`Invalid severity: ${severity}`);
    }

    console.log(`🔔 Creating notification: ${title} (${priority})`);

    const insertQuery = `INSERT INTO notifications
       (title, message, category, priority, severity, source, machine_id, user_id, timestamp, \`read\`)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), false)`;
    
    const insertParams = [title, message, category, priority, severity, source, machine_id, userId || null];
    
    console.log(`🔍 Executing insert query with params:`, insertParams);    try {
      // Use async/await with the promise-based database connection
      const [result] = await db.execute(insertQuery, insertParams);
      const notificationId = result.insertId;
      console.log(`✅ Notification inserted with ID: ${notificationId}`);

      // Get the created notification
      const selectQuery = "SELECT * FROM notifications WHERE id = ?";
      console.log(`🔍 Executing select query for notification ID: ${notificationId}`);
        const [selectResult] = await db.execute(selectQuery, [notificationId]);
      
      if (selectResult.length === 0) {
        console.error(`❌ Notification not found after creation: ${notificationId}`);
        throw new Error("Notification not found");
      }

      console.log(`✅ Notification retrieved successfully:`, selectResult[0]);
      return selectResult[0];
      
    } catch (error) {
      console.error("❌ Database error in createNotification:", error);
      throw error;
    }
  }

  /**
   * Create a system notification for all users
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} category - Category (alert, maintenance, update, info)
   * @returns {Promise<Object>} - The created notification
   */
  static async createSystemNotification(title, message, category = "info") {
    return this.createNotification({
      title,
      message,
      category,
      userId: null, // null means for all users
    })
  }

  /**
   * Create a maintenance notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification
   */
  static async createMaintenanceNotification(title, message) {
    return this.createSystemNotification(title, message, "maintenance")
  }

  /**
   * Create an alert notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification
   */
  static async createAlertNotification(title, message) {
    return this.createSystemNotification(title, message, "alert")
  }

  /**
   * Create an update notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification
   */
  static async createUpdateNotification(title, message) {
    return this.createSystemNotification(title, message, "update")
  }

  /**
   * Create a machine alert notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @param {number} machineId - Machine ID
   * @returns {Promise<Object>} - The created notification
   */
  static async createMachineAlertNotification(title, message, priority = 'high', machineId = null) {
    return this.createNotification({
      title,
      message,
      category: 'machine_alert',
      priority,
      severity: priority === 'critical' ? 'critical' : 'warning',
      source: 'machine_monitoring',
      machine_id: machineId,
      userId: null // Broadcast to all users
    })
  }

  /**
   * Create a production notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<Object>} - The created notification
   */
  static async createProductionNotification(title, message, priority = 'medium') {
    return this.createNotification({
      title,
      message,
      category: 'production',
      priority,
      severity: priority === 'critical' ? 'critical' : 'info',
      source: 'production_monitoring',
      userId: null // Broadcast to all users
    })
  }

  /**
   * Create a quality alert notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @param {number} machineId - Machine ID
   * @returns {Promise<Object>} - The created notification
   */
  static async createQualityAlertNotification(title, message, priority = 'high', machineId = null) {
    return this.createNotification({
      title,
      message,
      category: 'quality',
      priority,
      severity: priority === 'critical' ? 'critical' : 'warning',
      source: 'quality_monitoring',
      machine_id: machineId,
      userId: null // Broadcast to all users
    })
  }

  /**
   * Get user email addresses for notification delivery
   * @param {number|null} userId - Specific user ID or null for all users
   * @returns {Promise<Array>} - Array of email addresses
   */
  static async getUserEmails(userId = null) {
    try {
      let query, params;

      if (userId) {
        // Get specific user email
        query = "SELECT email FROM users WHERE id = ? AND email IS NOT NULL AND email != ''";
        params = [userId];
      } else {
        // Get all user emails (for broadcast notifications)
        query = "SELECT email FROM users WHERE email IS NOT NULL AND email != '' AND active = 1";
        params = [];
      }

      const [results] = await db.execute(query, params);
      const emails = results.map(row => row.email).filter(email => email && email.includes('@'));

      console.log(`📧 Retrieved ${emails.length} email addresses for notifications`);
      return emails;

    } catch (error) {
      console.error("❌ Error retrieving user emails:", error);
      return [];
    }
  }

  /**
   * Enhanced notification creation with email delivery
   * @param {Object} notification - Notification data
   * @param {boolean} sendEmail - Whether to send email notification
   * @returns {Promise<Object>} - The created notification with delivery status
   */
  static async createNotificationWithEmail(notification, sendEmail = false) {
    try {
      // Create the notification in database first
      const createdNotification = await this.createNotification(notification);

      // If email delivery is requested and it's a high priority notification
      if (sendEmail && ['high', 'critical'].includes(notification.priority)) {
        try {
          console.log(`📧 Attempting email delivery for ${notification.priority} priority notification`);

          // Get recipient email addresses
          const emails = await this.getUserEmails(notification.userId);

          if (emails.length > 0) {
            let emailResult;

            // Send appropriate email type based on category
            if (notification.category === 'machine_alert') {
              emailResult = await emailNotificationService.sendMachineAlert({
                title: notification.title,
                message: notification.message,
                priority: notification.priority,
                machine_id: notification.machine_id,
                machine_name: `Machine ${notification.machine_id}`, // TODO: Get actual machine name
                source: notification.source
              }, emails);
            } else {
              emailResult = await emailNotificationService.sendNotificationEmail(createdNotification, emails);
            }

            console.log(`✅ Email notification sent successfully: ${emailResult.messageId}`);

            return {
              ...createdNotification,
              emailDelivery: {
                sent: true,
                recipients: emails.length,
                messageId: emailResult.messageId,
                deliveryTime: emailResult.deliveryTime
              }
            };

          } else {
            console.log('⚠️ No email addresses found for notification delivery');
            return {
              ...createdNotification,
              emailDelivery: {
                sent: false,
                reason: 'no_recipients'
              }
            };
          }

        } catch (emailError) {
          console.error('❌ Email delivery failed:', emailError);
          return {
            ...createdNotification,
            emailDelivery: {
              sent: false,
              error: emailError.message
            }
          };
        }
      }

      return createdNotification;

    } catch (error) {
      console.error("❌ Error in createNotificationWithEmail:", error);
      throw error;
    }
  }

  /**
   * Create notification with enhanced dual-channel delivery (SSE + Email)
   * @param {Object} notification - Notification data
   * @returns {Promise<Object>} - The created notification with delivery status
   */
  static async createNotificationWithDualChannel(notification) {
    try {
      console.log(`📧 Creating notification with dual-channel delivery: ${notification.title}`);

      // Create the notification first
      const createdNotification = await this.createNotification(notification);

      // Get delivery preferences and target users
      const deliveryResult = await this.handleDualChannelDelivery(createdNotification);

      // Add delivery status to response
      createdNotification.deliveryStatus = deliveryResult;

      console.log(`✅ Notification created with dual-channel delivery: SSE=${deliveryResult.sse.success}, Email=${deliveryResult.email.success}`);

      return createdNotification;

    } catch (error) {
      console.error('❌ Error creating notification with dual-channel delivery:', error);
      throw error;
    }
  }

  /**
   * Handle dual-channel delivery (SSE + Email) based on user preferences
   * @param {Object} notification - Notification object
   * @returns {Promise<Object>} - Delivery results
   */
  static async handleDualChannelDelivery(notification) {
    const deliveryResult = {
      sse: { attempted: false, success: false, recipients: 0 },
      email: { attempted: false, success: false, recipients: 0, messageId: null },
      escalation: { attempted: false, success: false }
    };

    try {
      // Check if notification should be escalated immediately
      const shouldEscalate = await notificationEscalationService.shouldEscalateImmediately(notification);

      if (shouldEscalate) {
        console.log(`🚨 Immediate escalation required for notification: ${notification.title}`);
        const escalationResult = await notificationEscalationService.escalateImmediately(notification);
        deliveryResult.escalation = {
          attempted: true,
          success: escalationResult,
          type: 'immediate'
        };
      }

      // Get all users for email notifications (settings system removed)
      // TODO: Implement new settings system for notification preferences
      const emailUsersResult = await db.query('SELECT id, email, username FROM users WHERE email IS NOT NULL');
      const emailUsers = emailUsersResult || [];

      // Handle email delivery
      if (emailUsers.length > 0) {
        deliveryResult.email.attempted = true;
        deliveryResult.email.recipients = emailUsers.length;

        const emails = emailUsers.map(user => user.email);
        let emailResult;

        // Send appropriate email type based on category
        if (notification.category === 'machine_alert') {
          emailResult = await emailNotificationService.sendMachineAlert({
            title: notification.title,
            message: notification.message,
            priority: notification.priority,
            machine_id: notification.machine_id,
            machine_name: `Machine ${notification.machine_id}`,
            source: notification.source
          }, emails);
        } else {
          emailResult = await emailNotificationService.sendNotificationEmail(notification, emails);
        }

        deliveryResult.email.success = emailResult?.success || false;
        deliveryResult.email.messageId = emailResult?.messageId || null;
        deliveryResult.email.deliveryTime = emailResult?.deliveryTime || null;

        // If email failed, add to retry queue
        if (!emailResult?.success) {
          console.log(`⚠️ Email delivery failed, adding to retry queue`);

          for (const user of emailUsers) {
            await emailRetryService.addToRetryQueue({
              notificationId: notification.id,
              userId: user.id,
              emailAddress: user.email,
              subject: `🔔 ${notification.title}`,
              htmlContent: emailNotificationService.generateNotificationHTML(notification),
              textContent: emailNotificationService.generateNotificationText(notification),
              priority: notification.priority,
              error: emailResult?.error || 'Unknown email delivery error'
            });
          }
        }

        // Log email delivery attempt
        await this.logDeliveryAttempt(notification.id, emailUsers, 'email', emailResult?.success || false, emailResult?.messageId);
      }

      // Handle SSE delivery (always attempt for real-time notifications)
      deliveryResult.sse.attempted = true;
      // Note: SSE delivery is handled by the calling code (notificationRoutes.js)
      // This is just for tracking purposes

      return deliveryResult;

    } catch (error) {
      console.error('❌ Error in dual-channel delivery:', error);
      return deliveryResult;
    }
  }

  /**
   * Log delivery attempt in notification_delivery_log table
   * @param {number} notificationId - Notification ID
   * @param {Array} users - Array of user objects
   * @param {string} deliveryMethod - Delivery method (email, sse, both)
   * @param {boolean} success - Whether delivery was successful
   * @param {string} messageId - Email message ID (if applicable)
   */
  static async logDeliveryAttempt(notificationId, users, deliveryMethod, success, messageId = null) {
    try {
      for (const user of users) {
        const query = `
          INSERT INTO notification_delivery_log
          (notification_id, user_id, delivery_method, email_status, email_message_id, delivery_time)
          VALUES (?, ?, ?, ?, ?, NOW())
        `;

        await db.execute(query, [
          notificationId,
          user.id,
          deliveryMethod,
          success ? 'sent' : 'failed',
          messageId
        ]);
      }

      console.log(`📝 Logged delivery attempt for ${users.length} users`);

    } catch (error) {
      console.error('❌ Error logging delivery attempt:', error);
    }
  }

  /**
   * Acknowledge a notification
   * @param {number} notificationId - Notification ID
   * @param {number} userId - User ID who acknowledged
   * @returns {Promise<boolean>} - Success status
   */
  static async acknowledgeNotification(notificationId, userId) {
    try {
      const [result] = await db.execute(
        "UPDATE notifications SET acknowledged = true, acknowledged_by = ?, acknowledged_at = NOW() WHERE id = ?",
        [userId, notificationId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error("Database error:", error);
      throw error;
    }
  }

  /**
   * Create a critical machine alert with automatic email delivery
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {number} machineId - Machine ID
   * @returns {Promise<Object>} - The created notification with email delivery status
   */
  static async createCriticalMachineAlert(title, message, machineId = null) {
    console.log(`🚨 Creating critical machine alert with email delivery`);

    return this.createNotificationWithEmail({
      title,
      message,
      category: 'machine_alert',
      priority: 'critical',
      severity: 'critical',
      source: 'machine_monitoring',
      machine_id: machineId,
      userId: null // Broadcast to all users
    }, true); // Enable email delivery
  }

  /**
   * Create a critical production alert with automatic email delivery
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification with email delivery status
   */
  static async createCriticalProductionAlert(title, message) {
    console.log(`🚨 Creating critical production alert with email delivery`);

    return this.createNotificationWithEmail({
      title,
      message,
      category: 'production',
      priority: 'critical',
      severity: 'critical',
      source: 'production_monitoring',
      userId: null // Broadcast to all users
    }, true); // Enable email delivery
  }

  /**
   * Send shift report via email
   * @param {Object} reportData - Shift report data
   * @param {Array} recipients - Optional specific recipients, otherwise uses all user emails
   * @returns {Promise<Object>} - Email delivery result
   */
  static async sendShiftReportEmail(reportData, recipients = null) {
    try {
      console.log(`📊 Sending shift report email`);

      // Get recipients if not provided
      const emailAddresses = recipients || await this.getUserEmails();

      if (emailAddresses.length === 0) {
        console.log('⚠️ No email addresses found for shift report delivery');
        return {
          sent: false,
          reason: 'no_recipients'
        };
      }

      const result = await emailNotificationService.sendShiftReport(reportData, emailAddresses);

      console.log(`✅ Shift report email sent to ${emailAddresses.length} recipients`);
      return result;

    } catch (error) {
      console.error('❌ Failed to send shift report email:', error);
      throw error;
    }
  }

  /**
   * Test email configuration
   * @param {string} testEmail - Test email address
   * @returns {Promise<Object>} - Test result
   */
  static async testEmailConfiguration(testEmail) {
    try {
      console.log(`🧪 Testing email configuration with: ${testEmail}`);

      const result = await emailNotificationService.sendTestEmail(testEmail);

      if (result.success) {
        console.log('✅ Email configuration test successful');
      } else {
        console.log('❌ Email configuration test failed');
      }

      return result;

    } catch (error) {
      console.error('❌ Email configuration test error:', error);
      return {
        success: false,
        message: 'Email test failed',
        error: error.message
      };
    }
  }

  /**
   * Get email service health status
   * @returns {Promise<Object>} - Health status
   */
  static async getEmailServiceHealth() {
    try {
      return await emailNotificationService.healthCheck();
    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'Email service health check failed',
        error: error.message
      };
    }
  }

  /**
   * Get email delivery statistics
   * @returns {Object} - Delivery statistics
   */
  static getEmailDeliveryStats() {
    return emailNotificationService.getDeliveryStats();
  }
}

export default NotificationService

