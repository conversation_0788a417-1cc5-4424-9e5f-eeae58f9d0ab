import React, { useEffect, useState } from 'react';
import superagent from 'superagent';
import { Bar, Line } from 'react-chartjs-2';
import {
  Card, Row, Col, Progress, Statistic, Spin, Alert, Tabs, Descriptions,
  Modal, Table, Tag, Empty, Grid
} from 'antd';
import {
  DashboardOutlined, LineChartOutlined, ClockCircleOutlined,
  CheckCircleOutlined, CloseCircleOutlined
} from '@ant-design/icons';
import MainLayout from './MainLayout';
import { chartConfig } from '../utils/chartConfig';
import { getEnhancedChartOptions, getChartHeight, applyEnhancedSettingsToData } from './chart-config';
import { useSettings } from '../hooks/useSettings';
import SOMIPEM_COLORS from '../styles/brand-colors';

const { useBreakpoint } = Grid;

const Home = () => {
  const screens = useBreakpoint();
  const [currentDate] = useState(new Date().toLocaleDateString('fr-FR'));

  // Get settings for enhanced chart configuration
  const { settings } = useSettings();

  const [state, setState] = useState({
    machineData: [],
    sideCardData: {},
    dailyStats: [],
    selectedMachine: null,
    machineHistory: [],
    historyLoading: false,
    historyError: null,
    loading: true,
    error: null,
    visible: false
  });
  const baseURL = (() => {
    // Check if we're in a browser environment first
    if (typeof window !== 'undefined') {
      const currentOrigin = window.location.origin;

      // If running on ngrok domain, use the same origin (unified architecture)
      if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
        return currentOrigin;
      }

      // For local development, check environment variable first
      if (import.meta.env.VITE_API_URL) {
        return import.meta.env.VITE_API_URL;
      }

      // Fallback to current origin for local development
      return currentOrigin;
    }

    // Fallback for server-side rendering
    return 'http://localhost:5000';
  })();

  // 🔒 SECURITY: HTTP-only cookies configuration - no axios configuration needed
  const cardGradient = 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)';
  const accentColor = '#1890ff';
  const { md } = screens;

  const fetchData = async () => {
    try {
      // 🔒 SECURITY: Use SuperAgent with HTTP-only cookies
      const responses = await Promise.all([
        superagent.get(baseURL + '/api/RealTimeTable').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/MachineCard').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/sidecards').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/dailyStats').withCredentials().timeout(30000).retry(2)
      ]);

      const [rawData, machineCards, sideCards, daily] = responses;

      setState(prev => ({
        ...prev,
        machineData: machineCards.data.map(m => ({
          ...m,
          progress: (m.Total_Quantite_Bon / m.Total_Quantite_Planifier) * 100,
          status: m.Avg_TRS > 80 ? 'success' : m.Avg_TRS > 60 ? 'warning' : 'error'
        })),
        sideCardData: sideCards.data[0],
        dailyStats: daily.data,
        error: null,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
    }
  };

  useEffect(() => {
    const interval = setInterval(fetchData, 15000);
    fetchData();
    return () => clearInterval(interval);
  }, []);
  const fetchMachineHistory = async (machineName) => {
    try {
      setState(prev => ({ ...prev, historyLoading: true, historyError: null }));

      const response = await axios.get(`/api/machineHistory/${machineName}`);

      setState(prev => ({
        ...prev,
        machineHistory: response.data,
        historyLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        historyError: error.message,
        historyLoading: false
      }));
    }
  };
  const sidebarStats = [
    {
      title: "TRS moyen",
      value: state.machineData.length > 0
        ? (state.machineData.reduce((sum, m) => sum + (m.Avg_TRS || 0), 0) / state.machineData.length).toFixed(1)
        : 'N/A',
      suffix: "%"
    },
    {
      title: "Production totale",
      value: state.machineData.reduce((sum, m) => sum + (m.Total_Quantite_Bon || 0), 0) || 'N/A'
    }
  ];
  // Update the machine card click handler
  const handleMachineClick = (machine) => {
    setState(prev => ({
      ...prev,
      selectedMachine: machine.Machine_Name,
      visible: true
    }));
    fetchMachineHistory(machine.Machine_Name);
  };
  const modalContent = () => {
    if (state.historyLoading) {
      return <Spin size="large" style={{ display: 'block', margin: '40px auto' }} />;
    }

    if (state.historyError) {
      return (
        <Alert
          type="error"
          message="Erreur de chargement"
          description={state.historyError}
          showIcon
        />
      );
    }
    return (
      <Table
        columns={[
          { title: 'Cycle', dataIndex: 'cycle', sorter: (a, b) => a.cycle - b.cycle },
          { title: 'Quantité bonne', dataIndex: 'Quantite_Bon' },
          {
            title: 'TRS',
            dataIndex: 'TRS',
            render: value => <Tag color={value > 80 ? 'success' : 'error'}>{value}%</Tag>
          },
          { title: 'Heure', dataIndex: 'Stop_Time' }
        ]}
        dataSource={state.machineHistory}
        pagination={{ pageSize: 5 }}
        scroll={{ x: true }}
      />
    );
  };

  const isMobile = !screens.md;
  const cardPadding = isMobile ? 12 : 24;
  const chartHeight = isMobile ? 220 : 300;
  const tableScrollX = 800;

  // Add responsive grid configurations
  const responsiveGrid = {
    gutter: [16, 16],
    xs: 24,
    sm: 24,
    md: 12,
    lg: 8,
    xl: 6
  };

  // Update renderMachineCard with responsive styles
  const renderMachineCard = (machine) => (
    <Card
      hoverable
      onClick={() => handleMachineClick(machine)}
      style={{
        background: cardGradient,
        borderRadius: 12,
        height: '100%',
        borderLeft: `4px solid ${getStatusColor(machine.status)}`,
        marginBottom: isMobile ? 8 : 0
      }}
    >
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: isMobile ? 8 : 12,
        flexWrap: 'wrap'
      }}>
        <DashboardOutlined style={{
          fontSize: isMobile ? 20 : 24,
          marginRight: 8,
          color: accentColor
        }} />
        <h3 style={{
          margin: 0,
          fontSize: isMobile ? 16 : 18
        }}>{machine.Machine_Name}</h3>
        <Tag
          color={machine.status}
          style={{
            marginLeft: 'auto',
            fontSize: isMobile ? 12 : 14
          }}
        >
          TRS: {machine.Avg_TRS}%
        </Tag>
      </div>

      <Progress
        percent={machine.progress}
        strokeColor={getProgressColor(machine.progress)}
        strokeLinecap="square"
        format={percent => (
          <div style={{
            color: getProgressColor(machine.progress),
            fontSize: isMobile ? 12 : 14
          }}>
            {percent}% d'objectif
          </div>
        )}
      />

      <Row gutter={16} style={{ marginTop: isMobile ? 12 : 16 }}>
        {['Planifié', 'Bon', 'Rejet'].map((title, idx) => (
          <Col key={title} span={8}>
            <Statistic
              title={<span style={{ fontSize: isMobile ? 12 : 14 }}>{title}</span>}
              value={machine[
                idx === 0 ? 'Total_Quantite_Planifier' :
                idx === 1 ? 'Total_Quantite_Bon' : 'Total_Quantite_Rejet'
              ]}
              valueStyle={{ fontSize: isMobile ? 14 : 16 }}
              prefix={[
                <ClockCircleOutlined />,
                <CheckCircleOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />,
                <CloseCircleOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
              ][idx]}
            />
          </Col>
        ))}
      </Row>
    </Card>
  );

  // Update main return structure
  return (
      <div style={{ padding: isMobile ? 8 : 24 }}>
        {/* Error Alert */}
        {state.error && (
          <Alert
            type="error"
            message="Erreur de connexion"
            description={`Dernière erreur: ${state.error} | Mise à jour: ${new Date().toLocaleTimeString()}`}
            showIcon
            closable
            style={{ marginBottom: 16 }}
          />
        )}

        {/* Main Content Grid */}
        <Row gutter={[16, 16]}>
          {/* Machine Statistics */}
          <Col xs={24} md={16}>
  <Card
    title={
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <LineChartOutlined style={{
          fontSize: isMobile ? 18 : 20,
          marginRight: 8,
          color: accentColor
        }} />
        <span style={{ fontSize: isMobile ? 16 : 18 }}>Statistiques des Machines</span>
      </div>
    }
    bordered={false}
    style={{ borderRadius: 16 }}
    bodyStyle={{ padding: isMobile ? 8 : 16 }}
  >
    {state.loading ? (
      <Spin size="large" style={{ display: 'block', margin: '40px auto' }} />
    ) : (
      <Row gutter={[16, 16]}>
        {state.machineData.slice(0, 4).map((machine, index) => (
          <Col
            key={index}
            xs={24}  // Full width on mobile
            sm={24}   // Full width on small screens
            md={12}   // Half width (2 per row) on medium screens
            lg={12}   // Half width on large screens
          >
            {renderMachineCard(machine)}
          </Col>
        ))}
      </Row>
    )}
  </Card>
</Col>

          {/* Key Indicators */}
          <Col xs={24} md={8}>
            <Card
              title={<span style={{ fontSize: isMobile ? 16 : 18 }}>Indicateurs Clés</span>}
              bordered={false}
              style={{ borderRadius: 16 }}
              bodyStyle={{ padding: isMobile ? 8 : 16 }}
            >
              <Row gutter={[8, 8]}>
                {sidebarStats.map((stat, index) => (
                  <Col key={index} span={24}>
                    <Card.Grid style={{
                      width: '100%',
                      background: cardGradient,
                      padding: isMobile ? 12 : 16
                    }}>
                      <Statistic
                        title={<span style={{ fontSize: isMobile ? 12 : 14 }}>{stat.title}</span>}
                        value={stat.value}
                        suffix={stat.suffix}
                        precision={1}
                        valueStyle={{ fontSize: isMobile ? 16 : 18 }}
                      />
                    </Card.Grid>
                  </Col>
                ))}
                <Col span={24}>
                  <Progress
                    percent={(state.sideCardData.total_quantite_bon /
                      (state.sideCardData.total_quantite_bon + state.sideCardData.total_quantite_rejet)) * 100}
                    strokeColor={{
                      '0%': '#87d068',
                      '100%': '#108ee9',
                    }}
                    format={percent => (
                      <div style={{
                        color: '#1890ff',
                        fontSize: isMobile ? 12 : 14
                      }}>
                        {state.sideCardData.total_quantite_bon} /{' '}
                        {state.sideCardData.total_quantite_bon + state.sideCardData.total_quantite_rejet}
                      </div>
                    )}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* Charts Section */}
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col xs={24} md={16}>
            <Card
              title={<span style={{ fontSize: isMobile ? 16 : 18 }}>Analyse de Production</span>}
              style={{ borderRadius: 16 }}
              extra={<Tag color="geekblue">Temps réel</Tag>}
              bodyStyle={{ padding: isMobile ? 8 : 16 }}
            >
              <Bar
                data={applyEnhancedSettingsToData(chartConfig.productionData(state.machineData), settings)}
                options={getEnhancedChartOptions(settings, 'bar', chartConfig.barOptions)}
                height={getChartHeight(settings)}
              />
            </Card>
          </Col>

          <Col xs={24} md={8}>
            <Card
              title={<span style={{ fontSize: isMobile ? 16 : 18 }}>Performance Horaires</span>}
              style={{ borderRadius: 16 }}
              extra={<Tag color="cyan">Dernières 24h</Tag>}
              bodyStyle={{ padding: isMobile ? 8 : 16 }}
            >
              {state.dailyStats.length > 0 ? (
                <Line
                  key={state.dailyStats[0].time_bucket}
                  data={applyEnhancedSettingsToData(chartConfig.dailyData(state.dailyStats), settings)}
                  options={getEnhancedChartOptions(settings, 'line', chartConfig.lineOptions)}
                  height={getChartHeight(settings)}
                />
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={<span style={{ fontSize: isMobile ? 12 : 14 }}>Aucune donnée disponible</span>}
                />
              )}
            </Card>
          </Col>
        </Row>

        {/* Machine Details */}
        <Card
          title={<span style={{ fontSize: isMobile ? 16 : 18 }}>Détails des Machines</span>}
          style={{ marginTop: 16, borderRadius: 16 }}
          bodyStyle={{ padding: '8px 0' }}
        >
          <Tabs defaultActiveKey="1">
            <Tabs.TabPane
              tab={<span style={{ fontSize: isMobile ? 14 : 16 }}>Vue d'Ensemble</span>}
              key="1"
            >
              <Row gutter={[8, 8]} style={{ padding: 8 }}>
                {state.machineData.map(machine => (
                  <Col key={machine.Machine_Name} {...responsiveGrid}>
                    <Card
                      bodyStyle={{
                        padding: 8,
                        fontSize: isMobile ? 12 : 14
                      }}
                    >
                      <h4 style={{
                        margin: 0,
                        fontSize: isMobile ? 14 : 16
                      }}>{machine.Machine_Name}</h4>
                      <Tag
                        color={machine.status}
                        style={{ fontSize: isMobile ? 12 : 14 }}
                      >
                        TRS: {machine.Avg_TRS}%
                      </Tag>
                      <Progress
                        percent={machine.progress}
                        showInfo={false}
                        strokeColor={getProgressColor(machine.progress)}
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            </Tabs.TabPane>
          </Tabs>
        </Card>

        {/* Modal */}
        <Modal
          title={`Historique de ${state.selectedMachine}`}
          visible={state.visible}
          width={isMobile ? '90%' : 800}
          onCancel={() => setState(prev => ({
            ...prev,
            visible: false,
            historyError: null
          }))}
          footer={null}
          destroyOnClose
          bodyStyle={{ padding: isMobile ? 8 : 16 }}
        >
          {modalContent()}
        </Modal>
      </div>
  );
};

// Helper functions
const getStatusColor = (status) => ({
  success: SOMIPEM_COLORS.PRIMARY_BLUE,
  warning: SOMIPEM_COLORS.SECONDARY_BLUE,
  error: SOMIPEM_COLORS.CHART_TERTIARY
}[status]);

const getProgressColor = (percent) =>
  percent >= 90 ? SOMIPEM_COLORS.PRIMARY_BLUE : 
  percent >= 75 ? SOMIPEM_COLORS.SECONDARY_BLUE : 
  SOMIPEM_COLORS.CHART_TERTIARY;

export default Home;