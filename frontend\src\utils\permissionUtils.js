import { actionPermissions } from '../config/permissionConfig';

/**
 * Check if a user has permission for a specific action
 * 
 * @param {Function} hasPermission - Function to check if user has permission
 * @param {Function} hasRole - Function to check if user has role
 * @param {string} actionKey - Key of the action to check
 * @returns {boolean} True if user has permission, false otherwise
 */
export const checkActionPermission = (hasPermission, hasRole, actionKey) => {
  // Get action configuration
  const action = actionPermissions[actionKey];
  
  // If action doesn't exist in config, deny by default
  if (!action) {
    console.warn(`Action "${actionKey}" not found in permission configuration`);
    return false;
  }
  
  // Check if user has required permissions and roles
  return (
    // If permissions specified, user must have them
    (!action.permissions || hasPermission(action.permissions)) &&
    // If roles specified, user must have one
    (!action.roles || hasRole(action.roles))
  );
};

/**
 * Create a permission checker function with the provided permission checking functions
 * 
 * @param {Function} hasPermission - Function to check if user has permission
 * @param {Function} hasRole - Function to check if user has role
 * @returns {Function} Function to check if user has permission for an action
 */
export const createPermissionChecker = (hasPermission, hasRole) => {
  return (actionKey) => checkActionPermission(hasPermission, hasRole, actionKey);
};

/**
 * Filter an array of items based on permissions
 * 
 * @param {Array} items - Array of items to filter
 * @param {Function} hasPermission - Function to check if user has permission
 * @param {Function} hasRole - Function to check if user has role
 * @param {string} permissionKey - Key of the permission property in each item
 * @param {string} roleKey - Key of the role property in each item
 * @returns {Array} Filtered array of items
 */
export const filterByPermission = (
  items, 
  hasPermission, 
  hasRole, 
  permissionKey = 'permissions', 
  roleKey = 'roles'
) => {
  return items.filter(item => {
    // If item doesn't have permissions or roles, allow it
    if (!item[permissionKey] && !item[roleKey]) {
      return true;
    }
    
    // Check if user has required permissions and roles
    return (
      // If permissions specified, user must have them
      (!item[permissionKey] || hasPermission(item[permissionKey])) &&
      // If roles specified, user must have one
      (!item[roleKey] || hasRole(item[roleKey]))
    );
  });
};

export default {
  checkActionPermission,
  createPermissionChecker,
  filterByPermission
};
