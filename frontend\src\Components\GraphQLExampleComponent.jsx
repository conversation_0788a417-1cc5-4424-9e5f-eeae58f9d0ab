/**
 * Usage Examples for GraphQL Hooks
 * Demonstrates how to use useDailyTableGraphQL and useStopTableGraphQL hooks
 */

import React, { useEffect, useState } from 'react';
import useDailyTableGraphQL from '../hooks/useDailyTableGraphQL';
import useStopTableGraphQL from '../hooks/useStopTableGraphQL';

const GraphQLExampleComponent = () => {
  const [productionData, setProductionData] = useState(null);
  const [stopData, setStopData] = useState(null);

  // Initialize hooks
  const {
    loading: productionLoading,
    error: productionError,
    getProductionChart,
    getProductionSidecards,
    getMachineModels,
    getDashboardData
  } = useDailyTableGraphQL();

  const {
    loading: stopLoading,
    error: stopError,
    getAllMachineStops,
    getTop5Stops,
    getStopSidecards,
    getStopDashboardData
  } = useStopTableGraphQL();

  // Example: Fetch production data on component mount
  useEffect(() => {
    const fetchProductionData = async () => {
      try {
        // Get dashboard data with filters
        const filters = {
          machine: 'IPS01',
          dateRangeType: 'week',
          limit: 10
        };
        
        const dashboardData = await getDashboardData(filters);
        setProductionData(dashboardData);
        
        console.log('Production Dashboard Data:', dashboardData);
      } catch (err) {
        console.error('Error fetching production data:', err);
      }
    };

    fetchProductionData();
  }, [getDashboardData]);

  // Example: Fetch stop data on component mount
  useEffect(() => {
    const fetchStopData = async () => {
      try {
        // Get stop dashboard data with filters
        const filters = {
          machine: 'IPS01',
          limit: 20
        };
        
        const stopDashboard = await getStopDashboardData(filters);
        setStopData(stopDashboard);
        
        console.log('Stop Dashboard Data:', stopDashboard);
      } catch (err) {
        console.error('Error fetching stop data:', err);
      }
    };

    fetchStopData();
  }, [getStopDashboardData]);

  // Example: Function to fetch specific machine data
  const handleMachineSelect = async (machineName) => {
    try {
      const filters = { machine: machineName, limit: 5 };
      
      // Fetch production chart for specific machine
      const chartData = await getProductionChart(filters);
      console.log(`Production chart for ${machineName}:`, chartData);
      
      // Fetch stops for specific machine
      const stopsData = await getAllMachineStops(filters);
      console.log(`Stops for ${machineName}:`, stopsData);
      
    } catch (err) {
      console.error(`Error fetching data for ${machineName}:`, err);
    }
  };

  // Example: Function to get summary data
  const fetchSummaryData = async () => {
    try {
      // Get production sidecards
      const prodSidecards = await getProductionSidecards();
      console.log('Production sidecards:', prodSidecards);
      
      // Get stop sidecards
      const stopSidecards = await getStopSidecards();
      console.log('Stop sidecards:', stopSidecards);
      
      // Get top 5 stops
      const topStops = await getTop5Stops({ limit: 5 });
      console.log('Top 5 stops:', topStops);
      
    } catch (err) {
      console.error('Error fetching summary data:', err);
    }
  };

  if (productionLoading || stopLoading) {
    return <div>Loading...</div>;
  }

  if (productionError || stopError) {
    return <div>Error: {productionError || stopError}</div>;
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>GraphQL Hooks Usage Example</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Actions</h3>
        <button onClick={() => handleMachineSelect('IPS01')}>
          Get IPS01 Data
        </button>
        <button onClick={() => handleMachineSelect('IPS02')} style={{ marginLeft: '10px' }}>
          Get IPS02 Data
        </button>
        <button onClick={fetchSummaryData} style={{ marginLeft: '10px' }}>
          Get Summary Data
        </button>
      </div>

      {productionData && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Production Data</h3>
          <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '10px' }}>
            {JSON.stringify(productionData, null, 2)}
          </pre>
        </div>
      )}

      {stopData && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Stop Data</h3>
          <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '10px' }}>
            {JSON.stringify(stopData, null, 2)}
          </pre>
        </div>
      )}

      <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
        <h4>Available Functions:</h4>
        <h5>Production Hook (useDailyTableGraphQL):</h5>
        <ul>
          <li>getAllDailyProduction()</li>
          <li>getProductionChart(filters)</li>
          <li>getProductionSidecards(filters)</li>
          <li>getMachineModels()</li>
          <li>getMachineNames()</li>
          <li>getMachinePerformance(filters)</li>
          <li>getAvailabilityTrend(filters)</li>
          <li>getPerformanceMetrics(filters)</li>
          <li>getDashboardData(filters) - Composite</li>
        </ul>
        
        <h5>Stop Hook (useStopTableGraphQL):</h5>
        <ul>
          <li>getAllMachineStops(filters)</li>
          <li>getTop5Stops(filters)</li>
          <li>getStopStats(filters)</li>
          <li>getStopSidecards(filters)</li>
          <li>getStopMachineModels()</li>
          <li>getStopMachineNames()</li>
          <li>getStopDashboardData(filters) - Composite</li>
          <li>getStopsAnalysisData(filters) - Composite</li>
        </ul>
      </div>
    </div>
  );
};

export default GraphQLExampleComponent;
