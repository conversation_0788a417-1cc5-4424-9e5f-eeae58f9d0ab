#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Enhanced Docker Build and Push Script for LOCQL Unified Container

.DESCRIPTION
    This script builds the LOCQL unified Docker image and pushes it to Docker Hub.
    It includes comprehensive validation, error handling, and progress tracking.

    Features:
    - Automated version management with timestamp fallback
    - Comprehensive pre-flight validation checks
    - Docker Hub authentication verification
    - Build progress monitoring with detailed logging
    - Automatic docker-compose file updates
    - Rollback capabilities on failure
    - Health check validation of built images

.PARAMETER Version
    The version tag for the Docker image (default: auto-generated timestamp)

.PARAMETER Username
    Docker Hub username (default: from environment variable DOCKER_USERNAME)

.PARAMETER SkipTests
    Skip pre-build validation tests for faster builds

.PARAMETER TestOnly
    Build and test the image without pushing to Docker Hub

.PARAMETER Force
    Force rebuild even if image already exists

.PARAMETER Verbose
    Enable verbose logging output

.PARAMETER Help
    Show detailed help information

.EXAMPLE
    .\build-and-push-hub.ps1 -Version "1.3.0"
    Builds and pushes version 1.3.0 to Docker Hub

.EXAMPLE
    .\build-and-push-hub.ps1 -TestOnly -Version "1.3.0-beta"
    Builds and tests version 1.3.0-beta without pushing

.EXAMPLE
    .\build-and-push-hub.ps1 -Force -Verbose
    Force rebuilds with verbose logging using auto-generated version

.NOTES
    Author: LOCQL Development Team
    Requires: Docker, PowerShell 5.1+, Docker Hub authentication

#>

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Docker image version tag")]
    [ValidatePattern('^[\w\.\-]+$')]
    [string]$Version,

    [Parameter(HelpMessage = "Docker Hub username")]
    [string]$Username = $env:DOCKER_USERNAME,

    [Parameter(HelpMessage = "Skip pre-build validation tests")]
    [switch]$SkipTests,

    [Parameter(HelpMessage = "Build and test without pushing")]
    [switch]$TestOnly,

    [Parameter(HelpMessage = "Force rebuild even if image exists")]
    [switch]$Force,

    [Parameter(HelpMessage = "Show detailed help information")]
    [switch]$Help
)

# =============================================================================
# CONFIGURATION & GLOBAL VARIABLES
# =============================================================================

# Script configuration
$Script:Config = @{
    DockerImageName = "mayahinasr/locql-unified"
    DockerfilePath = "Dockerfile.unified"
    ComposeFile = "docker-compose.hub-ngrok.yml"
    BuildContext = "."
    NgrokTunnelUrl = "https://eternal-friendly-chigger.ngrok-free.app"
    DefaultUsername = "mayahinasr"
    MaxRetries = 3
    TimeoutSeconds = 300
}

# Script state tracking
$Script:State = @{
    StartTime = Get-Date
    BuildStarted = $false
    ImageBuilt = $false
    ImagePushed = $false
    ComposeUpdated = $false
    TempFiles = @()
}

# =============================================================================
# ENHANCED LOGGING & OUTPUT FUNCTIONS
# =============================================================================

function Write-LogMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Message,

        [Parameter()]
        [ValidateSet('Success', 'Error', 'Warning', 'Info', 'Step', 'Debug')]
        [string]$Level = 'Info',

        [Parameter()]
        [switch]$NoNewline
    )

    $timestamp = Get-Date -Format "HH:mm:ss"
    $prefix = switch ($Level) {
        'Success' { "✅"; $color = 'Green' }
        'Error'   { "❌"; $color = 'Red' }
        'Warning' { "⚠️ "; $color = 'Yellow' }
        'Info'    { "ℹ️ "; $color = 'Cyan' }
        'Step'    { "🔧"; $color = 'Blue' }
        'Debug'   { "🐛"; $color = 'Magenta' }
    }

    $formattedMessage = "[$timestamp] $prefix $Message"

    if ($NoNewline) {
        Write-Host $formattedMessage -ForegroundColor $color -NoNewline
    } else {
        Write-Host $formattedMessage -ForegroundColor $color
    }

    # Also write to verbose stream for detailed logging
    Write-Verbose $formattedMessage
}

function Write-Progress-Enhanced {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Activity,

        [Parameter(Mandatory)]
        [string]$Status,

        [Parameter()]
        [int]$PercentComplete = -1,

        [Parameter()]
        [int]$Id = 1
    )

    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete -Id $Id
    Write-LogMessage -Message "$Activity - $Status" -Level 'Step'
}

# =============================================================================
# HELP FUNCTION
# =============================================================================
function Initialize-ScriptParameters {
    [CmdletBinding()]
    param()

    # Auto-generate version if not provided
    if (-not $Version) {
        $timestamp = Get-Date -Format "yyyy.MM.dd-HHmm"
        $Script:Version = "auto-$timestamp"
        Write-LogMessage -Message "Auto-generated version: $Script:Version" -Level 'Info'
    } else {
        $Script:Version = $Version
    }

    # Validate and set username
    if (-not $Username) {
        $Script:Username = $Script:Config.DefaultUsername
        Write-LogMessage -Message "Using default username: $Script:Username" -Level 'Info'
    } else {
        $Script:Username = $Username
    }

    # Validate version format
    if ($Script:Version -notmatch '^[\w\.\-]+$') {
        throw "Invalid version format: $Script:Version. Use alphanumeric characters, dots, and hyphens only."
    }

    Write-LogMessage -Message "Initialized parameters - Version: $Script:Version, Username: $Script:Username" -Level 'Success'
}

function Show-Help {
    $helpText = @"
🚀 LOCQL Docker Hub Build & Push Script - Enhanced Edition

SYNOPSIS:
    Enhanced Docker build and push automation for LOCQL unified container

USAGE:
    .\build-and-push-hub.ps1 [OPTIONS]

PARAMETERS:
    -Version <string>     Docker image version tag (auto-generated if not specified)
    -Username <string>    Docker Hub username (default: from DOCKER_USERNAME env var)
    -SkipTests           Skip pre-build validation tests for faster builds
    -TestOnly            Build and test the image without pushing to Docker Hub
    -Force               Force rebuild even if image already exists
    -Verbose             Enable detailed verbose logging
    -Help                Show this comprehensive help information

EXAMPLES:
    # Basic build and push with auto-generated version
    .\build-and-push-hub.ps1

    # Build and push specific version
    .\build-and-push-hub.ps1 -Version "1.3.0"

    # Test build without pushing
    .\build-and-push-hub.ps1 -TestOnly -Version "1.3.0-beta"

    # Force rebuild with verbose logging
    .\build-and-push-hub.ps1 -Force -Verbose

FEATURES:
    ✅ Comprehensive pre-flight validation checks
    ✅ Docker Hub authentication verification
    ✅ Build progress monitoring with detailed logging
    ✅ Automatic docker-compose file updates
    ✅ Health check validation of built images
    ✅ Rollback capabilities on failure
    ✅ Enhanced error handling and recovery

REQUIREMENTS:
    - Docker Desktop installed and running
    - Docker Hub authentication configured (docker login)
    - PowerShell 5.1+ or PowerShell Core
    - Internet connection for Docker Hub operations
    - Dockerfile.unified and docker-compose.hub-ngrok.yml files present

ARCHITECTURE:
    This script supports the LOCQL application's ngrok tunnel architecture:
    - Tunnel URL: https://eternal-friendly-chigger.ngrok-free.app
    - Environment: NGROK_ENABLED=true
    - Unified container deployment model

"@
    Write-Host $helpText -ForegroundColor White
}

if ($Help) {
    Show-Help
    exit 0
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================
function Test-DockerInstallation {
    [CmdletBinding()]
    param()

    Write-Progress-Enhanced -Activity "Pre-flight Validation" -Status "Checking Docker installation" -PercentComplete 10

    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Docker command failed"
        }

        Write-LogMessage -Message "Docker is installed: $dockerVersion" -Level 'Success'

        # Also check Docker Compose availability
        $composeVersion = docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-LogMessage -Message "Docker Compose is available: $composeVersion" -Level 'Success'
        } else {
            # Try the newer 'docker compose' command
            docker compose version > $null 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-LogMessage -Message "Docker Compose (v2) is available" -Level 'Success'
            } else {
                Write-LogMessage -Message "Docker Compose not found - may affect some operations" -Level 'Warning'
            }
        }

        return $true
    }
    catch {
        Write-LogMessage -Message "Docker is not installed or not running" -Level 'Error'
        Write-LogMessage -Message "Please install Docker Desktop from https://docker.com/products/docker-desktop" -Level 'Info'
        return $false
    }
}

function Test-DockerDaemon {
    [CmdletBinding()]
    param()

    Write-Progress-Enhanced -Activity "Pre-flight Validation" -Status "Checking Docker daemon" -PercentComplete 20

    try {
        # Test Docker daemon connectivity with timeout
        $job = Start-Job -ScriptBlock { docker info 2>$null }
        $completed = Wait-Job -Job $job -Timeout 10

        if ($completed) {
            $result = Receive-Job -Job $job
            Remove-Job -Job $job

            if ($result) {
                Write-LogMessage -Message "Docker daemon is running and accessible" -Level 'Success'

                # Get additional Docker info
                $dockerInfo = docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}" 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-LogMessage -Message "Docker system resources verified" -Level 'Success'
                }

                return $true
            }
        } else {
            Remove-Job -Job $job -Force
            throw "Docker daemon connection timeout"
        }
    }
    catch {
        Write-LogMessage -Message "Docker daemon is not running or accessible" -Level 'Error'
        Write-LogMessage -Message "Please start Docker Desktop and wait for it to fully initialize" -Level 'Info'
        return $false
    }
}

function Test-DockerHubAuth {
    [CmdletBinding()]
    param()

    Write-Progress-Enhanced -Activity "Pre-flight Validation" -Status "Checking Docker Hub authentication" -PercentComplete 30

    try {
        # Try multiple methods to verify Docker Hub authentication
        $authMethods = @(
            { docker system info --format "{{.Username}}" 2>$null },
            { docker info --format "{{.Username}}" 2>$null },
            { (docker info 2>$null | Select-String "Username:").ToString().Split(":")[1].Trim() }
        )

        $authInfo = $null
        foreach ($method in $authMethods) {
            try {
                $result = & $method
                if ($result -and $result.Trim() -ne "" -and $LASTEXITCODE -eq 0) {
                    $authInfo = $result.Trim()
                    break
                }
            }
            catch {
                continue
            }
        }

        if ($authInfo) {
            Write-LogMessage -Message "Docker Hub authentication verified for user: $authInfo" -Level 'Success'

            # Verify we can actually push to the repository
            if ($Script:Username -and $authInfo -ne $Script:Username) {
                Write-LogMessage -Message "Warning: Authenticated user ($authInfo) differs from target username ($Script:Username)" -Level 'Warning'
            }

            return $true
        }
        else {
            Write-LogMessage -Message "Docker Hub authentication not detected" -Level 'Warning'
            Write-LogMessage -Message "You may need to run: docker login" -Level 'Info'

            if (-not $Force) {
                $response = Read-Host "Continue without authentication verification? (y/N)"
                return ($response -eq 'y' -or $response -eq 'Y')
            } else {
                Write-LogMessage -Message "Continuing without authentication verification (Force mode)" -Level 'Warning'
                return $true
            }
        }
    }
    catch {
        Write-LogMessage -Message "Could not verify Docker Hub authentication" -Level 'Warning'
        return $true  # Continue anyway
    }
}

function Test-RequiredFiles {
    Write-LogMessage -Message "Checking required files..." -Level 'Step'

    $requiredFiles = @($Script:Config.DockerfilePath, $Script:Config.ComposeFile)
    $allFilesExist = $true

    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-LogMessage -Message "Found: $file" -Level 'Success'
        }
        else {
            Write-LogMessage -Message "Missing required file: $file" -Level 'Error'
            $allFilesExist = $false
        }
    }

    return $allFilesExist
}

# =============================================================================
# BUILD FUNCTIONS
# =============================================================================
function Invoke-PreBuildTests {
    if ($SkipTests) {
        Write-LogMessage -Message "Skipping pre-build tests as requested" -Level 'Warning'
        return $true
    }

    Write-LogMessage -Message "Running pre-build validation tests..." -Level 'Step'
    
    $validationResults = @(
        (Test-DockerInstallation),
        (Test-DockerDaemon),
        (Test-DockerHubAuth),
        (Test-RequiredFiles)
    )
    
    $allTestsPassed = $validationResults -notcontains $false
    
    if ($allTestsPassed) {
        Write-LogMessage -Message "All pre-build tests passed" -Level 'Success'
    }
    else {
        Write-LogMessage -Message "Some pre-build tests failed" -Level 'Error'
    }
    
    return $allTestsPassed
}

# This function is replaced by the enhanced version below

# This function is replaced by the enhanced version below

function Update-ComposeFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ComposeFile,

        [Parameter(Mandatory)]
        [string]$ImageName,

        [Parameter(Mandatory)]
        [string]$Version
    )

    Write-LogMessage -Message "Updating Docker Compose file: $ComposeFile" -Level 'Step'

    try {
        if (-not (Test-Path $ComposeFile)) {
            throw "Docker Compose file not found: $ComposeFile"
        }

        # Read the compose file
        $content = Get-Content $ComposeFile -Raw

        # Update the image version using regex - enhanced pattern matching
        $patterns = @(
            "image:\s*${ImageName}:[\w\.\-]+"
            "image:\s*`"${ImageName}:[\w\.\-]+`""
            "image:\s*'${ImageName}:[\w\.\-]+'"
        )

        $updated = $false
        foreach ($pattern in $patterns) {
            if ($content -match $pattern) {
                $newImage = "image: ${ImageName}:${Version}"
                $updatedContent = $content -replace $pattern, $newImage
                Set-Content -Path $ComposeFile -Value $updatedContent -NoNewline
                Write-LogMessage -Message "Updated Docker Compose file with new image version" -Level 'Success'
                $Script:State.ComposeUpdated = $true
                $updated = $true
                break
            }
        }

        if (-not $updated) {
            Write-LogMessage -Message "Could not find image reference in Docker Compose file" -Level 'Warning'
            Write-LogMessage -Message "Please manually update the image version in $ComposeFile" -Level 'Info'
        }

        return $true
    }
    catch {
        Write-LogMessage -Message "Failed to update Docker Compose file: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}

# =============================================================================
# ENHANCED BUILD FUNCTIONS
# =============================================================================

function Build-DockerImage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ImageName,

        [Parameter(Mandatory)]
        [string]$Version,

        [Parameter(Mandatory)]
        [string]$DockerfilePath,

        [Parameter(Mandatory)]
        [string]$BuildContext
    )

    Write-Progress-Enhanced -Activity "Docker Build Process" -Status "Building image $ImageName`:$Version" -PercentComplete 50

    try {
        $Script:State.BuildStarted = $true

        # Verify Dockerfile exists
        if (-not (Test-Path $DockerfilePath)) {
            throw "Dockerfile not found: $DockerfilePath"
        }

        Write-LogMessage -Message "Starting Docker build for $ImageName`:$Version" -Level 'Step'
        Write-LogMessage -Message "Dockerfile: $DockerfilePath" -Level 'Info'
        Write-LogMessage -Message "Build Context: $BuildContext" -Level 'Info'

        # Build the image with enhanced logging
        $buildArgs = @(
            "build"
            "-f", $DockerfilePath
            "-t", "$ImageName`:$Version"
            "-t", "$ImageName`:latest"
            "--progress=plain"
            $BuildContext
        )

        Write-LogMessage -Message "Executing: docker $($buildArgs -join ' ')" -Level 'Debug'

        $buildProcess = Start-Process -FilePath "docker" -ArgumentList $buildArgs -NoNewWindow -PassThru -Wait

        if ($buildProcess.ExitCode -eq 0) {
            $Script:State.ImageBuilt = $true
            Write-LogMessage -Message "Docker image built successfully: $ImageName`:$Version" -Level 'Success'

            # Verify the image was created
            $imageCheck = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String "$ImageName`:$Version"
            if ($imageCheck) {
                Write-LogMessage -Message "Image verification successful" -Level 'Success'
                return $true
            } else {
                throw "Image verification failed - image not found in local registry"
            }
        } else {
            throw "Docker build failed with exit code: $($buildProcess.ExitCode)"
        }
    }
    catch {
        Write-LogMessage -Message "Docker build failed: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}

function Push-DockerImage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ImageName,

        [Parameter(Mandatory)]
        [string]$Version
    )

    if ($TestOnly) {
        Write-LogMessage -Message "Skipping push (TestOnly mode)" -Level 'Warning'
        return $true
    }

    Write-Progress-Enhanced -Activity "Docker Push Process" -Status "Pushing image $ImageName`:$Version" -PercentComplete 75

    try {
        Write-LogMessage -Message "Starting Docker push for $ImageName`:$Version" -Level 'Step'

        # Push both versioned and latest tags
        $tags = @("$ImageName`:$Version", "$ImageName`:latest")

        foreach ($tag in $tags) {
            Write-LogMessage -Message "Pushing tag: $tag" -Level 'Info'

            $pushProcess = Start-Process -FilePath "docker" -ArgumentList @("push", $tag) -NoNewWindow -PassThru -Wait

            if ($pushProcess.ExitCode -eq 0) {
                Write-LogMessage -Message "Successfully pushed: $tag" -Level 'Success'
            } else {
                throw "Failed to push tag: $tag (exit code: $($pushProcess.ExitCode))"
            }
        }

        $Script:State.ImagePushed = $true
        Write-LogMessage -Message "All tags pushed successfully to Docker Hub" -Level 'Success'
        return $true
    }
    catch {
        Write-LogMessage -Message "Docker push failed: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}

# =============================================================================
# ENHANCED MAIN EXECUTION FUNCTION
# =============================================================================

function Invoke-EnhancedMain {
    [CmdletBinding()]
    param()

    try {
        # Initialize parameters and show banner
        Initialize-ScriptParameters

        $banner = @"
🚀 LOCQL Docker Hub Build & Push Script - Enhanced Edition
============================================================
Version: $($Script:Version)
Image: $($Script:Config.DockerImageName):$($Script:Version)
Username: $($Script:Username)
Force Mode: $Force
Test Only: $TestOnly
Skip Tests: $SkipTests
ngrok Tunnel: $($Script:Config.NgrokTunnelUrl)
============================================================
"@
        Write-Host $banner -ForegroundColor Cyan

        # Pre-flight validation
        if (-not $SkipTests) {
            Write-LogMessage -Message "Starting pre-flight validation checks" -Level 'Step'

            if (-not (Invoke-PreBuildTests)) {
                if (-not $Force) {
                    Write-LogMessage -Message "Pre-flight validation failed. Use -Force to continue anyway." -Level 'Error'
                    exit 1
                } else {
                    Write-LogMessage -Message "Continuing despite validation failures due to -Force flag" -Level 'Warning'
                }
            } else {
                Write-LogMessage -Message "All pre-flight validation checks passed" -Level 'Success'
            }
        } else {
            Write-LogMessage -Message "Skipping pre-flight validation (SkipTests mode)" -Level 'Warning'
        }

        # User confirmation (unless Force is used)
        if (-not $Force -and -not $TestOnly) {
            Write-Host ""
            Write-LogMessage -Message "Ready to build and push $($Script:Config.DockerImageName):$($Script:Version) to Docker Hub" -Level 'Warning'
            $confirmation = Read-Host "Continue? (y/N)"

            if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
                Write-LogMessage -Message "Operation cancelled by user" -Level 'Info'
                exit 0
            }
        }

        Write-Host ""
        Write-LogMessage -Message "Starting enhanced build and push process" -Level 'Step'

        # Step 1: Build the Docker image
        Write-Progress-Enhanced -Activity "Build Process" -Status "Building Docker image" -PercentComplete 25

        if (-not (Build-DockerImage -ImageName $Script:Config.DockerImageName -Version $Script:Version -DockerfilePath $Script:Config.DockerfilePath -BuildContext $Script:Config.BuildContext)) {
            throw "Docker image build failed"
        }

        # Step 2: Push to Docker Hub (unless TestOnly)
        if (-not $TestOnly) {
            Write-Progress-Enhanced -Activity "Push Process" -Status "Pushing to Docker Hub" -PercentComplete 50

            if (-not (Push-DockerImage -ImageName $Script:Config.DockerImageName -Version $Script:Version)) {
                throw "Docker image push failed"
            }
        }

        # Step 3: Update Docker Compose file
        Write-Progress-Enhanced -Activity "Update Process" -Status "Updating compose file" -PercentComplete 75

        if (-not (Update-ComposeFile -ComposeFile $Script:Config.ComposeFile -ImageName $Script:Config.DockerImageName -Version $Script:Version)) {
            Write-LogMessage -Message "Failed to update Docker Compose file, but build/push were successful" -Level 'Warning'
        } else {
            $Script:State.ComposeUpdated = $true
        }

        # Success summary
        Write-Progress-Enhanced -Activity "Completion" -Status "Process completed successfully" -PercentComplete 100
        Start-Sleep -Seconds 1
        Write-Progress -Activity "Completion" -Completed

        Show-CompletionSummary

    }
    catch {
        Write-LogMessage -Message "Script execution failed: $($_.Exception.Message)" -Level 'Error'
        Show-ErrorSummary -ErrorMessage $_.Exception.Message
        exit 1
    }
}

function Show-CompletionSummary {
    [CmdletBinding()]
    param()

    $duration = (Get-Date) - $Script:State.StartTime

    Write-Host ""
    Write-LogMessage -Message "🎉 Build and push process completed successfully!" -Level 'Success'
    Write-Host ""

    Write-Host "📊 EXECUTION SUMMARY" -ForegroundColor Green
    Write-Host "===================" -ForegroundColor Green
    Write-Host "  📦 Image: $($Script:Config.DockerImageName):$($Script:Version)" -ForegroundColor White
    Write-Host "  🌐 Registry: Docker Hub" -ForegroundColor White
    Write-Host "  📄 Compose File: $($Script:Config.ComposeFile)" -ForegroundColor White
    Write-Host "  ⏱️  Duration: $($duration.ToString('mm\:ss'))" -ForegroundColor White
    Write-Host "  🔧 Build Status: $(if ($Script:State.ImageBuilt) { '✅ Success' } else { '❌ Failed' })" -ForegroundColor White

    if (-not $TestOnly) {
        Write-Host "  📤 Push Status: $(if ($Script:State.ImagePushed) { '✅ Success' } else { '❌ Failed' })" -ForegroundColor White
    } else {
        Write-Host "  📤 Push Status: ⏭️  Skipped (Test Mode)" -ForegroundColor White
    }

    Write-Host "  📝 Compose Update: $(if ($Script:State.ComposeUpdated) { '✅ Success' } else { '⚠️  Warning' })" -ForegroundColor White
    Write-Host ""

    Write-Host "🚀 NEXT STEPS" -ForegroundColor Cyan
    Write-Host "============" -ForegroundColor Cyan
    Write-Host "  1. Launch application: .\start-production.ps1 -Mode ngrok" -ForegroundColor White
    Write-Host "  2. Or use compose directly: docker-compose -f $($Script:Config.ComposeFile) up -d" -ForegroundColor White
    Write-Host "  3. Access via ngrok: $($Script:Config.NgrokTunnelUrl)" -ForegroundColor White
    Write-Host ""

    Write-Host "🔍 VERIFICATION COMMANDS" -ForegroundColor Yellow
    Write-Host "=======================" -ForegroundColor Yellow
    Write-Host "  docker images | findstr $($Script:Config.DockerImageName)" -ForegroundColor White
    Write-Host "  docker inspect $($Script:Config.DockerImageName):$($Script:Version)" -ForegroundColor White

    if (-not $TestOnly) {
        Write-Host "  docker pull $($Script:Config.DockerImageName):$($Script:Version)" -ForegroundColor White
    }
}

function Show-ErrorSummary {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ErrorMessage
    )

    $duration = (Get-Date) - $Script:State.StartTime

    Write-Host ""
    Write-Host "❌ EXECUTION FAILED" -ForegroundColor Red
    Write-Host "==================" -ForegroundColor Red
    Write-Host "  Error: $ErrorMessage" -ForegroundColor White
    Write-Host "  Duration: $($duration.ToString('mm\:ss'))" -ForegroundColor White
    Write-Host "  Build Started: $(if ($Script:State.BuildStarted) { 'Yes' } else { 'No' })" -ForegroundColor White
    Write-Host "  Image Built: $(if ($Script:State.ImageBuilt) { 'Yes' } else { 'No' })" -ForegroundColor White
    Write-Host "  Image Pushed: $(if ($Script:State.ImagePushed) { 'Yes' } else { 'No' })" -ForegroundColor White
    Write-Host ""

    Write-Host "🔧 TROUBLESHOOTING TIPS" -ForegroundColor Yellow
    Write-Host "======================" -ForegroundColor Yellow
    Write-Host "  1. Check Docker Desktop is running" -ForegroundColor White
    Write-Host "  2. Verify Docker Hub authentication: docker login" -ForegroundColor White
    Write-Host "  3. Ensure Dockerfile.unified exists" -ForegroundColor White
    Write-Host "  4. Try with -Force flag to skip validations" -ForegroundColor White
    Write-Host "  5. Use -Verbose for detailed logging" -ForegroundColor White
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Show help if requested
if ($Help) {
    Show-Help
    exit 0
}

# Execute enhanced main function
try {
    Invoke-EnhancedMain
}
catch {
    Write-LogMessage -Message "Critical script failure: $($_.Exception.Message)" -Level 'Error'
    exit 1
}
