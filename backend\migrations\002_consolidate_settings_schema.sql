-- Migration: Consolidate settings schema and eliminate redundancy
-- This migration will:
-- 1. Extend user_settings table with missing notification fields
-- 2. Migrate data from user_notification_preferences to user_settings
-- 3. Drop redundant user_notification_preferences table
-- 4. Ensure data consistency and proper constraints

-- Step 1: Add missing columns to user_settings table
ALTER TABLE `user_settings` 
ADD COLUMN IF NOT EXISTS `notification_categories_json` json DEFAULT NULL COMMENT 'JSON object for notification category preferences',
ADD COLUMN IF NOT EXISTS `priority_thresholds_json` json DEFAULT NULL COMMENT 'JSON object for priority threshold preferences',
ADD COLUMN IF NOT EXISTS `notification_email_frequency` enum('immediate','hourly_batch','daily_digest') DEFAULT 'immediate' COMMENT 'Email notification frequency preference',
ADD COLUMN IF NOT EXISTS `notification_email_enabled` tinyint(1) DEFAULT '1' COMMENT 'Whether email notifications are enabled';

-- Step 2: Migrate data from user_notification_preferences to user_settings
-- First, update existing user_settings records with notification preferences
UPDATE user_settings us
JOIN user_notification_preferences unp ON us.user_id = unp.user_id
SET 
  us.notification_categories_json = unp.notification_categories,
  us.priority_thresholds_json = unp.priority_thresholds,
  us.notification_email_frequency = unp.email_frequency,
  us.notification_email_enabled = unp.email_enabled,
  us.updated_at = NOW()
WHERE unp.user_id IS NOT NULL;

-- Step 3: Insert user_settings records for users who only have notification preferences
INSERT INTO user_settings (
  user_id,
  notification_categories_json,
  priority_thresholds_json,
  notification_email_frequency,
  notification_email_enabled,
  created_at,
  updated_at
)
SELECT 
  unp.user_id,
  unp.notification_categories,
  unp.priority_thresholds,
  unp.email_frequency,
  unp.email_enabled,
  NOW(),
  NOW()
FROM user_notification_preferences unp
LEFT JOIN user_settings us ON unp.user_id = us.user_id
WHERE us.user_id IS NULL;

-- Step 4: Ensure all users have settings records with proper defaults
INSERT IGNORE INTO user_settings (
  user_id,
  dark_mode,
  notifications_enabled,
  email_notifications,
  dashboard_refresh_rate,
  data_display_mode,
  compact_mode,
  animations_enabled,
  chart_animations,
  default_view,
  table_rows_per_page,
  default_shift,
  shift_report_notifications,
  shift_report_emails,
  shift1_notifications,
  shift2_notifications,
  shift3_notifications,
  shift1_emails,
  shift2_emails,
  shift3_emails,
  email_format,
  email_digest,
  notify_machine_alerts,
  notify_maintenance,
  notify_updates,
  default_report_format,
  report_auto_download,
  notification_delivery_method,
  critical_alert_email,
  critical_alert_escalation_minutes,
  weekend_notifications,
  notification_categories_json,
  priority_thresholds_json,
  notification_email_frequency,
  notification_email_enabled,
  created_at,
  updated_at
)
SELECT 
  u.id,
  0, -- dark_mode
  1, -- notifications_enabled
  1, -- email_notifications
  60, -- dashboard_refresh_rate
  'chart', -- data_display_mode
  0, -- compact_mode
  1, -- animations_enabled
  1, -- chart_animations
  'dashboard', -- default_view
  20, -- table_rows_per_page
  'Matin', -- default_shift
  1, -- shift_report_notifications
  1, -- shift_report_emails
  1, -- shift1_notifications
  1, -- shift2_notifications
  1, -- shift3_notifications
  1, -- shift1_emails
  1, -- shift2_emails
  1, -- shift3_emails
  'html', -- email_format
  0, -- email_digest
  1, -- notify_machine_alerts
  1, -- notify_maintenance
  1, -- notify_updates
  'pdf', -- default_report_format
  0, -- report_auto_download
  'both', -- notification_delivery_method
  1, -- critical_alert_email
  5, -- critical_alert_escalation_minutes
  1, -- weekend_notifications
  CASE 
    WHEN u.role = 'admin' THEN JSON_OBJECT(
      'machine_alert', true,
      'production', true,
      'quality', true,
      'maintenance', true,
      'update', true,
      'alert', true,
      'info', true
    )
    WHEN u.role = 'user' THEN JSON_OBJECT(
      'machine_alert', true,
      'production', true,
      'quality', false,
      'maintenance', false,
      'update', false,
      'alert', true,
      'info', false
    )
    ELSE JSON_OBJECT(
      'machine_alert', true,
      'production', true,
      'quality', true,
      'maintenance', true,
      'update', false,
      'alert', true,
      'info', false
    )
  END, -- notification_categories_json
  CASE 
    WHEN u.role = 'admin' THEN JSON_OBJECT(
      'critical', true,
      'high', true,
      'medium', true,
      'low', true
    )
    WHEN u.role = 'user' THEN JSON_OBJECT(
      'critical', true,
      'high', true,
      'medium', false,
      'low', false
    )
    ELSE JSON_OBJECT(
      'critical', true,
      'high', true,
      'medium', true,
      'low', false
    )
  END, -- priority_thresholds_json
  CASE 
    WHEN u.role = 'admin' THEN 'immediate'
    WHEN u.role = 'user' THEN 'hourly_batch'
    ELSE 'immediate'
  END, -- notification_email_frequency
  1, -- notification_email_enabled
  NOW(),
  NOW()
FROM users u
WHERE NOT EXISTS (
  SELECT 1 FROM user_settings us WHERE us.user_id = u.id
);

-- Step 5: Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_settings_notification_email_enabled 
ON user_settings(notification_email_enabled);

CREATE INDEX IF NOT EXISTS idx_user_settings_notification_email_frequency 
ON user_settings(notification_email_frequency);

-- Step 6: Update existing notification-related columns to match new structure
-- Sync old notification columns with new JSON structure where possible
UPDATE user_settings 
SET 
  notification_categories_json = JSON_OBJECT(
    'machine_alert', COALESCE(notify_machine_alerts, 1),
    'production', 1,
    'quality', 1,
    'maintenance', COALESCE(notify_maintenance, 1),
    'update', COALESCE(notify_updates, 1),
    'alert', 1,
    'info', 0
  ),
  priority_thresholds_json = JSON_OBJECT(
    'critical', 1,
    'high', 1,
    'medium', 1,
    'low', 0
  ),
  notification_email_enabled = COALESCE(email_notifications, 1),
  updated_at = NOW()
WHERE notification_categories_json IS NULL;

-- Step 7: Verification queries (commented out for production)
-- SELECT COUNT(*) as total_users FROM users;
-- SELECT COUNT(*) as users_with_settings FROM user_settings;
-- SELECT COUNT(*) as users_with_notification_prefs FROM user_notification_preferences;

-- Step 8: Drop the redundant table (commented out for safety - uncomment after verification)
-- DROP TABLE IF EXISTS user_notification_preferences;
