import {SOMIPEM_COLORS} from '../styles/brand-colors';

/**
 * Unified Chart Configuration Manager
 * Single source of truth for all chart settings across the application
 * Connects directly to Settings UI and provides consistent configuration to all charts
 */
class ChartConfigurationManager {
  constructor(settings) {
    // Ensure settings is always an object
    const safeSettings = settings && typeof settings === 'object' ? settings : {};
    this.settings = safeSettings;
    this.charts = safeSettings.charts || {};
    this.theme = safeSettings.theme || {};
    
    // Cache for performance
    this._configCache = new Map();
    this._lastSettingsHash = null;
  }

  /**
   * Update settings and clear cache
   */
  updateSettings(newSettings) {
    this.settings = newSettings;
    this.charts = newSettings.charts || {};
    this.theme = newSettings.theme || {};
    
    // Clear cache when settings change
    this._configCache.clear();
    this._lastSettingsHash = this._hashSettings(newSettings);
  }

  /**
   * Generate hash of settings for cache invalidation
   */
  _hashSettings(settings) {
    return JSON.stringify(settings);
  }

  /**
   * Get cached configuration or generate new one
   */
  _getCachedConfig(key, generator) {
    const settingsHash = this._hashSettings(this.settings);
    const cacheKey = `${key}_${settingsHash}`;
    
    if (this._configCache.has(cacheKey)) {
      return this._configCache.get(cacheKey);
    }
    
    const config = generator();
    this._configCache.set(cacheKey, config);
    return config;
  }

  // ==================== CORE CONFIGURATION METHODS ====================

  /**
   * Get complete chart configuration for any chart type
   */
  getChartConfig(chartType = 'bar', customOptions = {}) {
    return this._getCachedConfig(`chartConfig_${chartType}`, () => {
      return {
        type: this.getChartType(chartType),
        height: this.getChartHeight(),
        margins: this.getChartMargins(),
        colors: this.getColorScheme(),
        responsive: this.getResponsiveConfig(),
        animations: this.getAnimationConfig(),
        interactions: this.getInteractionConfig(),
        display: this.getDisplayConfig(),
        performance: this.getPerformanceConfig(),
        ...customOptions
      };
    });
  }

  /**
   * Get chart type (supports dynamic type switching)
   * Note: defaultType setting has been removed, using fallback only
   */
  getChartType(fallbackType = 'bar', allowedTypes = ['bar', 'line', 'pie']) {
    return allowedTypes.includes(fallbackType) ? fallbackType : 'bar';
  }

  /**
   * Get chart height with compact mode support
   */
  getChartHeight() {
    return this._getCachedConfig('chartHeight', () => {
      const layout = this.charts.layout || {};
      let height = layout.defaultHeight || 300;
      
      // Apply compact mode adjustment
      if (layout.compactMode) {
        height = Math.max(200, height * 0.8);
      }
      
      return height;
    });
  }

  /**
   * Get chart margins based on margin size setting
   */
  getChartMargins() {
    return this._getCachedConfig('chartMargins', () => {
      const marginSize = this.charts.layout?.marginSize || 'standard';
      
      switch (marginSize) {
        case 'compact':
          return { top: 10, right: 10, bottom: 10, left: 10 };
        case 'spacious':
          return { top: 30, right: 30, bottom: 30, left: 30 };
        default: // standard
          return { top: 20, right: 20, bottom: 20, left: 20 };
      }
    });
  }

  /**
   * Get color scheme based on settings
   */
  getColorScheme() {
    return this._getCachedConfig('colorScheme', () => {
      const scheme = this.charts.colorScheme || 'brand';

      switch (scheme) {
        case 'default':
          return null; // Return null to indicate original colors should be used
        case 'blue':
          return ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff'];
        case 'green':
          return ['#52c41a', '#73d13d', '#95de64', '#b7eb8f', '#d9f7be'];
        case 'red':
          return ['#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7', '#ffe1e1'];
        case 'brand':
        default: // brand (formerly somipem)
          return [
            SOMIPEM_COLORS.PRIMARY_BLUE,
            SOMIPEM_COLORS.SECONDARY_BLUE,
            SOMIPEM_COLORS.CHART_TERTIARY,
            SOMIPEM_COLORS.SUCCESS_GREEN,
            SOMIPEM_COLORS.WARNING_ORANGE
          ];
      }
    });
  }

  /**
   * Get responsive container configuration
   */
  getResponsiveConfig() {
    return this._getCachedConfig('responsiveConfig', () => {
      const aspectRatio = this.charts.layout?.aspectRatio || 'auto';
      const performanceConfig = this.getPerformanceConfig();
      
      let aspect;
      switch (aspectRatio) {
        case '16:9':
          aspect = 16/9;
          break;
        case '4:3':
          aspect = 4/3;
          break;
        case '1:1':
          aspect = 1;
          break;
        default:
          aspect = undefined;
      }

      return {
        width: '100%',
        height: this.getChartHeight(),
        aspect: aspect,
        debounceMs: performanceConfig.optimizeForSpeed ? 100 : 0
      };
    });
  }

  /**
   * Get animation configuration
   */
  getAnimationConfig() {
    return this._getCachedConfig('animationConfig', () => {
      const animationsEnabled = this.theme.animationsEnabled && this.theme.chartAnimations;
      const performanceMode = this.charts.performanceMode || false;
      
      return {
        isAnimationActive: animationsEnabled && !performanceMode,
        animationBegin: 0,
        animationDuration: (animationsEnabled && !performanceMode) ? 750 : 0,
        animationEasing: 'ease-in-out'
      };
    });
  }

  /**
   * Get interaction configuration
   */
  getInteractionConfig() {
    return this._getCachedConfig('interactionConfig', () => {
      const interactions = this.charts.interactions || {};

      return {
        hoverEffects: interactions.hoverEffects !== false,
        clickToExpand: interactions.clickToExpand !== false,
        tooltipStyle: interactions.tooltipStyle || 'standard'
      };
    });
  }

  /**
   * Get display configuration
   */
  getDisplayConfig() {
    return this._getCachedConfig('displayConfig', () => {
      const dataDisplay = this.charts.dataDisplay || {};
      
      return {
        showLegend: this.charts.showLegend !== false,
        showDataLabels: dataDisplay.showDataLabels || false,
        showGridLines: dataDisplay.showGridLines !== false,
        showDataPoints: dataDisplay.showDataPoints !== false,
        zeroBasedAxis: dataDisplay.zeroBasedAxis === true // Only when explicitly enabled
      };
    });
  }

  /**
   * Get performance configuration
   */
  getPerformanceConfig() {
    return this._getCachedConfig('performanceConfig', () => {
      const performanceMode = this.charts.performanceMode || false;
      
      if (!performanceMode) {
        return {
          optimizeForSpeed: false,
          maxDataPoints: Infinity,
          enableSampling: false,
          animationDuration: null
        };
      }

      return {
        optimizeForSpeed: true,
        maxDataPoints: 200,
        enableSampling: true,
        animationDuration: 0
      };
    });
  }

  // ==================== RECHARTS-SPECIFIC CONFIGURATIONS ====================

  /**
   * Get Recharts axis configuration
   */
  getAxisConfig() {
    return this._getCachedConfig('axisConfig', () => {
      const textColor = this.theme.darkMode ? '#ffffff' : '#666666';
      const displayConfig = this.getDisplayConfig();

      return {
        tick: {
          fill: textColor,
          fontSize: 11
        },
        axisLine: {
          stroke: textColor
        },
        tickLine: {
          stroke: textColor
        },
        domain: displayConfig.zeroBasedAxis ? [0, 'dataMax'] : ['dataMin', 'dataMax']
      };
    });
  }

  /**
   * Get Recharts grid configuration
   */
  getGridConfig() {
    return this._getCachedConfig('gridConfig', () => {
      const displayConfig = this.getDisplayConfig();

      return {
        stroke: this.theme.darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        strokeDasharray: displayConfig.showGridLines ? '3 3' : 'none',
        opacity: displayConfig.showGridLines ? 1 : 0
      };
    });
  }

  /**
   * Get Recharts tooltip configuration
   */
  getTooltipConfig() {
    return this._getCachedConfig('tooltipConfig', () => {
      const interactionConfig = this.getInteractionConfig();
      const baseStyle = {
        backgroundColor: this.theme.darkMode ? '#1f1f1f' : '#ffffff',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        fontSize: '12px',
        color: this.theme.darkMode ? '#ffffff' : '#000000'
      };

      switch (interactionConfig.tooltipStyle) {
        case 'minimal':
          return {
            contentStyle: {
              ...baseStyle,
              padding: '8px'
            },
            formatter: (value) => [value, ''],
            labelFormatter: () => ''
          };

        case 'detailed':
          return {
            contentStyle: {
              ...baseStyle,
              padding: '16px'
            },
            formatter: (value, name, props) => [
              value,
              `${name} (${props.dataKey})`
            ],
            labelFormatter: (label) => `Category: ${label}`
          };

        default: // standard
          return {
            contentStyle: baseStyle
          };
      }
    });
  }

  /**
   * Get Recharts legend configuration
   */
  getLegendConfig() {
    return this._getCachedConfig('legendConfig', () => {
      const displayConfig = this.getDisplayConfig();

      return {
        wrapperStyle: {
          color: this.theme.darkMode ? '#ffffff' : '#000000',
          fontSize: '12px'
        },
        // Hide legend by returning null content when showLegend is false
        ...(displayConfig.showLegend ? {} : { content: () => null })
      };
    });
  }

  /**
   * Get Recharts hover effects configuration
   */
  getHoverEffectsConfig() {
    return this._getCachedConfig('hoverEffectsConfig', () => {
      const interactionConfig = this.getInteractionConfig();

      if (!interactionConfig.hoverEffects) {
        return {
          cursor: 'default',
          style: {}
        };
      }

      return {
        cursor: 'pointer',
        style: {
          filter: 'brightness(1.1)',
          transition: 'all 0.2s ease'
        }
      };
    });
  }



  /**
   * Get click to expand configuration
   */
  getClickToExpandConfig() {
    return this._getCachedConfig('clickToExpandConfig', () => {
      const interactionConfig = this.getInteractionConfig();

      return {
        enabled: interactionConfig.clickToExpand,
        cursor: interactionConfig.clickToExpand ? 'pointer' : 'default'
      };
    });
  }

  // ==================== CHART-SPECIFIC CONFIGURATIONS ====================

  /**
   * Get Bar chart element configuration
   */
  getBarElementConfig(color, index = 0) {
    return this._getCachedConfig(`barElement_${color}_${index}`, () => {
      const hoverEffects = this.getHoverEffectsConfig();
      const animationConfig = this.getAnimationConfig();
      const colors = this.getColorScheme();

      // Handle null colors (default mode) by using provided color or falling back to brand colors
      let fillColor;
      if (color) {
        fillColor = color;
      } else if (colors && colors.length > 0) {
        fillColor = colors[index % colors.length];
      } else {
        // Default mode - use brand colors as fallback
        const defaultColors = [
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ];
        fillColor = defaultColors[index % defaultColors.length];
      }

      return {
        fill: fillColor,
        radius: [4, 4, 0, 0],
        ...animationConfig,
        ...(hoverEffects.cursor === 'pointer' && {
          style: hoverEffects.style,
          cursor: hoverEffects.cursor
        })
      };
    });
  }

  /**
   * Get Line chart element configuration
   */
  getLineElementConfig(color, index = 0) {
    return this._getCachedConfig(`lineElement_${color}_${index}`, () => {
      const hoverEffects = this.getHoverEffectsConfig();
      const animationConfig = this.getAnimationConfig();
      const colors = this.getColorScheme();
      const displayConfig = this.getDisplayConfig();

      // Handle null colors (default mode) by using provided color or falling back to brand colors
      let strokeColor;
      if (color) {
        strokeColor = color;
      } else if (colors && colors.length > 0) {
        strokeColor = colors[index % colors.length];
      } else {
        // Default mode - use brand colors as fallback
        const defaultColors = [
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ];
        strokeColor = defaultColors[index % defaultColors.length];
      }

      return {
        stroke: strokeColor,
        strokeWidth: 2,
        dot: displayConfig.showDataPoints ? {
          fill: strokeColor,
          strokeWidth: 2,
          r: 4
        } : false,
        activeDot: hoverEffects.cursor === 'pointer' ? {
          r: 6,
          stroke: strokeColor,
          strokeWidth: 2,
          fill: '#fff'
        } : { r: 4 },
        ...animationConfig
      };
    });
  }

  /**
   * Apply settings to chart data (for pie charts with colors)
   */
  applySettingsToData(data, chartType = 'bar') {
    if (!data) return data;

    const colors = this.getColorScheme();

    // For pie charts, add colors to each data point
    if (chartType === 'pie') {
      // Handle null colors (default mode) 
      if (!colors || colors.length === 0) {
        // In default mode, don't override colors - let charts use their original colors
        return data;
      }
      
      return data.map((item, index) => ({
        ...item,
        fill: colors[index % colors.length]
      }));
    }

    return data;
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get all responsive container props
   */
  getResponsiveContainerProps() {
    return this.getResponsiveConfig();
  }

  /**
   * Check if animations are active
   */
  areAnimationsActive() {
    return this.getAnimationConfig().isAnimationActive;
  }

  /**
   * Get text color based on theme
   */
  getTextColor() {
    return this.theme.darkMode ? '#ffffff' : '#666666';
  }

  /**
   * Get primary color from color scheme
   */
  getPrimaryColor() {
    const colors = this.getColorScheme();
    if (!colors || colors.length === 0) {
      // Return a sensible default when in 'default' mode or no colors available
      return SOMIPEM_COLORS.PRIMARY_BLUE;
    }
    return colors[0];
  }

  /**
   * Reset cache (useful for testing or manual cache invalidation)
   */
  clearCache() {
    this._configCache.clear();
  }

  /**
   * Get cache statistics (for debugging)
   */
  getCacheStats() {
    return {
      size: this._configCache.size,
      keys: Array.from(this._configCache.keys())
    };
  }
}

export default ChartConfigurationManager;
