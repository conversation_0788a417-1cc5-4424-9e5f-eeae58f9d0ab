// Test script to verify default color mode works correctly
import ChartConfigurationManager from './frontend/src/config/ChartConfigurationManager.js';

console.log('Testing Chart Configuration Manager with default color mode...');

// Test with default mode settings
const settingsWithDefault = {
  charts: {
    colorScheme: 'default'
  },
  theme: {
    darkMode: false
  }
};

const manager = new ChartConfigurationManager(settingsWithDefault);

console.log('1. Testing getColorScheme() with default mode:');
const colorScheme = manager.getColorScheme();
console.log('   Result:', colorScheme); // Should be null

console.log('2. Testing getPrimaryColor() with default mode:');
const primaryColor = manager.getPrimaryColor();
console.log('   Result:', primaryColor); // Should be fallback color

console.log('3. Testing getBarElementConfig() with default mode:');
const barConfig = manager.getBarElementConfig(null, 0);
console.log('   Fill color:', barConfig.fill); // Should not crash

console.log('4. Testing getLineElementConfig() with default mode:');
const lineConfig = manager.getLineElementConfig(null, 0);
console.log('   Stroke color:', lineConfig.stroke); // Should not crash

console.log('5. Testing applySettingsToData() with pie chart in default mode:');
const testData = [{ name: 'A', value: 10 }, { name: 'B', value: 20 }];
const processedData = manager.applySettingsToData(testData, 'pie');
console.log('   Result:', processedData); // Should return original data without color changes

console.log('\nAll tests completed! Default mode should work without null errors.');
