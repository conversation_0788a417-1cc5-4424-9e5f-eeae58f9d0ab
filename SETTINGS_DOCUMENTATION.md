# Settings System Documentation

## Overview
This document provides comprehensive documentation for all settings in the system, their purpose, and their immediate effects.

## Settings Categories

### 🎨 Theme & Display Settings

#### Dark Mode
- **Purpose**: Switch between light and dark themes
- **Immediate Effect**: Instantly changes all UI components to dark/light theme
- **Components Affected**: All cards, tables, menus, forms, modals, tooltips
- **Default**: `false` (Light mode)

#### Compact Mode
- **Purpose**: Reduce spacing and padding for more content density
- **Immediate Effect**: Reduces padding, margins, and font sizes across UI
- **Components Affected**: Cards, tables, buttons, forms, statistics
- **Default**: `false` (Normal spacing)

#### Animations Enabled
- **Purpose**: Control smooth transitions and animations
- **Immediate Effect**: Enables/disables all CSS transitions and animations
- **Components Affected**: All animated elements, page transitions
- **Default**: `true` (Animations enabled)

#### Chart Animations
- **Purpose**: Control chart-specific animations and transitions
- **Immediate Effect**: Enables/disables chart rendering animations
- **Components Affected**: All charts and data visualizations
- **Default**: `true` (Chart animations enabled)

### 📊 Table Settings

#### Default Page Size
- **Purpose**: Set default number of rows per page in tables
- **Immediate Effect**: Changes pagination in all tables immediately
- **Options**: 10, 20, 50, 100 rows
- **Components Affected**: All data tables, pagination controls
- **Default**: `20` rows

#### Page Size Options
- **Purpose**: Available page size options in pagination dropdowns
- **Immediate Effect**: Updates pagination dropdown options
- **Components Affected**: Table pagination controls
- **Default**: `[10, 20, 50, 100]`

#### Virtualization Threshold
- **Purpose**: Enable virtualization for large datasets
- **Immediate Effect**: Activates virtual scrolling for tables exceeding threshold
- **Components Affected**: Large data tables
- **Default**: `100` rows

#### Show Quick Jumper
- **Purpose**: Display page jump input in pagination
- **Immediate Effect**: Shows/hides quick page navigation input
- **Components Affected**: Table pagination controls
- **Default**: `true` (Show quick jumper)

### 📈 Chart Settings

#### Default Chart Type
- **Purpose**: Default visualization type for new charts
- **Immediate Effect**: Changes chart type in preview and new charts
- **Options**: Bar, Line, Pie, Area
- **Components Affected**: Chart components, data visualizations
- **Default**: `'bar'`

#### Show Legend
- **Purpose**: Display chart legends
- **Immediate Effect**: Shows/hides legends in all charts
- **Components Affected**: All chart components
- **Default**: `true` (Show legends)

#### Color Scheme
- **Purpose**: Chart color palette selection
- **Immediate Effect**: Changes colors in all charts immediately
- **Options**: SOMIPEM, Blue, Green, Red
- **Components Affected**: All charts and data visualizations
- **Default**: `'somipem'`

#### Performance Mode
- **Purpose**: Optimize charts for large datasets
- **Immediate Effect**: Reduces chart complexity for better performance
- **Components Affected**: All chart components
- **Default**: `false` (Full quality)

### 🔄 Refresh Settings

#### Dashboard Refresh Interval
- **Purpose**: How often to refresh dashboard data
- **Immediate Effect**: Changes auto-refresh timing immediately
- **Range**: 30 seconds to 1 hour
- **Components Affected**: Dashboard components, data fetching
- **Default**: `300` seconds (5 minutes)

#### Real-time Data Interval
- **Purpose**: How often to refresh real-time data
- **Immediate Effect**: Updates real-time data refresh frequency
- **Range**: 10 seconds to 10 minutes
- **Components Affected**: Real-time data components
- **Default**: `60` seconds (1 minute)

#### Auto Refresh Enabled
- **Purpose**: Enable automatic data refreshing
- **Immediate Effect**: Starts/stops automatic data updates
- **Components Affected**: All data components with auto-refresh
- **Default**: `true` (Auto refresh enabled)

#### Background Refresh
- **Purpose**: Refresh data when tab is not active
- **Immediate Effect**: Enables/disables background data updates
- **Components Affected**: Background data fetching processes
- **Default**: `true` (Background refresh enabled)

### 🔔 Notification Settings

#### Notification Categories
- **Purpose**: Control which types of notifications to receive
- **Immediate Effect**: Filters notifications by category
- **Categories**: Machine Alert, Production, Quality, Maintenance, Alert, Info, Updates
- **Components Affected**: Notification system, alert filtering
- **Default**: All categories enabled

#### Priority Levels
- **Purpose**: Control which priority levels to show
- **Immediate Effect**: Filters notifications by priority
- **Levels**: Critical, High, Medium, Low
- **Components Affected**: Notification filtering system
- **Default**: All priorities enabled

#### Delivery Methods
- **Purpose**: Choose how to receive notifications
- **Immediate Effect**: Enables/disables notification channels
- **Methods**: SSE (Server-Sent Events), Email, Browser
- **Components Affected**: Notification delivery system
- **Default**: SSE and Browser enabled, Email disabled

#### Notification Behavior
- **Purpose**: Control notification display behavior
- **Immediate Effect**: Changes notification appearance and behavior
- **Settings**: Sound, Auto Close, Auto Close Delay, Max Visible
- **Components Affected**: Notification display system
- **Defaults**: Sound enabled, Auto Close disabled, 5000ms delay, 5 max visible

### 📧 Email Settings

#### Enable Email Notifications
- **Purpose**: Receive notifications via email
- **Immediate Effect**: Activates/deactivates email notification system
- **Components Affected**: Email notification service
- **Default**: `false` (Email disabled)

#### Email Frequency
- **Purpose**: How often to send email notifications
- **Immediate Effect**: Changes email sending frequency
- **Options**: Immediate, Hourly Batch, Daily Digest
- **Components Affected**: Email scheduling system
- **Default**: `'immediate'`

#### Email Template
- **Purpose**: Choose email template style
- **Immediate Effect**: Changes email formatting and content
- **Options**: Minimal, Standard, Detailed
- **Components Affected**: Email generation system
- **Default**: `'standard'`

#### Email Categories & Priorities
- **Purpose**: Control which notifications to send via email
- **Immediate Effect**: Filters email notifications
- **Components Affected**: Email filtering system
- **Default**: Critical and high priority alerts only

#### Quiet Hours
- **Purpose**: Disable email notifications during specific hours
- **Immediate Effect**: Stops email sending during configured hours
- **Settings**: Enable/disable, Start time, End time, Timezone
- **Components Affected**: Email scheduling system
- **Default**: Disabled

#### Batch Settings
- **Purpose**: Configure email batching and digest options
- **Immediate Effect**: Changes email grouping and timing
- **Settings**: Hourly batch, Daily digest, Max notifications per batch
- **Components Affected**: Email batching system
- **Default**: Batching disabled

### 📄 Report Settings

#### Auto Generate Reports
- **Purpose**: Automatically generate reports
- **Immediate Effect**: Enables/disables automatic report generation
- **Components Affected**: Report generation system
- **Default**: `false` (Manual generation)

#### Report Format
- **Purpose**: Default format for generated reports
- **Immediate Effect**: Changes report output format
- **Options**: PDF, HTML, Excel
- **Components Affected**: Report generation system
- **Default**: `'pdf'`

#### Report Quality
- **Purpose**: Quality level for generated reports
- **Immediate Effect**: Changes report generation speed and quality
- **Options**: Low (Fast), Standard, High (Slow)
- **Components Affected**: Report generation system
- **Default**: `'standard'`

#### Include Charts/Tables
- **Purpose**: Include charts and tables in reports
- **Immediate Effect**: Adds/removes content from generated reports
- **Components Affected**: Report content generation
- **Default**: Both enabled

#### Report Scheduling
- **Purpose**: Schedule automatic report generation
- **Immediate Effect**: Sets up/cancels scheduled report generation
- **Options**: Daily, Weekly, Monthly schedules
- **Components Affected**: Report scheduling system
- **Default**: All schedules disabled

### ⚡ Performance Settings

#### Enable Caching
- **Purpose**: Cache data to improve performance
- **Immediate Effect**: Activates/deactivates data caching
- **Components Affected**: Data fetching and storage systems
- **Default**: `true` (Caching enabled)

#### Cache Duration
- **Purpose**: How long to cache data
- **Immediate Effect**: Changes cache expiration time
- **Range**: 60 seconds to 1 hour
- **Components Affected**: Caching system
- **Default**: `300` seconds (5 minutes)

#### Cache Strategy
- **Purpose**: Caching strategy selection
- **Immediate Effect**: Changes how data is cached and retrieved
- **Options**: None, Basic, Smart, Aggressive
- **Components Affected**: Caching system
- **Default**: `'smart'`

#### Lazy Loading
- **Purpose**: Load content as needed
- **Immediate Effect**: Enables/disables lazy loading for components
- **Components Affected**: Images, charts, large data sets
- **Default**: `true` (Lazy loading enabled)

#### Virtualization
- **Purpose**: Optimize large lists and tables
- **Immediate Effect**: Enables/disables virtual scrolling
- **Components Affected**: Large data tables and lists
- **Default**: `true` (Virtualization enabled)

#### Compression
- **Purpose**: Compress data transfers
- **Immediate Effect**: Enables/disables data compression
- **Components Affected**: API requests and responses
- **Default**: `false` (Compression disabled)

## Immediate Effect Guarantee

Every setting change produces an immediate, visible effect:

1. **Visual Changes**: Theme, layout, and display settings apply instantly
2. **Behavioral Changes**: Functionality changes take effect immediately
3. **Performance Changes**: Optimization settings activate without delay
4. **Data Changes**: Refresh intervals and caching changes apply immediately
5. **Notification Changes**: Filtering and delivery changes are instant

## Testing Settings Effects

To verify settings are working correctly:

1. Navigate to Settings page (`/settings`)
2. Use the Live Preview tab to see immediate effects
3. Toggle settings and observe instant changes
4. Check the Settings Effects Demo section for detailed feedback
5. Use Quick Theme Controls for rapid testing

## Troubleshooting

If settings don't produce immediate effects:

1. Check browser console for errors
2. Verify settings are being saved (check network tab)
3. Ensure components are using the settings context
4. Check if rate limiting is blocking updates
5. Verify provider hierarchy is correct

## Technical Implementation

- **Storage**: JSON column in `user_settings` database table
- **Context**: React Context API with optimistic updates
- **API**: RESTful endpoints with comprehensive validation
- **Rate Limiting**: 50 updates per minute per user
- **Persistence**: Automatic database synchronization
- **Real-time**: Immediate UI updates with rollback on errors
