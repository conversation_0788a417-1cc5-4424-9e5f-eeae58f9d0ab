/* Styles pour la page de profil utilisateur */
.profile-card {
    transition: all 0.3s ease;
    overflow: hidden;
  }
  
  .profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  .profile-avatar {
    transition: all 0.3s ease;
  }
  
  .profile-avatar:hover {
    transform: scale(1.05);
  }
  
  .profile-tabs-card {
    transition: all 0.3s ease;
  }
  
  /* Styles pour le mode sombre */
  .dark .profile-card,
  .dark .profile-tabs-card {
    background-color: #1f1f1f;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  }
  
  .dark .ant-descriptions-item-label,
  .dark .ant-form-item-label > label {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .dark .ant-descriptions-item-content,
  .dark .ant-input,
  .dark .ant-select-selector,
  .dark .ant-select-selection-item {
    color: rgba(255, 255, 255, 0.65);
  }
  
  .dark .ant-input,
  .dark .ant-input-password,
  .dark .ant-select-selector {
    background-color: #141414;
    border-color: #434343;
  }
  
  .dark .ant-input:hover,
  .dark .ant-input-password:hover,
  .dark .ant-select-selector:hover {
    border-color: var(--somipem-secondary-blue, #3B82F6);
  }
  
  .dark .ant-input:focus,
  .dark .ant-input-password:focus,
  .dark .ant-select-selector:focus {
    border-color: var(--somipem-primary-blue, #1E3A8A);
    box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.2);
  }
  
  .dark .ant-table {
    background-color: #1f1f1f;
  }
  
  .dark .ant-table-thead > tr > th {
    background-color: #141414;
    color: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid #303030;
  }
  
  .dark .ant-table-tbody > tr > td {
    border-bottom: 1px solid #303030;
  }
  
  .dark .ant-table-tbody > tr:hover > td {
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  .dark .ant-modal-content,
  .dark .ant-modal-header {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .dark .ant-modal-title {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .dark .ant-modal-close {
    color: rgba(255, 255, 255, 0.45);
  }
  
  .dark .ant-modal-close:hover {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .dark .ant-divider {
    border-top-color: #303030;
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .profile-card {
    animation: fadeIn 0.5s ease;
  }
  
  /* Styles pour les tableaux */
  .ant-table-wrapper {
    border-radius: 8px;
    overflow: hidden;
  }
  
  .ant-table-thead > tr > th {
    font-weight: 600;
  }
  
  /* Styles pour les formulaires */
  .ant-form-item-label > label {
    font-weight: 500;
  }
  
  .ant-form-item-explain-error {
    margin-top: 4px;
  }
  
  /* Styles pour les boutons */
  .ant-btn {
    border-radius: 6px;
    transition: all 0.3s ease;
  }
  
  .ant-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .ant-btn-primary:hover {
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3);
  }
  
  /* Styles pour les badges */
  .ant-badge-status-dot {
    width: 8px;
    height: 8px;
  }
  
  /* Styles pour les tags */
  .ant-tag {
    border-radius: 4px;
    padding: 2px 8px;
    font-weight: 500;
  }
  
  /* Styles pour les modals */
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
  }
  
  .ant-modal-header {
    padding: 16px 24px;
  }
  
  .ant-modal-body {
    padding: 24px;
  }
  
  .ant-modal-footer {
    padding: 16px 24px;
  }
  
  /* Styles pour les tabs */
  .ant-tabs-tab {
    padding: 12px 16px;
  }
  
  .ant-tabs-tab-active {
    font-weight: 500;
  }
  
  /* Styles pour les descriptions */
  .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgba(0, 0, 0, 0.02);
  }
  
  .dark .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  /* Styles pour les tooltips */
  .ant-tooltip-inner {
    border-radius: 4px;
    padding: 6px 12px;
  }
  
  /* Styles pour les popconfirm */
  .ant-popover-inner {
    border-radius: 8px;
  }
  
  /* Styles pour les selects */
  .ant-select-dropdown {
    border-radius: 8px;
  }
  
  .dark .ant-select-dropdown {
    background-color: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32);
  }
  
  .dark .ant-select-item {
    color: rgba(255, 255, 255, 0.65);
  }
  
  .dark .ant-select-item-option-active {
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  .dark .ant-select-item-option-selected {
    background-color: var(--somipem-selected-bg, rgba(30, 58, 138, 0.1));
    color: var(--somipem-primary-blue, #1E3A8A);
  }
  
  /* Styles responsifs */
  @media (max-width: 768px) {
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      padding: 12px 8px;
    }
  
    .ant-table {
      font-size: 13px;
    }
  
    .ant-tabs-tab {
      padding: 8px 12px;
    }
  }
  
  