import{r as l,a2 as I,ag as N,a as g,R as a,M as j,d as M,ay as H,T as _,A as Y,m as G,E as z,D as V,e as Q,f as L,g as J,l as q}from"./index-LbZyOyVE.js";import{d as K}from"./dayjs.min-dvN_FXBc.js";import{R as $}from"./SearchOutlined-DzAh4Hfi.js";import{L as k}from"./index-DaGThAV-.js";import{R as A}from"./ClockCircleOutlined-DqsC6tNJ.js";import{R as X}from"./FileTextOutlined-DeApLYgQ.js";var Z={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},ee={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M957.6 507.4L603.2 158.2a7.9 7.9 0 00-11.2 0L353.3 393.4a8.03 8.03 0 00-.1 11.3l.1.1 40 39.4-117.2 115.3a8.03 8.03 0 00-.1 11.3l.1.1 39.5 38.9-189.1 187H72.1c-4.4 0-8.1 3.6-8.1 8V860c0 4.4 3.6 8 8 8h344.9c2.1 0 4.1-.8 5.6-2.3l76.1-75.6 40.4 39.8a7.9 7.9 0 0011.2 0l117.1-115.6 40.1 39.5a7.9 7.9 0 0011.2 0l238.7-235.2c3.4-3 3.4-8 .3-11.2zM389.8 796.2H229.6l134.4-133 80.1 78.9-54.3 54.1zm154.8-62.1L373.2 565.2l68.6-67.6 171.4 168.9-68.6 67.6zM713.1 658L450.3 399.1 597.6 254l262.8 259-147.3 145z"}}]},name:"highlight",theme:"outlined"},te={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};function T(){return T=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(s[r]=t[r])}return s},T.apply(this,arguments)}const re=(s,e)=>l.createElement(I,T({},s,{ref:e,icon:Z})),ae=l.forwardRef(re);function C(){return C=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(s[r]=t[r])}return s},C.apply(this,arguments)}const ne=(s,e)=>l.createElement(I,C({},s,{ref:e,icon:ee})),se=l.forwardRef(ne);function O(){return O=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(s[r]=t[r])}return s},O.apply(this,arguments)}const oe=(s,e)=>l.createElement(I,O({},s,{ref:e,icon:te})),be=l.forwardRef(oe);var x={exports:{}},ce=x.exports,D;function ie(){return D||(D=1,function(s,e){(function(t,r){s.exports=r()})(ce,function(){var t="day";return function(r,i,d){var u=function(c){return c.add(4-c.isoWeekday(),t)},h=i.prototype;h.isoWeekYear=function(){return u(this).year()},h.isoWeek=function(c){if(!this.$utils().u(c))return this.add(7*(c-this.isoWeek()),t);var p,y,m,b,R=u(this),w=(p=this.isoWeekYear(),y=this.$u,m=(y?d.utc:d)().year(p).startOf("year"),b=4-m.isoWeekday(),m.isoWeekday()>4&&(b+=7),m.add(b,t));return R.diff(w,"week")+1},h.isoWeekday=function(c){return this.$utils().u(c)?this.day()||7:this.day(this.day()%7?c:c-7)};var v=h.startOf;h.startOf=function(c,p){var y=this.$utils(),m=!!y.u(p)||p;return y.p(c)==="isoweek"?m?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):v.bind(this)(c,p)}}})}(x)),x.exports}var le=ie();const ve=N(le),de=(()=>{if(typeof window<"u"){const s=window.location.origin;return s.includes("ngrok-free.app")||s.includes("ngrok.io")?(console.log("🔌 SearchService detected ngrok deployment - using same origin:",s),`${s}/api`):(console.log("🔌 SearchService using VITE_API_URL for local development:","http://localhost:5000"),"http://localhost:5000/api")}return"http://localhost:5000/api"})();class ue{constructor(){this.baseURL=`${de}/search`}async globalSearch(e,t={}){try{const{page:r=1,size:i=20}=t;return(await g.get(`${this.baseURL}/global`).query({q:e,page:r,size:i}).set("withCredentials",!0).retry(2)).body}catch(r){throw console.error("Global search error:",r),this.handleError(r)}}async searchMachineSessions(e={}){try{return(await g.get(`${this.baseURL}/sessions`).query(e).set("withCredentials",!0).retry(2)).body}catch(t){throw console.error("Machine sessions search error:",t),this.handleError(t)}}async searchReports(e={}){try{return(await g.get(`${this.baseURL}/reports`).query(e).set("withCredentials",!0).retry(2)).body}catch(t){throw console.error("Reports search error:",t),this.handleError(t)}}async searchProductionData(e={}){try{const r=(await g.get(`${this.baseURL}/production`).query(e).set("withCredentials",!0).timeout(1e4).retry(2)).body;return r.searchMethod==="sql_fallback"&&console.warn("Using SQL fallback for production search"),r}catch(t){throw console.error("Production search error:",t),t.code==="ECONNABORTED"||t.message.includes("timeout")?new Error("La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques."):this.handleError(t)}}async searchMachineStops(e={}){try{const r=(await g.get(`${this.baseURL}/stops`).query(e).set("withCredentials",!0).timeout(1e4).retry(2)).body;return r.searchMethod==="sql_fallback"&&console.warn("Using SQL fallback for machine stops search"),r}catch(t){throw console.error("Machine stops search error:",t),t.code==="ECONNABORTED"||t.message.includes("timeout")?new Error("La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques."):this.handleError(t)}}async getMachinePerformanceAnalytics(e,t){try{return(await g.get(`${this.baseURL}/analytics/machine-performance`).query({dateFrom:e,dateTo:t}).set("withCredentials",!0).retry(2)).body}catch(r){throw console.error("Analytics error:",r),this.handleError(r)}}async getSuggestions(e,t="machineName",r=10){try{return(await g.get(`${this.baseURL}/suggest`).query({q:e,field:t,size:r}).set("withCredentials",!0).retry(2)).body.suggestions}catch(i){return console.error("Suggestions error:",i),[]}}async checkHealth(){try{return(await g.get(`${this.baseURL}/health`).set("withCredentials",!0).retry(2)).body}catch(e){return console.error("Health check error:",e),{elasticsearch:{status:"error",error:e.message}}}}async reindex(e="all",t={}){try{return(await g.post(`${this.baseURL}/reindex`).send({index:e,...t}).set("withCredentials",!0).retry(2)).body}catch(r){throw console.error("Reindex error:",r),this.handleError(r)}}handleError(e){return e.response?{message:e.response.data.error||"Search request failed",details:e.response.data.details,status:e.response.status}:e.request?{message:"No response from search service",details:"Please check your connection and try again"}:{message:"Search request failed",details:e.message}}buildMachineSessionFilters(e){const t={};return e.query&&(t.q=e.query),e.machineId&&(t.machineId=e.machineId),e.machineModel&&(t.machineModel=e.machineModel),e.status&&(t.status=e.status),e.shift&&(t.shift=e.shift),e.dateFrom&&(t.dateFrom=e.dateFrom),e.dateTo&&(t.dateTo=e.dateTo),e.page&&(t.page=e.page),e.size&&(t.size=e.size),t}buildReportFilters(e){const t={};return e.query&&(t.q=e.query),e.type&&(t.type=e.type),e.machineId&&(t.machineId=e.machineId),e.generatedBy&&(t.generatedBy=e.generatedBy),e.dateFrom&&(t.dateFrom=e.dateFrom),e.dateTo&&(t.dateTo=e.dateTo),e.page&&(t.page=e.page),e.size&&(t.size=e.size),t}formatSearchResults(e,t){return!e||!e.results?[]:e.results.map(r=>({id:r.id,type:r.type||t,score:r.score,title:this.extractTitle(r.data,r.type),description:this.extractDescription(r.data,r.type),timestamp:r.data.timestamp||r.data.generatedAt||r.data.date,highlight:r.highlight,data:r.data}))}extractTitle(e,t){switch(t){case"machine-session":return`${e.machineName} - Session ${e.sessionId}`;case"production-data":return`${e.machineName} - Production ${e.date}`;case"machine-stop":return`${e.machineName} - Arrêt ${e.stopCode}`;case"report":return e.title||`${e.type} Report`;default:return e.title||e.machineName||"Unknown"}}extractDescription(e,t){var r,i,d,u;switch(t){case"machine-session":return`Operator: ${e.operator||"Unknown"}, TRS: ${e.trs||0}%, Production: ${((r=e.production)==null?void 0:r.total)||0}`;case"production-data":return`OEE: ${((d=(i=e.performance)==null?void 0:i.oee)==null?void 0:d.toFixed(1))||0}%, Production: ${((u=e.production)==null?void 0:u.good)||0} pièces, Opérateur: ${e.operator||"N/A"}`;case"machine-stop":return`${e.stopDescription||e.stopCode}, Durée: ${e.duration||0} min, Catégorie: ${e.stopCategory||"N/A"}`;case"report":return e.description||`Generated by ${e.generatedBy||"Unknown"}`;default:return JSON.stringify(e).substring(0,100)+"..."}}createDebouncedSearch(e,t=300){let r;return(...i)=>(clearTimeout(r),new Promise((d,u)=>{r=setTimeout(async()=>{try{const h=await e(...i);d(h)}catch(h){u(h)}},t)}))}}const S=new ue,{Text:f,Title:we}=_,{Search:he}=H,Se=({visible:s,onClose:e,onResultSelect:t})=>{const[r,i]=l.useState(""),[d,u]=l.useState([]),[h,v]=l.useState(!1),[c,p]=l.useState(null),[y,m]=l.useState(null),b=l.useCallback(S.createDebouncedSearch(S.globalSearch.bind(S),300),[]);l.useEffect(()=>{r.trim().length>=2?R(r.trim()):(u([]),m(null),p(null))},[r]);const R=async n=>{v(!0),p(null);try{const o=await b(n,{size:20}),E=S.formatSearchResults(o,"global");u(E),m({total:o.total,query:n,timestamp:new Date})}catch(o){p(o.message||"Search failed"),u([]),m(null)}finally{v(!1)}},w=n=>{t&&t(n),e()},P=n=>{switch(n){case"production-data":return a.createElement(q,{style:{color:"#52c41a"}});case"machine-stop":return a.createElement(A,{style:{color:"#ff4d4f"}});case"machine-session":return a.createElement(q,{style:{color:"#1890ff"}});case"report":return a.createElement(X,{style:{color:"#722ed1"}});default:return a.createElement($,{style:{color:"#666"}})}},U=n=>{const E={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"},"maintenance-log":{color:"orange",text:"Maintenance"}}[n]||{color:"default",text:"Inconnu"};return a.createElement(L,{color:E.color},E.text)},F=n=>{if(!n)return null;const o=Object.keys(n);return o.length===0?null:a.createElement("div",{style:{marginTop:8}},o.map(E=>a.createElement("div",{key:E,style:{marginBottom:4}},a.createElement(f,{type:"secondary",style:{fontSize:"12px"}},a.createElement(se,null)," ",E,":"),a.createElement("div",{style:{fontSize:"12px",marginLeft:16},dangerouslySetInnerHTML:{__html:n[E].join(" ... ")}}))))},W=n=>a.createElement(k.Item,{key:n.id,onClick:()=>w(n),style:{cursor:"pointer",padding:"12px 16px",borderRadius:"6px",margin:"4px 0",transition:"all 0.2s",border:"1px solid transparent"},onMouseEnter:o=>{o.currentTarget.style.backgroundColor="#f5f5f5",o.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:o=>{o.currentTarget.style.backgroundColor="transparent",o.currentTarget.style.borderColor="transparent"}},a.createElement(k.Item.Meta,{avatar:P(n.type),title:a.createElement(M,null,a.createElement(f,{strong:!0},n.title),U(n.type),n.score&&a.createElement(Q,{title:`Relevance score: ${n.score.toFixed(2)}`},a.createElement(L,{color:"purple",style:{fontSize:"10px"}},Math.round(n.score*100),"%"))),description:a.createElement("div",null,a.createElement(f,{type:"secondary"},n.description),n.timestamp&&a.createElement("div",{style:{marginTop:4}},a.createElement(A,{style:{marginRight:4}}),a.createElement(f,{type:"secondary",style:{fontSize:"12px"}},K(n.timestamp).format("DD/MM/YYYY HH:mm"))),F(n.highlight))}),a.createElement("div",null,a.createElement(J,{type:"text",size:"small",icon:a.createElement(ae,null),onClick:o=>{o.stopPropagation(),w(n)}}))),B=()=>{i(""),u([]),m(null),p(null),e()};return a.createElement(j,{title:a.createElement(M,null,a.createElement($,null),a.createElement("span",null,"Global Search")),open:s,onCancel:B,footer:null,width:800,style:{top:50},destroyOnClose:!0},a.createElement("div",{style:{marginBottom:16}},a.createElement(he,{placeholder:"Rechercher dans les données de production, arrêts, sessions et rapports...",value:r,onChange:n=>i(n.target.value),size:"large",allowClear:!0,autoFocus:!0})),y&&a.createElement("div",{style:{marginBottom:16}},a.createElement(f,{type:"secondary",style:{fontSize:"12px"}},"Found ",y.total,' results for "',y.query,'" (',((Date.now()-y.timestamp.getTime())/1e3).toFixed(2),"s)")),c&&a.createElement(Y,{message:"Search Error",description:c,type:"error",showIcon:!0,style:{marginBottom:16}}),a.createElement("div",{style:{maxHeight:"60vh",overflowY:"auto"}},h?a.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},a.createElement(G,{size:"large"}),a.createElement("div",{style:{marginTop:16}},a.createElement(f,{type:"secondary"},"Searching..."))):d.length>0?a.createElement(k,{dataSource:d,renderItem:W,split:!1}):r.trim().length>=2?a.createElement(z,{description:"No results found",image:z.PRESENTED_IMAGE_SIMPLE}):a.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},a.createElement($,{style:{fontSize:"48px",color:"#d9d9d9"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(f,{type:"secondary"},"Type at least 2 characters to start searching")),a.createElement("div",{style:{marginTop:8}},a.createElement(f,{type:"secondary",style:{fontSize:"12px"}},"Search across machine sessions, reports, and maintenance logs")))),d.length>0&&a.createElement(a.Fragment,null,a.createElement(V,null),a.createElement("div",{style:{textAlign:"center"}},a.createElement(f,{type:"secondary",style:{fontSize:"12px"}},"Click on any result to view details"))))};export{Se as G,be as R,ae as a,se as b,ve as i,S as s};
