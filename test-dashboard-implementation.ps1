# Dashboard Data System Testing Script
# 
# Tests the enhanced dashboard data system and validates the implementation

Write-Host "🧪 Dashboard Data System Testing" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Change to backend directory
Set-Location backend

Write-Host "`n📋 Available Tests:" -ForegroundColor Yellow
Write-Host "1. All Tests (comprehensive)" -ForegroundColor White
Write-Host "2. Indexing Only" -ForegroundColor White  
Write-Host "3. Elasticsearch Queries Only" -ForegroundColor White
Write-Host "4. GraphQL Resolvers Only" -ForegroundColor White
Write-Host "5. Data Accuracy Only" -ForegroundColor White
Write-Host "6. Performance Only" -ForegroundColor White
Write-Host "7. Quick Validation" -ForegroundColor White

$choice = Read-Host "`nSelect test to run (1-7)"

switch ($choice) {
    "1" { 
        Write-Host "`n🚀 Running all tests..." -ForegroundColor Green
        node test-dashboard-system.js all
    }
    "2" { 
        Write-Host "`n📥 Testing indexing..." -ForegroundColor Green
        node test-dashboard-system.js indexing
    }
    "3" { 
        Write-Host "`n🔍 Testing Elasticsearch queries..." -ForegroundColor Green
        node test-dashboard-system.js elasticsearch
    }
    "4" { 
        Write-Host "`n🔗 Testing GraphQL resolvers..." -ForegroundColor Green
        node test-dashboard-system.js graphql
    }
    "5" { 
        Write-Host "`n🎯 Testing data accuracy..." -ForegroundColor Green
        node test-dashboard-system.js accuracy
    }
    "6" { 
        Write-Host "`n⚡ Testing performance..." -ForegroundColor Green
        node test-dashboard-system.js performance
    }
    "7" { 
        Write-Host "`n⚡ Quick validation..." -ForegroundColor Green
        
        # Quick health checks
        Write-Host "Checking backend server..." -ForegroundColor Yellow
        $backendHealth = try {
            $response = Invoke-RestMethod -Uri "http://localhost:3001/api/health" -TimeoutSec 5
            $response
        } catch {
            Write-Host "❌ Backend server not responding" -ForegroundColor Red
            $null
        }
        
        if ($backendHealth) {
            Write-Host "✅ Backend server is running" -ForegroundColor Green
            
            # Test GraphQL endpoint
            Write-Host "Testing GraphQL endpoint..." -ForegroundColor Yellow
            $graphqlTest = try {
                $body = @{
                    query = "query { getDataSourceStatus { primarySource } }"
                } | ConvertTo-Json
                
                $response = Invoke-RestMethod -Uri "http://localhost:3001/graphql" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10
                $response
            } catch {
                Write-Host "❌ GraphQL endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
                $null
            }
            
            if ($graphqlTest -and $graphqlTest.data) {
                Write-Host "✅ GraphQL endpoint working" -ForegroundColor Green
                Write-Host "📊 Primary data source: $($graphqlTest.data.getDataSourceStatus.primarySource)" -ForegroundColor Cyan
            }
            
            # Test enhanced dashboard queries
            Write-Host "Testing enhanced dashboard queries..." -ForegroundColor Yellow
            $dashboardTest = try {
                $body = @{
                    query = "query { getDashboardStats { totalStops dataSource } }"
                } | ConvertTo-Json
                
                $response = Invoke-RestMethod -Uri "http://localhost:3001/graphql" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10
                $response
            } catch {
                Write-Host "❌ Dashboard query failed: $($_.Exception.Message)" -ForegroundColor Red
                $null
            }
            
            if ($dashboardTest -and $dashboardTest.data) {
                Write-Host "✅ Enhanced dashboard queries working" -ForegroundColor Green
                Write-Host "📈 Total stops: $($dashboardTest.data.getDashboardStats.totalStops)" -ForegroundColor Cyan
                Write-Host "📊 Data source: $($dashboardTest.data.getDashboardStats.dataSource)" -ForegroundColor Cyan
            }
        }
        
        Write-Host "`n✅ Quick validation completed" -ForegroundColor Green
    }
    default { 
        Write-Host "Invalid choice. Running all tests..." -ForegroundColor Yellow
        node test-dashboard-system.js all
    }
}

Write-Host "`n🎯 Testing completed!" -ForegroundColor Cyan

# Ask if user wants to see additional info
$showInfo = Read-Host "`nShow implementation summary? (y/n)"

if ($showInfo -eq "y" -or $showInfo -eq "Y") {
    Write-Host "`n📋 Implementation Summary:" -ForegroundColor Cyan
    Write-Host "==========================" -ForegroundColor Cyan
    
    Write-Host "`n🔧 Backend Components:" -ForegroundColor Yellow
    Write-Host "  ✅ dashboardDataIndexer.js - Data indexing service" -ForegroundColor Green
    Write-Host "  ✅ elasticsearchDashboardService.js - ES query service" -ForegroundColor Green
    Write-Host "  ✅ unifiedDashboardResolvers.js - Unified GraphQL resolvers" -ForegroundColor Green
    Write-Host "  ✅ enhancedDashboardTypes.js - Enhanced GraphQL types" -ForegroundColor Green
    Write-Host "  ✅ dashboardInitializer.js - System initialization" -ForegroundColor Green
    
    Write-Host "`n🖥️ Frontend Components:" -ForegroundColor Yellow
    Write-Host "  ✅ enhancedGraphQLInterface.jsx - Enhanced GraphQL client" -ForegroundColor Green
    Write-Host "  ✅ useDataSourceStatus.js - Data source monitoring hook" -ForegroundColor Green
    Write-Host "  ✅ EnhancedArretQueuedContext.jsx - Enhanced context provider" -ForegroundColor Green
    
    Write-Host "`n🎯 Key Features:" -ForegroundColor Yellow
    Write-Host "  🚀 Elasticsearch as primary data source" -ForegroundColor Cyan
    Write-Host "  🔄 Automatic MySQL fallback" -ForegroundColor Cyan
    Write-Host "  📊 Real-time data source monitoring" -ForegroundColor Cyan
    Write-Host "  ⚡ Enhanced query performance" -ForegroundColor Cyan
    Write-Host "  🔍 Advanced aggregations and analytics" -ForegroundColor Cyan
    Write-Host "  🛡️ Error handling and retry mechanisms" -ForegroundColor Cyan
    
    Write-Host "`n🔗 Integration Points:" -ForegroundColor Yellow
    Write-Host "  📈 ArretsDashboard.jsx - Main dashboard component" -ForegroundColor Cyan
    Write-Host "  🔧 server.js - Backend initialization" -ForegroundColor Cyan
    Write-Host "  📊 GraphQL schema - Enhanced queries and mutations" -ForegroundColor Cyan
    
    Write-Host "`n📚 Next Steps:" -ForegroundColor Yellow
    Write-Host "  1. Update ArretsDashboard to use EnhancedArretQueuedContext" -ForegroundColor White
    Write-Host "  2. Add data source status indicator to UI" -ForegroundColor White
    Write-Host "  3. Test with real production data" -ForegroundColor White
    Write-Host "  4. Monitor performance and optimize as needed" -ForegroundColor White
}

Set-Location ..
