import React, { useState } from 'react';
import { Card, Button, Space, Typography, Row, Col, Alert } from 'antd';
import { ExpandOutlined, BugOutlined } from '@ant-design/icons';
import ExpandableChart from './ExpandableChart';
import { EnhancedQuantityBarChart, EnhancedTRSLineChart } from './EnhancedChartComponents';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

/**
 * Test page specifically for debugging modal chart issues
 */
const ModalTestPage = () => {
  const [testResults, setTestResults] = useState([]);

  // Generate test data
  const generateTestData = (size = 20) => {
    const data = [];
    for (let i = 0; i < size; i++) {
      const date = dayjs().subtract(i, 'days').format('YYYY-MM-DD');
      data.push({
        date,
        good: Math.floor(Math.random() * 1000) + 500,
        reject: Math.floor(Math.random() * 100) + 10,
        oee: Math.random() * 100,
        speed: Math.random() * 60 + 20,
        Machine_Name: 'TEST_MACHINE',
        Shift: 'Test',
      });
    }
    return data.reverse(); // Chronological order
  };

  const smallTestData = generateTestData(10);
  const largeTestData = generateTestData(100);

  const addTestResult = (test, result, details) => {
    setTestResults(prev => [...prev, {
      test,
      result,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const handleModalTest = (testName, data) => {
    console.log(`Testing modal with ${testName}:`, data);
    addTestResult(testName, 'Started', `Testing with ${data.length} data points`);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <BugOutlined style={{ marginRight: '8px', color: '#ff4d4f' }} />
        Modal Chart Debug Page
      </Title>
      <Text type="secondary">
        This page is specifically designed to test and debug modal chart expansion issues.
      </Text>

      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        {/* Test Results */}
        <Col span={24}>
          <Card title="Test Results" size="small">
            {testResults.length === 0 ? (
              <Text type="secondary">No tests run yet. Click on chart expand buttons to start testing.</Text>
            ) : (
              <Space direction="vertical" style={{ width: '100%' }}>
                {testResults.map((result, index) => (
                  <Alert
                    key={index}
                    message={`${result.test} - ${result.result}`}
                    description={`${result.details} (${result.timestamp})`}
                    type={result.result === 'Started' ? 'info' : result.result === 'Success' ? 'success' : 'error'}
                    size="small"
                  />
                ))}
              </Space>
            )}
          </Card>
        </Col>

        {/* Small Dataset Test */}
        <Col span={12}>
          <ExpandableChart
            title="Small Dataset Test (10 points)"
            data={smallTestData}
            chartType="bar"
            expandMode="modal"
            onExpand={() => handleModalTest('Small Dataset', smallTestData)}
            onCollapse={() => addTestResult('Small Dataset', 'Closed', 'Modal closed successfully')}
            exportEnabled={true}
            zoomEnabled={true}
          >
            <EnhancedQuantityBarChart
              data={smallTestData}
              title="Small Test Data"
              dataKey="good"
              color="#1890ff"
              tooltipLabel="Test Quantity"
            />
          </ExpandableChart>
        </Col>

        {/* Large Dataset Test */}
        <Col span={12}>
          <ExpandableChart
            title="Large Dataset Test (100 points)"
            data={largeTestData}
            chartType="bar"
            expandMode="modal"
            onExpand={() => handleModalTest('Large Dataset', largeTestData)}
            onCollapse={() => addTestResult('Large Dataset', 'Closed', 'Modal closed successfully')}
            exportEnabled={true}
            zoomEnabled={true}
          >
            <EnhancedQuantityBarChart
              data={largeTestData}
              title="Large Test Data"
              dataKey="good"
              color="#52c41a"
              tooltipLabel="Test Quantity"
            />
          </ExpandableChart>
        </Col>

        {/* Line Chart Test */}
        <Col span={12}>
          <ExpandableChart
            title="Line Chart Test (TRS)"
            data={smallTestData}
            chartType="line"
            expandMode="modal"
            onExpand={() => handleModalTest('Line Chart', smallTestData)}
            onCollapse={() => addTestResult('Line Chart', 'Closed', 'Modal closed successfully')}
            exportEnabled={true}
            zoomEnabled={true}
          >
            <EnhancedTRSLineChart
              data={smallTestData}
              color="#faad14"
            />
          </ExpandableChart>
        </Col>

        {/* Empty Data Test */}
        <Col span={12}>
          <ExpandableChart
            title="Empty Data Test"
            data={[]}
            chartType="bar"
            expandMode="modal"
            onExpand={() => handleModalTest('Empty Data', [])}
            onCollapse={() => addTestResult('Empty Data', 'Closed', 'Modal closed successfully')}
            exportEnabled={true}
            zoomEnabled={true}
          >
            <EnhancedQuantityBarChart
              data={[]}
              title="Empty Test Data"
              dataKey="good"
              color="#f5222d"
              tooltipLabel="Test Quantity"
            />
          </ExpandableChart>
        </Col>

        {/* Debug Information */}
        <Col span={24}>
          <Card title="Debug Information">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text><strong>Small Dataset Length:</strong> {smallTestData.length}</Text>
              <Text><strong>Large Dataset Length:</strong> {largeTestData.length}</Text>
              <Text><strong>Sample Data Point:</strong></Text>
              <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {JSON.stringify(smallTestData[0], null, 2)}
              </pre>
              <Text><strong>Expected Behavior:</strong></Text>
              <ul>
                <li>Charts should display data in normal view</li>
                <li>Clicking expand button should open modal with chart</li>
                <li>Modal should show the same data with better formatting</li>
                <li>Modal should have visible close button (X)</li>
                <li>ESC key should close modal</li>
                <li>Clicking outside modal should close it</li>
              </ul>
            </Space>
          </Card>
        </Col>

        {/* Manual Test Buttons */}
        <Col span={24}>
          <Card title="Manual Tests">
            <Space wrap>
              <Button 
                type="primary" 
                icon={<ExpandOutlined />}
                onClick={() => {
                  console.log('Manual test: Check console for modal debug logs');
                  addTestResult('Manual Test', 'Info', 'Check browser console for debug logs');
                }}
              >
                Check Console Logs
              </Button>
              <Button 
                onClick={() => {
                  setTestResults([]);
                }}
              >
                Clear Test Results
              </Button>
              <Button 
                type="dashed"
                onClick={() => {
                  addTestResult('Browser Test', 'Info', `User Agent: ${navigator.userAgent.substring(0, 50)}...`);
                  addTestResult('Screen Test', 'Info', `Screen: ${window.screen.width}x${window.screen.height}`);
                  addTestResult('Viewport Test', 'Info', `Viewport: ${window.innerWidth}x${window.innerHeight}`);
                }}
              >
                Browser Info
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ModalTestPage;
