/* Enhanced Responsive Chart Modal Styles */

/* Base modal styles - z-index managed by LayoutContext */
.unified-chart-modal-wrap {
  /* z-index set dynamically by LayoutContext */
  position: relative;
}

.unified-chart-modal {
  /* z-index set dynamically by LayoutContext */
  position: relative;
}

/* Ensure modal content is above backdrop */
.unified-chart-modal .ant-modal-wrap {
  z-index: inherit;
}

.unified-chart-modal .ant-modal-mask {
  z-index: -1;
  position: fixed;
}

/* Force proper stacking order with blur effect */
.unified-chart-modal-wrap .ant-modal-mask {
  z-index: 10000 !important;
  /* Fallback for browsers that don't support backdrop-filter */
  background-color: rgba(255, 255, 255, 0.2) !important;
  /* Modern blur effect - positioned behind modal content */
  backdrop-filter: blur(6px) !important;
  -webkit-backdrop-filter: blur(6px) !important;
  /* Ensure mask is behind modal content */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* Ensure modal content is above the blurred background */
.unified-chart-modal-wrap .ant-modal-wrap {
  z-index: 10001 !important;
  position: relative !important;
  /* Remove any backdrop-filter from modal wrap to prevent double blur */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(6px)) {
  .unified-chart-modal-wrap .ant-modal-mask {
    background-color: rgba(255, 255, 255, 0.3) !important;
  }
}

.unified-chart-modal-wrap .ant-modal-wrap {
  z-index: 10001 !important;
}

.unified-chart-modal-wrap .ant-modal {
  z-index: 10001 !important;
  /* Ensure modal content is crisp and not blurred */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  position: relative !important;
}

/* Fullscreen mode enhanced blur effect */
.unified-chart-modal.fullscreen .ant-modal-mask {
  /* Fallback for browsers that don't support backdrop-filter */
  background-color: rgba(255, 255, 255, 0.25) !important;
  /* Enhanced blur for fullscreen */
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  z-index: 10010 !important;
  /* Ensure fullscreen mask covers entire viewport */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* Fullscreen modal content positioning */
.unified-chart-modal.fullscreen .ant-modal-wrap {
  z-index: 10011 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.unified-chart-modal.fullscreen .ant-modal {
  z-index: 10011 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Fullscreen fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(8px)) {
  .unified-chart-modal.fullscreen .ant-modal-mask {
    background-color: rgba(255, 255, 255, 0.35) !important;
  }
}

.unified-chart-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10002;
  /* Ensure modal content is never blurred */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  /* Ensure crisp rendering */
  transform: translateZ(0);
  will-change: transform;
}

.unified-chart-modal .ant-modal-header {
  border-bottom: none;
  padding: 0;
  background: transparent;
  position: relative;
  z-index: 2;
}

/* Ensure header buttons are clickable and properly positioned */
.unified-chart-modal .ant-modal-header .ant-btn {
  position: relative;
  z-index: 10;
  pointer-events: auto;
  cursor: pointer;
}

/* Specific styling for close button */
.unified-chart-modal .ant-modal-header .ant-btn[aria-label*="Fermer"],
.unified-chart-modal .ant-modal-header .ant-btn:last-child {
  position: relative;
  z-index: 15;
  pointer-events: auto;
  cursor: pointer;
  background: rgba(255,255,255,0.2) !important;
  border: 1px solid rgba(255,255,255,0.3) !important;
  color: white !important;
}

.unified-chart-modal .ant-modal-header .ant-btn:hover {
  background: rgba(255,255,255,0.3) !important;
  transform: none;
}

.unified-chart-modal .ant-modal-body {
  padding: 0;
  background: #fafafa;
  position: relative;
  z-index: 1;
}

.unified-chart-modal .ant-modal-close {
  display: none; /* We use custom close button */
}

/* Mobile-specific styles */
.unified-chart-modal.mobile .ant-modal-content {
  border-radius: 0;
  height: 100vh;
  width: 100vw;
  margin: 0;
  box-shadow: none;
}

.unified-chart-modal.mobile .ant-modal-body {
  height: calc(100vh - 48px);
  padding: 0;
}

/* Tablet-specific styles */
.unified-chart-modal.tablet .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
}

/* Fullscreen mode */
.unified-chart-modal.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 10010 !important; /* Higher than sidebar and all other elements */
}

.unified-chart-modal.fullscreen .ant-modal-content {
  border-radius: 0;
  height: 100vh;
  width: 100vw;
  margin: 0;
  box-shadow: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10011; /* Higher than fullscreen modal base */
}

.unified-chart-modal.fullscreen .ant-modal-body {
  height: calc(100vh - 60px);
}

/* Ensure fullscreen modal is above everything including sidebar */
.unified-chart-modal.fullscreen .ant-modal-wrap {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 10011 !important; /* Higher than sidebar and all other elements */
}

/* Animation transitions */
.unified-chart-modal-enter {
  opacity: 0;
  transform: scale(0.95) translateY(20px);
}

.unified-chart-modal-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unified-chart-modal-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.unified-chart-modal-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mask animations */
.unified-chart-modal-mask-enter {
  opacity: 0;
}

.unified-chart-modal-mask-enter-active {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.unified-chart-modal-mask-exit {
  opacity: 1;
}

.unified-chart-modal-mask-exit-active {
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Chart container enhancements */
.unified-chart-modal .chart-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.unified-chart-modal.mobile .chart-container {
  border-radius: 0;
}

.unified-chart-modal.fullscreen .chart-container {
  border-radius: 0;
}

/* Touch-friendly controls for mobile */
@media (max-width: 768px) {
  .unified-chart-modal .ant-btn {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }
  
  .unified-chart-modal .ant-btn-sm {
    min-height: 36px;
    min-width: 36px;
  }
}

/* Responsive breakpoints */
@media (max-width: 480px) {
  .unified-chart-modal .ant-modal-content {
    margin: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .unified-chart-modal .ant-modal-body {
    padding: 4px;
    height: calc(100vh - 44px);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .unified-chart-modal .ant-modal-content {
    margin: 8px;
    height: calc(100vh - 16px);
    border-radius: 8px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .unified-chart-modal .ant-modal-content {
    margin: 16px;
    height: calc(100vh - 32px);
    border-radius: 12px;
  }
}

@media (min-width: 1025px) {
  .unified-chart-modal .ant-modal-content {
    margin: 24px;
    height: calc(100vh - 48px);
    border-radius: 16px;
  }
}

/* Loading state improvements */
.unified-chart-modal .ant-spin-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-chart-modal .ant-spin-lg .ant-spin-dot {
  font-size: 32px;
}

/* Enhanced hover effects for desktop */
@media (hover: hover) {
  .unified-chart-modal .ant-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .unified-chart-modal .ant-modal-content {
    border: 2px solid #000;
  }
  
  .unified-chart-modal .ant-btn {
    border-width: 2px;
    font-weight: bold;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .unified-chart-modal-enter,
  .unified-chart-modal-enter-active,
  .unified-chart-modal-exit,
  .unified-chart-modal-exit-active,
  .unified-chart-modal-mask-enter,
  .unified-chart-modal-mask-enter-active,
  .unified-chart-modal-mask-exit,
  .unified-chart-modal-mask-exit-active {
    transition: none;
    animation: none;
  }
  
  .unified-chart-modal .chart-container,
  .unified-chart-modal .ant-btn {
    transition: none;
  }
}

/* Focus management for accessibility */
.unified-chart-modal .ant-btn:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.unified-chart-modal .ant-btn:focus:not(:focus-visible) {
  outline: none;
}

/* Print styles */
@media print {
  .unified-chart-modal {
    display: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .unified-chart-modal .ant-modal-body {
    background: #1f1f1f;
  }
  
  .unified-chart-modal .chart-container {
    background: #2d2d2d;
    color: #ffffff;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .unified-chart-modal .ant-modal-body {
    height: calc(100vh - 40px);
  }
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .unified-chart-modal.mobile .ant-modal-content {
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
  }
}
