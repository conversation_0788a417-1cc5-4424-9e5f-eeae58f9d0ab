import React, { memo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip as Recharts<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  AreaChart,
  Area,
  ComposedChart,
} from "recharts";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import { Empty } from "antd";
import { normalizePercentage } from "../../utils/dataUtils";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// Configure dayjs to use French locale
dayjs.locale("fr");

// SOMIPEM Brand Color Palette (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

// Helper function to render charts with empty state handling
export const renderChart = (data, ChartComponent, props = {}) => {
  if (!data || data.length === 0) {
    return (
      <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée disponible" />
      </div>
    );
  }
  
  return <ChartComponent data={data} {...props} />;
};

// Bar chart with OEE line
export const MemoizedBarChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={400}>
    <ComposedChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
      <XAxis
        dataKey="date"
        tick={{ fill: "#666" }}
        tickFormatter={(date) => dayjs(date).format("DD/MM")}
        label={{
          value: "Date",
          position: "bottom",
          offset: 0,
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="left"
        tickFormatter={(value) => value.toLocaleString()}
        label={{
          value: "Quantité",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        tickFormatter={(value) => `${value}%`}
        domain={[0, 100]}
        label={{
          value: "TRS",
          angle: 90,
          position: "insideRight",
          style: { fill: "#666" },
        }}
      />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Check if value is a valid number
          const isValidNumber = typeof value === "number" && !isNaN(value)
          const formattedValue = isValidNumber
            ? Number.isInteger(value)
              ? value.toLocaleString()
              : value.toFixed(2)
            : value

          const labels = {
            good: "Quantité bonne",
            reject: "Quantité rejetée (kg)",
            oee: "TRS",
            speed: "Cycle De Temps",
          }

          return [formattedValue, labels[name] || name]
        }}
        labelFormatter={(label) => `Date: ${dayjs(label).format("DD/MM/YYYY")}`}
      />
      <Legend
        wrapperStyle={{ paddingTop: 20 }}
        formatter={(value) => {
          const labels = {
            good: "Quantité bonne",
            reject: "Quantité rejetée (kg)",
            oee: "TRS",
            speed: "Cycle De Temps",
          }
          return <span style={{ color: "#666" }}>{labels[value] || value}</span>
        }}
      />
      <Bar yAxisId="left" dataKey="good" name="good" fill={COLORS[2]} maxBarSize={40} stackId="production" />
      <Bar yAxisId="left" dataKey="reject" name="reject" fill={COLORS[4]} maxBarSize={40} stackId="production" />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="oee"
        name="oee"
        stroke={COLORS[0]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[0] }}
        activeDot={{ r: 6, fill: "#fff", stroke: COLORS[0], strokeWidth: 2 }}
      />
    </ComposedChart>
  </ResponsiveContainer>
));

// Shift comparison chart
export const MemoizedShiftComparisonChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <BarChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
      <YAxis
        yAxisId="left"
        tickFormatter={(value) => value.toLocaleString()}
        label={{
          value: "Production",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        tickFormatter={(value) => `${value}%`}
        domain={[0, 100]}
        label={{
          value: "Performance",
          angle: 90,
          position: "insideRight",
          style: { fill: "#666" },
        }}
      />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Check if value is a valid number
          const isValidNumber = typeof value === "number" && !isNaN(value)

          if (name === "production") return [isValidNumber ? value.toLocaleString() : value, "Production"]
          if (name === "downtime") return [isValidNumber ? value.toLocaleString() : value, "Temps d'arrêt"]
          if (name === "oee") return [isValidNumber ? `${value.toFixed(1)}%` : `${value}%`, "TRS"]
          if (name === "performance") return [isValidNumber ? `${value.toFixed(1)}%` : `${value}%`, "Performance"]
          return [value, name]
        }}
        labelFormatter={(label) => `Équipe: ${label}`}
      />

      <Bar yAxisId="left" dataKey="production" name="Production" fill={COLORS[2]} maxBarSize={40} />
      <Bar yAxisId="left" dataKey="downtime" name="Temps d'arrêt" fill={COLORS[4]} maxBarSize={40} />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="oee"
        name="TRS"
        stroke={COLORS[0]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[0] }}
      />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="performance"
        name="Performance"
        stroke={COLORS[5]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[5] }}
      />
    </BarChart>
  </ResponsiveContainer>
));

// Machine performance chart
export const MemoizedMachinePerformance = memo(({ data }) => {
  // Aggregate data by machine name
  const aggregatedData = data.reduce((acc, item) => {
    const machineName = item.Machine_Name
    if (!acc[machineName]) {
      acc[machineName] = {
        Machine_Name: machineName,
        good: 0,
        reject: 0,
      }
    }

    acc[machineName].good += Number(item.good) || Number(item.Good_QTY_Day) || 0
    acc[machineName].reject += Number(item.reject) || Number(item.Rejects_QTY_Day) || 0

    return acc
  }, {})

  // Convert to array for the chart
  const chartData = Object.values(aggregatedData)

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="Machine_Name" tick={{ fill: "#666" }} interval={0} angle={-45} textAnchor="end" height={80} />
        <YAxis
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Quantité",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value, name) => {
            // Check if value is a number before using toFixed
            const isNumber = typeof value === "number" && !isNaN(value)
            const formattedValue = isNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value

            const labels = {
              good: "Production",
              reject: "Rejets (kg)",
            }

            return [formattedValue, labels[name] || name]
          }}
        />

        <Bar dataKey="good" name="good" fill={COLORS[0]} stackId="a" />
        <Bar dataKey="reject" name="reject" fill={COLORS[4]} stackId="a" />
      </BarChart>
    </ResponsiveContainer>
  )
});

// Pie chart
export const MemoizedPieChart = memo(({ data, dataKey = "value", nameKey = "name", colors = COLORS }) => (
  <ResponsiveContainer width="100%" height={300}>
    <PieChart margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <Pie
        data={data}
        dataKey={dataKey}
        nameKey={nameKey}
        cx="50%"
        cy="50%"
        innerRadius={60}
        outerRadius={80}
        paddingAngle={5}
        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
        ))}
      </Pie>
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      />
      <Legend
        layout="vertical"
        verticalAlign="middle"
        align="right"
        wrapperStyle={{
          paddingLeft: 24,
          fontSize: 14,
          color: "#666",
        }}
      />
    </PieChart>
  </ResponsiveContainer>
));

// Line chart
export const MemoizedLineChart = memo(({ data, dataKeys = ["oee", "speed"], colors = [COLORS[0], COLORS[1]] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="date" tick={{ fill: "#666" }} tickFormatter={(date) => dayjs(date).format("DD/MM")} />
      <YAxis tickFormatter={(value) => `${value}%`} domain={[0, 100]} />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Check if value is a valid number
          const isValidNumber = typeof value === "number" && !isNaN(value)
          const formattedValue = isValidNumber
            ? Number.isInteger(value)
              ? value.toLocaleString()
              : value.toFixed(2)
            : value

          const labels = {
            oee: "TRS",
            speed: "Cycle De Temps",
          }

          return [isValidNumber ? formattedValue + "%" : value, labels[name] || name]
        }}
        labelFormatter={(label) => `Date: ${dayjs(label).format("DD/MM/YYYY")}`}
      />

      {dataKeys.map((key, index) => (
        <Line
          key={key}
          type="monotone"
          dataKey={key}
          name={key}
          stroke={colors[index]}
          strokeWidth={2}
          dot={{ r: 4, fill: colors[index] }}
          activeDot={{ r: 6, fill: "#fff", stroke: colors[index], strokeWidth: 2 }}
        />
      ))}
    </LineChart>
  </ResponsiveContainer>
));

// Area chart
export const MemoizedAreaChart = memo(({ data, dataKey = "average_speed", color = COLORS[2] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <AreaChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis
        dataKey="hour"
        tick={{ fill: "#666" }}
        tickFormatter={(hour) => {
          if (!hour) return ""
          const parts = hour.split(" ")
          if (parts.length >= 2) {
            return `${parts[1]}h`
          }
          return hour
        }}
      />
      <YAxis tickFormatter={(value) => `${value} u/h`} />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value) => {
          // Check if value is a valid number
          const isValidNumber = typeof value === "number" && !isNaN(value)
          return [
            isValidNumber ? `${value.toFixed(2)} unités/heure` : `${value} unités/heure`,
            "Cycle De Temps Moyenne",
          ]
        }}
        labelFormatter={(hour) => {
          if (!hour) return "Heure inconnue"
          const parts = hour.split(" ")
          if (parts.length >= 2) {
            return `Date: ${parts[0]}, Heure: ${parts[1]}h`
          }
          return hour
        }}
      />

      <Area
        type="monotone"
        dataKey={dataKey}
        name="Cycle De Temps Moyenne"
        stroke={color}
        fill={color}
        fillOpacity={0.3}
      />
    </AreaChart>
  </ResponsiveContainer>
));

// Bullet chart for OEE by machine
export const MemoizedBulletChart = memo(({ data, dataKey = "oee", nameKey = "Machine_Name", color = COLORS[5] }) => {
  // Process data to get unique machines with their average OEE
  const processedData = data.reduce((acc, item) => {
    if (!acc[item.Machine_Name]) {
      acc[item.Machine_Name] = {
        Machine_Name: item.Machine_Name,
        oee: 0,
        count: 0,
      }
    }
    // Handle decimal percentages (0-1 range)
    let oeeValue = Number.parseFloat(item.oee) || 0;
    // Convert to percentage if in 0-1 range
    oeeValue = normalizePercentage(oeeValue);
    acc[item.Machine_Name].oee += oeeValue;
    acc[item.Machine_Name].count += 1
    return acc
  }, {})

  // Calculate averages and convert to array
  const chartData = Object.values(processedData)
    .map((item) => ({
      Machine_Name: item.Machine_Name,
      oee: item.count > 0 ? Math.round(item.oee / item.count) : 0,
      target: 85, // Target OEE value
      minimum: 70, // Minimum acceptable threshold
    }))
    .sort((a, b) => b.oee - a.oee) // Sort by OEE descending

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart layout="vertical" data={chartData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" horizontal={false} />
        <XAxis type="number" domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
        <YAxis dataKey={nameKey} type="category" width={120} tick={{ fontSize: 12 }} />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value, name) => {
            // Check if value is a valid number
            let displayValue = value;
            const isValidNumber = typeof value === "number" && !isNaN(value);

            if (name === "oee") return [isValidNumber ? `${displayValue.toFixed(2)}%` : `${value}%`, "TRS Actuel"]
            if (name === "target") return [`${value}%`, "Objectif"]
            if (name === "minimum") return [`${value}%`, "Minimum"]
            return [value, name]
          }}
        />
        <Legend
          formatter={(value) => {
            const labels = {
              oee: "TRS Actuel",
              target: "Objectif",
              minimum: "Minimum",
            }

            if (value === "oee") {
              return (
                <span>
                  <span>TRS Actuel</span>
                </span>
              )
            }

            return labels[value] || value
          }}
        />
        <Bar dataKey={dataKey} name="oee" fill={COLORS[0]} radius={[0, 4, 4, 0]} barSize={20} />
      </BarChart>
    </ResponsiveContainer>
  )
});

// Set display names for all components
MemoizedBarChart.displayName = 'MemoizedBarChart';
MemoizedShiftComparisonChart.displayName = 'MemoizedShiftComparisonChart';
MemoizedMachinePerformance.displayName = 'MemoizedMachinePerformance';
MemoizedPieChart.displayName = 'MemoizedPieChart';
MemoizedLineChart.displayName = 'MemoizedLineChart';
MemoizedAreaChart.displayName = 'MemoizedAreaChart';
MemoizedBulletChart.displayName = 'MemoizedBulletChart';
