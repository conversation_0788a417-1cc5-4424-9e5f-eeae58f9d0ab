import React, { useState } from 'react';
import {
  Card,
  List,
  Typography,
  Tag,
  Space,
  Button,
  Tooltip,
  Empty,
  Pagination,
  Statistic,
  Row,
  Col,
  Divider,
  <PERSON><PERSON>,
  Badge
} from 'antd';
import {
  SearchOutlined,
  SettingOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  HighlightOutlined,
  ExportOutlined,
  EyeOutlined,
  Bar<PERSON><PERSON>Outlined,
  StopOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text, Title, Paragraph } = Typography;

const SearchResultsDisplay = ({ 
  results, 
  searchQuery, 
  pageType, 
  loading = false,
  onResultSelect,
  onPageChange,
  currentPage = 1,
  pageSize = 20
}) => {
  const [selectedResults, setSelectedResults] = useState([]);

  if (!results) {
    return null;
  }

  const getResultIcon = (type) => {
    switch (type) {
      case 'production-data':
        return <BarChartOutlined style={{ color: '#52c41a' }} />;
      case 'machine-stop':
        return <StopOutlined style={{ color: '#ff4d4f' }} />;
      case 'machine-session':
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'report':
        return <FileTextOutlined style={{ color: '#722ed1' }} />;
      default:
        return <SearchOutlined style={{ color: '#666' }} />;
    }
  };

  const getResultTypeTag = (type) => {
    const typeConfig = {
      'production-data': { color: 'green', text: 'Production' },
      'machine-stop': { color: 'red', text: 'Arrêt' },
      'machine-session': { color: 'blue', text: 'Session' },
      'report': { color: 'purple', text: 'Rapport' }
    };

    const config = typeConfig[type] || { color: 'default', text: 'Inconnu' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const formatResultTitle = (result) => {
    const { data, type } = result;
    
    switch (type) {
      case 'production-data':
        return `${data.machineName} - ${dayjs(data.date).format('DD/MM/YYYY')}`;
      case 'machine-stop':
        return `Arrêt ${data.machineName} - ${data.stopCode}`;
      case 'machine-session':
        return `Session ${data.machineName} - ${data.sessionId}`;
      case 'report':
        return data.title || `Rapport ${data.type}`;
      default:
        return 'Résultat de recherche';
    }
  };

  const formatResultDescription = (result) => {
    const { data, type } = result;
    
    switch (type) {
      case 'production-data':
        return `OEE: ${data.performance?.oee?.toFixed(1) || 0}% | Production: ${data.production?.good || 0} pièces | Opérateur: ${data.operator || 'N/A'}`;
      case 'machine-stop':
        return `${data.stopDescription} | Durée: ${data.duration || 0} min | Catégorie: ${data.stopCategory || 'N/A'}`;
      case 'machine-session':
        return `TRS: ${data.performance?.trs?.toFixed(1) || 0}% | Production: ${data.production?.total || 0} | Opérateur: ${data.operator || 'N/A'}`;
      case 'report':
        return data.description || `Généré par ${data.generatedBy || 'N/A'}`;
      default:
        return 'Aucune description disponible';
    }
  };

  const renderHighlight = (highlight) => {
    if (!highlight) return null;

    const highlightFields = Object.keys(highlight);
    if (highlightFields.length === 0) return null;

    return (
      <div style={{ marginTop: 8, padding: '8px', backgroundColor: '#f6ffed', borderRadius: '4px' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          <HighlightOutlined /> Correspondances trouvées:
        </Text>
        {highlightFields.map(field => (
          <div key={field} style={{ marginTop: 4 }}>
            <Text strong style={{ fontSize: '12px', color: '#52c41a' }}>
              {field}:
            </Text>
            <div
              style={{ fontSize: '12px', marginLeft: 8 }}
              dangerouslySetInnerHTML={{
                __html: highlight[field].join(' ... ')
              }}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderResultMetrics = (result) => {
    const { data, type } = result;
    
    if (type === 'production-data') {
      return (
        <Row gutter={16} style={{ marginTop: 8 }}>
          <Col span={6}>
            <Statistic
              title="OEE"
              value={data.performance?.oee || 0}
              suffix="%"
              precision={1}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Production"
              value={data.production?.good || 0}
              suffix="pcs"
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Qualité"
              value={data.performance?.quality || 0}
              suffix="%"
              precision={1}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="TRS"
              value={data.performance?.trs || 0}
              suffix="%"
              precision={1}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
        </Row>
      );
    }
    
    if (type === 'machine-stop') {
      return (
        <Row gutter={16} style={{ marginTop: 8 }}>
          <Col span={8}>
            <Statistic
              title="Durée"
              value={data.duration || 0}
              suffix="min"
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={8}>
            <Tag color={data.severity === 'high' ? 'red' : data.severity === 'medium' ? 'orange' : 'green'}>
              {data.severity || 'low'}
            </Tag>
          </Col>
          <Col span={8}>
            <Badge 
              status={data.resolution?.resolved ? 'success' : 'error'} 
              text={data.resolution?.resolved ? 'Résolu' : 'En cours'}
            />
          </Col>
        </Row>
      );
    }
    
    return null;
  };

  const handleResultClick = (result) => {
    if (onResultSelect) {
      onResultSelect(result);
    }
  };

  const handleSelectResult = (result, selected) => {
    if (selected) {
      setSelectedResults([...selectedResults, result.id]);
    } else {
      setSelectedResults(selectedResults.filter(id => id !== result.id));
    }
  };

  const renderSearchResult = (result) => (
    <List.Item
      key={result.id}
      style={{
        padding: '16px',
        borderRadius: '8px',
        margin: '8px 0',
        border: '1px solid #f0f0f0',
        backgroundColor: '#fafafa',
        transition: 'all 0.2s'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = '#d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#fafafa';
        e.currentTarget.style.borderColor = '#f0f0f0';
      }}
      actions={[
        <Tooltip title="Voir les détails">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleResultClick(result)}
          />
        </Tooltip>,
        <Tooltip title="Exporter">
          <Button
            type="text"
            icon={<ExportOutlined />}
            onClick={() => handleResultClick(result)}
          />
        </Tooltip>
      ]}
    >
      <List.Item.Meta
        avatar={getResultIcon(result.type)}
        title={
          <Space>
            <Text strong style={{ cursor: 'pointer' }} onClick={() => handleResultClick(result)}>
              {formatResultTitle(result)}
            </Text>
            {getResultTypeTag(result.type)}
            {result.score && (
              <Tooltip title={`Score de pertinence: ${result.score.toFixed(3)}`}>
                <Tag color="purple" style={{ fontSize: '10px' }}>
                  {Math.round(result.score * 100)}%
                </Tag>
              </Tooltip>
            )}
          </Space>
        }
        description={
          <div>
            <Paragraph style={{ marginBottom: 8 }}>
              {formatResultDescription(result)}
            </Paragraph>
            
            {result.data.timestamp && (
              <div style={{ marginBottom: 8 }}>
                <ClockCircleOutlined style={{ marginRight: 4 }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {dayjs(result.data.timestamp || result.data.date).format('DD/MM/YYYY HH:mm')}
                </Text>
              </div>
            )}
            
            {renderResultMetrics(result)}
            {renderHighlight(result.highlight)}
          </div>
        }
      />
    </List.Item>
  );

  return (
    <Card
      title={
        <Space>
          <SearchOutlined />
          <span>Résultats de recherche pour "{searchQuery}"</span>
          <Tag color="blue">{results.total} résultat(s)</Tag>
        </Space>
      }
      extra={
        <Space>
          <Button size="small" type="link">
            Exporter tous
          </Button>
        </Space>
      }
    >
      {results.total === 0 ? (
        <Empty
          description={`Aucun résultat trouvé pour "${searchQuery}"`}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <>
          <Alert
            message={`${results.total} résultat(s) trouvé(s) dans les ${pageType === 'production' ? 'données de production' : 'arrêts de machine'}`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <List
            dataSource={results[pageType === 'production' ? 'production' : 'stops'] || results.results || []}
            renderItem={renderSearchResult}
            loading={loading}
            split={false}
          />
          
          {results.totalPages > 1 && (
            <>
              <Divider />
              <div style={{ textAlign: 'center' }}>
                <Pagination
                  current={currentPage}
                  total={results.total}
                  pageSize={pageSize}
                  onChange={onPageChange}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `${range[0]}-${range[1]} sur ${total} résultats`
                  }
                />
              </div>
            </>
          )}
        </>
      )}
    </Card>
  );
};

export default SearchResultsDisplay;
