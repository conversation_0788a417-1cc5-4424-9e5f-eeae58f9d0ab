/**
 * Utility modules index
 * @module utils
 */

// API utilities
export * from './apiConfig';
export * from './apiUtils';

// Format utilities
export * from './formatUtils';

// Storage utilities
export * from './storageUtils';

// Validation utilities
export * from './validationUtils';

// Route utilities
export * from './routeUtils';

// Error utilities
export * from './errorUtils';

// Default export with all utilities
import apiConfig from './apiConfig';
import * as apiUtils from './apiUtils';
import * as formatUtils from './formatUtils';
import * as storageUtils from './storageUtils';
import * as validationUtils from './validationUtils';
import * as routeUtils from './routeUtils';
import * as errorUtils from './errorUtils';

// Import existing modules if available
let chartConfig = {};
let websocketService = {};
let dataUtils = {};

try {
  // Try to import existing modules
  chartConfig = require('./chartConfig');
} catch (e) {
  console.warn('chartConfig module not available');
}

try {
  websocketService = require('./websocketService');
} catch (e) {
  console.warn('websocketService module not available');
}

try {
  dataUtils = require('./dataUtils');
} catch (e) {
  console.warn('dataUtils module not available');
}

export default {
  api: {
    ...apiConfig,
    ...apiUtils,
  },
  format: formatUtils,
  storage: storageUtils,
  validation: validationUtils,
  route: routeUtils,
  error: errorUtils,
  chart: chartConfig,
  websocket: websocketService,
  data: dataUtils,
};