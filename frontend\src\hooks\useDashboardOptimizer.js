import React from "react" ;
import { useState, useEffect, useCallback } from 'react';
import { useAggregatedData } from './useAggregatedData';
import { cachedRequest } from '../utils/requestCache';

export const useDashboardOptimizer = () => {
  const [activeOptimizations, setActiveOptimizations] = useState({
    aggregation: true,
    caching: true,
    progressiveLoading: true,
    dataLimiting: true
  });

  const toggleOptimization = useCallback((optimization) => {
    setActiveOptimizations(prev => ({
      ...prev,
      [optimization]: !prev[optimization]
    }));
  }, []);

  const getOptimizedRequest = useCallback((url, options = {}) => {
    const optimizedOptions = {
      ...options,
      cache: activeOptimizations.caching ? (options.cache !== false) : false,
      cacheTTL: options.cacheTTL || 60000
    };

    if (activeOptimizations.dataLimiting && !options.limit) {
      optimizedOptions.params = {
        ...optimizedOptions.params,
        limit: 100 // Default limit for large datasets
      };
    }

    return cachedRequest(url, optimizedOptions);
  }, [activeOptimizations]);

  return {
    activeOptimizations,
    toggleOptimization,
    getOptimizedRequest
  };
};

export const useMultiDashboardAggregation = (dashboardConfigs = []) => {
  const { data, loading, error, fetchAggregatedData } = useAggregatedData();
  const [dashboardData, setDashboardData] = useState({});

  useEffect(() => {
    if (dashboardConfigs.length === 0) return;

    const allRequests = dashboardConfigs.flatMap(config => 
      config.requests.map(req => ({
        ...req,
        key: `${config.dashboard}_${req.key}`
      }))
    );

    fetchAggregatedData(allRequests);
  }, [dashboardConfigs, fetchAggregatedData]);

  useEffect(() => {
    if (!data || Object.keys(data).length === 0) return;

    const organizedData = {};
    
    dashboardConfigs.forEach(config => {
      organizedData[config.dashboard] = {};
      
      config.requests.forEach(req => {
        const key = `${config.dashboard}_${req.key}`;
        organizedData[config.dashboard][req.key] = data[key]?.data;
      });
    });

    setDashboardData(organizedData);
  }, [data, dashboardConfigs]);

  return {
    dashboardData,
    loading,
    error,
    refreshData: () => {
      const allRequests = dashboardConfigs.flatMap(config => 
        config.requests.map(req => ({
          ...req,
          key: `${config.dashboard}_${req.key}`
        }))
      );
      fetchAggregatedData(allRequests);
    }
  };
};

export const useDataSampling = (data = [], maxPoints = 200) => {
  const [sampledData, setSampledData] = useState([]);
  const [samplingRatio, setSamplingRatio] = useState(1);

  useEffect(() => {
    if (!Array.isArray(data) || data.length === 0) {
      setSampledData([]);
      setSamplingRatio(1);
      return;
    }

    if (data.length <= maxPoints) {
      setSampledData(data);
      setSamplingRatio(1);
      return;
    }

    const step = Math.ceil(data.length / maxPoints);
    const sampled = data.filter((_, index) => index % step === 0);
    
    setSampledData(sampled);
    setSamplingRatio(sampled.length / data.length);
    
    console.log(`📊 Data sampling: ${data.length} → ${sampled.length} points (ratio: ${samplingRatio.toFixed(2)})`);
  }, [data, maxPoints]);

  return {
    sampledData,
    samplingRatio,
    originalLength: data.length,
    issampled: samplingRatio < 1
  };
};
