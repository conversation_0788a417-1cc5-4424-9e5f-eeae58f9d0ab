import React from "react";
import { Result, <PERSON><PERSON>, <PERSON>po<PERSON>, Space } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import { HomeOutlined, ArrowLeftOutlined, LockOutlined } from "@ant-design/icons";
import { useTheme } from "../theme-context";
import { useAuth } from "../hooks/useAuth";

const { Text, Title, Paragraph } = Typography;

/**
 * Component for displaying unauthorized access error page
 * 
 * @param {Object} props - Component props
 * @param {string} [props.title="Accès refusé"] - Error title
 * @param {string} [props.subTitle] - Error subtitle
 * @param {string} [props.status="403"] - Error status code
 * @returns {React.ReactNode} Error page component
 */
const UnauthorizedPage = ({ 
  title = "Accès refusé", 
  subTitle = "Vous n'avez pas les permissions nécessaires pour accéder à cette page.",
  status = "403" 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { darkMode } = useTheme();
  const { user } = useAuth();
  
  // Get the path the user was trying to access
  const from = location.state?.from?.pathname || "/";
  
  // Get user role for more specific error message
  const userRole = user?.role || "utilisateur";
  
  return (
    <div
      style={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: "20px",
        backgroundColor: darkMode ? "#141414" : "#f0f2f5",
      }}
    >
      <Result
        status={status}
        icon={<LockOutlined style={{ fontSize: 72, color: "#ff4d4f" }} />}
        title={<Title level={1}>{title}</Title>}
        subTitle={
          <div>
            <Text style={{ fontSize: "18px", color: darkMode ? "#d9d9d9" : "#595959" }}>
              {subTitle}
            </Text>
            <Paragraph style={{ marginTop: 16 }}>
              <Text type="secondary">
                Vous êtes connecté en tant que <Text strong>{userRole}</Text> et vous avez tenté d'accéder à <Text code>{from}</Text>
              </Text>
            </Paragraph>
            <Paragraph>
              <Text type="secondary">
                Si vous pensez que c'est une erreur, veuillez contacter votre administrateur système.
              </Text>
            </Paragraph>
          </div>
        }
        extra={
          <Space size="middle">
            <Button 
              type="primary" 
              icon={<HomeOutlined />} 
              onClick={() => navigate("/home")}
            >
              Retour à l'accueil
            </Button>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate(-1)}
            >
              Retour
            </Button>
          </Space>
        }
      />
    </div>
  );
};

export default UnauthorizedPage;
