import{ah as Z,u as ee,j as te,r as s,a as E,n as o,R as t,C as re,d as R,k as u,e as p,g as y,B as w,ai as ae,m as ne,E as N,b2 as oe,T as ie,f as v}from"./index-O2xm1U_Z.js";import{d as S}from"./dayjs.min-CgAD4wBe.js";import{R as z,b as ce,a as se,r as le}from"./relativeTime-UhGToeYb.js";import"./fr-D5QIIXkH.js";import{u as ue}from"./useMobile-DAououQR.js";import{R as fe}from"./ReloadOutlined-EeD9QNgc.js";import{R as de}from"./BellOutlined-D8NZd8pV.js";import{L as C}from"./index-fv7kzFnJ.js";import{R as me}from"./ClockCircleOutlined-D-iaV6k8.js";import{R as pe}from"./InfoCircleOutlined-BSC7vzM8.js";import{R as T}from"./AlertOutlined-BsPkpdqt.js";import{R as _}from"./AppstoreOutlined-BlZTSyy5.js";import{R as ye}from"./ToolOutlined-BRYzAkU2.js";S.extend(le);S.locale("fr");const{Title:qe,Text:g}=ie,Be=()=>{const{notifications:$,unreadCount:q,connectionStatus:Ee,connectionStats:ge,markAsRead:B,acknowledgeNotification:he,connect:j,isConnected:n,isConnecting:i,hasError:b,optimisticDeleteNotification:P,optimisticMarkAsRead:xe,optimisticMarkAllAsRead:D}=Z(),{user:Re}=ee(),{darkMode:l}=te(),h=ue(),[f,L]=s.useState("all"),[F,k]=s.useState(!1),[we,I]=s.useState(null),[G,d]=s.useState([]),c=s.useCallback(async()=>{var e,a;k(!0),I(null);try{const r=await E.get("/api/notifications").withCredentials().timeout(3e4).retry(2);r.body&&Array.isArray(r.body)?d(r.body):(d([]),console.warn("Unexpected response structure from /api/notifications:",r.body))}catch(r){I(((a=(e=r==null?void 0:r.response)==null?void 0:e.data)==null?void 0:a.message)||r.message||"Failed to fetch notifications"),o.error("Erreur lors du chargement des notifications")}finally{k(!1)}},[]);s.useEffect(()=>{!n&&!i&&c()},[n,i,c]);const m=e=>!!(e.read_at||e.read),A=n?$:G,x=n?q:A.filter(e=>!m(e)).length;s.useEffect(()=>{b&&!i&&o.error("Connexion aux notifications interrompue. Tentative de reconnexion...")},[b,i]);const O=async e=>{try{n?await B(e):(d(a=>a.map(r=>r.id===e?{...r,read_at:new Date().toISOString(),read:!0}:r)),await E.patch(`/api/notifications/${e}/read`).withCredentials().send({}).set("withCredentials",!0).retry(2),o.success("Notification marquée comme lue"))}catch(a){console.error("Error marking notification as read:",a),o.error("Erreur lors de la mise à jour de la notification"),n||c()}},U=async()=>{try{n?D():d(e=>e.map(a=>({...a,read_at:new Date().toISOString(),read:!0}))),await E.patch("/api/notifications/read-all").withCredentials().send({}).set("withCredentials",!0).retry(2),o.success("Toutes les notifications ont été marquées comme lues"),n||c()}catch(e){console.error("Error marking all notifications as read:",e),o.error("Erreur lors de la mise à jour des notifications"),n||c()}},W=async e=>{try{n?P(e):d(a=>a.filter(r=>r.id!==e)),await E.delete(`/api/notifications/${e}`).set("withCredentials",!0).retry(2),o.success("Notification supprimée")}catch(a){console.error("Error deleting notification:",a),o.error("Erreur lors de la suppression de la notification"),n||c()}},Q=()=>{!n&&!i&&j()},H=()=>{c()},J=(e,a)=>{const r=K(a);switch(e){case"alert":case"machine_alert":return t.createElement(T,{style:r});case"maintenance":return t.createElement(ye,{style:r});case"update":return t.createElement(_,{style:r});case"production":return t.createElement(_,{style:r});case"quality":return t.createElement(T,{style:r});case"info":default:return t.createElement(pe,{style:r})}},K=e=>{switch(e){case"critical":return{color:"#ff4d4f",fontSize:"18px"};case"high":return{color:"#fa8c16",fontSize:"16px"};case"medium":return{color:"#1890ff",fontSize:"16px"};case"low":return{color:"#52c41a",fontSize:"16px"};default:return{color:"#1890ff",fontSize:"16px"}}},V=(e,a)=>{switch(a){case"critical":return"error";case"high":return"warning";case"medium":return"processing";case"low":return"success";default:switch(e){case"alert":case"machine_alert":return"error";case"maintenance":return"warning";case"update":case"production":return"processing";case"quality":return"warning";case"info":default:return"success"}}},X=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},Y=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},M=A.filter(e=>f==="all"?!0:f==="unread"?!m(e):f==="critical"?e.priority==="critical":e.category===f);return t.createElement("div",{className:"notifications-page"},t.createElement(re,{title:t.createElement(R,null,t.createElement(de,null),t.createElement("span",null,"Notifications"),x>0&&t.createElement(w,{count:x,style:{backgroundColor:"#1890ff"}}),n?t.createElement(p,{title:"Connecté en temps réel"},t.createElement(ce,{style:{color:"#52c41a"}})):i?t.createElement(p,{title:"Connexion en cours..."},t.createElement(se,{style:{color:"#1890ff"}})):t.createElement(p,{title:"Déconnecté - Cliquez pour reconnecter"},t.createElement(y,{type:"text",size:"small",icon:t.createElement(ae,{style:{color:"#ff4d4f"}}),onClick:Q}))),extra:t.createElement(R,{wrap:!0},t.createElement(u.Group,{value:f,onChange:e=>L(e.target.value),optionType:"button",buttonStyle:"solid",size:h?"small":"middle"},t.createElement(u.Button,{value:"all"},"Toutes"),t.createElement(u.Button,{value:"unread"},"Non lues"),t.createElement(u.Button,{value:"critical"},"Critiques"),t.createElement(u.Button,{value:"machine_alert"},"Machines"),t.createElement(u.Button,{value:"maintenance"},"Maintenance")),t.createElement(p,{title:"Marquer tout comme lu"},t.createElement(y,{icon:t.createElement(z,null),onClick:U,disabled:x===0,size:h?"small":"middle"})),t.createElement(p,{title:"Recharger les notifications"},t.createElement(y,{icon:t.createElement(fe,null),onClick:H,disabled:i,size:h?"small":"middle"}))),style:{background:l?"#141414":"#fff",boxShadow:l?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},F?t.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},t.createElement(ne,{size:"large"})):M.length===0?t.createElement(N,{description:"Aucune notification",image:N.PRESENTED_IMAGE_SIMPLE}):t.createElement(C,{itemLayout:"horizontal",dataSource:M,renderItem:e=>t.createElement(C.Item,{key:e.id,actions:[t.createElement(y,{key:"delete",type:"text",icon:t.createElement(oe,null),onClick:()=>W(e.id)}),!m(e)&&t.createElement(y,{key:"markAsRead",type:"text",icon:t.createElement(z,null),onClick:()=>O(e.id)})],style:{background:m(e)?"transparent":e.priority==="critical"?l?"#2a1215":"#fff2f0":e.priority==="high"?l?"#2b1d11":"#fff7e6":l?"#111b26":"#f0f7ff",padding:"12px",borderRadius:"4px",marginBottom:"8px",border:e.priority==="critical"?l?"1px solid #a8071a":"1px solid #ff7875":"none"}},t.createElement(C.Item.Meta,{avatar:J(e.category,e.priority),title:t.createElement(R,{wrap:!0},t.createElement(g,{strong:!0,style:{color:e.priority==="critical"?"#ff4d4f":"inherit"}},e.title),t.createElement(v,{color:V(e.category,e.priority),style:{fontWeight:e.priority==="critical"?"bold":"normal"}},X(e.priority)),t.createElement(v,{size:"small"},Y(e.category)),!m(e)&&t.createElement(w,{status:"processing"}),(e.acknowledged_at||e.acknowledged)&&t.createElement(w,{status:"success",text:"Acquittée"})),description:t.createElement(t.Fragment,null,t.createElement("div",{style:{marginBottom:"8px"}},e.message),t.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"8px"}},t.createElement("div",null,t.createElement(me,{style:{marginRight:4}}),t.createElement(g,{type:"secondary"},S(e.created_at||e.timestamp).fromNow())," "),e.machine_id&&t.createElement(g,{type:"secondary",style:{fontSize:"12px"}},"Machine: ",e.machine_id),e.source&&t.createElement(g,{type:"secondary",style:{fontSize:"12px"}},"Source: ",e.source)))}))})))};export{Be as default};
