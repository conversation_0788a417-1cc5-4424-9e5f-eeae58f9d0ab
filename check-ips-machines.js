#!/usr/bin/env node

/**
 * Database Check for IPS Machines
 */

import mysql from 'mysql2/promise';

async function checkIPSMachines() {
  console.log('🔍 CHECKING IPS MACHINES IN DATABASE');
  console.log('===================================\n');

  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'testingarea51'
  });

  try {
    // Check all unique machine names
    const [machineNames] = await connection.execute(`
      SELECT DISTINCT Machine_Name 
      FROM machine_daily_table_mould 
      ORDER BY Machine_Name
    `);

    console.log('📋 All machines in database:');
    machineNames.forEach((machine, index) => {
      console.log(`   ${index + 1}. ${machine.Machine_Name}`);
    });
    console.log('');

    // Check IPS machines specifically
    const [ipsMachines] = await connection.execute(`
      SELECT DISTINCT Machine_Name 
      FROM machine_daily_table_mould 
      WHERE Machine_Name LIKE 'IPS%'
      ORDER BY Machine_Name
    `);

    console.log('🏭 IPS machines found:');
    ipsMachines.forEach((machine, index) => {
      console.log(`   ${index + 1}. ${machine.Machine_Name}`);
    });
    console.log('');

    // Check data counts for each IPS machine
    console.log('📊 Data counts per IPS machine:');
    for (const machine of ipsMachines) {
      const [count] = await connection.execute(`
        SELECT COUNT(*) as record_count 
        FROM machine_daily_table_mould 
        WHERE Machine_Name = ?
      `, [machine.Machine_Name]);
      
      console.log(`   ${machine.Machine_Name}: ${count[0].record_count} records`);
    }
    console.log('');

    // Check recent data for IPS machines
    const [recentData] = await connection.execute(`
      SELECT Machine_Name, COUNT(*) as recent_count
      FROM machine_daily_table_mould 
      WHERE Machine_Name LIKE 'IPS%'
        AND Date_Insert_Day >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY Machine_Name
      ORDER BY Machine_Name
    `);

    console.log('📅 Recent data (last 30 days) for IPS machines:');
    recentData.forEach((machine) => {
      console.log(`   ${machine.Machine_Name}: ${machine.recent_count} recent records`);
    });

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await connection.end();
  }
}

checkIPSMachines();
