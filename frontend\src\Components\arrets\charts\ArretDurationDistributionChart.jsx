import React, { useEffect, useRef, useState } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { Spin, Empty, Button, Space, Row, Col } from 'antd';
import { BarChartOutlined, PercentageOutlined } from '@ant-design/icons';
import SOMIPEM_COLORS from '../../../styles/brand-colors';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';
  
import { useSettings } from '../../../hooks/useSettings';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const ArretDurationDistributionChart = ({ data = [], loading = false, colors }) => {
  const chartRef = useRef();
  const [viewMode, setViewMode] = useState('count'); // 'count', 'percentage', or 'both'
  
  // Get settings for unified chart configuration
  const { settings, charts, theme } = useSettings();
  
  // Unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    charts,
    theme
  });
  
  // Use colors prop if provided, otherwise fall back to chart config colors
  const chartColors = colors || chartConfig.colors || [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.SUCCESS_GREEN,
    SOMIPEM_COLORS.WARNING_ORANGE
  ];
  
  // Custom date parser for multiple date formats
  const parseDate = (dateString) => {
    if (!dateString || typeof dateString !== 'string') return null;
    
    try {
      const str = String(dateString).trim();
      
      // Handle ISO format first (from GraphQL: "2024-12-07T14:30:00Z")
      if (str.includes('T') && (str.includes('Z') || str.includes('+') || str.includes('-'))) {
        const date = new Date(str);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
      
      // Handle DD/MM/YYYY HH:MM:SS format (legacy database with potential multiple spaces)
      if (str.includes('/')) {
        const parts = str.split(/\s+/);
        if (parts.length !== 2) return null;
        
        const [datePart, timePart] = parts;
        const [day, month, year] = datePart.split('/');
        const [hours, minutes, seconds] = timePart.split(':');
        
        // Validate all parts exist and are numeric
        if (!day || !month || !year || !hours || !minutes || !seconds) return null;
        if (isNaN(day) || isNaN(month) || isNaN(year) || isNaN(hours) || isNaN(minutes) || isNaN(seconds)) return null;
        
        // Create date object (month is 0-indexed)
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hours), parseInt(minutes), parseInt(seconds));
      }
      
      // Standard Date constructor fallback
      const date = new Date(str);
      if (!isNaN(date.getTime())) {
        return date;
      }
      
      return null;
    } catch (error) {
      console.warn('Date parsing error:', error, 'for string:', dateString);
      return null;
    }
  };

  // Debug useEffect to track data changes
  useEffect(() => {
    console.log('🎯 ArretDurationDistributionChart - Data received:', {
      dataLength: data?.length || 0,
      dataType: typeof data,
      isArray: Array.isArray(data),
      loading,
      firstItem: data?.[0] || null
    });
  }, [data, loading]);
  
  // Process data to analyze duration distribution
  const processDurationDistribution = (stopsData) => {
    console.log('🔍 ArretDurationDistributionChart - Processing data:', {
      dataLength: stopsData?.length || 0,
      isArray: Array.isArray(stopsData),
      firstItem: stopsData?.[0] || null,
      sampleFields: stopsData?.[0] ? Object.keys(stopsData[0]) : []
    });

    // Ensure data is an array - handle both direct arrays and response objects
    const processedData = Array.isArray(stopsData) ? stopsData : (stopsData?.data || []);
    
    if (!Array.isArray(processedData) || processedData.length === 0) {
      console.log('🔍 ArretDurationDistributionChart - No valid data to process');
      return { labels: [], counts: [], percentages: [] };
    }

    // Define duration buckets (in minutes)
    const buckets = [
      { label: '0-5 min', min: 0, max: 5, count: 0 },
      { label: '5-15 min', min: 5, max: 15, count: 0 },
      { label: '15-30 min', min: 15, max: 30, count: 0 },
      { label: '30-60 min', min: 30, max: 60, count: 0 },
      { label: '1-2 heures', min: 60, max: 120, count: 0 },
      { label: '2-4 heures', min: 120, max: 240, count: 0 },
      { label: '4+ heures', min: 240, max: Infinity, count: 0 }
    ];
    
    let totalStops = 0;
    let validDurations = 0;
    let processedStops = 0;
    
    processedData.forEach((stop, index) => {
      totalStops++;
      
      // Check multiple possible field names for start and end times
      const startTime = stop.Debut_Stop || stop.debut_stop || stop.startTime || stop.start_time;
      const endTime = stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.end_time || stop.Fin_Stop;
      
      if (index < 3) {
        console.log(`🔍 Stop ${index}:`, {
          startTime,
          endTime,
          duration: stop.duration,
          duration_minutes: stop.duration_minutes,
          allFields: Object.keys(stop)
        });
      }
      
      // Try to get duration from multiple sources
      let duration = 0;
      
      if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
        duration = parseFloat(stop.duration_minutes);
      } else if (stop.duration !== undefined && stop.duration !== null) {
        duration = parseFloat(stop.duration);
      } else if (startTime && endTime) {
        try {
          const start = parseDate(startTime);
          const end = parseDate(endTime);
          
          if (start && end && !isNaN(start.getTime()) && !isNaN(end.getTime())) {
            duration = (end - start) / (1000 * 60); // Duration in minutes
            
            if (index < 3) {
              console.log(`🔍 Duration calculation ${index}:`, {
                startTime,
                endTime,
                start: start.toISOString(),
                end: end.toISOString(),
                durationMinutes: duration
              });
            }
          } else {
            if (index < 3) {
              console.log(`🔍 Failed to parse dates for stop ${index}:`, {
                startTime,
                endTime,
                parsedStart: start,
                parsedEnd: end
              });
            }
          }
        } catch (error) {
          console.warn('Error calculating duration:', error);
        }
      }
      
      if (duration > 0) {
        processedStops++;
        validDurations++;
        
        // Find the appropriate bucket
        const bucket = buckets.find(b => duration >= b.min && duration < b.max);
        if (bucket) {
          bucket.count++;
        }
      }
    });
    
    console.log('🔍 Duration processing results:', {
      totalStops,
      processedStops,
      validDurations,
      buckets: buckets.map(b => ({ label: b.label, count: b.count }))
    });

    // Calculate percentages
    const labels = buckets.map(b => b.label);
    const counts = buckets.map(b => b.count);
    const percentages = buckets.map(b => validDurations > 0 ? Math.round((b.count / validDurations) * 100) : 0);

    return {
      labels,
      counts,
      percentages,
      buckets
    };
  };

  const chartData = processDurationDistribution(data);

  // Generate colors using dynamic color system
  const getColors = (index, total) => {
    const color = chartColors[index % chartColors.length];
    return {
      background: `${color}B3`, // 70% opacity
      border: color
    };
  };

  const getDatasets = () => {
    if (viewMode === 'count') {
      return [
        {
          label: 'Nombre d\'Arrêts',
          data: chartData.counts,
          backgroundColor: chartData.labels.map((_, index) => getColors(index, chartData.labels.length).background),
          borderColor: chartData.labels.map((_, index) => getColors(index, chartData.labels.length).border),
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false
        }
      ];
    } else if (viewMode === 'percentage') {
      return [
        {
          label: 'Pourcentage (%)',
          data: chartData.percentages,
          backgroundColor: `${chartColors[1]}99`, // Using second color with transparency
          borderColor: chartColors[1],
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false
        }
      ];
    } else {
      // both mode
      return [
        {
          label: 'Nombre d\'Arrêts',
          data: chartData.counts,
          backgroundColor: chartData.labels.map((_, index) => getColors(index, chartData.labels.length).background),
          borderColor: chartData.labels.map((_, index) => getColors(index, chartData.labels.length).border),
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false,
          yAxisID: 'y'
        },
        {
          label: 'Pourcentage (%)',
          data: chartData.percentages,
          backgroundColor: `${chartColors[1]}99`,
          borderColor: chartColors[1],
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false,
          yAxisID: 'y1'
        }
      ];
    }
  };
  const getOptions = () => {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              weight: '500'
            }
          }
        },
        title: {
          display: true,
          text: viewMode === 'count' ? 'Distribution - Nombre d\'Arrêts' : 
                viewMode === 'percentage' ? 'Distribution - Pourcentage' : 
                'Distribution des Durées d\'Arrêt',
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 20
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            title: function(context) {
              return `Durée: ${context[0].label}`;
            },
            label: function(context) {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              
              if (label.includes('Nombre')) {
                return `${label}: ${value} arrêts`;
              } else {
                return `${label}: ${value}%`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 11
            },
            maxRotation: 45,
            minRotation: 0
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      },
      animation: {
        duration: chartConfig.animationConfig?.isAnimationActive ? 1000 : 0,
        easing: 'easeInOutQuart'
      }
    };

    if (viewMode === 'both') {
      // Dual axis for both mode
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Nombre d\'Arrêts',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
      baseOptions.scales.y1 = {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Pourcentage (%)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          beginAtZero: true,
          max: 100,
          font: {
            size: 11
          },
          callback: function(value) {
            return value + '%';
          }
        }
      };
    } else {
      // Single axis
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: viewMode === 'count' ? 'Nombre d\'Arrêts' : 'Pourcentage (%)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          max: viewMode === 'percentage' ? 100 : undefined,
          font: {
            size: 11
          },
          callback: function(value) {
            return viewMode === 'percentage' ? value + '%' : value;
          }
        }
      };
    }

    return baseOptions;
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Spin size="large" tip="Chargement de la distribution des durées..." />
      </div>
    );
  }

  if (!chartData.labels || chartData.labels.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Empty 
          description="Aucune donnée de durée disponible"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }
  return (
    <div style={{ height: '100%', width: '100%' }}>
      {/* View Mode Toggle */}
      <Row style={{ marginBottom: '12px' }}>
        <Col span={24}>
          <Space size="small" style={{ width: '100%', justifyContent: 'center' }}>
            <Button
              type={viewMode === 'count' ? 'primary' : 'default'}
              icon={<BarChartOutlined />}
              onClick={() => setViewMode('count')}
              size="small"
            >
              Nombre
            </Button>
            <Button
              type={viewMode === 'percentage' ? 'primary' : 'default'}
              icon={<PercentageOutlined />}
              onClick={() => setViewMode('percentage')}
              size="small"
            >
              Pourcentage
            </Button>
            <Button
              type={viewMode === 'both' ? 'primary' : 'default'}
              onClick={() => setViewMode('both')}
              size="small"
            >
              Les deux
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Chart */}
      <div style={{
        height: chartConfig.height || 'calc(100% - 70px)',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '12px',
        padding: '16px'
      }}>
        <Bar 
          ref={chartRef} 
          data={{
            labels: chartData.labels,
            datasets: getDatasets()
          }} 
          options={getOptions()} 
        />
      </div>
    </div>
  );
};

export default ArretDurationDistributionChart;
