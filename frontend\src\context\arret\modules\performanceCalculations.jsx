import { PERFORMANCE_CONSTANTS } from './constants.jsx'

/**
 * Performance Calculations Module
 * Handles MTTR, MTBF, and Availability (DOPER) calculations
 * Matches the logic from Arrets2.jsx for consistency
 */

/**
 * Calculate performance metrics from stops data
 * @param {Array} stopsData - Array of machine stop data
 * @param {string} dateRangeType - Type of date range (day, week, month)
 * @param {Object} selectedDate - Selected date object
 * @returns {Object} Performance metrics { mttr, mtbf, doper }
 */
export const calculatePerformanceMetrics = (stopsData = [], dateRangeType = 'day', selectedDate = null) => {
  

  try {
    // Calculate total downtime in minutes using the same logic as Arrets2.jsx
    const totalDowntimeMinutes = stopsData.reduce((total, stop) => {
      if (stop.duree_arret) {
        // Convert HH:MM:SS to minutes
        const timeParts = stop.duree_arret.split(':');
        if (timeParts.length >= 3) {
          const hours = parseInt(timeParts[0]) || 0;
          const minutes = parseInt(timeParts[1]) || 0;
          const seconds = parseInt(timeParts[2]) || 0;
          return total + (hours * PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR) + minutes + (seconds / PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR);
        } else if (timeParts.length === 2) {
          // Handle MM:SS format
          const minutes = parseInt(timeParts[0]) || 0;
          const seconds = parseInt(timeParts[1]) || 0;
          return total + minutes + (seconds / PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR);
        }
      }
      return total;
    }, 0);

    // Calculate number of stops
    const numberOfStops = stopsData.length;

    // Calculate total available time based on date range (same logic as Arrets2.jsx)
    let totalAvailableTimeMinutes = 0;
    switch (dateRangeType) {
      case 'day':
        totalAvailableTimeMinutes = PERFORMANCE_CONSTANTS.HOURS_PER_DAY * PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR;
        break;
      case 'week':
        totalAvailableTimeMinutes = PERFORMANCE_CONSTANTS.DAYS_PER_WEEK * PERFORMANCE_CONSTANTS.HOURS_PER_DAY * PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR;
        break;
      case 'month':
        // Approximate month length
        totalAvailableTimeMinutes = PERFORMANCE_CONSTANTS.APPROX_DAYS_PER_MONTH * PERFORMANCE_CONSTANTS.HOURS_PER_DAY * PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR;
        break;
    }

    // Calculate MTTR (Mean Time To Repair) - same formula as Arrets2.jsx
    const mttr = numberOfStops > 0 ? (totalDowntimeMinutes / numberOfStops) : 0;

    // Calculate MTBF (Mean Time Between Failures) - same formula as Arrets2.jsx
    const mtbf = numberOfStops > 0 ? ((totalAvailableTimeMinutes - totalDowntimeMinutes) / numberOfStops) : 0;

    // Calculate DOPER (now renamed to Disponibilité) - same formula as Arrets2.jsx
    const doper = totalAvailableTimeMinutes > 0 ? 
      ((totalAvailableTimeMinutes - totalDowntimeMinutes) / totalAvailableTimeMinutes) * 100 : 100;

    const metrics = {
      mttr: Math.round(mttr * 100) / 100,
      mtbf: Math.round(mtbf * 100) / 100,
      doper: Math.round(doper * 100) / 100
    };

    

    return metrics;

  } catch (error) {
    console.error('❌ Error calculating performance metrics:', error);
    return {
      mttr: 0,
      mtbf: 0,
      doper: 0
    };
  }
}

/**
 * Format performance metric for display
 * @param {number} value - Metric value
 * @param {string} type - Metric type (mttr, mtbf, doper)
 * @returns {string} Formatted value
 */
export const formatPerformanceMetric = (value, type) => {
  if (value === 0 || value === null || value === undefined) {
    return '0';
  }

  switch (type) {
    case 'mttr':
    case 'mtbf':
      // Format time values (minutes)
      if (value < 60) {
        return `${Math.round(value)}min`;
      } else {
        const hours = Math.floor(value / 60);
        const minutes = Math.round(value % 60);
        return minutes > 0 ? `${hours}h ${minutes}min` : `${hours}h`;
      }
    case 'doper':
      // Format percentage
      return `${Math.round(value * 100) / 100}%`;
    default:
      return String(Math.round(value * 100) / 100);
  }
}

/**
 * Get performance metric status/color based on value
 * @param {number} value - Metric value
 * @param {string} type - Metric type
 * @returns {string} Status (good, warning, danger)
 */
export const getPerformanceMetricStatus = (value, type) => {
  if (value === 0 || value === null || value === undefined) {
    return 'danger';
  }

  switch (type) {
    case 'mttr':
      // Lower MTTR is better
      if (value <= 30) return 'good';     // <= 30 minutes
      if (value <= 60) return 'warning';  // <= 1 hour
      return 'danger';                    // > 1 hour
    
    case 'mtbf':
      // Higher MTBF is better
      if (value >= 480) return 'good';    // >= 8 hours
      if (value >= 240) return 'warning'; // >= 4 hours
      return 'danger';                    // < 4 hours
    
    case 'doper':
      // Higher availability is better
      if (value >= 95) return 'good';     // >= 95%
      if (value >= 85) return 'warning';  // >= 85%
      return 'danger';                    // < 85%
    
    default:
      return 'warning';
  }
}

/**
 * Calculate availability trend data from stops data
 * @param {Array} stopsData - Array of machine stop data
 * @param {string} dateRangeType - Type of date range (day, week, month)
 * @returns {Array} Availability trend data
 */
export const calculateAvailabilityTrendData = (stopsData = [], dateRangeType = 'day') => {
  try {
    console.log('🔧 calculateAvailabilityTrendData called with:', {
      dataType: typeof stopsData,
      isArray: Array.isArray(stopsData),
      dataLength: stopsData?.length || 0,
      dateRangeType: dateRangeType,
      sampleData: stopsData?.slice(0, 2) || []
    });
    
    if (!stopsData || stopsData.length === 0) {
      console.log('❌ No stops data provided to calculateAvailabilityTrendData');
      return [];
    }

    // Enhanced custom date parser for backend date format
    const parseDate = (dateString) => {
      if (!dateString || typeof dateString !== 'string') return null;
      
      try {
        // Handle multiple date formats from backend
        const cleanedDate = dateString.trim();
        
        // Format 1: "DD/MM/YYYY HH:MM:SS" (most common)
        let match = cleanedDate.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);
        if (match) {
          const [_, day, month, year, hours, minutes, seconds] = match;
          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 
                               parseInt(hours), parseInt(minutes), parseInt(seconds));
          if (!isNaN(date.getTime())) return date;
        }
        
        // Format 2: "YYYY HH:MM:SS-MM- DD" (alternative backend format)
        match = cleanedDate.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/);
        if (match) {
          const [_, year, hours, minutes, seconds, month, day] = match;
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 
                         parseInt(hours), parseInt(minutes), parseInt(seconds));
        }
        
        // ISO format fallback
        const isoDate = new Date(cleanedDate);
        if (!isNaN(isoDate.getTime())) return isoDate;
        
        return null;
      } catch (error) {
        return null;
      }
    };

    // Group stops by date
    const stopsByDate = {};
    
    stopsData.forEach(stop => {
      const startDate = parseDate(stop.Debut_Stop || stop.debut_stop || stop.startTime);
      if (!startDate) return;
      
      const dateKey = startDate.toISOString().split('T')[0]; // YYYY-MM-DD format
      
      if (!stopsByDate[dateKey]) {
        stopsByDate[dateKey] = {
          date: dateKey,
          stops: [],
          totalDowntime: 0,
          stopCount: 0
        };
      }
      
      stopsByDate[dateKey].stops.push(stop);
      stopsByDate[dateKey].stopCount++;
      
      // Calculate downtime for this stop
      const endDate = parseDate(stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.Fin_Stop);
      if (endDate && startDate) {
        const downtimeMinutes = (endDate - startDate) / (1000 * 60);
        if (downtimeMinutes > 0) {
          stopsByDate[dateKey].totalDowntime += downtimeMinutes;
        }
      }
    });

    console.log('📊 Grouped stops by date:', {
      uniqueDates: Object.keys(stopsByDate).length,
      stopsByDate: Object.keys(stopsByDate).slice(0, 3).map(date => ({
        date,
        stopCount: stopsByDate[date].stopCount,
        totalDowntime: stopsByDate[date].totalDowntime
      }))
    });

    // Calculate availability for each date
    const availabilityTrendData = Object.values(stopsByDate).map(dayData => {
      const totalAvailableTimeMinutes = PERFORMANCE_CONSTANTS.HOURS_PER_DAY * PERFORMANCE_CONSTANTS.MINUTES_PER_HOUR;
      const disponibilite = totalAvailableTimeMinutes > 0 ? 
        ((totalAvailableTimeMinutes - dayData.totalDowntime) / totalAvailableTimeMinutes) * 100 : 100;
      
      return {
        date: dayData.date,
        disponibilite: Math.round(disponibilite * 100) / 100,
        downtime: Math.round(dayData.totalDowntime * 100) / 100,
        stopCount: dayData.stopCount
      };
    });

    // Sort by date
    availabilityTrendData.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    return availabilityTrendData;
    
  } catch (error) {
    console.error('❌ Error calculating availability trend data:', error);
    return [];
  }
};

/**
 * Calculate MTTR calendar data from stops data
 * @param {Array} stopsData - Array of machine stop data
 * @returns {Array} MTTR calendar data
 */
export const calculateMTTRCalendarData = (stopsData = []) => {
  try {
    console.log('🔧 calculateMTTRCalendarData called with:', {
      dataType: typeof stopsData,
      isArray: Array.isArray(stopsData),
      dataLength: stopsData?.length || 0,
      sampleData: stopsData?.slice(0, 2) || []
    });
    
    if (!stopsData || stopsData.length === 0) {
      console.log('❌ No stops data provided to calculateMTTRCalendarData');
      return [];
    }

    // Enhanced custom date parser for backend date format
    const parseDate = (dateString) => {
      if (!dateString || typeof dateString !== 'string') return null;
      
      try {
        // Handle multiple date formats from backend
        const cleanedDate = dateString.trim();
        
        // Format 1: "DD/MM/YYYY HH:MM:SS" (most common)
        let match = cleanedDate.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);
        if (match) {
          const [_, day, month, year, hours, minutes, seconds] = match;
          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 
                               parseInt(hours), parseInt(minutes), parseInt(seconds));
          if (!isNaN(date.getTime())) return date;
        }
        
        // Format 2: "YYYY HH:MM:SS-MM- DD" (alternative backend format)
        match = cleanedDate.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/);
        if (match) {
          const [_, year, hours, minutes, seconds, month, day] = match;
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 
                         parseInt(hours), parseInt(minutes), parseInt(seconds));
        }
        
        // ISO format fallback
        const isoDate = new Date(cleanedDate);
        if (!isNaN(isoDate.getTime())) return isoDate;
        
        return null;
      } catch (error) {
        return null;
      }
    };

    // Group stops by date and calculate MTTR
    const mttrByDate = {};
    
    stopsData.forEach(stop => {
      const startDate = parseDate(stop.Debut_Stop || stop.debut_stop || stop.startTime);
      const endDate = parseDate(stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.Fin_Stop);
      
      if (!startDate || !endDate) return;
      
      const dateKey = startDate.toISOString().split('T')[0]; // YYYY-MM-DD format
      const downtimeMinutes = (endDate - startDate) / (1000 * 60);
      
      if (downtimeMinutes > 0) {
        if (!mttrByDate[dateKey]) {
          mttrByDate[dateKey] = {
            date: dateKey,
            totalDowntime: 0,
            stopCount: 0
          };
        }
        
        mttrByDate[dateKey].totalDowntime += downtimeMinutes;
        mttrByDate[dateKey].stopCount++;
      }
    });

    console.log('📊 MTTR grouped by date:', {
      uniqueDates: Object.keys(mttrByDate).length,
      mttrByDate: Object.keys(mttrByDate).slice(0, 3).map(date => ({
        date,
        stopCount: mttrByDate[date].stopCount,
        totalDowntime: mttrByDate[date].totalDowntime
      }))
    });

    // Calculate MTTR for each date
    const mttrCalendarData = Object.values(mttrByDate).map(dayData => {
      const mttr = dayData.stopCount > 0 ? dayData.totalDowntime / dayData.stopCount : 0;
      
      return {
        date: dayData.date,
        mttr: Math.round(mttr * 100) / 100,
        stopCount: dayData.stopCount,
        totalDowntime: Math.round(dayData.totalDowntime * 100) / 100
      };
    });

    // Sort by date
    mttrCalendarData.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    return mttrCalendarData;
    
  } catch (error) {
    console.error('❌ Error calculating MTTR calendar data:', error);
    return [];
  }
};

/**
 * Calculate downtime Pareto data from stops data
 * @param {Array} stopsData - Array of machine stop data
 * @returns {Array} Downtime Pareto data
 */
export const calculateDowntimeParetoData = (stopsData = []) => {
  try {
    console.log('🔧 calculateDowntimeParetoData called with:', {
      dataType: typeof stopsData,
      isArray: Array.isArray(stopsData),
      dataLength: stopsData?.length || 0,
      sampleData: stopsData?.slice(0, 2) || []
    });
    
    if (!stopsData || stopsData.length === 0) {
      console.log('❌ No stops data provided to calculateDowntimeParetoData');
      return [];
    }

    // Enhanced custom date parser for backend date format with better error handling
    const parseDate = (dateString) => {
      if (!dateString || typeof dateString !== 'string') {
        console.log('❌ Invalid date string:', dateString);
        return null;
      }
      
      try {
        // Handle multiple date formats from backend
        const cleanedDate = dateString.trim();
        
        // Format 1: "DD/MM/YYYY HH:MM:SS" (most common)
        let match = cleanedDate.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);
        if (match) {
          const [_, day, month, year, hours, minutes, seconds] = match;
          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 
                               parseInt(hours), parseInt(minutes), parseInt(seconds));
          if (!isNaN(date.getTime())) {
            console.log('✅ Parsed date format 1:', cleanedDate, '->', date);
            return date;
          }
        }
        
        // Format 2: "YYYY HH:MM:SS-MM- DD" (alternative backend format)
        match = cleanedDate.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/);
        if (match) {
          const [_, year, hours, minutes, seconds, month, day] = match;
          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 
                               parseInt(hours), parseInt(minutes), parseInt(seconds));
          if (!isNaN(date.getTime())) {
            console.log('✅ Parsed date format 2:', cleanedDate, '->', date);
            return date;
          }
        }
        
        // Format 3: ISO format fallback
        const isoDate = new Date(cleanedDate);
        if (!isNaN(isoDate.getTime())) {
          console.log('✅ Parsed ISO date:', cleanedDate, '->', isoDate);
          return isoDate;
        }
        
        console.log('❌ Could not parse date format:', cleanedDate);
        return null;
      } catch (error) {
        console.log('❌ Date parsing error:', error, 'for date:', dateString);
        return null;
      }
    };

    // Group stops by reason and calculate total downtime
    const downtimeByReason = {};
    let successfulParsing = 0;
    let failedParsing = 0;
    
    stopsData.forEach((stop, index) => {
      console.log(`🔍 Processing stop ${index + 1}:`, {
        Machine_Name: stop.Machine_Name,
        Code_Stop: stop.Code_Stop,  // Primary field for stop reason
        Type_Arret: stop.Type_Arret || 'N/A',  // Legacy field (doesn't exist in DB)
        Debut_Stop: stop.Debut_Stop,
        Fin_Stop_Time: stop.Fin_Stop_Time,
        Fin_Stop: stop.Fin_Stop
      });
      
      const startDate = parseDate(stop.Debut_Stop || stop.debut_stop || stop.startTime);
      const endDate = parseDate(stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.Fin_Stop);
      
      // Enhanced stop reason detection - use Code_Stop as primary field since Type_Arret doesn't exist
      let reason = stop.Code_Stop || stop.code_stop || stop.stopCode || stop.reason || stop.cause;
      
      // If still no reason found, try legacy field names
      if (!reason || reason === null || reason === 'null' || reason === '') {
        reason = stop.Type_Arret || stop.type_arret;
      }
      
      // Final fallback
      if (!reason || reason === null || reason === 'null' || reason === '') {
        reason = 'Arrêt non déclaré';
      }
      
      console.log(`🔍 Stop reason determined: "${reason}" from Code_Stop: "${stop.Code_Stop}", Type_Arret: "${stop.Type_Arret || 'N/A'}"`);
      
      if (!startDate || !endDate) {
        failedParsing++;
        console.log(`❌ Failed to parse dates for stop ${index + 1}:`, {
          startDateString: stop.Debut_Stop || stop.debut_stop || stop.startTime,
          endDateString: stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.Fin_Stop,
          parsedStart: startDate,
          parsedEnd: endDate
        });
        return;
      }
      
      const downtimeMinutes = (endDate - startDate) / (1000 * 60);
      
      console.log(`✅ Calculated downtime for stop ${index + 1}:`, {
        reason: reason,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        downtimeMinutes: downtimeMinutes
      });
      
      if (downtimeMinutes > 0) {
        successfulParsing++;
        if (!downtimeByReason[reason]) {
          downtimeByReason[reason] = {
            reason: reason,
            totalDowntime: 0,
            stopCount: 0
          };
        }
        
        downtimeByReason[reason].totalDowntime += downtimeMinutes;
        downtimeByReason[reason].stopCount++;
      }
    });

    console.log('📊 Parsing summary:', {
      totalStops: stopsData.length,
      successfulParsing: successfulParsing,
      failedParsing: failedParsing,
      uniqueReasons: Object.keys(downtimeByReason).length,
      downtimeByReason: downtimeByReason
    });

    // Convert to array and sort by total downtime (descending)
    const paretoData = Object.values(downtimeByReason)
      .sort((a, b) => b.totalDowntime - a.totalDowntime);

    // Calculate cumulative percentages for Pareto analysis
    const totalDowntime = paretoData.reduce((sum, item) => sum + item.totalDowntime, 0);
    let cumulativeDowntime = 0;
    
    const downtimeParetoData = paretoData.map(item => {
      cumulativeDowntime += item.totalDowntime;
      const percentage = totalDowntime > 0 ? (item.totalDowntime / totalDowntime) * 100 : 0;
      const cumulativePercentage = totalDowntime > 0 ? (cumulativeDowntime / totalDowntime) * 100 : 0;
      
      return {
        reason: item.reason,
        value: Math.round(item.totalDowntime * 100) / 100, // Charts expect 'value' field
        totalDowntime: Math.round(item.totalDowntime * 100) / 100, // Keep original field for backward compatibility
        stopCount: item.stopCount,
        percentage: Math.round(percentage * 100) / 100,
        cumulativePercentage: Math.round(cumulativePercentage * 100) / 100
      };
    });
    
    console.log('🎯 Final Pareto data:', {
      totalDowntime: totalDowntime,
      paretoDataLength: downtimeParetoData.length,
      paretoData: downtimeParetoData
    });
    
    return downtimeParetoData;
    
  } catch (error) {
    console.error('❌ Error calculating downtime Pareto data:', error);
    return [];
  }
};
