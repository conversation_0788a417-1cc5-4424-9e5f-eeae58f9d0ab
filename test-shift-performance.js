/**
 * Test Shift Performance Implementation
 * Verify that the new shift-based performance data works correctly
 */

import fetch from 'node-fetch';

const GRAPHQL_URL = 'http://localhost:5000/api/graphql';

// Test the new shift performance query
const testShiftPerformance = async () => {
  console.log('🔍 Testing Shift Performance Query...\n');

  const query = `
    query {
      enhancedGetShiftPerformance(filters: { model: "IPS" }) {
        data {
          shift_name
          production
          rejects
          availability
          performance
          oee
          quality
          disponibilite
          downtime
        }
        dataSource
      }
    }
  `;

  try {
    const response = await fetch(GRAPHQL_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.errors) {
      console.log('❌ GraphQL Errors:', JSON.stringify(result.errors, null, 2));
      return;
    }

    const data = result.data.enhancedGetShiftPerformance;
    console.log(`📦 Data source: ${data.dataSource}`);
    console.log(`🔄 Total shifts: ${data.data.length}`);
    console.log('');

    console.log('📋 Shift Performance Results (Team Comparison):');
    data.data.forEach((shift, index) => {
      console.log(`   ${index + 1}. ${shift.shift_name}`);
      console.log(`      Production: ${shift.production} pieces`);
      console.log(`      Rejects: ${shift.rejects} pieces`);
      console.log(`      OEE: ${shift.oee}%`);
      console.log(`      Availability: ${shift.availability}%`);
      console.log(`      Performance: ${shift.performance}%`);
      console.log(`      Quality: ${shift.quality}%`);
      console.log(`      Downtime: ${shift.downtime} hours`);
      console.log('');
    });

    // Verify we're getting shift data (not machine data)
    const uniqueShifts = [...new Set(data.data.map(s => s.shift_name))];
    console.log(`✅ Unique shifts found: ${uniqueShifts.join(', ')}`);
    
    if (uniqueShifts.length > 0 && !uniqueShifts.includes('IPS01')) {
      console.log('✅ SUCCESS: Data is properly aggregated by shifts (not machines)');
    } else {
      console.log('❌ ISSUE: Data might still be showing machine names instead of shifts');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Test both machine and shift performance for comparison
const testBothPerformanceQueries = async () => {
  console.log('🔄 Comparing Machine vs Shift Performance...\n');

  const machineQuery = `
    query {
      enhancedGetMachinePerformance(filters: { model: "IPS" }) {
        data {
          Machine_Name
          production
        }
        dataSource
      }
    }
  `;

  const shiftQuery = `
    query {
      enhancedGetShiftPerformance(filters: { model: "IPS" }) {
        data {
          shift_name
          production
        }
        dataSource
      }
    }
  `;

  try {
    const [machineResponse, shiftResponse] = await Promise.all([
      fetch(GRAPHQL_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: machineQuery })
      }),
      fetch(GRAPHQL_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: shiftQuery })
      })
    ]);

    const machineResult = await machineResponse.json();
    const shiftResult = await shiftResponse.json();

    console.log('🏭 Machine Performance (for individual machine charts):');
    machineResult.data.enhancedGetMachinePerformance.data.forEach(machine => {
      console.log(`   ${machine.Machine_Name}: ${machine.production} pieces`);
    });

    console.log('\n🔄 Shift Performance (for team comparison charts):');
    shiftResult.data.enhancedGetShiftPerformance.data.forEach(shift => {
      console.log(`   ${shift.shift_name}: ${shift.production} pieces`);
    });

    console.log('\n✅ Both queries working - different aggregation approaches confirmed!');

  } catch (error) {
    console.error('❌ Comparison test failed:', error);
  }
};

// Export for external testing
export { testShiftPerformance, testBothPerformanceQueries };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testShiftPerformance()
    .then(() => testBothPerformanceQueries())
    .catch(console.error);
}
