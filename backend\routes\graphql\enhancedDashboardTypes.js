/**
 * Enhanced GraphQL Types for Unified Dashboard
 * 
 * Additional types for the unified dashboard system with Elasticsearch support
 */

import { GraphQLObjectType, GraphQLString, GraphQLInt, GraphQLFloat, GraphQLList, GraphQLBoolean, GraphQLInputObjectType } from 'graphql';

// Data Source Status Type
export const DataSourceStatusType = new GraphQLObjectType({
  name: 'DataSourceStatus',
  fields: {
    primarySource: { type: GraphQLString },
    elasticsearch: { 
      type: new GraphQLObjectType({
        name: 'ElasticsearchStatus',
        fields: {
          available: { type: GraphQLBoolean },
          stats: {
            type: new GraphQLObjectType({
              name: 'ElasticsearchStats',
              fields: {
                documentCount: { type: GraphQLInt },
                indexSize: { type: GraphQLInt },
                status: { type: GraphQLString }
              }
            })
          },
          error: { type: GraphQLString }
        }
      })
    },
    mysql: {
      type: new GraphQLObjectType({
        name: 'MySQLStatus',
        fields: {
          available: { type: GraphQLBoolean },
          stats: { type: GraphQLString }, // Could be expanded later
          error: { type: GraphQLString }
        }
      })
    }
  }
});

// Enhanced Stop Type with Elasticsearch fields
export const EnhancedStopType = new GraphQLObjectType({
  name: 'EnhancedStop',
  fields: {
    id: { type: GraphQLString },
    machineId: { type: GraphQLString },
    machineName: { type: GraphQLString },
    stopReason: { type: GraphQLString },
    stopCode: { type: GraphQLString },
    startTime: { type: GraphQLString },
    endTime: { type: GraphQLString },
    duration: { type: GraphQLInt }, // in minutes
    shift: { type: GraphQLString },
    operator: { type: GraphQLString },
    description: { type: GraphQLString },
    category: { type: GraphQLString },
    severity: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    dayOfWeek: { type: GraphQLInt },
    hourOfDay: { type: GraphQLInt },
    weekNumber: { type: GraphQLInt },
    monthYear: { type: GraphQLString },
    highlights: { type: GraphQLString } // For search results
  }
});

// Enhanced Stops Result with metadata
export const EnhancedStopsResultType = new GraphQLObjectType({
  name: 'EnhancedStopsResult',
  fields: {
    stops: { type: new GraphQLList(EnhancedStopType) },
    total: { type: GraphQLInt },
    page: { type: GraphQLInt },
    size: { type: GraphQLInt },
    totalPages: { type: GraphQLInt },
    dataSource: { type: GraphQLString }
  }
});

// Category Count Type
export const CategoryCountType = new GraphQLObjectType({
  name: 'CategoryCount',
  fields: {
    category: { type: GraphQLString },
    count: { type: GraphQLInt }
  }
});

// Severity Count Type
export const SeverityCountType = new GraphQLObjectType({
  name: 'SeverityCount',
  fields: {
    severity: { type: GraphQLString },
    count: { type: GraphQLInt }
  }
});

// Dashboard Statistics Type
export const DashboardStatsType = new GraphQLObjectType({
  name: 'DashboardStats',
  fields: {
    totalStops: { type: GraphQLInt },
    totalDuration: { type: GraphQLInt },
    averageDuration: { type: GraphQLInt },
    uniqueMachines: { type: GraphQLInt },
    stopsBySeverity: { type: new GraphQLList(SeverityCountType) },
    stopsByCategory: { type: new GraphQLList(CategoryCountType) },
    dataSource: { type: GraphQLString }
  }
});

// Enhanced Top Stop Type
export const EnhancedTopStopType = new GraphQLObjectType({
  name: 'EnhancedTopStop',
  fields: {
    id: { type: GraphQLInt },
    reason: { type: GraphQLString },
    count: { type: GraphQLInt },
    totalDuration: { type: GraphQLInt },
    averageDuration: { type: GraphQLInt },
    percentage: { type: GraphQLFloat }
  }
});

// Enhanced Top Stops Result
export const EnhancedTopStopsResultType = new GraphQLObjectType({
  name: 'EnhancedTopStopsResult',
  fields: {
    reasons: { type: new GraphQLList(EnhancedTopStopType) },
    dataSource: { type: GraphQLString }
  }
});

// Machine Comparison Enhanced Type
export const EnhancedMachineComparisonType = new GraphQLObjectType({
  name: 'EnhancedMachineComparison',
  fields: {
    machineId: { type: GraphQLString },
    machineName: { type: GraphQLString },
    totalStops: { type: GraphQLInt },
    totalDuration: { type: GraphQLInt },
    averageDuration: { type: GraphQLInt },
    categories: { type: new GraphQLList(CategoryCountType) },
    severityBreakdown: { type: new GraphQLList(SeverityCountType) }
  }
});

// Enhanced Machine Comparison Result
export const EnhancedMachineComparisonResultType = new GraphQLObjectType({
  name: 'EnhancedMachineComparisonResult',
  fields: {
    machines: { type: new GraphQLList(EnhancedMachineComparisonType) },
    dataSource: { type: GraphQLString }
  }
});

// Evolution Data Point Type
export const EvolutionDataPointType = new GraphQLObjectType({
  name: 'EvolutionDataPoint',
  fields: {
    date: { type: GraphQLString },
    timestamp: { type: GraphQLString },
    stopsCount: { type: GraphQLInt },
    totalDuration: { type: GraphQLInt },
    categories: { type: new GraphQLList(CategoryCountType) }
  }
});

// Stop Evolution Result Type
export const StopEvolutionResultType = new GraphQLObjectType({
  name: 'StopEvolutionResult',
  fields: {
    evolution: { type: new GraphQLList(EvolutionDataPointType) },
    dataSource: { type: GraphQLString }
  }
});

// Performance Metrics Type
export const PerformanceMetricsType = new GraphQLObjectType({
  name: 'PerformanceMetrics',
  fields: {
    mttr: { type: GraphQLInt }, // Mean Time To Repair
    mtbf: { type: GraphQLInt }, // Mean Time Between Failures  
    availability: { type: GraphQLFloat }, // Availability percentage
    totalDowntime: { type: GraphQLInt },
    averageStopsPerDay: { type: GraphQLFloat },
    dataSource: { type: GraphQLString }
  }
});

// Enhanced Production Chart Type with data source
export const EnhancedProductionChartType = new GraphQLObjectType({
  name: 'EnhancedProductionChart',
  fields: {
    Date_Insert_Day: { type: GraphQLString },
    Total_Good_Qty_Day: { type: GraphQLFloat },
    Total_Rejects_Qty_Day: { type: GraphQLFloat },
    OEE_Day: { type: GraphQLFloat },
    Speed_Day: { type: GraphQLFloat },
    Availability_Rate_Day: { type: GraphQLFloat },
    Performance_Rate_Day: { type: GraphQLFloat },
    Quality_Rate_Day: { type: GraphQLFloat }
  }
});

// Enhanced Production Chart Result with data source
export const EnhancedProductionChartResultType = new GraphQLObjectType({
  name: 'EnhancedProductionChartResult',
  fields: {
    data: { type: new GraphQLList(EnhancedProductionChartType) },
    dataSource: { type: GraphQLString }
  }
});

// Enhanced Production Sidecards Type
export const EnhancedProductionSidecardsType = new GraphQLObjectType({
  name: 'EnhancedProductionSidecards',
  fields: {
    goodqty: { type: GraphQLFloat },
    rejetqty: { type: GraphQLFloat },
    dataSource: { type: GraphQLString }
  }
});

// Enhanced Machine Performance Type
export const EnhancedMachinePerformanceType = new GraphQLObjectType({
  name: 'EnhancedMachinePerformance',
  fields: {
    Machine_Name: { type: GraphQLString },
    Shift: { type: GraphQLString },
    production: { type: GraphQLFloat },
    rejects: { type: GraphQLFloat },
    availability: { type: GraphQLFloat },
    performance: { type: GraphQLFloat },
    oee: { type: GraphQLFloat },
    quality: { type: GraphQLFloat },
    disponibilite: { type: GraphQLFloat },
    downtime: { type: GraphQLFloat }
  }
});

// Enhanced Machine Performance Result with data source
export const EnhancedMachinePerformanceResultType = new GraphQLObjectType({
  name: 'EnhancedMachinePerformanceResult',
  fields: {
    data: { type: new GraphQLList(EnhancedMachinePerformanceType) },
    dataSource: { type: GraphQLString }
  }
});

// Enhanced Filter Input Type
export const EnhancedFilterInputType = new GraphQLInputObjectType({
  name: 'EnhancedFilterInput',
  fields: {
    dateRangeType: { type: GraphQLString },
    model: { type: GraphQLString },
    machine: { type: GraphQLString },
    date: { type: GraphQLString },
    startDate: { type: GraphQLString },
    endDate: { type: GraphQLString }
  }
});

// Data Sync Statistics Type
export const DataSyncStatsType = new GraphQLObjectType({
  name: 'DataSyncStats',
  fields: {
    isRunning: { type: GraphQLBoolean },
    lastSync: { type: GraphQLString },
    totalSyncs: { type: GraphQLInt },
    successfulSyncs: { type: GraphQLInt },
    failedSyncs: { type: GraphQLInt },
    conflictsResolved: { type: GraphQLInt },
    recordsSynced: { type: GraphQLInt },
    successRate: { type: GraphQLFloat },
    nextSyncEstimate: { type: GraphQLString }
  }
});

// Manual Sync Result Type
export const ManualSyncResultType = new GraphQLObjectType({
  name: 'ManualSyncResult',
  fields: {
    success: { type: GraphQLBoolean },
    message: { type: GraphQLString },
    stats: { type: DataSyncStatsType },
    duration: { type: GraphQLInt }
  }
});

// Pagination Input Type
export const PaginationInputType = new GraphQLInputObjectType({
  name: 'PaginationInput',
  fields: {
    page: { type: GraphQLInt },
    size: { type: GraphQLInt }
  }
});

// Export all types for use in schema
export const enhancedTypes = {
  DataSourceStatusType,
  EnhancedStopType,
  EnhancedStopsResultType,
  CategoryCountType,
  SeverityCountType,
  DashboardStatsType,
  EnhancedTopStopType,
  EnhancedTopStopsResultType,
  EnhancedMachineComparisonType,
  EnhancedMachineComparisonResultType,
  EvolutionDataPointType,
  StopEvolutionResultType,
  PerformanceMetricsType,
  EnhancedFilterInputType,
  PaginationInputType,
  // Production types with data source indicators
  EnhancedProductionChartType,
  EnhancedProductionChartResultType,
  EnhancedProductionSidecardsType,
  EnhancedMachinePerformanceType,
  EnhancedMachinePerformanceResultType,
  // Data synchronization types
  DataSyncStatsType,
  ManualSyncResultType
};