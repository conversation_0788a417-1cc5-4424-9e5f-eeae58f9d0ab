/**
 * Lazy Component Wrapper with Enhanced Skeleton Loading
 * Provides smart loading, caching, and beautiful skeleton states for dashboard components
 */

import React, { memo } from 'react';
import { Skeleton, Card } from 'antd';
import useLazyLoading from '../../hooks/useLazyLoading';

// Enhanced skeleton variants for different component types
const SkeletonVariants = {
  card: {
    avatar: true,
    paragraph: { rows: 2 },
    title: true
  },
  chart: {
    avatar: false,
    paragraph: { rows: 6 },
    title: true
  },
  table: {
    avatar: false,
    paragraph: { rows: 8 },
    title: false
  },
  stats: {
    avatar: true,
    paragraph: { rows: 1 },
    title: false
  },
  performance: {
    avatar: false,
    paragraph: { rows: 3 },
    title: true
  }
};

const LazyComponentWrapper = memo(({ 
  children, 
  priority = 1, 
  delay = 0, 
  loading = false,
  skeletonType = 'card',
  skeletonProps = {},
  fallback = null,
  height = 200,
  className = '',
  showCard = false,
  title = null
}) => {
  const { shouldRender, isVisible, elementRef } = useLazyLoading(priority, delay);

  // Get skeleton configuration based on type
  const skeletonConfig = {
    ...SkeletonVariants[skeletonType],
    ...skeletonProps
  };

  // Create enhanced skeleton with proper styling
  const renderSkeleton = () => {
    const skeletonContent = (
      <div style={{ padding: showCard ? '0' : '16px' }}>
        <Skeleton 
          active 
          {...skeletonConfig}
        />
        {/* Add some visual elements for better skeleton appearance */}
        {skeletonType === 'chart' && (
          <div style={{ 
            marginTop: '16px', 
            height: '200px', 
            background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
            backgroundSize: '200% 100%',
            animation: 'shimmer 1.5s infinite',
            borderRadius: '4px'
          }} />
        )}
        {skeletonType === 'stats' && (
          <div style={{ 
            marginTop: '8px', 
            display: 'flex', 
            gap: '8px' 
          }}>
            {[...Array(3)].map((_, i) => (
              <div 
                key={i}
                style={{ 
                  width: '60px', 
                  height: '20px', 
                  background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                  backgroundSize: '200% 100%',
                  animation: 'shimmer 1.5s infinite',
                  borderRadius: '4px',
                  animationDelay: `${i * 0.2}s`
                }} 
              />
            ))}
          </div>
        )}
      </div>
    );

    if (showCard) {
      return (
        <Card 
          title={title}
          style={{ minHeight: height }}
          className={className}
        >
          {skeletonContent}
        </Card>
      );
    }

    return skeletonContent;
  };

  // Show skeleton while loading or not yet rendered
  if (loading || !shouldRender) {
    return (
      <div ref={elementRef} className={className} style={{ minHeight: height }}>
        {fallback || renderSkeleton()}
        <style jsx>{`
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `}</style>
      </div>
    );
  }

  // Show skeleton if not visible yet (viewport lazy loading)
  if (!isVisible) {
    return (
      <div ref={elementRef} className={className} style={{ minHeight: height }}>
        {fallback || renderSkeleton()}
        <style jsx>{`
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `}</style>
      </div>
    );
  }

  // Render the actual component
  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
});

LazyComponentWrapper.displayName = 'LazyComponentWrapper';

export default LazyComponentWrapper;
