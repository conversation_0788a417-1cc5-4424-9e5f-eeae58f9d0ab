import React from 'react';
import { Row, Col, Card, Statistic, Spin, Progress, Space, Popover } from 'antd';
import { 
  AlertOutlined, 
  WarningOutlined,
  ClockCircleOutlined, 
  ToolOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';

const ArretStatsCards = () => {
  const context = useArretQueuedContext();
  
  if (!context) {
    console.error('🚨 ArretStatsCards - No context available!');
    return <div>Context not available</div>;
  }
  
  const { 
    sidebarStats = [], 
    loading = true, 
    totalDuration = 0,
    avgDuration = 0,
    operatorStats = [],
    dateFilterActive = false,
    dateRangeDescription = ''
  } = context;

  // Calculate percentage of non-declared stops
  const percentageNonDeclared = sidebarStats.length >= 2 && sidebarStats[0]?.value > 0
    ? ((sidebarStats[1].value / sidebarStats[0].value) * 100).toFixed(1)
    : 0;

  // Build extended stats array like in original Arrets2.jsx
  const extendedStats = [
    ...sidebarStats,
    {
      title: "Durée Totale",
      value: Math.round(totalDuration),
      suffix: "min",
      icon: <ClockCircleOutlined />,
      color: "#13c2c2", // CHART_COLORS.secondary
    },
    {
      title: "Durée Moyenne", 
      value: avgDuration.toFixed(1),
      suffix: "min",
      icon: <ClockCircleOutlined />,
      color: "#52c41a", // CHART_COLORS.success
    },
    {
      title: "Interventions",
      value: operatorStats.reduce((sum, op) => sum + (op.interventions || 0), 0),
      icon: <ToolOutlined />,
      color: "#722ed1", // CHART_COLORS.purple
    },
  ];

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      {extendedStats.map((stat, index) => (
        <Col key={index} xs={24} sm={12} md={8} lg={6}>
          <Card
            bordered={false}
            hoverable
            style={{
              borderTop: `2px solid ${stat.color || '#1890ff'}`,
              height: "100%",
            }}
          >
            <Spin spinning={loading}>
              <Statistic
                title={
                  <Space>
                    {stat.icon &&
                      React.cloneElement(stat.icon, {
                        style: { color: stat.color || '#1890ff' },
                      })}
                    <span>{stat.title}</span>
                    {stat.title === "Arrêts Totaux" && dateFilterActive && (
                      <Popover content={`Nombre total d'arrêts ${dateRangeDescription}`} title="Période sélectionnée">
                        <InfoCircleOutlined style={{ color: '#1890ff', cursor: "pointer" }} />
                      </Popover>
                    )}
                  </Space>
                }
                value={stat.value || 0}
                suffix={stat.suffix}
                valueStyle={{
                  fontSize: 24,
                  color: stat.color || '#1890ff',
                }}
              />
              
              {/* Special handling for "Arrêts Non Déclarés" like in original */}
              {stat.title === "Arrêts Non Déclarés" && (
                <div style={{ marginTop: 8 }}>
                  <span style={{ color: '#666', fontSize: '14px' }}>
                    {percentageNonDeclared}% du total
                  </span>
                  <Progress
                    percent={Number.parseFloat(percentageNonDeclared)}
                    showInfo={false}
                    strokeColor="#f5222d"
                    size="small"
                  />
                </div>
              )}
            </Spin>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default ArretStatsCards;
