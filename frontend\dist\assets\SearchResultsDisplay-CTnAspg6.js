import{r as s,av as ye,al as M,a1 as Se,Y as ve,aw as ue,q as Ce,ax as Re,a2 as be,R as e,b as X,c as C,d as D,B as me,ay as xe,g as S,f as $,D as Z,e as T,A as pe,C as ke,E as oe,au as we,T as $e}from"./index-O2xm1U_Z.js";import{i as l}from"./PieChart-CJMXTNKV.js";import{d as G}from"./dayjs.min-CgAD4wBe.js";import"./fr-D5QIIXkH.js";import{s as H,a as Ie,R as Oe,b as De}from"./GlobalSearchModal-CB8P5fP4.js";import{R as K}from"./SearchOutlined-CzaKf_7S.js";import{R as Me}from"./ExperimentOutlined-D3OB_L_Y.js";import{S as ze}from"./index-DcuuzGl6.js";import{R as le}from"./FilterOutlined-B3wNZQVB.js";import{S as Pe}from"./index-fmer6zpJ.js";import{R as J}from"./CalendarOutlined-ry8TLVWh.js";import{D as Ne,b as Ye}from"./DownloadOutlined-ClmkhSDC.js";import{R as Te}from"./ReloadOutlined-EeD9QNgc.js";import{R as Ae}from"./ThunderboltOutlined-xe_XoJ1p.js";import{R as de}from"./ClockCircleOutlined-D-iaV6k8.js";import{L as U}from"./index-fv7kzFnJ.js";import{R as je}from"./EyeOutlined-DDj6D5vZ.js";import{R as _e}from"./FileTextOutlined-ZE845-EP.js";import{R as Be}from"./BarChartOutlined-CX9KDBHm.js";import{S as Q}from"./index-Dc91-n-S.js";const{Option:se}=M;function ce(a){return(a==null?void 0:a.type)&&(a.type.isSelectOption||a.type.isSelectOptGroup)}const Le=(a,p)=>{var c,d;const{prefixCls:f,className:R,popupClassName:z,dropdownClassName:A,children:j,dataSource:P,dropdownStyle:N,dropdownRender:_,popupRender:B,onDropdownVisibleChange:L,onOpenChange:I,styles:b,classNames:i}=a,h=ye(j),r=((c=b==null?void 0:b.popup)===null||c===void 0?void 0:c.root)||N,t=((d=i==null?void 0:i.popup)===null||d===void 0?void 0:d.root)||z||A,o=B||_,k=I||L;let u;h.length===1&&s.isValidElement(h[0])&&!ce(h[0])&&([u]=h);const w=u?()=>u:void 0;let g;h.length&&ce(h[0])?g=j:g=P?P.map(v=>{if(s.isValidElement(v))return v;switch(typeof v){case"string":return s.createElement(se,{key:v,value:v},v);case"object":{const{value:Y}=v;return s.createElement(se,{key:Y,value:Y},v.text)}default:return}}):[];const{getPrefixCls:x}=s.useContext(Se),y=x("select",f),[q]=ve("SelectLike",r==null?void 0:r.zIndex);return s.createElement(M,Object.assign({ref:p,suffixIcon:null},ue(a,["dataSource","dropdownClassName","popupClassName"]),{prefixCls:y,classNames:{popup:{root:t},root:i==null?void 0:i.root},styles:{popup:{root:Object.assign(Object.assign({},r),{zIndex:q})},root:b==null?void 0:b.root},className:Ce(`${y}-auto-complete`,R),mode:M.SECRET_COMBOBOX_MODE_DO_NOT_USE,popupRender:o,onOpenChange:k,getInputElement:w}),g)},fe=s.forwardRef(Le),{Option:qe}=M,Fe=Re(fe,"dropdownAlign",a=>ue(a,["visible"])),te=fe;te.Option=qe;te._InternalPanelDoNotUseOrYouWillBeFired=Fe;var He={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};function ee(){return ee=Object.assign?Object.assign.bind():function(a){for(var p=1;p<arguments.length;p++){var c=arguments[p];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a},ee.apply(this,arguments)}const Qe=(a,p)=>s.createElement(be,ee({},a,{ref:p,icon:He})),Ve=s.forwardRef(Qe),Ge=a=>{switch(a){case"day":return"DD/MM/YYYY";case"week":return"DD/MM/YYYY";case"month":return"MM/YYYY";default:return"DD/MM/YYYY"}},{Option:ie}=M,{Search:We}=xe,Je=({machineModels:a,filteredMachineNames:p,selectedMachineModel:c="",selectedMachine:d="",dateFilter:f=null,dateRangeType:R,dateFilterActive:z,handleMachineModelChange:A,handleMachineChange:j,handleDateChange:P,handleDateRangeTypeChange:N,resetFilters:_,handleRefresh:B,loading:L=!1,dataSize:I=0,estimatedLoadTime:b=0,pageType:i="production",onSearchResults:h,enableElasticsearch:r=!0})=>{const[t,o]=s.useState(""),[k,u]=s.useState([]),[w,g]=s.useState(!1),[x,y]=s.useState(null),[q,v]=s.useState(!1),[Y,re]=s.useState(!1);s.useEffect(()=>{r&&he()},[r]);const he=async()=>{try{const n=await H.checkHealth();v(n.elasticsearch.status==="healthy")}catch(n){console.warn("Elasticsearch not available:",n),v(!1)}},Ee=s.useCallback(H.createDebouncedSearch(async n=>{if(!n||n.length<2){u([]);return}try{const m=i==="production"?"machineName":"stopDescription",E=await H.getSuggestions(n,m,8);u(E.map(O=>({value:O})))}catch(m){console.error("Error getting suggestions:",m),u([])}},300),[i]),ge=n=>{o(n),q&&Ee(n)},ne=async n=>{if(!(!n.trim()||!q)){g(!0);try{const m={query:n.trim(),dateFrom:f==null?void 0:f.startDate,dateTo:f==null?void 0:f.endDate,machineId:d,machineModel:c,page:1,size:50};let E;i==="production"?E=await H.searchProductionData(m):i==="arrets"&&(E=await H.searchMachineStops(m)),y(E),re(!0),h&&h(E,n)}catch(m){console.error("Search error:",m),y(null)}finally{g(!1)}}},ae=()=>{o(""),y(null),re(!1),u([]),h&&h(null,"")},F=n=>{const m=G();let E,O;switch(n){case"today":E=m,O="day";break;case"week":E=m,O="week";break;case"month":E=m,O="month";break;case"last7days":E=m.subtract(7,"days"),O="week";break;case"last30days":E=m.subtract(30,"days"),O="month";break;default:return}N(O),P(E)},W=I>1e3?{type:"warning",message:`Attention: ${I} enregistrements à charger (temps estimé: ${b}s)`}:I>500?{type:"info",message:`${I} enregistrements à charger`}:null;return e.createElement("div",null,e.createElement(X,{gutter:[16,16]},q&&e.createElement(C,{span:24},e.createElement(D,{wrap:!0,style:{width:"100%"}},e.createElement(me,{dot:Y,color:"green"},e.createElement(te,{style:{width:300},options:k,onSearch:ge,onSelect:n=>{o(n),ne(n)},value:t,placeholder:`Rechercher ${i==="production"?"dans les données de production":"dans les arrêts"}...`},e.createElement(We,{loading:w,onSearch:ne,enterButton:e.createElement(S,{type:"primary",icon:e.createElement(K,null)},"Rechercher")}))),Y&&e.createElement(D,null,e.createElement($,{color:"green",icon:e.createElement(Me,null)},"Mode recherche actif"),e.createElement(S,{size:"small",onClick:ae},"Retour aux filtres"),x&&e.createElement($,{color:"blue"},x.total," résultat(s) trouvé(s)")),e.createElement(ze,{checkedChildren:"ES",unCheckedChildren:"SQL",checked:Y,onChange:n=>{n||ae()},title:"Basculer entre recherche Elasticsearch et filtres SQL"})),e.createElement(Z,{style:{margin:"12px 0"}})),e.createElement(C,{span:24},e.createElement(D,{wrap:!0},e.createElement(T,{title:"Filtrer par modèle de machine (optionnel - toutes les données sont affichées par défaut)"},e.createElement(M,{placeholder:"Tous les modèles",style:{width:150},value:c||void 0,onChange:A,allowClear:!0,suffixIcon:e.createElement(le,{style:{color:"#1890ff"}})},a.map(n=>e.createElement(ie,{key:n,value:n},n)))),e.createElement(M,{placeholder:"Sélectionner une machine",style:{width:150},value:d||void 0,onChange:j,disabled:!c||p.length===0,allowClear:!0},p.map(n=>e.createElement(ie,{key:n.Machine_Name,value:n.Machine_Name},n.Machine_Name))),e.createElement(Pe,{options:[{label:"Jour",value:"day",icon:e.createElement(J,null)},{label:"Semaine",value:"week",icon:e.createElement(J,null)},{label:"Mois",value:"month",icon:e.createElement(J,null)}],value:R,onChange:N}),e.createElement(Ne,{placeholder:`Sélectionner un ${R==="day"?"jour":R==="week"?"semaine":"mois"}`,format:Ge(R),value:f?G(f):null,onChange:P,picker:R==="day"?void 0:R,allowClear:!0,style:{width:180}}),e.createElement(T,{title:"Réinitialiser les filtres"},e.createElement(S,{icon:e.createElement(Ye,null),onClick:_,disabled:!c&&!d&&!z})),e.createElement(T,{title:"Rafraîchir les données"},e.createElement(S,{type:"primary",icon:e.createElement(Te,null),onClick:B,loading:L})))),e.createElement(C,{span:24},e.createElement(D,{split:e.createElement(Z,{type:"vertical"}),wrap:!0},e.createElement(D,null,e.createElement($,{icon:e.createElement(Ae,null),color:"blue"},"Filtres rapides:"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("today")},"Aujourd'hui"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("week")},"Cette semaine"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("month")},"Ce mois"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("last7days")},"7 derniers jours"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("last30days")},"30 derniers jours")),!c&&e.createElement($,{icon:e.createElement(le,null),color:"blue"},"Affichage de tous les modèles de machines"),!z&&e.createElement($,{icon:e.createElement(de,null),color:"green"},"Filtre par défaut: 7 derniers jours"))),W&&e.createElement(C,{span:24},e.createElement(pe,{message:W.message,type:W.type,showIcon:!0,closable:!0,style:{marginBottom:0}}))))};Je.propTypes={machineModels:l.array.isRequired,filteredMachineNames:l.array.isRequired,selectedMachineModel:l.string,selectedMachine:l.string,dateFilter:l.object,dateRangeType:l.string.isRequired,dateFilterActive:l.bool.isRequired,handleMachineModelChange:l.func.isRequired,handleMachineChange:l.func.isRequired,handleDateChange:l.func.isRequired,handleDateRangeTypeChange:l.func.isRequired,resetFilters:l.func.isRequired,handleRefresh:l.func.isRequired,loading:l.bool,dataSize:l.number,estimatedLoadTime:l.number,pageType:l.oneOf(["production","arrets"]),onSearchResults:l.func,enableElasticsearch:l.bool};const{Text:V,Title:yt,Paragraph:Ue}=$e,St=({results:a,searchQuery:p,pageType:c,loading:d=!1,onResultSelect:f,onPageChange:R,currentPage:z=1,pageSize:A=20})=>{const[j,P]=s.useState([]);if(!a)return null;const N=r=>{switch(r){case"production-data":return e.createElement(Be,{style:{color:"#52c41a"}});case"machine-stop":return e.createElement(Oe,{style:{color:"#ff4d4f"}});case"machine-session":return e.createElement(Ve,{style:{color:"#1890ff"}});case"report":return e.createElement(_e,{style:{color:"#722ed1"}});default:return e.createElement(K,{style:{color:"#666"}})}},_=r=>{const o={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"}}[r]||{color:"default",text:"Inconnu"};return e.createElement($,{color:o.color},o.text)},B=r=>{const{data:t,type:o}=r;switch(o){case"production-data":return`${t.machineName} - ${G(t.date).format("DD/MM/YYYY")}`;case"machine-stop":return`Arrêt ${t.machineName} - ${t.stopCode}`;case"machine-session":return`Session ${t.machineName} - ${t.sessionId}`;case"report":return t.title||`Rapport ${t.type}`;default:return"Résultat de recherche"}},L=r=>{var k,u,w,g,x,y;const{data:t,type:o}=r;switch(o){case"production-data":return`OEE: ${((u=(k=t.performance)==null?void 0:k.oee)==null?void 0:u.toFixed(1))||0}% | Production: ${((w=t.production)==null?void 0:w.good)||0} pièces | Opérateur: ${t.operator||"N/A"}`;case"machine-stop":return`${t.stopDescription} | Durée: ${t.duration||0} min | Catégorie: ${t.stopCategory||"N/A"}`;case"machine-session":return`TRS: ${((x=(g=t.performance)==null?void 0:g.trs)==null?void 0:x.toFixed(1))||0}% | Production: ${((y=t.production)==null?void 0:y.total)||0} | Opérateur: ${t.operator||"N/A"}`;case"report":return t.description||`Généré par ${t.generatedBy||"N/A"}`;default:return"Aucune description disponible"}},I=r=>{if(!r)return null;const t=Object.keys(r);return t.length===0?null:e.createElement("div",{style:{marginTop:8,padding:"8px",backgroundColor:"#f6ffed",borderRadius:"4px"}},e.createElement(V,{type:"secondary",style:{fontSize:"12px"}},e.createElement(De,null)," Correspondances trouvées:"),t.map(o=>e.createElement("div",{key:o,style:{marginTop:4}},e.createElement(V,{strong:!0,style:{fontSize:"12px",color:"#52c41a"}},o,":"),e.createElement("div",{style:{fontSize:"12px",marginLeft:8},dangerouslySetInnerHTML:{__html:r[o].join(" ... ")}}))))},b=r=>{var k,u,w,g,x,y;const{data:t,type:o}=r;return o==="production-data"?e.createElement(X,{gutter:16,style:{marginTop:8}},e.createElement(C,{span:6},e.createElement(Q,{title:"OEE",value:((k=t.performance)==null?void 0:k.oee)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(C,{span:6},e.createElement(Q,{title:"Production",value:((u=t.production)==null?void 0:u.good)||0,suffix:"pcs",valueStyle:{fontSize:"14px"}})),e.createElement(C,{span:6},e.createElement(Q,{title:"Qualité",value:((w=t.performance)==null?void 0:w.quality)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(C,{span:6},e.createElement(Q,{title:"TRS",value:((g=t.performance)==null?void 0:g.trs)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}}))):o==="machine-stop"?e.createElement(X,{gutter:16,style:{marginTop:8}},e.createElement(C,{span:8},e.createElement(Q,{title:"Durée",value:t.duration||0,suffix:"min",valueStyle:{fontSize:"14px"}})),e.createElement(C,{span:8},e.createElement($,{color:t.severity==="high"?"red":t.severity==="medium"?"orange":"green"},t.severity||"low")),e.createElement(C,{span:8},e.createElement(me,{status:(x=t.resolution)!=null&&x.resolved?"success":"error",text:(y=t.resolution)!=null&&y.resolved?"Résolu":"En cours"}))):null},i=r=>{f&&f(r)},h=r=>e.createElement(U.Item,{key:r.id,style:{padding:"16px",borderRadius:"8px",margin:"8px 0",border:"1px solid #f0f0f0",backgroundColor:"#fafafa",transition:"all 0.2s"},onMouseEnter:t=>{t.currentTarget.style.backgroundColor="#f5f5f5",t.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:t=>{t.currentTarget.style.backgroundColor="#fafafa",t.currentTarget.style.borderColor="#f0f0f0"},actions:[e.createElement(T,{title:"Voir les détails"},e.createElement(S,{type:"text",icon:e.createElement(je,null),onClick:()=>i(r)})),e.createElement(T,{title:"Exporter"},e.createElement(S,{type:"text",icon:e.createElement(Ie,null),onClick:()=>i(r)}))]},e.createElement(U.Item.Meta,{avatar:N(r.type),title:e.createElement(D,null,e.createElement(V,{strong:!0,style:{cursor:"pointer"},onClick:()=>i(r)},B(r)),_(r.type),r.score&&e.createElement(T,{title:`Score de pertinence: ${r.score.toFixed(3)}`},e.createElement($,{color:"purple",style:{fontSize:"10px"}},Math.round(r.score*100),"%"))),description:e.createElement("div",null,e.createElement(Ue,{style:{marginBottom:8}},L(r)),r.data.timestamp&&e.createElement("div",{style:{marginBottom:8}},e.createElement(de,{style:{marginRight:4}}),e.createElement(V,{type:"secondary",style:{fontSize:"12px"}},G(r.data.timestamp||r.data.date).format("DD/MM/YYYY HH:mm"))),b(r),I(r.highlight))}));return e.createElement(ke,{title:e.createElement(D,null,e.createElement(K,null),e.createElement("span",null,'Résultats de recherche pour "',p,'"'),e.createElement($,{color:"blue"},a.total," résultat(s)")),extra:e.createElement(D,null,e.createElement(S,{size:"small",type:"link"},"Exporter tous"))},a.total===0?e.createElement(oe,{description:`Aucun résultat trouvé pour "${p}"`,image:oe.PRESENTED_IMAGE_SIMPLE}):e.createElement(e.Fragment,null,e.createElement(pe,{message:`${a.total} résultat(s) trouvé(s) dans les ${c==="production"?"données de production":"arrêts de machine"}`,type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(U,{dataSource:a[c==="production"?"production":"stops"]||a.results||[],renderItem:h,loading:d,split:!1}),a.totalPages>1&&e.createElement(e.Fragment,null,e.createElement(Z,null),e.createElement("div",{style:{textAlign:"center"}},e.createElement(we,{current:z,total:a.total,pageSize:A,onChange:R,showSizeChanger:!0,showQuickJumper:!0,showTotal:(r,t)=>`${t[0]}-${t[1]} sur ${r} résultats`})))))};export{Je as F,St as S};
