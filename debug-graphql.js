#!/usr/bin/env node

/**
 * Debug script to test GraphQL endpoints and database connectivity
 * Fixed version with immediate output and better error handling
 */

// Immediate output to verify script is running
console.log('🚀 GraphQL Debug Script Starting...');
console.log('📅 Script started at:', new Date().toISOString());

import fetch from 'node-fetch';

// Test immediate output
console.log('✅ ES6 imports loaded successfully');

// Configuration
const CONFIG = {
  GRAPHQL_ENDPOINT: 'http://localhost:5000/api/graphql',
  REST_BASE_URL: 'http://localhost:5000/api',
  TIMEOUT: 5000, // 5 seconds
  RETRY_ATTEMPTS: 1, // Reduced for faster feedback
  RETRY_DELAY: 1000 // 1 second
};

console.log('⚙️  Configuration loaded:', CONFIG);

/**
 * Simple test function with immediate feedback
 */
async function testGraphQL(query, variables = {}, description = '') {
  console.log(`\n🔍 Testing: ${description}`);
  console.log(`📡 Endpoint: ${CONFIG.GRAPHQL_ENDPOINT}`);
  console.log(`📝 Query: ${query.replace(/\s+/g, ' ').trim()}`);
  
  try {
    console.log('⏳ Sending request...');
    
    const response = await fetch(CONFIG.GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    console.log(`📊 Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ HTTP Error Response:', errorText.substring(0, 500));
      return { success: false, httpError: `${response.status}: ${errorText}` };
    }

    const result = await response.json();
    console.log('📦 Raw response received');
    
    if (result.errors) {
      console.log('❌ GraphQL Errors:', JSON.stringify(result.errors, null, 2));
      return { success: false, errors: result.errors };
    }

    console.log('✅ Success! Data received:');
    console.log(JSON.stringify(result.data, null, 2));
    return { success: true, data: result.data };
    
  } catch (error) {
    console.log('❌ Network/Parse Error:', error.message);
    console.log('🔍 Error details:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Simple REST API test function
 */
async function testRESTAPI(endpoint, description = '') {
  console.log(`\n🌐 Testing REST API: ${description}`);
  console.log(`📡 Endpoint: ${endpoint}`);
  
  try {
    console.log('⏳ Sending GET request...');
    
    const response = await fetch(endpoint);
    console.log(`📊 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ HTTP Error Response:', errorText.substring(0, 500));
      return { success: false, httpError: `${response.status}: ${errorText}` };
    }

    const result = await response.json();
    console.log('✅ Success! Data received:');
    console.log(JSON.stringify(result, null, 2));
    return { success: true, data: result };
    
  } catch (error) {
    console.log('❌ Network/Parse Error:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Main test runner - simplified for immediate feedback
 */
async function runTests() {
  console.log('\n' + '='.repeat(50));
  console.log('🎯 STARTING SIMPLIFIED TESTS');
  console.log('='.repeat(50));

  try {
    // Test 1: Basic connectivity
    console.log('\n1️⃣  BASIC CONNECTIVITY TEST');
    await testGraphQL(
      'query { __typename }',
      {},
      'Basic GraphQL Connectivity'
    );

    // Test 2: Simple health check via REST
    console.log('\n2️⃣  REST API HEALTH CHECK');
    await testRESTAPI(
      `${CONFIG.REST_BASE_URL}/health`,
      'Health Check Endpoint'
    );

    // Test 3: Your specific GraphQL queries
    console.log('\n3️⃣  STOP SIDECARDS QUERY');
    await testGraphQL(
      'query { getStopSidecards { Arret_Totale Arret_Totale_nondeclare } }',
      {},
      'Stop Sidecards Query'
    );

    console.log('\n4️⃣  PRODUCTION DASHBOARD QUERY');
    await testGraphQL(
      'query { getDashboardData { productionChart { Date_Insert_Day Total_Good_Qty_Day } sidecards { goodqty rejetqty } } }',
      {},
      'Production Dashboard Query'
    );

    // Test 4: Working REST comparison
    console.log('\n5️⃣  REST API COMPARISON');
    await testRESTAPI(
      `${CONFIG.REST_BASE_URL}/sidecards-arret`,
      'Working REST API (Arrets2)'
    );

  } catch (error) {
    console.log('\n❌ CRITICAL ERROR in test runner:', error);
  }

  console.log('\n' + '='.repeat(50));
  console.log('✅ ALL TESTS COMPLETED');
  console.log('='.repeat(50));
}

// Add immediate execution check
console.log('🔄 Setting up script execution...');

// Simple error handlers
process.on('unhandledRejection', (reason) => {
  console.error('❌ Unhandled Rejection:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

// Run the tests immediately
console.log('▶️  Starting tests now...');
runTests().catch((error) => {
  console.error('❌ Script execution failed:', error);
  console.error('🔍 Error stack:', error.stack);
});