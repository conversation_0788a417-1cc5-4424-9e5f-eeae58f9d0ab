import React from "react" ;
import { useEffect, useState } from 'react';
import { Table, Card, Row, Col, Statistic, DatePicker, Select } from 'antd';
import { StopOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>hart, Bar, PieChart, Pie, Cell, LineChart, Line, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import request from 'superagent';
import dayjs from 'dayjs';

const MachineStopsDashboard = () => {
  const [stopsData, setStopsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dateFilter, setDateFilter] = useState(dayjs());
  const [chartData, setChartData] = useState({
    stopReasons: [],
    durationTrend: [],
    machineComparison: [],
    operatorStats: []
  });
  const baseURL = (() => {
    if (typeof window !== 'undefined') {
      const currentOrigin = window.location.origin;

      // If running on ngrok domain, use the same origin
      if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
        return currentOrigin;
      }

      // For local development, check environment variable first
      if (import.meta.env.VITE_API_URL) {
        return import.meta.env.VITE_API_URL;
      }

      return currentOrigin;
    }
    return "http://localhost:5000";
  })();

  // Add these new API calls
  const fetchChartData = async (date) => {
    try {
      const formattedDate = date.format('YYYY-MM-DD');
      const [reasonsRes, trendRes, machinesRes, operatorsRes] = await Promise.all([
        request.get(`${baseURL}/api/stop-reasons/${formattedDate}`).retry(2).withCredentials(),
        request.get(`${baseURL}/api/stop-duration-trend/${formattedDate}`).retry(2).withCredentials(),
        request.get(`${baseURL}/api/machine-stop-comparison/${formattedDate}`).retry(2).withCredentials(),
        request.get(`${baseURL}/api/operator-stop-stats/${formattedDate}`).retry(2).withCredentials()
      ]);

      setChartData({
        stopReasons: reasonsRes.body,
        durationTrend: trendRes.body,
        machineComparison: machinesRes.body,
        operatorStats: operatorsRes.body
      });
    } catch (error) {
      console.error('Error fetching chart data:', error);
    }
  };

  const fetchStopsData = async (date) => {
    try {
      if (!date) {
        console.error('Error: date is null or undefined');
        return;
      }

      setLoading(true);
      const formattedDate = date.format('YYYY-MM-DD');

      const [stopsRes] = await Promise.all([
        request.get(`${baseURL}/api/arrets-table/${formattedDate}`).retry(2).withCredentials(),
        fetchChartData(date)
      ]);
  console.log(stopsRes.body);
      setStopsData(stopsRes.body);
    } catch (error) {
      console.error('Error fetching stops data:', error);
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    fetchStopsData(dateFilter);
  }, [dateFilter]);

  // New chart components
  const renderStopReasonsChart = () => (
    <Card title="Distribution des Raisons d'Arrêt">
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData.stopReasons}
            dataKey="count"
            nameKey="reason"
            cx="50%"
            cy="50%"
            outerRadius={80}
            label
          >
            {chartData.stopReasons.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={`hsl(${index * 60}, 70%, 50%)`} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </Card>
  );

  const renderDurationTrendChart = () => (
    <Card title="Évolution de la Durée des Arrêts">
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={chartData.durationTrend}>
          <XAxis dataKey="hour" />
          <YAxis unit="min" />
          <Tooltip />
          <Legend />
          <Line
            type="monotone"
            dataKey="avgDuration"
            stroke="#ff7300"
            strokeWidth={2}
            name="Durée Moyenne"
          />
        </LineChart>
      </ResponsiveContainer>
    </Card>
  );

  const renderMachineComparisonChart = () => (
    <Card title="Arrêts par Machine">
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData.machineComparison}>
          <XAxis dataKey="machine" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="stops" fill="#8884d8" name="Nombre d'Arrêts" />
          <Bar dataKey="totalDuration" fill="#82ca9d" name="Durée Totale (min)" />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );

  const renderOperatorStats = () => (
    <Card title="Interventions par Opérateur">
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData.operatorStats}>
          <XAxis dataKey="operator" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="interventions" fill="#413ea0" name="Interventions" />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );

  // Table columns
  const columns = [
    { title: 'Machine', dataIndex: 'Machine_Name', key: 'Machine_Name' },
    {
      title: 'Date Arrêt',
      dataIndex: 'Date_Insert',
      key: 'Date_Insert',
      render: (text) => text ? dayjs(text, 'DD/MM/YYYY HH:mm').format('DD/MM/YYYY HH:mm') : 'N/A'
    },
    {
      title: 'Durée (min)',
      key: 'Duree',
      render: (_, record) => {
        if (!record.Debut_Stop || !record.Fin_Stop_Time) return 'N/A';
        const start = dayjs(record.Debut_Stop, 'DD/MM/YYYY HH:mm');
        const end = dayjs(record.Fin_Stop_Time, 'DD/MM/YYYY HH:mm');
        return end.diff(start, 'minute');
      }
    },
    { title: 'Raison', dataIndex: 'Code_Stop', key: 'Code_Stop' },
    { title: 'OF', dataIndex: 'Part_NO', key: 'Part_NO' },
    { title: 'Responsable', dataIndex: 'Regleur_Prenom', key: 'Regleur_Prenom' }
  ];

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card title="Arrêts Totaux">
            <Statistic
              value={stopsData.length}
              prefix={<StopOutlined />}
              valueStyle={{ color: '#cf1322', fontSize: 32 }}
            />
          </Card>
        </Col>
        <Col span={16}>
          <Card
            title="Filtre par date"
            extra={
              <DatePicker
                defaultValue={dateFilter}
                onChange={setDateFilter}
                format="DD/MM/YYYY"
                style={{ width: 200 }}
              />
            }
          />
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>{renderStopReasonsChart()}</Col>
        <Col span={12}>{renderDurationTrendChart()}</Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={12}>{renderMachineComparisonChart()}</Col>
        <Col span={12}>{renderOperatorStats()}</Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="Détails des Arrêts">
            <Table
              columns={columns}
              dataSource={stopsData}
              loading={loading}
              rowKey={record => `${record.Machine_Name || 'Unknown'}-${record.Date_Insert || 'Unknown'}-${record.Debut_Stop || 'Unknown'}`}
              scroll={{ x: 1300 }}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default MachineStopsDashboard;