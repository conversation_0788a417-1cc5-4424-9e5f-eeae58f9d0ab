// Debug test: Check the actual data flow to ArretLineChart
import React from 'react';

// Test component to debug the chart data
const ChartDebugTest = () => {
  // Sample data that should be passed to the chart
  const testChartData = [
    { date: '2024-01-01', displayDate: '01/01', stops: 2 },
    { date: '2024-01-02', displayDate: '02/01', stops: 2 },
    { date: '2024-01-03', displayDate: '03/01', stops: 1 },
    { date: '2024-01-04', displayDate: '04/01', stops: 1 },
    { date: '2024-01-05', displayDate: '05/01', stops: 1 }
  ];

  console.log('📊 Test Chart Data:', testChartData);

  // Check if the data format is correct
  const hasEvolutionData = Array.isArray(testChartData) && testChartData.length > 0 && testChartData[0].date;
  console.log('✅ Has evolution data:', hasEvolutionData);
  
  // Check displayDate field
  const hasDisplayDate = testChartData.every(item => item.displayDate);
  console.log('✅ Has displayDate:', hasDisplayDate);

  // Check stops field
  const hasStops = testChartData.every(item => typeof item.stops === 'number');
  console.log('✅ Has stops:', hasStops);

  return (
    <div>
      <h3>Chart Debug Test</h3>
      <pre>{JSON.stringify(testChartData, null, 2)}</pre>
    </div>
  );
};

export default ChartDebugTest;
