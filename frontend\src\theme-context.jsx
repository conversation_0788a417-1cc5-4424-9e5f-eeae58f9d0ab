import React from "react";

import  { createContext, useContext, useState, useEffect, useMemo } from "react";
import { theme as antdTheme } from "antd";
import SOMIPEM_COLORS from "./styles/brand-colors";

/**
 * Theme context for managing application theme
 * @type {React.Context}
 */
const ThemeContext = createContext();

/**
 * Available theme modes
 * @type {Object}
 */
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
};

/**
 * Theme provider component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {JSX.Element} Theme provider
 */
export const ThemeProvider = ({ children }) => {
  // Get initial theme preference from localStorage or default to system
  const [themePreference, setThemePreference] = useState(() => {
    if (typeof window === 'undefined') return THEME_MODES.LIGHT;

    const savedPreference = localStorage.getItem('themePreference');
    return savedPreference || THEME_MODES.SYSTEM;
  });


  
  // Track system preference separately
  const [systemIsDark, setSystemIsDark] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia?.('(prefers-color-scheme: dark)').matches || false;
  });
  
  // Compute actual dark mode based on preference and system
  const darkMode = useMemo(() => {
    if (themePreference === THEME_MODES.SYSTEM) {
      return systemIsDark;
    }
    return themePreference === THEME_MODES.DARK;
  }, [themePreference, systemIsDark]);
  
  // Generate Ant Design theme token overrides with SOMIPEM brand colors
  const themeTokens = useMemo(() => ({
    colorPrimary: SOMIPEM_COLORS.PRIMARY_BLUE,
    borderRadius: 6,
    colorSuccess: SOMIPEM_COLORS.SUCCESS,
    colorWarning: SOMIPEM_COLORS.WARNING,
    colorError: SOMIPEM_COLORS.ERROR,
    colorInfo: SOMIPEM_COLORS.SECONDARY_BLUE,
    fontFamily: "'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    fontSize: 14,
    colorText: darkMode ? SOMIPEM_COLORS.DARK.TEXT : SOMIPEM_COLORS.DARK_GRAY,
    colorTextSecondary: darkMode ? SOMIPEM_COLORS.DARK.TEXT_SECONDARY : SOMIPEM_COLORS.LIGHT_GRAY,
    colorBgContainer: darkMode ? SOMIPEM_COLORS.DARK.CARD_BG : SOMIPEM_COLORS.WHITE,
    colorBgElevated: darkMode ? SOMIPEM_COLORS.DARK.BACKGROUND : SOMIPEM_COLORS.WHITE,
    colorBorder: darkMode ? SOMIPEM_COLORS.DARK.BORDER : SOMIPEM_COLORS.ACCENT_BORDER,
  }), [darkMode]);
  
  // Generate complete theme config for Ant Design with SOMIPEM branding
  const antdThemeConfig = useMemo(() => ({
    algorithm: darkMode ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
    token: themeTokens,
    components: {
      Button: {
        borderRadius: 6,
        colorPrimary: SOMIPEM_COLORS.PRIMARY_BLUE,
        colorPrimaryHover: SOMIPEM_COLORS.SECONDARY_BLUE,
        fontWeight: 500,
      },
      Card: {
        borderRadius: 8,
        boxShadowTertiary: darkMode 
          ? '0 2px 8px rgba(0,0,0,0.3)' 
          : '0 2px 8px rgba(30, 58, 138, 0.08)',
      },
      Table: {
        borderRadius: 8,
        headerBg: darkMode ? SOMIPEM_COLORS.DARK.CARD_BG : '#F8FAFC',
        headerColor: darkMode ? SOMIPEM_COLORS.DARK.TEXT : SOMIPEM_COLORS.DARK_GRAY,
      },
      Menu: {
        itemSelectedBg: darkMode 
          ? `rgba(59, 130, 246, 0.2)` 
          : SOMIPEM_COLORS.SELECTED_BG,
        itemSelectedColor: darkMode 
          ? SOMIPEM_COLORS.DARK.PRIMARY_BLUE 
          : SOMIPEM_COLORS.PRIMARY_BLUE,
        itemHoverBg: SOMIPEM_COLORS.HOVER_BLUE,
      },
      Tabs: {
        inkBarColor: SOMIPEM_COLORS.PRIMARY_BLUE,
        itemSelectedColor: SOMIPEM_COLORS.PRIMARY_BLUE,
        itemHoverColor: SOMIPEM_COLORS.SECONDARY_BLUE,
      },
      Progress: {
        defaultColor: SOMIPEM_COLORS.PRIMARY_BLUE,
      },
    },
  }), [darkMode, themeTokens]);
  
  // Apply theme changes to document and localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Save preference to localStorage
    localStorage.setItem('themePreference', themePreference);
    
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', darkMode ? THEME_MODES.DARK : THEME_MODES.LIGHT);
    
    // Update body background color
    document.body.style.backgroundColor = darkMode ? '#141414' : '#f0f2f5';
    
    // Add/remove dark class for CSS selectors
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode, themePreference]);
  
  // Listen for system preference changes
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e) => {
      setSystemIsDark(e.matches);
    };
    
    // Add listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener?.(handleChange);
    }
    
    // Cleanup
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        mediaQuery.removeListener?.(handleChange);
      }
    };
  }, []);
  
  /**
   * Toggle between light and dark mode
   */
  const toggleDarkMode = () => {
    setThemePreference(prev => {
      if (prev === THEME_MODES.SYSTEM) {
        return systemIsDark ? THEME_MODES.LIGHT : THEME_MODES.DARK;
      }
      return prev === THEME_MODES.DARK ? THEME_MODES.LIGHT : THEME_MODES.DARK;
    });
  };
  
  /**
   * Set theme preference explicitly
   * @param {string} mode - Theme mode from THEME_MODES
   */
  const setThemeMode = (mode) => {
    if (Object.values(THEME_MODES).includes(mode)) {
      setThemePreference(mode);
    } else {
      console.warn(`Invalid theme mode: ${mode}. Using system preference.`);
      setThemePreference(THEME_MODES.SYSTEM);
    }
  };
  
  /**
   * Reset theme to system preference
   */
  const resetTheme = () => {
    setThemePreference(THEME_MODES.SYSTEM);
  };
  
  // Context value
  const value = useMemo(() => ({
    darkMode,
    themePreference,
    systemIsDark,
    toggleDarkMode,
    setThemeMode,
    resetTheme,
    antdThemeConfig,
    themeTokens,
  }), [darkMode, themePreference, systemIsDark, antdThemeConfig, themeTokens]);
  
  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

/**
 * Hook to access theme context
 * @returns {Object} Theme context value
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};
