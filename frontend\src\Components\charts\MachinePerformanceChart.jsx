import React, { memo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON> as RechartsTooltip,
  Bar
} from "recharts";

// Palette de couleurs améliorée
const COLORS = [
  "#1890ff", // bleu
  "#13c2c2", // cyan
  "#52c41a", // vert
  "#faad14", // jaune
  "#f5222d", // rouge
  "#722ed1", // violet
  "#eb2f96", // rose
  "#fa8c16", // orange
  "#a0d911", // lime
  "#096dd9", // bleu foncé
];

/**
 * Chart component for machine performance
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @returns {JSX.Element} - Rendered chart component
 */
const MachinePerformanceChart = memo(({ data }) => {
  // Aggregate data by machine name
  const aggregatedData = data.reduce((acc, item) => {
    const machineName = item.Machine_Name
    if (!acc[machineName]) {
      acc[machineName] = {
        Machine_Name: machineName,
        good: 0,
        reject: 0,
      }
    }

    acc[machineName].good += Number(item.good) || Number(item.Good_QTY_Day) || 0
    acc[machineName].reject += Number(item.reject) || Number(item.Rejects_QTY_Day) || 0

    return acc
  }, {})

  // Convert to array for the chart
  const chartData = Object.values(aggregatedData)

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="Machine_Name" tick={{ fill: "#666" }} interval={0} angle={-45} textAnchor="end" height={80} />
        <YAxis
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Quantité",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value, name) => {
            // Check if value is a number before using toFixed
            const isNumber = typeof value === "number" && !isNaN(value)
            const formattedValue = isNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value

            const labels = {
              good: "Production",
              reject: "Rejets (kg)",
            }

            return [formattedValue, labels[name] || name]
          }}
        />

        <Bar dataKey="good" name="good" fill={COLORS[0]} stackId="a" />
        <Bar dataKey="reject" name="reject" fill={COLORS[4]} stackId="a" />
      </BarChart>
    </ResponsiveContainer>
  )
});

MachinePerformanceChart.displayName = 'MachinePerformanceChart';

export default MachinePerformanceChart;
