import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Toolt<PERSON>,
  Legend,
  Filler,
  LineController,
  BarController
} from 'chart.js';
import zoomPlugin from 'chartjs-plugin-zoom';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { SOMIPEM_COLORS } from '../styles/brand-colors';

// Register Chart.js components and plugins
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  LineController,
  BarController,
  zoomPlugin,
  ChartDataLabels
);

/**
 * Enhanced Chart Configuration Generator
 * Integrates all chart settings for immediate effects
 */
export class EnhancedChartConfig {
  constructor(settings = {}) {
    this.settings = settings;
    this.charts = settings.charts || {};
    this.theme = settings.theme || {};
  }

  /**
   * Get comprehensive chart configuration based on settings
   */
  getChartConfig(chartType = 'bar', customOptions = {}) {
    const baseConfig = this.getBaseConfig();
    const layoutConfig = this.getLayoutConfig();
    const dataDisplayConfig = this.getDataDisplayConfig();
    const interactionConfig = this.getInteractionConfig();
    const performanceConfig = this.getPerformanceConfig();

    return {
      type: chartType, // defaultType setting removed
      options: {
        ...baseConfig,
        ...layoutConfig,
        ...dataDisplayConfig,
        ...interactionConfig,
        ...performanceConfig,
        ...customOptions
      }
    };
  }

  /**
   * Base configuration (existing functionality)
   */
  getBaseConfig() {
    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: this.getAnimationConfig(),
      plugins: {
        legend: {
          display: this.charts.showLegend !== false,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12
            }
          }
        },
        tooltip: this.getTooltipConfig(),
        datalabels: this.getDataLabelsConfig()
      },
      scales: this.getScalesConfig()
    };
  }

  /**
   * Layout & Size Configuration (NEW)
   */
  getLayoutConfig() {
    const layout = this.charts.layout || {};
    
    return {
      layout: {
        padding: this.getLayoutPadding(layout.marginSize)
      },
      aspectRatio: this.getAspectRatio(layout.aspectRatio),
      // Height is handled by container, not Chart.js options
    };
  }

  /**
   * Data Display Configuration (NEW)
   */
  getDataDisplayConfig() {
    const dataDisplay = this.charts.dataDisplay || {};
    
    return {
      scales: {
        ...this.getScalesConfig(),
        y: {
          ...this.getScalesConfig().y,
          beginAtZero: dataDisplay.zeroBased !== false,
          grid: {
            display: dataDisplay.gridLines !== false,
            color: this.theme.darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          ...this.getScalesConfig().x,
          grid: {
            display: dataDisplay.gridLines !== false,
            color: this.theme.darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      elements: {
        point: {
          radius: dataDisplay.showDataPoints !== false ? 4 : 0,
          hoverRadius: dataDisplay.showDataPoints !== false ? 6 : 0
        }
      }
    };
  }

  /**
   * Interaction Configuration (NEW)
   */
  getInteractionConfig() {
    const interaction = this.charts.interaction || {};
    
    return {
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        zoom: {
          zoom: {
            wheel: {
              enabled: interaction.enableZoom !== false
            },
            pinch: {
              enabled: interaction.enableZoom !== false
            },
            mode: 'x'
          },
          pan: {
            enabled: interaction.enableZoom !== false,
            mode: 'x'
          }
        }
      },
      onHover: interaction.hoverEffects !== false ? this.getHoverHandler() : undefined
    };
  }

  /**
   * Performance Configuration (Enhanced)
   */
  getPerformanceConfig() {
    const performanceMode = this.charts.performanceMode || false;
    
    return {
      animation: performanceMode ? false : this.getAnimationConfig(),
      parsing: {
        xAxisKey: 'x',
        yAxisKey: 'y'
      },
      normalized: performanceMode,
      spanGaps: true,
      elements: {
        point: {
          radius: performanceMode ? 0 : (this.charts.dataDisplay?.showDataPoints !== false ? 4 : 0)
        }
      }
    };
  }

  /**
   * Animation Configuration
   */
  getAnimationConfig() {
    const animationsEnabled = this.theme.animationsEnabled && this.theme.chartAnimations;
    
    if (!animationsEnabled) {
      return false;
    }

    return {
      duration: 750,
      easing: 'easeInOutQuart',
      delay: (context) => {
        let delay = 0;
        if (context.type === 'data' && context.mode === 'default') {
          delay = context.dataIndex * 50 + context.datasetIndex * 100;
        }
        return delay;
      }
    };
  }

  /**
   * Tooltip Configuration
   */
  getTooltipConfig() {
    const tooltipStyle = this.charts.interaction?.tooltipStyle || 'standard';
    
    const baseTooltip = {
      backgroundColor: this.theme.darkMode ? 'rgba(33, 33, 33, 0.95)' : 'rgba(255, 255, 255, 0.95)',
      titleColor: this.theme.darkMode ? '#ffffff' : '#000000',
      bodyColor: this.theme.darkMode ? '#ffffff' : '#000000',
      borderColor: this.theme.darkMode ? '#404040' : '#d9d9d9',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      padding: 12
    };

    switch (tooltipStyle) {
      case 'minimal':
        return {
          ...baseTooltip,
          displayColors: false,
          padding: 8,
          callbacks: {
            title: () => '',
            label: (context) => `${context.parsed.y}`
          }
        };
      
      case 'detailed':
        return {
          ...baseTooltip,
          padding: 16,
          callbacks: {
            afterBody: (tooltipItems) => {
              const item = tooltipItems[0];
              return [
                '',
                `Dataset: ${item.dataset.label}`,
                `Index: ${item.dataIndex}`,
                `Total Items: ${item.dataset.data.length}`
              ];
            }
          }
        };
      
      default: // standard
        return baseTooltip;
    }
  }

  /**
   * Data Labels Configuration (NEW)
   */
  getDataLabelsConfig() {
    const showDataLabels = this.charts.dataDisplay?.showDataLabels || false;
    
    if (!showDataLabels) {
      return {
        display: false
      };
    }

    return {
      display: true,
      color: this.theme.darkMode ? '#ffffff' : '#000000',
      font: {
        size: 11,
        weight: 'bold'
      },
      formatter: (value, context) => {
        if (typeof value === 'number') {
          return value.toLocaleString();
        }
        return value;
      },
      anchor: 'end',
      align: 'top',
      offset: 4
    };
  }

  /**
   * Scales Configuration
   */
  getScalesConfig() {
    const textColor = this.theme.darkMode ? '#ffffff' : '#666666';
    
    return {
      x: {
        ticks: {
          color: textColor,
          font: {
            size: 11
          }
        },
        title: {
          display: false
        }
      },
      y: {
        ticks: {
          color: textColor,
          font: {
            size: 11
          }
        },
        title: {
          display: false
        }
      }
    };
  }

  /**
   * Layout Padding based on margin size setting
   */
  getLayoutPadding(marginSize = 'standard') {
    switch (marginSize) {
      case 'compact':
        return { top: 10, right: 10, bottom: 10, left: 10 };
      case 'spacious':
        return { top: 30, right: 30, bottom: 30, left: 30 };
      default: // standard
        return { top: 20, right: 20, bottom: 20, left: 20 };
    }
  }

  /**
   * Aspect Ratio based on setting
   */
  getAspectRatio(aspectRatio = 'auto') {
    switch (aspectRatio) {
      case '16:9':
        return 16/9;
      case '4:3':
        return 4/3;
      case '1:1':
        return 1;
      default: // auto
        return undefined; // Let Chart.js determine
    }
  }

  /**
   * Hover Handler for interaction effects
   */
  getHoverHandler() {
    return (event, activeElements, chart) => {
      if (activeElements.length > 0) {
        chart.canvas.style.cursor = 'pointer';
      } else {
        chart.canvas.style.cursor = 'default';
      }
    };
  }

  /**
   * Get chart height based on settings
   */
  getChartHeight() {
    const layout = this.charts.layout || {};
    let height = layout.defaultHeight || 300;
    
    // Apply compact mode adjustment
    if (layout.compactMode) {
      height = Math.max(200, height * 0.8);
    }
    
    return height;
  }

  /**
   * Get color scheme based on settings
   */
  getColorScheme() {
    const scheme = this.charts.colorScheme || 'somipem';
    
    switch (scheme) {
      case 'blue':
        return ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff'];
      case 'green':
        return ['#52c41a', '#73d13d', '#95de64', '#b7eb8f', '#d9f7be'];
      case 'red':
        return ['#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7', '#ffe1e1'];
      default: // somipem
        return [
          SOMIPEM_COLORS.PRIMARY_BLUE,
          SOMIPEM_COLORS.SECONDARY_BLUE,
          SOMIPEM_COLORS.CHART_TERTIARY,
          SOMIPEM_COLORS.SUCCESS_GREEN,
          SOMIPEM_COLORS.WARNING_ORANGE
        ];
    }
  }
}

/**
 * Hook to get enhanced chart configuration
 */
export const useEnhancedChartConfig = (settings, chartType = 'bar', customOptions = {}) => {
  const configGenerator = new EnhancedChartConfig(settings);
  return configGenerator.getChartConfig(chartType, customOptions);
};

/**
 * Utility function to apply settings to existing chart options
 */
export const applySettingsToChartOptions = (baseOptions, settings) => {
  const configGenerator = new EnhancedChartConfig(settings);
  const enhancedConfig = configGenerator.getChartConfig();
  
  return {
    ...baseOptions,
    ...enhancedConfig.options
  };
};

export default EnhancedChartConfig;
