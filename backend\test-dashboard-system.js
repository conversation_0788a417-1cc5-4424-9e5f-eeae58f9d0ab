/**
 * Dashboard Data Testing Script
 * 
 * Tests the enhanced dashboard data system including Elasticsearch integration,
 * GraphQL resolvers, and data accuracy validation.
 */

const dashboardDataIndexer = require('./services/dashboardDataIndexer');
const elasticsearchDashboardService = require('./services/elasticsearchDashboardService');
const unifiedDashboardResolvers = require('./routes/graphql/unifiedDashboardResolvers');
const { executeQuery } = require('./db');

class DashboardDataTester {
  constructor() {
    this.testResults = {
      indexing: null,
      elasticsearch: null,
      graphql: null,
      dataAccuracy: null,
      performance: null
    };
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Dashboard Data System Tests...\n');

    try {
      // Test 1: Indexing
      await this.testIndexing();
      
      // Test 2: Elasticsearch Queries
      await this.testElasticsearchQueries();
      
      // Test 3: GraphQL Resolvers
      await this.testGraphQLResolvers();
      
      // Test 4: Data Accuracy
      await this.testDataAccuracy();
      
      // Test 5: Performance
      await this.testPerformance();
      
      // Summary
      this.printTestSummary();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Test data indexing
   */
  async testIndexing() {
    console.log('📥 Testing Data Indexing...');
    
    try {
      // Create index mapping
      await dashboardDataIndexer.createIndexMapping();
      console.log('✅ Index mapping created');
      
      // Index some test data
      const indexResult = await dashboardDataIndexer.indexRecentData(24); // Last 24 hours
      console.log('📊 Index result:', indexResult);
      
      // Check index health
      const health = await dashboardDataIndexer.checkIndexHealth();
      console.log('🏥 Index health:', health);
      
      this.testResults.indexing = {
        success: true,
        indexed: indexResult.indexed,
        documentCount: health.documentCount,
        status: health.status
      };
      
      console.log('✅ Indexing tests passed\n');
      
    } catch (error) {
      console.error('❌ Indexing tests failed:', error);
      this.testResults.indexing = {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test Elasticsearch queries
   */
  async testElasticsearchQueries() {
    console.log('🔍 Testing Elasticsearch Queries...');
    
    try {
      const testFilters = {
        startDate: '2024-09-01',
        endDate: '2024-12-31'
      };
      
      // Test essential stats
      const stats = await elasticsearchDashboardService.getEssentialStats(testFilters);
      console.log('📊 Essential stats:', {
        totalStops: stats.totalStops,
        uniqueMachines: stats.uniqueMachines,
        categoriesCount: stats.stopsByCategory.length
      });
      
      // Test top stop reasons
      const topStops = await elasticsearchDashboardService.getTopStopReasons(testFilters, 5);
      console.log('🔝 Top stops count:', topStops.length);
      
      // Test machine comparison
      const machines = await elasticsearchDashboardService.getMachineComparison(testFilters);
      console.log('🏭 Machines found:', machines.length);
      
      // Test evolution data
      const evolution = await elasticsearchDashboardService.getEvolutionData(testFilters, 'day');
      console.log('📈 Evolution data points:', evolution.length);
      
      this.testResults.elasticsearch = {
        success: true,
        stats,
        topStopsCount: topStops.length,
        machinesCount: machines.length,
        evolutionPoints: evolution.length
      };
      
      console.log('✅ Elasticsearch query tests passed\n');
      
    } catch (error) {
      console.error('❌ Elasticsearch query tests failed:', error);
      this.testResults.elasticsearch = {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test GraphQL resolvers
   */
  async testGraphQLResolvers() {
    console.log('🔗 Testing GraphQL Resolvers...');
    
    try {
      const testFilters = {
        startDate: '2024-09-01',
        endDate: '2024-12-31'
      };
      
      // Test data source selection
      const dataSource = await unifiedDashboardResolvers.selectDataSource();
      console.log('📊 Selected data source:', dataSource);
      
      // Test dashboard stats resolver
      const statsResult = await unifiedDashboardResolvers.getDashboardStats(null, { filters: testFilters });
      console.log('📈 Dashboard stats:', {
        totalStops: statsResult.totalStops,
        dataSource: statsResult.dataSource
      });
      
      // Test top stops resolver
      const topStopsResult = await unifiedDashboardResolvers.getTop5Stops(null, { filters: testFilters });
      console.log('🔝 Top stops:', {
        count: topStopsResult.reasons?.length || 0,
        dataSource: topStopsResult.dataSource
      });
      
      // Test machine comparison resolver
      const comparisonResult = await unifiedDashboardResolvers.getMachineStopComparison(null, { filters: testFilters });
      console.log('🏭 Machine comparison:', {
        count: comparisonResult.machines?.length || 0,
        dataSource: comparisonResult.dataSource
      });
      
      // Test data source status
      const statusResult = await unifiedDashboardResolvers.getDataSourceStatus();
      console.log('🔍 Data source status:', statusResult.primarySource);
      
      this.testResults.graphql = {
        success: true,
        dataSource,
        statsDataSource: statsResult.dataSource,
        topStopsDataSource: topStopsResult.dataSource,
        comparisonDataSource: comparisonResult.dataSource,
        primarySource: statusResult.primarySource
      };
      
      console.log('✅ GraphQL resolver tests passed\n');
      
    } catch (error) {
      console.error('❌ GraphQL resolver tests failed:', error);
      this.testResults.graphql = {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test data accuracy by comparing sources
   */
  async testDataAccuracy() {
    console.log('🎯 Testing Data Accuracy...');
    
    try {
      // Get data from MySQL directly
      const mysqlQuery = `
        SELECT COUNT(*) as total_stops, 
               COUNT(DISTINCT machine_id) as unique_machines
        FROM machine_stop_table_mould 
        WHERE Date_Insert >= '2024-09-01' AND Date_Insert <= '2024-12-31'
      `;
      const mysqlResult = await executeQuery(mysqlQuery);
      const mysqlData = mysqlResult[0];
      
      console.log('🗄️ MySQL data:', mysqlData);
      
      // Get data from Elasticsearch
      const esData = await elasticsearchDashboardService.getEssentialStats({
        startDate: '2024-09-01',
        endDate: '2024-12-31'
      });
      
      console.log('🔍 Elasticsearch data:', {
        totalStops: esData.totalStops,
        uniqueMachines: esData.uniqueMachines
      });
      
      // Compare results
      const stopsMatch = Math.abs(mysqlData.total_stops - esData.totalStops) <= 1; // Allow 1 difference
      const machinesMatch = mysqlData.unique_machines === esData.uniqueMachines;
      
      console.log('📊 Comparison:', {
        stopsMatch,
        machinesMatch,
        mysqlStops: mysqlData.total_stops,
        esStops: esData.totalStops,
        mysqlMachines: mysqlData.unique_machines,
        esMachines: esData.uniqueMachines
      });
      
      this.testResults.dataAccuracy = {
        success: stopsMatch && machinesMatch,
        stopsMatch,
        machinesMatch,
        mysqlData,
        elasticsearchData: {
          totalStops: esData.totalStops,
          uniqueMachines: esData.uniqueMachines
        }
      };
      
      if (stopsMatch && machinesMatch) {
        console.log('✅ Data accuracy tests passed\n');
      } else {
        console.log('⚠️ Data accuracy tests have discrepancies\n');
      }
      
    } catch (error) {
      console.error('❌ Data accuracy tests failed:', error);
      this.testResults.dataAccuracy = {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Test performance comparison
   */
  async testPerformance() {
    console.log('⚡ Testing Performance...');
    
    try {
      const testFilters = {
        startDate: '2024-09-01',
        endDate: '2024-12-31'
      };
      
      // Test Elasticsearch performance
      const esStartTime = Date.now();
      const esResult = await elasticsearchDashboardService.getEssentialStats(testFilters);
      const esTime = Date.now() - esStartTime;
      
      // Test GraphQL resolver performance (will use Elasticsearch if available)
      const graphqlStartTime = Date.now();
      const graphqlResult = await unifiedDashboardResolvers.getDashboardStats(null, { filters: testFilters });
      const graphqlTime = Date.now() - graphqlStartTime;
      
      console.log('⏱️ Performance results:', {
        elasticsearch: `${esTime}ms`,
        graphqlResolver: `${graphqlTime}ms`,
        graphqlDataSource: graphqlResult.dataSource
      });
      
      this.testResults.performance = {
        success: true,
        elasticsearchTime: esTime,
        graphqlTime: graphqlTime,
        graphqlDataSource: graphqlResult.dataSource
      };
      
      console.log('✅ Performance tests completed\n');
      
    } catch (error) {
      console.error('❌ Performance tests failed:', error);
      this.testResults.performance = {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Print test summary
   */
  printTestSummary() {
    console.log('📋 Test Summary:');
    console.log('================');
    
    const tests = [
      { name: 'Indexing', result: this.testResults.indexing },
      { name: 'Elasticsearch Queries', result: this.testResults.elasticsearch },
      { name: 'GraphQL Resolvers', result: this.testResults.graphql },
      { name: 'Data Accuracy', result: this.testResults.dataAccuracy },
      { name: 'Performance', result: this.testResults.performance }
    ];
    
    tests.forEach(test => {
      const status = test.result?.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${test.name}`);
      if (!test.result?.success && test.result?.error) {
        console.log(`    Error: ${test.result.error}`);
      }
    });
    
    const passedTests = tests.filter(test => test.result?.success).length;
    const totalTests = tests.length;
    
    console.log(`\n📊 Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! Dashboard system is ready.');
    } else {
      console.log('⚠️ Some tests failed. Check the errors above.');
    }
  }

  /**
   * Test specific functionality
   */
  async testSpecific(testName) {
    switch (testName) {
      case 'indexing':
        await this.testIndexing();
        break;
      case 'elasticsearch':
        await this.testElasticsearchQueries();
        break;
      case 'graphql':
        await this.testGraphQLResolvers();
        break;
      case 'accuracy':
        await this.testDataAccuracy();
        break;
      case 'performance':
        await this.testPerformance();
        break;
      default:
        console.error('Unknown test:', testName);
    }
  }
}

// Export for use in other scripts
module.exports = DashboardDataTester;

// Run tests if called directly
if (require.main === module) {
  const tester = new DashboardDataTester();
  
  // Get test name from command line args
  const testName = process.argv[2];
  
  if (testName && testName !== 'all') {
    tester.testSpecific(testName).catch(console.error);
  } else {
    tester.runAllTests().catch(console.error);
  }
}
