import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

// Import queued GraphQL hook
import useQueuedStopGraphQL from '../../hooks/useQueuedStopGraphQL'
import { isAbortError } from '../../utils/error_handler'

// Import modular components
import { 
  CHART_COLORS, 
  INITIAL_SKELETON_STATE, 
  INITIAL_DATA_STATE 
} from './modules/constants.jsx'
import { useSkeletonManager } from './modules/skeletonManager.jsx'
import { useComputedValues } from './modules/computedValues.jsx'

// Extend dayjs with required plugins
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)

/**
 * ArretQueuedContext - Using queued GraphQL loading for progressive UI updates
 * 
 * This context uses a sequential loading approach to improve user experience
 * by prioritizing critical data first and loading other data progressively.
 */

const ArretQueuedContext = createContext()

export const useArretQueuedContext = () => {
  const context = useContext(ArretQueuedContext)
  if (!context) {
    console.error('⚠️  useArretQueuedContext: Context not found!')
    return null
  }
  return context
}

export const ArretQueuedProvider = ({ children }) => {
  console.log('🚀 ArretQueuedProvider: Initializing with sequential loading...');

  // Initialize the queued GraphQL hook
  const graphQL = useQueuedStopGraphQL();
  
  // Simple state management like Arrets2.jsx
  const [machineModels, setMachineModels] = useState([]);
  const [machineNames, setMachineNames] = useState([]);
  const [selectedMachineModel, setSelectedMachineModel] = useState("");
  const [selectedMachine, setSelectedMachine] = useState("");
  const [filteredMachineNames, setFilteredMachineNames] = useState([]);
  
  // Date filtering state
  const [dateRangeType, setDateRangeType] = useState("month"); // Default to month like UI shows
  const [selectedDate, setSelectedDate] = useState(null);
  const [dateRangeDescription, setDateRangeDescription] = useState("");
  const [dateFilterActive, setDateFilterActive] = useState(false);
  
  // Data state
  const [stopsData, setStopsData] = useState([]);
  const [arretStats, setArretStats] = useState([]);
  const [topStopsData, setTopStopsData] = useState([]);
  const [durationTrend, setDurationTrend] = useState([]);
  const [machineComparison, setMachineComparison] = useState([]);
  const [operatorStats, setOperatorStats] = useState([]);
  const [stopReasons, setStopReasons] = useState([]);
  
  // Loading states
  const [loading, setLoading] = useState(false);
  const [essentialLoading, setEssentialLoading] = useState(false);
  const [detailedLoading, setDetailedLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // UI state
  const [isChartModalVisible, setIsChartModalVisible] = useState(false);
  const [chartModalContent, setChartModalContent] = useState(null);
  
  // Refs for component lifecycle and optimization (following Arrets2.jsx pattern)
  const isMounted = useRef(true);
  const pendingFetch = useRef(false);
  
  // Add ref to track filter changes that need data refresh (like Arrets2.jsx)
  const filtersChanged = useRef(false);
  
  const [skeletonStates, setSkeletonStates] = useState(INITIAL_SKELETON_STATE);
  
  // Initialize skeleton manager
  const skeletonManager = useSkeletonManager(skeletonStates, setSkeletonStates);
  
  const fetchData = useCallback(async () => {
    // Prevent multiple simultaneous fetches
    if (pendingFetch.current) {
      console.log("🚫 Fetch already in progress, skipping...");
      return;
    }

    // SAFETY: Skip if we're unmounted
    if (!isMounted.current) {
      console.log('⏭️ Component unmounted, skipping fetch');
      return;
    }

    pendingFetch.current = true;
    setLoading(true);
    setError(null);

    console.log('🎯 fetchData called with filters:', {
      selectedMachineModel,
      selectedMachine,
      selectedDate: selectedDate?.format?.('YYYY-MM-DD'),
      dateRangeType,
      dateFilterActive
    });

    try {
      // Start appropriate skeleton loading
      skeletonManager.smartSkeletonForFilters(
        !!selectedMachineModel,
        !!selectedMachine,
        !!selectedDate
      );

      // Build filters for the GraphQL hook
      const filters = {
        model: selectedMachineModel || null,
        machine: selectedMachine || null,
        date: selectedDate ? selectedDate.format('YYYY-MM-DD') : null,
        startDate: selectedDate ? selectedDate.clone().startOf(dateRangeType).format('YYYY-MM-DD') : null,
        endDate: selectedDate ? selectedDate.clone().endOf(dateRangeType).format('YYYY-MM-DD') : null,
        dateRangeType: dateRangeType || 'month'
      };

      console.log('🎯 Built filters for GraphQL:', filters);

      // Use the queued loading approach - stats cards first, then table data, then charts
      console.log('📊 Step 1: Fetching stats cards...');
      const sidecards = await graphQL.getStopSidecards(filters);
      
      if (!isMounted.current) return;
      
      if (sidecards) {
        console.log('✅ Stats cards loaded:', sidecards);
        setArretStats(sidecards);
        skeletonManager.showSkeletonForComponent('sidecards', false);
      }

      console.log('📋 Step 2: Fetching table data...');
      const tableData = await graphQL.getStopTableData(filters);
      
      if (!isMounted.current) return;
      
      if (tableData) {
        console.log('✅ Table data loaded:', tableData.length, 'rows');
        setStopsData(tableData);
        skeletonManager.showSkeletonForComponent('table', false);
      }

      console.log('📊 Step 3: Fetching chart data...');
      const [topStops, durationTrend, machineComparison, operatorStats, stopReasons] = await Promise.all([
        graphQL.getTopStops(filters),
        graphQL.getDurationTrend(filters),
        graphQL.getMachineComparison(filters),
        graphQL.getOperatorStats(filters),
        graphQL.getStopReasons(filters)
      ]);

      if (!isMounted.current) return;

      // Update chart data
      if (topStops) {
        console.log('✅ Top stops loaded:', topStops.length, 'items');
        setTopStopsData(topStops);
        skeletonManager.showSkeletonForComponent('topStops', false);
      }

      if (durationTrend) {
        console.log('✅ Duration trend loaded:', durationTrend.length, 'items');
        setDurationTrend(durationTrend);
        skeletonManager.showSkeletonForComponent('durationTrend', false);
      }

      if (machineComparison) {
        console.log('✅ Machine comparison loaded:', machineComparison.length, 'items');
        setMachineComparison(machineComparison);
        skeletonManager.showSkeletonForComponent('machineComparison', false);
      }

      if (operatorStats) {
        console.log('✅ Operator stats loaded:', operatorStats.length, 'items');
        setOperatorStats(operatorStats);
        skeletonManager.showSkeletonForComponent('operatorStats', false);
      }

      if (stopReasons) {
        console.log('✅ Stop reasons loaded:', stopReasons.length, 'items');
        setStopReasons(stopReasons);
        skeletonManager.showSkeletonForComponent('stopReasons', false);
      }

      console.log('🎉 All data fetching complete!');

    } catch (error) {
      console.error('❌ Error fetching data:', error);
      
      if (!isMounted.current) return;
      
      if (!isAbortError(error)) {
        setError(error.message || 'Failed to fetch data');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
        setEssentialLoading(false);
        setDetailedLoading(false);
      }
      pendingFetch.current = false;
      filtersChanged.current = false; // Reset filter change flag
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, graphQL, skeletonManager]);
      console.log('📊 Step 1 Complete: Stats cards received:', sidecards);
      
      // Update state with the critical data first
      console.log('✅ Data refreshed with filters:', {
        model: filters.model,
        machine: filters.machine,
        startDate: filters.startDate,
        endDate: filters.endDate,
        totalStops: sidecards?.Arret_Totale,
        undeclaredStops: sidecards?.Arret_Totale_nondeclare,
        timestamp: new Date().toISOString()
      });
      
      setState(prev => ({
        ...prev,
        arretStats: sidecards,
        essentialLoading: false, // Essential data is loaded
      }));
      
      // Step 2: Get stops data (high priority)
      graphQL.getAllStops(filters)
        .then(stopsData => {
          if (!isMounted.current) return;
          
          setState(prev => ({
            ...prev,
            stopsData,
          }));
          
          console.log(`✅ Loaded ${stopsData?.length || 0} stops`);
        })
        .catch(error => {
          if (!isMounted.current) return;
          console.error('❌ Error loading stops data:', error);
        });
      
      // Step 3: Load chart data (medium priority)
      Promise.all([
        graphQL.getTop5Stops(filters),
        graphQL.getMachineStopComparison(filters)
      ])
        .then(([topStops, machineComparison]) => {
          if (!isMounted.current) return;
          
          setState(prev => ({
            ...prev,
            topStopsData: topStops,
            machineComparison,
          }));
          
          console.log('✅ Chart data loaded');
        })
        .catch(error => {
          if (!isMounted.current) return;
          console.error('❌ Error loading chart data:', error);
        });
      
      // Step 4: Load analytics data (low priority)
      Promise.all([
        graphQL.getOperatorStopStats(filters),
        graphQL.getStopDurationTrend(filters)
      ])
        .then(([operatorStats, durationTrend]) => {
          if (!isMounted.current) return;
          
          setState(prev => ({
            ...prev,
            operatorStats,
            durationTrend,
            loading: false, // All data is loaded
          }));
          
          console.log('✅ Analytics data loaded');
        })
        .catch(error => {
          if (!isMounted.current) return;
          console.error('❌ Error loading analytics data:', error);
          
          setState(prev => ({
            ...prev,
            loading: false, // Mark as done even if there's an error
          }));
        });
      
      // Progressive skeleton clearing for better UX
      skeletonManager.progressiveSkeletonClear();
      
    } catch (error) {
      // Check if this is an abort error (which can happen during navigation)
      if (isAbortError(error)) {
        console.log('ℹ️ ArretQueuedProvider: Data load aborted due to navigation.');
        
        // Set a special state for aborted requests
        setState(prev => ({ 
          ...prev,
          loading: false,
          essentialLoading: false,
          error: null // Don't show error for aborted requests
        }));
      } else {
        // This is a real error that should be displayed
        console.error('❌ ArretQueuedProvider: Data load failed:', error);
        
        // Set error state
        setState(prev => ({ 
          ...prev,
          loading: false,
          essentialLoading: false,
          error: error.message || 'Failed to load data'
        }));
      }
    } finally {
      pendingFetch.current = false;
      
      // Reset fetch counter after 5 seconds
      setTimeout(() => {
        if (fetchCount.current > 0) {
          console.log(`🔄 Fetch counter reset (was ${fetchCount.current})`);
          fetchCount.current = 0;
        }
      }, 5000);
    }
  }, [
    // Use a stable reference to these values instead of reading from state object
    graphQL, 
    setState, 
    skeletonManager,
    isMounted
    // REMOVED state dependencies to break the infinite loop
    // state dependencies are captured in the closure and used when needed
  ]);

  /**
   * Debounced version of fetchData for filter changes 
   * with protection against rapid successive calls
   */
  const debouncedFetchData = useCallback((forceRefresh = false) => {
    console.log('🕒 Debouncing fetch data call...');
    
    // Clear any existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    
    // Skip if a fetch is already in progress and this isn't a forced refresh
    if (pendingFetch.current && !forceRefresh) {
      console.log('🚫 Fetch already in progress, debounce skipped');
      return () => {}; // Return empty cleanup
    }

    // Log filter changes but don't show notifications (per user request)
    if (state.selectedMachineModel || state.selectedMachine || state.selectedDate) {
      console.log('📊 Applying filters:', {
        model: state.selectedMachineModel,
        machine: state.selectedMachine,
        date: state.selectedDate?.format('DD/MM/YYYY')
      });
    }

    // Set new timeout with a reasonable delay
    debounceTimeout.current = setTimeout(() => {
      console.log('⏱️ Debounce timeout expired, executing fetch');
      fetchData(forceRefresh);
    }, 500); // Increased to 500ms for better debounce behavior

    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, [fetchData]);

  /**
   * Fetch machine models using the queued hook
   */
  const fetchMachineModels = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      const models = await graphQL.getMachineModels();
      
      if (!models || models.length === 0) {
        console.warn('⚠️ No machine models returned');
        // Always use string values, not objects
        setState(prev => ({ 
          ...prev, 
          machineModels: ["IPS", "CCM24"],
          loading: false
        }));
        return;
      }
      
      // Extract model strings from objects if needed
      const processedModels = models.map(item => 
        typeof item === 'object' && item.model ? item.model : item
      );
      
      setState(prev => ({ 
        ...prev, 
        machineModels: processedModels,
        loading: false 
      }));
      
      console.log('✅ Machine models loaded:', processedModels);
    } catch (error) {
      console.error('❌ Failed to fetch machine models:', error);
      
      // Set fallback models in case of error - always as strings
      setState(prev => ({ 
        ...prev, 
        machineModels: ["IPS", "CCM24"],
        loading: false,
        error: error.message 
      }));
    }
  }, [graphQL, setState]);

  /**
   * Fetch machine names using the queued hook
   */
  const fetchMachineNames = useCallback(async (modelFilter = null) => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      const model = modelFilter || state.selectedMachineModel;
      
      console.log('🔍 fetchMachineNames called with model:', {
        providedFilter: modelFilter,
        stateModel: state.selectedMachineModel,
        finalModel: model
      });
      
      const machines = await graphQL.getMachineNames(model);
      
      // Debug the machines received from GraphQL
      console.log('📊 Machine names received from GraphQL:', {
        model,
        count: machines?.length || 0,
        sampleItems: machines?.slice(0, 3)
      });
      
      if (!machines || machines.length === 0) {
        console.warn(`⚠️ No machine names returned for model ${model}`);
        
        // Set fallback machines based on model
        const fallbackMachines = model === 'CCM24' 
          ? [
              { Machine_Name: "CCM2401" },
              { Machine_Name: "CCM2402" },
            ]
          : [
              { Machine_Name: "IPS01" },
              { Machine_Name: "IPS02" },
            ];
            
        console.log('🚨 Using fallback machines:', fallbackMachines);
            
        setState(prev => ({ 
          ...prev, 
          machineNames: fallbackMachines,
          filteredMachineNames: fallbackMachines,
          loading: false
        }));
        return;
      }
      
      setState(prev => ({ 
        ...prev, 
        machineNames: machines,
        filteredMachineNames: machines,
        loading: false 
      }));
      
      console.log(`✅ Machine names loaded for model ${model}:`, machines);
    } catch (error) {
      console.error('❌ Failed to fetch machine names:', error);
      
      // Set fallback machines in case of error
      const fallbackMachines = state.selectedMachineModel === 'CCM24' 
        ? [
            { Machine_Name: "CCM2401" },
            { Machine_Name: "CCM2402" },
          ]
        : [
            { Machine_Name: "IPS01" },
            { Machine_Name: "IPS02" },
          ];
          
      setState(prev => ({ 
        ...prev, 
        machineNames: fallbackMachines,
        filteredMachineNames: fallbackMachines,
        loading: false,
        error: error.message 
      }));
    }
  }, [graphQL, setState, state.selectedMachineModel]);

  // Comprehensive filter debug tracking
  const filterDebugTracker = useRef({
    transitions: [],
    lastSnapshot: null
  });

  const logFilterTransition = useCallback((action, details) => {
    const currentSnapshot = {
      model: state.selectedMachineModel,
      machine: state.selectedMachine,
      date: state.selectedDate?.format?.('YYYY-MM-DD') || null,
      dateActive: state.dateFilterActive,
      dateRangeType: state.dateRangeType
    };

    const transition = {
      timestamp: new Date().toISOString(),
      action,
      details,
      before: filterDebugTracker.current.lastSnapshot,
      after: currentSnapshot
    };

    filterDebugTracker.current.transitions.push(transition);
    filterDebugTracker.current.lastSnapshot = { ...currentSnapshot };

    console.log('🔍 FILTER TRANSITION:', {
      action,
      details,
      before: transition.before,
      after: currentSnapshot,
      changed: transition.before ? Object.keys(currentSnapshot).filter(key => 
        currentSnapshot[key] !== transition.before[key]
      ) : 'initial'
    });

    // Keep only last 10 transitions to avoid memory issues
    if (filterDebugTracker.current.transitions.length > 10) {
      filterDebugTracker.current.transitions.shift();
    }
  }, [state.selectedMachineModel, state.selectedMachine, state.selectedDate, state.dateFilterActive, state.dateRangeType]);

  // Event handlers for filters
  const handleMachineModelChange = useCallback((model) => {
    // Convert object to string if needed
    const modelValue = typeof model === 'object' ? model.model : model;
    
    logFilterTransition('MODEL_CHANGE', { 
      from: state.selectedMachineModel, 
      to: modelValue 
    });
    
    console.log('🔧 handleMachineModelChange called with:', {
      originalValue: model,
      processedValue: modelValue,
      currentState: {
        machine: state.selectedMachine,
        date: state.selectedDate?.format?.('YYYY-MM-DD'),
        dateActive: state.dateFilterActive,
        dateRangeType: state.dateRangeType
      }
    });
    
    // Build the new complete filter state
    const newFilterState = {
      selectedMachineModel: modelValue,
      selectedMachine: '', // Reset machine when model changes
      selectedDate: state.selectedDate, // Keep existing date
      dateFilterActive: state.dateFilterActive, // Keep existing date filter state
      dateRangeType: state.dateRangeType
    };
    
    // Update state immediately
    setState(prev => ({ 
      ...prev, 
      ...newFilterState,
      dateRangeDescription: prev.selectedDate 
        ? `pour ${modelValue || 'toutes les machines'}` 
        : ''
    }));
    
    // CRITICAL FIX: Pass the complete new filter state
    console.log('🔄 Applying model filter with complete state:', newFilterState);
    fetchData(true, newFilterState);
  }, [fetchData, state.selectedDate, state.dateFilterActive, state.dateRangeType, logFilterTransition]);

  const handleMachineNameChange = useCallback((machine) => {
    // Convert object to string if needed
    const machineName = typeof machine === 'object' ? machine.Machine_Name : machine;
    
    logFilterTransition('MACHINE_CHANGE', { 
      from: state.selectedMachine, 
      to: machineName 
    });
    
    console.log('🔧 handleMachineNameChange called with:', {
      originalValue: machine,
      processedValue: machineName,
      currentState: {
        model: state.selectedMachineModel,
        date: state.selectedDate?.format?.('YYYY-MM-DD'),
        dateActive: state.dateFilterActive,
        dateRangeType: state.dateRangeType
      }
    });
    
    // Build the new complete filter state
    const newFilterState = {
      selectedMachine: machineName,
      selectedMachineModel: state.selectedMachineModel, // Keep existing model
      selectedDate: state.selectedDate, // Keep existing date
      dateFilterActive: state.dateFilterActive, // Keep existing date filter state
      dateRangeType: state.dateRangeType
    };
    
    // Update state immediately
    setState(prev => ({ 
      ...prev, 
      ...newFilterState,
      dateRangeDescription: prev.selectedDate 
        ? `pour ${machineName || prev.selectedMachineModel || 'toutes les machines'}` 
        : ''
    }));
    
    // CRITICAL FIX: Pass the complete new filter state
    console.log('🔄 Applying machine filter with complete state:', newFilterState);
    fetchData(true, newFilterState);
  }, [fetchData, state.selectedMachineModel, state.selectedDate, state.dateFilterActive, state.dateRangeType, logFilterTransition]);

  const handleDateRangeTypeChange = useCallback((type) => {
    // CRITICAL DEBUG: Always log when this function is called
    console.error('� handleDateRangeTypeChange CALLED!', {
      newType: type,
      currentType: state.dateRangeType,
      currentState: {
        model: state.selectedMachineModel,
        machine: state.selectedMachine,
        date: state.selectedDate?.format?.('YYYY-MM-DD'),
        dateActive: state.dateFilterActive
      },
      stackTrace: new Error().stack
    });
    
    // If the type is not actually changing, skip the update
    if (state.dateRangeType === type) {
      console.log('📅 Date range type unchanged, skipping update');
      return;
    }
    
    // Build the new complete filter state
    const newFilterState = {
      dateRangeType: type,
      selectedMachineModel: state.selectedMachineModel, // Keep existing model
      selectedMachine: state.selectedMachine, // Keep existing machine
      selectedDate: state.selectedDate, // Keep existing date
      dateFilterActive: state.dateFilterActive // Keep existing date filter state
    };
    
    console.log('📅 handleDateRangeTypeChange - preserving filters:', {
      newFilterState,
      willClearDate: !state.selectedDate,
      willKeepMachine: !!state.selectedMachine,
      willKeepModel: !!state.selectedMachineModel
    });
    
    // Update state immediately
    setState(prev => ({ 
      ...prev, 
      dateRangeType: type
    }));
    
    // CRITICAL FIX: Only fetch if a date is selected, and use complete filter state
    if (state.selectedDate) {
      console.log('🔄 Applying date range type change with complete state:', newFilterState);
      fetchData(true, newFilterState);
    }
  }, [fetchData, state.selectedDate, state.selectedMachineModel, state.selectedMachine, state.dateFilterActive, state.dateRangeType]);

  const handleDateChange = useCallback((date) => {
    const isDateActive = !!date;
    const description = isDateActive 
      ? `pour ${state.selectedMachine || state.selectedMachineModel || 'toutes les machines'}` 
      : '';
    
    // CRITICAL DEBUG: Log the state BEFORE making any changes
    console.log('📅 handleDateChange - BEFORE state change:', {
      date: date?.format?.('YYYY-MM-DD') || null,
      isActive: isDateActive,
      currentState: {
        model: state.selectedMachineModel,
        machine: state.selectedMachine,
        dateRangeType: state.dateRangeType
      }
    });
    
    logFilterTransition('DATE_CHANGE', { 
      from: state.selectedDate?.format?.('YYYY-MM-DD') || null, 
      to: date?.format?.('YYYY-MM-DD') || null,
      dateActive: isDateActive
    });
    
    // Build the new complete filter state - EXPLICITLY preserve machine
    const newFilterState = {
      selectedDate: date,
      dateFilterActive: isDateActive,
      dateRangeType: state.dateRangeType, // Keep existing date range type
      selectedMachineModel: state.selectedMachineModel, // Keep existing model
      selectedMachine: state.selectedMachine // CRITICAL: Keep existing machine
    };
    
    // CRITICAL DEBUG: Log what we're about to set
    console.log('📅 handleDateChange - NEW filter state being applied:', {
      newFilterState,
      preservedMachine: state.selectedMachine,
      preservedModel: state.selectedMachineModel,
      preservedDateRangeType: state.dateRangeType
    });
    
    // Update state immediately
    setState(prev => {
      const newState = { 
        ...prev, 
        ...newFilterState,
        dateRangeDescription: description
      };
      
      // CRITICAL DEBUG: Log state transition
      console.log('📅 setState - Date change state transition:', {
        prevMachine: prev.selectedMachine,
        newMachine: newState.selectedMachine,
        prevModel: prev.selectedMachineModel,
        newModel: newState.selectedMachineModel,
        prevDate: prev.selectedDate?.format?.('YYYY-MM-DD') || null,
        newDate: newState.selectedDate?.format?.('YYYY-MM-DD') || null,
        machinePreserved: prev.selectedMachine === newState.selectedMachine
      });
      
      return newState;
    });
    
    // CRITICAL FIX: Pass the complete new filter state
    console.log('🔄 Applying date filter with complete state:', newFilterState);
    fetchData(true, newFilterState);
    
    // Enhanced debug log after fetch
    if (date) {
      console.log('📆 Date filter applied with combined filters:', {
        date: date.format('YYYY-MM-DD'),
        startOfPeriod: date.clone().startOf(state.dateRangeType).format('YYYY-MM-DD'),
        endOfPeriod: date.clone().endOf(state.dateRangeType).format('YYYY-MM-DD'),
        dateRangeType: state.dateRangeType,
        combinedWith: {
          model: state.selectedMachineModel,
          machine: state.selectedMachine
        }
      });
    }
  }, [state.selectedMachine, state.selectedMachineModel, state.dateRangeType, fetchData, logFilterTransition]);

  const handleFilterClear = useCallback(() => {
    console.log('🧹 handleFilterClear called - clearing all filters');
    
    // Build the complete cleared filter state
    const clearedFilterState = {
      selectedMachineModel: '',
      selectedMachine: '',
      selectedDate: null,
      dateFilterActive: false,
      dateRangeType: state.dateRangeType // Keep the date range type
    };
    
    setState(prev => ({
      ...prev,
      ...clearedFilterState,
      dateRangeDescription: ''
    }));
    
    // CRITICAL FIX: Pass the complete cleared filter state
    console.log('🔄 Applying filter clear with complete state:', clearedFilterState);
    fetchData(true, clearedFilterState);
  }, [fetchData, state.dateRangeType]);

  // Computed values from state
  const computedValues = useComputedValues(state);

  // Effects for component lifecycle and data management
  
  // Initial data load - strictly once and with cleanup flag
  useEffect(() => {
    console.log('🔄 ArretQueuedProvider: Initial data load...');
    
    // Set mounted flag to true on mount
    isMounted.current = true;
    
    // Flag to prevent multiple loads
    let initialLoadDone = false;
    
    const initializeData = async () => {
      // Skip if already loaded
      if (initialLoadDone) return;
      initialLoadDone = true;
      
      try {
        // Load machine models first
        await fetchMachineModels();
        
        // Verify we're still mounted
        if (!isMounted.current) return;
        
        // Also load all machine names for dropdown
        await fetchMachineNames(null); // null = get all machines
        
        // Verify we're still mounted
        if (!isMounted.current) return;
        
        // Initial data fetch with current filters - force refresh to avoid caching issues
        await fetchData(true);
        
        console.log('✅ ArretQueuedProvider: Initial data load completed');
      } catch (error) {
        if (!isMounted.current) return;
        console.error('❌ ArretQueuedProvider: Initial data load failed:', error);
      }
    };

    initializeData();
    
    // Cleanup function
    return () => {
      console.log('💢 ArretQueuedProvider: Unmounting, cleaning up resources');
      isMounted.current = false;
      
      // Clear any pending timeouts
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
        debounceTimeout.current = null;
      }
      
      // Cancel all pending requests
      graphQL.cancelAllRequests('Component unmounted');
      
      // Reset fetch state
      pendingFetch.current = false;
    };
  }, []); // Empty dependency array - runs strictly once

  // Smart data fetching when filters change - with aggressive infinite loop protection
  const lastFilterString = useRef('');
  const filterChangeCount = useRef(0);
  
  useEffect(() => {
    // SAFETY: Skip if we're unmounted
    if (!isMounted.current) return;
    
    // We've moved the actual data fetching to the filter handler functions
    // This effect now just tracks the filter state for debugging purposes
    
    // Create a string representation of current filters
    const currentFilterString = JSON.stringify({
      model: state.selectedMachineModel,
      machine: state.selectedMachine,
      date: state.selectedDate?.format?.('YYYY-MM-DD') || null,
      type: state.dateRangeType
    });
    
    // Skip if filters haven't actually changed
    if (currentFilterString === lastFilterString.current) {
      return;
    }
    
    console.log('� ArretQueuedProvider: Filter state changed:', {
      from: lastFilterString.current,
      to: currentFilterString
    });
    
    // Update last filter string
    lastFilterString.current = currentFilterString;
  }, [
    // Only depend on specific primitive values, not entire state object
    debouncedFetchData,
    // Specific primitive values to avoid unnecessary triggers
    state.selectedMachineModel,
    state.selectedMachine, 
    state.dateRangeType,
    // Convert date to string to avoid object reference comparison issues
    state.selectedDate ? state.selectedDate.format('YYYY-MM-DD') : null
  ]);

  // Ref to track machine model updates
  const modelRenderTracker = useRef({
    isFirstRender: true,
    lastModel: null,
    updateCount: 0
  });
  
  // Update filtered machine names when machine model changes - with loop protection
  useEffect(() => {
    // SAFETY: Skip if we're unmounted
    if (!isMounted.current) return;
    
    // Skip the first render to prevent unnecessary fetches
    if (modelRenderTracker.current.isFirstRender) {
      modelRenderTracker.current.isFirstRender = false;
      modelRenderTracker.current.lastModel = state.selectedMachineModel;
      return;
    }
    
    // Skip if model hasn't actually changed
    if (modelRenderTracker.current.lastModel === state.selectedMachineModel) {
      console.log('⏭️ Machine model unchanged, skipping update');
      return;
    }
    
    // Update last model
    modelRenderTracker.current.lastModel = state.selectedMachineModel;
    
    // SAFETY: Prevent runaway effects
    modelRenderTracker.current.updateCount++;
    if (modelRenderTracker.current.updateCount > 5) {
      console.error('🛑 SAFETY ABORT: Too many model change effects. Breaking potential infinite loop.');
      return;
    }
    
    console.log('🔄 Machine model changed to', state.selectedMachineModel || 'none', 'fetching appropriate machine names');
    
    if (state.selectedMachineModel) {
      // Explicitly log current state before fetching
      console.log('📊 Current state before fetching machine names:', {
        selectedModel: state.selectedMachineModel,
        currentMachineNames: state.machineNames?.length || 0,
        currentFilteredNames: state.filteredMachineNames?.length || 0
      });
      
      // Fetch machine names for the selected model
      fetchMachineNames(state.selectedMachineModel);
    } else {
      // When no model is selected, show all machines
      setState(prev => ({ 
        ...prev, 
        filteredMachineNames: prev.machineNames 
      }));
      
      console.log('🔄 Resetting to all machines:', {
        count: state.machineNames?.length || 0
      });
    }
    
    // Reset counter after 2 seconds to allow future model changes
    setTimeout(() => {
      if (modelRenderTracker.current.updateCount > 0) {
        modelRenderTracker.current.updateCount = 0;
      }
    }, 2000);
    
  }, [state.selectedMachineModel, fetchMachineNames, setState, isMounted]);

  // Chart modal handlers
  const openChartModal = (content) => {
    setState(prev => ({
      ...prev,
      isChartModalVisible: true,
      chartModalContent: content
    }));
  };

  const closeChartModal = () => {
    setState(prev => ({
      ...prev,
      isChartModalVisible: false,
      chartModalContent: null
    }));
  };

  // Build context value with aggressive stability measures
  const stableHandlers = React.useRef({
    handleMachineModelChange,
    handleMachineNameChange,
    // Add an alias for handleMachineChange to match what ArretFilters expects
    handleMachineChange: handleMachineNameChange, 
    handleDateRangeTypeChange,
    handleDateChange,
    handleFilterClear,
    fetchData,
    debouncedFetchData,
    fetchMachineModels,
    fetchMachineNames,
    openChartModal,
    closeChartModal,
    setIsChartModalVisible: (visible) => setState(prev => ({ 
      ...prev, 
      isChartModalVisible: visible 
    })),
    setChartModalContent: (content) => setState(prev => ({ 
      ...prev, 
      chartModalContent: content 
    }))
  }).current;
  
  // Track if context is being built excessively
  const contextBuildCount = useRef(0);
  
  // Build context value with minimal dependencies
  const contextValue = React.useMemo(() => {
    contextBuildCount.current++;
    
    // Log warning if rebuilding context too many times
    if (contextBuildCount.current > 10) {
      console.warn(`🔄 Context rebuilt ${contextBuildCount.current} times - possible performance issue`);
    }
    
    console.log(`🔄 Building context value (build #${contextBuildCount.current})`);
    
    return {
      // Core state - extract only what's needed
      ...state,
      
      // Skeleton states and management
      skeletonStates,
      ...skeletonManager,
      
      // Computed values
      ...computedValues,
      
      // Event handlers (stable references)
      ...stableHandlers,
      
      // Constants
      CHART_COLORS,
      
      // GraphQL hook access
      graphQL,
      
      // Add handleRefresh function for error scenarios
      handleRefresh: () => fetchData(true)
    };
  }, [
    // AGGRESSIVE MEASURE: Only depend on state values that MUST trigger an update
    state.selectedMachineModel,
    state.selectedMachine,
    state.selectedDate,
    state.dateRangeType,
    state.loading,
    state.error,
    state.essentialLoading,
    state.stopsData,
    state.arretStats,
    state.dateFilterActive,
    
    // Other dependencies
    skeletonStates,
    skeletonManager,
    computedValues,
    graphQL
  ]);
  
  // Reset context build counter after 2 seconds of inactivity
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (contextBuildCount.current > 0) {
        console.log(`🔄 Context build counter reset (was ${contextBuildCount.current})`);
        contextBuildCount.current = 0;
      }
    }, 2000);
    
    return () => clearTimeout(timer);
  });

  console.log('🎯 ArretQueuedProvider: Context value prepared with', 
    Object.keys(contextValue).length, 'properties');

  return (
    <ArretQueuedContext.Provider value={contextValue}>
      {children}
    </ArretQueuedContext.Provider>
  );
};

export default ArretQueuedContext;
