/**
 * DashboardContainer Component
 * Main container for the daily performance dashboard
 * This component matches the structure of the original DailyPerformanceDashboard
 */
import React, { useState } from 'react';
import {
  Row, Col, Card, Spin, Alert, Tabs, Button, Typography,
  Space, Divider, Badge, Tag, Table, Radio, Tooltip, Popover, Progress, message
} from 'antd';
import {
  DashboardOutlined, ReloadOutlined, CheckCircleOutlined,CloseCircleOutlined ,
  InfoCircleOutlined, FilterOutlined, FileTextOutlined, SettingOutlined
} from '@ant-design/icons';
import { useTheme } from '../../theme-context';
import { useDashboardData } from '../../hooks/useDashboardData';
import EnhancedMachineCard from './enhanced-machine-card';
import './machine-card.css';
import StatusIndicator from './StatusIndicator';
import MachineDetailModal from './MachineDetailModal';
import './dashboard.css';
import SOMIPEM_COLORS from '../../styles/brand-colors';

// Import SSE notification components
import SSENotificationBell from '../SSENotificationBell';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// Use SOMIPEM brand colors for consistent styling
const COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  primaryLight: SOMIPEM_COLORS.SELECTED_BG,
  success: SOMIPEM_COLORS.SUCCESS,
  warning: SOMIPEM_COLORS.WARNING,
  error: SOMIPEM_COLORS.ERROR,
  gray: SOMIPEM_COLORS.LIGHT_GRAY,
  textSecondary: SOMIPEM_COLORS.LIGHT_GRAY,
  bgLight: SOMIPEM_COLORS.LIGHT_BLUE_BG,
  buttonBlue: SOMIPEM_COLORS.SECONDARY_BLUE,
};

/**
 * DashboardContainer component - Main container for the daily performance dashboard
 * This component matches the structure and functionality of the original DailyPerformanceDashboard
 */
const DashboardContainer = () => {
  const { darkMode } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedView, setSelectedView] = useState("machines");



  // Helper function to render connection status indicator with SOMIPEM colors
  const renderConnectionStatus = (wsStatus) => {
    let statusText = "Connecté";
    let statusColor = COLORS.success;
    let statusIcon = <CheckCircleOutlined />;

    if (wsStatus.fallbackMode) {
      statusText = "Mode de secours";
      statusColor = COLORS.warning;
      statusIcon = <InfoCircleOutlined />;
    } else if (wsStatus.reconnecting) {
      statusText = "Reconnexion...";
      statusColor = COLORS.warning;
      statusIcon = <ReloadOutlined spin />;
    } else if (wsStatus.connecting) {
      statusText = "Connexion...";
      statusColor = COLORS.primary;
      statusIcon = <ReloadOutlined spin />;
    } else if (!wsStatus.connected) {
      statusText = "Déconnecté";
      statusColor = COLORS.error;
      statusIcon = <CloseCircleOutlined />;
    }

    return (
      <div style={{ display: 'flex', alignItems: 'center', marginRight: '16px' }}>
        <Badge
          status={wsStatus.connected ? "success" : wsStatus.fallbackMode ? "warning" : wsStatus.reconnecting ? "processing" : "error"}
          text={
            <Tooltip title={wsStatus.fallbackMode ? "Utilisation des mises à jour périodiques au lieu de la connexion en temps réel" : ""}>
              <span style={{ color: statusColor, display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '4px' }}>{statusIcon}</span>
                {statusText}
              </span>
            </Tooltip>
          }
        />
      </div>
    );
  };

  // Use our custom hook to manage dashboard data with WebSocket handling
  const {
    // Data
    machineData,
    selectedMachine,
    machineHistory,

    // Status
    loading,
    error,
    lastUpdate,
    wsStatus,
    historyLoading,
    historyError,

    // Actions
    setSelectedMachine,
    handleRefresh,
    fetchMachineHistory
  } = useDashboardData();

  // Format the last update time
  const formatLastUpdate = () => {
    return lastUpdate.toLocaleTimeString();
  };

  // Handle machine selection
  const handleMachineSelect = (machine) => {
    if (!machine.id) {
      message.info("Cette machine n'est pas encore configurée");
      return;
    }

    setSelectedMachine(machine);
    fetchMachineHistory(machine.id);
    setModalVisible(true);
  };

  // Handle view change
  const handleViewChange = (e) => {
    setSelectedView(e.target.value);
  };



  // Get status color based on machine status
  const getStatusColor = (status, machine) => {
    // Gray for unavailable/out of service machines (no ID)
    if ( machine && machine.id !== 1 ) return COLORS.gray;
    
    // Red for available but not working machines (offline)
    if (machine && machine.Etat === "off") return COLORS.error;
    
    // Green for working machines (online)
    if (machine && machine.Etat === "on") return COLORS.success;
    
    // Default fallback colors based on status
    if (status === 'success') return COLORS.success;
    if (status === 'warning') return COLORS.warning;
    return COLORS.error;
  };



  // Render the machine cards grid
  const renderMachineCards = () => {
    // Show loading spinner only during initial load, not during WebSocket updates
    if (loading && !wsStatus.connected && !machineData.length) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>Chargement des données...</div>
        </div>
      );
    }

    if (error && !machineData.length) {
      // Enhanced error message with more user-friendly information
      return (
        <Alert
          message="Erreur de connexion"
          description={
            <>
              {error}
              {wsStatus.fallbackMode && (
                <div style={{ marginTop: '10px' }}>
                  <strong>Mode de secours activé:</strong> Les données seront actualisées périodiquement au lieu des mises à jour en temps réel.
                </div>
              )}
              {!wsStatus.fallbackMode && (
                <div style={{ marginTop: '10px' }}>
                  <strong>Dépannage:</strong>
                  <ul>
                    <li>Vérifiez votre connexion réseau</li>
                    <li>Le serveur peut être temporairement indisponible</li>
                    <li>Essayez de rafraîchir la page</li>
                  </ul>
                </div>
              )}
            </>
          }
          type="error"
          showIcon
          action={
            <Button type="primary" onClick={handleRefresh}>
              Réessayer
            </Button>
          }
        />
      );
    }

    if (!machineData || machineData.length === 0) {
      return (
        <Alert
          message="Aucune donnée"
          description="Aucune donnée de machine disponible"
          type="info"
          showIcon
        />
      );
    }

    // If we have machine data, always show it regardless of connection status
    return (
      <>
        <Row gutter={[16, 16]}>
          {machineData.map((machine) => (
            <Col xs={24} sm={24} md={12} key={machine.id || machine.Machine_Name}>
              <div className="machine-card-container" style={{ position: "relative" }}>
                <EnhancedMachineCard
                  machine={machine}
                  handleMachineClick={handleMachineSelect}
                  getStatusColor={getStatusColor}
                />

                {/* Apply "In Development" overlay to all machines except ID 1 */}
                {machine.id !== 1 && (
                  <div className="soon-overlay">
                    <div className="soon-text">
                      En développement...
                    </div>
                    <Button type="primary" ghost size="small" icon={<SettingOutlined />}>
                      Configuration requise
                    </Button>
                  </div>
                )}

                {/* Special overlay for machines without ID */}
                {!machine.id && (
                  <div className="soon-overlay">
                    <div className="soon-text">
                      En développement...
                    </div>
                    <Button type="default" size="small">
                      Configuration requise
                    </Button>
                  </div>
                )}
              </div>
            </Col>
          ))}
        </Row>
      </>
    );
  };

  // Define machine table columns
  const machineColumns = [
    {
      title: 'Machine',
      dataIndex: 'Machine_Name',
      key: 'Machine_Name',
      sorter: (a, b) => a.Machine_Name.localeCompare(b.Machine_Name),
    },
    {
      title: 'Statut',
      dataIndex: 'Etat',
      key: 'Etat',
      render: (text) => (
        <Tag color={text === 'on' ? 'success' : 'error'}>
          {text === 'on' ? 'En ligne' : 'Hors ligne'}
        </Tag>
      ),
      filters: [
        { text: 'En ligne', value: 'on' },
        { text: 'Hors ligne', value: 'off' },
      ],
      onFilter: (value, record) => record.Etat === value,
    },
    {
      title: 'TRS',
      dataIndex: 'TRS',
      key: 'TRS',
      sorter: (a, b) => parseFloat(a.TRS || 0) - parseFloat(b.TRS || 0),
      render: (text) => {
        const value = parseFloat(text || 0);
        let color = 'red';
        if (value > 80) color = 'green';
        else if (value > 60) color = 'orange';

        return <span style={{ color }}>{value.toFixed(1)}%</span>;
      },
    },
    {
      title: 'Production',
      dataIndex: 'Quantite_Bon',
      key: 'Quantite_Bon',
      sorter: (a, b) => parseFloat(a.Quantite_Bon || 0) - parseFloat(b.Quantite_Bon || 0),
    },
    {
      title: 'Rejets',
      dataIndex: 'Quantite_Rejet',
      key: 'Quantite_Rejet',
      sorter: (a, b) => parseFloat(a.Quantite_Rejet || 0) - parseFloat(b.Quantite_Rejet || 0),
    },
    {
      title: 'Opérateur',
      dataIndex: 'Regleur_Prenom',
      key: 'Regleur_Prenom',
      render: (text) => text || 'Non assigné',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          disabled={record.Etat === 'off'} // Disable button for offline machines
          onClick={(e) => {
            e.stopPropagation();
            if (record.Etat !== 'off') { // Only allow details for online machines
              handleMachineSelect(record);
            }
          }}
          title={record.Etat === 'off' ? "Machine hors ligne. Détails non disponibles." : "Voir les détails de la machine"}
        >
          Détails
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      {/* Header with title and refresh button */}
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 24 }}>
        <div>
          <Title level={2}><DashboardOutlined /> Tableau de Bord</Title>
          <Text type="secondary">{new Date().toLocaleDateString()}</Text>
        </div>
        <Space>
          {/* Connection status indicator */}
          {renderConnectionStatus(wsStatus)}
          <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh}>
            Actualiser
          </Button>
        </Space>
      </div>

      {/* Error alert */}
      {error && (
        <Alert
          type="error"
          message="Erreur de connexion"
          description={`Dernière erreur: ${error} | Dernière mise à jour: ${formatLastUpdate()}`}
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Divider />

      {/* Filters and views */}
      <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
        <Radio.Group value={selectedView} onChange={handleViewChange} buttonStyle="solid">
          <Radio.Button value="machines">
            <DashboardOutlined /> Machines
          </Radio.Button>
        </Radio.Group>

        <Space>
          <Tooltip title="Filtrer les données">
            <Button icon={<FilterOutlined />}>Filtres</Button>
          </Tooltip>
          <Tooltip title="Exporter les données">
            <Button icon={<FileTextOutlined />}>Exporter</Button>
          </Tooltip>
        </Space>
      </div>

      {/* Connection status and refresh button */}
      <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
        <Col span={24}>
          <Card>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Text strong style={{ marginRight: "10px" }}>Dernière mise à jour: {formatLastUpdate()}</Text>
                <StatusIndicator
                  wsStatus={wsStatus}
                  onRefresh={handleRefresh}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>



      {/* Main content - Machine view */}
      {selectedView === "machines" && (
        <Row gutter={[18, 18]}>
          {/* Machine statistics */}
          <Col xs={24} lg={24}>
            <Card
              title={
                <div style={{ display: "flex", alignItems: "center" }}>
                  <DashboardOutlined style={{ fontSize: 20, marginRight: 8 }} />
                  <span>Statistiques des machines</span>
                </div>
              }
              extra={<Badge count={machineData.length} style={{ backgroundColor: "#1890ff" }} />}
            >
              {renderMachineCards()}
            </Card>
          </Col>
        </Row>
      )}

      <Divider />

      {/* Detailed machine table */}
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <SettingOutlined style={{ fontSize: 20, marginRight: 8 }} />
            <span>Détails des machines</span>
          </div>
        }
        extra={
          <Space>
            <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh} size="small">
              Actualiser
            </Button>
            <Tag color="processing">{machineData.filter((m) => m.Etat === "on").length} sessions actives</Tag>
          </Space>
        }
      >
        <Tabs defaultActiveKey="1" className={darkMode ? "dark-mode" : ""}>
          <TabPane tab="Tableau" key="1">
            <Table
              columns={machineColumns}
              dataSource={machineData.map((item, index) => ({ ...item, key: index }))}
              pagination={{ pageSize: 10 }}
              scroll={{ x: true }}
              onRow={(record) => ({
                onClick: () => record.Etat !== 'off' && handleMachineSelect(record),
                style: {
                  cursor: record.Etat === 'off' ? 'not-allowed' : 'pointer',
                  opacity: record.Etat === 'off' ? 0.7 : 1
                }
              })}
            />
          </TabPane>
          <TabPane tab="Cartes" key="2">
            <Row gutter={[16, 16]}>
              {machineData.map((machine, index) => (
                <Col key={index} xs={24} sm={12} md={8} lg={6}>
                  <div style={{ position: "relative" }}>
                    <Card
                      hoverable={!!machine.id && machine.Etat !== 'off'}
                      onClick={() => machine.id && machine.Etat !== 'off' && handleMachineSelect(machine)}
                      style={{
                        borderTop: `2px solid ${getStatusColor(machine.status, machine)}`,
                        opacity: machine.Etat === 'off' ? 0.7 : 1,
                        cursor: machine.Etat === 'off' ? 'not-allowed' : 'pointer'
                      }}
                      title={machine.Etat === 'off' ? "Machine hors ligne. Détails non disponibles." : ""}
                    >
                      <div style={{ textAlign: "center" }}>
                        <Title level={4}>{machine.Machine_Name || "Machine"}</Title>
                        <Progress
                          type="dashboard"
                          percent={parseFloat(machine.TRS || "0")}
                          status={
                            parseFloat(machine.TRS) > 80
                              ? "success"
                              : parseFloat(machine.TRS) > 60
                                ? "normal"
                                : "exception"
                          }
                        />
                        {machine.Etat === "on" && (
                          <Badge status="processing" text="Session active" style={{ marginTop: 8 }} />
                        )}
                        <div style={{ marginTop: 8 }}>
                          <Text>Production: {parseFloat(machine.Quantite_Bon || 0)}</Text>
                        </div>
                      </div>
                    </Card>

                    {/* Apply "In Development" overlay to all machines except ID 1 */}
                    {machine.id !== 1 && (
                      <div className="soon-overlay">
                        <div className="soon-text">
                          En développement...
                        </div>
                        <Button type="default" size="small">
                          Configuration requise
                        </Button>
                      </div>
                    )}

                    {/* Special overlay for machines without ID */}
                    {!machine.id && (
                      <div className="soon-overlay">
                        <div className="soon-text">
                          En développement...
                        </div>
                        <Button type="default" size="small">
                          Configuration requise
                        </Button>
                      </div>
                    )}
                  </div>
                </Col>
              ))}
            </Row>
          </TabPane>
        </Tabs>
      </Card>



      {/* Help and tools */}
      <div style={{ position: "fixed", bottom: 20, right: 20, zIndex: 1000 }}>
        <Popover
          content={
            <div style={{ width: 250 }}>
              <p>
                <strong>Available tools:</strong>
              </p>
              <ul>
                <li>Machine view</li>
                <li>Detailed performance analysis</li>
                <li>Data export</li>
              </ul>
              <Button type="primary" block>
                User Guide
              </Button>
            </div>
          }
          title="Help and tools"
          trigger="click"
          placement="topRight"
        >
          <Button
            type="primary"
            shape="circle"
            icon={<InfoCircleOutlined />}
            size="large"
            style={{ boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)" }}
          />
        </Popover>
      </div>

      {/* Machine history modal */}
      <MachineDetailModal
        visible={modalVisible}
        machine={selectedMachine}
        machineHistory={machineHistory}
        loading={historyLoading}
        error={historyError}
        onClose={() => setModalVisible(false)}
        onRefresh={() => fetchMachineHistory(selectedMachine?.id)}
        darkMode={darkMode}
      />
    </div>
  );
};

export default DashboardContainer;