import React, { useState } from 'react';
import { Card, Row, Col, Statistic, Progress, Space, Button, Tooltip, Tag, Empty } from 'antd';
import { 
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  ExperimentOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';

const AIPartPerformanceCard = ({ loading, filters }) => {
  const [viewMode, setViewMode] = useState('performance');

  // Mock AI-generated data (will be replaced with real ML model outputs)
  const partPerformanceData = [
    {
      partNumber: "ABC-123",
      aiScore: 94.2,
      efficiency: 87.5,
      qualityIndex: 98.3,
      predictedYield: 92.1,
      trend: "improving",
      status: "optimal",
      mlConfidence: 96.8,
      recommendations: [
        "Maintain current parameters",
        "Consider 2% speed increase",
        "Quality metrics excellent"
      ]
    },
    {
      partNumber: "DEF-456",
      aiScore: 78.4,
      efficiency: 71.2,
      qualityIndex: 89.7,
      predictedYield: 85.3,
      trend: "declining",
      status: "attention",
      mlConfidence: 89.2,
      recommendations: [
        "Optimize cycle time",
        "Check material consistency",
        "Review operator training"
      ]
    },
    {
      partNumber: "GHI-789",
      aiScore: 91.7,
      efficiency: 89.3,
      qualityIndex: 95.1,
      predictedYield: 88.9,
      trend: "stable",
      status: "good",
      mlConfidence: 94.5,
      recommendations: [
        "Performance stable",
        "Minor temperature adjustment",
        "Continue monitoring"
      ]
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'optimal': return '#52c41a';
      case 'good': return '#1890ff';
      case 'attention': return '#fa8c16';
      case 'critical': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'improving': return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'declining': return <FallOutlined style={{ color: '#f5222d' }} />;
      case 'stable': return <EyeOutlined style={{ color: '#1890ff' }} />;
      default: return null;
    }
  };

  if (loading) {
    return (
      <div style={{ 
        height: '400px', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }}>
        <Empty description="Loading AI Analysis..." />
      </div>
    );
  }

  return (
    <div style={{ minHeight: '400px' }}>
      {/* Control Panel */}
      <div style={{
        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
        borderRadius: '8px',
        padding: '12px',
        marginBottom: '16px'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <ExperimentOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>
                AI Model: Production Optimizer v2.1
              </span>
              <Tag color="green" style={{ borderRadius: '6px' }}>
                Active
              </Tag>
            </Space>
          </Col>
          <Col>
            <Space size="small">
              <Button 
                size="small" 
                type={viewMode === 'performance' ? 'primary' : 'default'}
                onClick={() => setViewMode('performance')}
                style={{ borderRadius: '6px' }}
              >
                Performance
              </Button>
              <Button 
                size="small" 
                type={viewMode === 'predictions' ? 'primary' : 'default'}
                onClick={() => setViewMode('predictions')}
                style={{ borderRadius: '6px' }}
              >
                Predictions
              </Button>
              <Button 
                size="small" 
                type={viewMode === 'insights' ? 'primary' : 'default'}
                onClick={() => setViewMode('insights')}
                style={{ borderRadius: '6px' }}
              >
                AI Insights
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Performance View */}
      {viewMode === 'performance' && (
        <Row gutter={[16, 16]}>
          {partPerformanceData.map((part, index) => (
            <Col xs={24} key={index}>
              <Card
                size="small"
                style={{
                  borderRadius: '12px',
                  border: `2px solid ${getStatusColor(part.status)}20`,
                  background: `linear-gradient(135deg, ${getStatusColor(part.status)}05 0%, ${getStatusColor(part.status)}10 100%)`
                }}
              >
                <Row gutter={16} align="middle">
                  <Col flex="auto">
                    <Space align="center" size="middle">
                      <div>
                        <div style={{ 
                          fontWeight: '600', 
                          fontSize: '14px',
                          marginBottom: '4px'
                        }}>
                          {part.partNumber}
                        </div>
                        <Space size="small">
                          {getTrendIcon(part.trend)}
                          <span style={{ 
                            fontSize: '12px', 
                            color: '#8c8c8c',
                            textTransform: 'capitalize'
                          }}>
                            {part.trend}
                          </span>
                          <Tag 
                            color={getStatusColor(part.status)} 
                            style={{ 
                              borderRadius: '4px',
                              fontSize: '10px',
                              textTransform: 'uppercase'
                            }}
                          >
                            {part.status}
                          </Tag>
                        </Space>
                      </div>
                    </Space>
                  </Col>
                  
                  <Col>
                    <Statistic
                      title="AI Score"
                      value={part.aiScore}
                      suffix="%"
                      precision={1}
                      valueStyle={{ 
                        fontSize: '18px', 
                        color: getStatusColor(part.status),
                        fontWeight: '700'
                      }}
                    />
                  </Col>
                  
                  <Col>
                    <div style={{ width: '120px' }}>
                      <div style={{ 
                        fontSize: '11px', 
                        color: '#8c8c8c',
                        marginBottom: '4px'
                      }}>
                        Efficiency
                      </div>
                      <Progress
                        percent={part.efficiency}
                        size="small"
                        strokeColor={getStatusColor(part.status)}
                        showInfo={true}
                        format={percent => `${percent}%`}
                      />
                    </div>
                  </Col>
                  
                  <Col>
                    <div style={{ width: '120px' }}>
                      <div style={{ 
                        fontSize: '11px', 
                        color: '#8c8c8c',
                        marginBottom: '4px'
                      }}>
                        Quality Index
                      </div>
                      <Progress
                        percent={part.qualityIndex}
                        size="small"
                        strokeColor="#722ed1"
                        showInfo={true}
                        format={percent => `${percent}%`}
                      />
                    </div>
                  </Col>
                  
                  <Col>
                    <Tooltip title="ML Model Confidence">
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ 
                          fontSize: '12px', 
                          color: '#8c8c8c',
                          marginBottom: '2px'
                        }}>
                          Confidence
                        </div>
                        <div style={{ 
                          fontSize: '14px', 
                          fontWeight: '600',
                          color: part.mlConfidence > 90 ? '#52c41a' : '#fa8c16'
                        }}>
                          {part.mlConfidence}%
                        </div>
                      </div>
                    </Tooltip>
                  </Col>
                </Row>
              </Card>
            </Col>
          ))}
        </Row>
      )}

      {/* Predictions View */}
      {viewMode === 'predictions' && (
        <div style={{
          background: 'linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)',
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'center'
        }}>
          <TrophyOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
          <h3 style={{ color: '#fa8c16', marginBottom: '8px' }}>
            AI Prediction Engine
          </h3>
          <p style={{ color: '#8c8c8c', marginBottom: '16px' }}>
            Real-time yield predictions and optimization recommendations
          </p>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {partPerformanceData.map((part, index) => (
              <div key={index} style={{
                background: 'white',
                borderRadius: '8px',
                padding: '12px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>{part.partNumber}</span>
                <Space>
                  <span style={{ color: '#8c8c8c' }}>Predicted Yield:</span>
                  <span style={{ 
                    fontWeight: '600', 
                    color: part.predictedYield > 90 ? '#52c41a' : '#fa8c16'
                  }}>
                    {part.predictedYield}%
                  </span>
                </Space>
              </div>
            ))}
          </Space>
        </div>
      )}

      {/* AI Insights View */}
      {viewMode === 'insights' && (
        <div>
          <h4 style={{ marginBottom: '16px', color: '#1890ff' }}>
            AI-Generated Recommendations
          </h4>
          <Row gutter={[16, 16]}>
            {partPerformanceData.map((part, index) => (
              <Col xs={24} key={index}>
                <Card
                  title={part.partNumber}
                  size="small"
                  style={{
                    borderRadius: '12px',
                    border: `2px solid ${getStatusColor(part.status)}20`
                  }}
                  extra={
                    <Tag color={getStatusColor(part.status)}>
                      {part.status}
                    </Tag>
                  }
                >
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    {part.recommendations.map((rec, recIndex) => (
                      <div 
                        key={recIndex}
                        style={{
                          background: '#f8faff',
                          borderRadius: '6px',
                          padding: '8px 12px',
                          fontSize: '13px',
                          color: '#595959'
                        }}
                      >
                        • {rec}
                      </div>
                    ))}
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      )}
    </div>
  );
};

export default AIPartPerformanceCard;
