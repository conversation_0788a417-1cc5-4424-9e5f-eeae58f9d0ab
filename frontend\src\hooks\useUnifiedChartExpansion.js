import { useState, useCallback } from 'react';
import { useUnifiedChartConfig } from './useUnifiedChartConfig';

/**
 * Unified Chart Expansion Hook
 * Provides consistent chart expansion functionality across the application
 * Integrates with unified chart configuration system
 */
export const useUnifiedChartExpansion = (options = {}) => {
  const {
    chartType = 'bar',
    expandMode = 'modal', // 'modal' or 'inline'
    onExpand,
    onCollapse,
    data = []
  } = options;

  const [isExpanded, setIsExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get unified chart configuration
  const chartConfig = useUnifiedChartConfig({ chartType });

  // Check if click to expand is enabled
  const clickToExpandEnabled = chartConfig.interactionConfig.clickToExpand;

  // Handle expansion toggle
  const handleExpansion = useCallback(async () => {
    if (!clickToExpandEnabled) return;

    setIsLoading(true);

    try {
      // Small delay for large datasets
      if (data && data.length > 100) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (expandMode === 'modal') {
        setIsModalVisible(true);
        if (onExpand) onExpand();
      } else {
        const newExpandedState = !isExpanded;
        setIsExpanded(newExpandedState);

        if (newExpandedState && onExpand) {
          onExpand();
        } else if (!newExpandedState && onCollapse) {
          onCollapse();
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [expandMode, isExpanded, onExpand, onCollapse, data, clickToExpandEnabled]);

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setIsModalVisible(false);
    if (onCollapse) {
      onCollapse();
    }
  }, [onCollapse]);

  // Handle card click
  const handleCardClick = useCallback((e) => {
    // Only trigger expansion if clicking on the card itself, not controls
    if (e.target.closest('.ant-card-extra') || e.target.closest('.chart-container')) {
      return;
    }
    // Respect the "Click to Expand" setting
    if (clickToExpandEnabled) {
      handleExpansion();
    }
  }, [clickToExpandEnabled, handleExpansion]);

  // Get expansion button props
  const getExpansionButtonProps = useCallback(() => ({
    onClick: handleExpansion,
    loading: isLoading,
    disabled: !clickToExpandEnabled,
    type: isExpanded ? 'primary' : 'default',
    className: 'expand-button'
  }), [handleExpansion, isLoading, clickToExpandEnabled, isExpanded]);

  // Get card props for expansion
  const getCardProps = useCallback(() => ({
    hoverable: clickToExpandEnabled,
    onClick: handleCardClick,
    style: {
      cursor: clickToExpandEnabled ? 'pointer' : 'default',
      transition: 'all 0.3s ease',
      ...(isExpanded && {
        position: 'relative',
        zIndex: 10,
        boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
      }),
    },
    className: `unified-chart-expansion ${isExpanded ? 'expanded' : ''}`
  }), [clickToExpandEnabled, handleCardClick, isExpanded]);

  // Get chart props for rendering
  const getChartProps = useCallback((baseHeight = 300) => {
    const height = isExpanded && expandMode === 'inline' 
      ? Math.max(600, baseHeight * 1.5) 
      : baseHeight;

    return {
      height,
      enhanced: isExpanded,
      expanded: isExpanded,
      isModal: false,
      chartConfig: chartConfig,
      data: data
    };
  }, [isExpanded, expandMode, chartConfig, data]);

  // Get modal props
  const getModalProps = useCallback(() => ({
    visible: isModalVisible,
    onClose: handleModalClose,
    chartConfig: chartConfig
  }), [isModalVisible, handleModalClose, chartConfig]);

  return {
    // State
    isExpanded,
    isModalVisible,
    isLoading,
    clickToExpandEnabled,
    
    // Handlers
    handleExpansion,
    handleModalClose,
    handleCardClick,
    
    // Props getters
    getExpansionButtonProps,
    getCardProps,
    getChartProps,
    getModalProps,
    
    // Configuration
    chartConfig,
    expandMode,
    
    // Utilities
    setIsExpanded,
    setIsModalVisible,
    setIsLoading
  };
};

/**
 * Simple chart expansion hook for basic modal functionality
 * Useful for components that just need modal expansion without inline support
 */
export const useSimpleChartModal = (options = {}) => {
  const {
    chartType = 'bar',
    onExpand,
    onCollapse
  } = options;

  const [isModalVisible, setIsModalVisible] = useState(false);
  const chartConfig = useUnifiedChartConfig({ chartType });
  const clickToExpandEnabled = chartConfig.interactionConfig.clickToExpand;

  const openModal = useCallback(() => {
    if (!clickToExpandEnabled) return;
    setIsModalVisible(true);
    if (onExpand) onExpand();
  }, [clickToExpandEnabled, onExpand]);

  const closeModal = useCallback(() => {
    setIsModalVisible(false);
    if (onCollapse) onCollapse();
  }, [onCollapse]);

  return {
    isModalVisible,
    openModal,
    closeModal,
    clickToExpandEnabled,
    chartConfig
  };
};

export default useUnifiedChartExpansion;
