/**
 * Reusable Chart Configuration Utilities
 * Standardized approach for implementing unified chart settings across all dashboard pages
 * 
 * This file provides patterns and utilities that can be easily replicated in:
 * - ArretsDashboard.jsx
 * - AnalyticsDashboard.jsx
 * - Other future dashboard pages
 */

import { useCallback, useMemo } from 'react';
import { getDynamicColors } from './chartColors';
import { useSettings } from '../hooks/useSettings';

/**
 * Custom hook for unified chart configuration across dashboards
 * 
 * @returns {Object} Chart configuration utilities
 * 
 * @example
 * // In any dashboard component:
 * const chartConfig = useUnifiedDashboardCharts();
 * 
 * // Get color for a specific chart
 * const primaryColor = chartConfig.getChartColor(SOMIPEM_COLORS.PRIMARY_BLUE, 0);
 * 
 * // Get colors array for multiple chart elements
 * const colors = chartConfig.getChartColors([SOMIPEM_COLORS.PRIMARY_BLUE, SOMIPEM_COLORS.SECONDARY_BLUE]);
 * 
 * // Check if expand functionality is enabled
 * if (chartConfig.expandEnabled) {
 *   // Render with UnifiedChartExpansion
 * }
 */
export const useUnifiedDashboardCharts = () => {
  const { settings, charts, theme } = useSettings();

  // Get dynamic colors based on current settings
  const COLORS = useMemo(() => {
    return getDynamicColors(settings);
  }, [settings]);

  // Helper function to get chart color - either from unified scheme or original
  const getChartColor = useCallback((originalColor, colorIndex = 0) => {
    if (COLORS === null) {
      return originalColor; // Use original color in default mode
    }
    return COLORS[colorIndex % COLORS.length]; // Use unified color scheme
  }, [COLORS]);

  // Helper function to get chart colors array - either from unified scheme or original
  const getChartColors = useCallback((originalColors) => {
    if (COLORS === null) {
      return originalColors; // Use original colors in default mode
    }
    return COLORS; // Use unified color scheme
  }, [COLORS]);

  // Check if click-to-expand is enabled
  const expandEnabled = useMemo(() => {
    return charts?.interactions?.clickToExpand !== false;
  }, [charts]);

  // Get common chart properties
  const chartProperties = useMemo(() => ({
    showLegend: charts?.showLegend !== false,
    performanceMode: charts?.performanceMode === true,
    animationsEnabled: theme?.animationsEnabled && theme?.chartAnimations,
    colorScheme: charts?.colorScheme || 'brand'
  }), [charts, theme]);

  return {
    // Color utilities
    getChartColor,
    getChartColors,
    colors: COLORS,
    
    // Feature flags
    expandEnabled,
    
    // Chart properties
    ...chartProperties,
    
    // Raw settings (for advanced usage)
    settings,
    charts,
    theme
  };
};

/**
 * Standard UnifiedChartExpansion wrapper props
 * Provides consistent configuration for chart expansion across all dashboards
 * 
 * @param {string} title - Chart title
 * @param {Array} data - Chart data
 * @param {string} chartType - Chart type ('bar', 'line', 'pie')
 * @param {Object} additionalProps - Additional props to merge
 * @returns {Object} Standard props for UnifiedChartExpansion
 * 
 * @example
 * const expansionProps = getStandardChartExpansionProps(
 *   "Production Trends",
 *   productionData,
 *   "bar"
 * );
 * 
 * <UnifiedChartExpansion {...expansionProps}>
 *   <YourChartComponent />
 * </UnifiedChartExpansion>
 */
export const getStandardChartExpansionProps = (title, data, chartType = 'bar', additionalProps = {}) => {
  return {
    title,
    data,
    chartType,
    expandMode: "modal",
    exportEnabled: true,
    ...additionalProps
  };
};

/**
 * Documentation and Implementation Guide
 * 
 * ## How to Implement Unified Chart Settings in Any Dashboard
 * 
 * ### 1. Import Required Dependencies
 * ```javascript
 * import { useUnifiedDashboardCharts, getStandardChartExpansionProps } from '../utils/unifiedChartUtils';
 * import { useUnifiedChartConfig } from '../hooks/useUnifiedChartConfig';
 * import UnifiedChartExpansion from '../Components/charts/UnifiedChartExpansion/UnifiedChartExpansion';
 * ```
 * 
 * ### 2. Set Up Chart Configuration
 * ```javascript
 * const YourDashboard = () => {
 *   const chartConfig = useUnifiedDashboardCharts();
 *   
 *   // Now you have access to:
 *   // - chartConfig.getChartColor()
 *   // - chartConfig.getChartColors()
 *   // - chartConfig.expandEnabled
 *   // - chartConfig.showLegend
 *   // - etc.
 * };
 * ```
 * 
 * ### 3. Use Enhanced Chart Components
 * ```javascript
 * import {
 *   EnhancedQuantityBarChart,
 *   EnhancedTRSLineChart,
 *   EnhancedPieChart
 * } from '../Components/charts/ChartExpansion/EnhancedChartComponents';
 * ```
 * 
 * ### 4. Wrap Charts with UnifiedChartExpansion
 * ```javascript
 * const expansionProps = getStandardChartExpansionProps(
 *   "Your Chart Title",
 *   yourData,
 *   "bar"
 * );
 * 
 * <UnifiedChartExpansion {...expansionProps}>
 *   <EnhancedQuantityBarChart
 *     data={yourData}
 *     color={chartConfig.getChartColor(SOMIPEM_COLORS.PRIMARY_BLUE, 0)}
 *     // ... other props
 *   />
 * </UnifiedChartExpansion>
 * ```
 * 
 * ### 5. Ensure Settings Compliance
 * - ✅ All charts use `useUnifiedChartConfig()` internally
 * - ✅ Colors come from `chartConfig.getChartColor()` or `chartConfig.getChartColors()`
 * - ✅ Click-to-expand respects `chartConfig.expandEnabled`
 * - ✅ Data labels automatically work via settings
 * - ✅ Zero-based axis automatically works via settings
 * - ✅ All other chart settings work immediately
 * 
 * ### 6. Pattern for Tables and Statistics
 * ```javascript
 * // Use chart colors for table badges, statistics, etc.
 * <Badge 
 *   count={data.length} 
 *   style={{ backgroundColor: chartConfig.getChartColor(SOMIPEM_COLORS.PRIMARY_BLUE, 0) }} 
 * />
 * 
 * // Use for icons and other UI elements
 * <Icon style={{ color: chartConfig.getChartColor(SOMIPEM_COLORS.SECONDARY_BLUE, 1) }} />
 * ```
 * 
 * ## Implementation Checklist for New Dashboards
 * 
 * - [ ] Import `useUnifiedDashboardCharts` hook
 * - [ ] Use Enhanced Chart Components from `EnhancedChartComponents.jsx`
 * - [ ] Wrap all charts with `UnifiedChartExpansion`
 * - [ ] Use `getChartColor()` and `getChartColors()` for all color assignments
 * - [ ] Respect `expandEnabled` for conditional expansion features
 * - [ ] Test all chart settings produce immediate visual effects
 * - [ ] Verify color scheme changes affect all charts instantly
 * - [ ] Confirm data labels and zero-based axis work via settings
 * 
 * ## Benefits of This Approach
 * 
 * 1. **Consistency**: All dashboards use identical chart behavior
 * 2. **Maintainability**: Changes to chart system affect all dashboards
 * 3. **Immediate Effects**: All settings changes are instantly visible
 * 4. **Easy Replication**: Copy-paste pattern for new dashboards
 * 5. **Future-Proof**: New chart settings automatically work everywhere
 */

export default {
  useUnifiedDashboardCharts,
  getStandardChartExpansionProps
};
