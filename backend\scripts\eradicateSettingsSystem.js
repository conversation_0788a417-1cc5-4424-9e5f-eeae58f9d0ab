import { executeQuery } from '../utils/dbUtils.js';
import fs from 'fs';
import path from 'path';

/**
 * COMPLETE SETTINGS SYSTEM ERADICATION SCRIPT
 * This script completely removes all traces of the broken settings system
 * from the database, preparing for a complete rebuild
 */

async function eradicateSettingsSystem() {
  try {
    console.log('🔥 COMPLETE SETTINGS SYSTEM ERADICATION');
    console.log('=====================================');
    console.log('⚠️  WARNING: This will completely remove all settings data!');
    console.log('⚠️  Make sure you have a database backup before proceeding!');
    
    // Step 1: Create backup of current settings data
    console.log('\n📋 Step 1: Creating backup of current settings data...');
    
    const backupData = {};
    
    // Backup user_settings table
    try {
      const userSettingsResult = await executeQuery('SELECT * FROM user_settings');
      if (userSettingsResult.success) {
        backupData.user_settings = userSettingsResult.data;
        console.log(`✅ Backed up ${userSettingsResult.data.length} user_settings records`);
      }
    } catch (error) {
      console.log('⚠️ user_settings table not found or empty');
    }
    
    // Backup user_notification_preferences table
    try {
      const notificationPrefsResult = await executeQuery('SELECT * FROM user_notification_preferences');
      if (notificationPrefsResult.success) {
        backupData.user_notification_preferences = notificationPrefsResult.data;
        console.log(`✅ Backed up ${notificationPrefsResult.data.length} notification preferences records`);
      }
    } catch (error) {
      console.log('⚠️ user_notification_preferences table not found or empty');
    }
    
    // Backup user_report_subscriptions table
    try {
      const reportSubsResult = await executeQuery('SELECT * FROM user_report_subscriptions');
      if (reportSubsResult.success) {
        backupData.user_report_subscriptions = reportSubsResult.data;
        console.log(`✅ Backed up ${reportSubsResult.data.length} report subscriptions records`);
      }
    } catch (error) {
      console.log('⚠️ user_report_subscriptions table not found or empty');
    }
    
    // Backup system_settings table
    try {
      const systemSettingsResult = await executeQuery('SELECT * FROM system_settings');
      if (systemSettingsResult.success) {
        backupData.system_settings = systemSettingsResult.data;
        console.log(`✅ Backed up ${systemSettingsResult.data.length} system settings records`);
      }
    } catch (error) {
      console.log('⚠️ system_settings table not found or empty');
    }
    
    // Save backup to file
    const backupPath = path.join(process.cwd(), 'database_backups', `settings_backup_${Date.now()}.json`);
    const backupDir = path.dirname(backupPath);
    
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
    console.log(`💾 Backup saved to: ${backupPath}`);
    
    // Step 2: Drop all settings-related tables
    console.log('\n📋 Step 2: Dropping all settings-related tables...');
    
    const tablesToDrop = [
      'user_settings',
      'user_notification_preferences', 
      'user_report_subscriptions',
      'system_settings'
    ];
    
    for (const table of tablesToDrop) {
      try {
        const dropResult = await executeQuery(`DROP TABLE IF EXISTS ${table}`);
        if (dropResult.success) {
          console.log(`✅ Dropped table: ${table}`);
        } else {
          console.log(`⚠️ Failed to drop table ${table}:`, dropResult.error?.message);
        }
      } catch (error) {
        console.log(`⚠️ Error dropping table ${table}:`, error.message);
      }
    }
    
    // Step 3: Remove settings-related columns from other tables
    console.log('\n📋 Step 3: Checking for settings-related columns in other tables...');
    
    // Check if there are any settings-related columns in other tables
    const columnsToCheck = [
      { table: 'users', columns: ['default_theme', 'notification_preferences', 'dashboard_settings'] }
    ];
    
    for (const tableInfo of columnsToCheck) {
      for (const column of tableInfo.columns) {
        try {
          const columnCheckResult = await executeQuery(`
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'Testingarea51' 
            AND TABLE_NAME = ? 
            AND COLUMN_NAME = ?
          `, [tableInfo.table, column]);
          
          if (columnCheckResult.success && columnCheckResult.data[0].count > 0) {
            console.log(`🔍 Found settings column ${column} in table ${tableInfo.table}`);
            
            // Drop the column
            const dropColumnResult = await executeQuery(`ALTER TABLE ${tableInfo.table} DROP COLUMN ${column}`);
            if (dropColumnResult.success) {
              console.log(`✅ Dropped column ${column} from table ${tableInfo.table}`);
            } else {
              console.log(`⚠️ Failed to drop column ${column}:`, dropColumnResult.error?.message);
            }
          }
        } catch (error) {
          console.log(`⚠️ Error checking column ${column} in table ${tableInfo.table}:`, error.message);
        }
      }
    }
    
    // Step 4: Verify complete removal
    console.log('\n📋 Step 4: Verifying complete removal...');
    
    // Check that all settings tables are gone
    const tableCheckResult = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'Testingarea51' 
      AND TABLE_NAME IN ('user_settings', 'user_notification_preferences', 'user_report_subscriptions', 'system_settings')
    `);
    
    if (tableCheckResult.success && tableCheckResult.data.length === 0) {
      console.log('✅ All settings tables successfully removed');
    } else {
      console.log('❌ Some settings tables still exist:', tableCheckResult.data.map(row => row.TABLE_NAME));
    }
    
    // Check for any remaining settings-related columns
    const settingsColumnsResult = await executeQuery(`
      SELECT TABLE_NAME, COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'Testingarea51' 
      AND (
        COLUMN_NAME LIKE '%settings%' OR 
        COLUMN_NAME LIKE '%preference%' OR 
        COLUMN_NAME LIKE '%theme%' OR
        COLUMN_NAME LIKE '%notification%' OR
        COLUMN_NAME LIKE '%dashboard%'
      )
      AND TABLE_NAME NOT IN ('notifications', 'notification_escalations')
    `);
    
    if (settingsColumnsResult.success) {
      if (settingsColumnsResult.data.length === 0) {
        console.log('✅ No remaining settings-related columns found');
      } else {
        console.log('⚠️ Found remaining settings-related columns:');
        settingsColumnsResult.data.forEach(row => {
          console.log(`   - ${row.TABLE_NAME}.${row.COLUMN_NAME}`);
        });
      }
    }
    
    // Step 5: Database cleanup summary
    console.log('\n🎉 DATABASE CLEANUP COMPLETED!');
    console.log('===============================');
    console.log('✅ Settings tables dropped');
    console.log('✅ Settings columns removed');
    console.log('✅ Backup created');
    console.log('✅ Database verified clean');
    
    console.log('\n📊 Summary:');
    console.log(`   - Backup saved: ${backupPath}`);
    console.log(`   - Tables dropped: ${tablesToDrop.length}`);
    console.log('   - Database ready for new settings system');
    
    console.log('\n🚀 Next Steps:');
    console.log('   1. Run backend cleanup script');
    console.log('   2. Run frontend cleanup script');
    console.log('   3. Verify application runs without errors');
    console.log('   4. Begin new settings system implementation');
    
  } catch (error) {
    console.error('❌ Settings system eradication failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    process.exit(0);
  }
}

// Run the eradication
eradicateSettingsSystem();
