import React from "react";
import { Result, But<PERSON>, Typo<PERSON>, Space } from "antd";
import { useNavigate } from "react-router-dom";
import { HomeOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import { useTheme } from "../theme-context";

const { Text, Title } = Typography;

const ErrorPage = ({ status = "404", title, subTitle, isAuthenticated = false }) => {
  const navigate = useNavigate();
  const { darkMode } = useTheme();

  // Définir les valeurs par défaut en fonction du statut
  const getDefaultValues = () => {
    switch (status) {
      case "403":
        return {
          title: title || "403",
          subTitle: subTitle || "Désolé, vous n'êtes pas autorisé à accéder à cette page.",
        };
      case "404":
        return {
          title: title || "404",
          subTitle: subTitle || "Désolé, la page que vous recherchez n'existe pas.",
        };
      default:
        return {
          title: title || "Erreur",
          subTitle: subTitle || "Une erreur s'est produite.",
        };
    }
  };

  const defaultValues = getDefaultValues();

  return (
    <div
      style={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: "20px",
        backgroundColor: darkMode ? "#141414" : "#f0f2f5",
      }}
    >
      <Result
        status={status}
        title={<Title level={1}>{defaultValues.title}</Title>}
        subTitle={
          <Text style={{ fontSize: "18px", color: darkMode ? "#d9d9d9" : "#595959" }}>
            {defaultValues.subTitle}
          </Text>
        }
        extra={
          <Space size="middle">
            <Button 
              type="primary" 
              icon={<HomeOutlined />} 
              onClick={() => navigate(isAuthenticated ? "/home" : "/login")}
            >
              {isAuthenticated ? "Retour à l'accueil" : "Se connecter"}
            </Button>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate(-1)}
            >
              Retour
            </Button>
          </Space>
        }
      />
    </div>
  );
};

export default ErrorPage;