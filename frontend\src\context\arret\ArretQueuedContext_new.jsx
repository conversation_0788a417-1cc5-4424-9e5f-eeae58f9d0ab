import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

// Import queued GraphQL hook
import useQueuedStopGraphQL from '../../hooks/useQueuedStopGraphQL'
import { isAbortError } from '../../utils/error_handler'

// Import modular components
import { 
  CHART_COLORS, 
  INITIAL_SKELETON_STATE, 
  INITIAL_DATA_STATE 
} from './modules/constants.jsx'
import { useSkeletonManager } from './modules/skeletonManager.jsx'
import { useComputedValues } from './modules/computedValues.jsx'

// Extend dayjs with required plugins
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)

/**
 * ArretQueuedContext - Using queued GraphQL loading for progressive UI updates
 * 
 * This context uses the robust filter mechanism from Arrets2.jsx with simple state management
 * and proper filter handling to prevent interference between filters.
 */

const ArretQueuedContext = createContext()

export const useArretQueuedContext = () => {
  const context = useContext(ArretQueuedContext)
  if (!context) {
    console.error('⚠️  useArretQueuedContext: Context not found!')
    return null
  }
  return context
}

export const ArretQueuedProvider = ({ children }) => {
  console.log('🚀 ArretQueuedProvider: Initializing with Arrets2-style filter mechanism...');

  // Initialize the queued GraphQL hook
  const graphQL = useQueuedStopGraphQL();
  
  // Machine selection state (following Arrets2.jsx pattern)
  const [machineModels, setMachineModels] = useState([]);
  const [machineNames, setMachineNames] = useState([]);
  const [selectedMachineModel, setSelectedMachineModel] = useState("");
  const [selectedMachine, setSelectedMachine] = useState("");
  const [filteredMachineNames, setFilteredMachineNames] = useState([]);
  
  // Enhanced date filtering state (following Arrets2.jsx pattern)
  const [dateRangeType, setDateRangeType] = useState("month"); // "day", "week", "month"
  const [selectedDate, setSelectedDate] = useState(null);
  const [dateRangeDescription, setDateRangeDescription] = useState("");
  const [dateFilterActive, setDateFilterActive] = useState(false);
  
  // Data state
  const [stopsData, setStopsData] = useState([]);
  const [arretStats, setArretStats] = useState([]);
  const [topStopsData, setTopStopsData] = useState([]);
  const [durationTrend, setDurationTrend] = useState([]);
  const [machineComparison, setMachineComparison] = useState([]);
  const [operatorStats, setOperatorStats] = useState([]);
  const [stopReasons, setStopReasons] = useState([]);
  const [totalDuration, setTotalDuration] = useState(0);
  const [avgDuration, setAvgDuration] = useState(0);
  
  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // UI state
  const [isChartModalVisible, setIsChartModalVisible] = useState(false);
  const [chartModalContent, setChartModalContent] = useState(null);
  
  // Refs for component lifecycle and optimization (following Arrets2.jsx pattern)
  const isMounted = useRef(true);
  const pendingFetch = useRef(false);
  const filtersChanged = useRef(false); // Track filter changes that need data refresh
  
  // Skeleton states
  const [skeletonStates, setSkeletonStates] = useState(INITIAL_SKELETON_STATE);
  const skeletonManager = useSkeletonManager(skeletonStates, setSkeletonStates);

  // Helper function to format date range for display (following Arrets2.jsx pattern)
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" };

    const formattedDate = dayjs(date);

    if (rangeType === "day") {
      return {
        short: formattedDate.format("DD/MM"),
        full: formattedDate.format("DD/MM/YYYY")
      };
    } else if (rangeType === "week") {
      const start = formattedDate.startOf('isoWeek');
      const end = formattedDate.endOf('isoWeek');
      return {
        short: `${start.format("DD/MM")} - ${end.format("DD/MM")}`,
        full: `Semaine du ${start.format("DD/MM/YYYY")} au ${end.format("DD/MM/YYYY")}`
      };
    } else if (rangeType === "month") {
      return {
        short: formattedDate.format("MM/YYYY"),
        full: formattedDate.format("MMMM YYYY")
      };
    }

    return { short: "", full: "" };
  }, []);

  // Main function to fetch data (simplified like Arrets2.jsx)
  const fetchData = useCallback(async () => {
    // Prevent multiple simultaneous fetches
    if (pendingFetch.current) {
      console.log("🚫 Fetch already in progress, skipping...");
      return;
    }

    // SAFETY: Skip if we're unmounted
    if (!isMounted.current) {
      console.log('⏭️ Component unmounted, skipping fetch');
      return;
    }

    pendingFetch.current = true;
    setLoading(true);
    setError(null);

    console.log('🎯 fetchData called with filters:', {
      selectedMachineModel,
      selectedMachine,
      selectedDate: selectedDate?.format?.('YYYY-MM-DD'),
      dateRangeType,
      dateFilterActive
    });

    try {
      // Start appropriate skeleton loading
      skeletonManager.smartSkeletonForFilters(
        !!selectedMachineModel,
        !!selectedMachine,
        !!selectedDate
      );

      // Build filters for the GraphQL hook
      const filters = {
        model: selectedMachineModel || null,
        machine: selectedMachine || null,
        date: selectedDate ? selectedDate.format('YYYY-MM-DD') : null,
        startDate: selectedDate ? selectedDate.clone().startOf(dateRangeType).format('YYYY-MM-DD') : null,
        endDate: selectedDate ? selectedDate.clone().endOf(dateRangeType).format('YYYY-MM-DD') : null,
        dateRangeType: dateRangeType || 'month'
      };

      console.log('🎯 Built filters for GraphQL:', filters);

      // Use the queued loading approach - stats cards first, then table data, then charts
      console.log('📊 Step 1: Fetching stats cards...');
      const sidecards = await graphQL.getStopSidecards(filters);
      
      if (!isMounted.current) return;
      
      if (sidecards) {
        console.log('✅ Stats cards loaded:', sidecards);
        setArretStats(sidecards);
        skeletonManager.showSkeletonForComponent('sidecards', false);
      }

      console.log('📋 Step 2: Fetching table data...');
      const tableData = await graphQL.getStopTableData(filters);
      
      if (!isMounted.current) return;
      
      if (tableData) {
        console.log('✅ Table data loaded:', tableData.length, 'rows');
        setStopsData(tableData);
        skeletonManager.showSkeletonForComponent('table', false);
      }

      console.log('📊 Step 3: Fetching chart data...');
      const [topStops, durationTrend, machineComparison, operatorStats, stopReasons] = await Promise.all([
        graphQL.getTopStops(filters),
        graphQL.getDurationTrend(filters),
        graphQL.getMachineComparison(filters),
        graphQL.getOperatorStats(filters),
        graphQL.getStopReasons(filters)
      ]);

      if (!isMounted.current) return;

      // Update chart data
      if (topStops) {
        console.log('✅ Top stops loaded:', topStops.length, 'items');
        setTopStopsData(topStops);
        skeletonManager.showSkeletonForComponent('topStops', false);
      }

      if (durationTrend) {
        console.log('✅ Duration trend loaded:', durationTrend.length, 'items');
        setDurationTrend(durationTrend);
        skeletonManager.showSkeletonForComponent('durationTrend', false);
      }

      if (machineComparison) {
        console.log('✅ Machine comparison loaded:', machineComparison.length, 'items');
        setMachineComparison(machineComparison);
        skeletonManager.showSkeletonForComponent('machineComparison', false);
      }

      if (operatorStats) {
        console.log('✅ Operator stats loaded:', operatorStats.length, 'items');
        setOperatorStats(operatorStats);
        skeletonManager.showSkeletonForComponent('operatorStats', false);
      }

      if (stopReasons) {
        console.log('✅ Stop reasons loaded:', stopReasons.length, 'items');
        setStopReasons(stopReasons);
        skeletonManager.showSkeletonForComponent('stopReasons', false);
      }

      console.log('🎉 All data fetching complete!');

    } catch (error) {
      console.error('❌ Error fetching data:', error);
      
      if (!isMounted.current) return;
      
      if (!isAbortError(error)) {
        setError(error.message || 'Failed to fetch data');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
      pendingFetch.current = false;
      filtersChanged.current = false; // Reset filter change flag
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, graphQL, skeletonManager]);

  // Fetch machine models
  const fetchMachineModels = useCallback(async () => {
    try {
      const models = await graphQL.getMachineModels();
      if (models && isMounted.current) {
        setMachineModels(models);
      }
    } catch (error) {
      console.error('❌ Error fetching machine models:', error);
    }
  }, [graphQL]);

  // Fetch machine names
  const fetchMachineNames = useCallback(async () => {
    try {
      const names = await graphQL.getMachineNames();
      if (names && isMounted.current) {
        setMachineNames(names);
      }
    } catch (error) {
      console.error('❌ Error fetching machine names:', error);
    }
  }, [graphQL]);

  // Effect to filter machines by selected model (following Arrets2.jsx pattern)
  useEffect(() => {
    if (selectedMachineModel) {
      const filtered = machineNames.filter(name => 
        name.model === selectedMachineModel || 
        (typeof name === 'string' && name.includes(selectedMachineModel))
      );
      setFilteredMachineNames(filtered);
      
      // Clear machine selection if it's no longer valid
      if (selectedMachine && !filtered.find(m => 
        (typeof m === 'string' ? m : m.name) === selectedMachine
      )) {
        setSelectedMachine("");
      }
    } else {
      setFilteredMachineNames([]);
    }
  }, [selectedMachineModel, machineNames, selectedMachine]);

  // Initial data fetch and setup (following Arrets2.jsx pattern)
  useEffect(() => {
    // Set isMounted to true when component mounts
    isMounted.current = true;

    // Fetch initial data
    fetchMachineModels();
    fetchMachineNames();
    fetchData();

    // Cleanup function
    return () => {
      isMounted.current = false;
      setLoading(false);
      pendingFetch.current = false;
    };
  }, [fetchMachineModels, fetchMachineNames, fetchData]);

  // Effect to trigger data fetch when filters change (following Arrets2.jsx pattern)
  useEffect(() => {
    if (filtersChanged.current) {
      console.log('🔄 Filters changed, triggering data fetch...');
      fetchData();
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, fetchData]);

  // Filter change handlers (following Arrets2.jsx pattern)
  const handleDateRangeTypeChange = useCallback((value) => {
    console.log('📅 Date range type changed:', value);
    setDateRangeType(value);
    filtersChanged.current = true;

    // If a date is already selected, update the description
    if (selectedDate) {
      const { full } = formatDateRange(selectedDate, value);
      setDateRangeDescription(full);
    }
  }, [selectedDate, formatDateRange]);

  const handleDateChange = useCallback((date) => {
    console.log('📅 Date changed:', date?.format?.('YYYY-MM-DD'));
    
    if (!date) {
      setSelectedDate(null);
      setDateRangeDescription("");
      setDateFilterActive(false);
    } else {
      setSelectedDate(date);
      const { full } = formatDateRange(date, dateRangeType);
      setDateRangeDescription(full);
      setDateFilterActive(true);
    }
    filtersChanged.current = true;
  }, [dateRangeType, formatDateRange]);

  const resetDateFilter = useCallback(() => {
    console.log('🗑️ Resetting date filter...');
    setSelectedDate(null);
    setDateRangeDescription("");
    setDateFilterActive(false);
    filtersChanged.current = true;
  }, []);

  const handleMachineModelChange = useCallback((value) => {
    console.log('🏭 Machine model changed:', value);
    if (value === selectedMachineModel) return;
    
    setSelectedMachineModel(value);
    setSelectedMachine(""); // Clear machine selection when model changes
    filtersChanged.current = true;
  }, [selectedMachineModel]);

  const handleMachineChange = useCallback((value) => {
    console.log('🔧 Machine changed:', value);
    if (value === selectedMachine) return;
    
    setSelectedMachine(value);
    filtersChanged.current = true;
  }, [selectedMachine]);

  const handleResetMachineSelection = useCallback(() => {
    console.log('🗑️ Resetting machine selection...');
    setSelectedMachineModel("");
    setSelectedMachine("");
    filtersChanged.current = true;
  }, []);

  // Refresh data handler
  const handleRefresh = useCallback(() => {
    console.log('🔄 Manual refresh triggered...');
    fetchData();
  }, [fetchData]);

  // Chart modal handlers
  const openChartModal = useCallback((content) => {
    setChartModalContent(content);
    setIsChartModalVisible(true);
  }, []);

  const closeChartModal = useCallback(() => {
    setIsChartModalVisible(false);
    setChartModalContent(null);
  }, []);

  // Computed values using the hook
  const computedValues = useComputedValues(
    stopsData,
    arretStats,
    topStopsData,
    durationTrend,
    machineComparison,
    operatorStats,
    stopReasons
  );

  // Context value
  const contextValue = {
    // Filter state
    machineModels,
    machineNames,
    selectedMachineModel,
    selectedMachine,
    filteredMachineNames,
    dateRangeType,
    selectedDate,
    dateRangeDescription,
    dateFilterActive,
    
    // Data state
    stopsData,
    arretStats,
    topStopsData,
    durationTrend,
    machineComparison,
    operatorStats,
    stopReasons,
    totalDuration,
    avgDuration,
    
    // Loading and error states
    loading,
    error,
    skeletonStates,
    
    // UI state
    isChartModalVisible,
    chartModalContent,
    
    // Filter handlers
    handleDateRangeTypeChange,
    handleDateChange,
    resetDateFilter,
    handleMachineModelChange,
    handleMachineChange,
    handleResetMachineSelection,
    handleRefresh,
    
    // Chart modal handlers
    openChartModal,
    closeChartModal,
    
    // Computed values
    ...computedValues,
    
    // Additional context functions
    formatDateRange,
    fetchData
  };

  return (
    <ArretQueuedContext.Provider value={contextValue}>
      {children}
    </ArretQueuedContext.Provider>
  );
};

export default ArretQueuedContext;
