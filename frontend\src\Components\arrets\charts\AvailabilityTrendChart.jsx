import React, { memo } from 'react';
import { <PERSON>sponsive<PERSON><PERSON>r, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Area, AreaChart } from 'recharts';
import { Empty, Spin } from 'antd';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const AvailabilityTrendChart = memo(({ data = [], loading = false }) => {
  // Get unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    chartType: 'area'
  });

  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }
  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée de disponibilité disponible" />      </div>
    );
  }  // Process data to focus only on availability
  const processedData = safeData.map(item => {
    let disponibilite = parseFloat(item.disponibilite || item.availability || 0);
    
    // If the value is between 0 and 1, it's likely a decimal (e.g., 0.85 for 85%)
    // Convert it to percentage for proper display
    if (disponibilite > 0 && disponibilite <= 1) {
      disponibilite = disponibilite * 100;
    }
    
    return {
      date: item.date || item.Stop_Date,
      disponibilite: Math.round(disponibilite * 100) / 100, // Round to 2 decimals
      downtime: item.downtime || 0,
      stopCount: item.stopCount || 0
    };
  }).filter(item => item.date); // Only include items with valid dates

  // Calculate average availability for reference line
  const avgAvailability = processedData.reduce((sum, item) => sum + item.disponibilite, 0) / processedData.length;return (
    <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
      <AreaChart data={processedData} margin={chartConfig.margins}>
        <defs>
          <linearGradient id="availabilityGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={chartConfig.getPrimaryColor()} stopOpacity={0.4}/>
            <stop offset="95%" stopColor={chartConfig.getPrimaryColor()} stopOpacity={0.1}/>
          </linearGradient>
        </defs>
        <CartesianGrid {...chartConfig.gridConfig} />
        <XAxis
          dataKey="date"
          {...chartConfig.axisConfig}
          height={30}
          angle={-30}
          textAnchor="end"
          tickFormatter={(date) => {
            try {
              return new Date(date).toLocaleDateString('fr-FR', { 
                day: '2-digit', 
                month: '2-digit' 
              });
            } catch {
              return date;
            }
          }}
        />        <YAxis
          {...chartConfig.axisConfig}
          width={40}
          domain={[0, 100]}
          tickCount={5}
          tickFormatter={(value) => `${value}%`}
          label={{
            value: "Disponibilité (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: chartConfig.getTextColor(), fontSize: 12 },
            offset: 0
          }}
        />
        <Tooltip {...chartConfig.tooltipConfig} />
        {chartConfig.displayConfig.showLegend && <Legend {...chartConfig.legendConfig} />}        <Area
          type="monotone"
          dataKey="disponibilite"
          stroke={chartConfig.getPrimaryColor()}
          strokeWidth={2}
          fill="url(#availabilityGradient)"
          dot={{ fill: chartConfig.getPrimaryColor(), strokeWidth: 1, r: 3 }}
          activeDot={{ r: 5, fill: "#fff", stroke: chartConfig.getPrimaryColor(), strokeWidth: 2 }}
          {...chartConfig.animationConfig}
        />
        {/* Reference line for average */}
        <Line
          type="monotone"
          dataKey={() => avgAvailability}
          stroke={chartConfig.colors[1] || chartConfig.getPrimaryColor()}
          strokeWidth={2}
          strokeDasharray="5 5"
          dot={false}
          name={`Moyenne: ${avgAvailability.toFixed(1)}%`}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
});

AvailabilityTrendChart.displayName = 'AvailabilityTrendChart';

export default AvailabilityTrendChart;
