import { useSettings as useSettingsContext } from '../contexts/SettingsContext';
import { useMemo, useCallback } from 'react';

/**
 * Enhanced useSettings hook with additional utilities
 * Provides convenient methods for working with settings
 */
export function useSettings() {
  const context = useSettingsContext();

  // Memoized setting getters for common settings
  const theme = useMemo(() => {
    return context.getSetting('theme', {
      darkMode: false,
      compactMode: false,
      animationsEnabled: true,
      chartAnimations: true
    });
  }, [context.settings]);

  const tables = useMemo(() => {
    return context.getSetting('tables', {
      defaultPageSize: 20,
      pageSizeOptions: [10, 20, 50, 100],
      virtualizationThreshold: 100,
      showQuickJumper: true
    });
  }, [context.settings]);

  const charts = useMemo(() => {
    return context.getSetting('charts', {
      animationsEnabled: true,
      showLegend: true,
      colorScheme: 'brand',
      performanceMode: false
    });
  }, [context.settings]);

  const refresh = useMemo(() => {
    return context.getSetting('refresh', {
      dashboardInterval: 300,
      realtimeInterval: 60,
      autoRefreshEnabled: true,
      backgroundRefresh: true
    });
  }, [context.settings]);

  const notifications = useMemo(() => {
    return context.getSetting('notifications', {
      categories: {
        machine_alert: true,
        production: true,
        quality: true,
        maintenance: true,
        alert: true,
        info: true,
        updates: true
      },
      priorities: {
        critical: true,
        high: true,
        medium: true,
        low: true
      },
      delivery: {
        sse: true,
        email: false,
        browser: true
      },
      behavior: {
        sound: true,
        autoClose: false,
        autoCloseDelay: 5000,
        maxVisible: 5
      }
    });
  }, [context.settings]);

  const email = useMemo(() => {
    return context.getSetting('email', {
      enabled: false,
      frequency: 'immediate',
      template: 'standard'
    });
  }, [context.settings]);

  const reports = useMemo(() => {
    return context.getSetting('reports', {
      generation: {
        autoGenerate: false,
        format: 'pdf',
        quality: 'standard',
        includeCharts: true,
        includeTables: true
      }
    });
  }, [context.settings]);

  const performance = useMemo(() => {
    return context.getSetting('performance', {
      caching: {
        enabled: true,
        duration: 300,
        strategy: 'smart'
      },
      optimization: {
        lazyLoading: true,
        virtualization: true,
        compression: false
      }
    });
  }, [context.settings]);

  // Convenience methods for common operations
  const toggleDarkMode = useCallback(() => {
    context.updateSetting('theme.darkMode', !theme.darkMode);
  }, [context.updateSetting, theme.darkMode]);

  const toggleCompactMode = useCallback(() => {
    context.updateSetting('theme.compactMode', !theme.compactMode);
  }, [context.updateSetting, theme.compactMode]);

  const toggleAnimations = useCallback(() => {
    context.updateSetting('theme.animationsEnabled', !theme.animationsEnabled);
  }, [context.updateSetting, theme.animationsEnabled]);

  const setPageSize = useCallback((size) => {
    context.updateSetting('tables.defaultPageSize', size);
  }, [context.updateSetting]);



  const setColorScheme = useCallback((scheme) => {
    context.updateSetting('charts.colorScheme', scheme);
  }, [context.updateSetting]);

  const setRefreshInterval = useCallback((interval) => {
    context.updateSetting('refresh.dashboardInterval', interval);
  }, [context.updateSetting]);

  const toggleAutoRefresh = useCallback(() => {
    context.updateSetting('refresh.autoRefreshEnabled', !refresh.autoRefreshEnabled);
  }, [context.updateSetting, refresh.autoRefreshEnabled]);

  const toggleNotificationCategory = useCallback((category) => {
    const currentValue = context.getSetting(`notifications.categories.${category}`, true);
    context.updateSetting(`notifications.categories.${category}`, !currentValue);
  }, [context.updateSetting, context.getSetting]);

  const toggleNotificationPriority = useCallback((priority) => {
    const currentValue = context.getSetting(`notifications.priorities.${priority}`, true);
    context.updateSetting(`notifications.priorities.${priority}`, !currentValue);
  }, [context.updateSetting, context.getSetting]);

  const toggleEmailNotifications = useCallback(() => {
    context.updateSetting('email.enabled', !email.enabled);
  }, [context.updateSetting, email.enabled]);

  const setEmailFrequency = useCallback((frequency) => {
    context.updateSetting('email.frequency', frequency);
  }, [context.updateSetting]);

  const toggleReportAutoGeneration = useCallback(() => {
    context.updateSetting('reports.generation.autoGenerate', !reports.generation.autoGenerate);
  }, [context.updateSetting, reports.generation.autoGenerate]);

  const setReportFormat = useCallback((format) => {
    context.updateSetting('reports.generation.format', format);
  }, [context.updateSetting]);

  const toggleCaching = useCallback(() => {
    context.updateSetting('performance.caching.enabled', !performance.caching.enabled);
  }, [context.updateSetting, performance.caching.enabled]);

  const setCacheDuration = useCallback((duration) => {
    context.updateSetting('performance.caching.duration', duration);
  }, [context.updateSetting]);

  const toggleLazyLoading = useCallback(() => {
    context.updateSetting('performance.optimization.lazyLoading', !performance.optimization.lazyLoading);
  }, [context.updateSetting, performance.optimization.lazyLoading]);

  const toggleVirtualization = useCallback(() => {
    context.updateSetting('performance.optimization.virtualization', !performance.optimization.virtualization);
  }, [context.updateSetting, performance.optimization.virtualization]);

  // Check if a notification should be shown based on settings
  const shouldShowNotification = useCallback((category, priority) => {
    const categoryEnabled = context.getSetting(`notifications.categories.${category}`, true);
    const priorityEnabled = context.getSetting(`notifications.priorities.${priority}`, true);
    return categoryEnabled && priorityEnabled;
  }, [context.getSetting]);

  // Check if email notifications should be sent
  const shouldSendEmail = useCallback((category, priority) => {
    if (!email.enabled) return false;
    
    const categoryEnabled = context.getSetting(`email.notifications.categories.${category}`, false);
    const priorityEnabled = context.getSetting(`email.notifications.priorities.${priority}`, false);
    return categoryEnabled && priorityEnabled;
  }, [context.getSetting, email.enabled]);

  // Get CSS classes based on theme settings
  const getThemeClasses = useCallback(() => {
    const classes = [];
    
    if (theme.darkMode) classes.push('dark');
    if (theme.compactMode) classes.push('compact');
    if (!theme.animationsEnabled) classes.push('no-animations');
    
    return classes.join(' ');
  }, [theme]);

  // Get table configuration
  const getTableConfig = useCallback(() => {
    return {
      pageSize: tables.defaultPageSize,
      pageSizeOptions: tables.pageSizeOptions,
      showQuickJumper: tables.showQuickJumper,
      virtualized: tables.virtualizationThreshold > 0
    };
  }, [tables]);

  // Get chart configuration
  const getChartConfig = useCallback(() => {
    return {
      animated: charts.animationsEnabled && theme.animationsEnabled,
      showLegend: charts.showLegend,
      colorScheme: charts.colorScheme,
      performanceMode: charts.performanceMode
    };
  }, [charts, theme.animationsEnabled]);

  return {
    // Original context methods and state
    ...context,
    
    // Structured settings
    theme,
    tables,
    charts,
    refresh,
    notifications,
    email,
    reports,
    performance,
    
    // Convenience methods
    toggleDarkMode,
    toggleCompactMode,
    toggleAnimations,
    setPageSize,
    setColorScheme,
    setRefreshInterval,
    toggleAutoRefresh,
    toggleNotificationCategory,
    toggleNotificationPriority,
    toggleEmailNotifications,
    setEmailFrequency,
    toggleReportAutoGeneration,
    setReportFormat,
    toggleCaching,
    setCacheDuration,
    toggleLazyLoading,
    toggleVirtualization,
    
    // Utility methods
    shouldShowNotification,
    shouldSendEmail,
    getThemeClasses,
    getTableConfig,
    getChartConfig
  };
}

export default useSettings;
