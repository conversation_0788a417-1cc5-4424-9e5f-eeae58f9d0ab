import express from "express"
import pool from "../db.js" // Import the promise-based pool
import { corsOptions } from "../middleware/cors.js"
import cors from "cors"
import { indexMachineStops } from "../middleware/elasticsearchMiddleware.js"
import redisRestApiEnhancer from '../services/RedisRestApiEnhancer.js';

const router = express.Router()

// Helper function to handle machine filtering consistently
const addMachineFilters = (conditions, queryParams, req) => {
  const machineModel = req.query.model
  const machineName = req.query.machine

  if (machineModel) {
    conditions.push(`Machine_Name LIKE ?`)
    queryParams.push(`${machineModel}%`)
  } else if (machineName) {
    conditions.push(`Machine_Name = ?`)
    queryParams.push(machineName)
  } else {
    // Default to IPS01 if no filter is provided
    conditions.push(`Machine_Name = ?`)
    queryParams.push("IPS01")
  }
}

// Add a helper function to handle date range filtering consistently
const addDateRangeFilter = (conditions, queryParams, req) => {
  const dateParam = req.query.date || req.params.date
  const dateRangeType = req.query.dateRangeType || "day" // Default to day view

  if (dateParam) {
    if (dateRangeType === "day") {
      // Single day view
      conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`)
      queryParams.push(dateParam)
    } else if (dateRangeType === "week") {
      // Week view - from the start of the week to the end of the week
      conditions.push(`
        DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') >=
        DATE_SUB(?, INTERVAL WEEKDAY(?) DAY)
      `)
      queryParams.push(dateParam, dateParam)

      conditions.push(`
        DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') <=
        DATE_ADD(DATE_SUB(?, INTERVAL WEEKDAY(?) DAY), INTERVAL 6 DAY)
      `)
      queryParams.push(dateParam, dateParam)
    } else if (dateRangeType === "month") {
      // Month view - from the start of the month to the end of the month
      conditions.push(`
        YEAR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) = YEAR(?)
      `)
      queryParams.push(dateParam)

      conditions.push(`
        MONTH(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) = MONTH(?)
      `)
      queryParams.push(dateParam)
    }
  }
}

// Improved stop table endpoint with better filtering
router.get("/StopTableMould", cors(corsOptions), async (req, res) => {
  try {
    const dateFilter = req.query.date

    let query = `SELECT * FROM machine_stop_table_mould`
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add date filter if provided
    if (dateFilter) {
      conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`)
      queryParams.push(dateFilter)
    }

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    const [results] = await pool.execute(query, queryParams)

    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Enhance the top-5-stops endpoint to properly handle date range filtering
router.get("/top-5-stops", cors(corsOptions), async (req, res) => {
  try {
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day" // Default to day view

    let query = `
      SELECT
        Code_Stop AS stopName,
        COUNT(*) AS count
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add date range filter if provided
    if (dateParam) {
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Code_Stop
      ORDER BY count DESC
      LIMIT 5
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved stop statistics endpoint
router.get("/arrets", cors(corsOptions), async (req, res) => {
  try {
    const dateRange = req.query.dateRange

    let query = `
      SELECT
        DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS Stop_Date,
        COUNT(*) AS Total_Stops
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add date range filter if provided
    if (dateRange) {
      const [startDate, endDate] = dateRange.split(",")
      if (startDate && endDate) {
        conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') BETWEEN ? AND ?`)
        queryParams.push(startDate, endDate)
      } else if (startDate) {
        conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') >= ?`)
        queryParams.push(startDate)
      }
    }

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Stop_Date
      ORDER BY Stop_Date DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved unique dates endpoint
router.get("/unique-dates", cors(corsOptions), async (req, res) => {
  try {
    let query = `
      SELECT DISTINCT
        DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS date
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += ` ORDER BY date DESC`

    const [results] = await pool.execute(query, queryParams)
    res.json(results.map((row) => row.date))
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved stop details by date endpoint
router.get("/arrets-table/:date", cors(corsOptions), indexMachineStops, async (req, res) => {
  try {
    const rawDate = req.params.date

    let query = `
      SELECT
        Machine_Name,
        Regleur_Prenom,
        Code_Stop,
        Date_Insert,
        Debut_Stop,
        Fin_Stop_Time,
        Part_NO,
        TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        ) AS duration_minutes
      FROM machine_stop_table_mould
      WHERE
        DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?
    `
    const queryParams = [rawDate]
    const conditions = [`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`]

    // Apply machine filtering with default - need to modify approach since we have a WHERE clause already
    const machineModel = req.query.model
    const machineName = req.query.machine

    if (machineModel) {
      conditions.push(`Machine_Name LIKE ?`)
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      conditions.push(`Machine_Name = ?`)
      queryParams.push(machineName)
    } else {
      // Default to IPS01 if no filter is provided
      conditions.push(`Machine_Name = ?`)
      queryParams.push("IPS01")
    }

    // Rebuild query with all conditions
    query = `
      SELECT
        Machine_Name,
        Regleur_Prenom,
        Code_Stop,
        Date_Insert,
        Debut_Stop,
        Fin_Stop_Time,
        Part_NO,
        TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        ) AS duration_minutes
      FROM machine_stop_table_mould
      WHERE ${conditions.join(" AND ")}
      ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved arrets-production endpoint
router.get("/arrets-production/:date", cors(corsOptions), async (req, res) => {
  try {
    const rawDate = req.params.date

    let query = `
      SELECT
        Machine_Name,
        Regleur_Prenom,
        Code_Stop,
        Date_Insert,
        Debut_Stop,
        Fin_Stop_Time,
        Part_NO,
        TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        ) AS duration_minutes
      FROM machine_stop_table_mould
      WHERE
        DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?
    `
    const queryParams = [rawDate]
    const conditions = [`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`]

    // Apply machine filtering with default - need to modify approach since we have a WHERE clause already
    const machineModel = req.query.model
    const machineName = req.query.machine

    if (machineModel) {
      conditions.push(`Machine_Name LIKE ?`)
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      conditions.push(`Machine_Name = ?`)
      queryParams.push(machineName)
    } else {
      // Default to IPS01 if no filter is provided
      conditions.push(`Machine_Name = ?`)
      queryParams.push("IPS01")
    }

    // Rebuild query with all conditions
    query = `
      SELECT
        Machine_Name,
        Regleur_Prenom,
        Code_Stop,
        Date_Insert,
        Debut_Stop,
        Fin_Stop_Time,
        Part_NO,
        TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        ) AS duration_minutes
      FROM machine_stop_table_mould
      WHERE ${conditions.join(" AND ")}
      ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved sidecards-arret endpoint
router.get("/sidecards-arret", cors(corsOptions), async (req, res) => {
  try {
    const dateFilter = req.query.date

    let query = `SELECT COUNT(*) AS Arret_Totale FROM machine_stop_table_mould`
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add date filter if provided
    if (dateFilter) {
      conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`)
      queryParams.push(dateFilter)
    }

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved sidecards-arretnonDeclare endpoint
router.get("/sidecards-arretnonDeclare", cors(corsOptions), async (req, res) => {
  try {
    const dateFilter = req.query.date
    const queryParams = []
    const conditions = [`Code_Stop = 'arrét non déclaré'`]

    // Apply machine filtering with default
    const machineModel = req.query.model
    const machineName = req.query.machine

    if (machineModel) {
      conditions.push(`Machine_Name LIKE ?`)
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      conditions.push(`Machine_Name = ?`)
      queryParams.push(machineName)
    } else {
      // Default to IPS01 if no filter is provided
      conditions.push(`Machine_Name = ?`)
      queryParams.push("IPS01")
    }

    // Add date filter if provided
    if (dateFilter) {
      conditions.push(`DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') = ?`)
      queryParams.push(dateFilter)
    }

    // Rebuild the query with conditions
    const query = `
      SELECT COUNT(*) AS Arret_Totale_nondeclare
      FROM machine_stop_table_mould
      WHERE ${conditions.join(" AND ")}
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved stop-reasons endpoint with no date (default)
router.get("/stop-reasons", cors(corsOptions), async (req, res) => {
  try {
    let query = `
      SELECT
        Code_Stop AS reason,
        COUNT(*) AS count
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Code_Stop
      ORDER BY count DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  }
  catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
});

// Improved stop-reasons endpoint for specific date
router.get("/stop-reasons/:date", cors(corsOptions), async (req, res) => {
  try {
    const date = req.params.date

    let query = `
      SELECT
        Code_Stop AS reason,
        COUNT(*) AS count
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Add date filter if provided
    if (date) {
      // Use the addDateRangeFilter helper function
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Code_Stop
      ORDER BY count DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved stop-duration-trend endpoint with default
router.get("/stop-duration-trend", cors(corsOptions), async (req, res) => {
  try {
    let query = `
      SELECT
        HOUR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) AS hour,
        AVG(TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        )) AS avgDuration
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY hour
      ORDER BY hour
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
});

// Improved stop-duration-trend endpoint with specific date
router.get("/stop-duration-trend/:date", cors(corsOptions), async (req, res) => {
  try {
    const date = req.params.date

    let query = `
      SELECT
        HOUR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) AS hour,
        AVG(TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        )) AS avgDuration
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Add date filter if provided
    if (date) {
      // Use the addDateRangeFilter helper function
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY hour
      ORDER BY hour
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved machine-stop-comparison endpoint with default - Enhanced with Redis caching
router.get("/machine-stop-comparison", cors(corsOptions), redisRestApiEnhancer.createHeavyAggregationEndpoint('/api/machine-stop-comparison', {
  ttl: 900, // 15 minutes TTL for heavy aggregation
  keyPrefix: 'stops'
})(async (req, res) => {
  try {
    let query = `
      SELECT
        Machine_Name,
        COUNT(*) AS stops,
        SUM(TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        )) AS totalDuration
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Machine_Name
      ORDER BY stops DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
}));

// Improved machine-stop-comparison endpoint with specific date
router.get("/machine-stop-comparison/:date", cors(corsOptions), async (req, res) => {
  try {
    const date = req.params.date

    let query = `
      SELECT
        Machine_Name,
        COUNT(*) AS stops,
        SUM(TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        )) AS totalDuration
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Add date filter if provided
    if (date) {
      // Use the addDateRangeFilter helper function
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Apply machine filtering with default
    // For machine comparison, we need to handle filtering differently
    // We'll filter by model but not force a single machine
    const machineModel = req.query.model
    const machineName = req.query.machine

    if (machineModel) {
      conditions.push(`Machine_Name LIKE ?`)
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      conditions.push(`Machine_Name = ?`)
      queryParams.push(machineName)
    } else {
      // For comparison, default to showing all IPS machines if no filter
      conditions.push(`Machine_Name LIKE ?`)
      queryParams.push(`IPS%`)
    }

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Machine_Name
      ORDER BY stops DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Improved operator-stop-stats endpoint with default - Enhanced with Redis caching
router.get("/operator-stop-stats", cors(corsOptions), redisRestApiEnhancer.createHeavyAggregationEndpoint('/api/operator-stop-stats', {
  ttl: 900, // 15 minutes TTL for operator aggregation
  keyPrefix: 'operators'
})(async (req, res) => {
  try {
    let query = `
      SELECT
        Regleur_Prenom AS operator,
        COUNT(*) AS interventions,
        SUM(TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        )) AS totalDuration
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Regleur_Prenom
      ORDER BY interventions DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
}));

// Improved operator-stop-stats endpoint with specific date
router.get("/operator-stop-stats/:date", cors(corsOptions), async (req, res) => {
  try {
    const date = req.params.date

    let query = `
      SELECT
        Regleur_Prenom AS operator,
        COUNT(*) AS interventions,
        SUM(TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        )) AS totalDuration
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Add date filter if provided
    if (date) {
      // Use the addDateRangeFilter helper function
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Regleur_Prenom
      ORDER BY interventions DESC
    `

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Fix: Improved machine models endpoint with better regex pattern
router.get("/stops/machine-models", cors(corsOptions), async (_, res) => {
  try {
    // Use a simpler approach to extract model prefixes that will work for both IPS and CCM24
    const [results] = await pool.execute(`
      SELECT DISTINCT
        CASE
          WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
          THEN REGEXP_REPLACE(Machine_Name, '[0-9].*$', '')
          ELSE Machine_Name
        END AS model
      FROM machine_stop_table_mould
      WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
      ORDER BY model
    `)

    // Log the results for debugging
    console.log("Machine models from database:", results)

    // If no results, return default models
    if (!results || results.length === 0) {
      console.log("No machine models found in database, returning defaults")
      return res.json([{ model: "IPS" }, { model: "CCM24" }])
    }

    res.json(results)
  } catch (err) {
    console.error("Database error in machine-models endpoint:", err)
    // Return default values if the query fails
    res.json([{ model: "IPS" }, { model: "CCM24" }])
  }
})

// Fix: Improved machine names endpoint with better error handling
router.get("/stops/machine-names", cors(corsOptions), async (req, res) => {
  try {
    // Add an optional model filter
    const machineModel = req.query.model
    let query = `
      SELECT DISTINCT Machine_Name
      FROM machine_stop_table_mould
      WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
    `
    const queryParams = []

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    }

    query += ` ORDER BY Machine_Name`

    const [results] = await pool.execute(query, queryParams)

    // Log the results for debugging
    console.log("Machine names from database:", results)
    console.log("For model filter:", machineModel)

    // If no results, return default machines based on the model
    if (!results || results.length === 0) {
      console.log("No machine names found in database, returning defaults")
      if (machineModel === "CCM24") {
        return res.json([
          { Machine_Name: "CCM2401" },
          { Machine_Name: "CCM2402" },
          { Machine_Name: "CCM2403" },
          { Machine_Name: "CCM2404" },
        ])
      } else {
        // Default to IPS machines
        return res.json([
          { Machine_Name: "IPS01" },
          { Machine_Name: "IPS02" },
          { Machine_Name: "IPS03" },
          { Machine_Name: "IPS04" },
        ])
      }
    }

    res.json(results)
  } catch (err) {
    console.error("Database error in machine-names endpoint:", err)
    // Return default values based on the model if the query fails
    if (req.query.model === "CCM24") {
      res.json([
        { Machine_Name: "CCM24SB" },
        { Machine_Name: "CCM24SC" },
        { Machine_Name: "CCM24SA" },
        { Machine_Name: "CCM24SD" },
      ])
    } else {
      res.json([
        { Machine_Name: "IPS01" },
        { Machine_Name: "IPS02" },
        { Machine_Name: "IPS03" },
        { Machine_Name: "IPS04" },
      ])
    }
  }
})

// Enhance the arrets-by-range endpoint to properly handle date range filtering
router.get("/arrets-by-range", cors(corsOptions), async (req, res) => {
  try {
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day" // Default to day view

    // Determine the appropriate date grouping based on the range type
    let dateGrouping = "DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d')"

    if (dateRangeType === "week") {
      // Group by week number for week view
      dateGrouping =
        "CONCAT(YEAR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')), '-', WEEK(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), 1))"
    } else if (dateRangeType === "month") {
      // Group by month for month view
      dateGrouping = "DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-01')"
    }

    let query = `
      SELECT
        ${dateGrouping} AS Stop_Date,
        COUNT(*) AS Total_Stops
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add date range filter if provided
    if (dateParam) {
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += `
      GROUP BY Stop_Date
      ORDER BY Stop_Date DESC
      LIMIT 30
    `

    const [results] = await pool.execute(query, queryParams)

    // For week and month views, convert the grouped date format back to a standard date
    if (dateRangeType === "week" || dateRangeType === "month") {
      const processedResults = results.map((item) => {
        if (dateRangeType === "week") {
          // Convert year-week format to a date (first day of that week)
          const [year, week] = item.Stop_Date.split("-")
          const firstDayOfWeek = new Date(year, 0, 1 + (week - 1) * 7)
          item.Stop_Date = firstDayOfWeek.toISOString().split("T")[0]
        }
        // For month view, the date is already in YYYY-MM-01 format
        return item
      })
      res.json(processedResults)
    } else {
      res.json(results)
    }
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

// Update the arrets-table endpoint to support date range filtering with default
router.get("/arrets-table-range", cors(corsOptions), indexMachineStops, async (req, res) => {
  try {
    // Set default dateRangeType in the request object for potential use in other functions
    req.query.dateRangeType = req.query.dateRangeType || "day" // Default to day view

    // Query logic for default date
    let query = `
      SELECT
        Machine_Name,
        Regleur_Prenom,
        Code_Stop,
        Date_Insert,
        Debut_Stop,
        Fin_Stop_Time,
        Part_NO,
        TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        ) AS duration_minutes
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += ` ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC`

    // Add limit if specified
    if (req.query.limit) {
      query += ` LIMIT ?`
      queryParams.push(parseInt(req.query.limit))
    }

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
});

// Update the arrets-table endpoint to support date range filtering with specific date
router.get("/arrets-table-range/:date", cors(corsOptions), indexMachineStops, async (req, res) => {
  try {
    const rawDate = req.params.date
    // Use dateRangeType in the addDateRangeFilter function
    req.query.dateRangeType = req.query.dateRangeType || "day" // Default to day view

    let query = `
      SELECT
        Machine_Name,
        Regleur_Prenom,
        Code_Stop,
        Date_Insert,
        Debut_Stop,
        Fin_Stop_Time,
        Part_NO,
        TIMESTAMPDIFF(MINUTE,
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
        ) AS duration_minutes
      FROM machine_stop_table_mould
    `
    const queryParams = []
    const conditions = []

    // Add date range filter based on the range type
    if (rawDate) {
      addDateRangeFilter(conditions, queryParams, req)
    }

    // Apply machine filtering with default
    addMachineFilters(conditions, queryParams, req)

    // Add WHERE clause if there are conditions
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(" AND ")
    }

    query += ` ORDER BY STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s') DESC`

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed", details: err.message })
  }
})

export default router
