/**
 * Authentication middleware with Redis session caching
 */
import jwt from 'jsonwebtoken';
import { executeQuery } from '../utils/dbUtils.js';
import { sendError } from '../utils/responseUtils.js';
import { parsePermissions } from '../utils/permissionUtils.js';
import redisSessionService from '../services/RedisSessionService.js';

/**
 * Authentication middleware with ngrok tunnel support
 */
const auth = async (req, res, next) => {
  console.log('🔐 Auth middleware - checking authentication...');
  console.log('🔐 Request URL:', req.originalUrl);
  console.log('🔐 Request method:', req.method);
  console.log('🔐 Request origin:', req.headers.origin);
  console.log('🔐 Environment:', {
    NODE_ENV: process.env.NODE_ENV,
    NGROK_ENABLED: process.env.NGROK_ENABLED,
    hasJwtSecret: !!process.env.JWT_SECRET
  });

  // ngrok tunnel authentication (standard JWT-based auth)
  console.log('🔐 Standard authentication check');

  // Fall back to traditional JWT authentication
  console.log('🔐 Using traditional JWT authentication');

  // Prioritize token from HTTP-only cookie
  // Check both main token and backup token for dual environment support
  // Fall back to headers for backward compatibility during transition
  const token = req.cookies.token ||
                req.cookies.token_backup ||
                req.header('Authorization')?.replace('Bearer ', '') ||
                req.header('x-auth-token');

  console.log('🔐 Token sources:', {
    cookie: !!req.cookies.token,
    cookieBackup: !!req.cookies.token_backup,
    authorization: !!req.header('Authorization'),
    xAuthToken: !!req.header('x-auth-token'),
    hasToken: !!token
  });

  // Check if no token
  if (!token) {
    console.log('❌ No authentication token found');
    return sendError(res, 'No token, authorization denied', 401);
  }

  try {
    // Initialize Redis session service if not already done
    if (!redisSessionService.initialized) {
      await redisSessionService.initialize();
    }

    // Try to get session from Redis first
    console.log('🔍 Checking Redis session cache...');
    const sessionResult = await redisSessionService.getSession(token);

    if (sessionResult.success && sessionResult.data) {
      console.log('📦 Session found in Redis cache');

      // Verify JWT token is still valid
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        console.error('❌ JWT_SECRET not configured');
        return sendError(res, 'Server configuration error', 500);
      }

      try {
        const decoded = jwt.verify(token, jwtSecret);
        console.log('✅ JWT token verified for user:', decoded.id);

        // Set user id in req.user
        req.user = { id: decoded.id };

        // Get user from database with role and department information
        const { success, data: users, error } = await executeQuery(
          `SELECT u.id, u.username, u.email, u.role, u.department_id, u.permissions,
                  r.name as role_name, r.permissions as role_permissions,
                  d.name as department_name
           FROM users u
           LEFT JOIN roles r ON u.role_id = r.id
           LEFT JOIN departments d ON u.department_id = d.id
           WHERE u.id = ?`,
          [decoded.id]
        );

        if (!success) {
          return sendError(res, 'Database error', 500, error);
        }

        if (users.length === 0) {
          // User not found - invalidate session
          await redisSessionService.invalidateSession(token);
          return sendError(res, 'User not found', 404);
        }

        // Continue with user data processing...
        const user = users[0];

        // Parse permissions
        user.permissions = parsePermissions(user.permissions);
        user.role_permissions = parsePermissions(user.role_permissions);

        // Combine all permissions
        const allPermissions = [...user.permissions, ...user.role_permissions];

        // Set complete user object in request
        req.user = {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          role_name: user.role_name,
          department_id: user.department_id,
          department_name: user.department_name,
          permissions: allPermissions,
          sessionData: sessionResult.data
        };

        console.log('✅ Authentication successful via Redis session cache');
        return next();

      } catch (jwtError) {
        console.log('❌ JWT token invalid - invalidating Redis session');
        await redisSessionService.invalidateSession(token);
        return sendError(res, 'Invalid token or token expired', 401);
      }
    }

    // Fallback to traditional JWT verification if Redis session not found
    console.log('🔄 Redis session not found - falling back to JWT verification');

    // Verify token
    console.log('🔐 Verifying JWT token...');
    const jwtSecret = process.env.JWT_SECRET;

    if (!jwtSecret) {
      console.error('❌ JWT_SECRET not configured');
      return sendError(res, 'Server configuration error', 500);
    }

    const decoded = jwt.verify(token, jwtSecret);
    console.log('✅ JWT token verified for user:', decoded.id);

    // Set user id in req.user
    req.user = { id: decoded.id };

    // Get user from database with role and department information
    const { success, data: users, error } = await executeQuery(
      `SELECT u.id, u.username, u.email, u.role, u.department_id, u.permissions,
              r.name as role_name, r.permissions as role_permissions,
              d.name as department_name
       FROM users u
       LEFT JOIN roles r ON u.role_id = r.id
       LEFT JOIN departments d ON u.department_id = d.id
       WHERE u.id = ?`,
      [decoded.id]
    );

    if (!success) {
      return sendError(res, 'Database error', 500, error);
    }

    if (users.length === 0) {
      return sendError(res, 'User not found', 404);
    }

    const user = users[0];

    // Parse permissions
    user.permissions = parsePermissions(user.permissions);
    user.role_permissions = parsePermissions(user.role_permissions);

    try {
      // Import dynamically to avoid circular dependency
      const { getAllRolePermissions } = await import('../utils/roleHierarchy.js');

      // Get role permissions from hierarchy
      let hierarchyPermissions = [];
      if (user.role) {
        hierarchyPermissions = getAllRolePermissions(user.role);
      }

      // Add hierarchy permissions to user object
      user.hierarchy_permissions = hierarchyPermissions;

      // Combine all permissions
      const allPermissions = [
        ...user.permissions,
        ...user.role_permissions,
        ...hierarchyPermissions
      ].filter(Boolean); // Remove null/undefined values

      // Add combined permissions to user object
      user.all_permissions = allPermissions;

      // Set userContext
      req.userContext = {
        departmentId: user.department_id,
        departmentName: user.department_name,
        roleName: user.role,
        permissions: allPermissions
      };
    } catch (error) {
      console.error('Error setting up permissions in auth middleware:', error);
      // If there's an error, set empty arrays to avoid undefined errors
      user.hierarchy_permissions = [];
      user.all_permissions = [...user.permissions, ...user.role_permissions].filter(Boolean);
    }

    // Set user in request
    req.user = user;

    console.log('✅ Authentication successful for user:', req.user.username || req.user.email);
    next();
  } catch (err) {
    console.error('❌ Authentication error:', err.message);
    if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
      return sendError(res, 'Token is not valid', 401);
    }
    return sendError(res, 'Authentication error', 500, err);
  }
};


export default auth;