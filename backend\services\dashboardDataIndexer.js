/**
 * Dashboard Data Indexer Service
 * 
 * This service handles indexing machine stop data from MySQL into Elasticsearch
 * for fast dashboard queries with proper da      console.log(`Found ${stopData.length} stop records to index`);
      
      if (!stopData || stopData.length === 0) {
        console.log('No data found in machine_stop_table_mould');
        return { success: true, indexed: 0, errors: 0 };
      }

      // Process in batches
      let indexed = 0;
      let errors = 0;
      
      for (let i = 0; i < stopData.length; i += this.batchSize) {
        const batch = stopData.slice(i, i + this.batchSize);
        const transformedBatch = batch
          .map(record => this.transformStopData(record))
          .filter(record => record !== null);n and normalization.
 */

import { createRequire } from 'module';
import { esClient } from '../config/elasticsearch.js';
import elasticsearchService from './elasticsearchService.js';
const require = createRequire(import.meta.url);

import { executeQuery } from '../utils/dbUtils.js';

class DashboardDataIndexer {
  constructor() {
    this.indexName = 'dashboard-machine-stops';
    this.batchSize = 1000;
  }

  /**
   * Transform MySQL data to Elasticsearch format
   */
  transformStopData(mysqlRecord) {
    try {
      // Parse date with flexible format handling
      const parseDate = (dateStr) => {
        if (!dateStr) return null;
        
        // Handle various date formats from database
        const cleaned = dateStr.toString().trim();
        
        // Try different date patterns
        const patterns = [
          /(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2}):(\d{2})/,  // DD/MM/YYYY HH:MM:SS
          /(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})/,         // DD/MM/YYYY HH:MM
          /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/,          // YYYY-MM-DD HH:MM:SS
          /(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})/             // ISO format
        ];
        
        for (const pattern of patterns) {
          const match = cleaned.match(pattern);
          if (match) {
            if (pattern === patterns[0]) { // DD/MM/YYYY HH:MM:SS format
              const [, day, month, year, hour, minute, second] = match;
              return new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute}:${second}`);
            } else if (pattern === patterns[1]) { // DD/MM/YYYY HH:MM format (no seconds)
              const [, day, month, year, hour, minute] = match;
              return new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute}:00`);
            } else {
              return new Date(cleaned);
            }
          }
        }
        
        // Fallback: try direct Date parsing
        const fallbackDate = new Date(cleaned);
        return isNaN(fallbackDate.getTime()) ? null : fallbackDate;
      };

      // Calculate duration in minutes
      const calculateDuration = (start, end) => {
        if (!start || !end) return null;
        const startDate = parseDate(start);
        const endDate = parseDate(end);
        if (!startDate || !endDate) return null;
        return Math.round((endDate - startDate) / (1000 * 60)); // minutes
      };

      const startTime = parseDate(mysqlRecord.start_time);
      const endTime = parseDate(mysqlRecord.end_time);
      const duration = calculateDuration(mysqlRecord.start_time, mysqlRecord.end_time);

      // Generate a unique ID since the table doesn't have one
      const uniqueId = `${mysqlRecord.machine_name || 'unknown'}_${mysqlRecord.date_insert || Date.now()}_${mysqlRecord.start_time || ''}`.replace(/[^a-zA-Z0-9_-]/g, '_');

      return {
        id: uniqueId,
        machineId: mysqlRecord.machine_name || 'Unknown',
        machineName: mysqlRecord.machine_name || 'Unknown',
        stopReason: mysqlRecord.stop_code || 'Unknown',
        stopCode: mysqlRecord.stop_code,
        startTime: startTime ? startTime.toISOString() : null,
        endTime: endTime ? endTime.toISOString() : null,
        duration: duration, // in minutes
        shift: 'Unknown', // Not available in source table
        operator: mysqlRecord.operator || 'Unknown',
        description: mysqlRecord.stop_code || '',
        partNo: mysqlRecord.part_no || '',
        dateInsert: mysqlRecord.date_insert,
        category: this.categorizeStopReason(mysqlRecord.stop_code),
        severity: this.calculateSeverity(duration),
        timestamp: startTime ? startTime.toISOString() : new Date().toISOString(),
        // Additional fields for analytics
        dayOfWeek: startTime ? startTime.getDay() : null,
        hourOfDay: startTime ? startTime.getHours() : null,
        weekNumber: startTime ? this.getWeekNumber(startTime) : null,
        monthYear: startTime ? `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')}` : null
      };
    } catch (error) {
      console.error('Error transforming stop data:', error, mysqlRecord);
      return null;
    }
  }

  /**
   * Categorize stop reasons for better analytics
   */
  categorizeStopReason(reason) {
    if (!reason) return 'Unknown';
    
    const reasonLower = reason.toLowerCase();
    
    if (reasonLower.includes('maintenance') || reasonLower.includes('maint')) {
      return 'Maintenance';
    } else if (reasonLower.includes('panne') || reasonLower.includes('breakdown') || reasonLower.includes('failure')) {
      return 'Equipment Failure';
    } else if (reasonLower.includes('matière') || reasonLower.includes('material') || reasonLower.includes('approvisionnement')) {
      return 'Material Issue';
    } else if (reasonLower.includes('qualité') || reasonLower.includes('quality') || reasonLower.includes('defect')) {
      return 'Quality Issue';
    } else if (reasonLower.includes('changement') || reasonLower.includes('setup') || reasonLower.includes('réglage')) {
      return 'Setup/Changeover';
    } else if (reasonLower.includes('pause') || reasonLower.includes('break') || reasonLower.includes('repas')) {
      return 'Scheduled Break';
    } else {
      return 'Other';
    }
  }

  /**
   * Calculate severity based on duration
   */
  calculateSeverity(durationMinutes) {
    if (!durationMinutes || durationMinutes < 0) return 'Unknown';
    
    if (durationMinutes <= 5) return 'Low';
    if (durationMinutes <= 30) return 'Medium';
    if (durationMinutes <= 120) return 'High';
    return 'Critical';
  }

  /**
   * Get ISO week number
   */
  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }

  /**
   * Index all machine stop data from MySQL to Elasticsearch
   */
  async indexAllStopData() {
    try {
      console.log('Starting full indexing of machine stop data...');
      
      // Get all data from machine_stop_table_mould
      const query = `
        SELECT 
          Machine_Name as machine_name,
          Date_Insert as date_insert,
          Part_NO as part_no,
          Code_Stop as stop_code,
          Debut_Stop as start_time,
          Fin_Stop_Time as end_time,
          Regleur_Prenom as operator
        FROM machine_stop_table_mould 
        WHERE Date_Insert IS NOT NULL
        ORDER BY Date_Insert DESC
      `;
      
      const results = await executeQuery(query);
      const stopData = results.success ? results.data : [];
      console.log(`Found ${stopData.length} stop records to index`);
      
      if (results.length === 0) {
        console.log('No data found in machine_stop_table_mould');
        return { success: true, indexed: 0, errors: 0 };
      }

      // Process in batches
      let indexed = 0;
      let errors = 0;
      
      for (let i = 0; i < stopData.length; i += this.batchSize) {
        const batch = stopData.slice(i, i + this.batchSize);
        const transformedBatch = batch
          .map(record => this.transformStopData(record))
          .filter(record => record !== null);
        
        if (transformedBatch.length > 0) {
          try {
            await elasticsearchService.indexBulkData(this.indexName, transformedBatch);
            indexed += transformedBatch.length;
            console.log(`Indexed batch ${Math.floor(i / this.batchSize) + 1}: ${transformedBatch.length} records`);
          } catch (error) {
            console.error(`Error indexing batch ${Math.floor(i / this.batchSize) + 1}:`, error);
            errors += batch.length;
          }
        }
      }
      
      console.log(`Indexing complete. Indexed: ${indexed}, Errors: ${errors}`);
      return { success: true, indexed, errors };
      
    } catch (error) {
      console.error('Error in indexAllStopData:', error);
      throw error;
    }
  }

  /**
   * Index incremental data (last N hours)
   */
async indexRecentData(hours = 1) {
  try {
    console.log(`Indexing recent data from last ${hours} hours...`);

    const query = `
      SELECT 
        Machine_Name,
        Date_Insert,
        Part_NO,
        Code_Stop,
        Debut_Stop,
        Fin_Stop_Time,
        Regleur_Prenom
      FROM machine_stop_table_mould 
      WHERE Date_Insert IS NOT NULL
      ORDER BY Date_Insert DESC
    `;

    const results = await executeQuery(query);
    const stopData = results.success ? results.data : [];
    console.log(`Found ${stopData.length} recent records to index`);

    if (stopData.length === 0) {
      return { success: true, indexed: 0, errors: 0 };
    }

    const transformedRecords = stopData
      .map(record => this.transformStopData(record))
      .filter(record => record !== null);

    if (transformedRecords.length > 0) {
      await elasticsearchService.indexBulkData(this.indexName, transformedRecords);
      console.log(`Indexed ${transformedRecords.length} recent records`);
    }

    return { success: true, indexed: transformedRecords.length, errors: 0 };

  } catch (error) {
    console.error('Error in indexRecentData:', error);
    throw error;
  }
}

  /**
   * Create or update the Elasticsearch index mapping
   */
  async createIndexMapping() {
    try {
      // Check if index already exists
      const indexExists = await esClient.indices.exists({ index: this.indexName });
      
      if (indexExists) {
        console.log(`Index ${this.indexName} already exists, skipping creation`);
        return;
      }

      const mapping = {
        properties: {
          id: { type: 'keyword' },
          machineId: { type: 'keyword' },
          machineName: { type: 'keyword' },
          stopReason: { 
            type: 'text',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          stopCode: { type: 'keyword' },
          startTime: { type: 'date' },
          endTime: { type: 'date' },
          duration: { type: 'integer' },
          shift: { type: 'keyword' },
          operator: { type: 'keyword' },
          description: { 
            type: 'text',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          category: { type: 'keyword' },
          severity: { type: 'keyword' },
          timestamp: { type: 'date' },
          dayOfWeek: { type: 'integer' },
          hourOfDay: { type: 'integer' },
          weekNumber: { type: 'integer' },
          monthYear: { type: 'keyword' }
        }
      };
      
      await esClient.indices.create({
        index: this.indexName,
        mappings: mapping
      });
      console.log(`Created index mapping for ${this.indexName}`);
      
    } catch (error) {
      console.error('Error creating index mapping:', error);
      throw error;
    }
  }

  /**
   * Setup periodic indexing (every 15 minutes)
   */
  schedulePeriodicIndexing() {
    console.log('Setting up periodic indexing every 15 minutes...');
    
    // Initial full index if needed
    this.indexAllStopData().catch(error => {
      console.error('Error in initial indexing:', error);
    });
    
    // Schedule incremental updates every 15 minutes
    setInterval(async () => {
      try {
        await this.indexRecentData(0.5); // Last 30 minutes with overlap
      } catch (error) {
        console.error('Error in periodic indexing:', error);
      }
    }, 15 * 60 * 1000); // 15 minutes
  }

  /**
   * Health check - verify index status
   */
  async checkIndexHealth() {
    try {
      const stats = await elasticsearchService.getIndexStats(this.indexName);
      const count = await elasticsearchService.getDocumentCount(this.indexName);
      
      return {
        indexExists: true,
        documentCount: count,
        indexSize: stats._all?.total?.store?.size_in_bytes || 0,
        status: 'healthy'
      };
    } catch (error) {
      return {
        indexExists: false,
        documentCount: 0,
        indexSize: 0,
        status: 'error',
        error: error.message
      };
    }
  }
}

export default new DashboardDataIndexer();
