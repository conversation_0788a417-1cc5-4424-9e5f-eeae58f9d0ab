import React from 'react';
import { Card, Space } from 'antd';
import { LineChartOutlined } from '@ant-design/icons';

const BusinessIntelligenceSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <LineChartOutlined style={{ fontSize: '64px', color: '#52c41a' }} />
          <h2 style={{ color: '#52c41a', margin: 0 }}>Business Intelligence</h2>
          <p style={{ color: '#8c8c8c' }}>Financial analytics and ROI optimization coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default BusinessIntelligenceSection;
