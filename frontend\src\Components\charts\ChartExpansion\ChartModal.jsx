import React, { useState, useCallback, useEffect } from "react";
import { Mo<PERSON>, Spin } from "antd";
import {
  FullscreenOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import PropTypes from "prop-types";
import "./ExpandableChart.css";

/**
 * Dedicated Modal Component for Chart Fullscreen Display
 * This component is designed to handle chart display in fullscreen mode
 * with proper data flow and state isolation
 */
const ChartModal = ({
  visible,
  onClose,
  title,
  data,
  chartType,
  children,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // Handle modal close
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === "Escape" && visible) {
        handleClose();
      }
    };

    if (visible) {
      document.addEventListener("keydown", handleKeyPress);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [visible, handleClose]);



  // Calculate modal chart height (convert vh to pixels)
  const getModalChartHeight = () => {
    const viewportHeight = window.innerHeight;
    return Math.floor(viewportHeight * 0.7); // 70% of viewport height
  };

  // Render the chart with modal-specific props
  const renderModalChart = () => {
    if (isLoading) {
      return (
        <div
          style={{
            height: getModalChartHeight(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Spin size="large" tip="Chargement du graphique..." />
        </div>
      );
    }

    if (!data || data.length === 0) {
      return (
        <div
          style={{
            height: getModalChartHeight(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column'
          }}
        >
          <div>Aucune donnée disponible</div>
        </div>
      );
    }

    const chartHeight = getModalChartHeight();

    // Clone the child chart component with modal-specific props
    return (
      <div
        id="modal-chart-container"
        className="chart-container modal-chart"
        style={{
          height: chartHeight,
          width: "100%",
          position: "relative",
          overflow: "visible",
          background: "transparent",
        }}
      >
        <div
          id="modal-chart"
          style={{
            height: "100%",
            width: "100%",
          }}
        >
          {React.cloneElement(children, {
            // Pass original props first
            ...children.props,
            // Override with modal-specific props
            data: data, // Always use original data in modal
            height: chartHeight,
            enhanced: true, // Always enhanced in modal
            expanded: true, // Always expanded in modal
            isModal: true, // Flag to indicate modal mode
            // Modal-specific chart configuration
            chartConfig: {
              showAllLabels: true,
              labelInterval: 0,
              maxBarSize: 60,
              strokeWidth: 3,
              dotSize: 6,
            },
          })}
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
          padding: '12px 20px',
          margin: '-16px -24px 0 -24px',
          borderRadius: '8px 8px 0 0'
        }}>
          <span style={{
            color: 'white',
            fontSize: '18px',
            fontWeight: 'bold',
            textShadow: '0 1px 2px rgba(0,0,0,0.3)',
            marginRight: '8px'
          }}>
            {title}
          </span>
          <FullscreenOutlined style={{
            color: "white",
            fontSize: '16px',
            textShadow: '0 1px 2px rgba(0,0,0,0.3)'
          }} />
        </div>
      }
      open={visible}
      onCancel={handleClose}
      width="95vw"
      style={{
        top: 20,
        maxWidth: "95vw",
        padding: 0
      }}
      bodyStyle={{
        height: "80vh",
        padding: "24px",
        margin: 0,
        overflow: "auto",
        background: "#fafafa"
      }}
      footer={null}
      destroyOnClose={true} // Destroy on close to ensure clean state
      className="chart-expansion-modal"
      mask={true}
      maskClosable={true}
      keyboard={true}
      zIndex={9999}
      getContainer={() => document.body}
      centered={true}
      closeIcon={
        <div style={{
          color: '#fff',
          fontSize: '18px',
          fontWeight: 'bold',
          background: 'rgba(0,0,0,0.6)',
          borderRadius: '50%',
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '2px solid rgba(255,255,255,0.8)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
          position: 'relative',
          zIndex: 10001
        }}>
          <CloseOutlined />
        </div>
      }
    >
      <div style={{
        height: "75vh",
        width: "100%",
        background: "white",
        borderRadius: "8px",
        padding: "20px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        overflow: "hidden"
      }}>
        {renderModalChart()}
      </div>
    </Modal>
  );
};

ChartModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  data: PropTypes.array.isRequired,
  chartType: PropTypes.string,
  children: PropTypes.element.isRequired,
};

export default ChartModal;
