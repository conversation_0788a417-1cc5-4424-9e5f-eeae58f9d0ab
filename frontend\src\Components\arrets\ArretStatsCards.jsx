import React, { useMemo } from 'react';
import { Row, Col, Card, Statistic, Spin, Progress, Space, Popover, Tag } from 'antd';
import { 
  AlertOutlined, 
  WarningOutlined,
  ClockCircleOutlined, 
  ToolOutlined,
  InfoCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import SOMIPEM_COLORS from '../../styles/brand-colors';
import dayjs from 'dayjs';
import { formatFrenchInteger, formatFrenchDecimal, formatFrenchPercentage } from '../../utils/numberFormatter';

// Chart colors matching Arrets2.jsx
const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
}

/**
 * ArretStatsCards - Displays key statistics for machine stops
 * Now fully integrated with the modular computed values system
 */

const ArretStatsCards = () => {
  const context = useArretQueuedContext();
  if (!context) {
    return <div>Context not available</div>;
  }
  
  const { 
    // Loading and UI states
    loading = true, 
    essentialLoading = false,
    dateFilterActive = false,
    dateRangeDescription = '',
    
    // Date filter data
    selectedDate,
    dateRangeType,
    stopsData = [],
    selectedMachine,
    selectedMachineModel,
    
    // Direct API response values (from backend sidecards)
    totalStops = 0,
    undeclaredStops = 0,
    
    // Computed values from the modular system
    computedValues = {},
    operatorStats = []
  } = context;

  // Extract computed values from the modular system (for duration calculations)
  const {
    chartDataCalculations = {},
    globalDataCalculations = {},
    filteredStopsData = [],
    avgDuration = 0,
    totalDuration = 0
  } = computedValues;

  // Use direct API response values for main stats (totalStops, undeclaredStops from backend)
  // Use computed values for duration calculations
  const displayTotalStops = totalStops; // Direct from API
  const displayUndeclaredStops = undeclaredStops; // Direct from API
  const displayTotalDuration = chartDataCalculations.totalDuration || globalDataCalculations.totalDuration || totalDuration;
  const displayAvgDuration = chartDataCalculations.averageDuration || globalDataCalculations.averageDuration || avgDuration;
  
  // Calculate percentage of non-declared stops using computed values
  const percentageNonDeclared = useMemo(() => {
    // Fallback calculation if needed
    if (displayTotalStops > 0 && displayUndeclaredStops >= 0) {
      return formatFrenchPercentage((displayUndeclaredStops / displayTotalStops) * 100, 1);
    }
    
    return '0';
  }, [displayTotalStops, displayUndeclaredStops]);

  // Calculate date range display text for the filter card
  const dateRangeText = useMemo(() => {
    if (!selectedDate || !dateFilterActive) return '';

    const formatDate = (date) => date.format('DD/MM/YYYY');

    switch (dateRangeType) {
      case 'day':
        return formatDate(selectedDate);
      case 'week':
        const weekStart = selectedDate.clone().startOf('isoWeek');
        const weekEnd = selectedDate.clone().endOf('isoWeek');
        return `${formatDate(weekStart)} - ${formatDate(weekEnd)}`;
      case 'month':
        return selectedDate.format('MMMM YYYY');
      default:
        return formatDate(selectedDate);
    }
  }, [selectedDate, dateRangeType, dateFilterActive]);

  // Calculate filtered stops count from computed values
  const filteredStopsCount = useMemo(() => {
    // Use filteredStopsData from computed values
    if (filteredStopsData && filteredStopsData.length >= 0) {
      return filteredStopsData.length;
    }
    
    // Otherwise use displayTotalStops when date filter is active
    if (dateFilterActive && displayTotalStops > 0) {
      return displayTotalStops;
    }
    
    return 0;
  }, [filteredStopsData, dateFilterActive, displayTotalStops]);

  // Calculate machine filter text
  const machineFilterText = useMemo(() => {
    if (selectedMachine) {
      return selectedMachine;
    } else if (selectedMachineModel) {
      return `Modèle ${selectedMachineModel}`;
    }
    return 'Toutes les machines';
  }, [selectedMachine, selectedMachineModel]);

  // Create date filter card when filter is active AND has results
  const dateFilterCard = useMemo(() => {
    // Don't show filter card if no filters active or no filtered results
    if (!dateFilterActive || filteredStopsCount === 0) return null;
    
    return {
      title: "Arrêts Filtrés",
      value: formatFrenchInteger(filteredStopsCount),
      icon: <CalendarOutlined />,
      color: SOMIPEM_COLORS.SECONDARY_BLUE, // #3B82F6 for filter highlights
      suffix: "arrêts",
      isDateFilter: true
    };
  }, [dateFilterActive, filteredStopsCount]);

  // Extended statistics using modular computed values - ALWAYS show global data for consistency
  const extendedStats = useMemo(() => {
    

    // Build stats exactly as shown in the image - USING GLOBAL DATA with French formatting
    const stats = [
      {
        title: "Arrêts Totaux",
        value: formatFrenchInteger(displayTotalStops),
        suffix: "",
        icon: <AlertOutlined />,
        color: SOMIPEM_COLORS.PRIMARY_BLUE // #1E3A8A
      },
      {
        title: "Arrêts Non Déclarés",
        value: formatFrenchInteger(displayUndeclaredStops),
        suffix: "",
        icon: <WarningOutlined />,
        color: SOMIPEM_COLORS.PRIMARY_BLUE // #1E3A8A
      },
      {
        title: "Durée Totale",
        value: formatFrenchInteger(Math.round(displayTotalDuration)),
        suffix: "min",
        icon: <ClockCircleOutlined />,
        color: SOMIPEM_COLORS.PRIMARY_BLUE // #1E3A8A
      },
      {
        title: "Durée Moyenne",
        value: formatFrenchDecimal(displayAvgDuration, 1),
        suffix: "min",
        icon: <ClockCircleOutlined />,
        color: SOMIPEM_COLORS.PRIMARY_BLUE // #1E3A8A
      },
      {
        title: "Interventions",
        // FIXED: Use total from operatorStats without filter dependency
        value: formatFrenchInteger(operatorStats?.reduce((sum, op) => sum + (op.interventions || 0), 0) || 0),
        suffix: "",
        icon: <ToolOutlined />,
        color: SOMIPEM_COLORS.PRIMARY_BLUE // #1E3A8A
      }
    ];

    return stats;
  }, [displayTotalStops, displayUndeclaredStops, displayTotalDuration, displayAvgDuration, operatorStats]);

  // Insert date filter card as third card when active (keeping the functionality)
  const statsToDisplay = useMemo(() => {
    if (!dateFilterCard) return extendedStats;
    
    // Insert date filter card as third card (index 2) - after "Arrêts Non Déclarés"
    const result = [...extendedStats];
    result.splice(2, 0, dateFilterCard);
    return result;
  }, [extendedStats, dateFilterCard]);

  // Debug logging to track computed values data flow
  React.useEffect(() => {
    
  }, [
    totalStops, undeclaredStops, displayTotalStops, displayUndeclaredStops, 
    displayTotalDuration, displayAvgDuration, chartDataCalculations, 
    percentageNonDeclared, filteredStopsCount, dateFilterActive, selectedDate, 
    selectedMachine, selectedMachineModel, operatorStats, statsToDisplay
  ]);
  // Calculate responsive column spans based on number of cards
  const getColSpan = () => {
    const cardCount = statsToDisplay.length;
    if (cardCount === 5) {
      return { xs: 24, sm: 12, md: 8, lg: 4, xl: 4 };
    } else if (cardCount === 6) {
      return { xs: 24, sm: 12, md: 8, lg: 4, xl: 4 };
    } else {
      return { xs: 24, sm: 12, md: 6, lg: 6, xl: 6 };
    }
  };

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      {statsToDisplay.map((stat, index) => (
        <Col 
          key={index} 
          {...getColSpan()}
        >
          <Card
            bordered={false}
            hoverable
            style={{
              backgroundColor: "#FFFFFF", // White background
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
              borderTop: `3px solid ${stat.color || SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue top border
              height: "100%",
              minHeight: "120px",
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
              ...(stat.isDateFilter && {
                backgroundColor: '#FFFFFF', // Keep white background for filter cards
                border: `1px solid ${SOMIPEM_COLORS.SECONDARY_BLUE}`, // Secondary Blue border for filter
                borderTop: `3px solid ${SOMIPEM_COLORS.SECONDARY_BLUE}` // Secondary Blue top border for filter
              })
            }}
          >
            <Spin spinning={essentialLoading || loading}>
              <Statistic
                title={
                  <Space>
                    {stat.icon && React.isValidElement(stat.icon) ? (
                      React.cloneElement(stat.icon, {
                        style: { 
                          color: stat.color || SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for icons
                          fontSize: 20 
                        },
                      })
                    ) : stat.icon ? (
                      <span style={{ 
                        color: stat.color || SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for icons
                        fontSize: 20 
                      }}>{stat.icon}</span>
                    ) : null}
                    <span style={{ 
                      color: SOMIPEM_COLORS.DARK_GRAY, // Dark Gray for titles
                      fontWeight: 600 
                    }}>{stat.title}</span>
                    {(stat.title === "Total Arrêts" || stat.title === "Arrêts Totaux") && dateFilterActive && (
                      <Popover content={`Nombre total d'arrêts ${dateRangeDescription}`} title="Période sélectionnée">
                        <InfoCircleOutlined style={{ 
                          color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for info icons
                          cursor: "pointer",
                          fontSize: 14 
                        }} />
                      </Popover>
                    )}
                  </Space>
                }
                value={stat.value || '0'}
                suffix={stat.suffix}
                valueStyle={{
                  fontSize: 24,
                  color: stat.color || SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for key numbers
                  fontWeight: 700,
                }}
                formatter={(value) => value} // Use pre-formatted value
              />
              
              {/* Special content for date filter card */}
              {stat.isDateFilter && (
                <div style={{ marginTop: 8 }}>
                  <Tag color="blue" style={{ 
                    marginBottom: 4,
                    backgroundColor: SOMIPEM_COLORS.SECONDARY_BLUE, // Secondary Blue for highlights
                    borderColor: SOMIPEM_COLORS.SECONDARY_BLUE,
                    color: "#FFFFFF"
                  }}>
                    {dateRangeText}
                  </Tag>
                  <div style={{ 
                    color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for subtitles
                    fontSize: '12px' 
                  }}>
                    {machineFilterText}
                  </div>
                </div>
              )}
              
              {/* Special handling for "Arrêts Non Déclarés" with percentage */}
              {stat.title === "Arrêts Non Déclarés" && (
                <div style={{ marginTop: 8 }}>
                  <span style={{ 
                    color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for muted text
                    fontSize: '14px' 
                  }}>
                    {percentageNonDeclared}% du total
                  </span>
                  <Progress
                    percent={displayTotalStops > 0 ? ((displayUndeclaredStops / displayTotalStops) * 100) : 0}
                    showInfo={false}
                    strokeColor={SOMIPEM_COLORS.SECONDARY_BLUE} // Secondary Blue for progress bars
                    trailColor="#F3F4F6" // Light gray trail
                    size="small"
                    strokeWidth={4}
                  />
                </div>
              )}
            </Spin>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default ArretStatsCards;
