# Minimal Dockerfile to test apicache specifically
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only essential dependencies to test apicache
RUN echo "=== Installing essential dependencies ===" && \
    npm install apicache express mysql2 cors dotenv jsonwebtoken bcrypt cookie-parser --verbose && \
    echo "=== Verifying apicache installation ===" && \
    npm list apicache && \
    echo "=== Testing apicache require ===" && \
    node -e "try { require('apicache'); console.log('SUCCESS: apicache found and working'); } catch(e) { console.error('ERROR: apicache missing:', e.message); process.exit(1); }" && \
    echo "=== Testing other key dependencies ===" && \
    node -e "try { require('express'); console.log('SUCCESS: express works'); } catch(e) { console.error('ERROR: express missing:', e.message); }" && \
    node -e "try { require('mysql2'); console.log('SUCCESS: mysql2 works'); } catch(e) { console.error('ERROR: mysql2 missing:', e.message); }" && \
    npm cache clean --force

# Copy source code
COPY . .

# Expose port
EXPOSE 5000

# Create a simple test server
RUN echo 'const express = require("express"); const apicache = require("apicache"); const app = express(); app.use(apicache.middleware("5 minutes")); app.get("/", (req, res) => res.json({message: "apicache works!", cache: "enabled"})); app.get("/health", (req, res) => res.json({status: "ok", apicache: "working"})); app.listen(5000, () => console.log("Test server running on port 5000 with apicache"));' > test-server.js

# Start the test application
CMD ["node", "test-server.js"]
