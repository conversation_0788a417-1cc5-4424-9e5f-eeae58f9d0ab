#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Production-ready startup script for LOCQL application
.DESCRIPTION
    Handles all deployment scenarios: unified container, ngrok tunnel, and local development
    Replaces temporary docker-compose.simple.yml with production-ready configuration
.PARAMETER Mode
    Deployment mode: unified, ngrok, or local
.PARAMETER Build
    Whether to rebuild containers
.PARAMETER Logs
    Whether to show logs after startup
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("unified", "ngrok", "local", "hybrid")]
    [string]$Mode = "unified",

    [Parameter(Mandatory=$false)]
    [switch]$Build,

    [Parameter(Mandatory=$false)]
    [switch]$Logs
)

Write-Host "🚀 LOCQL Production Startup Script" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to check prerequisites
function Test-Prerequisites {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        Write-Host "✅ Docker: $dockerVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Docker not found. Please install Docker Desktop." -ForegroundColor Red
        exit 1
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Host "✅ Docker Compose: $composeVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Docker Compose not found." -ForegroundColor Red
        exit 1
    }
    
    # Check if MySQL is running (for host.docker.internal)
    Write-Host "🔍 Checking MySQL connection..." -ForegroundColor Yellow
    try {
        $mysqlTest = Test-NetConnection -ComputerName localhost -Port 3306 -WarningAction SilentlyContinue
        if ($mysqlTest.TcpTestSucceeded) {
            Write-Host "✅ MySQL is running on localhost:3306" -ForegroundColor Green
        } else {
            Write-Host "⚠️ MySQL not detected on localhost:3306. Make sure your database is running." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Could not test MySQL connection." -ForegroundColor Yellow
    }
}

# Function to cleanup existing containers
function Stop-ExistingContainers {
    Write-Host "🧹 Cleaning up existing containers..." -ForegroundColor Yellow
    
    # Stop all LOCQL containers
    $containers = @("locql-app-simple", "locql-app-production", "locql-app-unified", "locql-backend", "locql-frontend", "locql-ngrok")
    
    foreach ($container in $containers) {
        try {
            docker stop $container 2>$null
            docker rm $container 2>$null
            Write-Host "✅ Cleaned up container: $container" -ForegroundColor Green
        } catch {
            # Container doesn't exist, ignore
        }
    }
    
    # Clean up docker-compose services
    try {
        docker-compose -f docker-compose.simple.yml down 2>$null
        docker-compose -f docker-compose.production.yml down 2>$null
        docker-compose -f docker-compose.ngrok.yml down 2>$null
        docker-compose -f docker-compose.local.yml down 2>$null
    } catch {
        # Ignore errors
    }
}

# Function to start unified container
function Start-UnifiedContainer {
    Write-Host "🔧 Starting Unified Container Mode..." -ForegroundColor Cyan
    
    $composeFile = "docker-compose.production.yml"
    $buildFlag = if ($Build) { "--build" } else { "" }
    
    Write-Host "📁 Using compose file: $composeFile" -ForegroundColor Yellow
    Write-Host "🏗️ Build flag: $buildFlag" -ForegroundColor Yellow
    
    try {
        if ($Build) {
            docker-compose -f $composeFile up --build -d
        } else {
            docker-compose -f $composeFile up -d
        }
        
        Write-Host "✅ Unified container started successfully!" -ForegroundColor Green
        Write-Host "🌐 Application available at: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "🔐 Authentication: Cookie-based (production-ready)" -ForegroundColor Cyan
        
    } catch {
        Write-Host "❌ Failed to start unified container: $_" -ForegroundColor Red
        exit 1
    }
}


# Function to start ngrok mode
function Start-NgrokMode {
    Write-Host "🔧 Starting ngrok Tunnel Mode..." -ForegroundColor Cyan

    $composeFile = "docker-compose.hub-ngrok.yml"

    try {
        # Start LOCQL application
        docker-compose -f $composeFile up -d locql

        # Start ngrok tunnel
        docker-compose -f $composeFile --profile ngrok up -d

        Write-Host "✅ ngrok mode started successfully!" -ForegroundColor Green
        Write-Host "🌐 Application available at: https://eternal-friendly-chigger.ngrok-free.app" -ForegroundColor Cyan
        Write-Host "🔐 Authentication: Standard JWT-based auth" -ForegroundColor Cyan

    } catch {
        Write-Host "❌ Failed to start ngrok mode: $_" -ForegroundColor Red
        exit 1
    }
}


# Function to start hybrid mode (both localhost and ngrok access)
function Start-HybridMode {
    Write-Host "🔧 Starting Hybrid Mode (localhost + ngrok)..." -ForegroundColor Cyan

    $composeFile = "docker-compose.hybrid.yml"
    $buildFlag = if ($Build) { "--build" } else { "" }

    try {
        if ($Build) {
            docker-compose -f $composeFile up --build -d
        } else {
            docker-compose -f $composeFile up -d
        }

        Write-Host "✅ Hybrid mode started successfully!" -ForegroundColor Green
        Write-Host "🌐 Localhost access: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "🌐 ngrok access: https://eternal-friendly-chigger.ngrok-free.app" -ForegroundColor Cyan
        Write-Host "🔐 Authentication: Cookie-based (works with both access methods)" -ForegroundColor Cyan

    } catch {
        Write-Host "❌ Failed to start hybrid mode: $_" -ForegroundColor Red
        exit 1
    }
}

# Function to start local development
function Start-LocalDevelopment {
    Write-Host "🔧 Starting Local Development Mode..." -ForegroundColor Cyan
    
    try {
        # Start backend
        Write-Host "🚀 Starting backend..." -ForegroundColor Yellow
        Start-Process -FilePath "npm" -ArgumentList "run", "dev" -WorkingDirectory "." -WindowStyle Minimized
        
        Write-Host "✅ Local development started!" -ForegroundColor Green
        Write-Host "🌐 Frontend: http://localhost:5173" -ForegroundColor Cyan
        Write-Host "🌐 Backend: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "🔐 Authentication: Development mode" -ForegroundColor Cyan
        
    } catch {
        Write-Host "❌ Failed to start local development: $_" -ForegroundColor Red
        exit 1
    }
}


# Function to show logs
function Show-Logs {
    if ($Mode -eq "local") {
        Write-Host "📋 Local development logs available in separate terminal windows" -ForegroundColor Yellow
        return
    }
    
    $composeFile = switch ($Mode) {
        "unified" { "docker-compose.production.yml" }
        "ngrok" { "docker-compose.hub-ngrok.yml" }
        "hybrid" { "docker-compose.hybrid.yml" }
    }
    
    Write-Host "📋 Showing logs for $Mode mode..." -ForegroundColor Yellow
    docker-compose -f $composeFile logs -f
}

# Main execution
try {
    Test-Prerequisites
    Stop-ExistingContainers
    
    switch ($Mode) {
        "unified" { Start-UnifiedContainer }
        "ngrok" { Start-NgrokMode }
        "hybrid" { Start-HybridMode }
        "local" { Start-LocalDevelopment }
    }
    
    if ($Logs) {
        Show-Logs
    } else {
        Write-Host "`n🎉 LOCQL application started successfully in $Mode mode!" -ForegroundColor Green
        Write-Host "💡 Use -Logs flag to view logs" -ForegroundColor Yellow
        Write-Host "💡 Use 'docker-compose -f docker-compose.production.yml logs -f' to view logs later" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Startup failed: $_" -ForegroundColor Red
    exit 1
}
