import React from "react" ;
import { Row, Col, Statistic, Divider, Table, Typography, Tag, Card, Empty, Space } from "antd"
import { CheckCircleOutlined, WarningOutlined, ClockCircleOutlined } from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text, Paragraph } = Typography

// Composant de détail pour les rapports de quart
export const ShiftReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Quart" value={report.shift} />
        </Col>
        <Col span={8}>
          <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} />
        </Col>
        <Col span={8}>
          <Statistic title="Période" value={`${report.startTime} - ${report.endTime}`} />
        </Col>
      </Row>

      <Divider orientation="left">Production</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic title="Taux de production" value={report.production.rate} suffix="unités/heure" precision={2} />
        </Col>
        <Col span={8}>
          <Statistic title="Machines actives" value={report.production.activeMachines} />
        </Col>
      </Row>

      <Divider orientation="left">Alertes</Divider>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="Nombre total d'alertes"
            value={report.alerts.total}
            valueStyle={{ color: report.alerts.total > 0 ? "#ff4d4f" : "#52c41a" }}
            prefix={report.alerts.total > 0 ? <WarningOutlined /> : <CheckCircleOutlined />}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="Machines avec alertes"
            value={report.alerts.machinesWithAlerts}
            valueStyle={{ color: report.alerts.machinesWithAlerts > 0 ? "#faad14" : "#52c41a" }}
            prefix={report.alerts.machinesWithAlerts > 0 ? <WarningOutlined /> : <CheckCircleOutlined />}
          />
        </Col>
      </Row>

      <Divider orientation="left">Maintenance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic title="Événements de maintenance" value={report.maintenance.total} />
        </Col>
        <Col span={12}>
          <Statistic title="Durée totale de maintenance" value={report.maintenance.duration} suffix="minutes" />
        </Col>
      </Row>

      {report.notes && (
        <>
          <Divider orientation="left">Notes</Divider>
          <Paragraph>{report.notes}</Paragraph>
        </>
      )}
    </div>
  )
}

// Composant de détail pour les rapports journaliers
export const DailyReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} />
        </Col>
        <Col span={8}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Performance"
            value={report.production.performance}
            suffix="%"
            valueStyle={{
              color:
                report.production.performance >= 90
                  ? "#52c41a"
                  : report.production.performance >= 70
                    ? "#faad14"
                    : "#ff4d4f",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Résumé par quart</Divider>

      <Table
        dataSource={report.shifts}
        rowKey="name"
        pagination={false}
        columns={[
          {
            title: "Quart",
            dataIndex: "name",
            key: "name",
          },
          {
            title: "Production",
            dataIndex: "production",
            key: "production",
            render: (value) => `${value} unités`,
          },
          {
            title: "Performance",
            dataIndex: "performance",
            key: "performance",
            render: (value) => (
              <Space>
                <Tag color={value >= 90 ? "success" : value >= 70 ? "warning" : "error"}>{value}%</Tag>
              </Space>
            ),
          },
          {
            title: "Alertes",
            dataIndex: "alerts",
            key: "alerts",
            render: (value) => <Tag color={value > 0 ? "error" : "success"}>{value}</Tag>,
          },
        ]}
      />

      <Divider orientation="left">Qualité</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title="Taux de qualité"
            value={report.quality.rate}
            suffix="%"
            valueStyle={{
              color: report.quality.rate >= 95 ? "#52c41a" : report.quality.rate >= 85 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic title="Rejets" value={report.quality.rejects} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Taux de rejet"
            value={report.quality.rejectRate}
            suffix="%"
            precision={2}
            valueStyle={{ color: "#ff4d4f" }}
          />
        </Col>
      </Row>
    </div>
  )
}

// Composant de détail pour les rapports hebdomadaires
export const WeeklyReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="Semaine"
            value={`${report.weekNumber} (${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")})`}
          />
        </Col>
        <Col span={12}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
      </Row>

      <Divider orientation="left">Performance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title="Performance moyenne"
            value={report.performance.average}
            suffix="%"
            valueStyle={{
              color:
                report.performance.average >= 90 ? "#52c41a" : report.performance.average >= 70 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Meilleur jour"
            value={report.performance.bestDay.performance}
            suffix="%"
            valueStyle={{ color: "#52c41a" }}
          />
          <Text type="secondary">{dayjs(report.performance.bestDay.date).format("dddd DD/MM")}</Text>
        </Col>
        <Col span={8}>
          <Statistic
            title="Jour le moins performant"
            value={report.performance.worstDay.performance}
            suffix="%"
            valueStyle={{ color: "#ff4d4f" }}
          />
          <Text type="secondary">{dayjs(report.performance.worstDay.date).format("dddd DD/MM")}</Text>
        </Col>
      </Row>

      <Divider orientation="left">Tendances</Divider>

      <Table
        dataSource={report.dailyData}
        rowKey="date"
        pagination={false}
        columns={[
          {
            title: "Jour",
            dataIndex: "date",
            key: "date",
            render: (date) => dayjs(date).format("dddd DD/MM"),
          },
          {
            title: "Production",
            dataIndex: "production",
            key: "production",
            render: (value) => `${value} unités`,
          },
          {
            title: "Performance",
            dataIndex: "performance",
            key: "performance",
            render: (value) => <Tag color={value >= 90 ? "success" : value >= 70 ? "warning" : "error"}>{value}%</Tag>,
          },
          {
            title: "Qualité",
            dataIndex: "quality",
            key: "quality",
            render: (value) => <Tag color={value >= 95 ? "success" : value >= 85 ? "warning" : "error"}>{value}%</Tag>,
          },
          {
            title: "Alertes",
            dataIndex: "alerts",
            key: "alerts",
            render: (value) => <Tag color={value > 0 ? "error" : "success"}>{value}</Tag>,
          },
        ]}
      />
    </div>
  )
}

// Composant de détail pour les rapports de machine
export const MachineReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Machine" value={report.machineName} />
        </Col>
        <Col span={8}>
          <Statistic
            title="Période"
            value={`${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")}`}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="État"
            value={report.status === "operational" ? "Opérationnelle" : "En maintenance"}
            valueStyle={{
              color: report.status === "operational" ? "#52c41a" : "#faad14",
            }}
            prefix={report.status === "operational" ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
          />
        </Col>
      </Row>

      <Divider orientation="left">Performance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Taux de production moyen"
            value={report.production.averageRate}
            suffix="unités/heure"
            precision={2}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Efficacité"
            value={report.efficiency}
            suffix="%"
            valueStyle={{
              color: report.efficiency >= 90 ? "#52c41a" : report.efficiency >= 70 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Maintenance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Temps de fonctionnement" value={report.uptime} suffix="heures" />
        </Col>
        <Col span={8}>
          <Statistic title="Temps d'arrêt" value={report.downtime} suffix="heures" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Disponibilité"
            value={report.availability}
            suffix="%"
            valueStyle={{
              color: report.availability >= 95 ? "#52c41a" : report.availability >= 85 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Événements de maintenance</Divider>

      {report.maintenanceEvents && report.maintenanceEvents.length > 0 ? (
        <Table
          dataSource={report.maintenanceEvents}
          rowKey="id"
          pagination={false}
          columns={[
            {
              title: "Date",
              dataIndex: "date",
              key: "date",
              render: (date) => dayjs(date).format("DD/MM/YYYY"),
            },
            {
              title: "Type",
              dataIndex: "type",
              key: "type",
            },
            {
              title: "Durée",
              dataIndex: "duration",
              key: "duration",
              render: (duration) => `${duration} minutes`,
            },
            {
              title: "Technicien",
              dataIndex: "technician",
              key: "technician",
            },
            {
              title: "Description",
              dataIndex: "description",
              key: "description",
              ellipsis: true,
            },
          ]}
        />
      ) : (
        <Empty description="Aucun événement de maintenance sur cette période" />
      )}
    </div>
  )
}

// Composant générique pour les autres types de rapports
export const GenericReportDetail = ({ report }) => {
  return (
    <Card>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="ID du rapport" value={report.id} />
        </Col>
        <Col span={8}>
          <Statistic title="Type" value={report.type} />
        </Col>
        <Col span={8}>
          <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} />
        </Col>
      </Row>

      <Divider />

      <Paragraph>
        Ce rapport a été généré le {dayjs(report.generatedAt).format("DD/MM/YYYY à HH:mm")} par {report.generatedBy}.
      </Paragraph>

      <Paragraph>
        <Text type="secondary">
          L'aperçu détaillé n'est pas disponible pour ce type de rapport. Veuillez l'exporter pour voir tous les
          détails.
        </Text>
      </Paragraph>
    </Card>
  )
}

// Composant principal qui sélectionne le bon composant de détail en fonction du type de rapport
const ReportDetail = ({ report }) => {
  if (!report) return null

  // Rendu en fonction du type de rapport
  switch (report.type) {
    case "shift":
      return <ShiftReportDetail report={report} />
    case "daily":
      return <DailyReportDetail report={report} />
    case "weekly":
      return <WeeklyReportDetail report={report} />
    case "machine":
      return <MachineReportDetail report={report} />
    default:
      return <GenericReportDetail report={report} />
  }
}

export default ReportDetail

