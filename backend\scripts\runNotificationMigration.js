import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { executeQuery } from '../utils/dbUtils.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Run database migration script for notification preferences
 */
async function runMigration() {
  try {
    console.log('🔄 Running notification preferences migration...');
    
    // Create tables manually since we can't read the migration file
    console.log('📝 Creating user_notification_preferences table...');
    
    const createPreferencesTable = `
      CREATE TABLE IF NOT EXISTS user_notification_preferences (
        id int NOT NULL AUTO_INCREMENT,
        user_id int NOT NULL,
        notification_categories json DEFAULT NULL,
        priority_thresholds json DEFAULT NULL,
        email_frequency enum('immediate','hourly_batch','daily_digest') DEFAULT 'immediate',
        email_enabled tinyint(1) DEFAULT '1',
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY unique_user_preferences (user_id),
        KEY idx_user_notification_preferences_user_id (user_id),
        KEY idx_user_notification_preferences_email_enabled (email_enabled),
        CONSTRAINT user_notification_preferences_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `;
    
    const result1 = await executeQuery(createPreferencesTable);
    if (result1.success) {
      console.log('✅ user_notification_preferences table created successfully');
    } else {
      console.error('❌ Failed to create user_notification_preferences table:', result1.error);
    }
    
    console.log('📝 Creating user_report_subscriptions table...');
    
    const createSubscriptionsTable = `
      CREATE TABLE IF NOT EXISTS user_report_subscriptions (
        id int NOT NULL AUTO_INCREMENT,
        user_id int NOT NULL,
        report_type varchar(100) NOT NULL,
        delivery_frequency enum('daily','weekly','monthly') DEFAULT 'weekly',
        delivery_time time DEFAULT '08:00:00',
        format_preference enum('pdf','html','both') DEFAULT 'pdf',
        is_active tinyint(1) DEFAULT '1',
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_user_report_subscriptions_user_id (user_id),
        KEY idx_user_report_subscriptions_report_type (report_type),
        KEY idx_user_report_subscriptions_active (is_active),
        CONSTRAINT user_report_subscriptions_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `;
    
    const result2 = await executeQuery(createSubscriptionsTable);
    if (result2.success) {
      console.log('✅ user_report_subscriptions table created successfully');
    } else {
      console.error('❌ Failed to create user_report_subscriptions table:', result2.error);
    }
    
    console.log('📝 Inserting default preferences for existing users...');
    
    // Insert default preferences for admin users
    const insertAdminPrefs = `
      INSERT IGNORE INTO user_notification_preferences (user_id, notification_categories, priority_thresholds, email_frequency, email_enabled)
      SELECT 
        id,
        JSON_OBJECT(
          'machine_alert', true,
          'production', true,
          'quality', true,
          'maintenance', true,
          'update', true,
          'alert', true,
          'info', true
        ),
        JSON_OBJECT(
          'critical', true,
          'high', true,
          'medium', true,
          'low', true
        ),
        'immediate',
        1
      FROM users 
      WHERE role = 'admin'
    `;
    
    const result3 = await executeQuery(insertAdminPrefs);
    if (result3.success) {
      console.log('✅ Default preferences inserted for admin users');
    } else {
      console.error('❌ Failed to insert admin preferences:', result3.error);
    }
    
    // Insert default preferences for regular users
    const insertUserPrefs = `
      INSERT IGNORE INTO user_notification_preferences (user_id, notification_categories, priority_thresholds, email_frequency, email_enabled)
      SELECT 
        id,
        JSON_OBJECT(
          'machine_alert', true,
          'production', true,
          'quality', false,
          'maintenance', false,
          'update', false,
          'alert', true,
          'info', false
        ),
        JSON_OBJECT(
          'critical', true,
          'high', true,
          'medium', false,
          'low', false
        ),
        'hourly_batch',
        1
      FROM users 
      WHERE role = 'user'
    `;
    
    const result4 = await executeQuery(insertUserPrefs);
    if (result4.success) {
      console.log('✅ Default preferences inserted for regular users');
    } else {
      console.error('❌ Failed to insert user preferences:', result4.error);
    }
    
    // Insert default preferences for other roles
    const insertOtherPrefs = `
      INSERT IGNORE INTO user_notification_preferences (user_id, notification_categories, priority_thresholds, email_frequency, email_enabled)
      SELECT 
        id,
        JSON_OBJECT(
          'machine_alert', true,
          'production', true,
          'quality', true,
          'maintenance', true,
          'update', false,
          'alert', true,
          'info', false
        ),
        JSON_OBJECT(
          'critical', true,
          'high', true,
          'medium', true,
          'low', false
        ),
        'immediate',
        1
      FROM users 
      WHERE role NOT IN ('admin', 'user')
    `;
    
    const result5 = await executeQuery(insertOtherPrefs);
    if (result5.success) {
      console.log('✅ Default preferences inserted for other role users');
    } else {
      console.error('❌ Failed to insert other role preferences:', result5.error);
    }
    
    console.log('✅ Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();
