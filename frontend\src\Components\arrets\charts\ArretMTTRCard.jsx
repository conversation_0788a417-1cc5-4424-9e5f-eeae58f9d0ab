import React, { memo } from 'react';
import { Card, Progress, Typography, Row, Col, Statistic } from 'antd';
import { ClockCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;

const ArretMTTRCard = memo(({ mttr = 0, loading = false }) => {
  if (loading) {
    return (
      <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>Chargement MTTR...</div>
      </div>
    );
  }

  // Calculate MTTR performance score (lower is better)
  const getPerformanceLevel = (mttr) => {
    if (mttr <= 15) return { level: 'Excellent', color: '#52c41a', score: 95 };
    if (mttr <= 30) return { level: 'Bon', color: '#faad14', score: 80 };
    if (mttr <= 60) return { level: 'Moyen', color: '#fa8c16', score: 60 };
    if (mttr <= 120) return { level: 'Faible', color: '#ff7875', score: 40 };
    return { level: 'Critique', color: '#f5222d', score: 20 };
  };

  const performance = getPerformanceLevel(mttr);

  return (
    <div style={{ height: '100%', padding: '16px 0' }}>
      <Row gutter={[16, 16]} style={{ height: '100%' }}>
        <Col span={24}>
          {/* Header */}
          <div style={{ textAlign: 'center', marginBottom: 16 }}>
            <ClockCircleOutlined 
              style={{ 
                fontSize: 32, 
                color: performance.color, 
                marginBottom: 8,
                display: 'block' 
              }} 
            />
            <Title level={4} style={{ margin: 0, color: performance.color }}>
              MTTR
            </Title>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Temps Moyen de Réparation
            </Text>
          </div>

          {/* Main Metric */}
          <div style={{ textAlign: 'center', marginBottom: 20 }}>
            <Statistic
              value={mttr}
              suffix="minutes"
              precision={1}
              valueStyle={{ 
                fontSize: 28, 
                fontWeight: 'bold',
                color: performance.color 
              }}
            />
          </div>

          {/* Performance Indicator */}
          <div style={{ textAlign: 'center', marginBottom: 16 }}>
            <Progress
              percent={performance.score}
              strokeColor={performance.color}
              showInfo={false}
              size="small"
              style={{ marginBottom: 8 }}
            />
            <Text 
              style={{ 
                fontSize: 14, 
                fontWeight: 'bold',
                color: performance.color 
              }}
            >
              {performance.level}
            </Text>
          </div>

          {/* Info */}
          <div style={{ 
            background: '#f5f5f5', 
            padding: 12, 
            borderRadius: 6,
            textAlign: 'center'
          }}>
            <InfoCircleOutlined style={{ marginRight: 6, color: '#666' }} />            <Text style={{ fontSize: 11, color: '#666' }}>
              Objectif: ≤ 30 min • Critique: {'>'}120 min
            </Text>
          </div>
        </Col>
      </Row>
    </div>
  );
});

ArretMTTRCard.displayName = 'ArretMTTRCard';

export default ArretMTTRCard;
