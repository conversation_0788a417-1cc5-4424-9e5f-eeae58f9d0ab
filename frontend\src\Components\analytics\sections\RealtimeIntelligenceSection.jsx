import React from 'react';
import { Card, Space } from 'antd';
import { EyeOutlined } from '@ant-design/icons';

const RealtimeIntelligenceSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <EyeOutlined style={{ fontSize: '64px', color: '#faad14' }} />
          <h2 style={{ color: '#faad14', margin: 0 }}>Real-time Intelligence</h2>
          <p style={{ color: '#8c8c8c' }}>Live monitoring and real-time analytics coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default RealtimeIntelligenceSection;
