import React, { useEffect } from 'react';
import { 
  Space, 
  Select, 
  DatePicker, 
  Button, 
  Segmented, 
  Tooltip, 
  Tag, 
  Row, 
  Col
} from 'antd';
import { 
  FilterOutlined, 
  CalendarOutlined, 
  ClearOutlined, 
  ReloadOutlined, 
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import SOMIPEM_COLORS from '../../styles/brand-colors';

const { Option } = Select;

const ArretFilters = ({ onFilterChange }) => {
  const context = useArretQueuedContext();
  
  // Extra defensive programming
  if (!context) {
    return <div>Context not available</div>;
  }

  const {
    // Machine filtering
    machineModels = [],
    filteredMachineNames = [],
    selectedMachineModel = '',
    selectedMachine = '',
    handleMachineModelChange,
    handleMachineChange,
    
    // Date filtering  
    dateRangeType = 'day',
    selectedDate = null,
    dateFilterActive = false,
    handleDateRangeTypeChange,
    handleDateChange,
    
    // Data and actions
    loading = false,
    stopsData = [],
    resetFilters,
    handleRefresh,
    
    // New GraphQL-specific properties
    complexFilterLoading = false,
    dataManager
  } = context;

  // Log filter state changes for monitoring
  useEffect(() => {
    // Create filter state object
    const currentFilters = {
      model: selectedMachineModel,
      machine: selectedMachine,
      date: selectedDate?.format('YYYY-MM-DD'),
      dateType: dateRangeType,
      dateFilterActive,
      hasAllFilters: selectedMachineModel && selectedMachine && dateFilterActive,
      dataCount: stopsData?.length
    };
    
   
    
    // Always notify parent component of filter state
    if (onFilterChange && typeof onFilterChange === 'function') {
      onFilterChange(currentFilters);
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, dateFilterActive, stopsData?.length, onFilterChange, machineModels, filteredMachineNames]);

  // Date picker rendering based on range type
  const renderDatePicker = () => {
    if (dateRangeType === "day") {
      return (
        <DatePicker
          value={selectedDate}
          onChange={handleDateChange}
          format="DD/MM/YYYY"
          placeholder="Sélectionner une date"
          allowClear
          style={{ width: '100%' }}
        />
      );
    } else if (dateRangeType === "week") {
      return (
        <DatePicker
          value={selectedDate}
          onChange={handleDateChange}
          picker="week"
          format="[Semaine] w YYYY"
          placeholder="Sélectionner une semaine"
          allowClear
          style={{ width: '100%' }}
        />
      );
    } else if (dateRangeType === "month") {
      return (
        <DatePicker
          value={selectedDate}
          onChange={handleDateChange}
          picker="month"
          format="MMMM YYYY"
          placeholder="Sélectionner un mois"
          allowClear
          style={{ width: '100%' }}
        />
      );
    }
    return null;
  };

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      {/* Filter Controls Row */}
      <Row gutter={[16, 16]} align="middle">
        {/* Machine Model Selection */}
        <Col xs={24} sm={12} lg={6}>
          <div>
            <div style={{ 
              marginBottom: 4, 
              fontSize: '12px', 
              color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for subtitles
            }}>
              Modèle de Machine
            </div>
            <Select
              placeholder="Sélectionner un modèle"
              value={selectedMachineModel}
              onChange={handleMachineModelChange}
              style={{ width: '100%' }}
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().includes(input.toLowerCase())
              }
            >
              {machineModels.map((modelItem) => {
                // Handle both string and object format
                const modelValue = typeof modelItem === 'object' ? modelItem.model : modelItem;
                return (
                  <Option key={modelValue} value={modelValue}>
                    {modelValue}
                  </Option>
                );
              })}
            </Select>
          </div>
        </Col>

        {/* Machine Selection */}
        <Col xs={24} sm={12} lg={6}>
          <div>
            <div style={{ 
              marginBottom: 4, 
              fontSize: '12px', 
              color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for subtitles
            }}>
              Machine Spécifique
            </div>
            <Select
              placeholder="Sélectionner une machine"
              value={selectedMachine}
              onChange={(value) => {
               
                if (typeof handleMachineChange === 'function') {
                  handleMachineChange(value);
                } else {
                  console.error('handleMachineChange is not a function!', typeof handleMachineChange);
                }
              }}
              style={{ width: '100%' }}
              allowClear
              showSearch
              disabled={!selectedMachineModel && filteredMachineNames.length === 0}
              filterOption={(input, option) =>
                option.children.toLowerCase().includes(input.toLowerCase())
              }
            >
              {filteredMachineNames.map((machine) => {
                // Get the machine name - could be a string or object with Machine_Name
                const machineName =  machine.name ;
                
                
                
                // Always ensure we have a valid string key and value
                return (
                  <Option key={machineName || `machine-${Math.random()}`} value={machineName}>
                    {machineName || 'Unknown Machine'}
                  </Option>
                );
              })}
            </Select>
          </div>
        </Col>

        {/* Date Range Type */}
        <Col xs={24} sm={12} lg={6}>
          <div>
            <div style={{ 
              marginBottom: 4, 
              fontSize: '12px', 
              color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for subtitles
            }}>
              Type de Période
            </div>
            <Segmented
              value={dateRangeType}
              onChange={handleDateRangeTypeChange}
              options={[
                { label: 'Jour', value: 'day', icon: <CalendarOutlined /> },
                { label: 'Semaine', value: 'week', icon: <CalendarOutlined /> },
                { label: 'Mois', value: 'month', icon: <CalendarOutlined /> }
              ]}
              style={{ width: '100%' }}
            />
          </div>
        </Col>

        {/* Date Picker */}
        <Col xs={24} sm={12} lg={6}>
          <div>
            <div style={{ 
              marginBottom: 4, 
              fontSize: '12px', 
              color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for subtitles
            }}>
              Sélection de Date
            </div>
            {renderDatePicker()}
          </div>
        </Col>
      </Row>

      {/* Action Buttons Row */}
      <Row gutter={[16, 16]} align="middle" justify="space-between">
        <Col>
          <Space>
            {/* Active Filters Display */}
            {(selectedMachineModel || selectedMachine || dateFilterActive) && (
              <Space wrap>
                {selectedMachineModel && (
                  <Tag color="blue" closable onClose={() => handleMachineModelChange('')}>
                    Modèle: {typeof selectedMachineModel === 'object' ? selectedMachineModel.model : selectedMachineModel}
                  </Tag>
                )}
                {selectedMachine && (
                  <Tag color="green" closable onClose={() => handleMachineChange('')}>
                    Machine: {typeof selectedMachine === 'object' ? selectedMachine.Machine_Name : selectedMachine}
                  </Tag>
                )}
                {dateFilterActive && selectedDate && (
                  <Tag color="orange" closable onClose={() => handleDateChange(null)}>
                    <ClockCircleOutlined style={{ marginRight: 4 }} />
                    {selectedDate.format(
                      dateRangeType === 'day' ? 'DD/MM/YYYY' :
                      dateRangeType === 'week' ? '[Semaine] w YYYY' :
                      'MMMM YYYY'
                    )}
                  </Tag>
                )}
              </Space>
            )}
          </Space>
        </Col>

        <Col>
          <Space>
            {/* Action Buttons */}
            <Tooltip title="Effacer tous les filtres">
              <Button 
                icon={<ClearOutlined />} 
                onClick={resetFilters}
                disabled={!selectedMachineModel && !selectedMachine && !dateFilterActive}
              >
                Effacer
              </Button>
            </Tooltip>

            <Tooltip title="Forcer le rechargement manuel des données">
              <Button 
                type="primary" 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={loading || complexFilterLoading}
              >
                {loading || complexFilterLoading ? 'Chargement...' : 'Forcer Refresh'}
              </Button>
            </Tooltip>
          </Space>
        </Col>
      </Row>

      {/* Data Summary */}
      <Row>
        <Col span={24}>
          <Space wrap>
            <Tag icon={<FilterOutlined />} color="processing">
              {stopsData.length} arrêts trouvés
              {selectedMachineModel && !selectedMachine && ` (modèle: ${selectedMachineModel})`}
              {selectedMachine && ` (machine: ${selectedMachine})`}
            </Tag>
            
            {/* Active filters count */}
            {(selectedMachineModel || selectedMachine || dateFilterActive) && (
              <Tag color="blue">
                {[
                  selectedMachineModel && 'Modèle',
                  selectedMachine && 'Machine', 
                  dateFilterActive && 'Date'
                ].filter(Boolean).length} filtre(s) actif(s)
              </Tag>
            )}
            
            {/* Specific filter indicators - show which filter is being applied */}
            {selectedMachineModel && !selectedMachine && loading && (
              <Tag color="processing">
                <ClockCircleOutlined spin /> Filtrage par modèle en cours...
              </Tag>
            )}
            
            {selectedMachine && loading && (
              <Tag color="processing">
                <ClockCircleOutlined spin /> Filtrage par machine spécifique en cours...
              </Tag>
            )}
            
            {dateFilterActive && loading && (
              <Tag color="orange">
                <ClockCircleOutlined spin /> Filtrage par date en cours...
              </Tag>
            )}
            
            {/* Loading indicator */}
            {loading && (
              <Tag color="blue">
                Chargement en cours...
              </Tag>
            )}
            
            {/* GraphQL request status indicator */}
            {complexFilterLoading && (
              <Tag color="gold">
                <ClockCircleOutlined spin /> Traitement complexe...
              </Tag>
            )}

            {/* Auto-refresh info */}
            <Tag color="success" style={{ marginLeft: 'auto' }}>
              ✓ Les changements de filtres actualisent automatiquement les données
            </Tag>
          </Space>
        </Col>
      </Row>
      
      {/* Error Recovery Guidance - Only shown if there's an error in the GraphQL context */}
      {context.error && (
        <Row style={{ marginTop: '16px' }}>
          <Col span={24}>
            <div style={{ 
              padding: '12px', 
              backgroundColor: '#FFFFFF', // White background
              borderRadius: '8px',
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ExclamationCircleOutlined style={{ 
                  color: '#ff4d4f', // Keep red for error icon
                  fontSize: '16px', 
                  marginRight: '8px' 
                }} />
                <div>
                  <div style={{ 
                    fontWeight: 'bold',
                    color: SOMIPEM_COLORS.DARK_GRAY // Dark Gray for error title
                  }}>Erreur de chargement des données</div>
                  <div style={{ 
                    fontSize: '12px', 
                    marginTop: '4px',
                    color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for error description
                  }}>
                    {context.error.includes && context.error.includes('AbortError') 
                      ? "La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page."
                      : typeof context.error === 'string' 
                        ? context.error
                        : "Une erreur est survenue lors du chargement des données. Veuillez réessayer."}
                  </div>
                  <Space style={{ marginTop: '8px' }}>
                    <Button 
                      size="small" 
                      type="primary" 
                      onClick={() => {
                        // Try to recover by clearing the GraphQL cache and refreshing
                        if (context.graphQL && context.graphQL.invalidateCache) {
                          context.graphQL.invalidateCache();
                        }
                        handleRefresh();
                      }}
                    >
                      Réessayer
                    </Button>
                    <Button 
                      size="small"
                      onClick={resetFilters}
                    >
                      Réinitialiser les filtres
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      )}
    </Space>
  );
};

export default ArretFilters;
