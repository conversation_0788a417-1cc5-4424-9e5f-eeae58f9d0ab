import React, { useMemo } from 'react';
import { Card, Statistic, Tag } from 'antd';
import { CalendarOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import dayjs from 'dayjs';

const ArretDateFilterCard = ({ loading = false }) => {
  const { 
    selectedDate, 
    dateRangeType, 
    dateFilterActive, 
    stopsData,
    selectedMachine,
    selectedMachineModel
  } = useArretQueuedContext();

  // Calculate date range display text
  const dateRangeText = useMemo(() => {
    if (!selectedDate || !dateFilterActive) return '';

    const formatDate = (date) => date.format('DD/MM/YYYY');

    switch (dateRangeType) {
      case 'day':
        return formatDate(selectedDate);
      case 'week':
        const weekStart = selectedDate.clone().startOf('isoWeek');
        const weekEnd = selectedDate.clone().endOf('isoWeek');
        return `${formatDate(weekStart)} - ${formatDate(weekEnd)}`;
      case 'month':
        return selectedDate.format('MMMM YYYY');
      default:
        return formatDate(selectedDate);
    }
  }, [selectedDate, dateRangeType, dateFilterActive]);

  // Calculate filtered stops count
  const filteredStopsCount = useMemo(() => {
    if (!stopsData || !dateFilterActive || !selectedDate) return 0;
    
    // The stopsData should already be filtered by the backend based on the date filter
    // So we can just return the count
    return stopsData.length;
  }, [stopsData, dateFilterActive, selectedDate]);

  // Calculate machine filter text
  const machineFilterText = useMemo(() => {
    if (selectedMachine) {
      return selectedMachine;
    } else if (selectedMachineModel) {
      return `Modèle ${selectedMachineModel}`;
    }
    return 'Toutes les machines';
  }, [selectedMachine, selectedMachineModel]);

  // Get period type display text
  const periodTypeText = useMemo(() => {
    switch (dateRangeType) {
      case 'day':
        return 'jour';
      case 'week':
        return 'semaine';
      case 'month':
        return 'mois';
      default:
        return 'période';
    }
  }, [dateRangeType]);

  // Don't show the card if date filter is not active
  if (!dateFilterActive || !selectedDate) {
    return null;
  }

  return (
    <Card
      style={{
        marginBottom: '16px',
        borderLeft: '4px solid #1890ff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}
      bodyStyle={{ padding: '16px' }}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <CalendarOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
          <div>
            <div style={{ fontSize: '16px', fontWeight: '600', color: '#262626' }}>
              Arrêts pour la {periodTypeText} sélectionnée
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '2px' }}>
              {machineFilterText} • {dateRangeText}
            </div>
          </div>
        </div>
        
        <div style={{ textAlign: 'right' }}>
          <Statistic
            value={filteredStopsCount}
            loading={loading}
            valueStyle={{ 
              fontSize: '24px', 
              fontWeight: '700', 
              color: '#1890ff' 
            }}
            suffix={
              <span style={{ fontSize: '14px', color: '#8c8c8c' }}>
                arrêts
              </span>
            }
          />
        </div>
      </div>
      
      <div style={{ marginTop: '12px', display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Tag 
          icon={<ClockCircleOutlined />} 
          color="blue"
          style={{ margin: 0 }}
        >
          Période filtrée active
        </Tag>
        {selectedMachine && (
          <Tag color="green" style={{ margin: 0 }}>
            Machine spécifique
          </Tag>
        )}
      </div>
    </Card>
  );
};

export default ArretDateFilterCard;
