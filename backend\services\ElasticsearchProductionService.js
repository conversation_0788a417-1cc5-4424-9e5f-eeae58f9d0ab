/**
 * 🔍 Elasticsearch Production Service
 * 
 * Phase 3: Elasticsearch Optimization for Production Dashboard
 * Implements high-performance queries, aggregations, and real-time indexing
 * for manufacturing production data analytics
 */

import { esClient } from '../config/elasticsearch.js';
import redisConfig from '../config/redisConfig.js';

class ElasticsearchProductionService {
  constructor() {
    this.indexName = 'production-data';
    this.redisClient = null;
    this.redisConfig = redisConfig;
    this.cachePrefix = 'es:production:';
    this.cacheTTL = 300; // 5 minutes
  }

  /**
   * Initialize the service
   */
  async initialize() {
    try {
      // Initialize Redis if not already connected
      if (!this.redisConfig.isConnected) {
        await this.redisConfig.initialize();
      }
      this.redisClient = this.redisConfig.getClient();
      console.log('✅ Elasticsearch Production Service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Elasticsearch Production Service:', error);
      console.warn('⚠️ Service will operate without Redis caching');
      return false;
    }
  }

  /**
   * Index production data from machine_daily_table_mould
   * @param {Array} productionData - Array of production records
   */
  async indexProductionData(productionData) {
    try {
      if (!Array.isArray(productionData) || productionData.length === 0) {
        return { success: false, message: 'No data to index' };
      }

      const body = [];
      
      for (const record of productionData) {
        // Index operation
        body.push({
          index: {
            _index: this.indexName,
            _id: `${record.id || record.machine_id}_${record.date || Date.now()}`
          }
        });
        
        // Document data - optimized for analytics
        body.push({
          recordId: record.id,
          machineId: record.machine_id,
          machineName: record.machine_name || `Machine ${record.machine_id}`,
          date: record.date,
          shift: record.shift || 'unknown',
          operator: record.operator || 'unknown',
          
          // Production metrics
          production: {
            good: parseInt(record.good_production) || 0,
            rejects: parseInt(record.rejects) || 0,
            total: (parseInt(record.good_production) || 0) + (parseInt(record.rejects) || 0),
            target: parseInt(record.target_production) || 0,
            rate: parseFloat(record.production_rate) || 0
          },
          
          // Performance metrics
          performance: {
            oee: parseFloat(record.oee) || 0,
            availability: parseFloat(record.availability) || 0,
            performance: parseFloat(record.performance_rate) || 0,
            quality: parseFloat(record.quality_rate) || 0,
            trs: parseFloat(record.trs) || 0
          },
          
          // Timing data
          timing: {
            runHours: parseFloat(record.run_hours) || 0,
            downHours: parseFloat(record.down_hours) || 0,
            speed: parseFloat(record.speed) || 0
          },
          
          // Part information
          partInfo: {
            partNumber: record.part_number || '',
            mouldNumber: record.mould_number || '',
            unitWeight: parseFloat(record.unit_weight) || 0,
            theoreticalCycle: parseFloat(record.theoretical_cycle) || 0,
            purgeWeight: parseFloat(record.purge_weight) || 0
          },
          
          // Order information
          orderInfo: {
            orderNumber: record.order_number || '',
            article: record.article || '',
            plannedQuantity: parseInt(record.planned_quantity) || 0
          },
          
          // Indexing metadata
          indexedAt: new Date().toISOString(),
          dataSource: 'machine_daily_table_mould'
        });
      }

      const response = await esClient.bulk({ body });
      
      if (response.errors) {
        console.error('❌ Elasticsearch bulk indexing errors:', response.items.filter(item => item.index.error));
        return { success: false, message: 'Bulk indexing completed with errors', errors: response.errors };
      }

      console.log(`✅ Indexed ${productionData.length} production records to Elasticsearch`);
      
      // Clear related cache entries
      await this.clearProductionCache();
      
      return { success: true, indexed: productionData.length };
      
    } catch (error) {
      console.error('❌ Error indexing production data:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Advanced production analytics query with aggregations
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options (pagination, sorting, etc.)
   */
  async getProductionAnalytics(filters = {}, options = {}) {
    try {
      const cacheKey = `${this.cachePrefix}analytics:${JSON.stringify({ filters, options })}`;
      
      // Try cache first
      if (this.redisClient) {
        const cached = await this.redisClient.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const query = this.buildProductionQuery(filters);
      const aggregations = this.buildProductionAggregations(filters);
      
      const searchBody = {
        query,
        aggs: aggregations,
        size: options.size || 100,
        from: options.from || 0,
        sort: options.sort || [{ date: { order: 'desc' } }]
      };

      const response = await esClient.search({
        index: this.indexName,
        body: searchBody
      });

      const result = {
        total: response.hits.total.value,
        records: response.hits.hits.map(hit => ({
          id: hit._id,
          ...hit._source
        })),
        aggregations: this.processAggregations(response.aggregations),
        query_time_ms: response.took
      };

      // Cache the result
      if (this.redisClient) {
        await this.redisClient.setex(cacheKey, this.cacheTTL, JSON.stringify(result));
      }

      return result;
      
    } catch (error) {
      console.error('❌ Error querying production analytics:', error);
      throw error;
    }
  }

  /**
   * Real-time production dashboard data
   * Optimized for sub-second response times
   */
  async getDashboardData(machineIds = [], dateRange = {}) {
    try {
      const cacheKey = `${this.cachePrefix}dashboard:${machineIds.join(',')}_${JSON.stringify(dateRange)}`;
      
      // Try cache first
      if (this.redisClient) {
        const cached = await this.redisClient.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const query = {
        bool: {
          must: []
        }
      };

      // Machine filter
      if (machineIds.length > 0) {
        query.bool.must.push({
          terms: { machineId: machineIds }
        });
      }

      // Date range filter
      if (dateRange.start || dateRange.end) {
        const dateFilter = { range: { date: {} } };
        if (dateRange.start) dateFilter.range.date.gte = dateRange.start;
        if (dateRange.end) dateFilter.range.date.lte = dateRange.end;
        query.bool.must.push(dateFilter);
      }

      const searchBody = {
        query,
        aggs: {
          // Machine performance summary
          machines: {
            terms: { field: 'machineId', size: 50 },
            aggs: {
              avg_oee: { avg: { field: 'performance.oee' } },
              avg_availability: { avg: { field: 'performance.availability' } },
              avg_performance: { avg: { field: 'performance.performance' } },
              avg_quality: { avg: { field: 'performance.quality' } },
              total_production: { sum: { field: 'production.good' } },
              total_rejects: { sum: { field: 'production.rejects' } }
            }
          },
          
          // Daily trends
          daily_trends: {
            date_histogram: {
              field: 'date',
              calendar_interval: 'day'
            },
            aggs: {
              avg_oee: { avg: { field: 'performance.oee' } },
              total_production: { sum: { field: 'production.good' } }
            }
          },
          
          // Shift analysis
          shift_performance: {
            terms: { field: 'shift' },
            aggs: {
              avg_oee: { avg: { field: 'performance.oee' } },
              total_production: { sum: { field: 'production.good' } }
            }
          }
        },
        size: 0 // Only aggregations
      };

      const response = await esClient.search({
        index: this.indexName,
        body: searchBody
      });

      const result = {
        machines: response.aggregations.machines.buckets.map(bucket => ({
          machineId: bucket.key,
          metrics: {
            oee: Math.round(bucket.avg_oee.value * 100) / 100,
            availability: Math.round(bucket.avg_availability.value * 100) / 100,
            performance: Math.round(bucket.avg_performance.value * 100) / 100,
            quality: Math.round(bucket.avg_quality.value * 100) / 100,
            totalProduction: bucket.total_production.value,
            totalRejects: bucket.total_rejects.value
          }
        })),
        dailyTrends: response.aggregations.daily_trends.buckets.map(bucket => ({
          date: bucket.key_as_string,
          oee: Math.round(bucket.avg_oee.value * 100) / 100,
          production: bucket.total_production.value
        })),
        shiftPerformance: response.aggregations.shift_performance.buckets.map(bucket => ({
          shift: bucket.key,
          oee: Math.round(bucket.avg_oee.value * 100) / 100,
          production: bucket.total_production.value
        })),
        query_time_ms: response.took
      };

      // Cache for 2 minutes (shorter TTL for dashboard data)
      if (this.redisClient) {
        await this.redisClient.setex(cacheKey, 120, JSON.stringify(result));
      }

      return result;
      
    } catch (error) {
      console.error('❌ Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Build Elasticsearch query from filters
   */
  buildProductionQuery(filters) {
    const query = { bool: { must: [] } };

    if (filters.machineIds && filters.machineIds.length > 0) {
      query.bool.must.push({ terms: { machineId: filters.machineIds } });
    }

    if (filters.dateRange) {
      const dateFilter = { range: { date: {} } };
      if (filters.dateRange.start) dateFilter.range.date.gte = filters.dateRange.start;
      if (filters.dateRange.end) dateFilter.range.date.lte = filters.dateRange.end;
      query.bool.must.push(dateFilter);
    }

    if (filters.shift) {
      query.bool.must.push({ term: { shift: filters.shift } });
    }

    if (filters.operator) {
      query.bool.must.push({ match: { operator: filters.operator } });
    }

    if (filters.minOEE) {
      query.bool.must.push({ range: { 'performance.oee': { gte: filters.minOEE } } });
    }

    return query.bool.must.length > 0 ? query : { match_all: {} };
  }

  /**
   * Build production aggregations
   */
  buildProductionAggregations(filters) {
    return {
      total_production: { sum: { field: 'production.good' } },
      total_rejects: { sum: { field: 'production.rejects' } },
      avg_oee: { avg: { field: 'performance.oee' } },
      avg_availability: { avg: { field: 'performance.availability' } },
      avg_performance: { avg: { field: 'performance.performance' } },
      avg_quality: { avg: { field: 'performance.quality' } }
    };
  }

  /**
   * Process aggregation results
   */
  processAggregations(aggregations) {
    if (!aggregations) return {};

    return {
      totalProduction: aggregations.total_production?.value || 0,
      totalRejects: aggregations.total_rejects?.value || 0,
      averageOEE: Math.round((aggregations.avg_oee?.value || 0) * 100) / 100,
      averageAvailability: Math.round((aggregations.avg_availability?.value || 0) * 100) / 100,
      averagePerformance: Math.round((aggregations.avg_performance?.value || 0) * 100) / 100,
      averageQuality: Math.round((aggregations.avg_quality?.value || 0) * 100) / 100
    };
  }

  /**
   * Clear production-related cache entries
   */
  async clearProductionCache() {
    if (!this.redisClient) return;
    
    try {
      const keys = await this.redisClient.keys(`${this.cachePrefix}*`);
      if (keys.length > 0) {
        await this.redisClient.del(...keys);
        console.log(`🧹 Cleared ${keys.length} production cache entries`);
      }
    } catch (error) {
      console.error('⚠️ Error clearing production cache:', error);
    }
  }

  /**
   * Check if Elasticsearch is available and index exists
   */
  async isAvailable() {
    try {
      // Import here to avoid circular dependency
      const { checkElasticsearchHealth } = await import('../config/elasticsearch.js');
      const health = await checkElasticsearchHealth();
      if (!health) {
        return false;
      }
      
      // Check if index exists
      const indexExists = await esClient.indices.exists({ index: this.indexName });
      return indexExists;
    } catch (error) {
      console.error('⚠️ Error checking Elasticsearch availability:', error);
      return false;
    }
  }

  /**
   * Get production chart data - placeholder for MySQL fallback
   */
  async getProductionChart(filters) {
    // This is a placeholder - all chart queries will fallback to MySQL
    throw new Error('Production chart data not available in Elasticsearch, using MySQL fallback');
  }

  /**
   * Get production sidecards data - placeholder for MySQL fallback
   */
  async getProductionSidecards(filters) {
    // This is a placeholder - all sidecard queries will fallback to MySQL
    throw new Error('Production sidecards data not available in Elasticsearch, using MySQL fallback');
  }

  /**
   * Get machine performance data - placeholder for MySQL fallback
   */
  async getMachinePerformance(filters) {
    // This is a placeholder - all machine performance queries will fallback to MySQL
    throw new Error('Machine performance data not available in Elasticsearch, using MySQL fallback');
  }

  /**
   * Get index statistics
   */
  async getIndexStats() {
    try {
      const stats = await esClient.indices.stats({ index: this.indexName });
      return {
        documentCount: stats.indices[this.indexName]?.total?.docs?.count || 0,
        indexSize: stats.indices[this.indexName]?.total?.store?.size_in_bytes || 0,
        status: 'available'
      };
    } catch (error) {
      console.error('⚠️ Error getting index stats:', error);
      return {
        documentCount: 0,
        indexSize: 0,
        status: 'error',
        error: error.message
      };
    }
  }
}

export default new ElasticsearchProductionService();
