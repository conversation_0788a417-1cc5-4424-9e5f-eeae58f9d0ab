import React from 'react';
import { Space } from 'antd';
import { LineChartOutlined } from '@ant-design/icons';

const EfficiencyTrendsCard = ({ loading, filters }) => {
  return (
    <div style={{ 
      height: '300px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)',
      borderRadius: '12px'
    }}>
      <Space direction="vertical" align="center">
        <LineChartOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />
        <h3 style={{ color: '#fa8c16', margin: 0 }}>Efficiency Trends AI</h3>
        <p style={{ color: '#8c8c8c', textAlign: 'center' }}>
          Deep learning trend analysis and predictive forecasting
        </p>
      </Space>
    </div>
  );
};

export default EfficiencyTrendsCard;
