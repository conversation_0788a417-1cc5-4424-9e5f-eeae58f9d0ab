$body = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

Write-Host "=== Testing Login with Headers ==="
Write-Host "Testing login with body: $body"

try {
    # Create a session to maintain cookies
    $session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

    # Use Invoke-WebRequest to get full response details including headers
    $loginWebResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/login" -Method POST -ContentType "application/json" -Body $body -WebSession $session

    Write-Host "Login Response Status: $($loginWebResponse.StatusCode)"
    Write-Host "Response Headers:"
    foreach ($header in $loginWebResponse.Headers.GetEnumerator()) {
        if ($header.Key -like "*cookie*" -or $header.Key -like "*set-cookie*") {
            Write-Host "  $($header.Key): $($header.Value)"
        }
    }

    # Parse JSON response
    $loginResponse = $loginWebResponse.Content | ConvertFrom-Json
    Write-Host "Login Success!"
    $token = $loginResponse.data.token
    Write-Host "Token: $($token.Substring(0, 50))..."

    # Check cookies in session
    Write-Host "`nCookies in session:"
    $cookieCount = 0
    foreach ($cookie in $session.Cookies.GetCookies("http://localhost:5000")) {
        Write-Host "  $($cookie.Name) = $($cookie.Value.Substring(0, [Math]::Min(50, $cookie.Value.Length)))... (HttpOnly: $($cookie.HttpOnly), Secure: $($cookie.Secure))"
        $cookieCount++
    }
    Write-Host "Total cookies: $cookieCount"

    Write-Host "`n=== Testing SSE Token with Cookies ==="
    # Test SSE token endpoint using the same session (with cookies)
    $sseResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/sse-token" -Method GET -WebSession $session
    Write-Host "SSE Token Success: $($sseResponse | ConvertTo-Json)"

    Write-Host "`n=== Testing /api/me with Cookies ==="
    # Test the /api/me endpoint that the browser is failing on
    $meResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/me" -Method GET -WebSession $session
    Write-Host "/api/me Success: $($meResponse | ConvertTo-Json)"

    Write-Host "`n=== Testing Debug Cookies Endpoint ==="
    # Test the debug endpoint to see cookie information
    $debugResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/debug-cookies" -Method GET -WebSession $session
    Write-Host "Debug Cookies: $($debugResponse | ConvertTo-Json)"

} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
