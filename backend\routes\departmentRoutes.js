import express from 'express';
import auth from '../middleware/auth.js';
import { checkPermission, filterByDepartment } from '../middleware/permission.js';
import { corsOptions } from '../middleware/cors.js';
import cors from 'cors';
import db from '../db.js';

const router = express.Router();

// @route   GET api/departments
// @desc    Get all departments
// @access  Private (with permission)
router.get('/', cors(corsOptions), auth, checkPermission(['view_all_departments', 'view_departments']), async (req, res) => {
  try {
    const [departments] = await db.execute('SELECT * FROM departments ORDER BY name');
    res.json({ success: true, data: departments });
  } catch (err) {
    console.error('Error fetching departments:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   GET api/departments/:id
// @desc    Get department by ID
// @access  Private (with permission)
router.get('/:id', cors(corsOptions), auth, checkPermission('view_all_departments'), async (req, res) => {
  try {
    const [departments] = await db.execute('SELECT * FROM departments WHERE id = ?', [req.params.id]);
    
    if (departments.length === 0) {
      return res.status(404).json({ success: false, message: 'Department not found' });
    }
    
    res.json({ success: true, data: departments[0] });
  } catch (err) {
    console.error('Error fetching department:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   POST api/departments
// @desc    Create a department
// @access  Private (with permission)
router.post('/', cors(corsOptions), auth, checkPermission('manage_departments'), async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ success: false, message: 'Department name is required' });
    }
    
    const [result] = await db.execute(
      'INSERT INTO departments (name, description) VALUES (?, ?)',
      [name, description || null]
    );
    
    const [newDepartment] = await db.execute('SELECT * FROM departments WHERE id = ?', [result.insertId]);
    
    res.status(201).json({ success: true, data: newDepartment[0] });
  } catch (err) {
    console.error('Error creating department:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   PUT api/departments/:id
// @desc    Update a department
// @access  Private (with permission)
router.put('/:id', cors(corsOptions), auth, checkPermission('manage_departments'), async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ success: false, message: 'Department name is required' });
    }
    
    const [result] = await db.execute(
      'UPDATE departments SET name = ?, description = ? WHERE id = ?',
      [name, description || null, req.params.id]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Department not found' });
    }
    
    const [updatedDepartment] = await db.execute('SELECT * FROM departments WHERE id = ?', [req.params.id]);
    
    res.json({ success: true, data: updatedDepartment[0] });
  } catch (err) {
    console.error('Error updating department:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   DELETE api/departments/:id
// @desc    Delete a department
// @access  Private (with permission)
router.delete('/:id', cors(corsOptions), auth, checkPermission('manage_departments'), async (req, res) => {
  try {
    // Check if department is in use
    const [usersInDept] = await db.execute('SELECT COUNT(*) as count FROM users WHERE department_id = ?', [req.params.id]);
    
    if (usersInDept[0].count > 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Cannot delete department that has users assigned to it' 
      });
    }
    
    const [result] = await db.execute('DELETE FROM departments WHERE id = ?', [req.params.id]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Department not found' });
    }
    
    res.json({ success: true, message: 'Department deleted successfully' });
  } catch (err) {
    console.error('Error deleting department:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   GET api/departments/:id/users
// @desc    Get all users in a department
// @access  Private (with permission)
router.get('/:id/users', cors(corsOptions), auth, checkPermission(['manage_departments', 'manage_users']), async (req, res) => {
  try {
    const [users] = await db.execute(
      `SELECT u.id, u.username, u.email, u.role, r.name as role_name, u.created_at 
       FROM users u 
       LEFT JOIN roles r ON u.role_id = r.id 
       WHERE u.department_id = ?`,
      [req.params.id]
    );
    
    res.json({ success: true, data: users });
  } catch (err) {
    console.error('Error fetching department users:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   POST api/departments/user-access
// @desc    Grant a user access to additional departments
// @access  Private (with permission)
router.post('/user-access', cors(corsOptions), auth, checkPermission('manage_departments'), async (req, res) => {
  try {
    const { userId, departmentId } = req.body;
    
    if (!userId || !departmentId) {
      return res.status(400).json({ success: false, message: 'User ID and Department ID are required' });
    }
    
    // Check if user exists
    const [userCheck] = await db.execute('SELECT id FROM users WHERE id = ?', [userId]);
    if (userCheck.length === 0) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    
    // Check if department exists
    const [deptCheck] = await db.execute('SELECT id FROM departments WHERE id = ?', [departmentId]);
    if (deptCheck.length === 0) {
      return res.status(404).json({ success: false, message: 'Department not found' });
    }
    
    // Add access (ignore if already exists due to UNIQUE constraint)
    await db.execute(
      'INSERT IGNORE INTO department_access (user_id, department_id) VALUES (?, ?)',
      [userId, departmentId]
    );
    
    res.json({ success: true, message: 'Department access granted successfully' });
  } catch (err) {
    console.error('Error granting department access:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   DELETE api/departments/user-access
// @desc    Remove a user's access to an additional department
// @access  Private (with permission)
router.delete('/user-access', cors(corsOptions), auth, checkPermission('manage_departments'), async (req, res) => {
  try {
    const { userId, departmentId } = req.body;
    
    if (!userId || !departmentId) {
      return res.status(400).json({ success: false, message: 'User ID and Department ID are required' });
    }
    
    const [result] = await db.execute(
      'DELETE FROM department_access WHERE user_id = ? AND department_id = ?',
      [userId, departmentId]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Access record not found' });
    }
    
    res.json({ success: true, message: 'Department access removed successfully' });
  } catch (err) {
    console.error('Error removing department access:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// @route   GET api/departments/user/:userId/access
// @desc    Get all departments a user has access to
// @access  Private (with permission)
router.get('/user/:userId/access', cors(corsOptions), auth, checkPermission(['manage_departments', 'manage_users']), async (req, res) => {
  try {
    // Get user's primary department
    const [userDept] = await db.execute(
      `SELECT u.department_id, d.name as department_name 
       FROM users u 
       LEFT JOIN departments d ON u.department_id = d.id 
       WHERE u.id = ?`,
      [req.params.userId]
    );
    
    if (userDept.length === 0) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    
    // Get additional department access
    const [additionalAccess] = await db.execute(
      `SELECT da.department_id, d.name as department_name 
       FROM department_access da 
       JOIN departments d ON da.department_id = d.id 
       WHERE da.user_id = ?`,
      [req.params.userId]
    );
    
    res.json({ 
      success: true, 
      data: {
        primaryDepartment: userDept[0].department_id ? {
          id: userDept[0].department_id,
          name: userDept[0].department_name
        } : null,
        additionalAccess: additionalAccess
      }
    });
  } catch (err) {
    console.error('Error fetching user department access:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

export default router;