# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for native modules (mysql2, canvas, puppeteer)
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont && \
    ln -sf python3 /usr/bin/python

# Set Puppeteer to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install dependencies with verbose logging for debugging
RUN echo "Installing Node.js dependencies..." && \
    echo "Checking for package-lock.json..." && \
    ls -la package* && \
    if [ -f package-lock.json ]; then \
        echo "Using npm ci with package-lock.json" && \
        npm ci --verbose; \
    else \
        echo "No package-lock.json found, using npm install" && \
        npm install --verbose; \
    fi && \
    echo "Verifying apicache installation..." && \
    npm list apicache && \
    echo "Node modules installed successfully" && \
    npm cache clean --force

# Copy backend source code (excluding node_modules)
COPY . .

# Create a verification script to check dependencies
RUN echo "#!/bin/sh" > /app/verify-deps.sh && \
    echo "echo 'Verifying critical dependencies...'" >> /app/verify-deps.sh && \
    echo "node -e \"try { require('apicache'); console.log('✅ apicache found'); } catch(e) { console.error('❌ apicache missing:', e.message); process.exit(1); }\"" >> /app/verify-deps.sh && \
    echo "node -e \"try { require('express'); console.log('✅ express found'); } catch(e) { console.error('❌ express missing:', e.message); process.exit(1); }\"" >> /app/verify-deps.sh && \
    echo "node -e \"try { require('mysql2'); console.log('✅ mysql2 found'); } catch(e) { console.error('❌ mysql2 missing:', e.message); process.exit(1); }\"" >> /app/verify-deps.sh && \
    echo "echo 'All dependencies verified successfully'" >> /app/verify-deps.sh && \
    chmod +x /app/verify-deps.sh

# Run dependency verification
RUN /app/verify-deps.sh

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory and node_modules
RUN chown -R nodejs:nodejs /app && \
    chown -R nodejs:nodejs /app/node_modules

# Switch to non-root user
USER nodejs

# Final verification as the nodejs user
RUN /app/verify-deps.sh

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/api/health/ping', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["node", "server.js"]
