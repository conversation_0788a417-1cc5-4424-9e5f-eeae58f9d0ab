var ut=Object.defineProperty;var lt=(n,t,e)=>t in n?ut(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var c=(n,t,e)=>lt(n,typeof t!="symbol"?t+"":t,e);import{S as C,G as dt,r as de,h as ft,R as o,A as Ce,b as z,c as H,C as L,m as ve,f as U,E as Re,i as pe,M as ht,a as ae,F as mt}from"./index-N0wOiMt6.js";import{a as wt,C as gt,b as yt,c as bt,d as xt,e as Mt,P as Dt,T as Tt,p as kt,f as Et,g as Pt,B as Ot,L as _t}from"./index-D8SzF-U0.js";import"./MainLayout-Dv5e74xz.js";import{l as Yt,g as Ne,a as <PERSON>,b as He}from"./chart-config-BbI-7uvm.js";import{R as Ct}from"./LineChartOutlined-2kYZXigx.js";import{S as Ie}from"./index-BP6n0Cjb.js";import{P as fe}from"./progress-CyD0QBQj.js";import{R as vt}from"./DashboardOutlined-CBEyWGTp.js";import{R as Rt}from"./ClockCircleOutlined-C6SZLNSK.js";import{R as pt}from"./CheckCircleOutlined-DcDkn_d7.js";import{R as Nt}from"./CloseCircleOutlined-kKLfKks7.js";import"./logo_for_DarkMode-hGdULrut.js";import"./ZoomOutOutlined-CdPjzMfa.js";import"./usePermission-DPfdkrAa.js";import"./SSENotificationBell-CKOcNCtR.js";import"./dayjs.min-BHt7dFLo.js";import"./relativeTime--yFQXkk2.js";import"./fr-DC3HkAP8.js";import"./ReloadOutlined-DZn6IdM2.js";import"./index-C2CgWKoY.js";import"./BellOutlined-D7NJM_PG.js";import"./InfoCircleOutlined-DImdGCrM.js";import"./AlertOutlined-8CxkZMww.js";import"./AppstoreOutlined-Dq42CaBx.js";import"./ToolOutlined-C50QNs7D.js";import"./HomeOutlined-LEhKHHpT.js";import"./BarChartOutlined-DzqCoGDG.js";import"./index-CPp7fyYr.js";import"./CloseOutlined-BCYtRaiR.js";import"./CalendarOutlined-BxlyaoqS.js";import"./index-DIJFODWS.js";const Ge=6048e5,Wt=864e5,K=6e4,ee=36e5,Ht=1e3,qe=Symbol.for("constructDateFrom");function k(n,t){return typeof n=="function"?n(t):n&&typeof n=="object"&&qe in n?n[qe](t):n instanceof Date?new n.constructor(t):new Date(t)}function h(n,t){return k(t||n,n)}function ue(n,t,e){const r=h(n,e==null?void 0:e.in);return isNaN(t)?k((e==null?void 0:e.in)||n,NaN):(t&&r.setDate(r.getDate()+t),r)}function xe(n,t,e){const r=h(n,e==null?void 0:e.in);if(isNaN(t))return k(n,NaN);if(!t)return r;const a=r.getDate(),s=k(n,r.getTime());s.setMonth(r.getMonth()+t+1,0);const i=s.getDate();return a>=i?s:(r.setFullYear(s.getFullYear(),s.getMonth(),a),r)}function Me(n,t,e){return k(n,+h(n)+t)}function It(n,t,e){return Me(n,t*ee)}let qt={};function B(){return qt}function W(n,t){var u,d,m,M;const e=B(),r=(t==null?void 0:t.weekStartsOn)??((d=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:d.weekStartsOn)??e.weekStartsOn??((M=(m=e.locale)==null?void 0:m.options)==null?void 0:M.weekStartsOn)??0,a=h(n,t==null?void 0:t.in),s=a.getDay(),i=(s<r?7:0)+s-r;return a.setDate(a.getDate()-i),a.setHours(0,0,0,0),a}function $(n,t){return W(n,{...t,weekStartsOn:1})}function ze(n,t){const e=h(n,t==null?void 0:t.in),r=e.getFullYear(),a=k(e,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);const s=$(a),i=k(e,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);const u=$(i);return e.getTime()>=s.getTime()?r+1:e.getTime()>=u.getTime()?r:r-1}function ce(n){const t=h(n),e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),+n-+e}function G(n,...t){const e=k.bind(null,t.find(r=>typeof r=="object"));return t.map(e)}function ge(n,t){const e=h(n,t==null?void 0:t.in);return e.setHours(0,0,0,0),e}function Xe(n,t,e){const[r,a]=G(e==null?void 0:e.in,n,t),s=ge(r),i=ge(a),u=+s-ce(s),d=+i-ce(i);return Math.round((u-d)/Wt)}function Lt(n,t){const e=ze(n,t),r=k(n,0);return r.setFullYear(e,0,4),r.setHours(0,0,0,0),$(r)}function Ft(n,t,e){const r=h(n,e==null?void 0:e.in);return r.setTime(r.getTime()+t*K),r}function St(n,t,e){return xe(n,t*3,e)}function Qt(n,t,e){return Me(n,t*1e3)}function At(n,t,e){return ue(n,t*7,e)}function Bt(n,t,e){return xe(n,t*12,e)}function J(n,t){const e=+h(n)-+h(t);return e<0?-1:e>0?1:e}function Gt(n){return n instanceof Date||typeof n=="object"&&Object.prototype.toString.call(n)==="[object Date]"}function $e(n){return!(!Gt(n)&&typeof n!="number"||isNaN(+h(n)))}function zt(n,t,e){const[r,a]=G(e==null?void 0:e.in,n,t),s=r.getFullYear()-a.getFullYear(),i=r.getMonth()-a.getMonth();return s*12+i}function Xt(n,t,e){const[r,a]=G(e==null?void 0:e.in,n,t);return r.getFullYear()-a.getFullYear()}function Ue(n,t,e){const[r,a]=G(e==null?void 0:e.in,n,t),s=Le(r,a),i=Math.abs(Xe(r,a));r.setDate(r.getDate()-s*i);const u=+(Le(r,a)===-s),d=s*(i-u);return d===0?0:d}function Le(n,t){const e=n.getFullYear()-t.getFullYear()||n.getMonth()-t.getMonth()||n.getDate()-t.getDate()||n.getHours()-t.getHours()||n.getMinutes()-t.getMinutes()||n.getSeconds()-t.getSeconds()||n.getMilliseconds()-t.getMilliseconds();return e<0?-1:e>0?1:e}function te(n){return t=>{const r=(n?Math[n]:Math.trunc)(t);return r===0?0:r}}function $t(n,t,e){const[r,a]=G(e==null?void 0:e.in,n,t),s=(+r-+a)/ee;return te(e==null?void 0:e.roundingMethod)(s)}function De(n,t){return+h(n)-+h(t)}function Ut(n,t,e){const r=De(n,t)/K;return te(e==null?void 0:e.roundingMethod)(r)}function je(n,t){const e=h(n,t==null?void 0:t.in);return e.setHours(23,59,59,999),e}function Ve(n,t){const e=h(n,t==null?void 0:t.in),r=e.getMonth();return e.setFullYear(e.getFullYear(),r+1,0),e.setHours(23,59,59,999),e}function jt(n,t){const e=h(n,t==null?void 0:t.in);return+je(e,t)==+Ve(e,t)}function Ze(n,t,e){const[r,a,s]=G(e==null?void 0:e.in,n,n,t),i=J(a,s),u=Math.abs(zt(a,s));if(u<1)return 0;a.getMonth()===1&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-i*u);let d=J(a,s)===-i;jt(r)&&u===1&&J(r,s)===1&&(d=!1);const m=i*(u-+d);return m===0?0:m}function Vt(n,t,e){const r=Ze(n,t,e)/3;return te(e==null?void 0:e.roundingMethod)(r)}function Zt(n,t,e){const r=De(n,t)/1e3;return te(e==null?void 0:e.roundingMethod)(r)}function Jt(n,t,e){const r=Ue(n,t,e)/7;return te(e==null?void 0:e.roundingMethod)(r)}function Kt(n,t,e){const[r,a]=G(e==null?void 0:e.in,n,t),s=J(r,a),i=Math.abs(Xt(r,a));r.setFullYear(1584),a.setFullYear(1584);const u=J(r,a)===-s,d=s*(i-+u);return d===0?0:d}function er(n,t){const e=h(n,t==null?void 0:t.in),r=e.getMonth(),a=r-r%3;return e.setMonth(a,1),e.setHours(0,0,0,0),e}function tr(n,t){const e=h(n,t==null?void 0:t.in);return e.setDate(1),e.setHours(0,0,0,0),e}function rr(n,t){const e=h(n,t==null?void 0:t.in),r=e.getFullYear();return e.setFullYear(r+1,0,0),e.setHours(23,59,59,999),e}function Je(n,t){const e=h(n,t==null?void 0:t.in);return e.setFullYear(e.getFullYear(),0,1),e.setHours(0,0,0,0),e}function nr(n,t){const e=h(n,t==null?void 0:t.in);return e.setMinutes(59,59,999),e}function ar(n,t){var u,d;const e=B(),r=e.weekStartsOn??((d=(u=e.locale)==null?void 0:u.options)==null?void 0:d.weekStartsOn)??0,a=h(n,t==null?void 0:t.in),s=a.getDay(),i=(s<r?-7:0)+6-(s-r);return a.setDate(a.getDate()+i),a.setHours(23,59,59,999),a}function sr(n,t){const e=h(n,t==null?void 0:t.in);return e.setSeconds(59,999),e}function ir(n,t){const e=h(n,t==null?void 0:t.in),r=e.getMonth(),a=r-r%3+3;return e.setMonth(a,0),e.setHours(23,59,59,999),e}function cr(n,t){const e=h(n,t==null?void 0:t.in);return e.setMilliseconds(999),e}const or={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},ur=(n,t,e)=>{let r;const a=or[n];return typeof a=="string"?r=a:t===1?r=a.one:r=a.other.replace("{{count}}",t.toString()),e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"in "+r:r+" ago":r};function he(n){return(t={})=>{const e=t.width?String(t.width):n.defaultWidth;return n.formats[e]||n.formats[n.defaultWidth]}}const lr={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},dr={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},fr={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},hr={date:he({formats:lr,defaultWidth:"full"}),time:he({formats:dr,defaultWidth:"full"}),dateTime:he({formats:fr,defaultWidth:"full"})},mr={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},wr=(n,t,e,r)=>mr[n];function j(n){return(t,e)=>{const r=e!=null&&e.context?String(e.context):"standalone";let a;if(r==="formatting"&&n.formattingValues){const i=n.defaultFormattingWidth||n.defaultWidth,u=e!=null&&e.width?String(e.width):i;a=n.formattingValues[u]||n.formattingValues[i]}else{const i=n.defaultWidth,u=e!=null&&e.width?String(e.width):n.defaultWidth;a=n.values[u]||n.values[i]}const s=n.argumentCallback?n.argumentCallback(t):t;return a[s]}}const gr={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},yr={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},br={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},xr={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Mr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Dr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Tr=(n,t)=>{const e=Number(n),r=e%100;if(r>20||r<10)switch(r%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},kr={ordinalNumber:Tr,era:j({values:gr,defaultWidth:"wide"}),quarter:j({values:yr,defaultWidth:"wide",argumentCallback:n=>n-1}),month:j({values:br,defaultWidth:"wide"}),day:j({values:xr,defaultWidth:"wide"}),dayPeriod:j({values:Mr,defaultWidth:"wide",formattingValues:Dr,defaultFormattingWidth:"wide"})};function V(n){return(t,e={})=>{const r=e.width,a=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],s=t.match(a);if(!s)return null;const i=s[0],u=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],d=Array.isArray(u)?Pr(u,_=>_.test(i)):Er(u,_=>_.test(i));let m;m=n.valueCallback?n.valueCallback(d):d,m=e.valueCallback?e.valueCallback(m):m;const M=t.slice(i.length);return{value:m,rest:M}}}function Er(n,t){for(const e in n)if(Object.prototype.hasOwnProperty.call(n,e)&&t(n[e]))return e}function Pr(n,t){for(let e=0;e<n.length;e++)if(t(n[e]))return e}function Or(n){return(t,e={})=>{const r=t.match(n.matchPattern);if(!r)return null;const a=r[0],s=t.match(n.parsePattern);if(!s)return null;let i=n.valueCallback?n.valueCallback(s[0]):s[0];i=e.valueCallback?e.valueCallback(i):i;const u=t.slice(a.length);return{value:i,rest:u}}}const _r=/^(\d+)(th|st|nd|rd)?/i,Yr=/\d+/i,Cr={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},vr={any:[/^b/i,/^(a|c)/i]},Rr={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},pr={any:[/1/i,/2/i,/3/i,/4/i]},Nr={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Wr={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Hr={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Ir={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},qr={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Lr={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Fr={ordinalNumber:Or({matchPattern:_r,parsePattern:Yr,valueCallback:n=>parseInt(n,10)}),era:V({matchPatterns:Cr,defaultMatchWidth:"wide",parsePatterns:vr,defaultParseWidth:"any"}),quarter:V({matchPatterns:Rr,defaultMatchWidth:"wide",parsePatterns:pr,defaultParseWidth:"any",valueCallback:n=>n+1}),month:V({matchPatterns:Nr,defaultMatchWidth:"wide",parsePatterns:Wr,defaultParseWidth:"any"}),day:V({matchPatterns:Hr,defaultMatchWidth:"wide",parsePatterns:Ir,defaultParseWidth:"any"}),dayPeriod:V({matchPatterns:qr,defaultMatchWidth:"any",parsePatterns:Lr,defaultParseWidth:"any"})},Te={code:"en-US",formatDistance:ur,formatLong:hr,formatRelative:wr,localize:kr,match:Fr,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Sr(n,t){const e=h(n,t==null?void 0:t.in);return Xe(e,Je(e))+1}function Ke(n,t){const e=h(n,t==null?void 0:t.in),r=+$(e)-+Lt(e);return Math.round(r/Ge)+1}function ke(n,t){var M,_,Y,v;const e=h(n,t==null?void 0:t.in),r=e.getFullYear(),a=B(),s=(t==null?void 0:t.firstWeekContainsDate)??((_=(M=t==null?void 0:t.locale)==null?void 0:M.options)==null?void 0:_.firstWeekContainsDate)??a.firstWeekContainsDate??((v=(Y=a.locale)==null?void 0:Y.options)==null?void 0:v.firstWeekContainsDate)??1,i=k((t==null?void 0:t.in)||n,0);i.setFullYear(r+1,0,s),i.setHours(0,0,0,0);const u=W(i,t),d=k((t==null?void 0:t.in)||n,0);d.setFullYear(r,0,s),d.setHours(0,0,0,0);const m=W(d,t);return+e>=+u?r+1:+e>=+m?r:r-1}function Qr(n,t){var u,d,m,M;const e=B(),r=(t==null?void 0:t.firstWeekContainsDate)??((d=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:d.firstWeekContainsDate)??e.firstWeekContainsDate??((M=(m=e.locale)==null?void 0:m.options)==null?void 0:M.firstWeekContainsDate)??1,a=ke(n,t),s=k((t==null?void 0:t.in)||n,0);return s.setFullYear(a,0,r),s.setHours(0,0,0,0),W(s,t)}function et(n,t){const e=h(n,t==null?void 0:t.in),r=+W(e,t)-+Qr(e,t);return Math.round(r/Ge)+1}function x(n,t){const e=n<0?"-":"",r=Math.abs(n).toString().padStart(t,"0");return e+r}const F={y(n,t){const e=n.getFullYear(),r=e>0?e:1-e;return x(t==="yy"?r%100:r,t.length)},M(n,t){const e=n.getMonth();return t==="M"?String(e+1):x(e+1,2)},d(n,t){return x(n.getDate(),t.length)},a(n,t){const e=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];case"aaaa":default:return e==="am"?"a.m.":"p.m."}},h(n,t){return x(n.getHours()%12||12,t.length)},H(n,t){return x(n.getHours(),t.length)},m(n,t){return x(n.getMinutes(),t.length)},s(n,t){return x(n.getSeconds(),t.length)},S(n,t){const e=t.length,r=n.getMilliseconds(),a=Math.trunc(r*Math.pow(10,e-3));return x(a,t.length)}},X={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Fe={G:function(n,t,e){const r=n.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return e.era(r,{width:"abbreviated"});case"GGGGG":return e.era(r,{width:"narrow"});case"GGGG":default:return e.era(r,{width:"wide"})}},y:function(n,t,e){if(t==="yo"){const r=n.getFullYear(),a=r>0?r:1-r;return e.ordinalNumber(a,{unit:"year"})}return F.y(n,t)},Y:function(n,t,e,r){const a=ke(n,r),s=a>0?a:1-a;if(t==="YY"){const i=s%100;return x(i,2)}return t==="Yo"?e.ordinalNumber(s,{unit:"year"}):x(s,t.length)},R:function(n,t){const e=ze(n);return x(e,t.length)},u:function(n,t){const e=n.getFullYear();return x(e,t.length)},Q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return x(r,2);case"Qo":return e.ordinalNumber(r,{unit:"quarter"});case"QQQ":return e.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return e.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return x(r,2);case"qo":return e.ordinalNumber(r,{unit:"quarter"});case"qqq":return e.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return e.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,t,e){const r=n.getMonth();switch(t){case"M":case"MM":return F.M(n,t);case"Mo":return e.ordinalNumber(r+1,{unit:"month"});case"MMM":return e.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return e.month(r,{width:"wide",context:"formatting"})}},L:function(n,t,e){const r=n.getMonth();switch(t){case"L":return String(r+1);case"LL":return x(r+1,2);case"Lo":return e.ordinalNumber(r+1,{unit:"month"});case"LLL":return e.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return e.month(r,{width:"wide",context:"standalone"})}},w:function(n,t,e,r){const a=et(n,r);return t==="wo"?e.ordinalNumber(a,{unit:"week"}):x(a,t.length)},I:function(n,t,e){const r=Ke(n);return t==="Io"?e.ordinalNumber(r,{unit:"week"}):x(r,t.length)},d:function(n,t,e){return t==="do"?e.ordinalNumber(n.getDate(),{unit:"date"}):F.d(n,t)},D:function(n,t,e){const r=Sr(n);return t==="Do"?e.ordinalNumber(r,{unit:"dayOfYear"}):x(r,t.length)},E:function(n,t,e){const r=n.getDay();switch(t){case"E":case"EE":case"EEE":return e.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(r,{width:"short",context:"formatting"});case"EEEE":default:return e.day(r,{width:"wide",context:"formatting"})}},e:function(n,t,e,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return x(s,2);case"eo":return e.ordinalNumber(s,{unit:"day"});case"eee":return e.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(a,{width:"short",context:"formatting"});case"eeee":default:return e.day(a,{width:"wide",context:"formatting"})}},c:function(n,t,e,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return x(s,t.length);case"co":return e.ordinalNumber(s,{unit:"day"});case"ccc":return e.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(a,{width:"narrow",context:"standalone"});case"cccccc":return e.day(a,{width:"short",context:"standalone"});case"cccc":default:return e.day(a,{width:"wide",context:"standalone"})}},i:function(n,t,e){const r=n.getDay(),a=r===0?7:r;switch(t){case"i":return String(a);case"ii":return x(a,t.length);case"io":return e.ordinalNumber(a,{unit:"day"});case"iii":return e.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(r,{width:"short",context:"formatting"});case"iiii":default:return e.day(r,{width:"wide",context:"formatting"})}},a:function(n,t,e){const a=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(n,t,e){const r=n.getHours();let a;switch(r===12?a=X.noon:r===0?a=X.midnight:a=r/12>=1?"pm":"am",t){case"b":case"bb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,t,e){const r=n.getHours();let a;switch(r>=17?a=X.evening:r>=12?a=X.afternoon:r>=4?a=X.morning:a=X.night,t){case"B":case"BB":case"BBB":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,t,e){if(t==="ho"){let r=n.getHours()%12;return r===0&&(r=12),e.ordinalNumber(r,{unit:"hour"})}return F.h(n,t)},H:function(n,t,e){return t==="Ho"?e.ordinalNumber(n.getHours(),{unit:"hour"}):F.H(n,t)},K:function(n,t,e){const r=n.getHours()%12;return t==="Ko"?e.ordinalNumber(r,{unit:"hour"}):x(r,t.length)},k:function(n,t,e){let r=n.getHours();return r===0&&(r=24),t==="ko"?e.ordinalNumber(r,{unit:"hour"}):x(r,t.length)},m:function(n,t,e){return t==="mo"?e.ordinalNumber(n.getMinutes(),{unit:"minute"}):F.m(n,t)},s:function(n,t,e){return t==="so"?e.ordinalNumber(n.getSeconds(),{unit:"second"}):F.s(n,t)},S:function(n,t){return F.S(n,t)},X:function(n,t,e){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Qe(r);case"XXXX":case"XX":return A(r);case"XXXXX":case"XXX":default:return A(r,":")}},x:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"x":return Qe(r);case"xxxx":case"xx":return A(r);case"xxxxx":case"xxx":default:return A(r,":")}},O:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Se(r,":");case"OOOO":default:return"GMT"+A(r,":")}},z:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Se(r,":");case"zzzz":default:return"GMT"+A(r,":")}},t:function(n,t,e){const r=Math.trunc(+n/1e3);return x(r,t.length)},T:function(n,t,e){return x(+n,t.length)}};function Se(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),a=Math.trunc(r/60),s=r%60;return s===0?e+String(a):e+String(a)+t+x(s,2)}function Qe(n,t){return n%60===0?(n>0?"-":"+")+x(Math.abs(n)/60,2):A(n,t)}function A(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),a=x(Math.trunc(r/60),2),s=x(r%60,2);return e+a+t+s}const Ae=(n,t)=>{switch(n){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},tt=(n,t)=>{switch(n){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},Ar=(n,t)=>{const e=n.match(/(P+)(p+)?/)||[],r=e[1],a=e[2];if(!a)return Ae(n,t);let s;switch(r){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;case"PPPP":default:s=t.dateTime({width:"full"});break}return s.replace("{{date}}",Ae(r,t)).replace("{{time}}",tt(a,t))},ye={p:tt,P:Ar},Br=/^D+$/,Gr=/^Y+$/,zr=["D","DD","YY","YYYY"];function rt(n){return Br.test(n)}function nt(n){return Gr.test(n)}function be(n,t,e){const r=Xr(n,t,e);if(console.warn(r),zr.includes(n))throw new RangeError(r)}function Xr(n,t,e){const r=n[0]==="Y"?"years":"days of the month";return`Use \`${n.toLowerCase()}\` instead of \`${n}\` (in \`${t}\`) for formatting ${r} to the input \`${e}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const $r=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ur=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,jr=/^'([^]*?)'?$/,Vr=/''/g,Zr=/[a-zA-Z]/;function Jr(n,t,e){var M,_,Y,v,f,S,Q,l;const r=B(),a=(e==null?void 0:e.locale)??r.locale??Te,s=(e==null?void 0:e.firstWeekContainsDate)??((_=(M=e==null?void 0:e.locale)==null?void 0:M.options)==null?void 0:_.firstWeekContainsDate)??r.firstWeekContainsDate??((v=(Y=r.locale)==null?void 0:Y.options)==null?void 0:v.firstWeekContainsDate)??1,i=(e==null?void 0:e.weekStartsOn)??((S=(f=e==null?void 0:e.locale)==null?void 0:f.options)==null?void 0:S.weekStartsOn)??r.weekStartsOn??((l=(Q=r.locale)==null?void 0:Q.options)==null?void 0:l.weekStartsOn)??0,u=h(n,e==null?void 0:e.in);if(!$e(u))throw new RangeError("Invalid time value");let d=t.match(Ur).map(w=>{const b=w[0];if(b==="p"||b==="P"){const I=ye[b];return I(w,a.formatLong)}return w}).join("").match($r).map(w=>{if(w==="''")return{isToken:!1,value:"'"};const b=w[0];if(b==="'")return{isToken:!1,value:Kr(w)};if(Fe[b])return{isToken:!0,value:w};if(b.match(Zr))throw new RangeError("Format string contains an unescaped latin alphabet character `"+b+"`");return{isToken:!1,value:w}});a.localize.preprocessor&&(d=a.localize.preprocessor(u,d));const m={firstWeekContainsDate:s,weekStartsOn:i,locale:a};return d.map(w=>{if(!w.isToken)return w.value;const b=w.value;(!(e!=null&&e.useAdditionalWeekYearTokens)&&nt(b)||!(e!=null&&e.useAdditionalDayOfYearTokens)&&rt(b))&&be(b,t,String(n));const I=Fe[b[0]];return I(u,b,a.localize,m)}).join("")}function Kr(n){const t=n.match(jr);return t?t[1].replace(Vr,"'"):n}function en(){return Object.assign({},B())}function tn(n,t){const e=h(n,t==null?void 0:t.in).getDay();return e===0?7:e}function rn(n,t){const e=nn(t)?new t(0):k(t,0);return e.setFullYear(n.getFullYear(),n.getMonth(),n.getDate()),e.setHours(n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()),e}function nn(n){var t;return typeof n=="function"&&((t=n.prototype)==null?void 0:t.constructor)===n}const an=10;class at{constructor(){c(this,"subPriority",0)}validate(t,e){return!0}}class sn extends at{constructor(t,e,r,a,s){super(),this.value=t,this.validateValue=e,this.setValue=r,this.priority=a,s&&(this.subPriority=s)}validate(t,e){return this.validateValue(t,this.value,e)}set(t,e,r){return this.setValue(t,e,this.value,r)}}class cn extends at{constructor(e,r){super();c(this,"priority",an);c(this,"subPriority",-1);this.context=e||(a=>k(r,a))}set(e,r){return r.timestampIsSet?e:k(e,rn(e,this.context))}}class y{run(t,e,r,a){const s=this.parse(t,e,r,a);return s?{setter:new sn(s.value,this.validate,this.set,this.priority,this.subPriority),rest:s.rest}:null}validate(t,e,r){return!0}}class on extends y{constructor(){super(...arguments);c(this,"priority",140);c(this,"incompatibleTokens",["R","u","t","T"])}parse(e,r,a){switch(r){case"G":case"GG":case"GGG":return a.era(e,{width:"abbreviated"})||a.era(e,{width:"narrow"});case"GGGGG":return a.era(e,{width:"narrow"});case"GGGG":default:return a.era(e,{width:"wide"})||a.era(e,{width:"abbreviated"})||a.era(e,{width:"narrow"})}}set(e,r,a){return r.era=a,e.setFullYear(a,0,1),e.setHours(0,0,0,0),e}}const P={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},p={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function O(n,t){return n&&{value:t(n.value),rest:n.rest}}function D(n,t){const e=t.match(n);return e?{value:parseInt(e[0],10),rest:t.slice(e[0].length)}:null}function N(n,t){const e=t.match(n);if(!e)return null;if(e[0]==="Z")return{value:0,rest:t.slice(1)};const r=e[1]==="+"?1:-1,a=e[2]?parseInt(e[2],10):0,s=e[3]?parseInt(e[3],10):0,i=e[5]?parseInt(e[5],10):0;return{value:r*(a*ee+s*K+i*Ht),rest:t.slice(e[0].length)}}function st(n){return D(P.anyDigitsSigned,n)}function E(n,t){switch(n){case 1:return D(P.singleDigit,t);case 2:return D(P.twoDigits,t);case 3:return D(P.threeDigits,t);case 4:return D(P.fourDigits,t);default:return D(new RegExp("^\\d{1,"+n+"}"),t)}}function oe(n,t){switch(n){case 1:return D(P.singleDigitSigned,t);case 2:return D(P.twoDigitsSigned,t);case 3:return D(P.threeDigitsSigned,t);case 4:return D(P.fourDigitsSigned,t);default:return D(new RegExp("^-?\\d{1,"+n+"}"),t)}}function Ee(n){switch(n){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function it(n,t){const e=t>0,r=e?t:1-t;let a;if(r<=50)a=n||100;else{const s=r+50,i=Math.trunc(s/100)*100,u=n>=s%100;a=n+i-(u?100:0)}return e?a:1-a}function ct(n){return n%400===0||n%4===0&&n%100!==0}class un extends y{constructor(){super(...arguments);c(this,"priority",130);c(this,"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"])}parse(e,r,a){const s=i=>({year:i,isTwoDigitYear:r==="yy"});switch(r){case"y":return O(E(4,e),s);case"yo":return O(a.ordinalNumber(e,{unit:"year"}),s);default:return O(E(r.length,e),s)}}validate(e,r){return r.isTwoDigitYear||r.year>0}set(e,r,a){const s=e.getFullYear();if(a.isTwoDigitYear){const u=it(a.year,s);return e.setFullYear(u,0,1),e.setHours(0,0,0,0),e}const i=!("era"in r)||r.era===1?a.year:1-a.year;return e.setFullYear(i,0,1),e.setHours(0,0,0,0),e}}class ln extends y{constructor(){super(...arguments);c(this,"priority",130);c(this,"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"])}parse(e,r,a){const s=i=>({year:i,isTwoDigitYear:r==="YY"});switch(r){case"Y":return O(E(4,e),s);case"Yo":return O(a.ordinalNumber(e,{unit:"year"}),s);default:return O(E(r.length,e),s)}}validate(e,r){return r.isTwoDigitYear||r.year>0}set(e,r,a,s){const i=ke(e,s);if(a.isTwoDigitYear){const d=it(a.year,i);return e.setFullYear(d,0,s.firstWeekContainsDate),e.setHours(0,0,0,0),W(e,s)}const u=!("era"in r)||r.era===1?a.year:1-a.year;return e.setFullYear(u,0,s.firstWeekContainsDate),e.setHours(0,0,0,0),W(e,s)}}class dn extends y{constructor(){super(...arguments);c(this,"priority",130);c(this,"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"])}parse(e,r){return oe(r==="R"?4:r.length,e)}set(e,r,a){const s=k(e,0);return s.setFullYear(a,0,4),s.setHours(0,0,0,0),$(s)}}class fn extends y{constructor(){super(...arguments);c(this,"priority",130);c(this,"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"])}parse(e,r){return oe(r==="u"?4:r.length,e)}set(e,r,a){return e.setFullYear(a,0,1),e.setHours(0,0,0,0),e}}class hn extends y{constructor(){super(...arguments);c(this,"priority",120);c(this,"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"])}parse(e,r,a){switch(r){case"Q":case"QQ":return E(r.length,e);case"Qo":return a.ordinalNumber(e,{unit:"quarter"});case"QQQ":return a.quarter(e,{width:"abbreviated",context:"formatting"})||a.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return a.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return a.quarter(e,{width:"wide",context:"formatting"})||a.quarter(e,{width:"abbreviated",context:"formatting"})||a.quarter(e,{width:"narrow",context:"formatting"})}}validate(e,r){return r>=1&&r<=4}set(e,r,a){return e.setMonth((a-1)*3,1),e.setHours(0,0,0,0),e}}class mn extends y{constructor(){super(...arguments);c(this,"priority",120);c(this,"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"])}parse(e,r,a){switch(r){case"q":case"qq":return E(r.length,e);case"qo":return a.ordinalNumber(e,{unit:"quarter"});case"qqq":return a.quarter(e,{width:"abbreviated",context:"standalone"})||a.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return a.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return a.quarter(e,{width:"wide",context:"standalone"})||a.quarter(e,{width:"abbreviated",context:"standalone"})||a.quarter(e,{width:"narrow",context:"standalone"})}}validate(e,r){return r>=1&&r<=4}set(e,r,a){return e.setMonth((a-1)*3,1),e.setHours(0,0,0,0),e}}class wn extends y{constructor(){super(...arguments);c(this,"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]);c(this,"priority",110)}parse(e,r,a){const s=i=>i-1;switch(r){case"M":return O(D(P.month,e),s);case"MM":return O(E(2,e),s);case"Mo":return O(a.ordinalNumber(e,{unit:"month"}),s);case"MMM":return a.month(e,{width:"abbreviated",context:"formatting"})||a.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return a.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return a.month(e,{width:"wide",context:"formatting"})||a.month(e,{width:"abbreviated",context:"formatting"})||a.month(e,{width:"narrow",context:"formatting"})}}validate(e,r){return r>=0&&r<=11}set(e,r,a){return e.setMonth(a,1),e.setHours(0,0,0,0),e}}class gn extends y{constructor(){super(...arguments);c(this,"priority",110);c(this,"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"])}parse(e,r,a){const s=i=>i-1;switch(r){case"L":return O(D(P.month,e),s);case"LL":return O(E(2,e),s);case"Lo":return O(a.ordinalNumber(e,{unit:"month"}),s);case"LLL":return a.month(e,{width:"abbreviated",context:"standalone"})||a.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return a.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return a.month(e,{width:"wide",context:"standalone"})||a.month(e,{width:"abbreviated",context:"standalone"})||a.month(e,{width:"narrow",context:"standalone"})}}validate(e,r){return r>=0&&r<=11}set(e,r,a){return e.setMonth(a,1),e.setHours(0,0,0,0),e}}function yn(n,t,e){const r=h(n,e==null?void 0:e.in),a=et(r,e)-t;return r.setDate(r.getDate()-a*7),h(r,e==null?void 0:e.in)}class bn extends y{constructor(){super(...arguments);c(this,"priority",100);c(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"])}parse(e,r,a){switch(r){case"w":return D(P.week,e);case"wo":return a.ordinalNumber(e,{unit:"week"});default:return E(r.length,e)}}validate(e,r){return r>=1&&r<=53}set(e,r,a,s){return W(yn(e,a,s),s)}}function xn(n,t,e){const r=h(n,e==null?void 0:e.in),a=Ke(r,e)-t;return r.setDate(r.getDate()-a*7),r}class Mn extends y{constructor(){super(...arguments);c(this,"priority",100);c(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"])}parse(e,r,a){switch(r){case"I":return D(P.week,e);case"Io":return a.ordinalNumber(e,{unit:"week"});default:return E(r.length,e)}}validate(e,r){return r>=1&&r<=53}set(e,r,a){return $(xn(e,a))}}const Dn=[31,28,31,30,31,30,31,31,30,31,30,31],Tn=[31,29,31,30,31,30,31,31,30,31,30,31];class kn extends y{constructor(){super(...arguments);c(this,"priority",90);c(this,"subPriority",1);c(this,"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"])}parse(e,r,a){switch(r){case"d":return D(P.date,e);case"do":return a.ordinalNumber(e,{unit:"date"});default:return E(r.length,e)}}validate(e,r){const a=e.getFullYear(),s=ct(a),i=e.getMonth();return s?r>=1&&r<=Tn[i]:r>=1&&r<=Dn[i]}set(e,r,a){return e.setDate(a),e.setHours(0,0,0,0),e}}class En extends y{constructor(){super(...arguments);c(this,"priority",90);c(this,"subpriority",1);c(this,"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"])}parse(e,r,a){switch(r){case"D":case"DD":return D(P.dayOfYear,e);case"Do":return a.ordinalNumber(e,{unit:"date"});default:return E(r.length,e)}}validate(e,r){const a=e.getFullYear();return ct(a)?r>=1&&r<=366:r>=1&&r<=365}set(e,r,a){return e.setMonth(0,a),e.setHours(0,0,0,0),e}}function Pe(n,t,e){var _,Y,v,f;const r=B(),a=(e==null?void 0:e.weekStartsOn)??((Y=(_=e==null?void 0:e.locale)==null?void 0:_.options)==null?void 0:Y.weekStartsOn)??r.weekStartsOn??((f=(v=r.locale)==null?void 0:v.options)==null?void 0:f.weekStartsOn)??0,s=h(n,e==null?void 0:e.in),i=s.getDay(),d=(t%7+7)%7,m=7-a,M=t<0||t>6?t-(i+m)%7:(d+m)%7-(i+m)%7;return ue(s,M,e)}class Pn extends y{constructor(){super(...arguments);c(this,"priority",90);c(this,"incompatibleTokens",["D","i","e","c","t","T"])}parse(e,r,a){switch(r){case"E":case"EE":case"EEE":return a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return a.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"})}}validate(e,r){return r>=0&&r<=6}set(e,r,a,s){return e=Pe(e,a,s),e.setHours(0,0,0,0),e}}class On extends y{constructor(){super(...arguments);c(this,"priority",90);c(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"])}parse(e,r,a,s){const i=u=>{const d=Math.floor((u-1)/7)*7;return(u+s.weekStartsOn+6)%7+d};switch(r){case"e":case"ee":return O(E(r.length,e),i);case"eo":return O(a.ordinalNumber(e,{unit:"day"}),i);case"eee":return a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"eeeee":return a.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"})}}validate(e,r){return r>=0&&r<=6}set(e,r,a,s){return e=Pe(e,a,s),e.setHours(0,0,0,0),e}}class _n extends y{constructor(){super(...arguments);c(this,"priority",90);c(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"])}parse(e,r,a,s){const i=u=>{const d=Math.floor((u-1)/7)*7;return(u+s.weekStartsOn+6)%7+d};switch(r){case"c":case"cc":return O(E(r.length,e),i);case"co":return O(a.ordinalNumber(e,{unit:"day"}),i);case"ccc":return a.day(e,{width:"abbreviated",context:"standalone"})||a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"});case"ccccc":return a.day(e,{width:"narrow",context:"standalone"});case"cccccc":return a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return a.day(e,{width:"wide",context:"standalone"})||a.day(e,{width:"abbreviated",context:"standalone"})||a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"})}}validate(e,r){return r>=0&&r<=6}set(e,r,a,s){return e=Pe(e,a,s),e.setHours(0,0,0,0),e}}function Yn(n,t,e){const r=h(n,e==null?void 0:e.in),a=tn(r,e),s=t-a;return ue(r,s,e)}class Cn extends y{constructor(){super(...arguments);c(this,"priority",90);c(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"])}parse(e,r,a){const s=i=>i===0?7:i;switch(r){case"i":case"ii":return E(r.length,e);case"io":return a.ordinalNumber(e,{unit:"day"});case"iii":return O(a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"}),s);case"iiiii":return O(a.day(e,{width:"narrow",context:"formatting"}),s);case"iiiiii":return O(a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"}),s);case"iiii":default:return O(a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"}),s)}}validate(e,r){return r>=1&&r<=7}set(e,r,a){return e=Yn(e,a),e.setHours(0,0,0,0),e}}class vn extends y{constructor(){super(...arguments);c(this,"priority",80);c(this,"incompatibleTokens",["b","B","H","k","t","T"])}parse(e,r,a){switch(r){case"a":case"aa":case"aaa":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,r,a){return e.setHours(Ee(a),0,0,0),e}}class Rn extends y{constructor(){super(...arguments);c(this,"priority",80);c(this,"incompatibleTokens",["a","B","H","k","t","T"])}parse(e,r,a){switch(r){case"b":case"bb":case"bbb":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,r,a){return e.setHours(Ee(a),0,0,0),e}}class pn extends y{constructor(){super(...arguments);c(this,"priority",80);c(this,"incompatibleTokens",["a","b","t","T"])}parse(e,r,a){switch(r){case"B":case"BB":case"BBB":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,r,a){return e.setHours(Ee(a),0,0,0),e}}class Nn extends y{constructor(){super(...arguments);c(this,"priority",70);c(this,"incompatibleTokens",["H","K","k","t","T"])}parse(e,r,a){switch(r){case"h":return D(P.hour12h,e);case"ho":return a.ordinalNumber(e,{unit:"hour"});default:return E(r.length,e)}}validate(e,r){return r>=1&&r<=12}set(e,r,a){const s=e.getHours()>=12;return s&&a<12?e.setHours(a+12,0,0,0):!s&&a===12?e.setHours(0,0,0,0):e.setHours(a,0,0,0),e}}class Wn extends y{constructor(){super(...arguments);c(this,"priority",70);c(this,"incompatibleTokens",["a","b","h","K","k","t","T"])}parse(e,r,a){switch(r){case"H":return D(P.hour23h,e);case"Ho":return a.ordinalNumber(e,{unit:"hour"});default:return E(r.length,e)}}validate(e,r){return r>=0&&r<=23}set(e,r,a){return e.setHours(a,0,0,0),e}}class Hn extends y{constructor(){super(...arguments);c(this,"priority",70);c(this,"incompatibleTokens",["h","H","k","t","T"])}parse(e,r,a){switch(r){case"K":return D(P.hour11h,e);case"Ko":return a.ordinalNumber(e,{unit:"hour"});default:return E(r.length,e)}}validate(e,r){return r>=0&&r<=11}set(e,r,a){return e.getHours()>=12&&a<12?e.setHours(a+12,0,0,0):e.setHours(a,0,0,0),e}}class In extends y{constructor(){super(...arguments);c(this,"priority",70);c(this,"incompatibleTokens",["a","b","h","H","K","t","T"])}parse(e,r,a){switch(r){case"k":return D(P.hour24h,e);case"ko":return a.ordinalNumber(e,{unit:"hour"});default:return E(r.length,e)}}validate(e,r){return r>=1&&r<=24}set(e,r,a){const s=a<=24?a%24:a;return e.setHours(s,0,0,0),e}}class qn extends y{constructor(){super(...arguments);c(this,"priority",60);c(this,"incompatibleTokens",["t","T"])}parse(e,r,a){switch(r){case"m":return D(P.minute,e);case"mo":return a.ordinalNumber(e,{unit:"minute"});default:return E(r.length,e)}}validate(e,r){return r>=0&&r<=59}set(e,r,a){return e.setMinutes(a,0,0),e}}class Ln extends y{constructor(){super(...arguments);c(this,"priority",50);c(this,"incompatibleTokens",["t","T"])}parse(e,r,a){switch(r){case"s":return D(P.second,e);case"so":return a.ordinalNumber(e,{unit:"second"});default:return E(r.length,e)}}validate(e,r){return r>=0&&r<=59}set(e,r,a){return e.setSeconds(a,0),e}}class Fn extends y{constructor(){super(...arguments);c(this,"priority",30);c(this,"incompatibleTokens",["t","T"])}parse(e,r){const a=s=>Math.trunc(s*Math.pow(10,-r.length+3));return O(E(r.length,e),a)}set(e,r,a){return e.setMilliseconds(a),e}}class Sn extends y{constructor(){super(...arguments);c(this,"priority",10);c(this,"incompatibleTokens",["t","T","x"])}parse(e,r){switch(r){case"X":return N(p.basicOptionalMinutes,e);case"XX":return N(p.basic,e);case"XXXX":return N(p.basicOptionalSeconds,e);case"XXXXX":return N(p.extendedOptionalSeconds,e);case"XXX":default:return N(p.extended,e)}}set(e,r,a){return r.timestampIsSet?e:k(e,e.getTime()-ce(e)-a)}}class Qn extends y{constructor(){super(...arguments);c(this,"priority",10);c(this,"incompatibleTokens",["t","T","X"])}parse(e,r){switch(r){case"x":return N(p.basicOptionalMinutes,e);case"xx":return N(p.basic,e);case"xxxx":return N(p.basicOptionalSeconds,e);case"xxxxx":return N(p.extendedOptionalSeconds,e);case"xxx":default:return N(p.extended,e)}}set(e,r,a){return r.timestampIsSet?e:k(e,e.getTime()-ce(e)-a)}}class An extends y{constructor(){super(...arguments);c(this,"priority",40);c(this,"incompatibleTokens","*")}parse(e){return st(e)}set(e,r,a){return[k(e,a*1e3),{timestampIsSet:!0}]}}class Bn extends y{constructor(){super(...arguments);c(this,"priority",20);c(this,"incompatibleTokens","*")}parse(e){return st(e)}set(e,r,a){return[k(e,a),{timestampIsSet:!0}]}}const Gn={G:new on,y:new un,Y:new ln,R:new dn,u:new fn,Q:new hn,q:new mn,M:new wn,L:new gn,w:new bn,I:new Mn,d:new kn,D:new En,E:new Pn,e:new On,c:new _n,i:new Cn,a:new vn,b:new Rn,B:new pn,h:new Nn,H:new Wn,K:new Hn,k:new In,m:new qn,s:new Ln,S:new Fn,X:new Sn,x:new Qn,t:new An,T:new Bn},zn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Xn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,$n=/^'([^]*?)'?$/,Un=/''/g,jn=/\S/,Vn=/[a-zA-Z]/;function Zn(n,t,e,r){var Q,l,w,b,I,re,ne,q;const a=()=>k((r==null?void 0:r.in)||e,NaN),s=en(),i=(r==null?void 0:r.locale)??s.locale??Te,u=(r==null?void 0:r.firstWeekContainsDate)??((l=(Q=r==null?void 0:r.locale)==null?void 0:Q.options)==null?void 0:l.firstWeekContainsDate)??s.firstWeekContainsDate??((b=(w=s.locale)==null?void 0:w.options)==null?void 0:b.firstWeekContainsDate)??1,d=(r==null?void 0:r.weekStartsOn)??((re=(I=r==null?void 0:r.locale)==null?void 0:I.options)==null?void 0:re.weekStartsOn)??s.weekStartsOn??((q=(ne=s.locale)==null?void 0:ne.options)==null?void 0:q.weekStartsOn)??0;if(!t)return n?a():h(e,r==null?void 0:r.in);const m={firstWeekContainsDate:u,weekStartsOn:d,locale:i},M=[new cn(r==null?void 0:r.in,e)],_=t.match(Xn).map(g=>{const T=g[0];if(T in ye){const R=ye[T];return R(g,i.formatLong)}return g}).join("").match(zn),Y=[];for(let g of _){!(r!=null&&r.useAdditionalWeekYearTokens)&&nt(g)&&be(g,t,n),!(r!=null&&r.useAdditionalDayOfYearTokens)&&rt(g)&&be(g,t,n);const T=g[0],R=Gn[T];if(R){const{incompatibleTokens:Oe}=R;if(Array.isArray(Oe)){const _e=Y.find(Ye=>Oe.includes(Ye.token)||Ye.token===T);if(_e)throw new RangeError(`The format string mustn't contain \`${_e.fullToken}\` and \`${g}\` at the same time`)}else if(R.incompatibleTokens==="*"&&Y.length>0)throw new RangeError(`The format string mustn't contain \`${g}\` and any other token at the same time`);Y.push({token:T,fullToken:g});const le=R.run(n,g,i.match,m);if(!le)return a();M.push(le.setter),n=le.rest}else{if(T.match(Vn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+T+"`");if(g==="''"?g="'":T==="'"&&(g=Jn(g)),n.indexOf(g)===0)n=n.slice(g.length);else return a()}}if(n.length>0&&jn.test(n))return a();const v=M.map(g=>g.priority).sort((g,T)=>T-g).filter((g,T,R)=>R.indexOf(g)===T).map(g=>M.filter(T=>T.priority===g).sort((T,R)=>R.subPriority-T.subPriority)).map(g=>g[0]);let f=h(e,r==null?void 0:r.in);if(isNaN(+f))return a();const S={};for(const g of v){if(!g.validate(f,m))return a();const T=g.set(f,S,m);Array.isArray(T)?(f=T[0],Object.assign(S,T[1])):f=T}return f}function Jn(n){return n.match($n)[1].replace(Un,"'")}function Kn(n,t){const e=h(n,t==null?void 0:t.in);return e.setMinutes(0,0,0),e}function ea(n,t){const e=h(n,t==null?void 0:t.in);return e.setSeconds(0,0),e}function ta(n,t){const e=h(n,t==null?void 0:t.in);return e.setMilliseconds(0),e}function ra(n,t){const e=()=>k(t==null?void 0:t.in,NaN),r=(t==null?void 0:t.additionalDigits)??2,a=ia(n);let s;if(a.date){const m=ca(a.date,r);s=oa(m.restDateString,m.year)}if(!s||isNaN(+s))return e();const i=+s;let u=0,d;if(a.time&&(u=ua(a.time),isNaN(u)))return e();if(a.timezone){if(d=la(a.timezone),isNaN(d))return e()}else{const m=new Date(i+u),M=h(0,t==null?void 0:t.in);return M.setFullYear(m.getUTCFullYear(),m.getUTCMonth(),m.getUTCDate()),M.setHours(m.getUTCHours(),m.getUTCMinutes(),m.getUTCSeconds(),m.getUTCMilliseconds()),M}return h(i+u+d,t==null?void 0:t.in)}const se={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},na=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,aa=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,sa=/^([+-])(\d{2})(?::?(\d{2}))?$/;function ia(n){const t={},e=n.split(se.dateTimeDelimiter);let r;if(e.length>2)return t;if(/:/.test(e[0])?r=e[0]:(t.date=e[0],r=e[1],se.timeZoneDelimiter.test(t.date)&&(t.date=n.split(se.timeZoneDelimiter)[0],r=n.substr(t.date.length,n.length))),r){const a=se.timezone.exec(r);a?(t.time=r.replace(a[1],""),t.timezone=a[1]):t.time=r}return t}function ca(n,t){const e=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=n.match(e);if(!r)return{year:NaN,restDateString:""};const a=r[1]?parseInt(r[1]):null,s=r[2]?parseInt(r[2]):null;return{year:s===null?a:s*100,restDateString:n.slice((r[1]||r[2]).length)}}function oa(n,t){if(t===null)return new Date(NaN);const e=n.match(na);if(!e)return new Date(NaN);const r=!!e[4],a=Z(e[1]),s=Z(e[2])-1,i=Z(e[3]),u=Z(e[4]),d=Z(e[5])-1;if(r)return wa(t,u,d)?da(t,u,d):new Date(NaN);{const m=new Date(0);return!ha(t,s,i)||!ma(t,a)?new Date(NaN):(m.setUTCFullYear(t,s,Math.max(a,i)),m)}}function Z(n){return n?parseInt(n):1}function ua(n){const t=n.match(aa);if(!t)return NaN;const e=me(t[1]),r=me(t[2]),a=me(t[3]);return ga(e,r,a)?e*ee+r*K+a*1e3:NaN}function me(n){return n&&parseFloat(n.replace(",","."))||0}function la(n){if(n==="Z")return 0;const t=n.match(sa);if(!t)return 0;const e=t[1]==="+"?-1:1,r=parseInt(t[2]),a=t[3]&&parseInt(t[3])||0;return ya(r,a)?e*(r*ee+a*K):NaN}function da(n,t,e){const r=new Date(0);r.setUTCFullYear(n,0,4);const a=r.getUTCDay()||7,s=(t-1)*7+e+1-a;return r.setUTCDate(r.getUTCDate()+s),r}const fa=[31,null,31,30,31,30,31,31,30,31,30,31];function ot(n){return n%400===0||n%4===0&&n%100!==0}function ha(n,t,e){return t>=0&&t<=11&&e>=1&&e<=(fa[t]||(ot(n)?29:28))}function ma(n,t){return t>=1&&t<=(ot(n)?366:365)}function wa(n,t,e){return t>=1&&t<=53&&e>=0&&e<=6}function ga(n,t,e){return n===24?t===0&&e===0:e>=0&&e<60&&t>=0&&t<60&&n>=0&&n<25}function ya(n,t){return t>=0&&t<=59}/*!
 * chartjs-adapter-date-fns v3.0.0
 * https://www.chartjs.org
 * (c) 2022 chartjs-adapter-date-fns Contributors
 * Released under the MIT license
 */const ba={datetime:"MMM d, yyyy, h:mm:ss aaaa",millisecond:"h:mm:ss.SSS aaaa",second:"h:mm:ss aaaa",minute:"h:mm aaaa",hour:"ha",day:"MMM d",week:"PP",month:"MMM yyyy",quarter:"qqq - yyyy",year:"yyyy"};wt._date.override({_id:"date-fns",formats:function(){return ba},parse:function(n,t){if(n===null||typeof n>"u")return null;const e=typeof n;return e==="number"||n instanceof Date?n=h(n):e==="string"&&(typeof t=="string"?n=Zn(n,t,new Date,this.options):n=ra(n,this.options)),$e(n)?n.getTime():null},format:function(n,t){return Jr(n,t,this.options)},add:function(n,t,e){switch(e){case"millisecond":return Me(n,t);case"second":return Qt(n,t);case"minute":return Ft(n,t);case"hour":return It(n,t);case"day":return ue(n,t);case"week":return At(n,t);case"month":return xe(n,t);case"quarter":return St(n,t);case"year":return Bt(n,t);default:return n}},diff:function(n,t,e){switch(e){case"millisecond":return De(n,t);case"second":return Zt(n,t);case"minute":return Ut(n,t);case"hour":return $t(n,t);case"day":return Ue(n,t);case"week":return Jt(n,t);case"month":return Ze(n,t);case"quarter":return Vt(n,t);case"year":return Kt(n,t);default:return 0}},startOf:function(n,t,e){switch(t){case"second":return ta(n);case"minute":return ea(n);case"hour":return Kn(n);case"day":return ge(n);case"week":return W(n);case"isoWeek":return W(n,{weekStartsOn:+e});case"month":return tr(n);case"quarter":return er(n);case"year":return Je(n);default:return n}},endOf:function(n,t){switch(t){case"second":return cr(n);case"minute":return sr(n);case"hour":return nr(n);case"day":return je(n);case"week":return ar(n);case"month":return Ve(n);case"quarter":return ir(n);case"year":return rr(n);default:return n}}});gt.register(yt,bt,xt,Mt,Dt,Tt,kt,Et,Pt);Yt.throttle((n,t)=>{n&&n.data&&(n.data=t,n.update())},500);const Be=n=>{let t=null,e=null;return r=>(r!==t&&(t=r,e=n(r)),e)},ie={productionData:Be((n=[])=>({labels:n.map(t=>t.Machine_Name),datasets:[{label:"Planifié",data:n.map(t=>t.Total_Quantite_Planifier),backgroundColor:C.CHART_TERTIARY,barThickness:20,borderRadius:4},{label:"Réalisé",data:n.map(t=>t.Total_Quantite_Bon),backgroundColor:C.PRIMARY_BLUE,barThickness:20,borderRadius:4},{label:"TRS",data:n.map(t=>t.Avg_TRS),borderColor:C.SECONDARY_BLUE,borderWidth:2,type:"line",fill:!1,tension:.4,pointRadius:window.innerWidth<768?0:4}]})),dailyData:Be((n=[])=>({labels:n.map(t=>t.time_bucket),datasets:[{label:"Production",data:n.map(t=>t.production),borderColor:C.PRIMARY_BLUE,borderWidth:2,fill:!0,tension:.3,backgroundColor:t=>{const r=t.chart.ctx.createLinearGradient(0,0,0,400);return r.addColorStop(0,C.LIGHT_BLUE_BG),r.addColorStop(1,"rgba(30, 58, 138, 0)"),r},pointRadius:4}]})),barOptions:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#666"}},tooltip:{backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",bodyColor:"#fff"}},scales:{x:{stacked:!1,grid:{display:!1}},y:{beginAtZero:!0,grid:{color:"#f0f0f0"},ticks:{color:"#666"}}},animation:!1,devicePixelRatio:1},lineOptions:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#666"}},tooltip:{mode:"index",intersect:!1,backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",bodyColor:"#fff"}},scales:{x:{type:"time",time:{parser:"HH:mm",unit:"hour",displayFormats:{hour:"HH:mm"},tooltipFormat:"HH:mm"},grid:{display:!1},adapters:{date:{locale:Te}},ticks:{color:"#666",maxTicksLimit:8,source:"auto"}},y:{beginAtZero:!0,grid:{color:"#f0f0f0"},ticks:{color:"#666",maxTicksLimit:6}}},elements:{line:{tension:.3},point:{radius:window.innerWidth<768?0:3,hoverRadius:6}},animation:!1,devicePixelRatio:1}},{useBreakpoint:xa}=dt,Ka=()=>{const n=xa(),[t]=de.useState(new Date().toLocaleDateString("fr-FR")),{settings:e}=ft(),[r,a]=de.useState({machineData:[],sideCardData:{},dailyStats:[],selectedMachine:null,machineHistory:[],historyLoading:!1,historyError:null,loading:!0,error:null,visible:!1}),s=(()=>{if(typeof window<"u"){const l=window.location.origin;return l.includes("ngrok-free.app")||l.includes("ngrok.io")?l:"http://localhost:5000"}return"http://localhost:5000"})(),i="linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",u="#1890ff",{md:d}=n,m=async()=>{try{const l=await Promise.all([ae.get(s+"/api/RealTimeTable").withCredentials().timeout(3e4).retry(2),ae.get(s+"/api/MachineCard").withCredentials().timeout(3e4).retry(2),ae.get(s+"/api/sidecards").withCredentials().timeout(3e4).retry(2),ae.get(s+"/api/dailyStats").withCredentials().timeout(3e4).retry(2)]),[w,b,I,re]=l;a(ne=>({...ne,machineData:b.data.map(q=>({...q,progress:q.Total_Quantite_Bon/q.Total_Quantite_Planifier*100,status:q.Avg_TRS>80?"success":q.Avg_TRS>60?"warning":"error"})),sideCardData:I.data[0],dailyStats:re.data,error:null,loading:!1}))}catch(l){a(w=>({...w,error:l.message,loading:!1}))}};de.useEffect(()=>{const l=setInterval(m,15e3);return m(),()=>clearInterval(l)},[]);const M=async l=>{try{a(b=>({...b,historyLoading:!0,historyError:null}));const w=await axios.get(`/api/machineHistory/${l}`);a(b=>({...b,machineHistory:w.data,historyLoading:!1}))}catch(w){a(b=>({...b,historyError:w.message,historyLoading:!1}))}},_=[{title:"TRS moyen",value:r.machineData.length>0?(r.machineData.reduce((l,w)=>l+(w.Avg_TRS||0),0)/r.machineData.length).toFixed(1):"N/A",suffix:"%"},{title:"Production totale",value:r.machineData.reduce((l,w)=>l+(w.Total_Quantite_Bon||0),0)||"N/A"}],Y=l=>{a(w=>({...w,selectedMachine:l.Machine_Name,visible:!0})),M(l.Machine_Name)},v=()=>r.historyLoading?o.createElement(ve,{size:"large",style:{display:"block",margin:"40px auto"}}):r.historyError?o.createElement(Ce,{type:"error",message:"Erreur de chargement",description:r.historyError,showIcon:!0}):o.createElement(mt,{columns:[{title:"Cycle",dataIndex:"cycle",sorter:(l,w)=>l.cycle-w.cycle},{title:"Quantité bonne",dataIndex:"Quantite_Bon"},{title:"TRS",dataIndex:"TRS",render:l=>o.createElement(U,{color:l>80?"success":"error"},l,"%")},{title:"Heure",dataIndex:"Stop_Time"}],dataSource:r.machineHistory,pagination:{pageSize:5},scroll:{x:!0}}),f=!n.md,S={gutter:[16,16],xs:24,sm:24,md:12,lg:8,xl:6},Q=l=>o.createElement(L,{hoverable:!0,onClick:()=>Y(l),style:{background:i,borderRadius:12,height:"100%",borderLeft:`4px solid ${Ma(l.status)}`,marginBottom:f?8:0}},o.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:f?8:12,flexWrap:"wrap"}},o.createElement(vt,{style:{fontSize:f?20:24,marginRight:8,color:u}}),o.createElement("h3",{style:{margin:0,fontSize:f?16:18}},l.Machine_Name),o.createElement(U,{color:l.status,style:{marginLeft:"auto",fontSize:f?12:14}},"TRS: ",l.Avg_TRS,"%")),o.createElement(fe,{percent:l.progress,strokeColor:we(l.progress),strokeLinecap:"square",format:w=>o.createElement("div",{style:{color:we(l.progress),fontSize:f?12:14}},w,"% d'objectif")}),o.createElement(z,{gutter:16,style:{marginTop:f?12:16}},["Planifié","Bon","Rejet"].map((w,b)=>o.createElement(H,{key:w,span:8},o.createElement(Ie,{title:o.createElement("span",{style:{fontSize:f?12:14}},w),value:l[b===0?"Total_Quantite_Planifier":b===1?"Total_Quantite_Bon":"Total_Quantite_Rejet"],valueStyle:{fontSize:f?14:16},prefix:[o.createElement(Rt,null),o.createElement(pt,{style:{color:C.PRIMARY_BLUE}}),o.createElement(Nt,{style:{color:C.SECONDARY_BLUE}})][b]})))));return o.createElement("div",{style:{padding:f?8:24}},r.error&&o.createElement(Ce,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${r.error} | Mise à jour: ${new Date().toLocaleTimeString()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),o.createElement(z,{gutter:[16,16]},o.createElement(H,{xs:24,md:16},o.createElement(L,{title:o.createElement("div",{style:{display:"flex",alignItems:"center"}},o.createElement(Ct,{style:{fontSize:f?18:20,marginRight:8,color:u}}),o.createElement("span",{style:{fontSize:f?16:18}},"Statistiques des Machines")),bordered:!1,style:{borderRadius:16},bodyStyle:{padding:f?8:16}},r.loading?o.createElement(ve,{size:"large",style:{display:"block",margin:"40px auto"}}):o.createElement(z,{gutter:[16,16]},r.machineData.slice(0,4).map((l,w)=>o.createElement(H,{key:w,xs:24,sm:24,md:12,lg:12},Q(l)))))),o.createElement(H,{xs:24,md:8},o.createElement(L,{title:o.createElement("span",{style:{fontSize:f?16:18}},"Indicateurs Clés"),bordered:!1,style:{borderRadius:16},bodyStyle:{padding:f?8:16}},o.createElement(z,{gutter:[8,8]},_.map((l,w)=>o.createElement(H,{key:w,span:24},o.createElement(L.Grid,{style:{width:"100%",background:i,padding:f?12:16}},o.createElement(Ie,{title:o.createElement("span",{style:{fontSize:f?12:14}},l.title),value:l.value,suffix:l.suffix,precision:1,valueStyle:{fontSize:f?16:18}})))),o.createElement(H,{span:24},o.createElement(fe,{percent:r.sideCardData.total_quantite_bon/(r.sideCardData.total_quantite_bon+r.sideCardData.total_quantite_rejet)*100,strokeColor:{"0%":"#87d068","100%":"#108ee9"},format:l=>o.createElement("div",{style:{color:"#1890ff",fontSize:f?12:14}},r.sideCardData.total_quantite_bon," /"," ",r.sideCardData.total_quantite_bon+r.sideCardData.total_quantite_rejet)})))))),o.createElement(z,{gutter:[16,16],style:{marginTop:16}},o.createElement(H,{xs:24,md:16},o.createElement(L,{title:o.createElement("span",{style:{fontSize:f?16:18}},"Analyse de Production"),style:{borderRadius:16},extra:o.createElement(U,{color:"geekblue"},"Temps réel"),bodyStyle:{padding:f?8:16}},o.createElement(Ot,{data:He(ie.productionData(r.machineData),e),options:We(e,"bar",ie.barOptions),height:Ne(e)}))),o.createElement(H,{xs:24,md:8},o.createElement(L,{title:o.createElement("span",{style:{fontSize:f?16:18}},"Performance Horaires"),style:{borderRadius:16},extra:o.createElement(U,{color:"cyan"},"Dernières 24h"),bodyStyle:{padding:f?8:16}},r.dailyStats.length>0?o.createElement(_t,{key:r.dailyStats[0].time_bucket,data:He(ie.dailyData(r.dailyStats),e),options:We(e,"line",ie.lineOptions),height:Ne(e)}):o.createElement(Re,{image:Re.PRESENTED_IMAGE_SIMPLE,description:o.createElement("span",{style:{fontSize:f?12:14}},"Aucune donnée disponible")})))),o.createElement(L,{title:o.createElement("span",{style:{fontSize:f?16:18}},"Détails des Machines"),style:{marginTop:16,borderRadius:16},bodyStyle:{padding:"8px 0"}},o.createElement(pe,{defaultActiveKey:"1"},o.createElement(pe.TabPane,{tab:o.createElement("span",{style:{fontSize:f?14:16}},"Vue d'Ensemble"),key:"1"},o.createElement(z,{gutter:[8,8],style:{padding:8}},r.machineData.map(l=>o.createElement(H,{key:l.Machine_Name,...S},o.createElement(L,{bodyStyle:{padding:8,fontSize:f?12:14}},o.createElement("h4",{style:{margin:0,fontSize:f?14:16}},l.Machine_Name),o.createElement(U,{color:l.status,style:{fontSize:f?12:14}},"TRS: ",l.Avg_TRS,"%"),o.createElement(fe,{percent:l.progress,showInfo:!1,strokeColor:we(l.progress)})))))))),o.createElement(ht,{title:`Historique de ${r.selectedMachine}`,visible:r.visible,width:f?"90%":800,onCancel:()=>a(l=>({...l,visible:!1,historyError:null})),footer:null,destroyOnClose:!0,bodyStyle:{padding:f?8:16}},v()))},Ma=n=>({success:C.PRIMARY_BLUE,warning:C.SECONDARY_BLUE,error:C.CHART_TERTIARY})[n],we=n=>n>=90?C.PRIMARY_BLUE:n>=75?C.SECONDARY_BLUE:C.CHART_TERTIARY;export{Ka as default};
