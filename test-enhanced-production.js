/**
 * Test Enhanced Production GraphQL Queries
 * Validates Elasticsearch → MySQL fallback implementation
 */

const testEnhancedProductionQueries = async () => {
  const GRAPHQL_ENDPOINT = 'http://localhost:5000/api/graphql';
  
  // Test enhanced production chart query
  const testProductionChart = async () => {
    console.log('\n🧪 Testing Enhanced Production Chart Query...');
    
    const query = `
      query TestProductionChart {
        enhancedGetProductionChart(filters: { dateRangeType: "day" }) {
          data {
            Date_Insert_Day
            Total_Good_Qty_Day
            Total_Rejects_Qty_Day
            OEE_Day
          }
          dataSource
        }
      }
    `;
    
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
      });
      
      const result = await response.json();
      
      if (result.errors) {
        console.error('❌ GraphQL Errors:', result.errors);
        return false;
      }
      
      const data = result.data?.enhancedGetProductionChart;
      console.log('✅ Production Chart Result:');
      console.log(`   📊 Data Source: ${data?.dataSource || 'N/A'}`);
      console.log(`   📈 Records: ${data?.data?.length || 0}`);
      
      if (data?.data?.length > 0) {
        console.log(`   📅 Sample: ${data.data[0].Date_Insert_Day} - Good: ${data.data[0].Total_Good_Qty_Day}`);
      }
      
      return true;
    } catch (error) {
      console.error('❌ Request Error:', error.message);
      return false;
    }
  };
  
  // Test enhanced production sidecards query
  const testProductionSidecards = async () => {
    console.log('\n🧪 Testing Enhanced Production Sidecards Query...');
    
    const query = `
      query TestProductionSidecards {
        enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) {
          goodqty
          rejetqty
          dataSource
        }
      }
    `;
    
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
      });
      
      const result = await response.json();
      
      if (result.errors) {
        console.error('❌ GraphQL Errors:', result.errors);
        return false;
      }
      
      const data = result.data?.enhancedGetProductionSidecards;
      console.log('✅ Production Sidecards Result:');
      console.log(`   📊 Data Source: ${data?.dataSource || 'N/A'}`);
      console.log(`   ✅ Good Qty: ${data?.goodqty || 0}`);
      console.log(`   ❌ Reject Qty: ${data?.rejetqty || 0}`);
      
      return true;
    } catch (error) {
      console.error('❌ Request Error:', error.message);
      return false;
    }
  };
  
  // Test enhanced machine performance query
  const testMachinePerformance = async () => {
    console.log('\n🧪 Testing Enhanced Machine Performance Query...');
    
    const query = `
      query TestMachinePerformance {
        enhancedGetMachinePerformance(filters: { dateRangeType: "day" }) {
          data {
            Machine_Name
            production
            oee
            availability
          }
          dataSource
        }
      }
    `;
    
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
      });
      
      const result = await response.json();
      
      if (result.errors) {
        console.error('❌ GraphQL Errors:', result.errors);
        return false;
      }
      
      const data = result.data?.enhancedGetMachinePerformance;
      console.log('✅ Machine Performance Result:');
      console.log(`   📊 Data Source: ${data?.dataSource || 'N/A'}`);
      console.log(`   🏭 Machines: ${data?.data?.length || 0}`);
      
      if (data?.data?.length > 0) {
        console.log(`   📈 Sample: ${data.data[0].Machine_Name} - OEE: ${data.data[0].oee}%`);
      }
      
      return true;
    } catch (error) {
      console.error('❌ Request Error:', error.message);
      return false;
    }
  };
  
  // Run all tests
  console.log('🚀 Starting Enhanced Production GraphQL Tests...');
  console.log('📝 Note: Elasticsearch is disabled, expecting MySQL fallback');
  
  const results = await Promise.all([
    testProductionChart(),
    testProductionSidecards(),
    testMachinePerformance()
  ]);
  
  const successCount = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log(`\n📊 Test Results: ${successCount}/${totalTests} passed`);
  
  if (successCount === totalTests) {
    console.log('🎉 All enhanced production queries working correctly!');
    console.log('✅ Elasticsearch → MySQL fallback system operational');
  } else {
    console.log('⚠️ Some tests failed - check GraphQL schema and resolvers');
  }
  
  return successCount === totalTests;
};

// Execute tests
testEnhancedProductionQueries()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('🚨 Test execution failed:', error);
    process.exit(1);
  });
