import{u as g,a5 as A,b7 as E,r as p,R as s,m as v,b6 as h,ae as z}from"./index-y9W4UQPd.js";import{u as R}from"./usePermission-WvgDJJ2d.js";const b=({permissions:a,roles:r,departments:i,redirectPath:d="/unauthorized",showNotification:o=!0})=>{const{isAuthenticated:e,user:x,loading:t}=g(),{hasPermission:u,hasRole:c,hasDepartmentAccess:l}=R(),f=A(),{notification:m}=E.useApp(),n=p.useMemo(()=>!e||t?!1:(!a||u(a))&&(!r||c(r))&&(!i||l(i)),[e,t,a,r,i,u,c,l]);return p.useEffect(()=>{!n&&o&&!t&&e&&m.error({message:"Accès refusé",description:"Vous n'avez pas les permissions nécessaires pour accéder à cette page.",duration:4})},[n,o,t,e,m]),t?s.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"}},s.createElement(v,{size:"large",tip:"Vérification de l'authentification..."})):e?n?s.createElement(z,null):s.createElement(h,{to:d,replace:!0,state:{from:f}}):s.createElement(h,{to:"/login",replace:!0,state:{from:f}})};export{b as default};
