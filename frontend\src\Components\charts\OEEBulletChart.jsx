import React, { memo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "recharts";
import { normalizePercentage } from "../../utils/dataUtils";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// SOMIPEM Brand Color Palette (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Bullet chart component for OEE by machine
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {string} props.dataKey - Data key for values (default: "oee")
 * @param {string} props.nameKey - Data key for names (default: "Machine_Name")
 * @param {string} props.color - Color for bars (default: COLORS[5])
 * @returns {JSX.Element} - Rendered chart component
 */
const OEEBulletChart = memo(({ data, dataKey = "oee", nameKey = "Machine_Name", color = COLORS[5] }) => {
  // Process data to get unique machines with their average OEE
  const processedData = data.reduce((acc, item) => {
    if (!acc[item.Machine_Name]) {
      acc[item.Machine_Name] = {
        Machine_Name: item.Machine_Name,
        oee: 0,
        count: 0,
      }
    }
    // Handle decimal percentages (0-1 range)
    let oeeValue = Number.parseFloat(item.oee) || 0;
    // Convert to percentage if in 0-1 range
    oeeValue = normalizePercentage(oeeValue);
    acc[item.Machine_Name].oee += oeeValue;
    acc[item.Machine_Name].count += 1
    return acc
  }, {})

  // Calculate averages and convert to array
  const chartData = Object.values(processedData)
    .map((item) => ({
      Machine_Name: item.Machine_Name,
      oee: item.count > 0 ? Math.round(item.oee / item.count) : 0,
    }))
    .sort((a, b) => b.oee - a.oee) // Sort by OEE descending

  // Function to determine bar color based on OEE value
  const getBarColor = (oee) => {
    if (oee >= 85) return COLORS[2] // Green for high OEE
    if (oee >= 70) return COLORS[3] // Yellow for medium OEE
    return COLORS[4] // Red for low OEE
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart
        data={chartData}
        layout="vertical"
        margin={{ top: 16, right: 24, left: 100, bottom: 16 }}
      >
        <XAxis
          type="number"
          domain={[0, 100]}
          tickFormatter={(value) => `${value}%`}
        />
        <YAxis
          type="category"
          dataKey={nameKey}
          tick={{ fill: "#666" }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value) => [`${value}%`, "TRS Moyen"]}
        />
        <Bar dataKey={dataKey} name="TRS" radius={[0, 4, 4, 0]}>
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={getBarColor(entry[dataKey])} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
});

OEEBulletChart.displayName = 'OEEBulletChart';

export default OEEBulletChart;
