import{r as m,G as zt,a as j,at as q,s as ye,R as a,m as Qt,b as W,c as p,C as b,T as Lt,d as k,al as ze,g as se,f as M,e as U,i as xt,E as Te,B as $e,F as gt,ad as Kt,ac as De,l as Bt}from"./index-y9W4UQPd.js";import{t as Ot,n as H,S as Wt,R as Ht}from"./dataUtils-QekaFx7r.js";import{d as s}from"./dayjs.min-D8Lc9v5x.js";import"./fr-DGSiljjZ.js";import{R as Ie}from"./InfoCircleOutlined-DzeHyv3U.js";import{R as je}from"./DashboardOutlined-DzkF0_SV.js";import{S as Gt}from"./index-C-PPiPHl.js";import{D as Ut,b as Jt,R as Se}from"./DownloadOutlined-BrMzz3_v.js";import{R as Xt}from"./ReloadOutlined-Cyu5KuEL.js";import{S as O}from"./index-pzHVCrlC.js";import{P as J}from"./progress-DWVYtOh3.js";import{R as Me}from"./LineChartOutlined-Bc1BVguD.js";import{R as Y,B as X,C as P,X as T,Y as R,T as v,g as z,L as ge,d as ae,a as Qe,P as Zt,e as ea,f as ta}from"./PieChart-Do9MpA08.js";import{R as he}from"./BarChartOutlined-B49EAf86.js";import{R as Et}from"./TableOutlined-Pxh1lqpj.js";import{R as qe}from"./ToolOutlined-bXQU0KMP.js";import{R as aa}from"./CalendarOutlined-BvTWpIAf.js";import{R as ra,a as oa}from"./RiseOutlined-DNANWaVe.js";import{R as na}from"./ClockCircleOutlined-ByEvWkZP.js";import{R as la}from"./ThunderboltOutlined-DF0_rH5I.js";import{R as ia}from"./CloseCircleOutlined-DqYSjFDX.js";import{R as sa}from"./CheckCircleOutlined-DX2ULMi3.js";import{C as ca,A as da,a as ma}from"./ComposedChart-DJEX9bJ5.js";import"./FilePdfOutlined-BhEOWXlP.js";s.locale("fr");const{Title:bt,Text:C,Paragraph:ua}=Lt,{TabPane:Ve}=xt,{useBreakpoint:fa}=zt,{Option:Nt}=ze,l=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96","#fa8c16","#a0d911","#096dd9"];m.memo(({data:u})=>a.createElement(Y,{width:"100%",height:400},a.createElement(ca,{data:u,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),a.createElement(T,{dataKey:"date",tick:{fill:"#666"},tickFormatter:i=>i?s(i).format("DD/MM"):"",label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),a.createElement(R,{yAxisId:"left",tickFormatter:i=>i.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(R,{yAxisId:"right",orientation:"right",tickFormatter:i=>`${i}%`,domain:[0,100],label:{value:"TRS",angle:90,position:"insideRight",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(i,E)=>[typeof i=="number"&&!isNaN(i)?Number.isInteger(i)?i.toLocaleString():i.toFixed(2):i,{good:"Quantité bonne",reject:"Quantité rejetée (kg)",oee:"TRS",speed:"Cycle De Temps"}[E]||E],labelFormatter:i=>`Date: ${s(i).format("DD/MM/YYYY")}`}),a.createElement(Qe,{wrapperStyle:{paddingTop:20},formatter:i=>{const E={good:"Quantité bonne",reject:"Quantité rejetée (kg)",oee:"TRS",speed:"Cycle De Temps"};return a.createElement("span",{style:{color:"#666"}},E[i]||i)}}),a.createElement(z,{yAxisId:"left",dataKey:"good",name:"good",fill:l[2],maxBarSize:40,stackId:"production"}),a.createElement(z,{yAxisId:"left",dataKey:"reject",name:"reject",fill:l[4],maxBarSize:40,stackId:"production"}),a.createElement(ae,{yAxisId:"right",type:"monotone",dataKey:"oee",name:"oee",stroke:l[0],strokeWidth:2,dot:{r:4,fill:l[0]},activeDot:{r:6,fill:"#fff",stroke:l[0],strokeWidth:2}}))));m.memo(({data:u})=>a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:u,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Shift",tick:{fill:"#666"}}),a.createElement(R,{yAxisId:"left",tickFormatter:i=>i.toLocaleString(),label:{value:"Production",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(R,{yAxisId:"right",orientation:"right",tickFormatter:i=>`${i}%`,domain:[0,100],label:{value:"Performance",angle:90,position:"insideRight",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(i,E)=>{const n=typeof i=="number"&&!isNaN(i);return E==="production"?[n?i.toLocaleString():i,"Production"]:E==="downtime"?[n?i.toLocaleString():i,"Temps d'arrêt"]:E==="oee"?[n?`${i.toFixed(1)}%`:`${i}%`,"TRS"]:E==="performance"?[n?`${i.toFixed(1)}%`:`${i}%`,"Performance"]:[i,E]},labelFormatter:i=>`Équipe: ${i}`}),a.createElement(z,{yAxisId:"left",dataKey:"production",name:"Production",fill:l[2],maxBarSize:40}),a.createElement(z,{yAxisId:"left",dataKey:"downtime",name:"Temps d'arrêt",fill:l[4],maxBarSize:40}),a.createElement(ae,{yAxisId:"right",type:"monotone",dataKey:"oee",name:"TRS",stroke:l[0],strokeWidth:2,dot:{r:4,fill:l[0]}}),a.createElement(ae,{yAxisId:"right",type:"monotone",dataKey:"performance",name:"Performance",stroke:l[5],strokeWidth:2,dot:{r:4,fill:l[5]}}))));m.memo(({data:u})=>{const i=u.reduce((n,y)=>{const _=y.Machine_Name;return n[_]||(n[_]={Machine_Name:_,good:0,reject:0}),n[_].good+=Number(y.good)||Number(y.Good_QTY_Day)||0,n[_].reject+=Number(y.reject)||Number(y.Rejects_QTY_Day)||0,n},{}),E=Object.values(i);return a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:E,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Machine_Name",tick:{fill:"#666"},interval:0,angle:-45,textAnchor:"end",height:80}),a.createElement(R,{tickFormatter:n=>n.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(n,y)=>[typeof n=="number"&&!isNaN(n)?Number.isInteger(n)?n.toLocaleString():n.toFixed(2):n,{good:"Production",reject:"Rejets (kg)"}[y]||y]}),a.createElement(z,{dataKey:"good",name:"good",fill:l[0],stackId:"a"}),a.createElement(z,{dataKey:"reject",name:"reject",fill:l[4],stackId:"a"})))});const pa=m.memo(({data:u,dataKey:i="value",nameKey:E="name",colors:n=l})=>a.createElement(Y,{width:"100%",height:300},a.createElement(Zt,{margin:{top:16,right:24,left:24,bottom:16}},a.createElement(ea,{data:u,dataKey:i,nameKey:E,cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:5,label:({name:y,percent:_})=>`${y}: ${(_*100).toFixed(0)}%`},u.map((y,_)=>a.createElement(ta,{key:`cell-${_}`,fill:n[_%n.length]}))),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),a.createElement(Qe,{layout:"vertical",verticalAlign:"middle",align:"right",wrapperStyle:{paddingLeft:24,fontSize:14,color:"#666"}}))));m.memo(({data:u,dataKeys:i=["oee","speed"],colors:E=[l[0],l[1]]})=>a.createElement(Y,{width:"100%",height:300},a.createElement(ge,{data:u,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"date",tick:{fill:"#666"},tickFormatter:n=>n?s(n).format("DD/MM"):""}),a.createElement(R,{tickFormatter:n=>`${n}%`,domain:[0,100]}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(n,y)=>{const _=typeof n=="number"&&!isNaN(n),d=_?Number.isInteger(n)?n.toLocaleString():n.toFixed(2):n,g={oee:"TRS",speed:"Cycle De Temps"};return[_?d+"%":n,g[y]||y]},labelFormatter:n=>`Date: ${s(n).format("DD/MM/YYYY")}`}),i.map((n,y)=>a.createElement(ae,{key:n,type:"monotone",dataKey:n,name:n,stroke:E[y],strokeWidth:2,dot:{r:4,fill:E[y]},activeDot:{r:6,fill:"#fff",stroke:E[y],strokeWidth:2}})))));m.memo(({data:u,dataKey:i="average_speed",color:E=l[2]})=>a.createElement(Y,{width:"100%",height:300},a.createElement(da,{data:u,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"hour",tick:{fill:"#666"},tickFormatter:n=>{if(!n)return"";const y=n.split(" ");return y.length>=2?`${y[1]}h`:n}}),a.createElement(R,{tickFormatter:n=>`${n} u/h`}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:n=>[typeof n=="number"&&!isNaN(n)?`${n.toFixed(2)} unités/heure`:`${n} unités/heure`,"Cycle De Temps Moyenne"],labelFormatter:n=>{if(!n)return"Heure inconnue";const y=n.split(" ");return y.length>=2?`Date: ${y[0]}, Heure: ${y[1]}h`:n}}),a.createElement(ma,{type:"monotone",dataKey:i,name:"Cycle De Temps Moyenne",stroke:E,fill:E,fillOpacity:.3}))));const ya=m.memo(({data:u,dataKey:i="oee",nameKey:E="Machine_Name",color:n=l[5]})=>{const y=u.reduce((d,g)=>{d[g.Machine_Name]||(d[g.Machine_Name]={Machine_Name:g.Machine_Name,oee:0,count:0});let F=Number.parseFloat(g.oee)||0;return F=F<=1&&F>0?F*100:F,d[g.Machine_Name].oee+=F,d[g.Machine_Name].count+=1,d},{}),_=Object.values(y).map(d=>({Machine_Name:d.Machine_Name,oee:d.count>0?d.oee/d.count:0,target:85,minimum:70})).sort((d,g)=>g.oee-d.oee);return a.createElement(Y,{width:"100%",height:300},a.createElement(X,{layout:"vertical",data:_,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3",horizontal:!1}),a.createElement(T,{type:"number",domain:[0,100],tickFormatter:d=>`${d}%`}),a.createElement(R,{dataKey:E,type:"category",width:120,tick:{fontSize:12}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(d,g)=>{let F=d;const G=typeof d=="number"&&!isNaN(d);return G&&g==="oee"&&d<=1&&d>0&&(F=d*100),g==="oee"?[G?`${F.toFixed(2)}%`:`${d}%`,"TRS Actuel"]:g==="target"?[`${d}%`,"Objectif"]:g==="minimum"?[`${d}%`,"Minimum"]:[d,g]}}),a.createElement(Qe,{formatter:d=>{const g={oee:"TRS Actuel",target:"Objectif",minimum:"Minimum"};return d==="oee"?a.createElement("span",null,a.createElement("span",null,"TRS Actuel")):g[d]||d}}),a.createElement(z,{dataKey:i,name:"oee",fill:l[0],radius:[0,4,4,0],barSize:20})))}),Wa=()=>{const[u,i]=m.useState([]),[E,n]=m.useState([]),[y,_]=m.useState([]),[d,g]=m.useState(0),[F,G]=m.useState(0),[Dt,Ee]=m.useState(!1),[$,Le]=m.useState(null),[ha,ga]=m.useState([]),[oe,be]=m.useState([]),[Ea,St]=m.useState([]),[ba,Ke]=m.useState([]),[Na,Be]=m.useState([]),[Ne,Oe]=m.useState([]),[V,Z]=m.useState([]),[xa,Mt]=m.useState("1"),_e=fa(),[_t,ke]=m.useState([]),[We,Re]=m.useState([]),[N,xe]=m.useState(""),[w,ce]=m.useState(""),[kt,Fe]=m.useState([]),[Q,He]=m.useState("day"),[Rt,we]=m.useState(""),[Ge,Ue]=m.useState(!1),I=(()=>{if(typeof window<"u"){const e=window.location.origin;return e.includes("ngrok-free.app")||e.includes("ngrok.io")?e:"http://localhost:5000"}return"http://localhost:5000"})(),Je=m.useCallback((e,t)=>{if(!e)return{short:"",full:""};const r=s(e);if(t==="day")return{short:r.format("DD/MM/YYYY"),full:`le ${r.format("DD MMMM YYYY")}`};if(t==="week"){const o=r.startOf("isoWeek"),x=r.endOf("isoWeek");return{short:`S${r.isoWeek()} ${r.format("YYYY")}`,full:`du ${o.format("DD MMMM")} au ${x.format("DD MMMM YYYY")}`}}else if(t==="month")return{short:r.format("MMMM YYYY"),full:`${r.format("MMMM YYYY")}`};return{short:"",full:""}},[]),Xe=m.useCallback(async()=>{try{console.log("Fetching machine models...");const e=await j.get(I+"/api/machine-models").withCredentials();if(e.data&&e.data.length>0){const t=e.data.map(r=>r.model||r);console.log("Machine models fetched:",t),ke(t)}else console.log("No machine models returned from API, using defaults"),ke(["IPS","CCM24"])}catch(e){console.error("Error loading machine models:",e),ke(["IPS","CCM24"])}},[]),Ze=m.useCallback(async()=>{try{console.log("Fetching machine names...");const e=await j.get(I+"/api/machine-names").withCredentials();e.data&&e.data.length>0?(console.log("Machine names fetched:",e.data),Re(e.data),e.data.find(r=>r.Machine_Name==="IPS01")&&!N&&xe("IPS")):(console.log("No machine names returned from API, using defaults"),Re([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}catch(e){console.error("Error loading machine names:",e),Re([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}},[N]),Ft=m.useCallback(()=>{const e=new URLSearchParams;if(N&&!w?e.append("model",N):w&&e.append("machine",w),$){const t=s($).format("YYYY-MM-DD");e.append("date",t),e.append("dateRangeType",Q)}else console.log("No date filter applied, showing all data for the selected machine model");return e.toString()?`?${e.toString()}`:""},[N,w,$,Q]),Ae=m.useCallback(async()=>{var e,t,r;Ee(!0);try{let o=function(){const c=s(),h=[];console.log("Generating sample data starting from:",c.format("YYYY-MM-DD"));for(let S=9;S>=0;S--){const f=c.subtract(S,"day").format("YYYY-MM-DD");if(!s(f).isValid()){console.error("Invalid date generated:",f);continue}h.push({date:f,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.floor(Math.random()*30)+70,speed:Math.floor(Math.random()*5)+5,Machine_Name:w||(N?`${N}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(Math.random()*3)]})}return console.log("Generated sample data:",h.length,"records"),console.log("Sample data first record:",h[0]),console.log("Sample data last record:",h[h.length-1]),h};const x=Ft();console.log("API query string:",x);const L=await Promise.allSettled([j.get(I+`/api/testing-chart-production${x}`).withCredentials(),j.get(I+"/api/unique-dates-production").withCredentials(),j.get(I+`/api/sidecards-prod${x}`).withCredentials(),j.get(I+`/api/sidecards-prod-rejet${x}`).withCredentials(),j.get(I+`/api/machine-performance${x}`).withCredentials(),j.get(I+`/api/hourly-trends${x}`).withCredentials(),j.get(I+`/api/machine-oee-trends${x}`).withCredentials(),j.get(I+`/api/speed-trends${x}`).withCredentials(),j.get(I+`/api/shift-comparison${x}`).withCredentials(),j.get(I+`/api/machine-daily-mould${x}`).withCredentials()]);console.log("API responses:",L.map(c=>c.status));const[D,ot,nt,lt,Pe,it,de,me,st,K]=L;if(D.status==="fulfilled"&&D.value.body){const c=q(D.value),S=(Array.isArray(c)?c:[]).map(Ot);i(S);let f=0;S.length>0&&(f=S.reduce((te,B)=>{let A=parseFloat(B.availability||0);return A=H(A),te+A},0)/S.length);let re=0;S.length>0&&(re=S.reduce((te,B)=>{let A=parseFloat(B.performance||0);return A=H(A),te+A},0)/S.length);let fe=0;S.length>0&&(fe=S.reduce((te,B)=>{let A=parseFloat(B.quality||0);return A=H(A),te+A},0)/S.length),console.log(" the avgAvailability is "+f.toFixed(2)+"%"),console.log(" the performance is "+re.toFixed(2)+"%"),console.log(" the quality is "+fe.toFixed(2)+"%")}else console.log("No chart data available"),i([]);if(ot.status==="fulfilled"){const c=q(ot.value);_(c||[])}if(nt.status==="fulfilled"){const c=q(nt.value);g(((e=c[0])==null?void 0:e.goodqty)||0)}else g(0);if(lt.status==="fulfilled"){const c=q(lt.value);G(((t=c[0])==null?void 0:t.rejetqty)||0)}else G(0);if(Pe.status==="fulfilled"&&Pe.value.data){const c=q(Pe.value);console.log("Machine performance data:",c),be(c||[])}else console.log("No machine performance data available"),be([]);if(it.status==="fulfilled"){const c=q(it.value);St(c||[])}const ct=de.status==="fulfilled"&&de.value.data?q(de.value).reduce((c,h)=>(c[h.date]=parseFloat(h.oee)||0,c),{}):{},dt=me.status==="fulfilled"&&me.value.data?q(me.value).reduce((c,h)=>{const S=parseFloat(h.speed);return!isNaN(S)&&S>0&&(c[h.date]=S),c},{}):{},ue=[...[...new Set([...Object.keys(ct),...Object.keys(dt)])]].sort((c,h)=>s(c).diff(s(h)));let mt=ue;if(ue.length>0){const c=s(ue[ue.length-1]);mt=ue.filter(h=>c.diff(s(h),"day")<=60)}const ne=mt.map(c=>({date:c,oee:ct[c]||0,speed:dt[c]||null})).sort((c,h)=>s(c.date).diff(s(h.date)));if(K&&K.status==="fulfilled"&&K.value.data){const c=q(K.value);if(c.length>0){console.log("Machine daily mould data available:",c.length,"records"),console.log("Sample record:",c[0]);try{const h=c.map(f=>{const re=parseFloat(f.Good_QTY_Day||f.good||0),fe=parseFloat(f.Rejects_QTY_Day||f.reject||0),ee=parseFloat(f.OEE_Day||f.oee||0),te=parseFloat(f.Speed_Day||f.speed||0),B=parseFloat(f.Availability_Rate_Day||f.availability||0),A=parseFloat(f.Performance_Rate_Day||f.performance||0),pe=parseFloat(f.Quality_Rate_Day||f.quality||0);let le=null;try{const ie=f.Date_Insert_Day||f.date;if(ie)if(s(ie).isValid())le=s(ie).format("YYYY-MM-DD");else{const qt=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const Vt of qt){const ht=s(ie,Vt);if(ht.isValid()){le=ht.format("YYYY-MM-DD");break}}}le||(console.warn(`Invalid date found: ${f.Date_Insert_Day||f.date}, using today's date instead`),le=s().format("YYYY-MM-DD"))}catch(ie){console.error("Error parsing date:",ie),le=s().format("YYYY-MM-DD")}let ut=0;isNaN(ee)||(ut=ee<=1&&ee>0?ee*100:ee);let ft=0;isNaN(B)||(ft=B<=1&&B>0?B*100:B);let pt=0;isNaN(A)||(pt=A<=1&&A>0?A*100:A);let yt=0;return isNaN(pe)||(yt=pe<=1&&pe>0?pe*100:pe),{date:le,oee:ut,speed:isNaN(te)?0:te,good:isNaN(re)?0:re,reject:isNaN(fe)?0:fe,Machine_Name:f.Machine_Name||"N/A",Shift:f.Shift||"N/A",availability:ft,performance:pt,quality:yt}}).sort((f,re)=>s(f.date).diff(s(re.date)));if(console.log("Processed mould data for charts:",h.length,"records"),console.log("First record:",h[0]),console.log("Last record:",h[h.length-1]),h.some(f=>f.good>0||f.reject>0||f.oee>0||f.speed>0))Z(h);else{console.warn("No valid data points found in processed mould data");const f=o();Z(f),ye.info({message:"Données de démonstration",description:"Aucune donnée valide n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}}catch(h){console.error("Error processing mould data:",h),Z(ne)}}else{if(console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback"),ne.length>0)Z(ne);else{const h=o();Z(h),ye.info({message:"Données de démonstration",description:"Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}(N||w||$)&&ye.info({message:"Aucune donnée disponible",description:"Aucune donnée n'a été trouvée pour les filtres sélectionnés. Essayez de modifier vos critères de recherche.",duration:5})}}else if(console.log("Machine daily mould API request failed or returned invalid data"),K&&K.status==="rejected"&&console.error("API error:",K.reason),ne.length>0)Z(ne);else{const c=o();Z(c),ye.info({message:"Données de démonstration",description:"Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}console.log("Current table data state:",V),console.log("New table data being set:",(K==null?void 0:K.status)==="fulfilled"&&((r=K.value.data)==null?void 0:r.length)>0?K.value.data:ne),de.status==="fulfilled"&&Ke(q(de.value)||[]),me.status==="fulfilled"&&Be(q(me.value)||[]),st.status==="fulfilled"&&Oe(q(st.value)||[])}catch(o){console.error("Error loading data:",o),g(0),G(0),i([]),be([])}finally{Ee(!1)}},[N,w,Q,$]),wt=e=>{if(!e){Ye();return}Le(e);const{full:t}=Je(e,Q);we(t),Ue(!0)},At=e=>{if(He(e),$){const{full:t}=Je($,e);we(t)}},Ye=()=>{Le(null),we(""),Ue(!1)},Yt=()=>{Ye(),He("day"),xe(""),ce(""),Fe([])},vt=()=>{Ae();let e=0;u.length>0&&(e=u.reduce((x,L)=>{let D=parseFloat(L.availability||0);return D=H(D),x+D},0)/u.length);let t=0;u.length>0&&(t=u.reduce((x,L)=>{let D=parseFloat(L.performance||0);return D=H(D),x+D},0)/u.length);let r=0;u.length>0&&(r=u.reduce((x,L)=>{let D=parseFloat(L.quality||0);return D=H(D),x+D},0)/u.length),console.log(" the Availability is "+e.toFixed(2)+"%"),console.log(" the performance is "+t.toFixed(2)+"%"),console.log(" the quality is "+r.toFixed(2)+"%")},Ct=e=>{xe(e)},Pt=e=>{ce(e)};m.useEffect(()=>{Xe()},[Xe]),m.useEffect(()=>{Ze()},[Ze]),m.useEffect(()=>{(async()=>{var t,r;try{Ee(!0);const o=await Promise.allSettled([j.get(I+"/api/sidecards-prod").withCredentials(),j.get(I+"/api/sidecards-prod-rejet").withCredentials()]),[x,L]=o;if(x.status==="fulfilled"){const D=q(x.value);console.log("Good quantity response:",D),g(((t=D[0])==null?void 0:t.goodqty)||15e3)}else console.error("Failed to fetch good quantity:",x.reason),g(15e3);if(L.status==="fulfilled"){const D=q(L.value);console.log("Rejected quantity response:",D),G(((r=D[0])==null?void 0:r.rejetqty)||750)}else console.error("Failed to fetch rejected quantity:",L.reason),G(750)}catch(o){console.error("Error loading general data:",o),g(15e3),G(750)}finally{Ee(!1)}})()},[N]),m.useEffect(()=>{if(N){const e=We.filter(t=>t.Machine_Name&&t.Machine_Name.startsWith(N));Fe(e),w&&!e.some(t=>t.Machine_Name===w)&&ce("")}else Fe([]),ce("")},[N,We,w]),m.useEffect(()=>{N?(Ae(),$||ye.info({message:"Affichage de toutes les données",description:"Aucun filtre de date n'est appliqué. Toutes les données disponibles pour le modèle sélectionné sont affichées.",icon:a.createElement(Ie,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:3})):(i([]),be([]),Z([]),Ke([]),Be([]),Oe([]))},[N,w,$,Q,Ae]);let et=0;u.length>0&&(et=u.reduce((t,r)=>{let o=parseFloat(r.oee||0);return o=H(o),t+o},0)/u.length);const Tt=d+F>0?F/(d+F)*100:0,$t=d+F>0?d/(d+F)*100:0;let ve=0;u.length>0&&(ve=u.reduce((t,r)=>{let o=parseFloat(r.availability||0);return o=H(o),t+o},0)/u.length);let Ce=0;u.length>0&&(Ce=u.reduce((t,r)=>{let o=parseFloat(r.performance||0);return o=H(o),t+o},0)/u.length);let tt=0;u.length>0&&(tt=u.reduce((t,r)=>{let o=parseFloat(r.quality||0);return o=H(o),t+o},0)/u.length),console.log(" the Availability is "+ve.toFixed(2)+"%"),console.log(" the performance is "+Ce.toFixed(2)+"%"),console.log(" the quality is "+tt.toFixed(2)+"%");const at=[{title:"Production Totale",value:d,suffix:"Pcs",icon:a.createElement(ra,null),color:"#52c41a",description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:F,suffix:"Kg",icon:a.createElement(oa,null),color:"#f5222d",description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:et,suffix:"%",icon:a.createElement(je,null),color:"#1890ff",description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:ve,suffix:"%",icon:a.createElement(na,null),color:"#722ed1",description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:Ce,suffix:"%",icon:a.createElement(la,null),color:"#eb2f96",description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:Tt,suffix:"%",icon:a.createElement(ia,null),color:"#fa8c16",description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:$t,suffix:"%",icon:a.createElement(sa,null),color:"#52c41a",description:"Pourcentage de pièces bonnes sur la production totale"}],It=[{title:"Machine",dataIndex:"Machine_Name",key:"machine",fixed:"left",render:e=>a.createElement(k,null,a.createElement(qe,{style:{color:l[0]}}),a.createElement(C,{strong:!0},e))},{title:"Moule",dataIndex:"mould_number",key:"mould",render:e=>a.createElement(M,{color:"cyan"},e||"N/A")},{title:"Équipe",dataIndex:"Shift",key:"shift",render:e=>a.createElement(M,{color:"blue"},e||"N/A")},{title:"Production",dataIndex:"good",key:"good",render:e=>a.createElement(M,{color:"green"},(e||0).toLocaleString()," pcs"),sorter:(e,t)=>(e.good||0)-(t.good||0)},{title:"Rejets",dataIndex:"reject",key:"reject",render:e=>a.createElement(M,{color:"red"},(e||0).toLocaleString()," kg"),sorter:(e,t)=>(e.reject||0)-(t.reject||0)},{title:"Heures Prod.",dataIndex:"run_hours",key:"run_hours",render:e=>a.createElement(M,{color:"green"},(e||0).toFixed(2)," h"),sorter:(e,t)=>(e.run_hours||0)-(t.run_hours||0)},{title:"Heures Arrêt",dataIndex:"down_hours",key:"down_hours",render:e=>a.createElement(M,{color:"orange"},(e||0).toFixed(2)," h"),sorter:(e,t)=>(e.down_hours||0)-(t.down_hours||0)},{title:"Cycle Théorique",dataIndex:"cycle_theorique",key:"cycle_theorique",render:e=>a.createElement(M,{color:"purple"},e||"N/A")},{title:"Disponibilité",dataIndex:"availability",key:"availability",render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de disponibilité`},a.createElement(J,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.availability=="number"&&!isNaN(e.availability)&&(r=e.availability<=1&&e.availability>0?e.availability*100:e.availability);let o=0;return typeof t.availability=="number"&&!isNaN(t.availability)&&(o=t.availability<=1&&t.availability>0?t.availability*100:t.availability),r-o}},{title:"Performance",dataIndex:"performance",key:"performance",render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de performance`},a.createElement(J,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.performance=="number"&&!isNaN(e.performance)&&(r=e.performance<=1&&e.performance>0?e.performance*100:e.performance);let o=0;return typeof t.performance=="number"&&!isNaN(t.performance)&&(o=t.performance<=1&&t.performance>0?t.performance*100:t.performance),r-o}},{title:"TRS",dataIndex:"oee",key:"oee",render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de TRS`},a.createElement(J,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.oee=="number"&&!isNaN(e.oee)&&(r=e.oee<=1&&e.oee>0?e.oee*100:e.oee);let o=0;return typeof t.oee=="number"&&!isNaN(t.oee)&&(o=t.oee<=1&&t.oee>0?t.oee*100:t.oee),r-o},defaultSortOrder:"descend"},{title:"Actions",key:"actions",render:(e,t)=>a.createElement(Kt,{overlay:a.createElement(De,null,a.createElement(De.Item,{key:"1",icon:a.createElement(Me,null)},"Voir tendances"),a.createElement(De.Item,{key:"2",icon:a.createElement(Bt,null)},"Paramètres"),a.createElement(De.Item,{key:"3",icon:a.createElement(Se,null)},"Exporter données")),trigger:["click"]},a.createElement(se,{type:"text",icon:a.createElement(Ht,null)}))}],rt=(e,t,r={})=>!e||e.length===0?a.createElement("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"}},a.createElement(Te,{description:"Aucune donnée disponible"})):a.createElement(t,{data:e,...r}),jt=()=>{const t=new Date().getHours();return t>=6&&t<14?"Matin":t>=14&&t<22?"Après-midi":"Nuit"};return a.createElement("div",{style:{padding:_e.md?24:16}},a.createElement(Qt,{spinning:Dt,tip:"Chargement des données...",size:"large"},a.createElement(W,{gutter:[24,24]},a.createElement(p,{span:24},a.createElement(b,{bordered:!1,bodyStyle:{padding:_e.md?24:16}},a.createElement(W,{gutter:[24,24],align:"middle"},a.createElement(p,{xs:24,md:12},a.createElement(bt,{level:3,style:{marginBottom:8}},a.createElement(je,{style:{marginRight:12,color:l[0]}}),"Tableau de Bord de Production")),a.createElement(p,{xs:24,md:12,style:{textAlign:_e.md?"right":"left"}},a.createElement(k,{direction:"vertical",style:{width:"100%"}},a.createElement(k,{wrap:!0},a.createElement(ze,{placeholder:"Sélectionner un modèle",style:{width:150},value:N||void 0,onChange:Ct,allowClear:!0},_t.map(e=>a.createElement(Nt,{key:e,value:e},e))),N&&a.createElement(ze,{placeholder:"Sélectionner une machine",style:{width:150},value:w||void 0,onChange:Pt,allowClear:!0},kt.map(e=>a.createElement(Nt,{key:e.Machine_Name,value:e.Machine_Name},e.Machine_Name))),a.createElement(Gt,{options:[{label:"Jour",value:"day"},{label:"Semaine",value:"week"},{label:"Mois",value:"month"}],value:Q,onChange:At}),a.createElement(Ut,{placeholder:"Filtrer par date",value:$,onChange:wt,format:"DD/MM/YYYY",picker:Q==="month"?"month":Q==="week"?"week":"date",allowClear:!0,disabledDate:e=>e>s().endOf("day")}),a.createElement(se,{icon:a.createElement(Jt,null),onClick:Yt,type:"primary",ghost:!0},"Réinitialiser"),a.createElement(se,{type:"primary",icon:a.createElement(Xt,null),onClick:vt,disabled:!N},"Actualiser")),(N||Ge)&&a.createElement(k,{wrap:!0,style:{marginTop:8}},N&&a.createElement(M,{color:"blue",closable:!0,onClose:()=>xe("")},"Modèle: ",N),w&&a.createElement(M,{color:"green",closable:!0,onClose:()=>ce("")},"Machine: ",w),Ge&&a.createElement(M,{color:"purple",closable:!0,onClose:Ye},"Période: ",Rt))))))),at.slice(0,4).map(e=>a.createElement(p,{key:e.title,xs:24,sm:12,md:6},a.createElement(b,{hoverable:!0,style:{borderTop:`2px solid ${e.color}`,height:"100%"}},a.createElement(O,{title:a.createElement(U,{title:e.description},a.createElement(k,null,a.cloneElement(e.icon,{style:{color:e.color,fontSize:20}}),a.createElement("span",null,e.title),a.createElement(Ie,{style:{color:"#8c8c8c",fontSize:14}}))),value:e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:e.color}}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&a.createElement(J,{percent:e.value,strokeColor:e.color,showInfo:!1,status:e.value>85?"success":e.value>70?"normal":"exception",style:{marginTop:12}})))),at.slice(4).map(e=>a.createElement(p,{key:e.title,xs:24,sm:12,md:6},a.createElement(b,{hoverable:!0,style:{borderTop:`2px solid ${e.color}`,height:"100%"}},a.createElement(O,{title:a.createElement(U,{title:e.description},a.createElement(k,null,a.cloneElement(e.icon,{style:{color:e.color,fontSize:20}}),a.createElement("span",null,e.title),a.createElement(Ie,{style:{color:"#8c8c8c",fontSize:14}}))),value:e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:e.color}}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&a.createElement(J,{percent:e.value,strokeColor:e.color,showInfo:!1,status:e.value>85?"success":e.value>70?"normal":"exception",style:{marginTop:12}})))),N?a.createElement(a.Fragment,null,a.createElement(p,{span:24},a.createElement(b,{bordered:!1},a.createElement(xt,{defaultActiveKey:"1",onChange:Mt,tabBarExtraContent:a.createElement(k,null,a.createElement(se,{type:"link",icon:a.createElement(Se,null),disabled:!0},"Exporter"),w&&a.createElement(Wt,{machineId:w,machineName:w,shift:jt()}))},a.createElement(Ve,{tab:a.createElement("span",null,a.createElement(Me,null),"Tendances"),key:"1"},a.createElement(W,{gutter:[24,24]},a.createElement(p,{span:24},a.createElement(b,{title:a.createElement(k,null,a.createElement(Me,{style:{fontSize:20,color:l[0]}}),a.createElement(C,{strong:!0},"Performance ",Q==="day"?"Journalière":Q==="week"?"Hebdomadaire":"Mensuelle")),bordered:!1,extra:a.createElement(M,{color:$?"blue":"green"},$?Q==="day"?`${s($).format("DD/MM/YYYY")}`:Q==="week"?`Semaine du ${s($).startOf("week").format("DD/MM/YYYY")}`:`${s($).format("MMMM YYYY")}`:"Toutes les données")},a.createElement(W,{gutter:[24,24]},a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Quantité Bonne",type:"inner"},V&&V.length>0?a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:V,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),a.createElement(T,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&s(e).isValid()?s(e).format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",e,t),"N/A"}}}),a.createElement(R,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{const t=parseFloat(e);return[isNaN(t)?"N/A":t.toLocaleString(),"Quantité bonne"]},labelFormatter:e=>{try{return e&&s(e).isValid()?`Date: ${s(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return console.error("Error formatting tooltip date:",e,t),"Date: N/A"}}}),a.createElement(z,{dataKey:"good",name:"Quantité bonne",fill:l[2],maxBarSize:40}))):a.createElement("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"}},a.createElement(Te,{description:"Aucune donnée disponible"})))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Quantité Rejetée",type:"inner"},V&&V.length>0?a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:V,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),a.createElement(T,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&s(e).isValid()?s(e).format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",e,t),"N/A"}}}),a.createElement(R,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité (kg)",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{const t=parseFloat(e);return[isNaN(t)?"N/A":t.toLocaleString(),"Quantité rejetée (kg)"]},labelFormatter:e=>{try{return e&&s(e).isValid()?`Date: ${s(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return console.error("Error formatting tooltip date:",e,t),"Date: N/A"}}}),a.createElement(z,{dataKey:"reject",name:"Quantité rejetée",fill:l[4],maxBarSize:40}))):a.createElement("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"}},a.createElement(Te,{description:"Aucune donnée disponible"}))))))),a.createElement(p,{xs:24,md:24},a.createElement(b,{title:a.createElement(k,null,a.createElement(Me,{style:{fontSize:20,color:l[0]}}),a.createElement(C,{strong:!0},"Tendances TRS et Cycle De Temps")),bordered:!1,extra:a.createElement(M,{color:"cyan"},"Évolution")},a.createElement(W,{gutter:[24,24]},a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"TRS",type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(ge,{data:V,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&s(e).isValid()?s(e).format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",e,t),"N/A"}}}),a.createElement(R,{tickFormatter:e=>`${e}%`,domain:[0,100]}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{let t=parseFloat(e);const r=!isNaN(t);return r&&t<=1&&t>0&&(t=t*100),[r?`${t.toFixed(2)}%`:`${e}%`,"TRS"]},labelFormatter:e=>{try{return e&&s(e).isValid()?`Date: ${s(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return console.error("Error formatting tooltip date:",e,t),"Date: N/A"}}}),a.createElement(ae,{type:"monotone",dataKey:"oee",name:"TRS",stroke:l[0],strokeWidth:2,dot:{r:4,fill:l[0]},activeDot:{r:6,fill:"#fff",stroke:l[0],strokeWidth:2}}))))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Cycle De Temps",type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(ge,{data:V,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&s(e).isValid()?s(e).format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",e,t),"N/A"}},type:"category",allowDuplicatedCategory:!1,interval:"preserveStartEnd"}),a.createElement(R,{domain:[0,"dataMax + 1"],tickFormatter:e=>`${e.toFixed(2)}`}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{const t=parseFloat(e);return[!isNaN(t)?t.toFixed(2):e,"Cycle De Temps"]},labelFormatter:e=>{try{return e&&s(e).isValid()?`Date: ${s(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return console.error("Error formatting tooltip date:",e,t),"Date: N/A"}}}),a.createElement(ae,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:l[1],strokeWidth:2,dot:{r:4,fill:l[1]},activeDot:{r:6,fill:"#fff",stroke:l[1],strokeWidth:2},connectNulls:!0})))))))))),a.createElement(Ve,{tab:a.createElement("span",null,a.createElement(he,null),"Performance"),key:"2"},a.createElement(W,{gutter:[24,24]},a.createElement(p,{xs:24,md:24},a.createElement(b,{title:a.createElement(k,null,a.createElement(he,{style:{fontSize:20,color:l[1]}}),a.createElement(C,{strong:!0},"Performance des Machines")),bordered:!1,extra:a.createElement($e,{count:oe.length,style:{backgroundColor:l[1]}})},a.createElement(W,{gutter:[24,24]},a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Production par Machine",bordered:!1,type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:Object.values(oe.reduce((e,t)=>{const r=t.Machine_Name;return e[r]||(e[r]={Machine_Name:r,production:0}),e[r].production+=Number(t.production)||0,e},{})),margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Machine_Name",tick:{fill:"#666"},interval:0,angle:-45,textAnchor:"end",height:80}),a.createElement(R,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Production"]}),a.createElement(z,{dataKey:"production",name:"Production",fill:l[0]}))))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Rejets par Machine",bordered:!1,type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:Object.values(oe.reduce((e,t)=>{const r=t.Machine_Name;return e[r]||(e[r]={Machine_Name:r,rejects:0}),e[r].rejects+=Number(t.rejects)||0,e},{})),margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Machine_Name",tick:{fill:"#666"},interval:0,angle:-45,textAnchor:"end",height:80}),a.createElement(R,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité (kg)",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Rejets (kg)"]}),a.createElement(z,{dataKey:"rejects",name:"Rejets",fill:l[4]})))))))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:a.createElement(k,null,a.createElement(he,{style:{fontSize:20,color:l[5]}}),a.createElement(C,{strong:!0},"TRS par Machine")),bordered:!1,extra:a.createElement(M,{color:"purple"},"Performance")},rt(oe,ya,{dataKey:"oee",nameKey:"Machine_Name",color:l[5]}))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:a.createElement(k,null,a.createElement(he,{style:{fontSize:20,color:l[3]}}),a.createElement(C,{strong:!0},"Répartition Production")),bordered:!1,extra:a.createElement(M,{color:"red"},"Qualité")},rt([{name:"Bonnes Pièces",value:Number(d)||0},{name:"Rejets",value:Number(F)||0}].filter(e=>e.value>0),pa,{colors:[l[2],l[4]]}))),a.createElement(p,{xs:24,md:24},a.createElement(b,{title:a.createElement(k,null,a.createElement(he,{style:{fontSize:20,color:l[3]}}),a.createElement(C,{strong:!0},"Comparaison des Équipes")),bordered:!1,extra:a.createElement(M,{color:"orange"},"Par équipe")},a.createElement(W,{gutter:[24,24]},a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Production par Équipe",bordered:!1,type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:Ne,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Shift",tick:{fill:"#666"}}),a.createElement(R,{tickFormatter:e=>e.toLocaleString(),label:{value:"Production",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Production"],labelFormatter:e=>`Équipe: ${e}`}),a.createElement(z,{dataKey:"production",name:"Production",fill:l[2],maxBarSize:40}))))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Temps d'arrêt par Équipe",bordered:!1,type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(X,{data:Ne,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Shift",tick:{fill:"#666"}}),a.createElement(R,{tickFormatter:e=>e.toLocaleString(),label:{value:"Temps d'arrêt",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Temps d'arrêt"],labelFormatter:e=>`Équipe: ${e}`}),a.createElement(z,{dataKey:"downtime",name:"Temps d'arrêt",fill:l[4],maxBarSize:40}))))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"TRS par Équipe",bordered:!1,type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(ge,{data:Ne,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Shift",tick:{fill:"#666"}}),a.createElement(R,{tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"TRS",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{let t=parseFloat(e);const r=!isNaN(t);return r&&t<=1&&t>0&&(t=t*100),[r?`${t.toFixed(2)}%`:`${e}%`,"TRS"]},labelFormatter:e=>`Équipe: ${e}`}),a.createElement(ae,{type:"monotone",dataKey:"oee",name:"TRS",stroke:l[0],strokeWidth:2,dot:{r:4,fill:l[0]}}))))),a.createElement(p,{xs:24,md:12},a.createElement(b,{title:"Performance par Équipe",bordered:!1,type:"inner"},a.createElement(Y,{width:"100%",height:300},a.createElement(ge,{data:Ne,margin:{top:16,right:24,left:24,bottom:16}},a.createElement(P,{strokeDasharray:"3 3"}),a.createElement(T,{dataKey:"Shift",tick:{fill:"#666"}}),a.createElement(R,{tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"Performance",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.createElement(v,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{let t=parseFloat(e);const r=!isNaN(t);return r&&t<=1&&t>0&&(t=t*100),[r?`${t.toFixed(2)}%`:`${e}%`,"Performance"]},labelFormatter:e=>`Équipe: ${e}`}),a.createElement(ae,{type:"monotone",dataKey:"performance",name:"Performance",stroke:l[5],strokeWidth:2,dot:{r:4,fill:l[5]}})))))))))),a.createElement(Ve,{tab:a.createElement("span",null,a.createElement(Et,null),"Détails"),key:"3"},a.createElement(W,{gutter:[24,24]},a.createElement(p,{span:24},a.createElement(b,{title:a.createElement(k,null,a.createElement(qe,{style:{fontSize:20,color:l[1]}}),a.createElement(C,{strong:!0},"Performance Totale par Quart")),bordered:!1,extra:a.createElement(k,null,a.createElement($e,{count:oe.length,style:{backgroundColor:l[1]}}),a.createElement(se,{type:"link",icon:a.createElement(Se,null),disabled:!0},"Exporter"))},a.createElement(gt,{dataSource:oe,columns:It,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:e=>`Total ${e} machines`},scroll:{x:1300},rowKey:"Machine_Name"}))),a.createElement(p,{span:24},a.createElement(b,{title:a.createElement(k,null,a.createElement(Et,{style:{fontSize:20,color:l[0]}}),a.createElement(C,{strong:!0},"Données Détaillées de Production")),bordered:!1,extra:a.createElement(k,null,a.createElement($e,{count:V.length,style:{backgroundColor:l[0]}}),a.createElement(se,{type:"link",icon:a.createElement(Se,null),disabled:!0},"Exporter"))},a.createElement(gt,{dataSource:V.map(e=>({...e,date:(()=>{try{const t=e.Date_Insert_Day||e.date;return t&&s(t).isValid()?s(t).format("YYYY-MM-DD"):(console.warn(`Invalid date found in table data: ${t}, using today's date`),s().format("YYYY-MM-DD"))}catch(t){return console.error("Error parsing date for table:",t),s().format("YYYY-MM-DD")}})(),Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",good:typeof e.good=="number"?e.good:typeof e.Good_QTY_Day=="number"?e.Good_QTY_Day:parseFloat(e.Good_QTY_Day)||0,reject:typeof e.reject=="number"?e.reject:typeof e.Rejects_QTY_Day=="number"?e.Rejects_QTY_Day:parseFloat(e.Rejects_QTY_Day)||0,oee:(()=>{let t;if(typeof e.oee=="number"&&!isNaN(e.oee))t=e.oee;else if(typeof e.OEE_Day=="number"&&!isNaN(e.OEE_Day))t=e.OEE_Day;else if(e.OEE_Day){const r=String(e.OEE_Day).replace(",",".");t=parseFloat(r)}else t=0;return!isNaN(t)&&t>0&&t<=1?t*100:t})(),speed:typeof e.speed=="number"?e.speed:typeof e.Speed_Day=="number"?e.Speed_Day:parseFloat(e.Speed_Day)||null,mould_number:e.Part_Number||e.mould_number||"N/A",poid_unitaire:e.Poid_Unitaire||e.poid_unitaire||"N/A",cycle_theorique:e.Cycle_Theorique||e.cycle_theorique||"N/A",poid_purge:e.Poid_Purge||e.poid_purge||"N/A",availability:(()=>{let t;if(typeof e.availability=="number"&&!isNaN(e.availability))t=e.availability;else if(typeof e.Availability_Rate_Day=="number"&&!isNaN(e.Availability_Rate_Day))t=e.Availability_Rate_Day;else if(e.Availability_Rate_Day){const r=String(e.Availability_Rate_Day).replace(",",".");t=parseFloat(r)}else t=0;return!isNaN(t)&&t>0&&t<=1?t*100:t})(),performance:(()=>{let t;if(typeof e.performance=="number"&&!isNaN(e.performance))t=e.performance;else if(typeof e.Performance_Rate_Day=="number"&&!isNaN(e.Performance_Rate_Day))t=e.Performance_Rate_Day;else if(e.Performance_Rate_Day){const r=String(e.Performance_Rate_Day).replace(",",".");t=parseFloat(r)}else t=0;return!isNaN(t)&&t>0&&t<=1?t*100:t})(),quality:(()=>{let t;if(typeof e.quality=="number"&&!isNaN(e.quality))t=e.quality;else if(typeof e.Quality_Rate_Day=="number"&&!isNaN(e.Quality_Rate_Day))t=e.Quality_Rate_Day;else if(e.Quality_Rate_Day){const r=String(e.Quality_Rate_Day).replace(",",".");t=parseFloat(r)}else t=0;return!isNaN(t)&&t>0&&t<=1?t*100:t})(),run_hours:typeof e.run_hours=="number"?e.run_hours:typeof e.Run_Hours_Day=="number"?e.Run_Hours_Day:parseFloat(e.Run_Hours_Day)||0,down_hours:typeof e.down_hours=="number"?e.down_hours:typeof e.Down_Hours_Day=="number"?e.Down_Hours_Day:parseFloat(e.Down_Hours_Day)||0})),rowKey:e=>`${e.date}-${e.Machine_Name||"unknown"}-${e.mould_number||"unknown"}`,scroll:{x:2200},pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:e=>`Total ${e} enregistrements`},expandable:{expandedRowRender:e=>a.createElement(b,{size:"small",title:"Informations du moule"},a.createElement(W,{gutter:[16,16]},a.createElement(p,{span:8},a.createElement(O,{title:"Numéro de moule",value:e.mould_number||"N/A",valueStyle:{fontSize:16}})),a.createElement(p,{span:8},a.createElement(O,{title:"Poids unitaire",value:e.poid_unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})),a.createElement(p,{span:8},a.createElement(O,{title:"Cycle théorique",value:e.cycle_theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})),a.createElement(p,{span:8},a.createElement(O,{title:"Poids purge",value:e.poid_purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})),a.createElement(p,{span:8},a.createElement(O,{title:"TRS (OEE)",value:(()=>{if(e.oee===void 0||e.oee===null)return"N/A";let t=parseFloat(e.oee);return isNaN(t)?"N/A":(t=t<=1&&t>0?t*100:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(e.oee===void 0||e.oee===null)return"";let t=parseFloat(e.oee);return isNaN(t)?"":(t=t<=1&&t>0?t*100:t,t>85?"#52c41a":t>70?"#1890ff":"#f5222d")})()},suffix:"%"})),a.createElement(p,{span:8},a.createElement(O,{title:"Disponibilité",value:(()=>{if(e.availability===void 0||e.availability===null)return"N/A";let t=parseFloat(e.availability);return isNaN(t)?"N/A":(t=t<=1&&t>0?t*100:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(e.availability===void 0||e.availability===null)return"";let t=parseFloat(e.availability);return isNaN(t)?"":(t=t<=1&&t>0?t*100:t,t>85?"#52c41a":t>70?"#1890ff":"#f5222d")})()},suffix:"%"})),a.createElement(p,{span:8},a.createElement(O,{title:"Performance",value:(()=>{if(e.performance===void 0||e.performance===null)return"N/A";let t=parseFloat(e.performance);return isNaN(t)?"N/A":(t=t<=1&&t>0?t*100:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(e.performance===void 0||e.performance===null)return"";let t=parseFloat(e.performance);return isNaN(t)?"":(t=t<=1&&t>0?t*100:t,t>85?"#52c41a":t>70?"#1890ff":"#f5222d")})()},suffix:"%"})),a.createElement(p,{span:8},a.createElement(O,{title:"Taux de qualité",value:(()=>{if(e.quality===void 0||e.quality===null)return"N/A";let t=parseFloat(e.quality);return isNaN(t)?"N/A":(t=t<=1&&t>0?t*100:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(e.quality===void 0||e.quality===null)return"";let t=parseFloat(e.quality);return isNaN(t)?"":(t=t<=1&&t>0?t*100:t,t>90?"#52c41a":t>80?"#1890ff":"#f5222d")})()},suffix:"%"})),a.createElement(p,{span:8},a.createElement(O,{title:"Vitesse",value:e.speed?e.speed.toFixed(1):"N/A",valueStyle:{fontSize:16},suffix:"u/h"})))),expandRowByClick:!0,rowExpandable:e=>e.mould_number&&e.mould_number!=="N/A"},columns:[{title:"Date",dataIndex:"date",key:"date",fixed:"left",width:120,render:e=>{const t=e?s(e).isValid():!1;return a.createElement(k,null,a.createElement(aa,{style:{color:l[0]}}),a.createElement(C,null,t?s(e).format("DD/MM/YYYY"):"N/A"))},sorter:(e,t)=>{const r=e.date?s(e.date):null,o=t.date?s(t.date):null;return!r&&!o?0:r?o?r.unix()-o.unix():-1:1},defaultSortOrder:"descend"},{title:"Machine",dataIndex:"Machine_Name",key:"machine",width:150,render:e=>a.createElement(k,null,a.createElement(qe,{style:{color:l[1]}}),a.createElement(C,null,e||"N/A")),filters:Array.from(new Set(V.map(e=>e.Machine_Name||"N/A"))).map(e=>({text:e,value:e})),onFilter:(e,t)=>t.Machine_Name===e||e==="N/A"&&!t.Machine_Name},{title:"Moule",dataIndex:"mould_number",key:"mould_number",width:120,render:e=>a.createElement(M,{color:"purple"},e||"N/A"),filters:Array.from(new Set(V.map(e=>e.mould_number||e.Mould_Number||"N/A"))).map(e=>({text:e,value:e})),onFilter:(e,t)=>t.mould_number===e||e==="N/A"&&!t.mould_number},{title:"Part Number",dataIndex:"Part_Number",key:"part_number",width:100,render:e=>a.createElement(M,{color:"cyan"},e||"N/A")},{title:"Équipe",dataIndex:"Shift",key:"shift",width:120,render:e=>a.createElement(M,{color:"blue"},e||"N/A"),filters:[{text:"Matin",value:"Matin"},{text:"Après-midi",value:"Après-midi"},{text:"Nuit",value:"Nuit"}],onFilter:(e,t)=>t.Shift===e},{title:"Production",dataIndex:"good",key:"good",width:120,render:e=>{const t=typeof e=="number"&&!isNaN(e)?e:0;return a.createElement(M,{color:"green"},t.toLocaleString()," pcs")},sorter:(e,t)=>{const r=typeof e.good=="number"&&!isNaN(e.good)?e.good:0,o=typeof t.good=="number"&&!isNaN(t.good)?t.good:0;return r-o}},{title:"Rejets",dataIndex:"reject",key:"reject",width:120,render:e=>{const t=typeof e=="number"&&!isNaN(e)?e:0;return a.createElement(M,{color:"red"},t.toLocaleString()," kg")},sorter:(e,t)=>{const r=typeof e.reject=="number"&&!isNaN(e.reject)?e.reject:0,o=typeof t.reject=="number"&&!isNaN(t.reject)?t.reject:0;return r-o}},{title:"Cycle Théorique",dataIndex:"cycle_theorique",key:"cycle_theorique",width:120,render:e=>a.createElement(C,null,e||"N/A")},{title:"Poids Unitaire",dataIndex:"poid_unitaire",key:"poid_unitaire",width:120,render:e=>a.createElement(C,null,e||"N/A")},{title:"Poids Purge",dataIndex:"poid_purge",key:"poid_purge",width:120,render:e=>a.createElement(C,null,e||"N/A")},{title:"TRS",dataIndex:"oee",key:"oee",width:150,render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de TRS`},a.createElement(J,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.oee=="number"&&!isNaN(e.oee)&&(r=e.oee<=1&&e.oee>0?e.oee*100:e.oee);let o=0;return typeof t.oee=="number"&&!isNaN(t.oee)&&(o=t.oee<=1&&t.oee>0?t.oee*100:t.oee),r-o}},{title:"Disponibilité",dataIndex:"availability",key:"availability",width:150,render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de disponibilité`},a.createElement(J,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.availability=="number"&&!isNaN(e.availability)&&(r=e.availability<=1&&e.availability>0?e.availability*100:e.availability);let o=0;return typeof t.availability=="number"&&!isNaN(t.availability)&&(o=t.availability<=1&&t.availability>0?t.availability*100:t.availability),r-o}},{title:"Performance",dataIndex:"performance",key:"performance",width:150,render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de performance`},a.createElement(J,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.performance=="number"&&!isNaN(e.performance)&&(r=e.performance<=1&&e.performance>0?e.performance*100:e.performance);let o=0;return typeof t.performance=="number"&&!isNaN(t.performance)&&(o=t.performance<=1&&t.performance>0?t.performance*100:t.performance),r-o}},{title:"Qualité",dataIndex:"quality",key:"quality",width:150,render:e=>{let t=0;if(typeof e=="number"&&!isNaN(e))t=e<=1&&e>0?e*100:e;else if(typeof e=="string"){const r=parseFloat(e);isNaN(r)||(t=r<=1&&r>0?r*100:r)}return t=Math.max(0,Math.min(100,t)),a.createElement(U,{title:`${t.toFixed(1)}% de qualité`},a.createElement(J,{percent:t,size:"small",status:t>90?"success":t>80?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(e,t)=>{let r=0;typeof e.quality=="number"&&!isNaN(e.quality)&&(r=e.quality<=1&&e.quality>0?e.quality*100:e.quality);let o=0;return typeof t.quality=="number"&&!isNaN(t.quality)&&(o=t.quality<=1&&t.quality>0?t.quality*100:t.quality),r-o}},{title:"Heures de Fonctionnement",dataIndex:"run_hours",key:"run_hours",width:150,render:e=>{const t=typeof e=="number"&&!isNaN(e)?e:parseFloat(e)||0;return a.createElement(C,null,t.toFixed(2),"h")},sorter:(e,t)=>{const r=typeof e.run_hours=="number"&&!isNaN(e.run_hours)?e.run_hours:parseFloat(e.run_hours)||0,o=typeof t.run_hours=="number"&&!isNaN(t.run_hours)?t.run_hours:parseFloat(t.run_hours)||0;return r-o}},{title:"Heures d'Arrêt",dataIndex:"down_hours",key:"down_hours",width:150,render:e=>{const t=typeof e=="number"&&!isNaN(e)?e:parseFloat(e)||0;return a.createElement(C,null,t.toFixed(2),"h")},sorter:(e,t)=>{const r=typeof e.down_hours=="number"&&!isNaN(e.down_hours)?e.down_hours:parseFloat(e.down_hours)||0,o=typeof t.down_hours=="number"&&!isNaN(t.down_hours)?t.down_hours:parseFloat(t.down_hours)||0;return r-o}}]}))))))))):a.createElement(a.Fragment,null,a.createElement(p,{span:24},a.createElement(b,null,a.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},a.createElement(je,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),a.createElement(bt,{level:3},"Veuillez sélectionner un modèle de machine"),a.createElement(ua,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},"Pour visualiser plus de données du tableau de bord, veuillez sélectionner un modèle de machine (IPS ou CCM24). Les données détaillées seront affichées en fonction de votre sélection."))))))))};export{Wa as default};
