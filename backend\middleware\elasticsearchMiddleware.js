import elasticsearchService from '../services/elasticsearchService.js';

/**
 * Middleware to automatically index data to Elasticsearch
 * This middleware should be used after successful database operations
 */

// Middleware for indexing machine session data
const indexMachineSessionMiddleware = async (req, res, next) => {
  // Store original res.json to intercept successful responses
  const originalJson = res.json;
  
  res.json = function(data) {
    // Only index if the operation was successful (status 200 or 201)
    if (res.statusCode === 200 || res.statusCode === 201) {
      // Index to Elasticsearch asynchronously (don't block response)
      setImmediate(async () => {
        try {
          const { machineId, machineData } = req.body;
          
          // Prepare session data for Elasticsearch
          const sessionData = {
            id: data.sessionId || Date.now(), // Use returned sessionId or timestamp
            machine_id: machineId,
            machine_name: machineData.Machine_Name,
            machine_model: machineData.Machine_Model || 'Unknown',
            timestamp: new Date().toISOString(),
            start_time: new Date().toISOString(),
            end_time: null, // Will be set when session ends
            duration: null,
            status: machineData.Etat || 'unknown',
            production_total: parseInt(machineData.Quantite_Bon) || 0,
            production_rate: calculateProductionRate(machineData),
            production_target: parseInt(machineData.Quantite_Planifier) || 0,
            efficiency: parseFloat(machineData.TRS) || 0,
            good_parts: parseInt(machineData.Quantite_Bon) || 0,
            defects: 0, // Calculate from rejects if needed
            rejects: parseInt(machineData.Quantite_Rejet) || 0,
            quality_rate: calculateQualityRate(machineData),
            trs: parseFloat(machineData.TRS) || 0,
            availability: calculateAvailability(machineData),
            performance: parseFloat(machineData.TRS) || 0,
            quality: calculateQualityRate(machineData),
            shift: determineShift(),
            operator: machineData.Regleur_Prenom || 'Unknown',
            notes: `Article: ${machineData.Article || 'N/A'}, Order: ${machineData.Ordre_Fabrication || 'N/A'}`
          };

          await elasticsearchService.indexMachineSession(sessionData);
          console.log(`Machine session indexed to Elasticsearch: ${sessionData.id}`);
        } catch (error) {
          console.error('Error indexing machine session to Elasticsearch:', error);
          // Don't throw error - this is background indexing
        }
      });
    }
    
    // Call original res.json
    return originalJson.call(this, data);
  };
  
  next();
};

// Middleware for indexing real-time data
const indexRealTimeDataMiddleware = async (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    // Index real-time data to Elasticsearch
    if (res.statusCode === 200 && Array.isArray(data)) {
      setImmediate(async () => {
        try {
          // Prepare bulk operations for all machines
          const bulkOperations = [];
          
          for (const machineData of data) {
            // Index operation
            bulkOperations.push({
              index: {
                _index: 'machine-realtime',
                _id: machineData.id || machineData.Machine_ID
              }
            });
            
            // Document data
            bulkOperations.push({
              machineId: machineData.id || machineData.Machine_ID,
              machineName: machineData.Machine_Name,
              machineModel: machineData.Machine_Model || 'Unknown',
              timestamp: new Date().toISOString(),
              status: machineData.Etat || 'unknown',
              currentProduction: parseInt(machineData.Quantite_Bon) || 0,
              productionRate: calculateProductionRate(machineData),
              trs: parseFloat(machineData.TRS) || 0,
              alerts: {
                active: machineData.Code_arret && machineData.Code_arret !== '',
                count: machineData.Code_arret ? 1 : 0,
                severity: determineSeverity(machineData.Code_arret),
                messages: machineData.Code_arret ? [machineData.Code_arret] : []
              },
              maintenance: {
                required: machineData.Etat === 'maintenance',
                lastMaintenance: null, // Would need to be fetched from maintenance records
                nextMaintenance: null
              },
              indexed_at: new Date().toISOString()
            });
          }
          
          if (bulkOperations.length > 0) {
            await elasticsearchService.bulkIndex(bulkOperations);
            console.log(`Bulk indexed ${data.length} real-time machine records to Elasticsearch`);
          }
        } catch (error) {
          console.error('Error bulk indexing real-time data to Elasticsearch:', error);
        }
      });
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

// Middleware for indexing reports
const indexReportMiddleware = async (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    // Index report if it's a successful creation or update
    if ((res.statusCode === 200 || res.statusCode === 201) && data.id) {
      setImmediate(async () => {
        try {
          await elasticsearchService.indexReport(data);
          console.log(`Report indexed to Elasticsearch: ${data.id}`);
        } catch (error) {
          console.error('Error indexing report to Elasticsearch:', error);
        }
      });
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

// Helper functions
function calculateProductionRate(machineData) {
  const production = parseInt(machineData.Quantite_Bon) || 0;
  const cycle = parseFloat(machineData.cycle) || 1;
  return cycle > 0 ? production / cycle : 0;
}

function calculateQualityRate(machineData) {
  const good = parseInt(machineData.Quantite_Bon) || 0;
  const rejects = parseInt(machineData.Quantite_Rejet) || 0;
  const total = good + rejects;
  return total > 0 ? (good / total) * 100 : 0;
}

function calculateAvailability(machineData) {
  // Simple availability calculation based on machine state
  return machineData.Etat === 'on' ? 100 : 0;
}

function determineShift() {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 14) return 'morning';
  if (hour >= 14 && hour < 22) return 'afternoon';
  return 'night';
}

function determineSeverity(codeArret) {
  if (!codeArret || codeArret === '') return 'none';
  
  // Define severity based on error codes (customize based on your codes)
  const highSeverityCodes = ['EMERGENCY', 'CRITICAL', 'SAFETY'];
  const mediumSeverityCodes = ['WARNING', 'MAINTENANCE'];
  
  const upperCode = codeArret.toUpperCase();
  
  if (highSeverityCodes.some(code => upperCode.includes(code))) return 'high';
  if (mediumSeverityCodes.some(code => upperCode.includes(code))) return 'medium';
  return 'low';
}

// Conditional middleware - only apply if Elasticsearch is available
const conditionalElasticsearchMiddleware = (middlewareFunction) => {
  return async (req, res, next) => {
    try {
      // Check if Elasticsearch is available
      const { checkElasticsearchHealth } = await import('../config/elasticsearch.js');
      const isHealthy = await checkElasticsearchHealth();
      
      if (isHealthy) {
        return middlewareFunction(req, res, next);
      } else {
        console.warn('Elasticsearch is not available, skipping indexing');
        return next();
      }
    } catch (error) {
      console.error('Error checking Elasticsearch health:', error);
      return next();
    }
  };
};

// Middleware for indexing production data
const indexProductionDataMiddleware = async (req, res, next) => {
  const originalJson = res.json;

  res.json = function(data) {
    // Index production data if successful response
    if ((res.statusCode === 200 || res.statusCode === 201) && Array.isArray(data)) {
      setImmediate(async () => {
        try {
          // Prepare bulk operations for production data
          const bulkOperations = [];

          for (const productionRecord of data) {
            // Index operation
            bulkOperations.push({
              index: {
                _index: 'production-data',
                _id: `${productionRecord.Machine_Name}_${productionRecord.Date_Insert_Day}`
              }
            });

            // Document data
            bulkOperations.push(productionRecord);
          }

          if (bulkOperations.length > 0) {
            await elasticsearchService.bulkIndex(bulkOperations);
            console.log(`Bulk indexed ${data.length} production records to Elasticsearch`);
          }
        } catch (error) {
          console.error('Error bulk indexing production data to Elasticsearch:', error);
        }
      });
    }

    return originalJson.call(this, data);
  };

  next();
};

// Middleware for indexing machine stops data
const indexMachineStopsMiddleware = async (req, res, next) => {
  const originalJson = res.json;

  res.json = function(data) {
    // Index stops data if successful response
    if ((res.statusCode === 200 || res.statusCode === 201) && Array.isArray(data)) {
      setImmediate(async () => {
        try {
          // Prepare bulk operations for stops data
          const bulkOperations = [];

          for (const stopRecord of data) {
            // Index operation
            bulkOperations.push({
              index: {
                _index: 'machine-stops',
                _id: `${stopRecord.Machine_Name}_${stopRecord.Date_Insert}_${stopRecord.Debut_Stop}`
              }
            });

            // Document data
            bulkOperations.push(stopRecord);
          }

          if (bulkOperations.length > 0) {
            await elasticsearchService.bulkIndex(bulkOperations);
            console.log(`Bulk indexed ${data.length} machine stops records to Elasticsearch`);
          }
        } catch (error) {
          console.error('Error bulk indexing machine stops data to Elasticsearch:', error);
        }
      });
    }

    return originalJson.call(this, data);
  };

  next();
};

// Export wrapped middleware functions
export const indexMachineSession = conditionalElasticsearchMiddleware(indexMachineSessionMiddleware);
export const indexRealTimeData = conditionalElasticsearchMiddleware(indexRealTimeDataMiddleware);
export const indexReport = conditionalElasticsearchMiddleware(indexReportMiddleware);
export const indexProductionData = conditionalElasticsearchMiddleware(indexProductionDataMiddleware);
export const indexMachineStops = conditionalElasticsearchMiddleware(indexMachineStopsMiddleware);
