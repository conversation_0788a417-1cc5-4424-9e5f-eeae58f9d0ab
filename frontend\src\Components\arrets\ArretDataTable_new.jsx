import React, { memo } from 'react';
import { Table, Card, Tag, Badge, Typography, Grid } from 'antd';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import dayjs from 'dayjs';

const { Text } = Typography;
const { useBreakpoint } = Grid;

const ArretDataTable = memo(() => {
  const { stopsData, loading } = useArretQueuedContext();
  const screens = useBreakpoint();

  const getStatusColor = (codeStop) => {
    if (!codeStop) return "default";
    if (codeStop.toLowerCase().includes("non déclaré")) return "error";
    if (codeStop.toLowerCase().includes("maintenance")) return "warning";
    if (codeStop.toLowerCase().includes("changement")) return "processing";
    if (codeStop.toLowerCase().includes("réglage")) return "cyan";
    if (codeStop.toLowerCase().includes("problème")) return "orange";
    return "default";
  };

  // Process data to handle invalid values and field name inconsistencies
  const processedData = stopsData.map((item) => {
    // Try to parse dates to check validity
    let dateInsert = item.Date_Insert;
    let debutStop = item.Debut_Stop;
    let finStopTime = item.Fin_Stop_Time;

    // Function to parse dates with multiple formats
    const parseDate = (dateStr) => {
      if (!dateStr) return null;

      // Try multiple formats (with both single and double digit days/months)
      const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

      for (const format of formats) {
        const parsed = dayjs(dateStr, format);
        if (parsed.isValid()) {
          return dateStr;
        }
      }

      // If none of the formats work, try automatic parsing
      const parsed = dayjs(dateStr);
      if (parsed.isValid()) {
        return dateStr;
      }

      return null;
    };

    // Parse all date fields
    dateInsert = parseDate(dateInsert);
    debutStop = parseDate(debutStop);
    finStopTime = parseDate(finStopTime);

    return {
      ...item,
      // Ensure all required fields exist and handle field name variations
      Date_Insert: dateInsert,
      Machine_Name: item.Machine_Name || "N/A",
      // Use Part_NO as returned by the backend
      Part_No: item.Part_NO || "N/A",
      Code_Stop: item.Code_Stop || "N/A",
      Debut_Stop: debutStop,
      Fin_Stop_Time: finStopTime,
      Regleur_Prenom: item.Regleur_Prenom || "Non assigné",
      // Use the duration_minutes field from the API if available
      duration_minutes: item.duration_minutes || null,
    };
  });

  const columns = [
    {
      title: "Date",
      dataIndex: "Date_Insert",
      key: "Date_Insert",
      render: (text) => {
        if (!text) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non disponible
            </Text>
          );
        }
        try {
          // Try multiple formats
          const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];
          let validDate = null;

          for (const format of formats) {
            const date = dayjs(text, format);
            if (date.isValid()) {
              validDate = date;
              break;
            }
          }

          // If no format worked, try automatic parsing
          if (!validDate) {
            validDate = dayjs(text);
          }

          if (validDate && validDate.isValid()) {
            return validDate.format("DD/MM/YYYY");
          } else {
            return (
              <Text type="secondary" style={{ fontStyle: "italic" }}>
                Non disponible
              </Text>
            );
          }
        } catch (e) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non disponible
            </Text>
          );
        }
      },
      sorter: (a, b) => {
        try {
          if (!a.Date_Insert || !b.Date_Insert) {
            return 0;
          }

          // Function to parse dates with multiple formats
          const parseDate = (dateStr) => {
            if (!dateStr) return null;

            // Try multiple formats
            const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

            for (const format of formats) {
              const parsed = dayjs(dateStr, format);
              if (parsed.isValid()) {
                return parsed;
              }
            }

            // If none of the formats work, try automatic parsing
            const parsed = dayjs(dateStr);
            if (parsed.isValid()) {
              return parsed;
            }

            return null;
          };

          const dateA = parseDate(a.Date_Insert);
          const dateB = parseDate(b.Date_Insert);

          if (!dateA || !dateB || !dateA.isValid() || !dateB.isValid()) {
            return 0;
          }

          return dateA.unix() - dateB.unix();
        } catch (e) {
          return 0;
        }
      },
    },
    {
      title: "Machine",
      dataIndex: "Machine_Name",
      key: "Machine_Name",
      render: (text) => <Tag color="blue">{text || "N/A"}</Tag>,
      filters: [...new Set(processedData.map((item) => item.Machine_Name).filter(Boolean))].map((machine) => ({
        text: machine,
        value: machine,
      })),
      onFilter: (value, record) => record.Machine_Name === value,
    },
    {
      title: "OF",
      dataIndex: "Part_No",
      key: "Part_No",
      render: (text) => (text && text !== "N/A" ? text : <Text type="secondary">Non spécifié</Text>),
      responsive: ["md"],
    },
    {
      title: "Code Arrêt",
      dataIndex: "Code_Stop",
      key: "Code_Stop",
      render: (text) => <Badge status={getStatusColor(text)} text={text || "N/A"} />,
      filters: [...new Set(processedData.map((item) => item.Code_Stop).filter(Boolean))].map((code) => ({
        text: code,
        value: code,
      })),
      onFilter: (value, record) => record.Code_Stop === value,
    },
    {
      title: "Début",
      dataIndex: "Debut_Stop",
      key: "Debut_Stop",
      render: (text) => {
        if (!text) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non disponible
            </Text>
          );
        }
        try {
          // Try multiple formats
          const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];
          let validTime = null;

          for (const format of formats) {
            const time = dayjs(text, format);
            if (time.isValid()) {
              validTime = time;
              break;
            }
          }

          // If no format worked, try automatic parsing
          if (!validTime) {
            validTime = dayjs(text);
          }

          if (validTime && validTime.isValid()) {
            return validTime.format("HH:mm");
          } else {
            return (
              <Text type="secondary" style={{ fontStyle: "italic" }}>
                Non disponible
              </Text>
            );
          }
        } catch (e) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non disponible
            </Text>
          );
        }
      },
    },
    {
      title: "Fin",
      dataIndex: "Fin_Stop_Time",
      key: "Fin_Stop_Time",
      render: (text) => {
        if (!text) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non disponible
            </Text>
          );
        }
        try {
          // Try multiple formats
          const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];
          let validTime = null;

          for (const format of formats) {
            const time = dayjs(text, format);
            if (time.isValid()) {
              validTime = time;
              break;
            }
          }

          // If no format worked, try automatic parsing
          if (!validTime) {
            validTime = dayjs(text);
          }

          if (validTime && validTime.isValid()) {
            return validTime.format("HH:mm");
          } else {
            return (
              <Text type="secondary" style={{ fontStyle: "italic" }}>
                Non disponible
              </Text>
            );
          }
        } catch (e) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non disponible
            </Text>
          );
        }
      },
    },
    {
      title: "Durée",
      key: "duration",
      render: (_, record) => {
        // First try to use the duration_minutes field from the API
        if (record.duration_minutes !== null && record.duration_minutes !== undefined) {
          return `${record.duration_minutes} min`;
        }

        // Fallback to calculating duration if the field is not available
        if (!record.Debut_Stop || !record.Fin_Stop_Time) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non calculable
            </Text>
          );
        }
        try {
          // Try multiple formats for both start and end times
          const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

          let validStartTime = null;
          let validEndTime = null;

          // Try to parse start time with different formats
          for (const format of formats) {
            const startTime = dayjs(record.Debut_Stop, format);
            if (startTime.isValid()) {
              validStartTime = startTime;
              break;
            }
          }

          // If no format worked, try automatic parsing
          if (!validStartTime) {
            validStartTime = dayjs(record.Debut_Stop);
          }

          // Try to parse end time with different formats
          for (const format of formats) {
            const endTime = dayjs(record.Fin_Stop_Time, format);
            if (endTime.isValid()) {
              validEndTime = endTime;
              break;
            }
          }

          // If no format worked, try automatic parsing
          if (!validEndTime) {
            validEndTime = dayjs(record.Fin_Stop_Time);
          }

          if (!validStartTime.isValid() || !validEndTime.isValid()) {
            return (
              <Text type="secondary" style={{ fontStyle: "italic" }}>
                Non calculable
              </Text>
            );
          }

          const durationMinutes = validEndTime.diff(validStartTime, "minute");
          if (durationMinutes < 0) {
            return (
              <Text type="secondary" style={{ fontStyle: "italic" }}>
                Non calculable
              </Text>
            );
          }
          return `${durationMinutes} min`;
        } catch (e) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              Non calculable
            </Text>
          );
        }
      },
      sorter: (a, b) => {
        // Use duration_minutes from API if available
        if (a.duration_minutes !== null && a.duration_minutes !== undefined &&
            b.duration_minutes !== null && b.duration_minutes !== undefined) {
          return a.duration_minutes - b.duration_minutes;
        }

        // Fallback to calculating duration
        try {
          if (!a.Debut_Stop || !a.Fin_Stop_Time || !b.Debut_Stop || !b.Fin_Stop_Time) {
            return 0;
          }

          // Function to parse dates with multiple formats
          const parseDateTime = (dateStr) => {
            if (!dateStr) return null;

            // Try multiple formats
            const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

            for (const format of formats) {
              const parsed = dayjs(dateStr, format);
              if (parsed.isValid()) {
                return parsed;
              }
            }

            // If none of the formats work, try automatic parsing
            const parsed = dayjs(dateStr);
            if (parsed.isValid()) {
              return parsed;
            }

            return null;
          };

          // Parse all date fields
          const startTimeA = parseDateTime(a.Debut_Stop);
          const endTimeA = parseDateTime(a.Fin_Stop_Time);
          const startTimeB = parseDateTime(b.Debut_Stop);
          const endTimeB = parseDateTime(b.Fin_Stop_Time);

          if (!startTimeA || !endTimeA || !startTimeB || !endTimeB ||
              !startTimeA.isValid() || !endTimeA.isValid() || !startTimeB.isValid() || !endTimeB.isValid()) {
            return 0;
          }

          const durationA = endTimeA.diff(startTimeA, "minute");
          const durationB = endTimeB.diff(startTimeB, "minute");
          return durationA - durationB;
        } catch (e) {
          return 0;
        }
      },
    },
    {
      title: "Responsable",
      dataIndex: "Regleur_Prenom",
      key: "Regleur_Prenom",
      render: (text) => text || "Non assigné",
      filters: [...new Set(processedData.map((item) => item.Regleur_Prenom || "Non assigné").filter(Boolean))].map(
        (name) => ({
          text: name,
          value: name === "Non assigné" ? null : name,
        }),
      ),
      onFilter: (value, record) => (value ? record.Regleur_Prenom === value : !record.Regleur_Prenom),
    },
  ];

  return (
    <Card 
      title="Tableau des Arrêts"
      bordered={false}
      className="arret-data-table"
    >
      <Table
        columns={columns}
        dataSource={processedData}
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50"],
          showTotal: (total) => `Total ${total} arrêts`,
        }}
        scroll={{ x: screens.md ? undefined : 1000 }}
        bordered
        size="middle"
        rowKey={(record, index) => `${record.Date_Insert}-${index}`}
        rowClassName={(record) => {
          if (record.Code_Stop && record.Code_Stop.toLowerCase().includes("non déclaré")) {
            return "table-row-error";
          }
          return "";
        }}
      />
      <style jsx>{`
        .table-row-error {
          background-color: rgba(245, 34, 45, 0.05);
        }
        .ant-table-row:hover {
          cursor: pointer;
          background-color: rgba(24, 144, 255, 0.05) !important;
        }
      `}</style>
    </Card>
  );
});

ArretDataTable.displayName = 'ArretDataTable';

export default ArretDataTable;
