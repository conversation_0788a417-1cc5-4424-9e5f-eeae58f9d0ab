.ant-card {
  margin-bottom: 24px;
}


/* Enhanced Table Styles */
.ant-table-thead > tr > th {
  background: #fafafa !important;
  font-weight: 600 !important;
}

.ant-table-tbody > tr:hover > td {
  background: #fafafa !important;
}

/* Chart Container Styles */
.recharts-wrapper {
  margin: 0 auto;
  transition: all 0.3s ease;
}

.recharts-legend-item {
  display: flex !important;
  align-items: center;
}

/* Card Header Styles */
.ant-card-head-title {
  display: flex !important;
  align-items: center;
  gap: 8px;
}