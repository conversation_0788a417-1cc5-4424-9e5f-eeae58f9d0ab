import{r as l,a1 as Ue,t as ut,q as $e,bc as kt,bd as $t,Q as _t,U as Lt,ap as Tt,V as tt,W as Bt,av as rt,b5 as zt,a2 as mt,af as ve,R as t,C as xe,S as i,d as z,e as we,g as $,T as ft,f as J,a9 as at,l as Se,D as Ut,b as ht,c as oe,al as he,ay as jt,u as qt,j as Nt,b7 as Ht,a as De,ad as Ft,m as Gt,be as nt,B as Vt,F as Kt,E as ot,M as Xt}from"./index-LbZyOyVE.js";import{d as P}from"./dayjs.min-dvN_FXBc.js";import"./fr-B8jKMo6i.js";import{u as Qt}from"./useMobile-BCubE-HC.js";import{a as W,e as Ye,f as fe}from"./numberFormatter-CKFvf91F.js";import{u as Wt}from"./useDailyTableGraphQL-h9FMOZI7.js";import{b as Jt,D as je,R as st}from"./DownloadOutlined-DDEO8hwP.js";import{R as gt}from"./FilterOutlined-bWWAWSgt.js";import{R as _e}from"./ToolOutlined-CByaXpyN.js";import{R as yt}from"./CalendarOutlined-C9bHRkbV.js";import{R as lt}from"./SearchOutlined-DzAh4Hfi.js";import{R as it}from"./EyeOutlined-Bf51LvXG.js";import{R as Zt}from"./index-BYa_3SSV.js";import{R as er}from"./ReloadOutlined-CoxAQyfN.js";import{R as Le}from"./FileTextOutlined-DeApLYgQ.js";import{R as tr}from"./SyncOutlined-DJUomMIm.js";import{P as rr}from"./progress-DqUiDqeJ.js";import{R as ar}from"./FilePdfOutlined-CZagYiQ5.js";import{R as nr}from"./ClockCircleOutlined-DqsC6tNJ.js";import{R as or}from"./BarChartOutlined-DwmNYdCA.js";import{R as sr}from"./LineChartOutlined-Dj8Kxu7c.js";import{R as lr}from"./DashboardOutlined-1-sdY0Ts.js";import{R as ir}from"./CheckCircleOutlined-Bxw2HLIH.js";import{R as cr}from"./AreaChartOutlined-DNIZ2LBu.js";const Ae=({children:r})=>{const{getPrefixCls:o}=l.useContext(Ue),s=o("breadcrumb");return l.createElement("li",{className:`${s}-separator`,"aria-hidden":"true"},r===""?r:r||"/")};Ae.__ANT_BREADCRUMB_SEPARATOR=!0;var dr=function(r,o){var s={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&o.indexOf(n)<0&&(s[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,n=Object.getOwnPropertySymbols(r);c<n.length;c++)o.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(r,n[c])&&(s[n[c]]=r[n[c]]);return s};function pr(r,o){if(r.title===void 0||r.title===null)return null;const s=Object.keys(o).join("|");return typeof r.title=="object"?r.title:String(r.title).replace(new RegExp(`:(${s})`,"g"),(n,c)=>o[c]||n)}function Et(r,o,s,n){if(s==null)return null;const{className:c,onClick:E}=o,y=dr(o,["className","onClick"]),I=Object.assign(Object.assign({},ut(y,{data:!0,aria:!0})),{onClick:E});return n!==void 0?l.createElement("a",Object.assign({},I,{className:$e(`${r}-link`,c),href:n}),s):l.createElement("span",Object.assign({},I,{className:$e(`${r}-link`,c)}),s)}function ur(r,o){return(n,c,E,y,I)=>{if(o)return o(n,c,E,y);const f=pr(n,c);return Et(r,n,f,I)}}var Te=function(r,o){var s={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&o.indexOf(n)<0&&(s[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,n=Object.getOwnPropertySymbols(r);c<n.length;c++)o.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(r,n[c])&&(s[n[c]]=r[n[c]]);return s};const bt=r=>{const{prefixCls:o,separator:s="/",children:n,menu:c,overlay:E,dropdownProps:y,href:I}=r,g=(u=>{if(c||E){const x=Object.assign({},y);if(c){const h=c||{},{items:b}=h,C=Te(h,["items"]);x.menu=Object.assign(Object.assign({},C),{items:b==null?void 0:b.map((D,R)=>{var{key:Y,title:_,label:O,path:L}=D,F=Te(D,["key","title","label","path"]);let q=O??_;return L&&(q=l.createElement("a",{href:`${I}${L}`},q)),Object.assign(Object.assign({},F),{key:Y??R,label:q})})})}else E&&(x.overlay=E);return l.createElement(kt,Object.assign({placement:"bottom"},x),l.createElement("span",{className:`${o}-overlay-link`},u,l.createElement($t,null)))}return u})(n);return g!=null?l.createElement(l.Fragment,null,l.createElement("li",null,g),s&&l.createElement(Ae,null,s)):null},vt=r=>{const{prefixCls:o,children:s,href:n}=r,c=Te(r,["prefixCls","children","href"]),{getPrefixCls:E}=l.useContext(Ue),y=E("breadcrumb",o);return l.createElement(bt,Object.assign({},c,{prefixCls:y}),Et(y,c,s,n))};vt.__ANT_BREADCRUMB_ITEM=!0;const mr=r=>{const{componentCls:o,iconCls:s,calc:n}=r;return{[o]:Object.assign(Object.assign({},Tt(r)),{color:r.itemColor,fontSize:r.fontSize,[s]:{fontSize:r.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:r.linkColor,transition:`color ${r.motionDurationMid}`,padding:`0 ${tt(r.paddingXXS)}`,borderRadius:r.borderRadiusSM,height:r.fontHeight,display:"inline-block",marginInline:n(r.marginXXS).mul(-1).equal(),"&:hover":{color:r.linkHoverColor,backgroundColor:r.colorBgTextHover}},Bt(r)),"li:last-child":{color:r.lastItemColor},[`${o}-separator`]:{marginInline:r.separatorMargin,color:r.separatorColor},[`${o}-link`]:{[`
          > ${s} + span,
          > ${s} + a
        `]:{marginInlineStart:r.marginXXS}},[`${o}-overlay-link`]:{borderRadius:r.borderRadiusSM,height:r.fontHeight,display:"inline-block",padding:`0 ${tt(r.paddingXXS)}`,marginInline:n(r.marginXXS).mul(-1).equal(),[`> ${s}`]:{marginInlineStart:r.marginXXS,fontSize:r.fontSizeIcon},"&:hover":{color:r.linkHoverColor,backgroundColor:r.colorBgTextHover,a:{color:r.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${r.componentCls}-rtl`]:{direction:"rtl"}})}},fr=r=>({itemColor:r.colorTextDescription,lastItemColor:r.colorText,iconFontSize:r.fontSize,linkColor:r.colorTextDescription,linkHoverColor:r.colorText,separatorColor:r.colorTextDescription,separatorMargin:r.marginXS}),hr=_t("Breadcrumb",r=>{const o=Lt(r,{});return mr(o)},fr);var ct=function(r,o){var s={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&o.indexOf(n)<0&&(s[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,n=Object.getOwnPropertySymbols(r);c<n.length;c++)o.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(r,n[c])&&(s[n[c]]=r[n[c]]);return s};function gr(r){const{breadcrumbName:o,children:s}=r,n=ct(r,["breadcrumbName","children"]),c=Object.assign({title:o},n);return s&&(c.menu={items:s.map(E=>{var{breadcrumbName:y}=E,I=ct(E,["breadcrumbName"]);return Object.assign(Object.assign({},I),{title:y})})}),c}function yr(r,o){return l.useMemo(()=>r||(o?o.map(gr):null),[r,o])}var Er=function(r,o){var s={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&o.indexOf(n)<0&&(s[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,n=Object.getOwnPropertySymbols(r);c<n.length;c++)o.indexOf(n[c])<0&&Object.prototype.propertyIsEnumerable.call(r,n[c])&&(s[n[c]]=r[n[c]]);return s};const br=(r,o)=>{if(o===void 0)return o;let s=(o||"").replace(/^\//,"");return Object.keys(r).forEach(n=>{s=s.replace(`:${n}`,r[n])}),s},qe=r=>{const{prefixCls:o,separator:s="/",style:n,className:c,rootClassName:E,routes:y,items:I,children:f,itemRender:g,params:u={}}=r,x=Er(r,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:h,direction:b,breadcrumb:C}=l.useContext(Ue);let D;const R=h("breadcrumb",o),[Y,_,O]=hr(R),L=yr(I,y),F=ur(R,g);if(L&&L.length>0){const U=[],N=I||y;D=L.map((m,k)=>{const{path:X,key:re,type:Ie,menu:de,overlay:ge,onClick:ye,className:ee,separator:H,dropdownProps:Ee}=m,se=br(u,X);se!==void 0&&U.push(se);const G=re??k;if(Ie==="separator")return l.createElement(Ae,{key:G},H);const le={},Me=k===L.length-1;de?le.menu=de:ge&&(le.overlay=ge);let{href:ae}=m;return U.length&&se!==void 0&&(ae=`#/${U.join("/")}`),l.createElement(bt,Object.assign({key:G},le,ut(m,{data:!0,aria:!0}),{className:ee,dropdownProps:Ee,href:ae,separator:Me?"":s,onClick:ye,prefixCls:R}),F(m,u,N,U,ae))})}else if(f){const U=rt(f).length;D=rt(f).map((N,m)=>{if(!N)return N;const k=m===U-1;return zt(N,{separator:k?"":s,key:m})})}const q=$e(R,C==null?void 0:C.className,{[`${R}-rtl`]:b==="rtl"},c,E,_,O),Z=Object.assign(Object.assign({},C==null?void 0:C.style),n);return Y(l.createElement("nav",Object.assign({className:q,style:Z},x),l.createElement("ol",null,D)))};qe.Item=vt;qe.Separator=Ae;var vr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM514.1 580.1l-61.8-102.4c-2.2-3.6-6.1-5.8-10.3-5.8h-38.4c-2.3 0-4.5.6-6.4 1.9-5.6 3.5-7.3 10.9-3.7 16.6l82.3 130.4-83.4 132.8a12.04 12.04 0 0010.2 18.4h34.5c4.2 0 8-2.2 10.2-5.7L510 664.8l62.3 101.4c2.2 3.6 6.1 5.7 10.2 5.7H620c2.3 0 4.5-.7 6.5-1.9 5.6-3.6 7.2-11 3.6-16.6l-84-130.4 85.3-132.5a12.04 12.04 0 00-10.1-18.5h-35.7c-4.2 0-8.1 2.2-10.3 5.8l-61.2 102.3z"}}]},name:"file-excel",theme:"outlined"},Rr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M820 436h-40c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zm32-104H732V120c0-4.4-3.6-8-8-8H300c-4.4 0-8 3.6-8 8v212H172c-44.2 0-80 35.8-80 80v328c0 17.7 14.3 32 32 32h168v132c0 4.4 3.6 8 8 8h424c4.4 0 8-3.6 8-8V772h168c17.7 0 32-14.3 32-32V412c0-44.2-35.8-80-80-80zM360 180h304v152H360V180zm304 664H360V568h304v276zm200-140H732V500H292v204H160V412c0-6.6 5.4-12 12-12h680c6.6 0 12 5.4 12 12v292z"}}]},name:"printer",theme:"outlined"};function Be(){return Be=Object.assign?Object.assign.bind():function(r){for(var o=1;o<arguments.length;o++){var s=arguments[o];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},Be.apply(this,arguments)}const xr=(r,o)=>l.createElement(mt,Be({},r,{ref:o,icon:vr})),wr=l.forwardRef(xr);function ze(){return ze=Object.assign?Object.assign.bind():function(r){for(var o=1;o<arguments.length;o++){var s=arguments[o];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(r[n]=s[n])}return r},ze.apply(this,arguments)}const Cr=(r,o)=>l.createElement(mt,ze({},r,{ref:o,icon:Rr})),Sr=l.forwardRef(Cr);var Ce={exports:{}},Ar=Ce.exports,dt;function Ir(){return dt||(dt=1,function(r,o){(function(s,n){n()})(Ar,function(){function s(g,u){return typeof u>"u"?u={autoBom:!1}:typeof u!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),u={autoBom:!u}),u.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(g.type)?new Blob(["\uFEFF",g],{type:g.type}):g}function n(g,u,x){var h=new XMLHttpRequest;h.open("GET",g),h.responseType="blob",h.onload=function(){f(h.response,u,x)},h.onerror=function(){console.error("could not download file")},h.send()}function c(g){var u=new XMLHttpRequest;u.open("HEAD",g,!1);try{u.send()}catch{}return 200<=u.status&&299>=u.status}function E(g){try{g.dispatchEvent(new MouseEvent("click"))}catch{var u=document.createEvent("MouseEvents");u.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),g.dispatchEvent(u)}}var y=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof ve=="object"&&ve.global===ve?ve:void 0,I=y.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),f=y.saveAs||(typeof window!="object"||window!==y?function(){}:"download"in HTMLAnchorElement.prototype&&!I?function(g,u,x){var h=y.URL||y.webkitURL,b=document.createElement("a");u=u||g.name||"download",b.download=u,b.rel="noopener",typeof g=="string"?(b.href=g,b.origin===location.origin?E(b):c(b.href)?n(g,u,x):E(b,b.target="_blank")):(b.href=h.createObjectURL(g),setTimeout(function(){h.revokeObjectURL(b.href)},4e4),setTimeout(function(){E(b)},0))}:"msSaveOrOpenBlob"in navigator?function(g,u,x){if(u=u||g.name||"download",typeof g!="string")navigator.msSaveOrOpenBlob(s(g,x),u);else if(c(g))n(g,u,x);else{var h=document.createElement("a");h.href=g,h.target="_blank",setTimeout(function(){E(h)})}}:function(g,u,x,h){if(h=h||open("","_blank"),h&&(h.document.title=h.document.body.innerText="downloading..."),typeof g=="string")return n(g,u,x);var b=g.type==="application/octet-stream",C=/constructor/i.test(y.HTMLElement)||y.safari,D=/CriOS\/[\d]+/.test(navigator.userAgent);if((D||b&&C||I)&&typeof FileReader<"u"){var R=new FileReader;R.onloadend=function(){var O=R.result;O=D?O:O.replace(/^data:[^;]*;/,"data:attachment/file;"),h?h.location.href=O:location=O,h=null},R.readAsDataURL(g)}else{var Y=y.URL||y.webkitURL,_=Y.createObjectURL(g);h?h.location=_:location.href=_,h=null,setTimeout(function(){Y.revokeObjectURL(_)},4e4)}});y.saveAs=f.saveAs=f,r.exports=f})}(Ce)),Ce.exports}var Mr=Ir();const{Text:B}=ft,{Option:Oe}=he,{RangePicker:Pr}=je,Rt=l.memo(({activeReportType:r,dateRange:o,selectedShift:s,selectedMachines:n,selectedModels:c,searchText:E,machines:y,models:I,shifts:f,onReportTypeChange:g,onDateRangeChange:u,onShiftChange:x,onMachineChange:h,onModelChange:b,onSearchChange:C,onClearFilters:D,machinesLoading:R=!1,modelsLoading:Y=!1,existingReports:_=[],onCheckReportExists:O})=>{var N;const L=s||n.length>0||c.length>0||E,F=[s,n.length>0,c.length>0,E].filter(Boolean).length,q=r==="shift"&&(o==null?void 0:o[0])&&s&&_.some(m=>m.date===o[0].format("YYYY-MM-DD")&&m.shift===s),Z=r!=="shift"||(o==null?void 0:o[0])&&s&&n.length>0,U=m=>{if(r==="shift"){const k=Array.isArray(m)?m[0]:m;h(k?[k]:[]),k&&!c.includes("IPS")&&b(["IPS"])}else h(m||[])};return t.createElement(xe,{title:t.createElement(z,null,t.createElement(gt,{style:{color:i.SECONDARY_BLUE}}),t.createElement(B,{strong:!0},"Filtres Avancés"),F>0&&t.createElement(J,{color:i.PRIMARY_BLUE,style:{marginLeft:8}},F," actif",F>1?"s":"")),extra:t.createElement(z,null,L&&t.createElement(we,{title:"Effacer tous les filtres"},t.createElement($,{type:"text",icon:t.createElement(Jt,null),onClick:D,style:{color:i.LIGHT_GRAY},size:"small"},"Effacer"))),style:{marginBottom:16,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",border:`1px solid ${i.PRIMARY_BLUE}20`},bodyStyle:{paddingBottom:16}},L&&t.createElement(t.Fragment,null,t.createElement("div",{style:{padding:"8px 12px",backgroundColor:"#f0f8ff",borderRadius:"6px",border:`1px solid ${i.SECONDARY_BLUE}20`,marginBottom:"16px"}},t.createElement(z,{wrap:!0,size:"small"},t.createElement(B,{style:{fontSize:"12px",color:i.SECONDARY_BLUE,fontWeight:500}},"Filtres actifs:"),s&&t.createElement(J,{closable:!0,onClose:()=>x(null),color:i.SECONDARY_BLUE,size:"small"},t.createElement(at,null)," Équipe: ",(N=f.find(m=>m.key===s))==null?void 0:N.label),n.length>0&&t.createElement(J,{closable:!0,onClose:()=>h([]),color:i.PRIMARY_BLUE,size:"small"},t.createElement(_e,null)," Machines: ",n.length),c.length>0&&t.createElement(J,{closable:!0,onClose:()=>b([]),color:i.CHART_TERTIARY,size:"small"},t.createElement(Se,null)," Modèles: ",c.length),E&&t.createElement(J,{closable:!0,onClose:()=>C(""),color:"orange",size:"small"},'Recherche: "',E,'"'))),t.createElement(Ut,{style:{margin:"16px 0"}})),t.createElement(ht,{gutter:[16,16]},t.createElement(oe,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:i.DARK_GRAY}},t.createElement(Se,{style:{marginRight:4}}),"Modèles",c.length>0&&t.createElement(J,{size:"small",color:i.CHART_TERTIARY,style:{marginLeft:4}},c.length))),t.createElement(he,{mode:"multiple",placeholder:"Tous les modèles",style:{width:"100%"},allowClear:!0,onChange:b,value:c,maxTagCount:"responsive",showSearch:!0,loading:Y,filterOption:(m,k)=>{var X;return((X=k.children)==null?void 0:X.toLowerCase().indexOf(m.toLowerCase()))>=0},notFoundContent:Y?"Chargement...":"Aucun modèle trouvé"},I.map(m=>t.createElement(Oe,{key:m.id||m.name,value:m.id||m.name},m.name)))),t.createElement(oe,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:i.DARK_GRAY}},t.createElement(_e,{style:{marginRight:4}}),"Machines",n.length>0&&t.createElement(J,{size:"small",color:i.PRIMARY_BLUE,style:{marginLeft:4}},n.length))),t.createElement(he,{mode:r==="shift"?"single":"multiple",placeholder:r==="shift"?"Sélectionner une machine":"Toutes les machines",style:{width:"100%"},allowClear:!0,onChange:U,value:r==="shift"?n[0]:n,maxTagCount:"responsive",showSearch:!0,loading:R,filterOption:(m,k)=>{var X;return((X=k.children)==null?void 0:X.toLowerCase().indexOf(m.toLowerCase()))>=0},notFoundContent:R?"Chargement...":"Aucune machine trouvée"},y.map(m=>t.createElement(Oe,{key:m.id||m.name,value:m.id||m.name},m.name)))),(r==="shift"||r==="production")&&t.createElement(oe,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:i.DARK_GRAY}},t.createElement(at,{style:{marginRight:4}}),"Équipe",r==="shift"&&t.createElement(B,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),t.createElement(he,{value:s,onChange:x,placeholder:r==="shift"?"Sélectionner une équipe":"Toutes les équipes",allowClear:r!=="shift",style:{width:"100%"}},f.map(m=>t.createElement(Oe,{key:m.key,value:m.key},t.createElement(z,null,t.createElement("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:m.color}}),m.label," (",m.hours,")"))))),t.createElement(oe,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:i.DARK_GRAY}},t.createElement(yt,{style:{marginRight:4}}),r==="shift"?"Date":"Période",r==="shift"&&t.createElement(B,{style:{color:"#ff4d4f",fontSize:"12px"}}," *"))),r==="shift"?t.createElement(je,{value:o[0],onChange:m=>u([m,m]),format:"DD/MM/YYYY",placeholder:"Sélectionner une date",style:{width:"100%"},allowClear:!1}):t.createElement(Pr,{value:o,onChange:u,format:"DD/MM/YYYY",placeholder:["Date début","Date fin"],style:{width:"100%"},allowClear:!1})),t.createElement(oe,{xs:24,sm:12,md:8,lg:6},t.createElement("div",{style:{marginBottom:8}},t.createElement(B,{strong:!0,style:{color:i.DARK_GRAY}},t.createElement(lt,{style:{marginRight:4}}),"Recherche")),t.createElement(jt,{placeholder:"Rechercher par type, machine, date, utilisateur...",prefix:t.createElement(lt,null),allowClear:!0,onChange:m=>C(m.target.value),value:E,style:{width:"100%"}}))),L&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#52c41a"}},"✓ Filtres appliqués - Les données sont filtrées selon vos critères")),r==="shift"&&t.createElement(t.Fragment,null,q&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff7e6",border:"1px solid #ffd666",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#d48806"}},"⚠️ Un rapport existe déjà pour cette date et équipe")),!Z&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#cf1322"}},"❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine")),Z&&!q&&t.createElement("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"}},t.createElement(B,{style:{fontSize:"12px",color:"#52c41a"}},"✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)"))))});Rt.displayName="ReportFilters";const Dr=(r,o,s,n,c)=>{if(r!=="shift")return{isValid:!0,canCreate:!0,reportExists:!1};const E=(o==null?void 0:o[0])&&s&&c.some(f=>f.date===o[0].format("YYYY-MM-DD")&&f.shift===s),y=(o==null?void 0:o[0])&&s&&n.length>0;return{isValid:y,canCreate:y&&!E,reportExists:E}};var Yr={};P.locale("fr");const{Title:Or,Text:j}=ft,{Option:na}=he,{RangePicker:oa}=je,kr=[{key:"matin",label:"Équipe Matin",hours:"06:00-14:00",color:i.SECONDARY_BLUE},{key:"apres-midi",label:"Équipe Après-midi",hours:"14:00-22:00",color:i.PRIMARY_BLUE},{key:"nuit",label:"Équipe Nuit",hours:"22:00-06:00",color:i.DARK_GRAY}],ke=Yr.REACT_APP_API_URL||"/api",ce=[{key:"shift",label:"Rapports de quart",icon:t.createElement(nr,null),description:"Rapports par équipe de travail",endpoint:"/reports/shift",color:i.PRIMARY_BLUE,priority:1},{key:"daily",label:"Rapports journaliers",icon:t.createElement(yt,null),description:"Rapports quotidiens de production",endpoint:"/reports/daily",color:i.SECONDARY_BLUE,priority:2},{key:"weekly",label:"Rapports hebdomadaires",icon:t.createElement(or,null),description:"Rapports de performance hebdomadaire",endpoint:"/reports/weekly",color:i.CHART_TERTIARY,priority:3},{key:"monthly",label:"Rapports mensuels",icon:t.createElement(sr,null),description:"Rapports mensuels et tendances",endpoint:"/reports/monthly",color:i.CHART_QUATERNARY,priority:4},{key:"machine",label:"Rapports par machine",icon:t.createElement(_e,null),description:"Performance individuelle des machines",endpoint:"/reports/machine",color:i.PRIMARY_BLUE,priority:5},{key:"production",label:"Rapports de production",icon:t.createElement(lr,null),description:"Rapports de production quotidienne et performance",endpoint:"/reports/production",color:i.SECONDARY_BLUE,priority:6},{key:"maintenance",label:"Rapports de maintenance",icon:t.createElement(Se,null),description:"Maintenance préventive et corrective",endpoint:"/reports/maintenance",color:i.CHART_TERTIARY,priority:7},{key:"quality",label:"Rapports de qualité",icon:t.createElement(ir,null),description:"Contrôle qualité et rejets",endpoint:"/reports/quality",color:i.CHART_QUATERNARY,priority:8},{key:"financial",label:"Rapports financiers",icon:t.createElement(cr,null),description:"Rapports financiers et coûts",endpoint:"/reports/financial",color:i.PRIMARY_BLUE,priority:9},{key:"custom",label:"Rapports personnalisés",icon:t.createElement(Se,null),description:"Rapports configurables sur mesure",endpoint:"/reports/custom",color:i.SECONDARY_BLUE,priority:10}],pt=[{key:"pdf",label:"PDF",icon:t.createElement(ar,null),description:"Document PDF formaté",mimeType:"application/pdf"},{key:"excel",label:"Excel",icon:t.createElement(wr,null),description:"Fichier Excel avec données",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{key:"csv",label:"CSV",icon:t.createElement(Le,null),description:"Données CSV pour analyse",mimeType:"text/csv"}],Re={pending:{color:"processing",text:"En cours"},generating:{color:"processing",text:"Génération..."},completed:{color:"success",text:"Terminé"},failed:{color:"error",text:"Échec"},cancelled:{color:"default",text:"Annulé"}},sa=()=>{var We;const{user:r}=qt(),{darkMode:o}=Nt(),s=Qt(),{notification:n}=Ht.useApp(),{getAllDailyProduction:c,getMachinePerformance:E,getMachineModels:y,getMachineNames:I}=Wt(),[f,g]=l.useState("shift"),[u,x]=l.useState([P().subtract(7,"day"),P()]),[h,b]=l.useState(null),[C,D]=l.useState([]),[R,Y]=l.useState([]),[_,O]=l.useState(""),[L,F]=l.useState(!1),[q,Z]=l.useState(!0),[U,N]=l.useState([]),[m,k]=l.useState([]),[X,re]=l.useState([]),[Ie,de]=l.useState(!1),[ge,ye]=l.useState(!1),[ee,H]=l.useState({current:1,pageSize:10,total:0}),[Ee,se]=l.useState(!1),[G,le]=l.useState(null),[Me,ae]=l.useState(!1),[xt,pe]=l.useState(0),[be,ie]=l.useState(null),[Q,Ne]=l.useState(!0),[Pe,He]=l.useState([]),T=l.useMemo(()=>Dr(f,u,h,C,Pe),[f,u,h,C,Pe]),V=l.useMemo(()=>({async request(e,a={}){var d;try{const p=`${ke}${e}`;let v=De[((d=a.method)==null?void 0:d.toLowerCase())||"get"](p).withCredentials().retry(2).timeout(3e4).set("Content-Type","application/json");return a.headers&&Object.entries(a.headers).forEach(([w,K])=>{v=v.set(w,K)}),a.body&&a.method!=="GET"&&(v=v.send(a.body)),(await v).body}catch(p){throw console.error(`API Error for ${e}:`,p),p}},async getMachines(){try{console.log("🔄 GraphQL: Calling getMachineNames...");const e=await I();console.log("📊 GraphQL getMachineNames result:",e);const a=e.getMachineNames||[];console.log("✅ Raw machines data:",a);const d=a.map(p=>({id:p.Machine_Name,name:p.Machine_Name}));return console.log("✅ Processed machines:",d),d}catch(e){return console.error("❌ Error fetching machines via GraphQL:",e),[]}},async getModels(){try{console.log("🔄 GraphQL: Calling getMachineModels...");const e=await y();console.log("📊 GraphQL getMachineModels result:",e);const a=e.getMachineModels||[];console.log("✅ Raw models data:",a);const d=a.map(p=>({id:p.model,name:p.model}));return console.log("✅ Processed models:",d),d}catch(e){return console.error("❌ Error fetching models via GraphQL:",e),[]}},async getReports(e){try{console.log("🔍 [REPORTS] Fetching reports with params:",e);const a=new URLSearchParams;e.type&&e.type!=="all"&&a.append("type",e.type),e.startDate&&a.append("startDate",e.startDate),e.endDate&&a.append("endDate",e.endDate),e.shift&&a.append("shift",e.shift),e.machines&&a.append("machines",e.machines),e.search&&a.append("search",e.search),e.page&&a.append("page",e.page),e.pageSize&&a.append("pageSize",e.pageSize);const d=`/reports?${a.toString()}`;console.log("🔍 [REPORTS] Calling endpoint:",d);const p=await this.request(d,{method:"GET"});return console.log("✅ [REPORTS] API response:",p),p}catch(a){throw console.error("❌ [REPORTS] Error fetching reports:",a),a}},async generateReport(e,a=!1){var d,p,v,S,w,K;try{if(e.type==="shift"){if(!((p=(d=e.filters)==null?void 0:d.machines)!=null&&p[0]))throw new Error("Une machine doit être sélectionnée pour générer un rapport de quart.");if(!((v=e.filters)!=null&&v.shift))throw new Error("Une équipe doit être sélectionnée pour générer un rapport de quart.");if(!((S=e.dateRange)!=null&&S.start))throw new Error("Une date doit être sélectionnée pour générer un rapport de quart.");console.log("🔍 [SHIFT REPORT] Generating with params:",{machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift,enhanced:a});const A=a?"/shift-reports/generate-enhanced":"/shift-reports/generate",M=await De.post(`${ke}${A}`).withCredentials().send({machineId:e.filters.machines[0],date:e.dateRange.start,shift:e.filters.shift}).timeout(6e4).retry(2);if(M.body&&M.body.success){const me=M.body.version||"standard";return{success:!0,reportId:M.body.reportId||Date.now(),filePath:M.body.filePath,downloadPath:M.body.downloadPath||M.body.filePath,fileSize:M.body.fileSize,version:me,performance:(w=M.body.reportData)==null?void 0:w.performance,message:`Rapport de quart ${me==="enhanced"?"amélioré":"standard"} généré avec succès`,downloadUrl:M.body.downloadPath||`/api/shift-reports/download/${M.body.filename||"report.pdf"}`}}else throw new Error("Erreur lors de la génération du rapport de quart")}else throw e.type==="daily"?new Error("Les rapports quotidiens ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="weekly"?new Error("Les rapports hebdomadaires ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="machine"?new Error("Les rapports machine ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):e.type==="production"?new Error("Les rapports de production ne sont pas encore implémentés. Utilisez les rapports de quart pour le moment."):new Error(`Type de rapport non supporté: ${e.type}. Seuls les rapports de quart sont actuellement disponibles.`)}catch(A){throw console.error("Error generating report:",A),A.code==="ECONNABORTED"?new Error("La génération du rapport a pris trop de temps. Veuillez réessayer."):A.response?new Error(`Erreur ${A.response.status}: ${((K=A.response.data)==null?void 0:K.error)||A.response.statusText}`):A.request?new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):A}},async exportReport(e,a){return new Response(new Blob(["Mock export data"],{type:"text/plain"}))},async deleteReport(e){return{success:!0}}}),[r==null?void 0:r.token,r==null?void 0:r.name,I,y,E,c]),Fe=l.useCallback(async()=>{try{de(!0),console.log("🔄 Fetching machines...");const e=await V.getMachines();console.log("✅ Machines fetched:",e),console.log("📊 Machines data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting machines:",e),k(e)):(console.log("⚠️ Unexpected data format for machines:",e),k([])),ie(null)}catch(e){console.error("❌ Error fetching machines:",e),k([]),ie("Impossible de charger la liste des machines"),n.error({message:"Erreur de chargement",description:"Impossible de charger la liste des machines",duration:4})}finally{de(!1)}},[V]),Ge=l.useCallback(async()=>{try{ye(!0),console.log("🔄 Fetching models...");const e=await V.getModels();console.log("✅ Models fetched:",e),console.log("📊 Models data structure:",e),console.log("📊 Is array?",Array.isArray(e)),console.log("📊 Length:",e==null?void 0:e.length),Array.isArray(e)?(console.log("✅ Setting models:",e),re(e)):(console.log("⚠️ Unexpected data format for models:",e),re([]))}catch(e){console.error("❌ Error fetching models:",e),re([]),n.error({message:"Erreur de chargement",description:"Impossible de charger la liste des modèles",duration:4})}finally{ye(!1)}},[V]),te=l.useCallback(async()=>{var d;const e=Date.now();let a;try{F(!0),ie(null);const p={type:f,startDate:u[0].format("YYYY-MM-DD"),endDate:u[1].format("YYYY-MM-DD"),page:ee.current,pageSize:ee.pageSize,...h&&{shift:h},...C.length>0&&{machines:C.join(",")},...R.length>0&&{models:R.join(",")},..._&&{search:_}};console.log("🔍 [REPORTS] Fetching reports with params:",p);const v=new Promise((A,M)=>{a=setTimeout(()=>{M(new Error("Request timeout: La requête a pris trop de temps (45 secondes)"))},45e3)}),S=V.getReports(p),w=await Promise.race([S,v]);a&&clearTimeout(a);const K=Date.now()-e;console.log(`✅ [REPORTS] Fetch completed in ${K}ms`),N(Array.isArray(w)?w:w.reports||[]),H(A=>{var M;return{...A,total:w.total||((M=w.reports)==null?void 0:M.length)||0}})}catch(p){a&&clearTimeout(a);const v=Date.now()-e;console.error(`❌ [REPORTS] Error fetching reports after ${v}ms:`,p);let S="Impossible de charger les rapports",w="Une erreur inattendue s'est produite.";const A=(((d=p.response)==null?void 0:d.body)||{}).code||p.code;p.message.includes("timeout")||p.message.includes("Timeout")?(S="Délai d'attente dépassé",w="La requête a pris trop de temps (45 secondes). Essayez de réduire la plage de dates ou réessayez plus tard."):A==="INVALID_PARAMETERS"?(S="Paramètres invalides",w="Les paramètres de la requête sont incorrects. Veuillez réinitialiser les filtres et réessayer."):A==="DATABASE_ERROR"?(S="Erreur de base de données",w="Un problème temporaire avec la base de données s'est produit. Veuillez réessayer dans quelques instants."):A==="DATABASE_COMMUNICATION_ERROR"?(S="Erreur de communication",w="Problème de communication avec la base de données. Veuillez réessayer ou contacter l'administrateur."):A==="QUERY_TIMEOUT"?(S="Requête trop lente",w="La requête prend trop de temps. Essayez de réduire la plage de dates ou les filtres."):p.message.includes("Unauthorized")||p.status===401?(S="Session expirée",w="Votre session a expiré. Veuillez vous reconnecter."):p.message.includes("Not Found")||p.status===404?(S="Service indisponible",w="Le service de rapports n'est pas disponible. Contactez l'administrateur."):(p.message.includes("Network")||!navigator.onLine)&&(S="Problème de connexion",w="Vérifiez votre connexion internet et réessayez."),ie(S),N([]),n.error({message:S,description:w,duration:6,placement:"topRight"})}finally{F(!1),Z(!1),a&&clearTimeout(a)}},[f,u,h,C,R,_,ee.current,ee.pageSize,V]),ue=l.useCallback(async()=>{try{He([{date:"2025-07-13",shift:"matin",machine:"IPS01"},{date:"2025-07-13",shift:"apres-midi",machine:"IPS02"}])}catch(e){console.error("Error checking existing reports:",e),He([])}},[]);l.useEffect(()=>{Fe(),Ge(),te(),ue()},[Fe,Ge,te,ue]),l.useEffect(()=>{const e=U.filter(a=>["pending","generating"].includes(a.status));if(e.length>0&&!G){const a=setInterval(te,5e3);le(a)}else e.length===0&&G&&(clearInterval(G),le(null));return()=>{G&&clearInterval(G)}},[U,G,te]);const wt=l.useCallback(e=>{if(g(e),H(a=>({...a,current:1})),e==="shift"&&u){const a=u[0];a&&x([a,a])}},[u]),Ct=l.useCallback(e=>{x(e||[P().subtract(7,"day"),P()]),H(a=>({...a,current:1}))},[]),St=l.useCallback(e=>{b(e),H(a=>({...a,current:1}))},[]),At=l.useCallback(e=>{if(f==="shift"){const a=Array.isArray(e)?e:[e];D(a.filter(Boolean)),a.length>0&&R.length===0&&Y(["IPS"])}else D(e||[]);H(a=>({...a,current:1}))},[f,R.length]),It=l.useCallback(e=>{Y(e||[]),H(a=>({...a,current:1}))},[]),Mt=l.useCallback(e=>{O(e),H(a=>({...a,current:1}))},[]),Pt=l.useCallback(()=>{b(null),D([]),Y([]),O(""),H(e=>({...e,current:1}))},[]),Dt=l.useCallback(e=>{H(e)},[]),Ve=l.useCallback(async e=>{if(e.status!=="completed"){n.warning({message:"Rapport non disponible",description:"Ce rapport n'est pas encore terminé.",duration:3});return}try{console.log("🔍 [VIEW REPORT] Opening report:",e.id),n.info({message:"Ouverture du rapport",description:"Chargement du rapport PDF en cours...",duration:0,key:`loading-${e.id}`});const a=`${ke}/shift-reports/download/${e.id}`,d=await De.head(a).withCredentials().timeout(3e4).retry(2);if(n.destroy(`loading-${e.id}`),d.status===200)window.open(a,"_blank")?(console.log("✅ [VIEW REPORT] PDF opened successfully"),n.success({message:"Rapport ouvert",description:"Le rapport PDF a été ouvert dans un nouvel onglet.",duration:3})):n.warning({message:"Popup bloqué",description:t.createElement("div",null,t.createElement("p",null,"Le popup a été bloqué par votre navigateur."),t.createElement($,{type:"link",size:"small",onClick:()=>window.location.href=a},"Cliquez ici pour ouvrir le rapport")),duration:8});else throw new Error(`HTTP ${d.status}`)}catch(a){console.error("❌ [VIEW REPORT] Error opening report:",a),n.destroy(`loading-${e.id}`);let d="Erreur lors de l'ouverture du rapport",p="Une erreur inattendue s'est produite.";a.status===404?(d="Rapport introuvable",p="Le fichier PDF de ce rapport n'existe plus sur le serveur."):a.status===401||a.status===403?(d="Accès refusé",p="Vous n'avez pas les permissions pour voir ce rapport."):(a.timeout||a.message.includes("timeout"))&&(d="Délai d'attente dépassé",p="Le serveur met trop de temps à répondre. Réessayez plus tard."),n.error({message:d,description:p,duration:6})}},[n]),Ke=l.useCallback(async(e,a)=>{try{se(!0);const d=await V.exportReport(e.id,a),p=pt.find(v=>v.key===a);if(d instanceof Response){const v=await d.blob(),S=`rapport_${e.id}_${P().format("YYYY-MM-DD_HH-mm")}.${a}`;Mr.saveAs(v,S),n.success({message:"Export réussi",description:`Rapport exporté en ${(p==null?void 0:p.label)||a}`,duration:3})}}catch(d){console.error("Export error:",d),n.error({message:"Erreur d'exportation",description:`Impossible d'exporter le rapport: ${d.message}`,duration:4})}finally{se(!1)}},[V]),Xe=l.useCallback(async e=>{try{if(f==="shift"&&!T.canCreate){T.reportExists?n.warning({message:"Rapport déjà existant",description:"Un rapport existe déjà pour cette date et équipe.",duration:4}):n.error({message:"Informations manquantes",description:"Veuillez sélectionner la date, l'équipe et la machine pour créer un rapport de quart.",duration:4});return}ae(!0),pe(0);const a=setInterval(()=>{pe(p=>Math.min(p+10,90))},500),d=await V.generateReport({type:f,dateRange:{start:u[0].format("YYYY-MM-DD"),end:u[1].format("YYYY-MM-DD")},filters:{shift:h,machines:C,models:R.length>0?R:["IPS"]},...e},Q);clearInterval(a),pe(100),setTimeout(()=>{var p;ae(!1),pe(0),f==="shift"&&ue(),te(),n.success({message:`Rapport ${d.version==="enhanced"?"amélioré":"standard"} généré avec succès`,description:t.createElement("div",null,t.createElement("p",null,"Le rapport a été généré et sauvegardé avec succès."),t.createElement(z,null,t.createElement($,{type:"primary",size:"small",icon:t.createElement(it,null),onClick:()=>{d.downloadUrl&&window.open(d.downloadUrl,"_blank")}},"Voir le rapport"),t.createElement($,{size:"small",icon:t.createElement(st,null),onClick:()=>{if(d.downloadUrl){const v=document.createElement("a");v.href=d.downloadUrl,v.download=`rapport_${d.version}_${P().format("YYYY-MM-DD_HH-mm")}.pdf`,document.body.appendChild(v),v.click(),document.body.removeChild(v)}}},"Télécharger"))),duration:10,placement:"topRight"}),d.version==="enhanced"&&d.performance&&n.info({message:"Résumé Performance",description:`OEE: ${d.performance.totalProduction} unités produites, Qualité: ${(p=d.performance.qualityRate)==null?void 0:p.toFixed(1)}%`,duration:6})},1e3)}catch(a){console.error("Generation error:",a),ae(!1),pe(0),n.error({message:"Erreur de génération",description:`Impossible de générer le rapport: ${a.message}`,duration:4})}},[f,u,h,C,R,V,te,Q,T,ue]),Qe=l.useCallback(e=>{const a=window.open("","_blank");if(!a){n.error({message:"Erreur d'impression",description:"Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur."});return}const d=Yt(e),p=ce.find(v=>v.key===e.type);a.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport ${(p==null?void 0:p.label)||e.type} #${e.id}</title>
          <meta charset="utf-8">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 20px; 
              line-height: 1.6;
              color: #333;
            }
            .header { 
              display: flex; 
              justify-content: space-between; 
              align-items: center; 
              border-bottom: 2px solid ${i.PRIMARY_BLUE};
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 { 
              color: ${i.PRIMARY_BLUE}; 
              margin: 0;
              font-size: 24px;
            }
            .header .logo {
              font-weight: bold;
              color: ${i.SECONDARY_BLUE};
              font-size: 18px;
            }
            table { 
              border-collapse: collapse; 
              width: 100%; 
              margin: 20px 0;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 12px 8px; 
              text-align: left; 
            }
            th { 
              background-color: ${i.PRIMARY_BLUE}; 
              color: white;
              font-weight: 600;
            }
            tr:nth-child(even) { 
              background-color: #f9f9f9; 
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin: 20px 0;
            }
            .stat-card {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid ${i.SECONDARY_BLUE};
            }
            .stat-title {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: ${i.PRIMARY_BLUE};
            }
            .footer { 
              margin-top: 40px; 
              font-size: 12px; 
              color: #888; 
              text-align: center; 
              border-top: 1px solid #eee;
              padding-top: 15px;
            }
            .section {
              margin: 25px 0;
            }
            .section-title {
              font-size: 18px;
              color: ${i.PRIMARY_BLUE};
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              button { display: none !important; }
              .no-print { display: none !important; }
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Rapport ${(p==null?void 0:p.label)||e.type} #${e.id}</h1>
              <p style="margin: 5px 0; color: #666;">
                ${P(e.date).format("DD MMMM YYYY")} | 
                Généré le ${P(e.generatedAt).format("DD/MM/YYYY à HH:mm")}
              </p>
            </div>
            <div class="logo">SOMIPEM</div>
          </div>
          ${d}
          <div class="footer">
            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>
            <p>Généré par: ${e.generatedBy||(r==null?void 0:r.name)||"Système"} | ${P().format("DD/MM/YYYY à HH:mm")}</p>
          </div>
        </body>
      </html>
    `),a.document.close(),setTimeout(()=>{a.print()},500)},[r==null?void 0:r.name]),Yt=l.useCallback(e=>{var d,p,v,S,w,K,A,M,me,Je,Ze,et;const a=ce.find(ne=>ne.key===e.type);switch(e.type){case"production":return`
          <div class="section">
            <h2 class="section-title">Résumé de Production</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Production Totale</div>
                <div class="stat-value">${W(((d=e.production)==null?void 0:d.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Taux de Performance</div>
                <div class="stat-value">${fe((((p=e.production)==null?void 0:p.performance)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Qualité</div>
                <div class="stat-value">${fe((((v=e.quality)==null?void 0:v.rate)||0)/100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Rejets</div>
                <div class="stat-value">${W(((S=e.quality)==null?void 0:S.rejects)||0)} unités</div>
              </div>
            </div>
          </div>
          ${e.machineData?`
            <div class="section">
              <h2 class="section-title">Performance par Machine</h2>
              <table>
                <thead>
                  <tr>
                    <th>Machine</th>
                    <th>Production</th>
                    <th>Performance</th>
                    <th>Disponibilité</th>
                    <th>Rejets</th>
                  </tr>
                </thead>
                <tbody>
                  ${e.machineData.map(ne=>`
                    <tr>
                      <td>${ne.name}</td>
                      <td>${W(ne.production)} unités</td>
                      <td>${fe(ne.performance/100)}</td>
                      <td>${fe(ne.availability/100)}</td>
                      <td>${W(ne.rejects)} unités</td>
                    </tr>
                  `).join("")}
                </tbody>
              </table>
            </div>
          `:""}
        `;case"arrets":return`
          <div class="section">
            <h2 class="section-title">Analyse des Arrêts</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Total Arrêts</div>
                <div class="stat-value">${W(((w=e.arrets)==null?void 0:w.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Durée Totale</div>
                <div class="stat-value">${Ye((((K=e.arrets)==null?void 0:K.totalDuration)||0)/60,1)} heures</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">MTTR Moyen</div>
                <div class="stat-value">${Ye(((A=e.arrets)==null?void 0:A.averageMTTR)||0,1)} min</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Disponibilité</div>
                <div class="stat-value">${fe((((M=e.arrets)==null?void 0:M.availability)||0)/100)}</div>
              </div>
            </div>
          </div>
        `;case"shift":return`
          <div class="section">
            <h2 class="section-title">Rapport d'Équipe</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Équipe</div>
                <div class="stat-value">${e.shift||"N/A"}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Production</div>
                <div class="stat-value">${W(((me=e.production)==null?void 0:me.total)||0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Alertes</div>
                <div class="stat-value">${W(((Je=e.alerts)==null?void 0:Je.total)||0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Machines Actives</div>
                <div class="stat-value">${W(((Ze=e.production)==null?void 0:Ze.activeMachines)||0)}</div>
              </div>
            </div>
          </div>
        `;default:return`
          <div class="section">
            <h2 class="section-title">Détails du Rapport</h2>
            <p>Type: ${(a==null?void 0:a.label)||e.type}</p>
            <p>Période: ${P(e.startDate).format("DD/MM/YYYY")} - ${P(e.endDate).format("DD/MM/YYYY")}</p>
            <p>Statut: ${((et=Re[e.status])==null?void 0:et.text)||e.status}</p>
          </div>
        `}},[]),Ot=l.useCallback(()=>[{title:"ID",dataIndex:"id",key:"id",width:100,render:e=>t.createElement(j,{code:!0,style:{color:i.PRIMARY_BLUE}},"#",e),sorter:(e,a)=>e.id-a.id},{title:"Type",dataIndex:"type",key:"type",width:150,render:e=>{const a=ce.find(d=>d.key===e);return t.createElement(J,{icon:a==null?void 0:a.icon,color:(a==null?void 0:a.color)||i.LIGHT_GRAY,style:{borderRadius:"4px"}},(a==null?void 0:a.label)||e)},filters:ce.map(e=>({text:e.label,value:e.key})),onFilter:(e,a)=>a.type===e},{title:"Période",dataIndex:"date",key:"date",width:180,render:(e,a)=>t.createElement("div",null,t.createElement("div",{style:{fontWeight:500}},P(e).format("DD/MM/YYYY")),a.endDate&&a.endDate!==e&&t.createElement(j,{type:"secondary",style:{fontSize:"12px"}},"au ",P(a.endDate).format("DD/MM/YYYY"))),sorter:(e,a)=>new Date(e.date)-new Date(a.date),defaultSortOrder:"descend"},{title:"Statut",dataIndex:"status",key:"status",width:120,render:e=>{const a=Re[e]||{color:"default",text:e};return t.createElement(J,{color:a.color,style:{borderRadius:"4px"}},a.text)},filters:Object.keys(Re).map(e=>({text:Re[e].text,value:e})),onFilter:(e,a)=>a.status===e},{title:"Généré le",dataIndex:"generatedAt",key:"generatedAt",width:160,render:e=>t.createElement("div",null,t.createElement("div",null,P(e).format("DD/MM/YYYY")),t.createElement(j,{type:"secondary",style:{fontSize:"12px"}},P(e).format("HH:mm"))),responsive:["md"],sorter:(e,a)=>new Date(e.generatedAt)-new Date(a.generatedAt)},{title:"Généré par",dataIndex:"generatedBy",key:"generatedBy",width:140,render:e=>t.createElement(j,{style:{color:i.DARK_GRAY}},e||"Système"),responsive:["lg"]},{title:"Taille",dataIndex:"size",key:"size",width:100,render:e=>t.createElement(j,{type:"secondary"},e?`${Ye(e/1024,1)} KB`:"N/A"),responsive:["xl"],sorter:(e,a)=>(e.size||0)-(a.size||0)},{title:"Actions",key:"actions",width:160,fixed:"right",render:(e,a)=>t.createElement(z,{size:"small"},t.createElement(we,{title:"Voir le rapport"},t.createElement($,{type:"text",icon:t.createElement(it,null),onClick:()=>Ve(a),style:{color:i.PRIMARY_BLUE}})),t.createElement(Ft,{menu:{items:pt.map(d=>({key:d.key,icon:d.icon,label:t.createElement(z,null,d.label,t.createElement(j,{type:"secondary",style:{fontSize:"11px"}},d.description)),onClick:()=>Ke(a,d.key),disabled:a.status!=="completed"}))},trigger:["click"],disabled:a.status!=="completed"},t.createElement(we,{title:a.status==="completed"?"Exporter":"Rapport non terminé"},t.createElement($,{type:"text",icon:t.createElement(st,null),loading:Ee,disabled:a.status!=="completed",style:{color:a.status==="completed"?i.SECONDARY_BLUE:i.LIGHT_GRAY}}))),t.createElement(we,{title:"Imprimer"},t.createElement($,{type:"text",icon:t.createElement(Sr,null),onClick:()=>Qe(a),disabled:a.status!=="completed",style:{color:a.status==="completed"?i.DARK_GRAY:i.LIGHT_GRAY}})))}],[Ve,Ke,Qe,Ee]);if(be&&q){const e=be.includes("Paramètres invalides")||be.includes("invalides");return t.createElement("div",{style:{padding:"24px"}},t.createElement(Zt,{status:"error",title:"Erreur de chargement des rapports",subTitle:be,extra:[t.createElement($,{key:"retry",type:"primary",onClick:()=>{ie(null),Z(!0),te()}},"Réessayer"),e&&t.createElement($,{key:"reset",onClick:()=>{b(null),D([]),Y([]),O(""),x([P().subtract(7,"days"),P()]),ie(null),Z(!0)}},"Réinitialiser les filtres"),t.createElement($,{key:"reload",onClick:()=>window.location.reload()},"Recharger la page")].filter(Boolean)}))}return q?t.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh",flexDirection:"column",gap:"16px"}},t.createElement(Gt,{size:"large"}),t.createElement(j,{style:{color:i.DARK_GRAY}},"Chargement des rapports...")):t.createElement("div",{className:"reports-page",style:{padding:s?"16px":"24px"}},t.createElement(xe,{title:t.createElement(z,null,t.createElement(Le,{style:{color:i.PRIMARY_BLUE}}),t.createElement(Or,{level:4,style:{margin:0,color:i.PRIMARY_BLUE}},"Rapports de Production")),extra:t.createElement(z,null,f==="shift"&&t.createElement(z,null,t.createElement(j,{style:{fontSize:"12px",color:i.LIGHT_GRAY}},"Format:"),t.createElement($.Group,{size:"small"},t.createElement($,{type:Q?"default":"primary",onClick:()=>Ne(!1),style:{backgroundColor:Q?"transparent":i.PRIMARY_BLUE,borderColor:i.PRIMARY_BLUE,color:Q?i.PRIMARY_BLUE:"white"}},"Standard"),t.createElement($,{type:Q?"primary":"default",onClick:()=>Ne(!0),style:{backgroundColor:Q?i.SECONDARY_BLUE:"transparent",borderColor:i.SECONDARY_BLUE,color:Q?"white":i.SECONDARY_BLUE}},"Amélioré"))),t.createElement($,{icon:t.createElement(nt,null),type:"primary",onClick:()=>Xe(),disabled:f==="shift"&&!T.canCreate,style:{backgroundColor:f==="shift"&&!T.canCreate?"#d9d9d9":i.PRIMARY_BLUE,borderColor:f==="shift"&&!T.canCreate?"#d9d9d9":i.PRIMARY_BLUE},title:f==="shift"&&!T.canCreate?T.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Nouveau Rapport"),t.createElement($,{icon:t.createElement(er,null),onClick:te,loading:L},"Actualiser")),style:{background:o?"#141414":"#fff",boxShadow:o?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},t.createElement(qe,{items:[{title:"Accueil"},{title:"Rapports"},{title:((We=ce.find(e=>e.key===f))==null?void 0:We.label)||"Tous les rapports"}],style:{marginBottom:16}}),t.createElement(ht,{gutter:[16,16]},t.createElement(oe,{xs:24,md:6,lg:5,xl:4},t.createElement(xe,{title:t.createElement(z,null,t.createElement(gt,{style:{color:i.SECONDARY_BLUE}}),t.createElement(j,{strong:!0},"Types de rapports")),size:"small",bodyStyle:{padding:0},style:{marginBottom:s?16:0}},t.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"4px",padding:"8px"}},ce.map(e=>t.createElement("div",{key:e.key,onClick:()=>wt(e.key),style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"12px 8px",borderRadius:"6px",cursor:"pointer",backgroundColor:f===e.key?i.HOVER_BLUE:"transparent",border:f===e.key?`1px solid ${i.PRIMARY_BLUE}`:"1px solid transparent",transition:"all 0.2s ease"},onMouseEnter:a=>{f!==e.key&&(a.currentTarget.style.backgroundColor="#f8f9fa")},onMouseLeave:a=>{f!==e.key&&(a.currentTarget.style.backgroundColor="transparent")}},t.createElement("span",{style:{color:e.color,fontSize:"16px",marginTop:"2px"}},e.icon),t.createElement("div",{style:{flex:1}},t.createElement("div",{style:{fontWeight:500,color:f===e.key?i.PRIMARY_BLUE:i.DARK_GRAY,fontSize:"14px",marginBottom:"2px",lineHeight:"1.3"}},e.label),t.createElement("div",{style:{fontSize:"11px",color:i.LIGHT_GRAY,lineHeight:"1.2"}},e.description))))))),t.createElement(oe,{xs:24,md:18,lg:19,xl:20},t.createElement(Rt,{activeReportType:f,dateRange:u,selectedShift:h,selectedMachines:C,selectedModels:R,searchText:_,machines:m,models:X,shifts:kr,onReportTypeChange:g,onDateRangeChange:Ct,onShiftChange:St,onMachineChange:At,onModelChange:It,onSearchChange:Mt,onClearFilters:Pt,machinesLoading:Ie,modelsLoading:ge,existingReports:Pe,onCheckReportExists:ue}),t.createElement(xe,{title:t.createElement(z,null,t.createElement(Le,{style:{color:i.SECONDARY_BLUE}}),t.createElement(j,{strong:!0},"Rapports disponibles"),t.createElement(Vt,{count:ee.total,style:{backgroundColor:i.PRIMARY_BLUE}})),extra:G&&t.createElement(z,null,t.createElement(tr,{spin:!0}),t.createElement(j,{type:"secondary"},"Actualisation automatique..."))},t.createElement(Kt,{columns:Ot(),dataSource:U,rowKey:"id",loading:L,pagination:{...ee,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:(e,a)=>`${a[0]}-${a[1]} sur ${W(e)} rapports`},onChange:Dt,locale:{emptyText:t.createElement(ot,{image:ot.PRESENTED_IMAGE_SIMPLE,description:"Aucun rapport trouvé",style:{color:i.LIGHT_GRAY}},t.createElement($,{type:"primary",icon:t.createElement(nt,null),onClick:()=>Xe(),disabled:f==="shift"&&!T.canCreate,style:{backgroundColor:f==="shift"&&!T.canCreate?"#d9d9d9":i.PRIMARY_BLUE,borderColor:f==="shift"&&!T.canCreate?"#d9d9d9":i.PRIMARY_BLUE},title:f==="shift"&&!T.canCreate?T.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine":""},"Générer ",f==="shift"&&Q?"Rapport Amélioré":"Rapport"))},scroll:{x:1200},size:"middle"}))))),t.createElement(Xt,{title:"Génération du rapport",open:Me,footer:null,closable:!1,centered:!0},t.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},t.createElement(rr,{type:"circle",percent:xt,strokeColor:i.PRIMARY_BLUE}),t.createElement("div",{style:{marginTop:16}},t.createElement(j,null,"Génération en cours...")))))};export{sa as default};
