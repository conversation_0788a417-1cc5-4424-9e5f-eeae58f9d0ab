/**
 * Utility functions for data handling
 */

/**
 * Normalizes percentage values (converts from 0-1 to 0-100 if needed)
 * @param {number|string} value - The value to normalize
 * @returns {number} Normalized percentage value
 */
export const normalizePercentage = (value) => {
  const num = parseFloat(value);
  if (isNaN(num)) return 0;
  // If value is between 0-1, multiply by 100 to get percentage
  return num <= 1 && num > 0 ? num * 100 : num;
};

/**
 * Formats a date string to a more readable format
 * @param {string} dateStr - Date string in YYYY-MM-DD format
 * @returns {string} Formatted date string
 */
export const formatDate = (dateStr) => {
  if (!dateStr) return '';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString();
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateStr;
  }
};

/**
 * Transforms raw data from API to a consistent format
 * @param {Object} item - Raw data item
 * @returns {Object} Transformed data item
 */
export const transformData = (item) => {
  const normalize = (val) => {
    const num = parseFloat(val);
    if (isNaN(num)) return 0;
    return num <= 1 && num > 0 ? num * 100 : num;
  };

  return {
    date: item.Date_Insert_Day,
    Machine_Name: item.Machine_Name || 'N/A',
    Shift: item.Shift || 'N/A',
    good: parseFloat(item.Good_QTY_Day) || 0,
    reject: parseFloat(item.Rejects_QTY_Day) || 0,
    oee: normalize(item.OEE_Day),
    speed: parseFloat(item.Speed_Day) || 0,
    run_hours: parseFloat(item.Run_Hours_Day) || 0,
    down_hours: parseFloat(item.Down_Hours_Day) || 0,
    availability: normalize(item.Availability_Rate_Day),
    performance: normalize(item.Performance_Rate_Day),
    quality: normalize(item.Quality_Rate_Day),
    mould_number: item.Part_Number || 'N/A',
    poid_unitaire: item.Poid_Unitaire || 'N/A',
    cycle_theorique: item.Cycle_Theorique || 'N/A',
    poid_purge: item.Poid_Purge || 'N/A'
  };
};
