import React from 'react';
import { Card, Row, Col, Space, Badge } from 'antd';
import { FundOutlined, DatabaseOutlined, ExperimentOutlined, LineChartOutlined } from '@ant-design/icons';

const WeightAnalyticsSection = ({ loading, filters }) => {
  const weightFeatures = [
    {
      title: "Weight Pattern ML",
      description: "Machine learning weight pattern analysis",
      icon: <DatabaseOutlined />,
      color: "#fa8c16"
    },
    {
      title: "Variance Prediction",
      description: "AI-powered weight variance prediction",
      icon: <LineChartOutlined />,
      color: "#52c41a"
    },
    {
      title: "Quality Correlation",
      description: "Weight-quality correlation analysis",
      icon: <ExperimentOutlined />,
      color: "#722ed1"
    },
    {
      title: "Optimization Engine",
      description: "Weight optimization recommendations",
      icon: <FundOutlined />,
      color: "#1890ff"
    }
  ];

  return (
    <div style={{
      background: 'linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px'
    }}>
      {/* Section Header */}
      <div style={{
        background: 'linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '24px',
        color: 'white'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space align="center" size="large">
              <div style={{
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '10px',
                padding: '10px'
              }}>
                <FundOutlined style={{ fontSize: '24px' }} />
              </div>
              <div>
                <h2 style={{ color: 'white', margin: 0, fontSize: '24px' }}>
                  Weight Analytics Intelligence
                </h2>
                <p style={{ color: 'rgba(255,255,255,0.9)', margin: 0, fontSize: '14px' }}>
                  Advanced weight analysis and optimization using machine learning
                </p>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Badge count="ML Powered" style={{ backgroundColor: '#faad14' }} />
              <Badge count="Precision+" style={{ backgroundColor: '#13c2c2' }} />
            </Space>
          </Col>
        </Row>
      </div>

      {/* Feature Cards */}
      <Row gutter={[24, 24]}>
        {weightFeatures.map((feature, index) => (
          <Col xs={24} md={12} key={index}>
            <Card
              style={{
                height: '200px',
                borderRadius: '16px',
                border: 'none',
                background: `linear-gradient(135deg, ${feature.color}10 0%, ${feature.color}05 100%)`,
                boxShadow: '0 8px 24px rgba(0,0,0,0.06)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              bodyStyle={{ 
                height: '100%', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                textAlign: 'center'
              }}
            >
              <Space direction="vertical" align="center" size="large">
                <div style={{
                  background: feature.color,
                  borderRadius: '50%',
                  width: '60px',
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '24px'
                }}>
                  {feature.icon}
                </div>
                <div>
                  <h3 style={{ margin: 0, color: feature.color }}>
                    {feature.title}
                  </h3>
                  <p style={{ margin: '8px 0 0 0', color: '#8c8c8c' }}>
                    {feature.description}
                  </p>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default WeightAnalyticsSection;
