import db from '../db.js';
import { parsePermissions, hasPermission, getUserPermissions } from '../utils/permissionUtils.js';
import { getAllRolePermissions, ROLE_HIERARCHY } from '../utils/roleHierarchy.js';

/**
 * Middleware to check if user has required permissions
 * @param {string|string[]} requiredPermissions - Permission(s) required to access the route
 * @param {Object} options - Additional options
 * @param {boolean} options.requireAll - If true, user must have all required permissions
 * @returns {function} Express middleware function
 */
const checkPermission = (requiredPermissions, options = {}) => {
  return async (req, res, next) => {
    try {
      // If no permissions required, proceed
      if (!requiredPermissions ||
         (Array.isArray(requiredPermissions) && requiredPermissions.length === 0)) {
        return next();
      }

      // Parse required permissions
      const permissions = parsePermissions(requiredPermissions);

      // Get requireAll option
      const { requireAll = false } = options;

      // Get user's role with permissions and role name
      const [roleResults] = await db.execute(
        'SELECT r.name as role_name, r.permissions FROM users u JOIN roles r ON u.role_id = r.id WHERE u.id = ?',
        [req.user.id]
      );

      if (!roleResults || roleResults.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Unauthorized: User role not found'
        });
      }

      // Get user's role name
      const roleName = roleResults[0].role_name;

      // Parse permissions from JSON
      let rolePermissionsFromDb = [];
      try {
        // Handle case where permissions might be already a JSON object
        if (typeof roleResults[0].permissions === 'object' && roleResults[0].permissions !== null) {
          rolePermissionsFromDb = roleResults[0].permissions;
        } else {
          rolePermissionsFromDb = JSON.parse(roleResults[0].permissions || '[]');
        }
      } catch (e) {
        // Handle case where permissions is stored as a string instead of JSON
        if (typeof roleResults[0].permissions === 'string') {
          rolePermissionsFromDb = roleResults[0].permissions.split(',').map(p => p.trim());
        }
      }

      // Get user's individual permissions and department
      const [userResults] = await db.execute(
        'SELECT permissions, department_id FROM users WHERE id = ?',
        [req.user.id]
      );

      // Parse user permissions from JSON
      let userPermissions = [];
      let departmentId = null;
      
      if (userResults && userResults.length > 0) {
        try {
          // Handle case where permissions might be already a JSON object
          if (userResults[0]?.permissions && typeof userResults[0].permissions === 'object' && userResults[0].permissions !== null) {
            userPermissions = userResults[0].permissions;
          } else {
            userPermissions = JSON.parse(userResults[0]?.permissions || '[]');
          }
        } catch (e) {
          // Handle case where permissions is stored as a string instead of JSON
          if (userResults[0]?.permissions && typeof userResults[0].permissions === 'string') {
            userPermissions = userResults[0].permissions.split(',').map(p => p.trim());
          }
        }

        // Get user's department
        departmentId = userResults[0]?.department_id;
      }

      // Get department name if department_id exists
      let departmentName = null;
      if (departmentId) {
        const [deptResults] = await db.execute(
          'SELECT name FROM departments WHERE id = ?',
          [departmentId]
        );

        if (deptResults && deptResults.length > 0) {
          departmentName = deptResults[0].name;
        }
      }

      // Get inherited permissions from role hierarchy
      let hierarchyPermissions = [];
      if (roleName && ROLE_HIERARCHY[roleName]) {
        hierarchyPermissions = getAllRolePermissions(roleName);
      }

      // Ensure all permission arrays are properly initialized
      const safeUserPermissions = Array.isArray(userPermissions) ? userPermissions : [];
      const safeRolePermissions = Array.isArray(rolePermissionsFromDb) ? rolePermissionsFromDb : [];
      const safeHierarchyPermissions = Array.isArray(hierarchyPermissions) ? hierarchyPermissions : [];

      // Combine all permissions: direct user permissions, role permissions from DB, and hierarchy permissions
      const allPermissions = [
        ...safeUserPermissions,
        ...safeRolePermissions,
        ...safeHierarchyPermissions
      ].filter(Boolean); // Remove null/undefined values

      // Add department context to request for later use
      req.userContext = {
        departmentId,
        departmentName,
        roleName,
        permissions: allPermissions
      };

      // Check if user has system:admin permission which grants all access
      if (allPermissions.includes('system:admin') || allPermissions.includes('admin')) {
        return next();
      }

      // Check for system:view_all_departments permission for department-related routes
      const viewAllDepts = allPermissions.includes('system:view_all_departments') ||
                          allPermissions.includes('view_all_departments');

      if (viewAllDepts && permissions.some(p => p.includes('view_'))) {
        return next();
      }

      // Check if user has the required permissions
      if (hasPermission(allPermissions, permissions, requireAll)) {
        return next();
      }

      // Special case for department-specific permissions
      // If the permission is department-specific, check if user has access to that department
      if (departmentName) {
        const departmentSpecificPermission = permissions.some(p => {
          const [namespace] = p.split(':');
          // Convert department name to lowercase for comparison
          return namespace.toLowerCase() === departmentName.toLowerCase();
        });

        if (departmentSpecificPermission) {
          return next();
        }
      }

      return res.status(403).json({
        success: false,
        message: 'Unauthorized: Insufficient permissions'
      });
    } catch (error) {
      console.error('Permission middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error checking permissions'
      });
    }
  };
};

/**
 * Middleware to filter data by user's department
 * @param {string} tableField - The database field name that contains department_id
 * @returns {function} Express middleware function
 */
const filterByDepartment = (tableField = 'department_id') => {
  return async (req, res, next) => {
    try {
      // If userContext is already set by checkPermission middleware, use it
      if (req.userContext) {
        const { departmentId, permissions, roleName } = req.userContext;

        // Check if user has admin permission or can view all departments
        if (permissions.includes('system:admin') ||
            permissions.includes('admin') ||
            permissions.includes('system:view_all_departments') ||
            permissions.includes('view_all_departments')) {
          // No filtering needed
          return next();
        }

        // Check if user's role is head_manager or higher in the hierarchy
        if (roleName && ROLE_HIERARCHY[roleName] && ROLE_HIERARCHY[roleName].level >= ROLE_HIERARCHY['head_manager'].level) {
          // No filtering needed for head manager or higher
          return next();
        }

        // Get user's accessible departments (own department + additional access)
        const [accessResults] = await db.execute(
          'SELECT department_id FROM department_access WHERE user_id = ?',
          [req.user.id]
        );

        // Create array of accessible department IDs
        const departmentIds = departmentId ? [departmentId] : [];
        if (accessResults && accessResults.length > 0) {
          accessResults.forEach(access => {
            if (access.department_id) {
              departmentIds.push(access.department_id);
            }
          });
        }

        // Add department filter to request for controllers to use
        req.departmentFilter = {
          field: tableField,
          departments: departmentIds.filter(Boolean) // Remove null/undefined values
        };

        return next();
      }

      // If userContext is not set, fall back to original implementation
      // Get user's role and department
      const [userResults] = await db.execute(
        'SELECT u.department_id, r.name as role_name, r.permissions FROM users u ' +
        'LEFT JOIN roles r ON u.role_id = r.id ' +
        'WHERE u.id = ?',
        [req.user.id]
      );

      if (!userResults || userResults.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Unauthorized: User not found'
        });
      }

      if (userResults.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Unauthorized: User not found'
        });
      }

      const user = userResults[0];
      const roleName = user.role_name;

      // Parse permissions from JSON
      let rolePermissions = [];
      try {
        rolePermissions = JSON.parse(user.permissions || '[]');
      } catch (e) {
        // Handle case where permissions is stored as a string instead of JSON
        if (typeof user.permissions === 'string') {
          rolePermissions = user.permissions.split(',').map(p => p.trim());
        }
      }

      // Get inherited permissions from role hierarchy
      let hierarchyPermissions = [];
      if (roleName && ROLE_HIERARCHY[roleName]) {
        hierarchyPermissions = getAllRolePermissions(roleName);
      }

      // Ensure all permission arrays are properly initialized
      const safeRolePermissions = Array.isArray(rolePermissions) ? rolePermissions : [];
      const safeHierarchyPermissions = Array.isArray(hierarchyPermissions) ? hierarchyPermissions : [];

      // Combine permissions
      const allPermissions = [...safeRolePermissions, ...safeHierarchyPermissions];

      // Check if user has admin permission or can view all departments
      if (allPermissions.includes('system:admin') ||
          allPermissions.includes('admin') ||
          allPermissions.includes('system:view_all_departments') ||
          allPermissions.includes('view_all_departments')) {
        // No filtering needed
        return next();
      }

      // Check if user's role is head_manager or higher in the hierarchy
      if (roleName && ROLE_HIERARCHY[roleName] && ROLE_HIERARCHY[roleName].level >= ROLE_HIERARCHY['head_manager'].level) {
        // No filtering needed for head manager or higher
        return next();
      }

      // Get user's accessible departments (own department + additional access)
      const [accessResults] = await db.execute(
        'SELECT department_id FROM department_access WHERE user_id = ?',
        [req.user.id]
      );

      // Create array of accessible department IDs
      const departmentIds = user.department_id ? [user.department_id] : [];
      if (accessResults && accessResults.length > 0) {
        accessResults.forEach(access => {
          if (access.department_id) {
            departmentIds.push(access.department_id);
          }
        });
      }

      // Add department filter to request for controllers to use
      req.departmentFilter = {
        field: tableField,
        departments: departmentIds.filter(Boolean) // Remove null/undefined values
      };

      next();
    } catch (error) {
      console.error('Department filter middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error applying department filter'
      });
    }
  };
};

export { checkPermission, filterByDepartment };