import React, { useState, useEffect } from 'react';
import { Select, Space, Button, Tag, Alert } from 'antd';
import { useProduction } from '../context/ProductionContext';

const { Option } = Select;

/**
 * Minimal Filter Test Component
 * Connected to ProductionContext for real filter testing
 * Used to isolate and debug filter selection issues
 */
const MinimalFilterTest = () => {
  // Connect to the actual ProductionContext
  const {
    selectedMachineModel,
    selectedMachine,
    machineModels,
    filteredMachineNames,
    handleMachineModelChange,
    handleMachineChange,
    resetFilters,
    loading
  } = useProduction();

  // Local state for testing coordination
  const [testCoordination, setTestCoordination] = useState('WAITING');
  const [lastUpdate, setLastUpdate] = useState(null);

  // Monitor context changes for coordination testing
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    setLastUpdate(timestamp);
    
    if (selectedMachineModel || selectedMachine) {
      setTestCoordination('ACTIVE');
      console.log('🧪 [TEST COORDINATION] Context updated:', {
        model: selectedMachineModel,
        machine: selectedMachine,
        timestamp
      });
    } else {
      setTestCoordination('IDLE');
      console.log('🧪 [TEST COORDINATION] Context cleared:', { timestamp });
    }
  }, [selectedMachineModel, selectedMachine]);

  const handleTestModelChange = (value) => {
    console.log('🧪 [TEST] Model change requested:', value);
    console.log('🧪 [TEST] Calling context handleMachineModelChange');
    setTestCoordination('COORDINATING');
    handleMachineModelChange(value);
  };

  const handleTestMachineChange = (value) => {
    console.log('🧪 [TEST] Machine change requested:', value);
    console.log('🧪 [TEST] Calling context handleMachineChange');
    setTestCoordination('COORDINATING');
    handleMachineChange(value);
  };

  const resetTest = () => {
    console.log('🧪 [TEST] Reset requested');
    console.log('🧪 [TEST] Calling context resetFilters');
    setTestCoordination('RESETTING');
    resetFilters();
  };

  return (
    <div style={{ 
      padding: '16px', 
      border: '2px solid #1890ff', 
      borderRadius: '8px',
      backgroundColor: '#f8f9fa',
      margin: '16px 0' 
    }}>
      <div style={{ marginBottom: '12px' }}>
        <strong style={{ color: '#1890ff' }}>🧪 Filter Coordination Test</strong>
        <Tag 
          color={
            testCoordination === 'ACTIVE' ? 'green' :
            testCoordination === 'COORDINATING' ? 'orange' :
            testCoordination === 'RESETTING' ? 'red' : 'default'
          }
          style={{ marginLeft: '8px' }}
        >
          {testCoordination}
        </Tag>
        {lastUpdate && (
          <span style={{ fontSize: '12px', marginLeft: '8px', color: '#666' }}>
            Last update: {lastUpdate}
          </span>
        )}
      </div>
      
      <div style={{ marginBottom: '12px' }}>
        <strong>Context State:</strong>
        <br />
        <span style={{ fontSize: '12px' }}>
          Model: <code>{selectedMachineModel || 'None'}</code>
          <br />
          Machine: <code>{selectedMachine || 'None'}</code>
          <br />
          Available Models: <code>{machineModels?.length || 0}</code>
          <br />
          Filtered Machines: <code>{filteredMachineNames?.length || 0}</code>
          <br />
          Loading: <code>{loading ? 'Yes' : 'No'}</code>
        </span>
      </div>

      {testCoordination === 'WAITING' && (
        <Alert
          message="Waiting for filter interaction..."
          type="info"
          size="small"
          style={{ marginBottom: '12px' }}
        />
      )}

      <Space wrap>
        <Select
          placeholder="Select Model"
          style={{ width: 150 }}
          value={selectedMachineModel || undefined}
          onChange={handleTestModelChange}
          allowClear
          loading={loading}
        >
          {(machineModels || []).map((model) => (
            <Option key={model} value={model}>
              {model}
            </Option>
          ))}
        </Select>

        <Select
          placeholder="Select Machine"
          style={{ width: 150 }}
          value={selectedMachine || undefined}
          onChange={handleTestMachineChange}
          disabled={!selectedMachineModel || !filteredMachineNames?.length}
          allowClear
          loading={loading}
        >
          {(filteredMachineNames || []).map((machine) => (
            <Option key={machine.Machine_Name} value={machine.Machine_Name}>
              {machine.Machine_Name}
            </Option>
          ))}
        </Select>

        <Button onClick={resetTest} disabled={loading}>
          Reset Test
        </Button>
      </Space>

      {testCoordination === 'ACTIVE' && (
        <Alert
          message="✅ Coordination working - filters are connected to context!"
          type="success"
          size="small"
          style={{ marginTop: '12px' }}
        />
      )}
    </div>
  );
};

export default MinimalFilterTest;
