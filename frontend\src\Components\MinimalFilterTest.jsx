import React, { useState } from 'react';
import { Select, Space, Button } from 'antd';

const { Option } = Select;

/**
 * Minimal Filter Test Component
 * Used to isolate and debug filter selection issues
 */
const MinimalFilterTest = () => {
  const [selectedModel, setSelectedModel] = useState('');
  const [selectedMachine, setSelectedMachine] = useState('');

  const machineModels = ['IPS', 'CCM24'];
  const machineNames = [
    { Machine_Name: 'IPS01' },
    { Machine_Name: 'IPS02' },
    { Machine_Name: 'IPS03' },
    { Machine_Name: 'IPS04' }
  ];

  const handleModelChange = (value) => {
    console.log('🧪 [TEST] Model changed to:', value);
    setSelectedModel(value);
  };

  const handleMachineChange = (value) => {
    console.log('🧪 [TEST] Machine changed to:', value);
    setSelectedMachine(value);
  };

  const resetTest = () => {
    console.log('🧪 [TEST] Resetting filters');
    setSelectedModel('');
    setSelectedMachine('');
  };

  return (
    <div style={{ padding: '20px', border: '2px solid #1890ff', margin: '20px' }}>
      <h3>🧪 Minimal Filter Test</h3>
      
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <strong>Current State:</strong>
          <br />
          Model: {selectedModel || 'None'}
          <br />
          Machine: {selectedMachine || 'None'}
        </div>

        <Space>
          <Select
            placeholder="Select Model"
            style={{ width: 150 }}
            value={selectedModel || undefined}
            onChange={handleModelChange}
            allowClear
          >
            {machineModels.map((model) => (
              <Option key={model} value={model}>
                {model}
              </Option>
            ))}
          </Select>

          <Select
            placeholder="Select Machine"
            style={{ width: 150 }}
            value={selectedMachine || undefined}
            onChange={handleMachineChange}
            disabled={!selectedModel}
            allowClear
          >
            {machineNames
              .filter(machine => machine.Machine_Name.startsWith(selectedModel))
              .map((machine) => (
                <Option key={machine.Machine_Name} value={machine.Machine_Name}>
                  {machine.Machine_Name}
                </Option>
              ))}
          </Select>

          <Button onClick={resetTest}>Reset</Button>
        </Space>
      </Space>
    </div>
  );
};

export default MinimalFilterTest;
