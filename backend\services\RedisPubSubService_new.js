/**
 * Redis Pub/Sub Service for Real-time WebSocket Optimization
 * Replaces polling mechanism with event-driven updates
 * 
 * Performance Targets:
 * - 70% reduction in WebSocket latency
 * - Event-driven updates instead of 3-second polling
 * - Efficient multi-client state synchronization
 */

import Redis from 'ioredis';
import redisConfig from '../config/redisConfig.js';

class RedisPubSubService {
  constructor() {
    this.publisher = null;
    this.subscriber = null;
    this.initialized = false;
    this.subscriptions = new Map();
    this.metrics = {
      messagesPublished: 0,
      messagesReceived: 0,
      activeSubscriptions: 0,
      avgPublishTime: 0,
      lastActivity: null
    };

    // Channel names for different data types
    this.CHANNELS = {
      MACHINE_DATA: 'machine:data:updates',
      MACHINE_ALERTS: 'machine:alerts',
      PRODUCTION_UPDATES: 'production:updates',
      SYSTEM_EVENTS: 'system:events',
      STATE_UPDATES: 'state:updates',
      USER_PRESENCE: 'user:presence'
    };
  }

  /**
   * Initialize Redis pub/sub connections with fallback support
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    console.log('🔄 Initializing Redis Pub/Sub Service with fallback support...');

    try {
      // Use the centralized Redis config with fallback
      await redisConfig.initialize();
      
      this.publisher = redisConfig.getPublisher();
      this.subscriber = redisConfig.getSubscriber();

      // Set up error handlers that don't crash the application
      if (this.publisher && this.publisher.on) {
        this.publisher.on('error', (error) => {
          console.log('❌ Redis Publisher error:', error.message);
          // Don't set initialized to false - let fallback handle it
        });

        this.publisher.on('connect', () => {
          console.log('✅ Redis Publisher connected');
        });
      }

      if (this.subscriber && this.subscriber.on) {
        this.subscriber.on('error', (error) => {
          console.log('❌ Redis Subscriber error:', error.message);
          // Don't set initialized to false - let fallback handle it
        });

        this.subscriber.on('connect', () => {
          console.log('✅ Redis Subscriber connected');
        });
      }

      this.initialized = true;
      this.metrics.lastActivity = Date.now();

      if (redisConfig.isInFallbackMode()) {
        console.log('✅ Redis Pub/Sub Service initialized with in-memory fallback');
      } else {
        console.log('✅ Redis Pub/Sub Service initialized with Redis connection');
      }

      return true;

    } catch (error) {
      console.error('❌ Redis Pub/Sub Service initialization failed:', error.message);
      console.log('⚠️ Continuing with fallback mode - pub/sub will use in-memory simulation');
      this.initialized = true; // Mark as initialized even in fallback mode
      return true;
    }
  }

  /**
   * Publish machine data updates through Redis or fallback
   */
  async publishMachineDataUpdate(machineData) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const startTime = Date.now();
      const messageData = {
        type: 'machine_data_update',
        data: machineData,
        timestamp: Date.now(),
        source: 'production_system'
      };

      const message = JSON.stringify(messageData);
      
      if (this.publisher && this.publisher.publish) {
        await this.publisher.publish(this.CHANNELS.MACHINE_DATA, message);
      }

      this.metrics.messagesPublished++;
      this.updateMetrics('publish', Date.now() - startTime);

      const modeIndicator = redisConfig.isInFallbackMode() ? '[FALLBACK]' : '[REDIS]';
      console.log(`📡 ${modeIndicator} Published machine data update for ${machineData.length} machines`);

      return true;
    } catch (error) {
      console.error('❌ Failed to publish machine data update:', error);
      return false;
    }
  }

  /**
   * Publish machine alert through Redis or fallback
   */
  async publishMachineAlert(alertData) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const startTime = Date.now();
      const messageData = {
        type: 'machine_alert',
        data: alertData,
        timestamp: Date.now(),
        severity: alertData.severity || 'medium',
        source: 'alert_system'
      };

      const message = JSON.stringify(messageData);
      
      if (this.publisher && this.publisher.publish) {
        await this.publisher.publish(this.CHANNELS.MACHINE_ALERTS, message);
      }

      this.metrics.messagesPublished++;
      this.updateMetrics('publish', Date.now() - startTime);

      const modeIndicator = redisConfig.isInFallbackMode() ? '[FALLBACK]' : '[REDIS]';
      console.log(`🚨 ${modeIndicator} Published machine alert: ${alertData.type}`);

      return true;
    } catch (error) {
      console.error('❌ Failed to publish machine alert:', error);
      return false;
    }
  }

  /**
   * Subscribe to machine data updates
   */
  async subscribeToMachineData(callback) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const channelName = this.CHANNELS.MACHINE_DATA;
      
      if (this.subscriber && this.subscriber.subscribe) {
        await this.subscriber.subscribe(channelName);
        
        this.subscriber.on('message', (channel, message) => {
          if (channel === channelName) {
            try {
              const parsedMessage = JSON.parse(message);
              this.metrics.messagesReceived++;
              this.updateMetrics('receive');
              callback(parsedMessage);
            } catch (parseError) {
              console.error('❌ Failed to parse machine data message:', parseError);
            }
          }
        });
      }

      this.subscriptions.set(channelName, callback);
      this.metrics.activeSubscriptions = this.subscriptions.size;

      const modeIndicator = redisConfig.isInFallbackMode() ? '[FALLBACK]' : '[REDIS]';
      console.log(`📺 ${modeIndicator} Subscribed to machine data updates`);

      return true;
    } catch (error) {
      console.error('❌ Failed to subscribe to machine data updates:', error);
      return false;
    }
  }

  /**
   * Subscribe to machine alerts
   */
  async subscribeToMachineAlerts(callback) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const channelName = this.CHANNELS.MACHINE_ALERTS;
      
      if (this.subscriber && this.subscriber.subscribe) {
        await this.subscriber.subscribe(channelName);
        
        this.subscriber.on('message', (channel, message) => {
          if (channel === channelName) {
            try {
              const parsedMessage = JSON.parse(message);
              this.metrics.messagesReceived++;
              this.updateMetrics('receive');
              callback(parsedMessage);
            } catch (parseError) {
              console.error('❌ Failed to parse machine alert message:', parseError);
            }
          }
        });
      }

      this.subscriptions.set(channelName, callback);
      this.metrics.activeSubscriptions = this.subscriptions.size;

      const modeIndicator = redisConfig.isInFallbackMode() ? '[FALLBACK]' : '[REDIS]';
      console.log(`🚨 ${modeIndicator} Subscribed to machine alerts`);

      return true;
    } catch (error) {
      console.error('❌ Failed to subscribe to machine alerts:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from a channel
   */
  async unsubscribe(channelName) {
    try {
      if (this.subscriber && this.subscriber.unsubscribe) {
        await this.subscriber.unsubscribe(channelName);
      }
      
      this.subscriptions.delete(channelName);
      this.metrics.activeSubscriptions = this.subscriptions.size;

      const modeIndicator = redisConfig.isInFallbackMode() ? '[FALLBACK]' : '[REDIS]';
      console.log(`📺 ${modeIndicator} Unsubscribed from ${channelName}`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to unsubscribe from ${channelName}:`, error);
      return false;
    }
  }

  /**
   * Update performance metrics
   */
  updateMetrics(type, responseTime = 0) {
    this.metrics.lastActivity = Date.now();
    
    if (type === 'publish' && responseTime > 0) {
      const currentAvg = this.metrics.avgPublishTime;
      const count = this.metrics.messagesPublished;
      this.metrics.avgPublishTime = ((currentAvg * (count - 1)) + responseTime) / count;
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      isConnected: this.initialized,
      fallbackMode: redisConfig.isInFallbackMode(),
      uptime: this.metrics.lastActivity ? Date.now() - this.metrics.lastActivity : 0
    };
  }

  /**
   * Health check for Redis pub/sub connections
   */
  async healthCheck() {
    try {
      if (!this.initialized) {
        return { status: 'unhealthy', reason: 'Not initialized' };
      }

      if (redisConfig.isInFallbackMode()) {
        return { 
          status: 'healthy', 
          mode: 'fallback',
          reason: 'Running in fallback mode'
        };
      }

      if (this.publisher && this.publisher.ping) {
        await this.publisher.ping();
        return { 
          status: 'healthy', 
          mode: 'redis',
          metrics: this.getMetrics()
        };
      }

      return { status: 'unhealthy', reason: 'Publisher not available' };

    } catch (error) {
      return { 
        status: 'unhealthy', 
        reason: error.message,
        fallbackAvailable: true
      };
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      if (this.subscriber && this.subscriber.disconnect) {
        await this.subscriber.disconnect();
      }
      if (this.publisher && this.publisher.disconnect) {
        await this.publisher.disconnect();
      }
      
      this.initialized = false;
      this.subscriptions.clear();
      this.metrics.activeSubscriptions = 0;

      console.log('✅ Redis Pub/Sub Service shutdown completed');
    } catch (error) {
      console.error('❌ Error during Redis Pub/Sub shutdown:', error);
    }
  }

  /**
   * Generic publish method for custom channels
   */
  async publish(channel, data) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const messageData = {
        type: 'custom',
        data: data,
        timestamp: Date.now(),
        channel: channel
      };

      const message = JSON.stringify(messageData);
      
      if (this.publisher && this.publisher.publish) {
        await this.publisher.publish(channel, message);
      }

      this.metrics.messagesPublished++;

      return true;
    } catch (error) {
      console.error(`❌ Failed to publish to ${channel}:`, error);
      return false;
    }
  }

  /**
   * Generic subscribe method for custom channels
   */
  async subscribe(channel, callback) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      if (this.subscriber && this.subscriber.subscribe) {
        await this.subscriber.subscribe(channel);
        
        this.subscriber.on('message', (receivedChannel, message) => {
          if (receivedChannel === channel) {
            try {
              const parsedMessage = JSON.parse(message);
              this.metrics.messagesReceived++;
              callback(parsedMessage);
            } catch (parseError) {
              console.error(`❌ Failed to parse message from ${channel}:`, parseError);
            }
          }
        });
      }

      this.subscriptions.set(channel, callback);
      this.metrics.activeSubscriptions = this.subscriptions.size;

      return true;
    } catch (error) {
      console.error(`❌ Failed to subscribe to ${channel}:`, error);
      return false;
    }
  }

  /**
   * Publish state updates for multi-client synchronization
   */
  async publishStateUpdate(stateType, stateKey, stateData) {
    const channel = `${this.CHANNELS.STATE_UPDATES}:${stateType}`;
    
    const updateData = {
      stateType,
      stateKey,
      stateData,
      timestamp: Date.now()
    };

    return await this.publish(channel, updateData);
  }
}

// Create singleton instance
const redisPubSubService = new RedisPubSubService();

export default redisPubSubService;
