// Dashboard Integration Example - Ready to Use Components

import React from 'react';
import { useProductionAggregation } from '../hooks/useProductionAggregation';
import { useStopsAggregation } from '../hooks/useStopsAggregation';
import { useDashboardOptimizer, useDataSampling } from '../hooks/useDashboardOptimizer';
import { OptimizedStatCard, OptimizedChartWrapper, DashboardMetrics } from './OptimizedComponents';

// Example: Optimized Production Dashboard Integration
export const OptimizedProductionDashboardExample = ({ 
  selectedMachineModel, 
  dateFilter, 
  showDebugPanel = false 
}) => {
  // Use optimized hooks
  const { productionSummary, machinePerformance, topMachines, loading } = useProductionAggregation({
    selectedMachineModel,
    startDate: dateFilter.start
  });  const { activeOptimizations, toggleOptimization } = useDashboardOptimizer();
  const { sampledData: sampledMachines } = useDataSampling(machinePerformance, 100);

  if (loading) {
    return <div>Loading optimized dashboard...</div>;
  }
  return (
    <div className="optimized-production-dashboard">
      {/* Key Metrics with Optimized Components */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <OptimizedStatCard
            title="Good Quantity"
            value={productionSummary?.goodQty || 0}
            suffix="units"
            color="#52c41a"
            loading={loading}
          />
        </Col>
        <Col xs={24} sm={8}>
          <OptimizedStatCard
            title="Reject Quantity"
            value={productionSummary?.rejetQty || 0}
            suffix="units"
            color="#ff4d4f"
            loading={loading}
          />
        </Col>
        <Col xs={24} sm={8}>
          <OptimizedStatCard
            title="Average OEE"
            value={productionSummary?.avgOEE || 0}
            suffix="%"
            precision={1}
            color="#1890ff"
            loading={loading}
          />
        </Col>
      </Row>

      {/* Machine Performance Chart with Data Sampling */}
      <OptimizedChartWrapper
        title="Machine Performance"
        loading={loading}
        dataLength={machinePerformance?.length || 0}
        maxDataPoints={100}
      >
        {/* Your chart component using sampledMachines */}
        <div>Chart with {sampledMachines.length} data points</div>
      </OptimizedChartWrapper>

      {/* Top Machines Table */}
      <Card title="Top Performing Machines" style={{ marginTop: 16 }}>
        {topMachines?.slice(0, 5).map((machine, index) => (
          <div key={index} style={{ padding: '8px 0' }}>
            {machine.Machine_Name}: {machine.totalProduction} units
          </div>
        ))}
      </Card>
    </div>
  );
};

// Example: Optimized Arrets Dashboard Integration
export const OptimizedArretsDashboardExample = ({ 
  selectedMachineModel, 
  dateFilter 
}) => {
  const { stopsSummary, recentStops, loading } = useStopsAggregation({
    selectedMachineModel,
    startDate: dateFilter.start
  });

  return (
    <div className="optimized-arrets-dashboard">
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12}>
          <OptimizedStatCard
            title="Total Stops"
            value={stopsSummary?.totalStops || 0}
            color="#ff4d4f"
            loading={loading}
          />
        </Col>
        <Col xs={24} sm={12}>
          <OptimizedStatCard
            title="Average Duration"
            value={stopsSummary?.avgDuration || 0}
            suffix="min"
            precision={1}
            color="#faad14"
            loading={loading}
          />
        </Col>
      </Row>

      <Card title="Recent Stops" style={{ marginTop: 16 }}>
        {recentStops?.slice(0, 10).map((stop, index) => (
          <div key={index} style={{ padding: '4px 0', borderBottom: '1px solid #f0f0f0' }}>
            <strong>{stop.Machine_Name}</strong>: {stop.Motif_Arret} ({stop.Duree_Arret}min)
          </div>
        ))}
      </Card>
    </div>
  );
};

// Universal Dashboard Performance Monitor
export const UniversalDashboardMonitor = () => {
  const [metrics, setMetrics] = React.useState({});

  React.useEffect(() => {
    const interval = setInterval(() => {
      setMetrics({ summary: {} }); // Placeholder since performanceMonitor is removed
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return <DashboardMetrics metrics={metrics.summary} />;
};

export default {
  OptimizedProductionDashboardExample,
  OptimizedArretsDashboardExample,
  UniversalDashboardMonitor
};
