import React from "react" ;
import request from 'superagent';

// Configuration - Unified Architecture API Detection
const API_BASE_URL = (() => {
  // Check if we're in a browser environment first
  if (typeof window !== 'undefined') {
    const currentOrigin = window.location.origin;

    // If running on ngrok domain, use the same origin (unified architecture)
    if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
      console.log('🔌 SearchService detected ngrok deployment - using same origin:', currentOrigin);
      return `${currentOrigin}/api`;
    }

    // For local development, check environment variable first
    if (import.meta.env.VITE_API_URL) {
      console.log('🔌 SearchService using VITE_API_URL for local development:', import.meta.env.VITE_API_URL);
      return `${import.meta.env.VITE_API_URL}/api`;
    }

    // Fallback to current origin for local development
    console.log('🔌 SearchService using current origin for local development:', currentOrigin);
    return `${currentOrigin}/api`;
  }

  // Fallback for server-side rendering
  return 'http://localhost:5000/api';
})();

class SearchService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/search`;
  }

  // Global search across all data types
  async globalSearch(query, options = {}) {
    try {
      const { page = 1, size = 20 } = options;
      const response = await request.get(`${this.baseURL}/global`)
        .query({ q: query, page, size })
        .set('withCredentials', true)
        .retry(2);
      return response.body;
    } catch (error) {
      console.error('Global search error:', error);
      throw this.handleError(error);
    }
  }

  // Search machine sessions with advanced filtering
  async searchMachineSessions(searchParams = {}) {
    try {
      const response = await request.get(`${this.baseURL}/sessions`)
        .query(searchParams)
        .set('withCredentials', true)
        .retry(2);
      return response.body;
    } catch (error) {
      console.error('Machine sessions search error:', error);
      throw this.handleError(error);
    }
  }

  // Search reports with filtering
  async searchReports(searchParams = {}) {
    try {
      const response = await request.get(`${this.baseURL}/reports`)
        .query(searchParams)
        .set('withCredentials', true)
        .retry(2);
      return response.body;
    } catch (error) {
      console.error('Reports search error:', error);
      throw this.handleError(error);
    }
  }

  // Search production data with advanced filtering and fallback handling
  async searchProductionData(searchParams = {}) {
    try {
      const response = await request.get(`${this.baseURL}/production`)
        .query(searchParams)
        .set('withCredentials', true)
        .timeout(10000) // 10 second timeout
        .retry(2);

      // Add search method information to results
      const results = response.body;
      if (results.searchMethod === 'sql_fallback') {
        console.warn('Using SQL fallback for production search');
      }

      return results;
    } catch (error) {
      console.error('Production search error:', error);

      // If it's a network error, provide a user-friendly message
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new Error('La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques.');
      }

      throw this.handleError(error);
    }
  }

  // Search machine stops/arrets with filtering and fallback handling
  async searchMachineStops(searchParams = {}) {
    try {
      const response = await request.get(`${this.baseURL}/stops`)
        .query(searchParams)
        .set('withCredentials', true)
        .timeout(10000) // 10 second timeout
        .retry(2);

      // Add search method information to results
      const results = response.body;
      if (results.searchMethod === 'sql_fallback') {
        console.warn('Using SQL fallback for machine stops search');
      }

      return results;
    } catch (error) {
      console.error('Machine stops search error:', error);

      // If it's a network error, provide a user-friendly message
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new Error('La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques.');
      }

      throw this.handleError(error);
    }
  }

  // Get machine performance analytics
  async getMachinePerformanceAnalytics(dateFrom, dateTo) {
    try {
      const response = await request.get(`${this.baseURL}/analytics/machine-performance`)
        .query({ dateFrom, dateTo })
        .set('withCredentials', true)
        .retry(2);
      return response.body;
    } catch (error) {
      console.error('Analytics error:', error);
      throw this.handleError(error);
    }
  }

  // Get search suggestions/autocomplete
  async getSuggestions(query, field = 'machineName', size = 10) {
    try {
      const response = await request.get(`${this.baseURL}/suggest`)
        .query({ q: query, field, size })
        .set('withCredentials', true)
        .retry(2);
      return response.body.suggestions;
    } catch (error) {
      console.error('Suggestions error:', error);
      return []; // Return empty array on error
    }
  }

  // Check Elasticsearch health
  async checkHealth() {
    try {
      const response = await request.get(`${this.baseURL}/health`)
        .set('withCredentials', true)
        .retry(2);
      return response.body;
    } catch (error) {
      console.error('Health check error:', error);
      return { elasticsearch: { status: 'error', error: error.message } };
    }
  }

  // Trigger reindexing
  async reindex(index = 'all', options = {}) {
    try {
      const response = await request.post(`${this.baseURL}/reindex`)
        .send({
          index,
          ...options
        })
        .set('withCredentials', true)
        .retry(2);
      return response.body;
    } catch (error) {
      console.error('Reindex error:', error);
      throw this.handleError(error);
    }
  }

  // Helper method to handle errors consistently
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data.error || 'Search request failed',
        details: error.response.data.details,
        status: error.response.status
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'No response from search service',
        details: 'Please check your connection and try again'
      };
    } else {
      // Something else happened
      return {
        message: 'Search request failed',
        details: error.message
      };
    }
  }

  // Build search filters for machine sessions
  buildMachineSessionFilters(filters) {
    const params = {};
    
    if (filters.query) params.q = filters.query;
    if (filters.machineId) params.machineId = filters.machineId;
    if (filters.machineModel) params.machineModel = filters.machineModel;
    if (filters.status) params.status = filters.status;
    if (filters.shift) params.shift = filters.shift;
    if (filters.dateFrom) params.dateFrom = filters.dateFrom;
    if (filters.dateTo) params.dateTo = filters.dateTo;
    if (filters.page) params.page = filters.page;
    if (filters.size) params.size = filters.size;
    
    return params;
  }

  // Build search filters for reports
  buildReportFilters(filters) {
    const params = {};
    
    if (filters.query) params.q = filters.query;
    if (filters.type) params.type = filters.type;
    if (filters.machineId) params.machineId = filters.machineId;
    if (filters.generatedBy) params.generatedBy = filters.generatedBy;
    if (filters.dateFrom) params.dateFrom = filters.dateFrom;
    if (filters.dateTo) params.dateTo = filters.dateTo;
    if (filters.page) params.page = filters.page;
    if (filters.size) params.size = filters.size;
    
    return params;
  }

  // Format search results for display
  formatSearchResults(results, type) {
    if (!results || !results.results) return [];

    return results.results.map(result => ({
      id: result.id,
      type: result.type || type,
      score: result.score,
      title: this.extractTitle(result.data, result.type),
      description: this.extractDescription(result.data, result.type),
      timestamp: result.data.timestamp || result.data.generatedAt || result.data.date,
      highlight: result.highlight,
      data: result.data
    }));
  }

  // Extract title from search result data
  extractTitle(data, type) {
    switch (type) {
      case 'machine-session':
        return `${data.machineName} - Session ${data.sessionId}`;
      case 'production-data':
        return `${data.machineName} - Production ${data.date}`;
      case 'machine-stop':
        return `${data.machineName} - Arrêt ${data.stopCode}`;
      case 'report':
        return data.title || `${data.type} Report`;
      default:
        return data.title || data.machineName || 'Unknown';
    }
  }

  // Extract description from search result data
  extractDescription(data, type) {
    switch (type) {
      case 'machine-session':
        return `Operator: ${data.operator || 'Unknown'}, TRS: ${data.trs || 0}%, Production: ${data.production?.total || 0}`;
      case 'production-data':
        return `OEE: ${data.performance?.oee?.toFixed(1) || 0}%, Production: ${data.production?.good || 0} pièces, Opérateur: ${data.operator || 'N/A'}`;
      case 'machine-stop':
        return `${data.stopDescription || data.stopCode}, Durée: ${data.duration || 0} min, Catégorie: ${data.stopCategory || 'N/A'}`;
      case 'report':
        return data.description || `Generated by ${data.generatedBy || 'Unknown'}`;
      default:
        return JSON.stringify(data).substring(0, 100) + '...';
    }
  }

  // Debounced search function for real-time search
  createDebouncedSearch(searchFunction, delay = 300) {
    let timeoutId;
    
    return (...args) => {
      clearTimeout(timeoutId);
      return new Promise((resolve, reject) => {
        timeoutId = setTimeout(async () => {
          try {
            const result = await searchFunction(...args);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        }, delay);
      });
    };
  }
}

// Create singleton instance
const searchService = new SearchService();

export default searchService;
