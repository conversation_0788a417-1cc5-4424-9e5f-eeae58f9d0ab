import React from "react";

import  { useEffect } from 'react';

import { useAuth } from '../hooks/useAuth';
import DashboardContainer from '../Components/dashboard/DashboardContainer';
import { registerChartComponents } from '../Components/chart-config';
import websocketService from "../utils/websocketService";
import request from 'superagent';
import { Button, message } from 'antd';

if (typeof window !== 'undefined') {
  registerChartComponents();
}

const OptimizedDailyPerformanceDashboard = () => {
  // Authentication is handled in the useDashboardData hook
  const { isAuthenticated, loading } = useAuth();

  // 🔒 SECURITY: Helper function to create authenticated requests
  // Uses HTTP-only cookies exclusively for security
  const createAuthRequest = (method, url) => {
    const baseURL = (() => {
      // Check if we're in a browser environment first
      if (typeof window !== 'undefined') {
        const currentOrigin = window.location.origin;

        // If running on ngrok domain, use the same origin (unified architecture)
        if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
          return currentOrigin;
        }

        // For local development, check environment variable first
        if (import.meta.env.VITE_API_URL) {
          return import.meta.env.VITE_API_URL;
        }

        // Fallback to current origin for local development
        return currentOrigin;
      }

      // Fallback for server-side rendering
      return "http://localhost:5000";
    })();

    return request[method](`${baseURL}${url}`)
      .retry(2)
      .withCredentials() // ✅ FIXED: Correct SuperAgent syntax
      .timeout(30000);   // ✅ ADDED: Consistent timeout
    // Note: No Authorization header needed - backend uses HTTP-only cookies
  };

  // Initialize the WebSocket connection
  useEffect(() => {
    // Connect to WebSocket server
    console.log('Initializing WebSocket connection from OptimizedDailyPerformanceDashboard');
    websocketService.connect();

    // Handle window focus/blur events to manage connection
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // When tab becomes visible again, check connection and reconnect if needed
        if (!websocketService.isConnected) {
          console.log('Tab is visible again, checking WebSocket connection...');
          websocketService.ensureConnection();
        }
      }
    };

    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      websocketService.disconnect();
    };
  }, []);

  // Function to create a test notification using the working SSE test endpoint
  const createTestNotification = async () => {
    try {
      // Check authentication before making request
      if (!isAuthenticated) {
        message.error('Please log in to create test notifications', 3);
        return;
      }

      message.loading('Creating test notification...', 1);

      // Use the authenticated request helper
      const response = await createAuthRequest('post', '/api/notifications/test-sse')
        .send({});
      
      console.log('✅ Test SSE notification response:', response.body);
      
      if (response.body.success) {
        const { notification, broadcast_result, database_saved, database_error } = response.body;
        
        if (database_saved) {
          message.success(`✅ Test notification created and saved! ${notification.title}`, 4);
          console.log('📊 Database saved successfully. Notification will persist after reload.');
        } else {
          message.warning(`⚠️ Notification broadcast but not saved to database: ${database_error}. Will disappear on reload.`, 6);
          console.log('⚠️ Database error:', database_error);
          console.log('📡 SSE broadcast successful but notification is temporary');
        }
        
        console.log('📊 SSE Broadcast metrics:', broadcast_result);
      } else {
        message.warning('Test notification created but may not have been broadcast properly', 3);
      }
    } catch (error) {
      console.error('❌ Failed to create test notification via SSE:', error);
      console.error('❌ Error details:', {
        message: error.message,
        status: error.status,
        response: error.response?.body || error.response,
        code: error.code
      });

      if (error.code === 'ECONNABORTED') {
        message.error('Test notification timed out - server may be busy', 3);
      } else if (error.status === 401 || error.response?.status === 401) {
        message.error('Authentication required - please login again', 3);
        console.error('🔐 Authentication failed - user may need to re-login');
      } else if (error.code === 'ECONNREFUSED') {
        message.error('Cannot connect to server - please check if the application is running', 4);
        console.error('🔌 Connection refused - backend server may not be running');
      } else if (error.status >= 500) {
        message.error('Server error - please try again later', 3);
        console.error('🚨 Server error (5xx) - check backend logs');
      } else {
        message.error(`Failed to create test notification: ${error.response?.body?.message || error.message}`, 3);
      }
    }
  };

  return (
    <div className="optimized-dashboard-wrapper">
      {/* Test SSE notification button - Moved to higher z-index */}
      <div style={{ position: 'absolute', top: '10px', right: '10px', zIndex: 2000 }}>
        <Button
          type="primary"
          onClick={createTestNotification}
          disabled={loading || !isAuthenticated}
          loading={loading}
          style={{
            backgroundColor: isAuthenticated ? '#52c41a' : '#d9d9d9',
            borderColor: isAuthenticated ? '#52c41a' : '#d9d9d9',
            fontWeight: 'bold'
          }}
          title={!isAuthenticated ? 'Please log in to use this feature' : 'Create a test SSE notification'}
        >
          🧪 Test SSE Notification
        </Button>
      </div>
      
      {/* The DashboardContainer component handles all the dashboard functionality */}
      <DashboardContainer />
    </div>
  );
};

export default OptimizedDailyPerformanceDashboard;
