import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Chart as ChartJS } from 'chart.js';
import { useSettings } from '../hooks/useSettings';
import { EnhancedChartConfig } from '../utils/enhancedChartConfig';
import { Card, Button, Modal, Spin } from 'antd';
import { ExpandAltOutlined, ReloadOutlined } from '@ant-design/icons';

/**
 * Enhanced Chart Component
 * Integrates all chart settings for immediate effects
 */
function EnhancedChart({
  data,
  type = 'bar',
  title,
  customOptions = {},
  showExpandButton = true,
  showRefreshButton = false,
  onRefresh,
  loading = false,
  className = '',
  style = {},
  ...props
}) {
  const canvasRef = useRef(null);
  const chartRef = useRef(null);
  const [expandModalVisible, setExpandModalVisible] = useState(false);
  const [expandedChartRef, setExpandedChartRef] = useState(null);
  
  const { settings, theme } = useSettings();
  
  // Generate chart configuration based on current settings
  const chartConfig = useMemo(() => {
    const configGenerator = new EnhancedChartConfig(settings);
    return configGenerator.getChartConfig(type, customOptions);
  }, [settings, type, customOptions]);

  // Get chart height from settings
  const chartHeight = useMemo(() => {
    const configGenerator = new EnhancedChartConfig(settings);
    return configGenerator.getChartHeight();
  }, [settings]);

  // Get color scheme from settings
  const colorScheme = useMemo(() => {
    const configGenerator = new EnhancedChartConfig(settings);
    return configGenerator.getColorScheme();
  }, [settings]);

  // Apply color scheme to data
  const enhancedData = useMemo(() => {
    if (!data) return null;

    const colors = colorScheme;
    
    return {
      ...data,
      datasets: data.datasets?.map((dataset, index) => ({
        ...dataset,
        backgroundColor: dataset.backgroundColor || colors[index % colors.length],
        borderColor: dataset.borderColor || colors[index % colors.length],
        borderWidth: dataset.borderWidth || 2,
        tension: dataset.tension || 0.4
      })) || []
    };
  }, [data, colorScheme]);

  // Create/update chart
  useEffect(() => {
    if (!canvasRef.current || !enhancedData) return;

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy();
    }

    // Create new chart with enhanced configuration
    const ctx = canvasRef.current.getContext('2d');
    chartRef.current = new ChartJS(ctx, {
      ...chartConfig,
      data: enhancedData
    });

    // Handle click to expand if enabled
    if (settings.charts?.interaction?.clickToExpand !== false && showExpandButton) {
      chartRef.current.options.onClick = (event, elements) => {
        if (elements.length > 0) {
          setExpandModalVisible(true);
        }
      };
    }

    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [chartConfig, enhancedData, settings, showExpandButton]);

  // Update chart when data changes (without recreating)
  useEffect(() => {
    if (chartRef.current && enhancedData) {
      chartRef.current.data = enhancedData;
      chartRef.current.update('none'); // No animation for data updates
    }
  }, [enhancedData]);

  // Create expanded chart in modal
  useEffect(() => {
    if (expandModalVisible && expandedChartRef && enhancedData) {
      const ctx = expandedChartRef.getContext('2d');
      
      // Create expanded chart with larger size
      const expandedConfig = {
        ...chartConfig,
        options: {
          ...chartConfig.options,
          maintainAspectRatio: false,
          plugins: {
            ...chartConfig.options.plugins,
            legend: {
              ...chartConfig.options.plugins.legend,
              labels: {
                ...chartConfig.options.plugins.legend.labels,
                font: {
                  size: 14
                }
              }
            }
          }
        }
      };

      const expandedChart = new ChartJS(ctx, {
        ...expandedConfig,
        data: enhancedData
      });

      return () => {
        expandedChart.destroy();
      };
    }
  }, [expandModalVisible, expandedChartRef, chartConfig, enhancedData]);

  // Handle refresh
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  // Card actions
  const cardActions = [];
  
  if (showRefreshButton && onRefresh) {
    cardActions.push(
      <Button
        key="refresh"
        type="text"
        icon={<ReloadOutlined />}
        onClick={handleRefresh}
        loading={loading}
      >
        Refresh
      </Button>
    );
  }
  
  if (showExpandButton && settings.charts?.interaction?.clickToExpand !== false) {
    cardActions.push(
      <Button
        key="expand"
        type="text"
        icon={<ExpandAltOutlined />}
        onClick={() => setExpandModalVisible(true)}
      >
        Expand
      </Button>
    );
  }

  const chartContent = (
    <div style={{ position: 'relative', height: chartHeight, ...style }}>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 10
          }}
        >
          <Spin size="large" />
        </div>
      )}
      <canvas
        ref={canvasRef}
        className={className}
        {...props}
      />
    </div>
  );

  return (
    <>
      {title || cardActions.length > 0 ? (
        <Card
          title={title}
          actions={cardActions.length > 0 ? cardActions : undefined}
          style={{ height: '100%' }}
        >
          {chartContent}
        </Card>
      ) : (
        chartContent
      )}

      {/* Expanded Chart Modal */}
      <Modal
        title={title ? `${title} - Expanded View` : 'Chart - Expanded View'}
        open={expandModalVisible}
        onCancel={() => setExpandModalVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        <div style={{ height: '70vh', padding: '20px 0' }}>
          <canvas
            ref={setExpandedChartRef}
            style={{ width: '100%', height: '100%' }}
          />
        </div>
      </Modal>
    </>
  );
}

export default EnhancedChart;
