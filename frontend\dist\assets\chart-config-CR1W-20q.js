import{af as Zi,ag as Gv,S as zr}from"./index-LbZyOyVE.js";import{h as qt,i as sn,j as Nu,v as $r,k as Vs,s as Js,_ as Yv,m as Kr,r as _n,l as Yr,t as qv,n as Xv,o as kv,A as Uu,P as Wu,d as Fu,q as Zv,C as Oe,b as pf,c as vf,e as _f,p as mf,f as yf,g as xf,u as Tf,w as wf,x as Ef}from"./index-DuFKvyxm.js";var Gr={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */var $v=Gr.exports,Qs;function Kv(){return Qs||(Qs=1,function(i,u){(function(){var r,s="4.17.21",l=200,g="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",v="Expected a function",w="Invalid `variable` option passed into `_.template`",S="__lodash_hash_undefined__",O=500,P="__lodash_placeholder__",N=1,k=2,V=4,Rn=1,fn=2,En=1,Ln=2,on=4,xn=8,Zn=16,Wn=32,wt=64,Fn=128,Xt=256,gt=512,Me=30,kt="...",Le=800,Jr=16,ce=1,ji=2,no=3,he=1/0,Zt=9007199254740991,Qr=17976931348623157e292,De=NaN,et=**********,de=et-1,to=et>>>1,ar=[["ary",Fn],["bind",En],["bindKey",Ln],["curry",xn],["curryRight",Zn],["flip",gt],["partial",Wn],["partialRight",wt],["rearg",Xt]],$t="[object Arguments]",Ne="[object Array]",bn="[object AsyncFunction]",$n="[object Boolean]",tn="[object Date]",On="[object DOMException]",Rt="[object Error]",Et="[object Function]",Kt="[object GeneratorFunction]",An="[object Map]",pt="[object Number]",rt="[object Null]",Cn="[object Object]",sr="[object Promise]",jr="[object Proxy]",bt="[object RegExp]",un="[object Set]",ge="[object String]",Ue="[object Symbol]",eo="[object Undefined]",pe="[object WeakMap]",ro="[object WeakSet]",Vt="[object ArrayBuffer]",Ot="[object DataView]",We="[object Float32Array]",Fe="[object Float64Array]",Jt="[object Int8Array]",ve="[object Int16Array]",fr="[object Int32Array]",lr="[object Uint8Array]",cr="[object Uint8ClampedArray]",hr="[object Uint16Array]",dr="[object Uint32Array]",Be=/\b__p \+= '';/g,io=/\b(__p \+=) '' \+/g,oo=/(__e\(.*?\)|\b__t\)) \+\n'';/g,gr=/&(?:amp|lt|gt|quot|#39);/g,pr=/[&<>"']/g,vr=RegExp(gr.source),uo=RegExp(pr.source),ao=/<%-([\s\S]+?)%>/g,so=/<%([\s\S]+?)%>/g,_r=/<%=([\s\S]+?)%>/g,fo=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,lo=/^\w*$/,co=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Qt=/[\\^$.*+?()[\]{}|]/g,ho=RegExp(Qt.source),mr=/^\s+/,ni=/\s/,yr=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,go=/\{\n\/\* \[wrapped with (.+)\] \*/,ti=/,? & /,po=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ei=/[()=,{}\[\]\/\s]/,ri=/\\(\\)?/g,ii=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,xr=/\w*$/,Tr=/^[-+]0x[0-9a-f]+$/i,Pt=/^0b[01]+$/i,_e=/^\[object .+?Constructor\]$/,me=/^0o[0-7]+$/i,ze=/^(?:0|[1-9]\d*)$/,wr=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,He=/($^)/,vo=/['\n\r\u2028\u2029\\]/g,Mt="\\ud800-\\udfff",Bn="\\u0300-\\u036f",jt="\\ufe20-\\ufe2f",At="\\u20d0-\\u20ff",it=Bn+jt+At,ne="\\u2700-\\u27bf",Kn="a-z\\xdf-\\xf6\\xf8-\\xff",vt="\\xac\\xb1\\xd7\\xf7",oi="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",ui="\\u2000-\\u206f",Ge=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",zn="A-Z\\xc0-\\xd6\\xd8-\\xde",ye="\\ufe0e\\ufe0f",Ye=vt+oi+ui+Ge,xe="['’]",Er="["+Mt+"]",qe="["+Ye+"]",Lt="["+it+"]",_t="\\d+",_o="["+ne+"]",br="["+Kn+"]",Xe="[^"+Mt+Ye+_t+ne+Kn+zn+"]",ke="\\ud83c[\\udffb-\\udfff]",mo="(?:"+Lt+"|"+ke+")",ai="[^"+Mt+"]",f="(?:\\ud83c[\\udde6-\\uddff]){2}",d="[\\ud800-\\udbff][\\udc00-\\udfff]",_="["+zn+"]",T="\\u200d",R="(?:"+br+"|"+Xe+")",L="(?:"+_+"|"+Xe+")",Y="(?:"+xe+"(?:d|ll|m|re|s|t|ve))?",dn="(?:"+xe+"(?:D|LL|M|RE|S|T|VE))?",vn=mo+"?",Tn="["+ye+"]?",ot="(?:"+T+"(?:"+[ai,f,d].join("|")+")"+Tn+vn+")*",Zf="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",$f="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Gu=Tn+vn+ot,Kf="(?:"+[_o,f,d].join("|")+")"+Gu,Vf="(?:"+[ai+Lt+"?",Lt,f,d,Er].join("|")+")",Jf=RegExp(xe,"g"),Qf=RegExp(Lt,"g"),yo=RegExp(ke+"(?="+ke+")|"+Vf+Gu,"g"),jf=RegExp([_+"?"+br+"+"+Y+"(?="+[qe,_,"$"].join("|")+")",L+"+"+dn+"(?="+[qe,_+R,"$"].join("|")+")",_+"?"+R+"+"+Y,_+"+"+dn,$f,Zf,_t,Kf].join("|"),"g"),nl=RegExp("["+T+Mt+it+ye+"]"),tl=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,el=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],rl=-1,rn={};rn[We]=rn[Fe]=rn[Jt]=rn[ve]=rn[fr]=rn[lr]=rn[cr]=rn[hr]=rn[dr]=!0,rn[$t]=rn[Ne]=rn[Vt]=rn[$n]=rn[Ot]=rn[tn]=rn[Rt]=rn[Et]=rn[An]=rn[pt]=rn[Cn]=rn[bt]=rn[un]=rn[ge]=rn[pe]=!1;var en={};en[$t]=en[Ne]=en[Vt]=en[Ot]=en[$n]=en[tn]=en[We]=en[Fe]=en[Jt]=en[ve]=en[fr]=en[An]=en[pt]=en[Cn]=en[bt]=en[un]=en[ge]=en[Ue]=en[lr]=en[cr]=en[hr]=en[dr]=!0,en[Rt]=en[Et]=en[pe]=!1;var il={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},ol={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ul={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},al={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},sl=parseFloat,fl=parseInt,Yu=typeof Zi=="object"&&Zi&&Zi.Object===Object&&Zi,ll=typeof self=="object"&&self&&self.Object===Object&&self,Sn=Yu||ll||Function("return this")(),xo=u&&!u.nodeType&&u,Te=xo&&!0&&i&&!i.nodeType&&i,qu=Te&&Te.exports===xo,To=qu&&Yu.process,ut=function(){try{var y=Te&&Te.require&&Te.require("util").types;return y||To&&To.binding&&To.binding("util")}catch{}}(),Xu=ut&&ut.isArrayBuffer,ku=ut&&ut.isDate,Zu=ut&&ut.isMap,$u=ut&&ut.isRegExp,Ku=ut&&ut.isSet,Vu=ut&&ut.isTypedArray;function Vn(y,b,E){switch(E.length){case 0:return y.call(b);case 1:return y.call(b,E[0]);case 2:return y.call(b,E[0],E[1]);case 3:return y.call(b,E[0],E[1],E[2])}return y.apply(b,E)}function cl(y,b,E,D){for(var z=-1,J=y==null?0:y.length;++z<J;){var mn=y[z];b(D,mn,E(mn),y)}return D}function at(y,b){for(var E=-1,D=y==null?0:y.length;++E<D&&b(y[E],E,y)!==!1;);return y}function hl(y,b){for(var E=y==null?0:y.length;E--&&b(y[E],E,y)!==!1;);return y}function Ju(y,b){for(var E=-1,D=y==null?0:y.length;++E<D;)if(!b(y[E],E,y))return!1;return!0}function te(y,b){for(var E=-1,D=y==null?0:y.length,z=0,J=[];++E<D;){var mn=y[E];b(mn,E,y)&&(J[z++]=mn)}return J}function si(y,b){var E=y==null?0:y.length;return!!E&&Ze(y,b,0)>-1}function wo(y,b,E){for(var D=-1,z=y==null?0:y.length;++D<z;)if(E(b,y[D]))return!0;return!1}function an(y,b){for(var E=-1,D=y==null?0:y.length,z=Array(D);++E<D;)z[E]=b(y[E],E,y);return z}function ee(y,b){for(var E=-1,D=b.length,z=y.length;++E<D;)y[z+E]=b[E];return y}function Eo(y,b,E,D){var z=-1,J=y==null?0:y.length;for(D&&J&&(E=y[++z]);++z<J;)E=b(E,y[z],z,y);return E}function dl(y,b,E,D){var z=y==null?0:y.length;for(D&&z&&(E=y[--z]);z--;)E=b(E,y[z],z,y);return E}function bo(y,b){for(var E=-1,D=y==null?0:y.length;++E<D;)if(b(y[E],E,y))return!0;return!1}var gl=Ao("length");function pl(y){return y.split("")}function vl(y){return y.match(po)||[]}function Qu(y,b,E){var D;return E(y,function(z,J,mn){if(b(z,J,mn))return D=J,!1}),D}function fi(y,b,E,D){for(var z=y.length,J=E+(D?1:-1);D?J--:++J<z;)if(b(y[J],J,y))return J;return-1}function Ze(y,b,E){return b===b?Il(y,b,E):fi(y,ju,E)}function _l(y,b,E,D){for(var z=E-1,J=y.length;++z<J;)if(D(y[z],b))return z;return-1}function ju(y){return y!==y}function na(y,b){var E=y==null?0:y.length;return E?So(y,b)/E:De}function Ao(y){return function(b){return b==null?r:b[y]}}function Co(y){return function(b){return y==null?r:y[b]}}function ta(y,b,E,D,z){return z(y,function(J,mn,nn){E=D?(D=!1,J):b(E,J,mn,nn)}),E}function ml(y,b){var E=y.length;for(y.sort(b);E--;)y[E]=y[E].value;return y}function So(y,b){for(var E,D=-1,z=y.length;++D<z;){var J=b(y[D]);J!==r&&(E=E===r?J:E+J)}return E}function Io(y,b){for(var E=-1,D=Array(y);++E<y;)D[E]=b(E);return D}function yl(y,b){return an(b,function(E){return[E,y[E]]})}function ea(y){return y&&y.slice(0,ua(y)+1).replace(mr,"")}function Jn(y){return function(b){return y(b)}}function Ro(y,b){return an(b,function(E){return y[E]})}function Ar(y,b){return y.has(b)}function ra(y,b){for(var E=-1,D=y.length;++E<D&&Ze(b,y[E],0)>-1;);return E}function ia(y,b){for(var E=y.length;E--&&Ze(b,y[E],0)>-1;);return E}function xl(y,b){for(var E=y.length,D=0;E--;)y[E]===b&&++D;return D}var Tl=Co(il),wl=Co(ol);function El(y){return"\\"+al[y]}function bl(y,b){return y==null?r:y[b]}function $e(y){return nl.test(y)}function Al(y){return tl.test(y)}function Cl(y){for(var b,E=[];!(b=y.next()).done;)E.push(b.value);return E}function Oo(y){var b=-1,E=Array(y.size);return y.forEach(function(D,z){E[++b]=[z,D]}),E}function oa(y,b){return function(E){return y(b(E))}}function re(y,b){for(var E=-1,D=y.length,z=0,J=[];++E<D;){var mn=y[E];(mn===b||mn===P)&&(y[E]=P,J[z++]=E)}return J}function li(y){var b=-1,E=Array(y.size);return y.forEach(function(D){E[++b]=D}),E}function Sl(y){var b=-1,E=Array(y.size);return y.forEach(function(D){E[++b]=[D,D]}),E}function Il(y,b,E){for(var D=E-1,z=y.length;++D<z;)if(y[D]===b)return D;return-1}function Rl(y,b,E){for(var D=E+1;D--;)if(y[D]===b)return D;return D}function Ke(y){return $e(y)?Pl(y):gl(y)}function mt(y){return $e(y)?Ml(y):pl(y)}function ua(y){for(var b=y.length;b--&&ni.test(y.charAt(b)););return b}var Ol=Co(ul);function Pl(y){for(var b=yo.lastIndex=0;yo.test(y);)++b;return b}function Ml(y){return y.match(yo)||[]}function Ll(y){return y.match(jf)||[]}var Dl=function y(b){b=b==null?Sn:Ve.defaults(Sn.Object(),b,Ve.pick(Sn,el));var E=b.Array,D=b.Date,z=b.Error,J=b.Function,mn=b.Math,nn=b.Object,Po=b.RegExp,Nl=b.String,st=b.TypeError,ci=E.prototype,Ul=J.prototype,Je=nn.prototype,hi=b["__core-js_shared__"],di=Ul.toString,j=Je.hasOwnProperty,Wl=0,aa=function(){var n=/[^.]+$/.exec(hi&&hi.keys&&hi.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}(),gi=Je.toString,Fl=di.call(nn),Bl=Sn._,zl=Po("^"+di.call(j).replace(Qt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),pi=qu?b.Buffer:r,ie=b.Symbol,vi=b.Uint8Array,sa=pi?pi.allocUnsafe:r,_i=oa(nn.getPrototypeOf,nn),fa=nn.create,la=Je.propertyIsEnumerable,mi=ci.splice,ca=ie?ie.isConcatSpreadable:r,Cr=ie?ie.iterator:r,we=ie?ie.toStringTag:r,yi=function(){try{var n=Se(nn,"defineProperty");return n({},"",{}),n}catch{}}(),Hl=b.clearTimeout!==Sn.clearTimeout&&b.clearTimeout,Gl=D&&D.now!==Sn.Date.now&&D.now,Yl=b.setTimeout!==Sn.setTimeout&&b.setTimeout,xi=mn.ceil,Ti=mn.floor,Mo=nn.getOwnPropertySymbols,ql=pi?pi.isBuffer:r,ha=b.isFinite,Xl=ci.join,kl=oa(nn.keys,nn),yn=mn.max,Pn=mn.min,Zl=D.now,$l=b.parseInt,da=mn.random,Kl=ci.reverse,Lo=Se(b,"DataView"),Sr=Se(b,"Map"),Do=Se(b,"Promise"),Qe=Se(b,"Set"),Ir=Se(b,"WeakMap"),Rr=Se(nn,"create"),wi=Ir&&new Ir,je={},Vl=Ie(Lo),Jl=Ie(Sr),Ql=Ie(Do),jl=Ie(Qe),nc=Ie(Ir),Ei=ie?ie.prototype:r,Or=Ei?Ei.valueOf:r,ga=Ei?Ei.toString:r;function c(n){if(hn(n)&&!H(n)&&!(n instanceof $)){if(n instanceof ft)return n;if(j.call(n,"__wrapped__"))return ps(n)}return new ft(n)}var nr=function(){function n(){}return function(t){if(!ln(t))return{};if(fa)return fa(t);n.prototype=t;var e=new n;return n.prototype=r,e}}();function bi(){}function ft(n,t){this.__wrapped__=n,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=r}c.templateSettings={escape:ao,evaluate:so,interpolate:_r,variable:"",imports:{_:c}},c.prototype=bi.prototype,c.prototype.constructor=c,ft.prototype=nr(bi.prototype),ft.prototype.constructor=ft;function $(n){this.__wrapped__=n,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=et,this.__views__=[]}function tc(){var n=new $(this.__wrapped__);return n.__actions__=Hn(this.__actions__),n.__dir__=this.__dir__,n.__filtered__=this.__filtered__,n.__iteratees__=Hn(this.__iteratees__),n.__takeCount__=this.__takeCount__,n.__views__=Hn(this.__views__),n}function ec(){if(this.__filtered__){var n=new $(this);n.__dir__=-1,n.__filtered__=!0}else n=this.clone(),n.__dir__*=-1;return n}function rc(){var n=this.__wrapped__.value(),t=this.__dir__,e=H(n),o=t<0,a=e?n.length:0,h=ph(0,a,this.__views__),p=h.start,m=h.end,x=m-p,A=o?m:p-1,C=this.__iteratees__,I=C.length,M=0,U=Pn(x,this.__takeCount__);if(!e||!o&&a==x&&U==x)return Fa(n,this.__actions__);var F=[];n:for(;x--&&M<U;){A+=t;for(var q=-1,B=n[A];++q<I;){var Z=C[q],K=Z.iteratee,nt=Z.type,Un=K(B);if(nt==ji)B=Un;else if(!Un){if(nt==ce)continue n;break n}}F[M++]=B}return F}$.prototype=nr(bi.prototype),$.prototype.constructor=$;function Ee(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var o=n[t];this.set(o[0],o[1])}}function ic(){this.__data__=Rr?Rr(null):{},this.size=0}function oc(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t}function uc(n){var t=this.__data__;if(Rr){var e=t[n];return e===S?r:e}return j.call(t,n)?t[n]:r}function ac(n){var t=this.__data__;return Rr?t[n]!==r:j.call(t,n)}function sc(n,t){var e=this.__data__;return this.size+=this.has(n)?0:1,e[n]=Rr&&t===r?S:t,this}Ee.prototype.clear=ic,Ee.prototype.delete=oc,Ee.prototype.get=uc,Ee.prototype.has=ac,Ee.prototype.set=sc;function Dt(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var o=n[t];this.set(o[0],o[1])}}function fc(){this.__data__=[],this.size=0}function lc(n){var t=this.__data__,e=Ai(t,n);if(e<0)return!1;var o=t.length-1;return e==o?t.pop():mi.call(t,e,1),--this.size,!0}function cc(n){var t=this.__data__,e=Ai(t,n);return e<0?r:t[e][1]}function hc(n){return Ai(this.__data__,n)>-1}function dc(n,t){var e=this.__data__,o=Ai(e,n);return o<0?(++this.size,e.push([n,t])):e[o][1]=t,this}Dt.prototype.clear=fc,Dt.prototype.delete=lc,Dt.prototype.get=cc,Dt.prototype.has=hc,Dt.prototype.set=dc;function Nt(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var o=n[t];this.set(o[0],o[1])}}function gc(){this.size=0,this.__data__={hash:new Ee,map:new(Sr||Dt),string:new Ee}}function pc(n){var t=Wi(this,n).delete(n);return this.size-=t?1:0,t}function vc(n){return Wi(this,n).get(n)}function _c(n){return Wi(this,n).has(n)}function mc(n,t){var e=Wi(this,n),o=e.size;return e.set(n,t),this.size+=e.size==o?0:1,this}Nt.prototype.clear=gc,Nt.prototype.delete=pc,Nt.prototype.get=vc,Nt.prototype.has=_c,Nt.prototype.set=mc;function be(n){var t=-1,e=n==null?0:n.length;for(this.__data__=new Nt;++t<e;)this.add(n[t])}function yc(n){return this.__data__.set(n,S),this}function xc(n){return this.__data__.has(n)}be.prototype.add=be.prototype.push=yc,be.prototype.has=xc;function yt(n){var t=this.__data__=new Dt(n);this.size=t.size}function Tc(){this.__data__=new Dt,this.size=0}function wc(n){var t=this.__data__,e=t.delete(n);return this.size=t.size,e}function Ec(n){return this.__data__.get(n)}function bc(n){return this.__data__.has(n)}function Ac(n,t){var e=this.__data__;if(e instanceof Dt){var o=e.__data__;if(!Sr||o.length<l-1)return o.push([n,t]),this.size=++e.size,this;e=this.__data__=new Nt(o)}return e.set(n,t),this.size=e.size,this}yt.prototype.clear=Tc,yt.prototype.delete=wc,yt.prototype.get=Ec,yt.prototype.has=bc,yt.prototype.set=Ac;function pa(n,t){var e=H(n),o=!e&&Re(n),a=!e&&!o&&fe(n),h=!e&&!o&&!a&&ir(n),p=e||o||a||h,m=p?Io(n.length,Nl):[],x=m.length;for(var A in n)(t||j.call(n,A))&&!(p&&(A=="length"||a&&(A=="offset"||A=="parent")||h&&(A=="buffer"||A=="byteLength"||A=="byteOffset")||Bt(A,x)))&&m.push(A);return m}function va(n){var t=n.length;return t?n[Xo(0,t-1)]:r}function Cc(n,t){return Fi(Hn(n),Ae(t,0,n.length))}function Sc(n){return Fi(Hn(n))}function No(n,t,e){(e!==r&&!xt(n[t],e)||e===r&&!(t in n))&&Ut(n,t,e)}function Pr(n,t,e){var o=n[t];(!(j.call(n,t)&&xt(o,e))||e===r&&!(t in n))&&Ut(n,t,e)}function Ai(n,t){for(var e=n.length;e--;)if(xt(n[e][0],t))return e;return-1}function Ic(n,t,e,o){return oe(n,function(a,h,p){t(o,a,e(a),p)}),o}function _a(n,t){return n&&St(t,wn(t),n)}function Rc(n,t){return n&&St(t,Yn(t),n)}function Ut(n,t,e){t=="__proto__"&&yi?yi(n,t,{configurable:!0,enumerable:!0,value:e,writable:!0}):n[t]=e}function Uo(n,t){for(var e=-1,o=t.length,a=E(o),h=n==null;++e<o;)a[e]=h?r:vu(n,t[e]);return a}function Ae(n,t,e){return n===n&&(e!==r&&(n=n<=e?n:e),t!==r&&(n=n>=t?n:t)),n}function lt(n,t,e,o,a,h){var p,m=t&N,x=t&k,A=t&V;if(e&&(p=a?e(n,o,a,h):e(n)),p!==r)return p;if(!ln(n))return n;var C=H(n);if(C){if(p=_h(n),!m)return Hn(n,p)}else{var I=Mn(n),M=I==Et||I==Kt;if(fe(n))return Ha(n,m);if(I==Cn||I==$t||M&&!a){if(p=x||M?{}:us(n),!m)return x?uh(n,Rc(p,n)):oh(n,_a(p,n))}else{if(!en[I])return a?n:{};p=mh(n,I,m)}}h||(h=new yt);var U=h.get(n);if(U)return U;h.set(n,p),Ns(n)?n.forEach(function(B){p.add(lt(B,t,e,B,n,h))}):Ls(n)&&n.forEach(function(B,Z){p.set(Z,lt(B,t,e,Z,n,h))});var F=A?x?eu:tu:x?Yn:wn,q=C?r:F(n);return at(q||n,function(B,Z){q&&(Z=B,B=n[Z]),Pr(p,Z,lt(B,t,e,Z,n,h))}),p}function Oc(n){var t=wn(n);return function(e){return ma(e,n,t)}}function ma(n,t,e){var o=e.length;if(n==null)return!o;for(n=nn(n);o--;){var a=e[o],h=t[a],p=n[a];if(p===r&&!(a in n)||!h(p))return!1}return!0}function ya(n,t,e){if(typeof n!="function")throw new st(v);return Fr(function(){n.apply(r,e)},t)}function Mr(n,t,e,o){var a=-1,h=si,p=!0,m=n.length,x=[],A=t.length;if(!m)return x;e&&(t=an(t,Jn(e))),o?(h=wo,p=!1):t.length>=l&&(h=Ar,p=!1,t=new be(t));n:for(;++a<m;){var C=n[a],I=e==null?C:e(C);if(C=o||C!==0?C:0,p&&I===I){for(var M=A;M--;)if(t[M]===I)continue n;x.push(C)}else h(t,I,o)||x.push(C)}return x}var oe=ka(Ct),xa=ka(Fo,!0);function Pc(n,t){var e=!0;return oe(n,function(o,a,h){return e=!!t(o,a,h),e}),e}function Ci(n,t,e){for(var o=-1,a=n.length;++o<a;){var h=n[o],p=t(h);if(p!=null&&(m===r?p===p&&!jn(p):e(p,m)))var m=p,x=h}return x}function Mc(n,t,e,o){var a=n.length;for(e=G(e),e<0&&(e=-e>a?0:a+e),o=o===r||o>a?a:G(o),o<0&&(o+=a),o=e>o?0:Ws(o);e<o;)n[e++]=t;return n}function Ta(n,t){var e=[];return oe(n,function(o,a,h){t(o,a,h)&&e.push(o)}),e}function In(n,t,e,o,a){var h=-1,p=n.length;for(e||(e=xh),a||(a=[]);++h<p;){var m=n[h];t>0&&e(m)?t>1?In(m,t-1,e,o,a):ee(a,m):o||(a[a.length]=m)}return a}var Wo=Za(),wa=Za(!0);function Ct(n,t){return n&&Wo(n,t,wn)}function Fo(n,t){return n&&wa(n,t,wn)}function Si(n,t){return te(t,function(e){return zt(n[e])})}function Ce(n,t){t=ae(t,n);for(var e=0,o=t.length;n!=null&&e<o;)n=n[It(t[e++])];return e&&e==o?n:r}function Ea(n,t,e){var o=t(n);return H(n)?o:ee(o,e(n))}function Dn(n){return n==null?n===r?eo:rt:we&&we in nn(n)?gh(n):Sh(n)}function Bo(n,t){return n>t}function Lc(n,t){return n!=null&&j.call(n,t)}function Dc(n,t){return n!=null&&t in nn(n)}function Nc(n,t,e){return n>=Pn(t,e)&&n<yn(t,e)}function zo(n,t,e){for(var o=e?wo:si,a=n[0].length,h=n.length,p=h,m=E(h),x=1/0,A=[];p--;){var C=n[p];p&&t&&(C=an(C,Jn(t))),x=Pn(C.length,x),m[p]=!e&&(t||a>=120&&C.length>=120)?new be(p&&C):r}C=n[0];var I=-1,M=m[0];n:for(;++I<a&&A.length<x;){var U=C[I],F=t?t(U):U;if(U=e||U!==0?U:0,!(M?Ar(M,F):o(A,F,e))){for(p=h;--p;){var q=m[p];if(!(q?Ar(q,F):o(n[p],F,e)))continue n}M&&M.push(F),A.push(U)}}return A}function Uc(n,t,e,o){return Ct(n,function(a,h,p){t(o,e(a),h,p)}),o}function Lr(n,t,e){t=ae(t,n),n=ls(n,t);var o=n==null?n:n[It(ht(t))];return o==null?r:Vn(o,n,e)}function ba(n){return hn(n)&&Dn(n)==$t}function Wc(n){return hn(n)&&Dn(n)==Vt}function Fc(n){return hn(n)&&Dn(n)==tn}function Dr(n,t,e,o,a){return n===t?!0:n==null||t==null||!hn(n)&&!hn(t)?n!==n&&t!==t:Bc(n,t,e,o,Dr,a)}function Bc(n,t,e,o,a,h){var p=H(n),m=H(t),x=p?Ne:Mn(n),A=m?Ne:Mn(t);x=x==$t?Cn:x,A=A==$t?Cn:A;var C=x==Cn,I=A==Cn,M=x==A;if(M&&fe(n)){if(!fe(t))return!1;p=!0,C=!1}if(M&&!C)return h||(h=new yt),p||ir(n)?rs(n,t,e,o,a,h):hh(n,t,x,e,o,a,h);if(!(e&Rn)){var U=C&&j.call(n,"__wrapped__"),F=I&&j.call(t,"__wrapped__");if(U||F){var q=U?n.value():n,B=F?t.value():t;return h||(h=new yt),a(q,B,e,o,h)}}return M?(h||(h=new yt),dh(n,t,e,o,a,h)):!1}function zc(n){return hn(n)&&Mn(n)==An}function Ho(n,t,e,o){var a=e.length,h=a,p=!o;if(n==null)return!h;for(n=nn(n);a--;){var m=e[a];if(p&&m[2]?m[1]!==n[m[0]]:!(m[0]in n))return!1}for(;++a<h;){m=e[a];var x=m[0],A=n[x],C=m[1];if(p&&m[2]){if(A===r&&!(x in n))return!1}else{var I=new yt;if(o)var M=o(A,C,x,n,t,I);if(!(M===r?Dr(C,A,Rn|fn,o,I):M))return!1}}return!0}function Aa(n){if(!ln(n)||wh(n))return!1;var t=zt(n)?zl:_e;return t.test(Ie(n))}function Hc(n){return hn(n)&&Dn(n)==bt}function Gc(n){return hn(n)&&Mn(n)==un}function Yc(n){return hn(n)&&qi(n.length)&&!!rn[Dn(n)]}function Ca(n){return typeof n=="function"?n:n==null?qn:typeof n=="object"?H(n)?Ra(n[0],n[1]):Ia(n):$s(n)}function Go(n){if(!Wr(n))return kl(n);var t=[];for(var e in nn(n))j.call(n,e)&&e!="constructor"&&t.push(e);return t}function qc(n){if(!ln(n))return Ch(n);var t=Wr(n),e=[];for(var o in n)o=="constructor"&&(t||!j.call(n,o))||e.push(o);return e}function Yo(n,t){return n<t}function Sa(n,t){var e=-1,o=Gn(n)?E(n.length):[];return oe(n,function(a,h,p){o[++e]=t(a,h,p)}),o}function Ia(n){var t=iu(n);return t.length==1&&t[0][2]?ss(t[0][0],t[0][1]):function(e){return e===n||Ho(e,n,t)}}function Ra(n,t){return uu(n)&&as(t)?ss(It(n),t):function(e){var o=vu(e,n);return o===r&&o===t?_u(e,n):Dr(t,o,Rn|fn)}}function Ii(n,t,e,o,a){n!==t&&Wo(t,function(h,p){if(a||(a=new yt),ln(h))Xc(n,t,p,e,Ii,o,a);else{var m=o?o(su(n,p),h,p+"",n,t,a):r;m===r&&(m=h),No(n,p,m)}},Yn)}function Xc(n,t,e,o,a,h,p){var m=su(n,e),x=su(t,e),A=p.get(x);if(A){No(n,e,A);return}var C=h?h(m,x,e+"",n,t,p):r,I=C===r;if(I){var M=H(x),U=!M&&fe(x),F=!M&&!U&&ir(x);C=x,M||U||F?H(m)?C=m:gn(m)?C=Hn(m):U?(I=!1,C=Ha(x,!0)):F?(I=!1,C=Ga(x,!0)):C=[]:Br(x)||Re(x)?(C=m,Re(m)?C=Fs(m):(!ln(m)||zt(m))&&(C=us(x))):I=!1}I&&(p.set(x,C),a(C,x,o,h,p),p.delete(x)),No(n,e,C)}function Oa(n,t){var e=n.length;if(e)return t+=t<0?e:0,Bt(t,e)?n[t]:r}function Pa(n,t,e){t.length?t=an(t,function(h){return H(h)?function(p){return Ce(p,h.length===1?h[0]:h)}:h}):t=[qn];var o=-1;t=an(t,Jn(W()));var a=Sa(n,function(h,p,m){var x=an(t,function(A){return A(h)});return{criteria:x,index:++o,value:h}});return ml(a,function(h,p){return ih(h,p,e)})}function kc(n,t){return Ma(n,t,function(e,o){return _u(n,o)})}function Ma(n,t,e){for(var o=-1,a=t.length,h={};++o<a;){var p=t[o],m=Ce(n,p);e(m,p)&&Nr(h,ae(p,n),m)}return h}function Zc(n){return function(t){return Ce(t,n)}}function qo(n,t,e,o){var a=o?_l:Ze,h=-1,p=t.length,m=n;for(n===t&&(t=Hn(t)),e&&(m=an(n,Jn(e)));++h<p;)for(var x=0,A=t[h],C=e?e(A):A;(x=a(m,C,x,o))>-1;)m!==n&&mi.call(m,x,1),mi.call(n,x,1);return n}function La(n,t){for(var e=n?t.length:0,o=e-1;e--;){var a=t[e];if(e==o||a!==h){var h=a;Bt(a)?mi.call(n,a,1):$o(n,a)}}return n}function Xo(n,t){return n+Ti(da()*(t-n+1))}function $c(n,t,e,o){for(var a=-1,h=yn(xi((t-n)/(e||1)),0),p=E(h);h--;)p[o?h:++a]=n,n+=e;return p}function ko(n,t){var e="";if(!n||t<1||t>Zt)return e;do t%2&&(e+=n),t=Ti(t/2),t&&(n+=n);while(t);return e}function X(n,t){return fu(fs(n,t,qn),n+"")}function Kc(n){return va(or(n))}function Vc(n,t){var e=or(n);return Fi(e,Ae(t,0,e.length))}function Nr(n,t,e,o){if(!ln(n))return n;t=ae(t,n);for(var a=-1,h=t.length,p=h-1,m=n;m!=null&&++a<h;){var x=It(t[a]),A=e;if(x==="__proto__"||x==="constructor"||x==="prototype")return n;if(a!=p){var C=m[x];A=o?o(C,x,m):r,A===r&&(A=ln(C)?C:Bt(t[a+1])?[]:{})}Pr(m,x,A),m=m[x]}return n}var Da=wi?function(n,t){return wi.set(n,t),n}:qn,Jc=yi?function(n,t){return yi(n,"toString",{configurable:!0,enumerable:!1,value:yu(t),writable:!0})}:qn;function Qc(n){return Fi(or(n))}function ct(n,t,e){var o=-1,a=n.length;t<0&&(t=-t>a?0:a+t),e=e>a?a:e,e<0&&(e+=a),a=t>e?0:e-t>>>0,t>>>=0;for(var h=E(a);++o<a;)h[o]=n[o+t];return h}function jc(n,t){var e;return oe(n,function(o,a,h){return e=t(o,a,h),!e}),!!e}function Ri(n,t,e){var o=0,a=n==null?o:n.length;if(typeof t=="number"&&t===t&&a<=to){for(;o<a;){var h=o+a>>>1,p=n[h];p!==null&&!jn(p)&&(e?p<=t:p<t)?o=h+1:a=h}return a}return Zo(n,t,qn,e)}function Zo(n,t,e,o){var a=0,h=n==null?0:n.length;if(h===0)return 0;t=e(t);for(var p=t!==t,m=t===null,x=jn(t),A=t===r;a<h;){var C=Ti((a+h)/2),I=e(n[C]),M=I!==r,U=I===null,F=I===I,q=jn(I);if(p)var B=o||F;else A?B=F&&(o||M):m?B=F&&M&&(o||!U):x?B=F&&M&&!U&&(o||!q):U||q?B=!1:B=o?I<=t:I<t;B?a=C+1:h=C}return Pn(h,de)}function Na(n,t){for(var e=-1,o=n.length,a=0,h=[];++e<o;){var p=n[e],m=t?t(p):p;if(!e||!xt(m,x)){var x=m;h[a++]=p===0?0:p}}return h}function Ua(n){return typeof n=="number"?n:jn(n)?De:+n}function Qn(n){if(typeof n=="string")return n;if(H(n))return an(n,Qn)+"";if(jn(n))return ga?ga.call(n):"";var t=n+"";return t=="0"&&1/n==-1/0?"-0":t}function ue(n,t,e){var o=-1,a=si,h=n.length,p=!0,m=[],x=m;if(e)p=!1,a=wo;else if(h>=l){var A=t?null:lh(n);if(A)return li(A);p=!1,a=Ar,x=new be}else x=t?[]:m;n:for(;++o<h;){var C=n[o],I=t?t(C):C;if(C=e||C!==0?C:0,p&&I===I){for(var M=x.length;M--;)if(x[M]===I)continue n;t&&x.push(I),m.push(C)}else a(x,I,e)||(x!==m&&x.push(I),m.push(C))}return m}function $o(n,t){return t=ae(t,n),n=ls(n,t),n==null||delete n[It(ht(t))]}function Wa(n,t,e,o){return Nr(n,t,e(Ce(n,t)),o)}function Oi(n,t,e,o){for(var a=n.length,h=o?a:-1;(o?h--:++h<a)&&t(n[h],h,n););return e?ct(n,o?0:h,o?h+1:a):ct(n,o?h+1:0,o?a:h)}function Fa(n,t){var e=n;return e instanceof $&&(e=e.value()),Eo(t,function(o,a){return a.func.apply(a.thisArg,ee([o],a.args))},e)}function Ko(n,t,e){var o=n.length;if(o<2)return o?ue(n[0]):[];for(var a=-1,h=E(o);++a<o;)for(var p=n[a],m=-1;++m<o;)m!=a&&(h[a]=Mr(h[a]||p,n[m],t,e));return ue(In(h,1),t,e)}function Ba(n,t,e){for(var o=-1,a=n.length,h=t.length,p={};++o<a;){var m=o<h?t[o]:r;e(p,n[o],m)}return p}function Vo(n){return gn(n)?n:[]}function Jo(n){return typeof n=="function"?n:qn}function ae(n,t){return H(n)?n:uu(n,t)?[n]:gs(Q(n))}var nh=X;function se(n,t,e){var o=n.length;return e=e===r?o:e,!t&&e>=o?n:ct(n,t,e)}var za=Hl||function(n){return Sn.clearTimeout(n)};function Ha(n,t){if(t)return n.slice();var e=n.length,o=sa?sa(e):new n.constructor(e);return n.copy(o),o}function Qo(n){var t=new n.constructor(n.byteLength);return new vi(t).set(new vi(n)),t}function th(n,t){var e=t?Qo(n.buffer):n.buffer;return new n.constructor(e,n.byteOffset,n.byteLength)}function eh(n){var t=new n.constructor(n.source,xr.exec(n));return t.lastIndex=n.lastIndex,t}function rh(n){return Or?nn(Or.call(n)):{}}function Ga(n,t){var e=t?Qo(n.buffer):n.buffer;return new n.constructor(e,n.byteOffset,n.length)}function Ya(n,t){if(n!==t){var e=n!==r,o=n===null,a=n===n,h=jn(n),p=t!==r,m=t===null,x=t===t,A=jn(t);if(!m&&!A&&!h&&n>t||h&&p&&x&&!m&&!A||o&&p&&x||!e&&x||!a)return 1;if(!o&&!h&&!A&&n<t||A&&e&&a&&!o&&!h||m&&e&&a||!p&&a||!x)return-1}return 0}function ih(n,t,e){for(var o=-1,a=n.criteria,h=t.criteria,p=a.length,m=e.length;++o<p;){var x=Ya(a[o],h[o]);if(x){if(o>=m)return x;var A=e[o];return x*(A=="desc"?-1:1)}}return n.index-t.index}function qa(n,t,e,o){for(var a=-1,h=n.length,p=e.length,m=-1,x=t.length,A=yn(h-p,0),C=E(x+A),I=!o;++m<x;)C[m]=t[m];for(;++a<p;)(I||a<h)&&(C[e[a]]=n[a]);for(;A--;)C[m++]=n[a++];return C}function Xa(n,t,e,o){for(var a=-1,h=n.length,p=-1,m=e.length,x=-1,A=t.length,C=yn(h-m,0),I=E(C+A),M=!o;++a<C;)I[a]=n[a];for(var U=a;++x<A;)I[U+x]=t[x];for(;++p<m;)(M||a<h)&&(I[U+e[p]]=n[a++]);return I}function Hn(n,t){var e=-1,o=n.length;for(t||(t=E(o));++e<o;)t[e]=n[e];return t}function St(n,t,e,o){var a=!e;e||(e={});for(var h=-1,p=t.length;++h<p;){var m=t[h],x=o?o(e[m],n[m],m,e,n):r;x===r&&(x=n[m]),a?Ut(e,m,x):Pr(e,m,x)}return e}function oh(n,t){return St(n,ou(n),t)}function uh(n,t){return St(n,is(n),t)}function Pi(n,t){return function(e,o){var a=H(e)?cl:Ic,h=t?t():{};return a(e,n,W(o,2),h)}}function tr(n){return X(function(t,e){var o=-1,a=e.length,h=a>1?e[a-1]:r,p=a>2?e[2]:r;for(h=n.length>3&&typeof h=="function"?(a--,h):r,p&&Nn(e[0],e[1],p)&&(h=a<3?r:h,a=1),t=nn(t);++o<a;){var m=e[o];m&&n(t,m,o,h)}return t})}function ka(n,t){return function(e,o){if(e==null)return e;if(!Gn(e))return n(e,o);for(var a=e.length,h=t?a:-1,p=nn(e);(t?h--:++h<a)&&o(p[h],h,p)!==!1;);return e}}function Za(n){return function(t,e,o){for(var a=-1,h=nn(t),p=o(t),m=p.length;m--;){var x=p[n?m:++a];if(e(h[x],x,h)===!1)break}return t}}function ah(n,t,e){var o=t&En,a=Ur(n);function h(){var p=this&&this!==Sn&&this instanceof h?a:n;return p.apply(o?e:this,arguments)}return h}function $a(n){return function(t){t=Q(t);var e=$e(t)?mt(t):r,o=e?e[0]:t.charAt(0),a=e?se(e,1).join(""):t.slice(1);return o[n]()+a}}function er(n){return function(t){return Eo(ks(Xs(t).replace(Jf,"")),n,"")}}function Ur(n){return function(){var t=arguments;switch(t.length){case 0:return new n;case 1:return new n(t[0]);case 2:return new n(t[0],t[1]);case 3:return new n(t[0],t[1],t[2]);case 4:return new n(t[0],t[1],t[2],t[3]);case 5:return new n(t[0],t[1],t[2],t[3],t[4]);case 6:return new n(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new n(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var e=nr(n.prototype),o=n.apply(e,t);return ln(o)?o:e}}function sh(n,t,e){var o=Ur(n);function a(){for(var h=arguments.length,p=E(h),m=h,x=rr(a);m--;)p[m]=arguments[m];var A=h<3&&p[0]!==x&&p[h-1]!==x?[]:re(p,x);if(h-=A.length,h<e)return ja(n,t,Mi,a.placeholder,r,p,A,r,r,e-h);var C=this&&this!==Sn&&this instanceof a?o:n;return Vn(C,this,p)}return a}function Ka(n){return function(t,e,o){var a=nn(t);if(!Gn(t)){var h=W(e,3);t=wn(t),e=function(m){return h(a[m],m,a)}}var p=n(t,e,o);return p>-1?a[h?t[p]:p]:r}}function Va(n){return Ft(function(t){var e=t.length,o=e,a=ft.prototype.thru;for(n&&t.reverse();o--;){var h=t[o];if(typeof h!="function")throw new st(v);if(a&&!p&&Ui(h)=="wrapper")var p=new ft([],!0)}for(o=p?o:e;++o<e;){h=t[o];var m=Ui(h),x=m=="wrapper"?ru(h):r;x&&au(x[0])&&x[1]==(Fn|xn|Wn|Xt)&&!x[4].length&&x[9]==1?p=p[Ui(x[0])].apply(p,x[3]):p=h.length==1&&au(h)?p[m]():p.thru(h)}return function(){var A=arguments,C=A[0];if(p&&A.length==1&&H(C))return p.plant(C).value();for(var I=0,M=e?t[I].apply(this,A):C;++I<e;)M=t[I].call(this,M);return M}})}function Mi(n,t,e,o,a,h,p,m,x,A){var C=t&Fn,I=t&En,M=t&Ln,U=t&(xn|Zn),F=t&gt,q=M?r:Ur(n);function B(){for(var Z=arguments.length,K=E(Z),nt=Z;nt--;)K[nt]=arguments[nt];if(U)var Un=rr(B),tt=xl(K,Un);if(o&&(K=qa(K,o,a,U)),h&&(K=Xa(K,h,p,U)),Z-=tt,U&&Z<A){var pn=re(K,Un);return ja(n,t,Mi,B.placeholder,e,K,pn,m,x,A-Z)}var Tt=I?e:this,Gt=M?Tt[n]:n;return Z=K.length,m?K=Ih(K,m):F&&Z>1&&K.reverse(),C&&x<Z&&(K.length=x),this&&this!==Sn&&this instanceof B&&(Gt=q||Ur(Gt)),Gt.apply(Tt,K)}return B}function Ja(n,t){return function(e,o){return Uc(e,n,t(o),{})}}function Li(n,t){return function(e,o){var a;if(e===r&&o===r)return t;if(e!==r&&(a=e),o!==r){if(a===r)return o;typeof e=="string"||typeof o=="string"?(e=Qn(e),o=Qn(o)):(e=Ua(e),o=Ua(o)),a=n(e,o)}return a}}function jo(n){return Ft(function(t){return t=an(t,Jn(W())),X(function(e){var o=this;return n(t,function(a){return Vn(a,o,e)})})})}function Di(n,t){t=t===r?" ":Qn(t);var e=t.length;if(e<2)return e?ko(t,n):t;var o=ko(t,xi(n/Ke(t)));return $e(t)?se(mt(o),0,n).join(""):o.slice(0,n)}function fh(n,t,e,o){var a=t&En,h=Ur(n);function p(){for(var m=-1,x=arguments.length,A=-1,C=o.length,I=E(C+x),M=this&&this!==Sn&&this instanceof p?h:n;++A<C;)I[A]=o[A];for(;x--;)I[A++]=arguments[++m];return Vn(M,a?e:this,I)}return p}function Qa(n){return function(t,e,o){return o&&typeof o!="number"&&Nn(t,e,o)&&(e=o=r),t=Ht(t),e===r?(e=t,t=0):e=Ht(e),o=o===r?t<e?1:-1:Ht(o),$c(t,e,o,n)}}function Ni(n){return function(t,e){return typeof t=="string"&&typeof e=="string"||(t=dt(t),e=dt(e)),n(t,e)}}function ja(n,t,e,o,a,h,p,m,x,A){var C=t&xn,I=C?p:r,M=C?r:p,U=C?h:r,F=C?r:h;t|=C?Wn:wt,t&=~(C?wt:Wn),t&on||(t&=-4);var q=[n,t,a,U,I,F,M,m,x,A],B=e.apply(r,q);return au(n)&&cs(B,q),B.placeholder=o,hs(B,n,t)}function nu(n){var t=mn[n];return function(e,o){if(e=dt(e),o=o==null?0:Pn(G(o),292),o&&ha(e)){var a=(Q(e)+"e").split("e"),h=t(a[0]+"e"+(+a[1]+o));return a=(Q(h)+"e").split("e"),+(a[0]+"e"+(+a[1]-o))}return t(e)}}var lh=Qe&&1/li(new Qe([,-0]))[1]==he?function(n){return new Qe(n)}:wu;function ns(n){return function(t){var e=Mn(t);return e==An?Oo(t):e==un?Sl(t):yl(t,n(t))}}function Wt(n,t,e,o,a,h,p,m){var x=t&Ln;if(!x&&typeof n!="function")throw new st(v);var A=o?o.length:0;if(A||(t&=-97,o=a=r),p=p===r?p:yn(G(p),0),m=m===r?m:G(m),A-=a?a.length:0,t&wt){var C=o,I=a;o=a=r}var M=x?r:ru(n),U=[n,t,e,o,a,C,I,h,p,m];if(M&&Ah(U,M),n=U[0],t=U[1],e=U[2],o=U[3],a=U[4],m=U[9]=U[9]===r?x?0:n.length:yn(U[9]-A,0),!m&&t&(xn|Zn)&&(t&=-25),!t||t==En)var F=ah(n,t,e);else t==xn||t==Zn?F=sh(n,t,m):(t==Wn||t==(En|Wn))&&!a.length?F=fh(n,t,e,o):F=Mi.apply(r,U);var q=M?Da:cs;return hs(q(F,U),n,t)}function ts(n,t,e,o){return n===r||xt(n,Je[e])&&!j.call(o,e)?t:n}function es(n,t,e,o,a,h){return ln(n)&&ln(t)&&(h.set(t,n),Ii(n,t,r,es,h),h.delete(t)),n}function ch(n){return Br(n)?r:n}function rs(n,t,e,o,a,h){var p=e&Rn,m=n.length,x=t.length;if(m!=x&&!(p&&x>m))return!1;var A=h.get(n),C=h.get(t);if(A&&C)return A==t&&C==n;var I=-1,M=!0,U=e&fn?new be:r;for(h.set(n,t),h.set(t,n);++I<m;){var F=n[I],q=t[I];if(o)var B=p?o(q,F,I,t,n,h):o(F,q,I,n,t,h);if(B!==r){if(B)continue;M=!1;break}if(U){if(!bo(t,function(Z,K){if(!Ar(U,K)&&(F===Z||a(F,Z,e,o,h)))return U.push(K)})){M=!1;break}}else if(!(F===q||a(F,q,e,o,h))){M=!1;break}}return h.delete(n),h.delete(t),M}function hh(n,t,e,o,a,h,p){switch(e){case Ot:if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;n=n.buffer,t=t.buffer;case Vt:return!(n.byteLength!=t.byteLength||!h(new vi(n),new vi(t)));case $n:case tn:case pt:return xt(+n,+t);case Rt:return n.name==t.name&&n.message==t.message;case bt:case ge:return n==t+"";case An:var m=Oo;case un:var x=o&Rn;if(m||(m=li),n.size!=t.size&&!x)return!1;var A=p.get(n);if(A)return A==t;o|=fn,p.set(n,t);var C=rs(m(n),m(t),o,a,h,p);return p.delete(n),C;case Ue:if(Or)return Or.call(n)==Or.call(t)}return!1}function dh(n,t,e,o,a,h){var p=e&Rn,m=tu(n),x=m.length,A=tu(t),C=A.length;if(x!=C&&!p)return!1;for(var I=x;I--;){var M=m[I];if(!(p?M in t:j.call(t,M)))return!1}var U=h.get(n),F=h.get(t);if(U&&F)return U==t&&F==n;var q=!0;h.set(n,t),h.set(t,n);for(var B=p;++I<x;){M=m[I];var Z=n[M],K=t[M];if(o)var nt=p?o(K,Z,M,t,n,h):o(Z,K,M,n,t,h);if(!(nt===r?Z===K||a(Z,K,e,o,h):nt)){q=!1;break}B||(B=M=="constructor")}if(q&&!B){var Un=n.constructor,tt=t.constructor;Un!=tt&&"constructor"in n&&"constructor"in t&&!(typeof Un=="function"&&Un instanceof Un&&typeof tt=="function"&&tt instanceof tt)&&(q=!1)}return h.delete(n),h.delete(t),q}function Ft(n){return fu(fs(n,r,ms),n+"")}function tu(n){return Ea(n,wn,ou)}function eu(n){return Ea(n,Yn,is)}var ru=wi?function(n){return wi.get(n)}:wu;function Ui(n){for(var t=n.name+"",e=je[t],o=j.call(je,t)?e.length:0;o--;){var a=e[o],h=a.func;if(h==null||h==n)return a.name}return t}function rr(n){var t=j.call(c,"placeholder")?c:n;return t.placeholder}function W(){var n=c.iteratee||xu;return n=n===xu?Ca:n,arguments.length?n(arguments[0],arguments[1]):n}function Wi(n,t){var e=n.__data__;return Th(t)?e[typeof t=="string"?"string":"hash"]:e.map}function iu(n){for(var t=wn(n),e=t.length;e--;){var o=t[e],a=n[o];t[e]=[o,a,as(a)]}return t}function Se(n,t){var e=bl(n,t);return Aa(e)?e:r}function gh(n){var t=j.call(n,we),e=n[we];try{n[we]=r;var o=!0}catch{}var a=gi.call(n);return o&&(t?n[we]=e:delete n[we]),a}var ou=Mo?function(n){return n==null?[]:(n=nn(n),te(Mo(n),function(t){return la.call(n,t)}))}:Eu,is=Mo?function(n){for(var t=[];n;)ee(t,ou(n)),n=_i(n);return t}:Eu,Mn=Dn;(Lo&&Mn(new Lo(new ArrayBuffer(1)))!=Ot||Sr&&Mn(new Sr)!=An||Do&&Mn(Do.resolve())!=sr||Qe&&Mn(new Qe)!=un||Ir&&Mn(new Ir)!=pe)&&(Mn=function(n){var t=Dn(n),e=t==Cn?n.constructor:r,o=e?Ie(e):"";if(o)switch(o){case Vl:return Ot;case Jl:return An;case Ql:return sr;case jl:return un;case nc:return pe}return t});function ph(n,t,e){for(var o=-1,a=e.length;++o<a;){var h=e[o],p=h.size;switch(h.type){case"drop":n+=p;break;case"dropRight":t-=p;break;case"take":t=Pn(t,n+p);break;case"takeRight":n=yn(n,t-p);break}}return{start:n,end:t}}function vh(n){var t=n.match(go);return t?t[1].split(ti):[]}function os(n,t,e){t=ae(t,n);for(var o=-1,a=t.length,h=!1;++o<a;){var p=It(t[o]);if(!(h=n!=null&&e(n,p)))break;n=n[p]}return h||++o!=a?h:(a=n==null?0:n.length,!!a&&qi(a)&&Bt(p,a)&&(H(n)||Re(n)))}function _h(n){var t=n.length,e=new n.constructor(t);return t&&typeof n[0]=="string"&&j.call(n,"index")&&(e.index=n.index,e.input=n.input),e}function us(n){return typeof n.constructor=="function"&&!Wr(n)?nr(_i(n)):{}}function mh(n,t,e){var o=n.constructor;switch(t){case Vt:return Qo(n);case $n:case tn:return new o(+n);case Ot:return th(n,e);case We:case Fe:case Jt:case ve:case fr:case lr:case cr:case hr:case dr:return Ga(n,e);case An:return new o;case pt:case ge:return new o(n);case bt:return eh(n);case un:return new o;case Ue:return rh(n)}}function yh(n,t){var e=t.length;if(!e)return n;var o=e-1;return t[o]=(e>1?"& ":"")+t[o],t=t.join(e>2?", ":" "),n.replace(yr,`{
/* [wrapped with `+t+`] */
`)}function xh(n){return H(n)||Re(n)||!!(ca&&n&&n[ca])}function Bt(n,t){var e=typeof n;return t=t??Zt,!!t&&(e=="number"||e!="symbol"&&ze.test(n))&&n>-1&&n%1==0&&n<t}function Nn(n,t,e){if(!ln(e))return!1;var o=typeof t;return(o=="number"?Gn(e)&&Bt(t,e.length):o=="string"&&t in e)?xt(e[t],n):!1}function uu(n,t){if(H(n))return!1;var e=typeof n;return e=="number"||e=="symbol"||e=="boolean"||n==null||jn(n)?!0:lo.test(n)||!fo.test(n)||t!=null&&n in nn(t)}function Th(n){var t=typeof n;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?n!=="__proto__":n===null}function au(n){var t=Ui(n),e=c[t];if(typeof e!="function"||!(t in $.prototype))return!1;if(n===e)return!0;var o=ru(e);return!!o&&n===o[0]}function wh(n){return!!aa&&aa in n}var Eh=hi?zt:bu;function Wr(n){var t=n&&n.constructor,e=typeof t=="function"&&t.prototype||Je;return n===e}function as(n){return n===n&&!ln(n)}function ss(n,t){return function(e){return e==null?!1:e[n]===t&&(t!==r||n in nn(e))}}function bh(n){var t=Gi(n,function(o){return e.size===O&&e.clear(),o}),e=t.cache;return t}function Ah(n,t){var e=n[1],o=t[1],a=e|o,h=a<(En|Ln|Fn),p=o==Fn&&e==xn||o==Fn&&e==Xt&&n[7].length<=t[8]||o==(Fn|Xt)&&t[7].length<=t[8]&&e==xn;if(!(h||p))return n;o&En&&(n[2]=t[2],a|=e&En?0:on);var m=t[3];if(m){var x=n[3];n[3]=x?qa(x,m,t[4]):m,n[4]=x?re(n[3],P):t[4]}return m=t[5],m&&(x=n[5],n[5]=x?Xa(x,m,t[6]):m,n[6]=x?re(n[5],P):t[6]),m=t[7],m&&(n[7]=m),o&Fn&&(n[8]=n[8]==null?t[8]:Pn(n[8],t[8])),n[9]==null&&(n[9]=t[9]),n[0]=t[0],n[1]=a,n}function Ch(n){var t=[];if(n!=null)for(var e in nn(n))t.push(e);return t}function Sh(n){return gi.call(n)}function fs(n,t,e){return t=yn(t===r?n.length-1:t,0),function(){for(var o=arguments,a=-1,h=yn(o.length-t,0),p=E(h);++a<h;)p[a]=o[t+a];a=-1;for(var m=E(t+1);++a<t;)m[a]=o[a];return m[t]=e(p),Vn(n,this,m)}}function ls(n,t){return t.length<2?n:Ce(n,ct(t,0,-1))}function Ih(n,t){for(var e=n.length,o=Pn(t.length,e),a=Hn(n);o--;){var h=t[o];n[o]=Bt(h,e)?a[h]:r}return n}function su(n,t){if(!(t==="constructor"&&typeof n[t]=="function")&&t!="__proto__")return n[t]}var cs=ds(Da),Fr=Yl||function(n,t){return Sn.setTimeout(n,t)},fu=ds(Jc);function hs(n,t,e){var o=t+"";return fu(n,yh(o,Rh(vh(o),e)))}function ds(n){var t=0,e=0;return function(){var o=Zl(),a=Jr-(o-e);if(e=o,a>0){if(++t>=Le)return arguments[0]}else t=0;return n.apply(r,arguments)}}function Fi(n,t){var e=-1,o=n.length,a=o-1;for(t=t===r?o:t;++e<t;){var h=Xo(e,a),p=n[h];n[h]=n[e],n[e]=p}return n.length=t,n}var gs=bh(function(n){var t=[];return n.charCodeAt(0)===46&&t.push(""),n.replace(co,function(e,o,a,h){t.push(a?h.replace(ri,"$1"):o||e)}),t});function It(n){if(typeof n=="string"||jn(n))return n;var t=n+"";return t=="0"&&1/n==-1/0?"-0":t}function Ie(n){if(n!=null){try{return di.call(n)}catch{}try{return n+""}catch{}}return""}function Rh(n,t){return at(ar,function(e){var o="_."+e[0];t&e[1]&&!si(n,o)&&n.push(o)}),n.sort()}function ps(n){if(n instanceof $)return n.clone();var t=new ft(n.__wrapped__,n.__chain__);return t.__actions__=Hn(n.__actions__),t.__index__=n.__index__,t.__values__=n.__values__,t}function Oh(n,t,e){(e?Nn(n,t,e):t===r)?t=1:t=yn(G(t),0);var o=n==null?0:n.length;if(!o||t<1)return[];for(var a=0,h=0,p=E(xi(o/t));a<o;)p[h++]=ct(n,a,a+=t);return p}function Ph(n){for(var t=-1,e=n==null?0:n.length,o=0,a=[];++t<e;){var h=n[t];h&&(a[o++]=h)}return a}function Mh(){var n=arguments.length;if(!n)return[];for(var t=E(n-1),e=arguments[0],o=n;o--;)t[o-1]=arguments[o];return ee(H(e)?Hn(e):[e],In(t,1))}var Lh=X(function(n,t){return gn(n)?Mr(n,In(t,1,gn,!0)):[]}),Dh=X(function(n,t){var e=ht(t);return gn(e)&&(e=r),gn(n)?Mr(n,In(t,1,gn,!0),W(e,2)):[]}),Nh=X(function(n,t){var e=ht(t);return gn(e)&&(e=r),gn(n)?Mr(n,In(t,1,gn,!0),r,e):[]});function Uh(n,t,e){var o=n==null?0:n.length;return o?(t=e||t===r?1:G(t),ct(n,t<0?0:t,o)):[]}function Wh(n,t,e){var o=n==null?0:n.length;return o?(t=e||t===r?1:G(t),t=o-t,ct(n,0,t<0?0:t)):[]}function Fh(n,t){return n&&n.length?Oi(n,W(t,3),!0,!0):[]}function Bh(n,t){return n&&n.length?Oi(n,W(t,3),!0):[]}function zh(n,t,e,o){var a=n==null?0:n.length;return a?(e&&typeof e!="number"&&Nn(n,t,e)&&(e=0,o=a),Mc(n,t,e,o)):[]}function vs(n,t,e){var o=n==null?0:n.length;if(!o)return-1;var a=e==null?0:G(e);return a<0&&(a=yn(o+a,0)),fi(n,W(t,3),a)}function _s(n,t,e){var o=n==null?0:n.length;if(!o)return-1;var a=o-1;return e!==r&&(a=G(e),a=e<0?yn(o+a,0):Pn(a,o-1)),fi(n,W(t,3),a,!0)}function ms(n){var t=n==null?0:n.length;return t?In(n,1):[]}function Hh(n){var t=n==null?0:n.length;return t?In(n,he):[]}function Gh(n,t){var e=n==null?0:n.length;return e?(t=t===r?1:G(t),In(n,t)):[]}function Yh(n){for(var t=-1,e=n==null?0:n.length,o={};++t<e;){var a=n[t];o[a[0]]=a[1]}return o}function ys(n){return n&&n.length?n[0]:r}function qh(n,t,e){var o=n==null?0:n.length;if(!o)return-1;var a=e==null?0:G(e);return a<0&&(a=yn(o+a,0)),Ze(n,t,a)}function Xh(n){var t=n==null?0:n.length;return t?ct(n,0,-1):[]}var kh=X(function(n){var t=an(n,Vo);return t.length&&t[0]===n[0]?zo(t):[]}),Zh=X(function(n){var t=ht(n),e=an(n,Vo);return t===ht(e)?t=r:e.pop(),e.length&&e[0]===n[0]?zo(e,W(t,2)):[]}),$h=X(function(n){var t=ht(n),e=an(n,Vo);return t=typeof t=="function"?t:r,t&&e.pop(),e.length&&e[0]===n[0]?zo(e,r,t):[]});function Kh(n,t){return n==null?"":Xl.call(n,t)}function ht(n){var t=n==null?0:n.length;return t?n[t-1]:r}function Vh(n,t,e){var o=n==null?0:n.length;if(!o)return-1;var a=o;return e!==r&&(a=G(e),a=a<0?yn(o+a,0):Pn(a,o-1)),t===t?Rl(n,t,a):fi(n,ju,a,!0)}function Jh(n,t){return n&&n.length?Oa(n,G(t)):r}var Qh=X(xs);function xs(n,t){return n&&n.length&&t&&t.length?qo(n,t):n}function jh(n,t,e){return n&&n.length&&t&&t.length?qo(n,t,W(e,2)):n}function nd(n,t,e){return n&&n.length&&t&&t.length?qo(n,t,r,e):n}var td=Ft(function(n,t){var e=n==null?0:n.length,o=Uo(n,t);return La(n,an(t,function(a){return Bt(a,e)?+a:a}).sort(Ya)),o});function ed(n,t){var e=[];if(!(n&&n.length))return e;var o=-1,a=[],h=n.length;for(t=W(t,3);++o<h;){var p=n[o];t(p,o,n)&&(e.push(p),a.push(o))}return La(n,a),e}function lu(n){return n==null?n:Kl.call(n)}function rd(n,t,e){var o=n==null?0:n.length;return o?(e&&typeof e!="number"&&Nn(n,t,e)?(t=0,e=o):(t=t==null?0:G(t),e=e===r?o:G(e)),ct(n,t,e)):[]}function id(n,t){return Ri(n,t)}function od(n,t,e){return Zo(n,t,W(e,2))}function ud(n,t){var e=n==null?0:n.length;if(e){var o=Ri(n,t);if(o<e&&xt(n[o],t))return o}return-1}function ad(n,t){return Ri(n,t,!0)}function sd(n,t,e){return Zo(n,t,W(e,2),!0)}function fd(n,t){var e=n==null?0:n.length;if(e){var o=Ri(n,t,!0)-1;if(xt(n[o],t))return o}return-1}function ld(n){return n&&n.length?Na(n):[]}function cd(n,t){return n&&n.length?Na(n,W(t,2)):[]}function hd(n){var t=n==null?0:n.length;return t?ct(n,1,t):[]}function dd(n,t,e){return n&&n.length?(t=e||t===r?1:G(t),ct(n,0,t<0?0:t)):[]}function gd(n,t,e){var o=n==null?0:n.length;return o?(t=e||t===r?1:G(t),t=o-t,ct(n,t<0?0:t,o)):[]}function pd(n,t){return n&&n.length?Oi(n,W(t,3),!1,!0):[]}function vd(n,t){return n&&n.length?Oi(n,W(t,3)):[]}var _d=X(function(n){return ue(In(n,1,gn,!0))}),md=X(function(n){var t=ht(n);return gn(t)&&(t=r),ue(In(n,1,gn,!0),W(t,2))}),yd=X(function(n){var t=ht(n);return t=typeof t=="function"?t:r,ue(In(n,1,gn,!0),r,t)});function xd(n){return n&&n.length?ue(n):[]}function Td(n,t){return n&&n.length?ue(n,W(t,2)):[]}function wd(n,t){return t=typeof t=="function"?t:r,n&&n.length?ue(n,r,t):[]}function cu(n){if(!(n&&n.length))return[];var t=0;return n=te(n,function(e){if(gn(e))return t=yn(e.length,t),!0}),Io(t,function(e){return an(n,Ao(e))})}function Ts(n,t){if(!(n&&n.length))return[];var e=cu(n);return t==null?e:an(e,function(o){return Vn(t,r,o)})}var Ed=X(function(n,t){return gn(n)?Mr(n,t):[]}),bd=X(function(n){return Ko(te(n,gn))}),Ad=X(function(n){var t=ht(n);return gn(t)&&(t=r),Ko(te(n,gn),W(t,2))}),Cd=X(function(n){var t=ht(n);return t=typeof t=="function"?t:r,Ko(te(n,gn),r,t)}),Sd=X(cu);function Id(n,t){return Ba(n||[],t||[],Pr)}function Rd(n,t){return Ba(n||[],t||[],Nr)}var Od=X(function(n){var t=n.length,e=t>1?n[t-1]:r;return e=typeof e=="function"?(n.pop(),e):r,Ts(n,e)});function ws(n){var t=c(n);return t.__chain__=!0,t}function Pd(n,t){return t(n),n}function Bi(n,t){return t(n)}var Md=Ft(function(n){var t=n.length,e=t?n[0]:0,o=this.__wrapped__,a=function(h){return Uo(h,n)};return t>1||this.__actions__.length||!(o instanceof $)||!Bt(e)?this.thru(a):(o=o.slice(e,+e+(t?1:0)),o.__actions__.push({func:Bi,args:[a],thisArg:r}),new ft(o,this.__chain__).thru(function(h){return t&&!h.length&&h.push(r),h}))});function Ld(){return ws(this)}function Dd(){return new ft(this.value(),this.__chain__)}function Nd(){this.__values__===r&&(this.__values__=Us(this.value()));var n=this.__index__>=this.__values__.length,t=n?r:this.__values__[this.__index__++];return{done:n,value:t}}function Ud(){return this}function Wd(n){for(var t,e=this;e instanceof bi;){var o=ps(e);o.__index__=0,o.__values__=r,t?a.__wrapped__=o:t=o;var a=o;e=e.__wrapped__}return a.__wrapped__=n,t}function Fd(){var n=this.__wrapped__;if(n instanceof $){var t=n;return this.__actions__.length&&(t=new $(this)),t=t.reverse(),t.__actions__.push({func:Bi,args:[lu],thisArg:r}),new ft(t,this.__chain__)}return this.thru(lu)}function Bd(){return Fa(this.__wrapped__,this.__actions__)}var zd=Pi(function(n,t,e){j.call(n,e)?++n[e]:Ut(n,e,1)});function Hd(n,t,e){var o=H(n)?Ju:Pc;return e&&Nn(n,t,e)&&(t=r),o(n,W(t,3))}function Gd(n,t){var e=H(n)?te:Ta;return e(n,W(t,3))}var Yd=Ka(vs),qd=Ka(_s);function Xd(n,t){return In(zi(n,t),1)}function kd(n,t){return In(zi(n,t),he)}function Zd(n,t,e){return e=e===r?1:G(e),In(zi(n,t),e)}function Es(n,t){var e=H(n)?at:oe;return e(n,W(t,3))}function bs(n,t){var e=H(n)?hl:xa;return e(n,W(t,3))}var $d=Pi(function(n,t,e){j.call(n,e)?n[e].push(t):Ut(n,e,[t])});function Kd(n,t,e,o){n=Gn(n)?n:or(n),e=e&&!o?G(e):0;var a=n.length;return e<0&&(e=yn(a+e,0)),Xi(n)?e<=a&&n.indexOf(t,e)>-1:!!a&&Ze(n,t,e)>-1}var Vd=X(function(n,t,e){var o=-1,a=typeof t=="function",h=Gn(n)?E(n.length):[];return oe(n,function(p){h[++o]=a?Vn(t,p,e):Lr(p,t,e)}),h}),Jd=Pi(function(n,t,e){Ut(n,e,t)});function zi(n,t){var e=H(n)?an:Sa;return e(n,W(t,3))}function Qd(n,t,e,o){return n==null?[]:(H(t)||(t=t==null?[]:[t]),e=o?r:e,H(e)||(e=e==null?[]:[e]),Pa(n,t,e))}var jd=Pi(function(n,t,e){n[e?0:1].push(t)},function(){return[[],[]]});function ng(n,t,e){var o=H(n)?Eo:ta,a=arguments.length<3;return o(n,W(t,4),e,a,oe)}function tg(n,t,e){var o=H(n)?dl:ta,a=arguments.length<3;return o(n,W(t,4),e,a,xa)}function eg(n,t){var e=H(n)?te:Ta;return e(n,Yi(W(t,3)))}function rg(n){var t=H(n)?va:Kc;return t(n)}function ig(n,t,e){(e?Nn(n,t,e):t===r)?t=1:t=G(t);var o=H(n)?Cc:Vc;return o(n,t)}function og(n){var t=H(n)?Sc:Qc;return t(n)}function ug(n){if(n==null)return 0;if(Gn(n))return Xi(n)?Ke(n):n.length;var t=Mn(n);return t==An||t==un?n.size:Go(n).length}function ag(n,t,e){var o=H(n)?bo:jc;return e&&Nn(n,t,e)&&(t=r),o(n,W(t,3))}var sg=X(function(n,t){if(n==null)return[];var e=t.length;return e>1&&Nn(n,t[0],t[1])?t=[]:e>2&&Nn(t[0],t[1],t[2])&&(t=[t[0]]),Pa(n,In(t,1),[])}),Hi=Gl||function(){return Sn.Date.now()};function fg(n,t){if(typeof t!="function")throw new st(v);return n=G(n),function(){if(--n<1)return t.apply(this,arguments)}}function As(n,t,e){return t=e?r:t,t=n&&t==null?n.length:t,Wt(n,Fn,r,r,r,r,t)}function Cs(n,t){var e;if(typeof t!="function")throw new st(v);return n=G(n),function(){return--n>0&&(e=t.apply(this,arguments)),n<=1&&(t=r),e}}var hu=X(function(n,t,e){var o=En;if(e.length){var a=re(e,rr(hu));o|=Wn}return Wt(n,o,t,e,a)}),Ss=X(function(n,t,e){var o=En|Ln;if(e.length){var a=re(e,rr(Ss));o|=Wn}return Wt(t,o,n,e,a)});function Is(n,t,e){t=e?r:t;var o=Wt(n,xn,r,r,r,r,r,t);return o.placeholder=Is.placeholder,o}function Rs(n,t,e){t=e?r:t;var o=Wt(n,Zn,r,r,r,r,r,t);return o.placeholder=Rs.placeholder,o}function Os(n,t,e){var o,a,h,p,m,x,A=0,C=!1,I=!1,M=!0;if(typeof n!="function")throw new st(v);t=dt(t)||0,ln(e)&&(C=!!e.leading,I="maxWait"in e,h=I?yn(dt(e.maxWait)||0,t):h,M="trailing"in e?!!e.trailing:M);function U(pn){var Tt=o,Gt=a;return o=a=r,A=pn,p=n.apply(Gt,Tt),p}function F(pn){return A=pn,m=Fr(Z,t),C?U(pn):p}function q(pn){var Tt=pn-x,Gt=pn-A,Ks=t-Tt;return I?Pn(Ks,h-Gt):Ks}function B(pn){var Tt=pn-x,Gt=pn-A;return x===r||Tt>=t||Tt<0||I&&Gt>=h}function Z(){var pn=Hi();if(B(pn))return K(pn);m=Fr(Z,q(pn))}function K(pn){return m=r,M&&o?U(pn):(o=a=r,p)}function nt(){m!==r&&za(m),A=0,o=x=a=m=r}function Un(){return m===r?p:K(Hi())}function tt(){var pn=Hi(),Tt=B(pn);if(o=arguments,a=this,x=pn,Tt){if(m===r)return F(x);if(I)return za(m),m=Fr(Z,t),U(x)}return m===r&&(m=Fr(Z,t)),p}return tt.cancel=nt,tt.flush=Un,tt}var lg=X(function(n,t){return ya(n,1,t)}),cg=X(function(n,t,e){return ya(n,dt(t)||0,e)});function hg(n){return Wt(n,gt)}function Gi(n,t){if(typeof n!="function"||t!=null&&typeof t!="function")throw new st(v);var e=function(){var o=arguments,a=t?t.apply(this,o):o[0],h=e.cache;if(h.has(a))return h.get(a);var p=n.apply(this,o);return e.cache=h.set(a,p)||h,p};return e.cache=new(Gi.Cache||Nt),e}Gi.Cache=Nt;function Yi(n){if(typeof n!="function")throw new st(v);return function(){var t=arguments;switch(t.length){case 0:return!n.call(this);case 1:return!n.call(this,t[0]);case 2:return!n.call(this,t[0],t[1]);case 3:return!n.call(this,t[0],t[1],t[2])}return!n.apply(this,t)}}function dg(n){return Cs(2,n)}var gg=nh(function(n,t){t=t.length==1&&H(t[0])?an(t[0],Jn(W())):an(In(t,1),Jn(W()));var e=t.length;return X(function(o){for(var a=-1,h=Pn(o.length,e);++a<h;)o[a]=t[a].call(this,o[a]);return Vn(n,this,o)})}),du=X(function(n,t){var e=re(t,rr(du));return Wt(n,Wn,r,t,e)}),Ps=X(function(n,t){var e=re(t,rr(Ps));return Wt(n,wt,r,t,e)}),pg=Ft(function(n,t){return Wt(n,Xt,r,r,r,t)});function vg(n,t){if(typeof n!="function")throw new st(v);return t=t===r?t:G(t),X(n,t)}function _g(n,t){if(typeof n!="function")throw new st(v);return t=t==null?0:yn(G(t),0),X(function(e){var o=e[t],a=se(e,0,t);return o&&ee(a,o),Vn(n,this,a)})}function mg(n,t,e){var o=!0,a=!0;if(typeof n!="function")throw new st(v);return ln(e)&&(o="leading"in e?!!e.leading:o,a="trailing"in e?!!e.trailing:a),Os(n,t,{leading:o,maxWait:t,trailing:a})}function yg(n){return As(n,1)}function xg(n,t){return du(Jo(t),n)}function Tg(){if(!arguments.length)return[];var n=arguments[0];return H(n)?n:[n]}function wg(n){return lt(n,V)}function Eg(n,t){return t=typeof t=="function"?t:r,lt(n,V,t)}function bg(n){return lt(n,N|V)}function Ag(n,t){return t=typeof t=="function"?t:r,lt(n,N|V,t)}function Cg(n,t){return t==null||ma(n,t,wn(t))}function xt(n,t){return n===t||n!==n&&t!==t}var Sg=Ni(Bo),Ig=Ni(function(n,t){return n>=t}),Re=ba(function(){return arguments}())?ba:function(n){return hn(n)&&j.call(n,"callee")&&!la.call(n,"callee")},H=E.isArray,Rg=Xu?Jn(Xu):Wc;function Gn(n){return n!=null&&qi(n.length)&&!zt(n)}function gn(n){return hn(n)&&Gn(n)}function Og(n){return n===!0||n===!1||hn(n)&&Dn(n)==$n}var fe=ql||bu,Pg=ku?Jn(ku):Fc;function Mg(n){return hn(n)&&n.nodeType===1&&!Br(n)}function Lg(n){if(n==null)return!0;if(Gn(n)&&(H(n)||typeof n=="string"||typeof n.splice=="function"||fe(n)||ir(n)||Re(n)))return!n.length;var t=Mn(n);if(t==An||t==un)return!n.size;if(Wr(n))return!Go(n).length;for(var e in n)if(j.call(n,e))return!1;return!0}function Dg(n,t){return Dr(n,t)}function Ng(n,t,e){e=typeof e=="function"?e:r;var o=e?e(n,t):r;return o===r?Dr(n,t,r,e):!!o}function gu(n){if(!hn(n))return!1;var t=Dn(n);return t==Rt||t==On||typeof n.message=="string"&&typeof n.name=="string"&&!Br(n)}function Ug(n){return typeof n=="number"&&ha(n)}function zt(n){if(!ln(n))return!1;var t=Dn(n);return t==Et||t==Kt||t==bn||t==jr}function Ms(n){return typeof n=="number"&&n==G(n)}function qi(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=Zt}function ln(n){var t=typeof n;return n!=null&&(t=="object"||t=="function")}function hn(n){return n!=null&&typeof n=="object"}var Ls=Zu?Jn(Zu):zc;function Wg(n,t){return n===t||Ho(n,t,iu(t))}function Fg(n,t,e){return e=typeof e=="function"?e:r,Ho(n,t,iu(t),e)}function Bg(n){return Ds(n)&&n!=+n}function zg(n){if(Eh(n))throw new z(g);return Aa(n)}function Hg(n){return n===null}function Gg(n){return n==null}function Ds(n){return typeof n=="number"||hn(n)&&Dn(n)==pt}function Br(n){if(!hn(n)||Dn(n)!=Cn)return!1;var t=_i(n);if(t===null)return!0;var e=j.call(t,"constructor")&&t.constructor;return typeof e=="function"&&e instanceof e&&di.call(e)==Fl}var pu=$u?Jn($u):Hc;function Yg(n){return Ms(n)&&n>=-9007199254740991&&n<=Zt}var Ns=Ku?Jn(Ku):Gc;function Xi(n){return typeof n=="string"||!H(n)&&hn(n)&&Dn(n)==ge}function jn(n){return typeof n=="symbol"||hn(n)&&Dn(n)==Ue}var ir=Vu?Jn(Vu):Yc;function qg(n){return n===r}function Xg(n){return hn(n)&&Mn(n)==pe}function kg(n){return hn(n)&&Dn(n)==ro}var Zg=Ni(Yo),$g=Ni(function(n,t){return n<=t});function Us(n){if(!n)return[];if(Gn(n))return Xi(n)?mt(n):Hn(n);if(Cr&&n[Cr])return Cl(n[Cr]());var t=Mn(n),e=t==An?Oo:t==un?li:or;return e(n)}function Ht(n){if(!n)return n===0?n:0;if(n=dt(n),n===he||n===-1/0){var t=n<0?-1:1;return t*Qr}return n===n?n:0}function G(n){var t=Ht(n),e=t%1;return t===t?e?t-e:t:0}function Ws(n){return n?Ae(G(n),0,et):0}function dt(n){if(typeof n=="number")return n;if(jn(n))return De;if(ln(n)){var t=typeof n.valueOf=="function"?n.valueOf():n;n=ln(t)?t+"":t}if(typeof n!="string")return n===0?n:+n;n=ea(n);var e=Pt.test(n);return e||me.test(n)?fl(n.slice(2),e?2:8):Tr.test(n)?De:+n}function Fs(n){return St(n,Yn(n))}function Kg(n){return n?Ae(G(n),-9007199254740991,Zt):n===0?n:0}function Q(n){return n==null?"":Qn(n)}var Vg=tr(function(n,t){if(Wr(t)||Gn(t)){St(t,wn(t),n);return}for(var e in t)j.call(t,e)&&Pr(n,e,t[e])}),Bs=tr(function(n,t){St(t,Yn(t),n)}),ki=tr(function(n,t,e,o){St(t,Yn(t),n,o)}),Jg=tr(function(n,t,e,o){St(t,wn(t),n,o)}),Qg=Ft(Uo);function jg(n,t){var e=nr(n);return t==null?e:_a(e,t)}var np=X(function(n,t){n=nn(n);var e=-1,o=t.length,a=o>2?t[2]:r;for(a&&Nn(t[0],t[1],a)&&(o=1);++e<o;)for(var h=t[e],p=Yn(h),m=-1,x=p.length;++m<x;){var A=p[m],C=n[A];(C===r||xt(C,Je[A])&&!j.call(n,A))&&(n[A]=h[A])}return n}),tp=X(function(n){return n.push(r,es),Vn(zs,r,n)});function ep(n,t){return Qu(n,W(t,3),Ct)}function rp(n,t){return Qu(n,W(t,3),Fo)}function ip(n,t){return n==null?n:Wo(n,W(t,3),Yn)}function op(n,t){return n==null?n:wa(n,W(t,3),Yn)}function up(n,t){return n&&Ct(n,W(t,3))}function ap(n,t){return n&&Fo(n,W(t,3))}function sp(n){return n==null?[]:Si(n,wn(n))}function fp(n){return n==null?[]:Si(n,Yn(n))}function vu(n,t,e){var o=n==null?r:Ce(n,t);return o===r?e:o}function lp(n,t){return n!=null&&os(n,t,Lc)}function _u(n,t){return n!=null&&os(n,t,Dc)}var cp=Ja(function(n,t,e){t!=null&&typeof t.toString!="function"&&(t=gi.call(t)),n[t]=e},yu(qn)),hp=Ja(function(n,t,e){t!=null&&typeof t.toString!="function"&&(t=gi.call(t)),j.call(n,t)?n[t].push(e):n[t]=[e]},W),dp=X(Lr);function wn(n){return Gn(n)?pa(n):Go(n)}function Yn(n){return Gn(n)?pa(n,!0):qc(n)}function gp(n,t){var e={};return t=W(t,3),Ct(n,function(o,a,h){Ut(e,t(o,a,h),o)}),e}function pp(n,t){var e={};return t=W(t,3),Ct(n,function(o,a,h){Ut(e,a,t(o,a,h))}),e}var vp=tr(function(n,t,e){Ii(n,t,e)}),zs=tr(function(n,t,e,o){Ii(n,t,e,o)}),_p=Ft(function(n,t){var e={};if(n==null)return e;var o=!1;t=an(t,function(h){return h=ae(h,n),o||(o=h.length>1),h}),St(n,eu(n),e),o&&(e=lt(e,N|k|V,ch));for(var a=t.length;a--;)$o(e,t[a]);return e});function mp(n,t){return Hs(n,Yi(W(t)))}var yp=Ft(function(n,t){return n==null?{}:kc(n,t)});function Hs(n,t){if(n==null)return{};var e=an(eu(n),function(o){return[o]});return t=W(t),Ma(n,e,function(o,a){return t(o,a[0])})}function xp(n,t,e){t=ae(t,n);var o=-1,a=t.length;for(a||(a=1,n=r);++o<a;){var h=n==null?r:n[It(t[o])];h===r&&(o=a,h=e),n=zt(h)?h.call(n):h}return n}function Tp(n,t,e){return n==null?n:Nr(n,t,e)}function wp(n,t,e,o){return o=typeof o=="function"?o:r,n==null?n:Nr(n,t,e,o)}var Gs=ns(wn),Ys=ns(Yn);function Ep(n,t,e){var o=H(n),a=o||fe(n)||ir(n);if(t=W(t,4),e==null){var h=n&&n.constructor;a?e=o?new h:[]:ln(n)?e=zt(h)?nr(_i(n)):{}:e={}}return(a?at:Ct)(n,function(p,m,x){return t(e,p,m,x)}),e}function bp(n,t){return n==null?!0:$o(n,t)}function Ap(n,t,e){return n==null?n:Wa(n,t,Jo(e))}function Cp(n,t,e,o){return o=typeof o=="function"?o:r,n==null?n:Wa(n,t,Jo(e),o)}function or(n){return n==null?[]:Ro(n,wn(n))}function Sp(n){return n==null?[]:Ro(n,Yn(n))}function Ip(n,t,e){return e===r&&(e=t,t=r),e!==r&&(e=dt(e),e=e===e?e:0),t!==r&&(t=dt(t),t=t===t?t:0),Ae(dt(n),t,e)}function Rp(n,t,e){return t=Ht(t),e===r?(e=t,t=0):e=Ht(e),n=dt(n),Nc(n,t,e)}function Op(n,t,e){if(e&&typeof e!="boolean"&&Nn(n,t,e)&&(t=e=r),e===r&&(typeof t=="boolean"?(e=t,t=r):typeof n=="boolean"&&(e=n,n=r)),n===r&&t===r?(n=0,t=1):(n=Ht(n),t===r?(t=n,n=0):t=Ht(t)),n>t){var o=n;n=t,t=o}if(e||n%1||t%1){var a=da();return Pn(n+a*(t-n+sl("1e-"+((a+"").length-1))),t)}return Xo(n,t)}var Pp=er(function(n,t,e){return t=t.toLowerCase(),n+(e?qs(t):t)});function qs(n){return mu(Q(n).toLowerCase())}function Xs(n){return n=Q(n),n&&n.replace(wr,Tl).replace(Qf,"")}function Mp(n,t,e){n=Q(n),t=Qn(t);var o=n.length;e=e===r?o:Ae(G(e),0,o);var a=e;return e-=t.length,e>=0&&n.slice(e,a)==t}function Lp(n){return n=Q(n),n&&uo.test(n)?n.replace(pr,wl):n}function Dp(n){return n=Q(n),n&&ho.test(n)?n.replace(Qt,"\\$&"):n}var Np=er(function(n,t,e){return n+(e?"-":"")+t.toLowerCase()}),Up=er(function(n,t,e){return n+(e?" ":"")+t.toLowerCase()}),Wp=$a("toLowerCase");function Fp(n,t,e){n=Q(n),t=G(t);var o=t?Ke(n):0;if(!t||o>=t)return n;var a=(t-o)/2;return Di(Ti(a),e)+n+Di(xi(a),e)}function Bp(n,t,e){n=Q(n),t=G(t);var o=t?Ke(n):0;return t&&o<t?n+Di(t-o,e):n}function zp(n,t,e){n=Q(n),t=G(t);var o=t?Ke(n):0;return t&&o<t?Di(t-o,e)+n:n}function Hp(n,t,e){return e||t==null?t=0:t&&(t=+t),$l(Q(n).replace(mr,""),t||0)}function Gp(n,t,e){return(e?Nn(n,t,e):t===r)?t=1:t=G(t),ko(Q(n),t)}function Yp(){var n=arguments,t=Q(n[0]);return n.length<3?t:t.replace(n[1],n[2])}var qp=er(function(n,t,e){return n+(e?"_":"")+t.toLowerCase()});function Xp(n,t,e){return e&&typeof e!="number"&&Nn(n,t,e)&&(t=e=r),e=e===r?et:e>>>0,e?(n=Q(n),n&&(typeof t=="string"||t!=null&&!pu(t))&&(t=Qn(t),!t&&$e(n))?se(mt(n),0,e):n.split(t,e)):[]}var kp=er(function(n,t,e){return n+(e?" ":"")+mu(t)});function Zp(n,t,e){return n=Q(n),e=e==null?0:Ae(G(e),0,n.length),t=Qn(t),n.slice(e,e+t.length)==t}function $p(n,t,e){var o=c.templateSettings;e&&Nn(n,t,e)&&(t=r),n=Q(n),t=ki({},t,o,ts);var a=ki({},t.imports,o.imports,ts),h=wn(a),p=Ro(a,h),m,x,A=0,C=t.interpolate||He,I="__p += '",M=Po((t.escape||He).source+"|"+C.source+"|"+(C===_r?ii:He).source+"|"+(t.evaluate||He).source+"|$","g"),U="//# sourceURL="+(j.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++rl+"]")+`
`;n.replace(M,function(B,Z,K,nt,Un,tt){return K||(K=nt),I+=n.slice(A,tt).replace(vo,El),Z&&(m=!0,I+=`' +
__e(`+Z+`) +
'`),Un&&(x=!0,I+=`';
`+Un+`;
__p += '`),K&&(I+=`' +
((__t = (`+K+`)) == null ? '' : __t) +
'`),A=tt+B.length,B}),I+=`';
`;var F=j.call(t,"variable")&&t.variable;if(!F)I=`with (obj) {
`+I+`
}
`;else if(ei.test(F))throw new z(w);I=(x?I.replace(Be,""):I).replace(io,"$1").replace(oo,"$1;"),I="function("+(F||"obj")+`) {
`+(F?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(m?", __e = _.escape":"")+(x?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+I+`return __p
}`;var q=Zs(function(){return J(h,U+"return "+I).apply(r,p)});if(q.source=I,gu(q))throw q;return q}function Kp(n){return Q(n).toLowerCase()}function Vp(n){return Q(n).toUpperCase()}function Jp(n,t,e){if(n=Q(n),n&&(e||t===r))return ea(n);if(!n||!(t=Qn(t)))return n;var o=mt(n),a=mt(t),h=ra(o,a),p=ia(o,a)+1;return se(o,h,p).join("")}function Qp(n,t,e){if(n=Q(n),n&&(e||t===r))return n.slice(0,ua(n)+1);if(!n||!(t=Qn(t)))return n;var o=mt(n),a=ia(o,mt(t))+1;return se(o,0,a).join("")}function jp(n,t,e){if(n=Q(n),n&&(e||t===r))return n.replace(mr,"");if(!n||!(t=Qn(t)))return n;var o=mt(n),a=ra(o,mt(t));return se(o,a).join("")}function nv(n,t){var e=Me,o=kt;if(ln(t)){var a="separator"in t?t.separator:a;e="length"in t?G(t.length):e,o="omission"in t?Qn(t.omission):o}n=Q(n);var h=n.length;if($e(n)){var p=mt(n);h=p.length}if(e>=h)return n;var m=e-Ke(o);if(m<1)return o;var x=p?se(p,0,m).join(""):n.slice(0,m);if(a===r)return x+o;if(p&&(m+=x.length-m),pu(a)){if(n.slice(m).search(a)){var A,C=x;for(a.global||(a=Po(a.source,Q(xr.exec(a))+"g")),a.lastIndex=0;A=a.exec(C);)var I=A.index;x=x.slice(0,I===r?m:I)}}else if(n.indexOf(Qn(a),m)!=m){var M=x.lastIndexOf(a);M>-1&&(x=x.slice(0,M))}return x+o}function tv(n){return n=Q(n),n&&vr.test(n)?n.replace(gr,Ol):n}var ev=er(function(n,t,e){return n+(e?" ":"")+t.toUpperCase()}),mu=$a("toUpperCase");function ks(n,t,e){return n=Q(n),t=e?r:t,t===r?Al(n)?Ll(n):vl(n):n.match(t)||[]}var Zs=X(function(n,t){try{return Vn(n,r,t)}catch(e){return gu(e)?e:new z(e)}}),rv=Ft(function(n,t){return at(t,function(e){e=It(e),Ut(n,e,hu(n[e],n))}),n});function iv(n){var t=n==null?0:n.length,e=W();return n=t?an(n,function(o){if(typeof o[1]!="function")throw new st(v);return[e(o[0]),o[1]]}):[],X(function(o){for(var a=-1;++a<t;){var h=n[a];if(Vn(h[0],this,o))return Vn(h[1],this,o)}})}function ov(n){return Oc(lt(n,N))}function yu(n){return function(){return n}}function uv(n,t){return n==null||n!==n?t:n}var av=Va(),sv=Va(!0);function qn(n){return n}function xu(n){return Ca(typeof n=="function"?n:lt(n,N))}function fv(n){return Ia(lt(n,N))}function lv(n,t){return Ra(n,lt(t,N))}var cv=X(function(n,t){return function(e){return Lr(e,n,t)}}),hv=X(function(n,t){return function(e){return Lr(n,e,t)}});function Tu(n,t,e){var o=wn(t),a=Si(t,o);e==null&&!(ln(t)&&(a.length||!o.length))&&(e=t,t=n,n=this,a=Si(t,wn(t)));var h=!(ln(e)&&"chain"in e)||!!e.chain,p=zt(n);return at(a,function(m){var x=t[m];n[m]=x,p&&(n.prototype[m]=function(){var A=this.__chain__;if(h||A){var C=n(this.__wrapped__),I=C.__actions__=Hn(this.__actions__);return I.push({func:x,args:arguments,thisArg:n}),C.__chain__=A,C}return x.apply(n,ee([this.value()],arguments))})}),n}function dv(){return Sn._===this&&(Sn._=Bl),this}function wu(){}function gv(n){return n=G(n),X(function(t){return Oa(t,n)})}var pv=jo(an),vv=jo(Ju),_v=jo(bo);function $s(n){return uu(n)?Ao(It(n)):Zc(n)}function mv(n){return function(t){return n==null?r:Ce(n,t)}}var yv=Qa(),xv=Qa(!0);function Eu(){return[]}function bu(){return!1}function Tv(){return{}}function wv(){return""}function Ev(){return!0}function bv(n,t){if(n=G(n),n<1||n>Zt)return[];var e=et,o=Pn(n,et);t=W(t),n-=et;for(var a=Io(o,t);++e<n;)t(e);return a}function Av(n){return H(n)?an(n,It):jn(n)?[n]:Hn(gs(Q(n)))}function Cv(n){var t=++Wl;return Q(n)+t}var Sv=Li(function(n,t){return n+t},0),Iv=nu("ceil"),Rv=Li(function(n,t){return n/t},1),Ov=nu("floor");function Pv(n){return n&&n.length?Ci(n,qn,Bo):r}function Mv(n,t){return n&&n.length?Ci(n,W(t,2),Bo):r}function Lv(n){return na(n,qn)}function Dv(n,t){return na(n,W(t,2))}function Nv(n){return n&&n.length?Ci(n,qn,Yo):r}function Uv(n,t){return n&&n.length?Ci(n,W(t,2),Yo):r}var Wv=Li(function(n,t){return n*t},1),Fv=nu("round"),Bv=Li(function(n,t){return n-t},0);function zv(n){return n&&n.length?So(n,qn):0}function Hv(n,t){return n&&n.length?So(n,W(t,2)):0}return c.after=fg,c.ary=As,c.assign=Vg,c.assignIn=Bs,c.assignInWith=ki,c.assignWith=Jg,c.at=Qg,c.before=Cs,c.bind=hu,c.bindAll=rv,c.bindKey=Ss,c.castArray=Tg,c.chain=ws,c.chunk=Oh,c.compact=Ph,c.concat=Mh,c.cond=iv,c.conforms=ov,c.constant=yu,c.countBy=zd,c.create=jg,c.curry=Is,c.curryRight=Rs,c.debounce=Os,c.defaults=np,c.defaultsDeep=tp,c.defer=lg,c.delay=cg,c.difference=Lh,c.differenceBy=Dh,c.differenceWith=Nh,c.drop=Uh,c.dropRight=Wh,c.dropRightWhile=Fh,c.dropWhile=Bh,c.fill=zh,c.filter=Gd,c.flatMap=Xd,c.flatMapDeep=kd,c.flatMapDepth=Zd,c.flatten=ms,c.flattenDeep=Hh,c.flattenDepth=Gh,c.flip=hg,c.flow=av,c.flowRight=sv,c.fromPairs=Yh,c.functions=sp,c.functionsIn=fp,c.groupBy=$d,c.initial=Xh,c.intersection=kh,c.intersectionBy=Zh,c.intersectionWith=$h,c.invert=cp,c.invertBy=hp,c.invokeMap=Vd,c.iteratee=xu,c.keyBy=Jd,c.keys=wn,c.keysIn=Yn,c.map=zi,c.mapKeys=gp,c.mapValues=pp,c.matches=fv,c.matchesProperty=lv,c.memoize=Gi,c.merge=vp,c.mergeWith=zs,c.method=cv,c.methodOf=hv,c.mixin=Tu,c.negate=Yi,c.nthArg=gv,c.omit=_p,c.omitBy=mp,c.once=dg,c.orderBy=Qd,c.over=pv,c.overArgs=gg,c.overEvery=vv,c.overSome=_v,c.partial=du,c.partialRight=Ps,c.partition=jd,c.pick=yp,c.pickBy=Hs,c.property=$s,c.propertyOf=mv,c.pull=Qh,c.pullAll=xs,c.pullAllBy=jh,c.pullAllWith=nd,c.pullAt=td,c.range=yv,c.rangeRight=xv,c.rearg=pg,c.reject=eg,c.remove=ed,c.rest=vg,c.reverse=lu,c.sampleSize=ig,c.set=Tp,c.setWith=wp,c.shuffle=og,c.slice=rd,c.sortBy=sg,c.sortedUniq=ld,c.sortedUniqBy=cd,c.split=Xp,c.spread=_g,c.tail=hd,c.take=dd,c.takeRight=gd,c.takeRightWhile=pd,c.takeWhile=vd,c.tap=Pd,c.throttle=mg,c.thru=Bi,c.toArray=Us,c.toPairs=Gs,c.toPairsIn=Ys,c.toPath=Av,c.toPlainObject=Fs,c.transform=Ep,c.unary=yg,c.union=_d,c.unionBy=md,c.unionWith=yd,c.uniq=xd,c.uniqBy=Td,c.uniqWith=wd,c.unset=bp,c.unzip=cu,c.unzipWith=Ts,c.update=Ap,c.updateWith=Cp,c.values=or,c.valuesIn=Sp,c.without=Ed,c.words=ks,c.wrap=xg,c.xor=bd,c.xorBy=Ad,c.xorWith=Cd,c.zip=Sd,c.zipObject=Id,c.zipObjectDeep=Rd,c.zipWith=Od,c.entries=Gs,c.entriesIn=Ys,c.extend=Bs,c.extendWith=ki,Tu(c,c),c.add=Sv,c.attempt=Zs,c.camelCase=Pp,c.capitalize=qs,c.ceil=Iv,c.clamp=Ip,c.clone=wg,c.cloneDeep=bg,c.cloneDeepWith=Ag,c.cloneWith=Eg,c.conformsTo=Cg,c.deburr=Xs,c.defaultTo=uv,c.divide=Rv,c.endsWith=Mp,c.eq=xt,c.escape=Lp,c.escapeRegExp=Dp,c.every=Hd,c.find=Yd,c.findIndex=vs,c.findKey=ep,c.findLast=qd,c.findLastIndex=_s,c.findLastKey=rp,c.floor=Ov,c.forEach=Es,c.forEachRight=bs,c.forIn=ip,c.forInRight=op,c.forOwn=up,c.forOwnRight=ap,c.get=vu,c.gt=Sg,c.gte=Ig,c.has=lp,c.hasIn=_u,c.head=ys,c.identity=qn,c.includes=Kd,c.indexOf=qh,c.inRange=Rp,c.invoke=dp,c.isArguments=Re,c.isArray=H,c.isArrayBuffer=Rg,c.isArrayLike=Gn,c.isArrayLikeObject=gn,c.isBoolean=Og,c.isBuffer=fe,c.isDate=Pg,c.isElement=Mg,c.isEmpty=Lg,c.isEqual=Dg,c.isEqualWith=Ng,c.isError=gu,c.isFinite=Ug,c.isFunction=zt,c.isInteger=Ms,c.isLength=qi,c.isMap=Ls,c.isMatch=Wg,c.isMatchWith=Fg,c.isNaN=Bg,c.isNative=zg,c.isNil=Gg,c.isNull=Hg,c.isNumber=Ds,c.isObject=ln,c.isObjectLike=hn,c.isPlainObject=Br,c.isRegExp=pu,c.isSafeInteger=Yg,c.isSet=Ns,c.isString=Xi,c.isSymbol=jn,c.isTypedArray=ir,c.isUndefined=qg,c.isWeakMap=Xg,c.isWeakSet=kg,c.join=Kh,c.kebabCase=Np,c.last=ht,c.lastIndexOf=Vh,c.lowerCase=Up,c.lowerFirst=Wp,c.lt=Zg,c.lte=$g,c.max=Pv,c.maxBy=Mv,c.mean=Lv,c.meanBy=Dv,c.min=Nv,c.minBy=Uv,c.stubArray=Eu,c.stubFalse=bu,c.stubObject=Tv,c.stubString=wv,c.stubTrue=Ev,c.multiply=Wv,c.nth=Jh,c.noConflict=dv,c.noop=wu,c.now=Hi,c.pad=Fp,c.padEnd=Bp,c.padStart=zp,c.parseInt=Hp,c.random=Op,c.reduce=ng,c.reduceRight=tg,c.repeat=Gp,c.replace=Yp,c.result=xp,c.round=Fv,c.runInContext=y,c.sample=rg,c.size=ug,c.snakeCase=qp,c.some=ag,c.sortedIndex=id,c.sortedIndexBy=od,c.sortedIndexOf=ud,c.sortedLastIndex=ad,c.sortedLastIndexBy=sd,c.sortedLastIndexOf=fd,c.startCase=kp,c.startsWith=Zp,c.subtract=Bv,c.sum=zv,c.sumBy=Hv,c.template=$p,c.times=bv,c.toFinite=Ht,c.toInteger=G,c.toLength=Ws,c.toLower=Kp,c.toNumber=dt,c.toSafeInteger=Kg,c.toString=Q,c.toUpper=Vp,c.trim=Jp,c.trimEnd=Qp,c.trimStart=jp,c.truncate=nv,c.unescape=tv,c.uniqueId=Cv,c.upperCase=ev,c.upperFirst=mu,c.each=Es,c.eachRight=bs,c.first=ys,Tu(c,function(){var n={};return Ct(c,function(t,e){j.call(c.prototype,e)||(n[e]=t)}),n}(),{chain:!1}),c.VERSION=s,at(["bind","bindKey","curry","curryRight","partial","partialRight"],function(n){c[n].placeholder=c}),at(["drop","take"],function(n,t){$.prototype[n]=function(e){e=e===r?1:yn(G(e),0);var o=this.__filtered__&&!t?new $(this):this.clone();return o.__filtered__?o.__takeCount__=Pn(e,o.__takeCount__):o.__views__.push({size:Pn(e,et),type:n+(o.__dir__<0?"Right":"")}),o},$.prototype[n+"Right"]=function(e){return this.reverse()[n](e).reverse()}}),at(["filter","map","takeWhile"],function(n,t){var e=t+1,o=e==ce||e==no;$.prototype[n]=function(a){var h=this.clone();return h.__iteratees__.push({iteratee:W(a,3),type:e}),h.__filtered__=h.__filtered__||o,h}}),at(["head","last"],function(n,t){var e="take"+(t?"Right":"");$.prototype[n]=function(){return this[e](1).value()[0]}}),at(["initial","tail"],function(n,t){var e="drop"+(t?"":"Right");$.prototype[n]=function(){return this.__filtered__?new $(this):this[e](1)}}),$.prototype.compact=function(){return this.filter(qn)},$.prototype.find=function(n){return this.filter(n).head()},$.prototype.findLast=function(n){return this.reverse().find(n)},$.prototype.invokeMap=X(function(n,t){return typeof n=="function"?new $(this):this.map(function(e){return Lr(e,n,t)})}),$.prototype.reject=function(n){return this.filter(Yi(W(n)))},$.prototype.slice=function(n,t){n=G(n);var e=this;return e.__filtered__&&(n>0||t<0)?new $(e):(n<0?e=e.takeRight(-n):n&&(e=e.drop(n)),t!==r&&(t=G(t),e=t<0?e.dropRight(-t):e.take(t-n)),e)},$.prototype.takeRightWhile=function(n){return this.reverse().takeWhile(n).reverse()},$.prototype.toArray=function(){return this.take(et)},Ct($.prototype,function(n,t){var e=/^(?:filter|find|map|reject)|While$/.test(t),o=/^(?:head|last)$/.test(t),a=c[o?"take"+(t=="last"?"Right":""):t],h=o||/^find/.test(t);a&&(c.prototype[t]=function(){var p=this.__wrapped__,m=o?[1]:arguments,x=p instanceof $,A=m[0],C=x||H(p),I=function(Z){var K=a.apply(c,ee([Z],m));return o&&M?K[0]:K};C&&e&&typeof A=="function"&&A.length!=1&&(x=C=!1);var M=this.__chain__,U=!!this.__actions__.length,F=h&&!M,q=x&&!U;if(!h&&C){p=q?p:new $(this);var B=n.apply(p,m);return B.__actions__.push({func:Bi,args:[I],thisArg:r}),new ft(B,M)}return F&&q?n.apply(this,m):(B=this.thru(I),F?o?B.value()[0]:B.value():B)})}),at(["pop","push","shift","sort","splice","unshift"],function(n){var t=ci[n],e=/^(?:push|sort|unshift)$/.test(n)?"tap":"thru",o=/^(?:pop|shift)$/.test(n);c.prototype[n]=function(){var a=arguments;if(o&&!this.__chain__){var h=this.value();return t.apply(H(h)?h:[],a)}return this[e](function(p){return t.apply(H(p)?p:[],a)})}}),Ct($.prototype,function(n,t){var e=c[t];if(e){var o=e.name+"";j.call(je,o)||(je[o]=[]),je[o].push({name:t,func:e})}}),je[Mi(r,Ln).name]=[{name:"wrapper",func:r}],$.prototype.clone=tc,$.prototype.reverse=ec,$.prototype.value=rc,c.prototype.at=Md,c.prototype.chain=Ld,c.prototype.commit=Dd,c.prototype.next=Nd,c.prototype.plant=Wd,c.prototype.reverse=Fd,c.prototype.toJSON=c.prototype.valueOf=c.prototype.value=Bd,c.prototype.first=c.prototype.head,Cr&&(c.prototype[Cr]=Ud),c},Ve=Dl();Te?((Te.exports=Ve)._=Ve,xo._=Ve):Sn._=Ve}).call($v)}(Gr,Gr.exports)),Gr.exports}var Vv=Kv(),Au={exports:{}};/*! Hammer.JS - v2.0.7 - 2016-04-22
 * http://hammerjs.github.io/
 *
 * Copyright (c) 2016 Jorik Tangelder;
 * Licensed under the MIT license */var js;function Jv(){return js||(js=1,function(i){(function(u,r,s,l){var g=["","webkit","Moz","MS","ms","o"],v=r.createElement("div"),w="function",S=Math.round,O=Math.abs,P=Date.now;function N(f,d,_){return setTimeout(xn(f,_),d)}function k(f,d,_){return Array.isArray(f)?(V(f,_[d],_),!0):!1}function V(f,d,_){var T;if(f)if(f.forEach)f.forEach(d,_);else if(f.length!==l)for(T=0;T<f.length;)d.call(_,f[T],T,f),T++;else for(T in f)f.hasOwnProperty(T)&&d.call(_,f[T],T,f)}function Rn(f,d,_){var T="DEPRECATED METHOD: "+d+`
`+_+` AT 
`;return function(){var R=new Error("get-stack-trace"),L=R&&R.stack?R.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",Y=u.console&&(u.console.warn||u.console.log);return Y&&Y.call(u.console,T,L),f.apply(this,arguments)}}var fn;typeof Object.assign!="function"?fn=function(d){if(d===l||d===null)throw new TypeError("Cannot convert undefined or null to object");for(var _=Object(d),T=1;T<arguments.length;T++){var R=arguments[T];if(R!==l&&R!==null)for(var L in R)R.hasOwnProperty(L)&&(_[L]=R[L])}return _}:fn=Object.assign;var En=Rn(function(d,_,T){for(var R=Object.keys(_),L=0;L<R.length;)(!T||T&&d[R[L]]===l)&&(d[R[L]]=_[R[L]]),L++;return d},"extend","Use `assign`."),Ln=Rn(function(d,_){return En(d,_,!0)},"merge","Use `assign`.");function on(f,d,_){var T=d.prototype,R;R=f.prototype=Object.create(T),R.constructor=f,R._super=T,_&&fn(R,_)}function xn(f,d){return function(){return f.apply(d,arguments)}}function Zn(f,d){return typeof f==w?f.apply(d&&d[0]||l,d):f}function Wn(f,d){return f===l?d:f}function wt(f,d,_){V(Me(d),function(T){f.addEventListener(T,_,!1)})}function Fn(f,d,_){V(Me(d),function(T){f.removeEventListener(T,_,!1)})}function Xt(f,d){for(;f;){if(f==d)return!0;f=f.parentNode}return!1}function gt(f,d){return f.indexOf(d)>-1}function Me(f){return f.trim().split(/\s+/g)}function kt(f,d,_){if(f.indexOf&&!_)return f.indexOf(d);for(var T=0;T<f.length;){if(_&&f[T][_]==d||!_&&f[T]===d)return T;T++}return-1}function Le(f){return Array.prototype.slice.call(f,0)}function Jr(f,d,_){for(var T=[],R=[],L=0;L<f.length;){var Y=f[L][d];kt(R,Y)<0&&T.push(f[L]),R[L]=Y,L++}return T=T.sort(function(vn,Tn){return vn[d]>Tn[d]}),T}function ce(f,d){for(var _,T,R=d[0].toUpperCase()+d.slice(1),L=0;L<g.length;){if(_=g[L],T=_?_+R:d,T in f)return T;L++}return l}var ji=1;function no(){return ji++}function he(f){var d=f.ownerDocument||f;return d.defaultView||d.parentWindow||u}var Zt=/mobile|tablet|ip(ad|hone|od)|android/i,Qr="ontouchstart"in u,De=ce(u,"PointerEvent")!==l,et=Qr&&Zt.test(navigator.userAgent),de="touch",to="pen",ar="mouse",$t="kinect",Ne=25,bn=1,$n=2,tn=4,On=8,Rt=1,Et=2,Kt=4,An=8,pt=16,rt=Et|Kt,Cn=An|pt,sr=rt|Cn,jr=["x","y"],bt=["clientX","clientY"];function un(f,d){var _=this;this.manager=f,this.callback=d,this.element=f.element,this.target=f.options.inputTarget,this.domHandler=function(T){Zn(f.options.enable,[f])&&_.handler(T)},this.init()}un.prototype={handler:function(){},init:function(){this.evEl&&wt(this.element,this.evEl,this.domHandler),this.evTarget&&wt(this.target,this.evTarget,this.domHandler),this.evWin&&wt(he(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&Fn(this.element,this.evEl,this.domHandler),this.evTarget&&Fn(this.target,this.evTarget,this.domHandler),this.evWin&&Fn(he(this.element),this.evWin,this.domHandler)}};function ge(f){var d,_=f.options.inputClass;return _?d=_:De?d=vr:et?d=Qt:Qr?d=yr:d=Be,new d(f,Ue)}function Ue(f,d,_){var T=_.pointers.length,R=_.changedPointers.length,L=d&bn&&T-R===0,Y=d&(tn|On)&&T-R===0;_.isFirst=!!L,_.isFinal=!!Y,L&&(f.session={}),_.eventType=d,eo(f,_),f.emit("hammer.input",_),f.recognize(_),f.session.prevInput=_}function eo(f,d){var _=f.session,T=d.pointers,R=T.length;_.firstInput||(_.firstInput=Vt(d)),R>1&&!_.firstMultiple?_.firstMultiple=Vt(d):R===1&&(_.firstMultiple=!1);var L=_.firstInput,Y=_.firstMultiple,dn=Y?Y.center:L.center,vn=d.center=Ot(T);d.timeStamp=P(),d.deltaTime=d.timeStamp-L.timeStamp,d.angle=ve(dn,vn),d.distance=Jt(dn,vn),pe(_,d),d.offsetDirection=Fe(d.deltaX,d.deltaY);var Tn=We(d.deltaTime,d.deltaX,d.deltaY);d.overallVelocityX=Tn.x,d.overallVelocityY=Tn.y,d.overallVelocity=O(Tn.x)>O(Tn.y)?Tn.x:Tn.y,d.scale=Y?lr(Y.pointers,T):1,d.rotation=Y?fr(Y.pointers,T):0,d.maxPointers=_.prevInput?d.pointers.length>_.prevInput.maxPointers?d.pointers.length:_.prevInput.maxPointers:d.pointers.length,ro(_,d);var ot=f.element;Xt(d.srcEvent.target,ot)&&(ot=d.srcEvent.target),d.target=ot}function pe(f,d){var _=d.center,T=f.offsetDelta||{},R=f.prevDelta||{},L=f.prevInput||{};(d.eventType===bn||L.eventType===tn)&&(R=f.prevDelta={x:L.deltaX||0,y:L.deltaY||0},T=f.offsetDelta={x:_.x,y:_.y}),d.deltaX=R.x+(_.x-T.x),d.deltaY=R.y+(_.y-T.y)}function ro(f,d){var _=f.lastInterval||d,T=d.timeStamp-_.timeStamp,R,L,Y,dn;if(d.eventType!=On&&(T>Ne||_.velocity===l)){var vn=d.deltaX-_.deltaX,Tn=d.deltaY-_.deltaY,ot=We(T,vn,Tn);L=ot.x,Y=ot.y,R=O(ot.x)>O(ot.y)?ot.x:ot.y,dn=Fe(vn,Tn),f.lastInterval=d}else R=_.velocity,L=_.velocityX,Y=_.velocityY,dn=_.direction;d.velocity=R,d.velocityX=L,d.velocityY=Y,d.direction=dn}function Vt(f){for(var d=[],_=0;_<f.pointers.length;)d[_]={clientX:S(f.pointers[_].clientX),clientY:S(f.pointers[_].clientY)},_++;return{timeStamp:P(),pointers:d,center:Ot(d),deltaX:f.deltaX,deltaY:f.deltaY}}function Ot(f){var d=f.length;if(d===1)return{x:S(f[0].clientX),y:S(f[0].clientY)};for(var _=0,T=0,R=0;R<d;)_+=f[R].clientX,T+=f[R].clientY,R++;return{x:S(_/d),y:S(T/d)}}function We(f,d,_){return{x:d/f||0,y:_/f||0}}function Fe(f,d){return f===d?Rt:O(f)>=O(d)?f<0?Et:Kt:d<0?An:pt}function Jt(f,d,_){_||(_=jr);var T=d[_[0]]-f[_[0]],R=d[_[1]]-f[_[1]];return Math.sqrt(T*T+R*R)}function ve(f,d,_){_||(_=jr);var T=d[_[0]]-f[_[0]],R=d[_[1]]-f[_[1]];return Math.atan2(R,T)*180/Math.PI}function fr(f,d){return ve(d[1],d[0],bt)+ve(f[1],f[0],bt)}function lr(f,d){return Jt(d[0],d[1],bt)/Jt(f[0],f[1],bt)}var cr={mousedown:bn,mousemove:$n,mouseup:tn},hr="mousedown",dr="mousemove mouseup";function Be(){this.evEl=hr,this.evWin=dr,this.pressed=!1,un.apply(this,arguments)}on(Be,un,{handler:function(d){var _=cr[d.type];_&bn&&d.button===0&&(this.pressed=!0),_&$n&&d.which!==1&&(_=tn),this.pressed&&(_&tn&&(this.pressed=!1),this.callback(this.manager,_,{pointers:[d],changedPointers:[d],pointerType:ar,srcEvent:d}))}});var io={pointerdown:bn,pointermove:$n,pointerup:tn,pointercancel:On,pointerout:On},oo={2:de,3:to,4:ar,5:$t},gr="pointerdown",pr="pointermove pointerup pointercancel";u.MSPointerEvent&&!u.PointerEvent&&(gr="MSPointerDown",pr="MSPointerMove MSPointerUp MSPointerCancel");function vr(){this.evEl=gr,this.evWin=pr,un.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}on(vr,un,{handler:function(d){var _=this.store,T=!1,R=d.type.toLowerCase().replace("ms",""),L=io[R],Y=oo[d.pointerType]||d.pointerType,dn=Y==de,vn=kt(_,d.pointerId,"pointerId");L&bn&&(d.button===0||dn)?vn<0&&(_.push(d),vn=_.length-1):L&(tn|On)&&(T=!0),!(vn<0)&&(_[vn]=d,this.callback(this.manager,L,{pointers:_,changedPointers:[d],pointerType:Y,srcEvent:d}),T&&_.splice(vn,1))}});var uo={touchstart:bn,touchmove:$n,touchend:tn,touchcancel:On},ao="touchstart",so="touchstart touchmove touchend touchcancel";function _r(){this.evTarget=ao,this.evWin=so,this.started=!1,un.apply(this,arguments)}on(_r,un,{handler:function(d){var _=uo[d.type];if(_===bn&&(this.started=!0),!!this.started){var T=fo.call(this,d,_);_&(tn|On)&&T[0].length-T[1].length===0&&(this.started=!1),this.callback(this.manager,_,{pointers:T[0],changedPointers:T[1],pointerType:de,srcEvent:d})}}});function fo(f,d){var _=Le(f.touches),T=Le(f.changedTouches);return d&(tn|On)&&(_=Jr(_.concat(T),"identifier")),[_,T]}var lo={touchstart:bn,touchmove:$n,touchend:tn,touchcancel:On},co="touchstart touchmove touchend touchcancel";function Qt(){this.evTarget=co,this.targetIds={},un.apply(this,arguments)}on(Qt,un,{handler:function(d){var _=lo[d.type],T=ho.call(this,d,_);T&&this.callback(this.manager,_,{pointers:T[0],changedPointers:T[1],pointerType:de,srcEvent:d})}});function ho(f,d){var _=Le(f.touches),T=this.targetIds;if(d&(bn|$n)&&_.length===1)return T[_[0].identifier]=!0,[_,_];var R,L,Y=Le(f.changedTouches),dn=[],vn=this.target;if(L=_.filter(function(Tn){return Xt(Tn.target,vn)}),d===bn)for(R=0;R<L.length;)T[L[R].identifier]=!0,R++;for(R=0;R<Y.length;)T[Y[R].identifier]&&dn.push(Y[R]),d&(tn|On)&&delete T[Y[R].identifier],R++;if(dn.length)return[Jr(L.concat(dn),"identifier"),dn]}var mr=2500,ni=25;function yr(){un.apply(this,arguments);var f=xn(this.handler,this);this.touch=new Qt(this.manager,f),this.mouse=new Be(this.manager,f),this.primaryTouch=null,this.lastTouches=[]}on(yr,un,{handler:function(d,_,T){var R=T.pointerType==de,L=T.pointerType==ar;if(!(L&&T.sourceCapabilities&&T.sourceCapabilities.firesTouchEvents)){if(R)go.call(this,_,T);else if(L&&po.call(this,T))return;this.callback(d,_,T)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});function go(f,d){f&bn?(this.primaryTouch=d.changedPointers[0].identifier,ti.call(this,d)):f&(tn|On)&&ti.call(this,d)}function ti(f){var d=f.changedPointers[0];if(d.identifier===this.primaryTouch){var _={x:d.clientX,y:d.clientY};this.lastTouches.push(_);var T=this.lastTouches,R=function(){var L=T.indexOf(_);L>-1&&T.splice(L,1)};setTimeout(R,mr)}}function po(f){for(var d=f.srcEvent.clientX,_=f.srcEvent.clientY,T=0;T<this.lastTouches.length;T++){var R=this.lastTouches[T],L=Math.abs(d-R.x),Y=Math.abs(_-R.y);if(L<=ni&&Y<=ni)return!0}return!1}var ei=ce(v.style,"touchAction"),ri=ei!==l,ii="compute",xr="auto",Tr="manipulation",Pt="none",_e="pan-x",me="pan-y",ze=vo();function wr(f,d){this.manager=f,this.set(d)}wr.prototype={set:function(f){f==ii&&(f=this.compute()),ri&&this.manager.element.style&&ze[f]&&(this.manager.element.style[ei]=f),this.actions=f.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var f=[];return V(this.manager.recognizers,function(d){Zn(d.options.enable,[d])&&(f=f.concat(d.getTouchAction()))}),He(f.join(" "))},preventDefaults:function(f){var d=f.srcEvent,_=f.offsetDirection;if(this.manager.session.prevented){d.preventDefault();return}var T=this.actions,R=gt(T,Pt)&&!ze[Pt],L=gt(T,me)&&!ze[me],Y=gt(T,_e)&&!ze[_e];if(R){var dn=f.pointers.length===1,vn=f.distance<2,Tn=f.deltaTime<250;if(dn&&vn&&Tn)return}if(!(Y&&L)&&(R||L&&_&rt||Y&&_&Cn))return this.preventSrc(d)},preventSrc:function(f){this.manager.session.prevented=!0,f.preventDefault()}};function He(f){if(gt(f,Pt))return Pt;var d=gt(f,_e),_=gt(f,me);return d&&_?Pt:d||_?d?_e:me:gt(f,Tr)?Tr:xr}function vo(){if(!ri)return!1;var f={},d=u.CSS&&u.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(_){f[_]=d?u.CSS.supports("touch-action",_):!0}),f}var Mt=1,Bn=2,jt=4,At=8,it=At,ne=16,Kn=32;function vt(f){this.options=fn({},this.defaults,f||{}),this.id=no(),this.manager=null,this.options.enable=Wn(this.options.enable,!0),this.state=Mt,this.simultaneous={},this.requireFail=[]}vt.prototype={defaults:{},set:function(f){return fn(this.options,f),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(f){if(k(f,"recognizeWith",this))return this;var d=this.simultaneous;return f=Ge(f,this),d[f.id]||(d[f.id]=f,f.recognizeWith(this)),this},dropRecognizeWith:function(f){return k(f,"dropRecognizeWith",this)?this:(f=Ge(f,this),delete this.simultaneous[f.id],this)},requireFailure:function(f){if(k(f,"requireFailure",this))return this;var d=this.requireFail;return f=Ge(f,this),kt(d,f)===-1&&(d.push(f),f.requireFailure(this)),this},dropRequireFailure:function(f){if(k(f,"dropRequireFailure",this))return this;f=Ge(f,this);var d=kt(this.requireFail,f);return d>-1&&this.requireFail.splice(d,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(f){return!!this.simultaneous[f.id]},emit:function(f){var d=this,_=this.state;function T(R){d.manager.emit(R,f)}_<At&&T(d.options.event+oi(_)),T(d.options.event),f.additionalEvent&&T(f.additionalEvent),_>=At&&T(d.options.event+oi(_))},tryEmit:function(f){if(this.canEmit())return this.emit(f);this.state=Kn},canEmit:function(){for(var f=0;f<this.requireFail.length;){if(!(this.requireFail[f].state&(Kn|Mt)))return!1;f++}return!0},recognize:function(f){var d=fn({},f);if(!Zn(this.options.enable,[this,d])){this.reset(),this.state=Kn;return}this.state&(it|ne|Kn)&&(this.state=Mt),this.state=this.process(d),this.state&(Bn|jt|At|ne)&&this.tryEmit(d)},process:function(f){},getTouchAction:function(){},reset:function(){}};function oi(f){return f&ne?"cancel":f&At?"end":f&jt?"move":f&Bn?"start":""}function ui(f){return f==pt?"down":f==An?"up":f==Et?"left":f==Kt?"right":""}function Ge(f,d){var _=d.manager;return _?_.get(f):f}function zn(){vt.apply(this,arguments)}on(zn,vt,{defaults:{pointers:1},attrTest:function(f){var d=this.options.pointers;return d===0||f.pointers.length===d},process:function(f){var d=this.state,_=f.eventType,T=d&(Bn|jt),R=this.attrTest(f);return T&&(_&On||!R)?d|ne:T||R?_&tn?d|At:d&Bn?d|jt:Bn:Kn}});function ye(){zn.apply(this,arguments),this.pX=null,this.pY=null}on(ye,zn,{defaults:{event:"pan",threshold:10,pointers:1,direction:sr},getTouchAction:function(){var f=this.options.direction,d=[];return f&rt&&d.push(me),f&Cn&&d.push(_e),d},directionTest:function(f){var d=this.options,_=!0,T=f.distance,R=f.direction,L=f.deltaX,Y=f.deltaY;return R&d.direction||(d.direction&rt?(R=L===0?Rt:L<0?Et:Kt,_=L!=this.pX,T=Math.abs(f.deltaX)):(R=Y===0?Rt:Y<0?An:pt,_=Y!=this.pY,T=Math.abs(f.deltaY))),f.direction=R,_&&T>d.threshold&&R&d.direction},attrTest:function(f){return zn.prototype.attrTest.call(this,f)&&(this.state&Bn||!(this.state&Bn)&&this.directionTest(f))},emit:function(f){this.pX=f.deltaX,this.pY=f.deltaY;var d=ui(f.direction);d&&(f.additionalEvent=this.options.event+d),this._super.emit.call(this,f)}});function Ye(){zn.apply(this,arguments)}on(Ye,zn,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[Pt]},attrTest:function(f){return this._super.attrTest.call(this,f)&&(Math.abs(f.scale-1)>this.options.threshold||this.state&Bn)},emit:function(f){if(f.scale!==1){var d=f.scale<1?"in":"out";f.additionalEvent=this.options.event+d}this._super.emit.call(this,f)}});function xe(){vt.apply(this,arguments),this._timer=null,this._input=null}on(xe,vt,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[xr]},process:function(f){var d=this.options,_=f.pointers.length===d.pointers,T=f.distance<d.threshold,R=f.deltaTime>d.time;if(this._input=f,!T||!_||f.eventType&(tn|On)&&!R)this.reset();else if(f.eventType&bn)this.reset(),this._timer=N(function(){this.state=it,this.tryEmit()},d.time,this);else if(f.eventType&tn)return it;return Kn},reset:function(){clearTimeout(this._timer)},emit:function(f){this.state===it&&(f&&f.eventType&tn?this.manager.emit(this.options.event+"up",f):(this._input.timeStamp=P(),this.manager.emit(this.options.event,this._input)))}});function Er(){zn.apply(this,arguments)}on(Er,zn,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[Pt]},attrTest:function(f){return this._super.attrTest.call(this,f)&&(Math.abs(f.rotation)>this.options.threshold||this.state&Bn)}});function qe(){zn.apply(this,arguments)}on(qe,zn,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:rt|Cn,pointers:1},getTouchAction:function(){return ye.prototype.getTouchAction.call(this)},attrTest:function(f){var d=this.options.direction,_;return d&(rt|Cn)?_=f.overallVelocity:d&rt?_=f.overallVelocityX:d&Cn&&(_=f.overallVelocityY),this._super.attrTest.call(this,f)&&d&f.offsetDirection&&f.distance>this.options.threshold&&f.maxPointers==this.options.pointers&&O(_)>this.options.velocity&&f.eventType&tn},emit:function(f){var d=ui(f.offsetDirection);d&&this.manager.emit(this.options.event+d,f),this.manager.emit(this.options.event,f)}});function Lt(){vt.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}on(Lt,vt,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[Tr]},process:function(f){var d=this.options,_=f.pointers.length===d.pointers,T=f.distance<d.threshold,R=f.deltaTime<d.time;if(this.reset(),f.eventType&bn&&this.count===0)return this.failTimeout();if(T&&R&&_){if(f.eventType!=tn)return this.failTimeout();var L=this.pTime?f.timeStamp-this.pTime<d.interval:!0,Y=!this.pCenter||Jt(this.pCenter,f.center)<d.posThreshold;this.pTime=f.timeStamp,this.pCenter=f.center,!Y||!L?this.count=1:this.count+=1,this._input=f;var dn=this.count%d.taps;if(dn===0)return this.hasRequireFailures()?(this._timer=N(function(){this.state=it,this.tryEmit()},d.interval,this),Bn):it}return Kn},failTimeout:function(){return this._timer=N(function(){this.state=Kn},this.options.interval,this),Kn},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==it&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}});function _t(f,d){return d=d||{},d.recognizers=Wn(d.recognizers,_t.defaults.preset),new Xe(f,d)}_t.VERSION="2.0.7",_t.defaults={domEvents:!1,touchAction:ii,enable:!0,inputTarget:null,inputClass:null,preset:[[Er,{enable:!1}],[Ye,{enable:!1},["rotate"]],[qe,{direction:rt}],[ye,{direction:rt},["swipe"]],[Lt],[Lt,{event:"doubletap",taps:2},["tap"]],[xe]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var _o=1,br=2;function Xe(f,d){this.options=fn({},_t.defaults,d||{}),this.options.inputTarget=this.options.inputTarget||f,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=f,this.input=ge(this),this.touchAction=new wr(this,this.options.touchAction),ke(this,!0),V(this.options.recognizers,function(_){var T=this.add(new _[0](_[1]));_[2]&&T.recognizeWith(_[2]),_[3]&&T.requireFailure(_[3])},this)}Xe.prototype={set:function(f){return fn(this.options,f),f.touchAction&&this.touchAction.update(),f.inputTarget&&(this.input.destroy(),this.input.target=f.inputTarget,this.input.init()),this},stop:function(f){this.session.stopped=f?br:_o},recognize:function(f){var d=this.session;if(!d.stopped){this.touchAction.preventDefaults(f);var _,T=this.recognizers,R=d.curRecognizer;(!R||R&&R.state&it)&&(R=d.curRecognizer=null);for(var L=0;L<T.length;)_=T[L],d.stopped!==br&&(!R||_==R||_.canRecognizeWith(R))?_.recognize(f):_.reset(),!R&&_.state&(Bn|jt|At)&&(R=d.curRecognizer=_),L++}},get:function(f){if(f instanceof vt)return f;for(var d=this.recognizers,_=0;_<d.length;_++)if(d[_].options.event==f)return d[_];return null},add:function(f){if(k(f,"add",this))return this;var d=this.get(f.options.event);return d&&this.remove(d),this.recognizers.push(f),f.manager=this,this.touchAction.update(),f},remove:function(f){if(k(f,"remove",this))return this;if(f=this.get(f),f){var d=this.recognizers,_=kt(d,f);_!==-1&&(d.splice(_,1),this.touchAction.update())}return this},on:function(f,d){if(f!==l&&d!==l){var _=this.handlers;return V(Me(f),function(T){_[T]=_[T]||[],_[T].push(d)}),this}},off:function(f,d){if(f!==l){var _=this.handlers;return V(Me(f),function(T){d?_[T]&&_[T].splice(kt(_[T],d),1):delete _[T]}),this}},emit:function(f,d){this.options.domEvents&&mo(f,d);var _=this.handlers[f]&&this.handlers[f].slice();if(!(!_||!_.length)){d.type=f,d.preventDefault=function(){d.srcEvent.preventDefault()};for(var T=0;T<_.length;)_[T](d),T++}},destroy:function(){this.element&&ke(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}};function ke(f,d){var _=f.element;if(_.style){var T;V(f.options.cssProps,function(R,L){T=ce(_.style,L),d?(f.oldCssProps[T]=_.style[T],_.style[T]=R):_.style[T]=f.oldCssProps[T]||""}),d||(f.oldCssProps={})}}function mo(f,d){var _=r.createEvent("Event");_.initEvent(f,!0,!0),_.gesture=d,d.target.dispatchEvent(_)}fn(_t,{INPUT_START:bn,INPUT_MOVE:$n,INPUT_END:tn,INPUT_CANCEL:On,STATE_POSSIBLE:Mt,STATE_BEGAN:Bn,STATE_CHANGED:jt,STATE_ENDED:At,STATE_RECOGNIZED:it,STATE_CANCELLED:ne,STATE_FAILED:Kn,DIRECTION_NONE:Rt,DIRECTION_LEFT:Et,DIRECTION_RIGHT:Kt,DIRECTION_UP:An,DIRECTION_DOWN:pt,DIRECTION_HORIZONTAL:rt,DIRECTION_VERTICAL:Cn,DIRECTION_ALL:sr,Manager:Xe,Input:un,TouchAction:wr,TouchInput:Qt,MouseInput:Be,PointerEventInput:vr,TouchMouseInput:yr,SingleTouchInput:_r,Recognizer:vt,AttrRecognizer:zn,Tap:Lt,Pan:ye,Swipe:qe,Pinch:Ye,Rotate:Er,Press:xe,on:wt,off:Fn,each:V,merge:Ln,extend:En,assign:fn,inherit:on,bindFn:xn,prefixed:ce});var ai=typeof u<"u"?u:typeof self<"u"?self:{};ai.Hammer=_t,i.exports?i.exports=_t:u[s]=_t})(window,document,"Hammer")}(Au)),Au.exports}var Qv=Jv();const qr=Gv(Qv);/*!
* chartjs-plugin-zoom v2.2.0
* https://www.chartjs.org/chartjs-plugin-zoom/2.2.0/
 * (c) 2016-2024 chartjs-plugin-zoom Contributors
 * Released under the MIT License
 */const Vr=i=>i&&i.enabled&&i.modifierKey,bf=(i,u)=>i&&u[i+"Key"],Bu=(i,u)=>i&&!u[i+"Key"];function le(i,u,r){return i===void 0?!0:typeof i=="string"?i.indexOf(u)!==-1:typeof i=="function"?i({chart:r}).indexOf(u)!==-1:!1}function Cu(i,u){return typeof i=="function"&&(i=i({chart:u})),typeof i=="string"?{x:i.indexOf("x")!==-1,y:i.indexOf("y")!==-1}:{x:!1,y:!1}}function jv(i,u){let r;return function(){return clearTimeout(r),r=setTimeout(i,u),u}}function n0({x:i,y:u},r){const s=r.scales,l=Object.keys(s);for(let g=0;g<l.length;g++){const v=s[l[g]];if(u>=v.top&&u<=v.bottom&&i>=v.left&&i<=v.right)return v}return null}function Af(i,u,r){const{mode:s="xy",scaleMode:l,overScaleMode:g}=i||{},v=n0(u,r),w=Cu(s,r),S=Cu(l,r);if(g){const P=Cu(g,r);for(const N of["x","y"])P[N]&&(S[N]=w[N],w[N]=!1)}if(v&&S[v.axis])return[v];const O=[];return qt(r.scales,function(P){w[P.axis]&&O.push(P)}),O}const Iu=new WeakMap;function cn(i){let u=Iu.get(i);return u||(u={originalScaleLimits:{},updatedScaleLimits:{},handlers:{},panDelta:{},dragging:!1,panning:!1},Iu.set(i,u)),u}function t0(i){Iu.delete(i)}function Cf(i,u,r,s){const l=Math.max(0,Math.min(1,(i-u)/r||0)),g=1-l;return{min:s*l,max:s*g}}function Sf(i,u){const r=i.isHorizontal()?u.x:u.y;return i.getValueForPixel(r)}function If(i,u,r){const s=i.max-i.min,l=s*(u-1),g=Sf(i,r);return Cf(g,i.min,s,l)}function e0(i,u,r){const s=Sf(i,r);if(s===void 0)return{min:i.min,max:i.max};const l=Math.log10(i.min),g=Math.log10(i.max),v=Math.log10(s),w=g-l,S=w*(u-1),O=Cf(v,l,w,S);return{min:Math.pow(10,l+O.min),max:Math.pow(10,g-O.max)}}function r0(i,u){return u&&(u[i.id]||u[i.axis])||{}}function nf(i,u,r,s,l){let g=r[s];if(g==="original"){const v=i.originalScaleLimits[u.id][s];g=$r(v.options,v.scale)}return $r(g,l)}function i0(i,u,r){const s=i.getValueForPixel(u),l=i.getValueForPixel(r);return{min:Math.min(s,l),max:Math.max(s,l)}}function o0(i,{min:u,max:r,minLimit:s,maxLimit:l},g){const v=(i-r+u)/2;u-=v,r+=v;const w=g.min.options??g.min.scale,S=g.max.options??g.max.scale,O=i/1e6;return Vs(u,w,O)&&(u=w),Vs(r,S,O)&&(r=S),u<s?(u=s,r=Math.min(s+i,l)):r>l&&(r=l,u=Math.max(l-i,s)),{min:u,max:r}}function Pe(i,{min:u,max:r},s,l=!1){const g=cn(i.chart),{options:v}=i,w=r0(i,s),{minRange:S=0}=w,O=nf(g,i,w,"min",-1/0),P=nf(g,i,w,"max",1/0);if(l==="pan"&&(u<O||r>P))return!0;const N=i.max-i.min,k=l?Math.max(r-u,S):N;if(l&&k===S&&N<=S)return!0;const V=o0(k,{min:u,max:r,minLimit:O,maxLimit:P},g.originalScaleLimits[i.id]);return v.min=V.min,v.max=V.max,g.updatedScaleLimits[i.id]=V,i.parse(V.min)!==i.min||i.parse(V.max)!==i.max}function u0(i,u,r,s){const l=If(i,u,r),g={min:i.min+l.min,max:i.max-l.max};return Pe(i,g,s,!0)}function a0(i,u,r,s){const l=e0(i,u,r);return Pe(i,l,s,!0)}function s0(i,u,r,s){Pe(i,i0(i,u,r),s,!0)}const tf=i=>i===0||isNaN(i)?0:i<0?Math.min(Math.round(i),-1):Math.max(Math.round(i),1);function f0(i){const r=i.getLabels().length-1;i.min>0&&(i.min-=1),i.max<r&&(i.max+=1)}function l0(i,u,r,s){const l=If(i,u,r);i.min===i.max&&u<1&&f0(i);const g={min:i.min+tf(l.min),max:i.max-tf(l.max)};return Pe(i,g,s,!0)}function c0(i){return i.isHorizontal()?i.width:i.height}function h0(i,u,r){const l=i.getLabels().length-1;let{min:g,max:v}=i;const w=Math.max(v-g,1),S=Math.round(c0(i)/Math.max(w,10)),O=Math.round(Math.abs(u/S));let P;return u<-S?(v=Math.min(v+O,l),g=w===1?v:v-w,P=v===l):u>S&&(g=Math.max(0,g-O),v=w===1?g:g+w,P=g===0),Pe(i,{min:g,max:v},r)||P}const d0={second:500,minute:30*1e3,hour:30*60*1e3,day:12*60*60*1e3,week:3.5*24*60*60*1e3,month:15*24*60*60*1e3,quarter:60*24*60*60*1e3,year:182*24*60*60*1e3};function Rf(i,u,r,s=!1){const{min:l,max:g,options:v}=i,w=v.time&&v.time.round,S=d0[w]||0,O=i.getValueForPixel(i.getPixelForValue(l+S)-u),P=i.getValueForPixel(i.getPixelForValue(g+S)-u);return isNaN(O)||isNaN(P)?!0:Pe(i,{min:O,max:P},r,s?"pan":!1)}function ef(i,u,r){return Rf(i,u,r,!0)}const Ru={category:l0,default:u0,logarithmic:a0},Ou={default:s0},Pu={category:h0,default:Rf,logarithmic:ef,timeseries:ef};function g0(i,u,r){const{id:s,options:{min:l,max:g}}=i;if(!u[s]||!r[s])return!0;const v=r[s];return v.min!==l||v.max!==g}function rf(i,u){qt(i,(r,s)=>{u[s]||delete i[s]})}function ur(i,u){const{scales:r}=i,{originalScaleLimits:s,updatedScaleLimits:l}=u;return qt(r,function(g){g0(g,s,l)&&(s[g.id]={min:{scale:g.min,options:g.options.min},max:{scale:g.max,options:g.options.max}})}),rf(s,r),rf(l,r),s}function of(i,u,r,s){const l=Ru[i.type]||Ru.default;sn(l,[i,u,r,s])}function uf(i,u,r,s){const l=Ou[i.type]||Ou.default;sn(l,[i,u,r,s])}function p0(i){const u=i.chartArea;return{x:(u.left+u.right)/2,y:(u.top+u.bottom)/2}}function zu(i,u,r="none",s="api"){const{x:l=1,y:g=1,focalPoint:v=p0(i)}=typeof u=="number"?{x:u,y:u}:u,w=cn(i),{options:{limits:S,zoom:O}}=w;ur(i,w);const P=l!==1,N=g!==1,k=Af(O,v,i);qt(k||i.scales,function(V){V.isHorizontal()&&P?of(V,l,v,S):!V.isHorizontal()&&N&&of(V,g,v,S)}),i.update(r),sn(O.onZoom,[{chart:i,trigger:s}])}function Of(i,u,r,s="none",l="api"){const g=cn(i),{options:{limits:v,zoom:w}}=g,{mode:S="xy"}=w;ur(i,g);const O=le(S,"x",i),P=le(S,"y",i);qt(i.scales,function(N){N.isHorizontal()&&O?uf(N,u.x,r.x,v):!N.isHorizontal()&&P&&uf(N,u.y,r.y,v)}),i.update(s),sn(w.onZoom,[{chart:i,trigger:l}])}function v0(i,u,r,s="none",l="api"){var w;const g=cn(i);ur(i,g);const v=i.scales[u];Pe(v,r,void 0,!0),i.update(s),sn((w=g.options.zoom)==null?void 0:w.onZoom,[{chart:i,trigger:l}])}function _0(i,u="default"){const r=cn(i),s=ur(i,r);qt(i.scales,function(l){const g=l.options;s[l.id]?(g.min=s[l.id].min.options,g.max=s[l.id].max.options):(delete g.min,delete g.max),delete r.updatedScaleLimits[l.id]}),i.update(u),sn(r.options.zoom.onZoomComplete,[{chart:i}])}function m0(i,u){const r=i.originalScaleLimits[u];if(!r)return;const{min:s,max:l}=r;return $r(l.options,l.scale)-$r(s.options,s.scale)}function y0(i){const u=cn(i);let r=1,s=1;return qt(i.scales,function(l){const g=m0(u,l.id);if(g){const v=Math.round(g/(l.max-l.min)*100)/100;r=Math.min(r,v),s=Math.max(s,v)}}),r<1?r:s}function af(i,u,r,s){const{panDelta:l}=s,g=l[i.id]||0;Js(g)===Js(u)&&(u+=g);const v=Pu[i.type]||Pu.default;sn(v,[i,u,r])?l[i.id]=0:l[i.id]=u}function Pf(i,u,r,s="none"){const{x:l=0,y:g=0}=typeof u=="number"?{x:u,y:u}:u,v=cn(i),{options:{pan:w,limits:S}}=v,{onPan:O}=w||{};ur(i,v);const P=l!==0,N=g!==0;qt(r||i.scales,function(k){k.isHorizontal()&&P?af(k,l,S,v):!k.isHorizontal()&&N&&af(k,g,S,v)}),i.update(s),sn(O,[{chart:i}])}function Mf(i){const u=cn(i);ur(i,u);const r={};for(const s of Object.keys(i.scales)){const{min:l,max:g}=u.originalScaleLimits[s]||{min:{},max:{}};r[s]={min:l.scale,max:g.scale}}return r}function x0(i){const u=cn(i),r={};for(const s of Object.keys(i.scales))r[s]=u.updatedScaleLimits[s];return r}function T0(i){const u=Mf(i);for(const r of Object.keys(i.scales)){const{min:s,max:l}=u[r];if(s!==void 0&&i.scales[r].min!==s||l!==void 0&&i.scales[r].max!==l)return!0}return!1}function sf(i){const u=cn(i);return u.panning||u.dragging}const ff=(i,u,r)=>Math.min(r,Math.max(u,i));function kn(i,u){const{handlers:r}=cn(i),s=r[u];s&&s.target&&(s.target.removeEventListener(u,s),delete r[u])}function Xr(i,u,r,s){const{handlers:l,options:g}=cn(i),v=l[r];if(v&&v.target===u)return;kn(i,r),l[r]=S=>s(i,S,g),l[r].target=u;const w=r==="wheel"?!1:void 0;u.addEventListener(r,l[r],{passive:w})}function w0(i,u){const r=cn(i);r.dragStart&&(r.dragging=!0,r.dragEnd=u,i.update("none"))}function E0(i,u){const r=cn(i);!r.dragStart||u.key!=="Escape"||(kn(i,"keydown"),r.dragging=!1,r.dragStart=r.dragEnd=null,i.update("none"))}function Mu(i,u){if(i.target!==u.canvas){const r=u.canvas.getBoundingClientRect();return{x:i.clientX-r.left,y:i.clientY-r.top}}return Nu(i,u)}function Lf(i,u,r){const{onZoomStart:s,onZoomRejected:l}=r;if(s){const g=Mu(u,i);if(sn(s,[{chart:i,event:u,point:g}])===!1)return sn(l,[{chart:i,event:u}]),!1}}function b0(i,u){if(i.legend){const g=Nu(u,i);if(Yv(g,i.legend))return}const r=cn(i),{pan:s,zoom:l={}}=r.options;if(u.button!==0||bf(Vr(s),u)||Bu(Vr(l.drag),u))return sn(l.onZoomRejected,[{chart:i,event:u}]);Lf(i,u,l)!==!1&&(r.dragStart=u,Xr(i,i.canvas.ownerDocument,"mousemove",w0),Xr(i,window.document,"keydown",E0))}function A0({begin:i,end:u},r){let s=u.x-i.x,l=u.y-i.y;const g=Math.abs(s/l);g>r?s=Math.sign(s)*Math.abs(l*r):g<r&&(l=Math.sign(l)*Math.abs(s/r)),u.x=i.x+s,u.y=i.y+l}function lf(i,u,r,{min:s,max:l,prop:g}){i[s]=ff(Math.min(r.begin[g],r.end[g]),u[s],u[l]),i[l]=ff(Math.max(r.begin[g],r.end[g]),u[s],u[l])}function C0(i,u,r){const s={begin:Mu(u.dragStart,i),end:Mu(u.dragEnd,i)};if(r){const l=i.chartArea.width/i.chartArea.height;A0(s,l)}return s}function Df(i,u,r,s){const l=le(u,"x",i),g=le(u,"y",i),{top:v,left:w,right:S,bottom:O,width:P,height:N}=i.chartArea,k={top:v,left:w,right:S,bottom:O},V=C0(i,r,s&&l&&g);l&&lf(k,i.chartArea,V,{min:"left",max:"right",prop:"x"}),g&&lf(k,i.chartArea,V,{min:"top",max:"bottom",prop:"y"});const Rn=k.right-k.left,fn=k.bottom-k.top;return{...k,width:Rn,height:fn,zoomX:l&&Rn?1+(P-Rn)/P:1,zoomY:g&&fn?1+(N-fn)/N:1}}function S0(i,u){const r=cn(i);if(!r.dragStart)return;kn(i,"mousemove");const{mode:s,onZoomComplete:l,drag:{threshold:g=0,maintainAspectRatio:v}}=r.options.zoom,w=Df(i,s,{dragStart:r.dragStart,dragEnd:u},v),S=le(s,"x",i)?w.width:0,O=le(s,"y",i)?w.height:0,P=Math.sqrt(S*S+O*O);if(r.dragStart=r.dragEnd=null,P<=g){r.dragging=!1,i.update("none");return}Of(i,{x:w.left,y:w.top},{x:w.right,y:w.bottom},"zoom","drag"),r.dragging=!1,r.filterNextClick=!0,sn(l,[{chart:i}])}function I0(i,u,r){if(Bu(Vr(r.wheel),u)){sn(r.onZoomRejected,[{chart:i,event:u}]);return}if(Lf(i,u,r)!==!1&&(u.cancelable&&u.preventDefault(),u.deltaY!==void 0))return!0}function R0(i,u){const{handlers:{onZoomComplete:r},options:{zoom:s}}=cn(i);if(!I0(i,u,s))return;const l=u.target.getBoundingClientRect(),g=s.wheel.speed,v=u.deltaY>=0?2-1/(1-g):1+g,w={x:v,y:v,focalPoint:{x:u.clientX-l.left,y:u.clientY-l.top}};zu(i,w,"zoom","wheel"),sn(r,[{chart:i}])}function O0(i,u,r,s){r&&(cn(i).handlers[u]=jv(()=>sn(r,[{chart:i}]),s))}function P0(i,u){const r=i.canvas,{wheel:s,drag:l,onZoomComplete:g}=u.zoom;s.enabled?(Xr(i,r,"wheel",R0),O0(i,"onZoomComplete",g,250)):kn(i,"wheel"),l.enabled?(Xr(i,r,"mousedown",b0),Xr(i,r.ownerDocument,"mouseup",S0)):(kn(i,"mousedown"),kn(i,"mousemove"),kn(i,"mouseup"),kn(i,"keydown"))}function M0(i){kn(i,"mousedown"),kn(i,"mousemove"),kn(i,"mouseup"),kn(i,"wheel"),kn(i,"click"),kn(i,"keydown")}function L0(i,u){return function(r,s){const{pan:l,zoom:g={}}=u.options;if(!l||!l.enabled)return!1;const v=s&&s.srcEvent;return v&&!u.panning&&s.pointerType==="mouse"&&(Bu(Vr(l),v)||bf(Vr(g.drag),v))?(sn(l.onPanRejected,[{chart:i,event:s}]),!1):!0}}function D0(i,u){const r=Math.abs(i.clientX-u.clientX),s=Math.abs(i.clientY-u.clientY),l=r/s;let g,v;return l>.3&&l<1.7?g=v=!0:r>s?g=!0:v=!0,{x:g,y:v}}function Nf(i,u,r){if(u.scale){const{center:s,pointers:l}=r,g=1/u.scale*r.scale,v=r.target.getBoundingClientRect(),w=D0(l[0],l[1]),S=u.options.zoom.mode,O={x:w.x&&le(S,"x",i)?g:1,y:w.y&&le(S,"y",i)?g:1,focalPoint:{x:s.x-v.left,y:s.y-v.top}};zu(i,O,"zoom","pinch"),u.scale=r.scale}}function N0(i,u,r){if(u.options.zoom.pinch.enabled){const s=Nu(r,i);sn(u.options.zoom.onZoomStart,[{chart:i,event:r,point:s}])===!1?(u.scale=null,sn(u.options.zoom.onZoomRejected,[{chart:i,event:r}])):u.scale=1}}function U0(i,u,r){u.scale&&(Nf(i,u,r),u.scale=null,sn(u.options.zoom.onZoomComplete,[{chart:i}]))}function Uf(i,u,r){const s=u.delta;s&&(u.panning=!0,Pf(i,{x:r.deltaX-s.x,y:r.deltaY-s.y},u.panScales),u.delta={x:r.deltaX,y:r.deltaY})}function W0(i,u,r){const{enabled:s,onPanStart:l,onPanRejected:g}=u.options.pan;if(!s)return;const v=r.target.getBoundingClientRect(),w={x:r.center.x-v.left,y:r.center.y-v.top};if(sn(l,[{chart:i,event:r,point:w}])===!1)return sn(g,[{chart:i,event:r}]);u.panScales=Af(u.options.pan,w,i),u.delta={x:0,y:0},Uf(i,u,r)}function F0(i,u){u.delta=null,u.panning&&(u.panning=!1,u.filterNextClick=!0,sn(u.options.pan.onPanComplete,[{chart:i}]))}const Lu=new WeakMap;function cf(i,u){const r=cn(i),s=i.canvas,{pan:l,zoom:g}=u,v=new qr.Manager(s);g&&g.pinch.enabled&&(v.add(new qr.Pinch),v.on("pinchstart",w=>N0(i,r,w)),v.on("pinch",w=>Nf(i,r,w)),v.on("pinchend",w=>U0(i,r,w))),l&&l.enabled&&(v.add(new qr.Pan({threshold:l.threshold,enable:L0(i,r)})),v.on("panstart",w=>W0(i,r,w)),v.on("panmove",w=>Uf(i,r,w)),v.on("panend",()=>F0(i,r))),Lu.set(i,v)}function hf(i){const u=Lu.get(i);u&&(u.remove("pinchstart"),u.remove("pinch"),u.remove("pinchend"),u.remove("panstart"),u.remove("pan"),u.remove("panend"),u.destroy(),Lu.delete(i))}function B0(i,u){var v,w,S,O;const{pan:r,zoom:s}=i,{pan:l,zoom:g}=u;return((w=(v=s==null?void 0:s.zoom)==null?void 0:v.pinch)==null?void 0:w.enabled)!==((O=(S=g==null?void 0:g.zoom)==null?void 0:S.pinch)==null?void 0:O.enabled)||(r==null?void 0:r.enabled)!==(l==null?void 0:l.enabled)||(r==null?void 0:r.threshold)!==(l==null?void 0:l.threshold)}var z0="2.2.0";function $i(i,u,r){const s=r.zoom.drag,{dragStart:l,dragEnd:g}=cn(i);if(s.drawTime!==u||!g)return;const{left:v,top:w,width:S,height:O}=Df(i,r.zoom.mode,{dragStart:l,dragEnd:g},s.maintainAspectRatio),P=i.ctx;P.save(),P.beginPath(),P.fillStyle=s.backgroundColor||"rgba(225,225,225,0.3)",P.fillRect(v,w,S,O),s.borderWidth>0&&(P.lineWidth=s.borderWidth,P.strokeStyle=s.borderColor||"rgba(225,225,225)",P.strokeRect(v,w,S,O)),P.restore()}var Wf={id:"zoom",version:z0,defaults:{pan:{enabled:!1,mode:"xy",threshold:10,modifierKey:null},zoom:{wheel:{enabled:!1,speed:.1,modifierKey:null},drag:{enabled:!1,drawTime:"beforeDatasetsDraw",modifierKey:null},pinch:{enabled:!1},mode:"xy"}},start:function(i,u,r){const s=cn(i);s.options=r,Object.prototype.hasOwnProperty.call(r.zoom,"enabled")&&console.warn("The option `zoom.enabled` is no longer supported. Please use `zoom.wheel.enabled`, `zoom.drag.enabled`, or `zoom.pinch.enabled`."),(Object.prototype.hasOwnProperty.call(r.zoom,"overScaleMode")||Object.prototype.hasOwnProperty.call(r.pan,"overScaleMode"))&&console.warn("The option `overScaleMode` is deprecated. Please use `scaleMode` instead (and update `mode` as desired)."),qr&&cf(i,r),i.pan=(l,g,v)=>Pf(i,l,g,v),i.zoom=(l,g)=>zu(i,l,g),i.zoomRect=(l,g,v)=>Of(i,l,g,v),i.zoomScale=(l,g,v)=>v0(i,l,g,v),i.resetZoom=l=>_0(i,l),i.getZoomLevel=()=>y0(i),i.getInitialScaleBounds=()=>Mf(i),i.getZoomedScaleBounds=()=>x0(i),i.isZoomedOrPanned=()=>T0(i),i.isZoomingOrPanning=()=>sf(i)},beforeEvent(i,{event:u}){if(sf(i))return!1;if(u.type==="click"||u.type==="mouseup"){const r=cn(i);if(r.filterNextClick)return r.filterNextClick=!1,!1}},beforeUpdate:function(i,u,r){const s=cn(i),l=s.options;s.options=r,B0(l,r)&&(hf(i),cf(i,r)),P0(i,r)},beforeDatasetsDraw(i,u,r){$i(i,"beforeDatasetsDraw",r)},afterDatasetsDraw(i,u,r){$i(i,"afterDatasetsDraw",r)},beforeDraw(i,u,r){$i(i,"beforeDraw",r)},afterDraw(i,u,r){$i(i,"afterDraw",r)},stop:function(i){M0(i),qr&&hf(i),t0(i)},panFunctions:Pu,zoomFunctions:Ru,zoomRectFunctions:Ou};/*!
 * chartjs-plugin-datalabels v2.2.0
 * https://chartjs-plugin-datalabels.netlify.app
 * (c) 2017-2022 chartjs-plugin-datalabels contributors
 * Released under the MIT license
 */var df=function(){if(typeof window<"u"){if(window.devicePixelRatio)return window.devicePixelRatio;var i=window.screen;if(i)return(i.deviceXDPI||1)/(i.logicalXDPI||1)}return 1}(),kr={toTextLines:function(i){var u=[],r;for(i=[].concat(i);i.length;)r=i.pop(),typeof r=="string"?u.unshift.apply(u,r.split(`
`)):Array.isArray(r)?i.push.apply(i,r):Yr(i)||u.unshift(""+r);return u},textSize:function(i,u,r){var s=[].concat(u),l=s.length,g=i.font,v=0,w;for(i.font=r.string,w=0;w<l;++w)v=Math.max(i.measureText(s[w]).width,v);return i.font=g,{height:l*r.lineHeight,width:v}},bound:function(i,u,r){return Math.max(i,Math.min(u,r))},arrayDiff:function(i,u){var r=i.slice(),s=[],l,g,v,w;for(l=0,v=u.length;l<v;++l)w=u[l],g=r.indexOf(w),g===-1?s.push([w,1]):r.splice(g,1);for(l=0,v=r.length;l<v;++l)s.push([r[l],-1]);return s},rasterize:function(i){return Math.round(i*df)/df}};function Su(i,u){var r=u.x,s=u.y;if(r===null)return{x:0,y:-1};if(s===null)return{x:1,y:0};var l=i.x-r,g=i.y-s,v=Math.sqrt(l*l+g*g);return{x:v?l/v:0,y:v?g/v:-1}}function H0(i,u,r,s,l){switch(l){case"center":r=s=0;break;case"bottom":r=0,s=1;break;case"right":r=1,s=0;break;case"left":r=-1,s=0;break;case"top":r=0,s=-1;break;case"start":r=-r,s=-s;break;case"end":break;default:l*=Math.PI/180,r=Math.cos(l),s=Math.sin(l);break}return{x:i,y:u,vx:r,vy:s}}var G0=0,Ff=1,Bf=2,zf=4,Hf=8;function Ki(i,u,r){var s=G0;return i<r.left?s|=Ff:i>r.right&&(s|=Bf),u<r.top?s|=Hf:u>r.bottom&&(s|=zf),s}function Y0(i,u){for(var r=i.x0,s=i.y0,l=i.x1,g=i.y1,v=Ki(r,s,u),w=Ki(l,g,u),S,O,P;!(!(v|w)||v&w);)S=v||w,S&Hf?(O=r+(l-r)*(u.top-s)/(g-s),P=u.top):S&zf?(O=r+(l-r)*(u.bottom-s)/(g-s),P=u.bottom):S&Bf?(P=s+(g-s)*(u.right-r)/(l-r),O=u.right):S&Ff&&(P=s+(g-s)*(u.left-r)/(l-r),O=u.left),S===v?(r=O,s=P,v=Ki(r,s,u)):(l=O,g=P,w=Ki(l,g,u));return{x0:r,x1:l,y0:s,y1:g}}function Vi(i,u){var r=u.anchor,s=i,l,g;return u.clamp&&(s=Y0(s,u.area)),r==="start"?(l=s.x0,g=s.y0):r==="end"?(l=s.x1,g=s.y1):(l=(s.x0+s.x1)/2,g=(s.y0+s.y1)/2),H0(l,g,i.vx,i.vy,u.align)}var Ji={arc:function(i,u){var r=(i.startAngle+i.endAngle)/2,s=Math.cos(r),l=Math.sin(r),g=i.innerRadius,v=i.outerRadius;return Vi({x0:i.x+s*g,y0:i.y+l*g,x1:i.x+s*v,y1:i.y+l*v,vx:s,vy:l},u)},point:function(i,u){var r=Su(i,u.origin),s=r.x*i.options.radius,l=r.y*i.options.radius;return Vi({x0:i.x-s,y0:i.y-l,x1:i.x+s,y1:i.y+l,vx:r.x,vy:r.y},u)},bar:function(i,u){var r=Su(i,u.origin),s=i.x,l=i.y,g=0,v=0;return i.horizontal?(s=Math.min(i.x,i.base),g=Math.abs(i.base-i.x)):(l=Math.min(i.y,i.base),v=Math.abs(i.base-i.y)),Vi({x0:s,y0:l+v,x1:s+g,y1:l,vx:r.x,vy:r.y},u)},fallback:function(i,u){var r=Su(i,u.origin);return Vi({x0:i.x,y0:i.y,x1:i.x+(i.width||0),y1:i.y+(i.height||0),vx:r.x,vy:r.y},u)}},Yt=kr.rasterize;function q0(i){var u=i.borderWidth||0,r=i.padding,s=i.size.height,l=i.size.width,g=-l/2,v=-s/2;return{frame:{x:g-r.left-u,y:v-r.top-u,w:l+r.width+u*2,h:s+r.height+u*2},text:{x:g,y:v,w:l,h:s}}}function X0(i,u){var r=u.chart.getDatasetMeta(u.datasetIndex).vScale;if(!r)return null;if(r.xCenter!==void 0&&r.yCenter!==void 0)return{x:r.xCenter,y:r.yCenter};var s=r.getBasePixel();return i.horizontal?{x:s,y:null}:{x:null,y:s}}function k0(i){return i instanceof Uu?Ji.arc:i instanceof Wu?Ji.point:i instanceof Fu?Ji.bar:Ji.fallback}function Z0(i,u,r,s,l,g){var v=Math.PI/2;if(g){var w=Math.min(g,l/2,s/2),S=u+w,O=r+w,P=u+s-w,N=r+l-w;i.moveTo(u,O),S<P&&O<N?(i.arc(S,O,w,-Math.PI,-v),i.arc(P,O,w,-v,0),i.arc(P,N,w,0,v),i.arc(S,N,w,v,Math.PI)):S<P?(i.moveTo(S,r),i.arc(P,O,w,-v,v),i.arc(S,O,w,v,Math.PI+v)):O<N?(i.arc(S,O,w,-Math.PI,0),i.arc(S,N,w,0,Math.PI)):i.arc(S,O,w,-Math.PI,Math.PI),i.closePath(),i.moveTo(u,r)}else i.rect(u,r,s,l)}function $0(i,u,r){var s=r.backgroundColor,l=r.borderColor,g=r.borderWidth;!s&&(!l||!g)||(i.beginPath(),Z0(i,Yt(u.x)+g/2,Yt(u.y)+g/2,Yt(u.w)-g,Yt(u.h)-g,r.borderRadius),i.closePath(),s&&(i.fillStyle=s,i.fill()),l&&g&&(i.strokeStyle=l,i.lineWidth=g,i.lineJoin="miter",i.stroke()))}function K0(i,u,r){var s=r.lineHeight,l=i.w,g=i.x,v=i.y+s/2;return u==="center"?g+=l/2:(u==="end"||u==="right")&&(g+=l),{h:s,w:l,x:g,y:v}}function V0(i,u,r){var s=i.shadowBlur,l=r.stroked,g=Yt(r.x),v=Yt(r.y),w=Yt(r.w);l&&i.strokeText(u,g,v,w),r.filled&&(s&&l&&(i.shadowBlur=0),i.fillText(u,g,v,w),s&&l&&(i.shadowBlur=s))}function J0(i,u,r,s){var l=s.textAlign,g=s.color,v=!!g,w=s.font,S=u.length,O=s.textStrokeColor,P=s.textStrokeWidth,N=O&&P,k;if(!(!S||!v&&!N))for(r=K0(r,l,w),i.font=w.string,i.textAlign=l,i.textBaseline="middle",i.shadowBlur=s.textShadowBlur,i.shadowColor=s.textShadowColor,v&&(i.fillStyle=g),N&&(i.lineJoin="round",i.lineWidth=P,i.strokeStyle=O),k=0,S=u.length;k<S;++k)V0(i,u[k],{stroked:N,filled:v,w:r.w,x:r.x,y:r.y+r.h*k})}var Gf=function(i,u,r,s){var l=this;l._config=i,l._index=s,l._model=null,l._rects=null,l._ctx=u,l._el=r};Kr(Gf.prototype,{_modelize:function(i,u,r,s){var l=this,g=l._index,v=qv(_n([r.font,{}],s,g)),w=_n([r.color,Xv.color],s,g);return{align:_n([r.align,"center"],s,g),anchor:_n([r.anchor,"center"],s,g),area:s.chart.chartArea,backgroundColor:_n([r.backgroundColor,null],s,g),borderColor:_n([r.borderColor,null],s,g),borderRadius:_n([r.borderRadius,0],s,g),borderWidth:_n([r.borderWidth,0],s,g),clamp:_n([r.clamp,!1],s,g),clip:_n([r.clip,!1],s,g),color:w,display:i,font:v,lines:u,offset:_n([r.offset,4],s,g),opacity:_n([r.opacity,1],s,g),origin:X0(l._el,s),padding:kv(_n([r.padding,4],s,g)),positioner:k0(l._el),rotation:_n([r.rotation,0],s,g)*(Math.PI/180),size:kr.textSize(l._ctx,u,v),textAlign:_n([r.textAlign,"start"],s,g),textShadowBlur:_n([r.textShadowBlur,0],s,g),textShadowColor:_n([r.textShadowColor,w],s,g),textStrokeColor:_n([r.textStrokeColor,w],s,g),textStrokeWidth:_n([r.textStrokeWidth,0],s,g)}},update:function(i){var u=this,r=null,s=null,l=u._index,g=u._config,v,w,S,O=_n([g.display,!0],i,l);O&&(v=i.dataset.data[l],w=$r(sn(g.formatter,[v,i]),v),S=Yr(w)?[]:kr.toTextLines(w),S.length&&(r=u._modelize(O,S,g,i),s=q0(r))),u._model=r,u._rects=s},geometry:function(){return this._rects?this._rects.frame:{}},rotation:function(){return this._model?this._model.rotation:0},visible:function(){return this._model&&this._model.opacity},model:function(){return this._model},draw:function(i,u){var r=this,s=i.ctx,l=r._model,g=r._rects,v;this.visible()&&(s.save(),l.clip&&(v=l.area,s.beginPath(),s.rect(v.left,v.top,v.right-v.left,v.bottom-v.top),s.clip()),s.globalAlpha=kr.bound(0,l.opacity,1),s.translate(Yt(u.x),Yt(u.y)),s.rotate(l.rotation),$0(s,g.frame,l),J0(s,l.lines,g.text,l),s.restore())}});var Q0=Number.MIN_SAFE_INTEGER||-9007199254740991,j0=Number.MAX_SAFE_INTEGER||9007199254740991;function Hr(i,u,r){var s=Math.cos(r),l=Math.sin(r),g=u.x,v=u.y;return{x:g+s*(i.x-g)-l*(i.y-v),y:v+l*(i.x-g)+s*(i.y-v)}}function gf(i,u){var r=j0,s=Q0,l=u.origin,g,v,w,S,O;for(g=0;g<i.length;++g)v=i[g],w=v.x-l.x,S=v.y-l.y,O=u.vx*w+u.vy*S,r=Math.min(r,O),s=Math.max(s,O);return{min:r,max:s}}function Qi(i,u){var r=u.x-i.x,s=u.y-i.y,l=Math.sqrt(r*r+s*s);return{vx:(u.x-i.x)/l,vy:(u.y-i.y)/l,origin:i,ln:l}}var Yf=function(){this._rotation=0,this._rect={x:0,y:0,w:0,h:0}};Kr(Yf.prototype,{center:function(){var i=this._rect;return{x:i.x+i.w/2,y:i.y+i.h/2}},update:function(i,u,r){this._rotation=r,this._rect={x:u.x+i.x,y:u.y+i.y,w:u.w,h:u.h}},contains:function(i){var u=this,r=1,s=u._rect;return i=Hr(i,u.center(),-u._rotation),!(i.x<s.x-r||i.y<s.y-r||i.x>s.x+s.w+r*2||i.y>s.y+s.h+r*2)},intersects:function(i){var u=this._points(),r=i._points(),s=[Qi(u[0],u[1]),Qi(u[0],u[3])],l,g,v;for(this._rotation!==i._rotation&&s.push(Qi(r[0],r[1]),Qi(r[0],r[3])),l=0;l<s.length;++l)if(g=gf(u,s[l]),v=gf(r,s[l]),g.max<v.min||v.max<g.min)return!1;return!0},_points:function(){var i=this,u=i._rect,r=i._rotation,s=i.center();return[Hr({x:u.x,y:u.y},s,r),Hr({x:u.x+u.w,y:u.y},s,r),Hr({x:u.x+u.w,y:u.y+u.h},s,r),Hr({x:u.x,y:u.y+u.h},s,r)]}});function qf(i,u,r){var s=u.positioner(i,u),l=s.vx,g=s.vy;if(!l&&!g)return{x:s.x,y:s.y};var v=r.w,w=r.h,S=u.rotation,O=Math.abs(v/2*Math.cos(S))+Math.abs(w/2*Math.sin(S)),P=Math.abs(v/2*Math.sin(S))+Math.abs(w/2*Math.cos(S)),N=1/Math.max(Math.abs(l),Math.abs(g));return O*=l*N,P*=g*N,O+=u.offset*l,P+=u.offset*g,{x:s.x+O,y:s.y+P}}function n_(i,u){var r,s,l,g;for(r=i.length-1;r>=0;--r)for(l=i[r].$layout,s=r-1;s>=0&&l._visible;--s)g=i[s].$layout,g._visible&&l._box.intersects(g._box)&&u(l,g);return i}function t_(i){var u,r,s,l,g,v,w;for(u=0,r=i.length;u<r;++u)s=i[u],l=s.$layout,l._visible&&(w=new Proxy(s._el,{get:(S,O)=>S.getProps([O],!0)[O]}),g=s.geometry(),v=qf(w,s.model(),g),l._box.update(v,g,s.rotation()));return n_(i,function(S,O){var P=S._hidable,N=O._hidable;P&&N||N?O._visible=!1:P&&(S._visible=!1)})}var Zr={prepare:function(i){var u=[],r,s,l,g,v;for(r=0,l=i.length;r<l;++r)for(s=0,g=i[r].length;s<g;++s)v=i[r][s],u.push(v),v.$layout={_box:new Yf,_hidable:!1,_visible:!0,_set:r,_idx:v._index};return u.sort(function(w,S){var O=w.$layout,P=S.$layout;return O._idx===P._idx?P._set-O._set:P._idx-O._idx}),this.update(u),u},update:function(i){var u=!1,r,s,l,g,v;for(r=0,s=i.length;r<s;++r)l=i[r],g=l.model(),v=l.$layout,v._hidable=g&&g.display==="auto",v._visible=l.visible(),u|=v._hidable;u&&t_(i)},lookup:function(i,u){var r,s;for(r=i.length-1;r>=0;--r)if(s=i[r].$layout,s&&s._visible&&s._box.contains(u))return i[r];return null},draw:function(i,u){var r,s,l,g,v,w;for(r=0,s=u.length;r<s;++r)l=u[r],g=l.$layout,g._visible&&(v=l.geometry(),w=qf(l._el,l.model(),v),g._box.update(w,v,l.rotation()),l.draw(i,w))}},e_=function(i){if(Yr(i))return null;var u=i,r,s,l;if(Zv(i))if(!Yr(i.label))u=i.label;else if(!Yr(i.r))u=i.r;else for(u="",r=Object.keys(i),l=0,s=r.length;l<s;++l)u+=(l!==0?", ":"")+r[l]+": "+i[r[l]];return""+u},r_={align:"center",anchor:"center",backgroundColor:null,borderColor:null,borderRadius:0,borderWidth:0,clamp:!1,clip:!1,color:void 0,display:!0,font:{family:void 0,lineHeight:1.2,size:void 0,style:void 0,weight:null},formatter:e_,labels:void 0,listeners:{},offset:4,opacity:1,padding:{top:4,right:4,bottom:4,left:4},rotation:0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,textShadowBlur:0,textShadowColor:void 0},Xn="$datalabels",Xf="$default";function i_(i,u){var r=i.datalabels,s={},l=[],g,v;return r===!1?null:(r===!0&&(r={}),u=Kr({},[u,r]),g=u.labels||{},v=Object.keys(g),delete u.labels,v.length?v.forEach(function(w){g[w]&&l.push(Kr({},[u,g[w],{_key:w}]))}):l.push(u),s=l.reduce(function(w,S){return qt(S.listeners||{},function(O,P){w[P]=w[P]||{},w[P][S._key||Xf]=O}),delete S.listeners,w},{}),{labels:l,listeners:s})}function Du(i,u,r,s){if(u){var l=r.$context,g=r.$groups,v;u[g._set]&&(v=u[g._set][g._key],v&&sn(v,[l,s])===!0&&(i[Xn]._dirty=!0,r.update(l)))}}function o_(i,u,r,s,l){var g,v;!r&&!s||(r?s?r!==s&&(v=g=!0):v=!0:g=!0,v&&Du(i,u.leave,r,l),g&&Du(i,u.enter,s,l))}function u_(i,u){var r=i[Xn],s=r._listeners,l,g;if(!(!s.enter&&!s.leave)){if(u.type==="mousemove")g=Zr.lookup(r._labels,u);else if(u.type!=="mouseout")return;l=r._hovered,r._hovered=g,o_(i,s,l,g,u)}}function a_(i,u){var r=i[Xn],s=r._listeners.click,l=s&&Zr.lookup(r._labels,u);l&&Du(i,s,l,u)}var kf={id:"datalabels",defaults:r_,beforeInit:function(i){i[Xn]={_actives:[]}},beforeUpdate:function(i){var u=i[Xn];u._listened=!1,u._listeners={},u._datasets=[],u._labels=[]},afterDatasetUpdate:function(i,u,r){var s=u.index,l=i[Xn],g=l._datasets[s]=[],v=i.isDatasetVisible(s),w=i.data.datasets[s],S=i_(w,r),O=u.meta.data||[],P=i.ctx,N,k,V,Rn,fn,En,Ln,on;for(P.save(),N=0,V=O.length;N<V;++N)if(Ln=O[N],Ln[Xn]=[],v&&Ln&&i.getDataVisibility(N)&&!Ln.skip)for(k=0,Rn=S.labels.length;k<Rn;++k)fn=S.labels[k],En=fn._key,on=new Gf(fn,P,Ln,N),on.$groups={_set:s,_key:En||Xf},on.$context={active:!1,chart:i,dataIndex:N,dataset:w,datasetIndex:s},on.update(on.$context),Ln[Xn].push(on),g.push(on);P.restore(),Kr(l._listeners,S.listeners,{merger:function(xn,Zn,Wn){Zn[xn]=Zn[xn]||{},Zn[xn][u.index]=Wn[xn],l._listened=!0}})},afterUpdate:function(i){i[Xn]._labels=Zr.prepare(i[Xn]._datasets)},afterDatasetsDraw:function(i){Zr.draw(i,i[Xn]._labels)},beforeEvent:function(i,u){if(i[Xn]._listened){var r=u.event;switch(r.type){case"mousemove":case"mouseout":u_(i,r);break;case"click":a_(i,r);break}}},afterEvent:function(i){var u=i[Xn],r=u._actives,s=u._actives=i.getActiveElements(),l=kr.arrayDiff(r,s),g,v,w,S,O,P,N;for(g=0,v=l.length;g<v;++g)if(O=l[g],O[1])for(N=O[0].element[Xn]||[],w=0,S=N.length;w<S;++w)P=N[w],P.$context.active=O[1]===1,P.update(P.$context);(u._dirty||l.length)&&(Zr.update(u._labels),i.render()),delete u._dirty}};Oe.register(pf,vf,Wu,_f,Fu,Uu,mf,yf,xf,Tf,wf,Ef,Wf,kf);class Hu{constructor(u={}){this.settings=u,this.charts=u.charts||{},this.theme=u.theme||{}}getChartConfig(u="bar",r={}){const s=this.getBaseConfig(),l=this.getLayoutConfig(),g=this.getDataDisplayConfig(),v=this.getInteractionConfig(),w=this.getPerformanceConfig();return{type:u,options:{...s,...l,...g,...v,...w,...r}}}getBaseConfig(){return{responsive:!0,maintainAspectRatio:!1,animation:this.getAnimationConfig(),plugins:{legend:{display:this.charts.showLegend!==!1,position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12}}},tooltip:this.getTooltipConfig(),datalabels:this.getDataLabelsConfig()},scales:this.getScalesConfig()}}getLayoutConfig(){const u=this.charts.layout||{};return{layout:{padding:this.getLayoutPadding(u.marginSize)},aspectRatio:this.getAspectRatio(u.aspectRatio)}}getDataDisplayConfig(){const u=this.charts.dataDisplay||{};return{scales:{...this.getScalesConfig(),y:{...this.getScalesConfig().y,beginAtZero:u.zeroBased!==!1,grid:{display:u.gridLines!==!1,color:this.theme.darkMode?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"}},x:{...this.getScalesConfig().x,grid:{display:u.gridLines!==!1,color:this.theme.darkMode?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"}}},elements:{point:{radius:u.showDataPoints!==!1?4:0,hoverRadius:u.showDataPoints!==!1?6:0}}}}getInteractionConfig(){const u=this.charts.interaction||{};return{interaction:{intersect:!1,mode:"index"},plugins:{zoom:{zoom:{wheel:{enabled:u.enableZoom!==!1},pinch:{enabled:u.enableZoom!==!1},mode:"x"},pan:{enabled:u.enableZoom!==!1,mode:"x"}}},onHover:u.hoverEffects!==!1?this.getHoverHandler():void 0}}getPerformanceConfig(){var r;const u=this.charts.performanceMode||!1;return{animation:u?!1:this.getAnimationConfig(),parsing:{xAxisKey:"x",yAxisKey:"y"},normalized:u,spanGaps:!0,elements:{point:{radius:u?0:((r=this.charts.dataDisplay)==null?void 0:r.showDataPoints)!==!1?4:0}}}}getAnimationConfig(){return this.theme.animationsEnabled&&this.theme.chartAnimations?{duration:750,easing:"easeInOutQuart",delay:r=>{let s=0;return r.type==="data"&&r.mode==="default"&&(s=r.dataIndex*50+r.datasetIndex*100),s}}:!1}getTooltipConfig(){var s;const u=((s=this.charts.interaction)==null?void 0:s.tooltipStyle)||"standard",r={backgroundColor:this.theme.darkMode?"rgba(33, 33, 33, 0.95)":"rgba(255, 255, 255, 0.95)",titleColor:this.theme.darkMode?"#ffffff":"#000000",bodyColor:this.theme.darkMode?"#ffffff":"#000000",borderColor:this.theme.darkMode?"#404040":"#d9d9d9",borderWidth:1,cornerRadius:8,displayColors:!0,padding:12};switch(u){case"minimal":return{...r,displayColors:!1,padding:8,callbacks:{title:()=>"",label:l=>`${l.parsed.y}`}};case"detailed":return{...r,padding:16,callbacks:{afterBody:l=>{const g=l[0];return["",`Dataset: ${g.dataset.label}`,`Index: ${g.dataIndex}`,`Total Items: ${g.dataset.data.length}`]}}};default:return r}}getDataLabelsConfig(){var r;return((r=this.charts.dataDisplay)==null?void 0:r.showDataLabels)||!1?{display:!0,color:this.theme.darkMode?"#ffffff":"#000000",font:{size:11,weight:"bold"},formatter:(s,l)=>typeof s=="number"?s.toLocaleString():s,anchor:"end",align:"top",offset:4}:{display:!1}}getScalesConfig(){const u=this.theme.darkMode?"#ffffff":"#666666";return{x:{ticks:{color:u,font:{size:11}},title:{display:!1}},y:{ticks:{color:u,font:{size:11}},title:{display:!1}}}}getLayoutPadding(u="standard"){switch(u){case"compact":return{top:10,right:10,bottom:10,left:10};case"spacious":return{top:30,right:30,bottom:30,left:30};default:return{top:20,right:20,bottom:20,left:20}}}getAspectRatio(u="auto"){switch(u){case"16:9":return 16/9;case"4:3":return 4/3;case"1:1":return 1;default:return}}getHoverHandler(){return(u,r,s)=>{r.length>0?s.canvas.style.cursor="pointer":s.canvas.style.cursor="default"}}getChartHeight(){const u=this.charts.layout||{};let r=u.defaultHeight||300;return u.compactMode&&(r=Math.max(200,r*.8)),r}getColorScheme(){switch(this.charts.colorScheme||"somipem"){case"blue":return["#1890ff","#40a9ff","#69c0ff","#91d5ff","#bae7ff"];case"green":return["#52c41a","#73d13d","#95de64","#b7eb8f","#d9f7be"];case"red":return["#ff4d4f","#ff7875","#ffa39e","#ffccc7","#ffe1e1"];default:return[zr.PRIMARY_BLUE,zr.SECONDARY_BLUE,zr.CHART_TERTIARY,zr.SUCCESS_GREEN,zr.WARNING_ORANGE]}}}Vv.throttle((i,u)=>{i&&i.data&&(i.data=u,i.update("none"))},500);function c_(){Oe.register(pf,vf,Wu,_f,Fu,mf,yf,xf,Tf,Uu,wf,Ef,Wf,kf),Oe.defaults.font.family="'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",Oe.defaults.responsive=!0,Oe.defaults.maintainAspectRatio=!1,Oe.defaults.animation=!1,Oe.defaults.devicePixelRatio=1}function h_(i,u="bar",r={}){var v;const l=new Hu(i).getChartConfig(u,r);return{...getChartOptions(((v=i.theme)==null?void 0:v.darkMode)||!1),...l.options,...r}}function d_(i){return new Hu(i).getChartHeight()}function s_(i){return new Hu(i).getColorScheme()}function g_(i,u){if(!i||!i.datasets)return i;const r=s_(u);return{...i,datasets:i.datasets.map((s,l)=>({...s,backgroundColor:s.backgroundColor||r[l%r.length],borderColor:s.borderColor||r[l%r.length],borderWidth:s.borderWidth||2,tension:s.tension||.4}))}}export{h_ as a,g_ as b,d_ as g,Vv as l,c_ as r};
