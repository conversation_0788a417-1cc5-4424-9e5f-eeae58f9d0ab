import React, { useEffect, useRef, useState } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { Spin, Empty, Typography, Space, Button, Row, Col } from 'antd';
import { Bar<PERSON>hartOutlined, ClockCircleOutlined, UserOutlined } from '@ant-design/icons';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const { Text } = Typography;

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const ArretOperatorChart = ({ data = [], loading = false, colors }) => {
  const chartRef = useRef();
  const [viewMode, setViewMode] = useState('stops'); // 'stops' or 'duration' or 'both'
  
  // Use colors prop if provided, otherwise fall back to default SOMIPEM colors
  const chartColors = colors || [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.SUCCESS_GREEN,
    SOMIPEM_COLORS.WARNING_ORANGE
  ];
  // Process data to group by operator
  const processOperatorData = (stopsData) => {
    if (!Array.isArray(stopsData) || stopsData.length === 0) {
      return { labels: [], stopCounts: [], avgDurations: [] };
    }

    // Group by operator (Regleur_Prenom)
    const operatorStats = {};
    
    stopsData.forEach(stop => {
      const operator = stop.Regleur_Prenom || 'Non assigné';
      
      if (!operatorStats[operator]) {
        operatorStats[operator] = {
          count: 0,
          totalDuration: 0,
          avgDuration: 0
        };
      }
      
      operatorStats[operator].count += 1;
      
      // Calculate duration from start/end times
      if (stop.Debut_Stop && stop.Fin_Stop_Time) {
        try {
          const start = new Date(stop.Debut_Stop);
          const end = new Date(stop.Fin_Stop_Time);
          const duration = (end - start) / (1000 * 60); // Duration in minutes
          
          if (duration > 0) {
            operatorStats[operator].totalDuration += duration;
          }
        } catch (error) {
          console.warn('Error calculating duration:', error);
        }
      }
    });

    // Calculate averages and sort by total stops
    Object.keys(operatorStats).forEach(operator => {
      const stats = operatorStats[operator];
      stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
    });

    const sortedOperators = Object.entries(operatorStats)
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 10); // Top 10 operators

    const labels = sortedOperators.map(([operator]) => operator);
    const stopCounts = sortedOperators.map(([, stats]) => stats.count);
    const avgDurations = sortedOperators.map(([, stats]) => Math.round(stats.avgDuration));

    return {
      labels,
      stopCounts,
      avgDurations
    };
  };

  const chartData = processOperatorData(data);

  const getDatasets = () => {
    if (viewMode === 'stops') {
      return [
        {
          label: 'Nombre d\'Arrêts',
          data: chartData.stopCounts,
          backgroundColor: `${chartColors[0]}99`, // Using chartColors with transparency
          borderColor: chartColors[0],
          borderWidth: 2,
          borderRadius: 6,
          borderSkipped: false
        }
      ];
    } else if (viewMode === 'duration') {
      return [
        {
          label: 'Durée Moyenne (min)',
          data: chartData.avgDurations,
          backgroundColor: `${chartColors[1]}99`, // Using chartColors with transparency
          borderColor: chartColors[1],
          borderWidth: 2,
          borderRadius: 6,
          borderSkipped: false
        }
      ];
    } else {
      // both mode
      return [
        {
          label: 'Nombre d\'Arrêts',
          data: chartData.stopCounts,
          backgroundColor: `${chartColors[0]}99`,
          borderColor: chartColors[0],
          borderWidth: 2,
          borderRadius: 6,
          borderSkipped: false,
          yAxisID: 'y'
        },
        {
          label: 'Durée Moyenne (min)',
          data: chartData.avgDurations,
          backgroundColor: `${chartColors[1]}99`,
          borderColor: chartColors[1],
          borderWidth: 2,
          borderRadius: 6,
          borderSkipped: false,
          yAxisID: 'y1'
        }
      ];
    }
  };
  const getOptions = () => {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              weight: '500'
            }
          }
        },
        title: {
          display: true,
          text: viewMode === 'stops' ? 'Nombre d\'Arrêts par Opérateur' : 
                viewMode === 'duration' ? 'Durée Moyenne par Opérateur' : 
                'Performance des Opérateurs',
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 20
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context) {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              
              if (label.includes('Nombre')) {
                return `${label}: ${value} arrêts`;
              } else {
                return `${label}: ${value} minutes`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 11
            },
            maxRotation: 45,
            minRotation: 0
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    };

    if (viewMode === 'both') {
      // Dual axis for both mode
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Nombre d\'Arrêts',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
      baseOptions.scales.y1 = {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Durée Moyenne (min)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
    } else {
      // Single axis
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: viewMode === 'stops' ? 'Nombre d\'Arrêts' : 'Durée Moyenne (min)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
    }

    return baseOptions;
  };  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Spin size="large" tip="Chargement des données opérateurs..." />
      </div>
    );
  }

  if (!chartData.labels || chartData.labels.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Empty 
          description="Aucune donnée d'opérateur disponible"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%' }}>
      {/* View Mode Toggle */}
      <Row style={{ marginBottom: '12px' }}>
        <Col span={24}>
          <Space size="small" style={{ width: '100%', justifyContent: 'center' }}>
            <Button
              type={viewMode === 'stops' ? 'primary' : 'default'}
              icon={<UserOutlined />}
              onClick={() => setViewMode('stops')}
              size="small"
            >
              Arrêts
            </Button>
            <Button
              type={viewMode === 'duration' ? 'primary' : 'default'}
              icon={<ClockCircleOutlined />}
              onClick={() => setViewMode('duration')}
              size="small"
            >
              Durée
            </Button>
            <Button
              type={viewMode === 'both' ? 'primary' : 'default'}
              onClick={() => setViewMode('both')}
              size="small"
            >
              Les deux
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Chart */}
      <div style={{ 
        height: 'calc(100% - 70px)',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '12px',
        padding: '16px'
      }}>
        <Bar 
          ref={chartRef} 
          data={{
            labels: chartData.labels,
            datasets: getDatasets()
          }} 
          options={getOptions()} 
        />
      </div>
    </div>
  );
};

export default ArretOperatorChart;
