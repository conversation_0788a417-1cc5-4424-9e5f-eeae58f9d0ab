import{av as _,bk as S,r,a1 as b,aw as F,J as k,bl as h,q as O,aQ as z,bm as B,bn as T}from"./index-N0wOiMt6.js";function q(t,o,a){return typeof a=="boolean"?a:t.length?!0:_(o).some(s=>s.type===S)}var j=function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(a[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,e=Object.getOwnPropertySymbols(t);s<e.length;s++)o.indexOf(e[s])<0&&Object.prototype.propertyIsEnumerable.call(t,e[s])&&(a[e[s]]=t[e[s]]);return a};function d({suffixCls:t,tagName:o,displayName:a}){return e=>r.forwardRef((l,c)=>r.createElement(e,Object.assign({ref:c,suffixCls:t,tagName:o},l)))}const g=r.forwardRef((t,o)=>{const{prefixCls:a,suffixCls:e,className:s,tagName:l}=t,c=j(t,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:m}=r.useContext(b),n=m("layout",a),[u,C,y]=h(n),x=e?`${n}-${e}`:n;return u(r.createElement(l,Object.assign({className:O(a||x,s,C,y),ref:o},c)))}),J=r.forwardRef((t,o)=>{const{direction:a}=r.useContext(b),[e,s]=r.useState([]),{prefixCls:l,className:c,rootClassName:m,children:n,hasSider:u,tagName:C,style:y}=t,x=j(t,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),v=F(x,["suffixCls"]),{getPrefixCls:w,className:P,style:E}=k("layout"),f=w("layout",l),H=q(e,n,u),[L,$,A]=h(f),I=O(f,{[`${f}-has-sider`]:H,[`${f}-rtl`]:a==="rtl"},P,c,m,$,A),R=r.useMemo(()=>({siderHook:{addSider:p=>{s(N=>[].concat(z(N),[p]))},removeSider:p=>{s(N=>N.filter(V=>V!==p))}}}),[]);return L(r.createElement(B.Provider,{value:R},r.createElement(C,Object.assign({ref:o,className:I,style:Object.assign(Object.assign({},E),y)},v),n)))}),M=d({tagName:"div",displayName:"Layout"})(J),Q=d({suffixCls:"header",tagName:"header",displayName:"Header"})(g),W=d({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(g),D=d({suffixCls:"content",tagName:"main",displayName:"Content"})(g),i=M;i.Header=Q;i.Footer=W;i.Content=D;i.Sider=S;i._InternalSiderContext=T;export{i as L};
