/* Status Indicator Styles */
.ws-status-tag {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
  transition: all 0.3s ease;
}

.ws-status-tag .anticon {
  margin-right: 4px;
}

.ws-status-updating {
  animation: pulse 1.5s infinite;
}

/* Pulse animation for updating status */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/* Spin animation for loading indicators */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Connection status styles */
.connection-status {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.connection-status.connected {
  background-color: rgba(16, 185, 129, 0.1);
  border-left: 4px solid var(--somipem-success, #10B981);
}

.connection-status.connecting {
  background-color: rgba(30, 58, 138, 0.1);
  border-left: 4px solid var(--somipem-primary-blue, #1E3A8A);
}

.connection-status.disconnected {
  background-color: rgba(245, 158, 11, 0.1);
  border-left: 4px solid var(--somipem-warning, #F59E0B);
}

.connection-status.error {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid var(--somipem-error, #EF4444);
}
