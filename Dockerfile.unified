# Unified LOCQL Container - Frontend + Backend Development

FROM node:18-alpine

WORKDIR /app

# Install minimal system dependencies
RUN apk add --no-cache \
    bash \
    curl \
    git \
    python3 \
    make \
    g++

# Set environment variables
ENV NODE_ENV=production

# ---------------------------
# Install root dependencies
# ---------------------------
COPY package*.json ./
RUN echo "=== Installing root dependencies ===" && \
   npm install --verbose

# ---------------------------
# Install frontend dependencies
# ---------------------------
WORKDIR /app/frontend

# Copy only frontend package files for cache
COPY frontend/package*.json ./

RUN echo "=== Installing frontend dependencies ===" && \
    npm install --verbose

# Copy frontend source files
COPY frontend/. .

# Install Vite globally if needed
RUN npm install -g vite@6.3.5


# ---------------------------
# Install backend dependencies
# ---------------------------
WORKDIR /app/backend

# Copy only backend package files for cache
COPY backend/package*.json ./

RUN echo "=== Installing backend dependencies ===" && \
    npm install --verbose && \
    echo "=== Fixing bcrypt native dependency ===" && \
    npm uninstall bcrypt && \
    npm install bcrypt --build-from-source

# Copy backend source files
COPY backend/. .

# ---------------------------
# Final general setup
# ---------------------------

# Copy remaining root-level files if needed
WORKDIR /app

# Build frontend for production (creates dist folder)
RUN npm run build

COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Optimized ownership change (avoid node_modules)
RUN chown nodejs:nodejs /app && \
    chown -R nodejs:nodejs /app/frontend/dist /app/backend || true

USER nodejs

# Expose single port (backend serves API and built frontend)
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start unified production server
CMD ["npm", "run", "start"]
