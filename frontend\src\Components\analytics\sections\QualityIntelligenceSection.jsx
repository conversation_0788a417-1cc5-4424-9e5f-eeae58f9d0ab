import React from 'react';
import { Card, Space } from 'antd';
import { TrophyOutlined } from '@ant-design/icons';

const QualityIntelligenceSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <TrophyOutlined style={{ fontSize: '64px', color: '#eb2f96' }} />
          <h2 style={{ color: '#eb2f96', margin: 0 }}>Quality Intelligence</h2>
          <p style={{ color: '#8c8c8c' }}>AI-powered quality assurance and optimization coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default QualityIntelligenceSection;
