# Build and Push LOCQL Unified Container to Docker Hub (PowerShell)
# This script builds the production-ready unified container and pushes it to Docker Hub

param(
    [string]$Username = $env:DOCKER_USERNAME,
    [string]$Version = "1.0.0",
    [switch]$TestOnly,
    [switch]$SkipDbTest,
    [switch]$Help
)

# Configuration
$ImageName = "locql-unified"
$LatestTag = "latest"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Show-Usage {
    Write-Host "Usage: .\build-and-push.ps1 [OPTIONS]" -ForegroundColor $Colors.White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor $Colors.White
    Write-Host "  -Username USERNAME    Docker Hub username" -ForegroundColor $Colors.White
    Write-Host "  -Version VERSION      Image version tag (default: 1.0.0)" -ForegroundColor $Colors.White
    Write-Host "  -TestOnly            Build and test only, don't push" -ForegroundColor $Colors.White
    Write-Host "  -SkipDbTest          Skip database connectivity tests" -ForegroundColor $Colors.White
    Write-Host "  -Help                Show this help message" -ForegroundColor $Colors.White
    Write-Host ""
    Write-Host "Environment Variables:" -ForegroundColor $Colors.White
    Write-Host "  DOCKER_USERNAME      Docker Hub username" -ForegroundColor $Colors.White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor $Colors.White
    Write-Host "  .\build-and-push.ps1 -Username myusername -Version 1.2.0" -ForegroundColor $Colors.White
    Write-Host "  .\build-and-push.ps1 -TestOnly" -ForegroundColor $Colors.White
    Write-Host "  .\build-and-push.ps1 -TestOnly -SkipDbTest" -ForegroundColor $Colors.White
    Write-Host "  `$env:DOCKER_USERNAME='myuser'; .\build-and-push.ps1" -ForegroundColor $Colors.White
}

function Test-Docker {
    Write-Status "Checking Docker installation..."
    
    try {
        $null = Get-Command docker -ErrorAction Stop
    }
    catch {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    
    try {
        $null = docker info 2>$null
    }
    catch {
        Write-Error "Docker daemon is not running"
        exit 1
    }
    
    Write-Success "Docker is running"
}

function Test-DockerLogin {
    Write-Status "Checking Docker Hub authentication..."

    $dockerInfo = docker info 2>$null | Out-String
    if (-not ($dockerInfo -match "Username")) {
        Write-Warning "Not logged into Docker Hub. Please run: docker login"
        $response = Read-Host "Do you want to login now? (y/n)"
        if ($response -match "^[Yy]$") {
            docker login
        }
        else {
            Write-Error "Docker Hub login required to push images"
            exit 1
        }
    }
    Write-Success "Docker Hub authentication verified"
}

function Test-DatabaseConnectivity {
    param([switch]$SkipDbTest)

    if ($SkipDbTest) {
        Write-Warning "Skipping database connectivity test as requested"
        return $true
    }

    Write-Status "Checking database connectivity (matching working ngrok configuration)..."

    # Database configuration from ngrok.env and config.env
    $dbHost = "localhost"  # For host testing, use localhost
    $dbUser = "root"
    $dbPass = "root"
    $dbName = "Testingarea51"
    $dbPort = 3306

    Write-Status "Testing connection to MySQL database:"
    Write-Status "  Host: $dbHost"
    Write-Status "  Port: $dbPort"
    Write-Status "  User: $dbUser"
    Write-Status "  Database: $dbName"

    # Test if MySQL service is running
    try {
        $mysqlTest = Test-NetConnection -ComputerName $dbHost -Port $dbPort -WarningAction SilentlyContinue -ErrorAction Stop
        if (-not $mysqlTest.TcpTestSucceeded) {
            Write-Error "MySQL server is not running on ${dbHost}:${dbPort}"
            Write-Status "Please start MySQL server before running the test"
            return $false
        }
        Write-Success "MySQL server is running on ${dbHost}:${dbPort}"
    }
    catch {
        Write-Error "Failed to test MySQL connectivity: $_"
        return $false
    }

    # Test MySQL connection with credentials
    if (Get-Command mysql -ErrorAction SilentlyContinue) {
        try {
            Write-Status "Testing MySQL authentication..."
            $testQuery = "SELECT 1 as test;"
            $result = mysql -h $dbHost -P $dbPort -u $dbUser -p$dbPass -e $testQuery 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Success "MySQL authentication successful"

                # Test if database exists
                Write-Status "Checking if database '$dbName' exists..."
                $dbQuery = "SHOW DATABASES LIKE '$dbName';"
                $dbResult = mysql -h $dbHost -P $dbPort -u $dbUser -p$dbPass -e $dbQuery 2>$null
                if ($LASTEXITCODE -eq 0 -and $dbResult -match $dbName) {
                    Write-Success "Database '$dbName' exists and is accessible"
                    return $true
                }
                else {
                    Write-Warning "Database '$dbName' does not exist"
                    Write-Status "Creating database '$dbName'..."
                    $createQuery = "CREATE DATABASE IF NOT EXISTS ``$dbName``;"
                    mysql -h $dbHost -P $dbPort -u $dbUser -p$dbPass -e $createQuery 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        Write-Success "Database '$dbName' created successfully"
                        return $true
                    }
                    else {
                        Write-Error "Failed to create database '$dbName'"
                        return $false
                    }
                }
            }
            else {
                Write-Error "MySQL authentication failed with user '$dbUser'"
                Write-Status "Please verify MySQL credentials: user='$dbUser', password='$dbPass'"
                return $false
            }
        }
        catch {
            Write-Error "MySQL connection test failed: $_"
            return $false
        }
    }
    else {
        Write-Warning "MySQL client not found - cannot test database connectivity"
        Write-Status "The container test will proceed, but may fail if database is not accessible"
        return $true
    }
}

function Build-Image {
    Write-Status "Building LOCQL Unified Container..."
    Write-Status "Image: $Username/$ImageName`:$Version"
    
    $buildDate = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
    
    # Build with version tag
    $buildArgs = @(
        "build",
        "-f", "Dockerfile.production",
        "-t", "$Username/$ImageName`:$Version",
        "-t", "$Username/$ImageName`:$LatestTag",
        "--build-arg", "BUILD_DATE=$buildDate",
        "--build-arg", "VERSION=$Version",
        "."
    )
    
    & docker $buildArgs
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker build failed"
        exit 1
    }
    
    Write-Success "Docker image built successfully"
}

function Push-Image {
    Write-Status "Pushing image to Docker Hub..."
    
    # Push version tag
    docker push "$Username/$ImageName`:$Version"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to push version tag"
        exit 1
    }
    Write-Success "Pushed $Username/$ImageName`:$Version"
    
    # Push latest tag
    docker push "$Username/$ImageName`:$LatestTag"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to push latest tag"
        exit 1
    }
    Write-Success "Pushed $Username/$ImageName`:$LatestTag"
}

# Main execution
function Main {
    if ($Help) {
        Show-Usage
        exit 0
    }
    
    # Validate Docker username
    if (-not $Username -or $Username -eq "your-dockerhub-username") {
        Write-Error "Please set your Docker Hub username using -Username parameter or DOCKER_USERNAME environment variable"
        Show-Usage
        exit 1
    }
    
    Write-Status "Starting LOCQL Unified Container build process..."
    Write-Status "Docker Username: $Username"
    Write-Status "Image Name: $ImageName"
    Write-Status "Version: $Version"
    Write-Status "Test Only: $TestOnly"
    Write-Status "Skip DB Test: $SkipDbTest"
    Write-Host ""

    Test-Docker

    # Test database connectivity before building (unless skipped)
    if (-not (Test-DatabaseConnectivity -SkipDbTest:$SkipDbTest)) {
        Write-Error "Database connectivity test failed"
        Write-Status "You can skip database tests with -SkipDbTest parameter"
        Write-Status "Example: .\build-and-push.ps1 -Username $Username -TestOnly -SkipDbTest"
        exit 1
    }

    if (-not $TestOnly) {
        Test-DockerLogin
    }

    Build-Image
    
    if (-not $TestOnly) {
        Push-Image
        Write-Success "🎉 Image successfully pushed to Docker Hub!"
        Write-Host ""
        Write-Status "You can now pull and run the image with:"
        Write-Host "  docker pull $Username/$ImageName`:$Version" -ForegroundColor $Colors.White
        Write-Host "  docker run -p 5000:5000 $Username/$ImageName`:$Version" -ForegroundColor $Colors.White
    }
    else {
        Write-Success "🧪 Build and test completed successfully!"
        Write-Host ""
        Write-Status "To push the image, run without -TestOnly flag"
    }
}

# Run main function
Main
