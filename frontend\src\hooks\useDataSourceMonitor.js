/**
 * Data Source Monitor Hook
 * Provides real-time monitoring of Elasticsearch and MySQL availability
 * Implements automatic retry mechanisms and status notifications
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { notification } from 'antd';
import { ThunderboltOutlined, ToolOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const useDataSourceMonitor = () => {
  const [status, setStatus] = useState({
    elasticsearch: false,
    mysql: true,
    primary: 'mysql',
    lastCheck: null,
    retryCount: 0
  });

  const [isMonitoring, setIsMonitoring] = useState(false);
  const retryTimeoutRef = useRef(null);
  const checkIntervalRef = useRef(null);

  // Test data source connectivity
  const testDataSources = useCallback(async () => {
    try {
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query TestDataSources {
              enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) {
                dataSource
                goodqty
              }
            }
          `
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        console.warn('GraphQL errors during data source test:', result.errors);
        return { elasticsearch: false, mysql: true, primary: 'mysql' };
      }

      const dataSource = result.data?.enhancedGetProductionSidecards?.dataSource || 'mysql';
      
      return {
        elasticsearch: dataSource === 'elasticsearch',
        mysql: dataSource === 'mysql' || dataSource === 'unknown',
        primary: dataSource
      };
    } catch (error) {
      console.error('Error testing data sources:', error);
      return { elasticsearch: false, mysql: true, primary: 'mysql' };
    }
  }, []);

  // Handle status changes and notifications
  const handleStatusChange = useCallback((newStatus) => {
    const prevStatus = status;
    
    setStatus(prev => ({
      ...prev,
      ...newStatus,
      lastCheck: new Date().toISOString(),
      retryCount: newStatus.elasticsearch ? 0 : prev.retryCount + 1
    }));

    // Show notifications for status changes
    if (prevStatus.primary !== newStatus.primary) {
      if (newStatus.elasticsearch && !prevStatus.elasticsearch) {
        notification.success({
          message: 'Elasticsearch Restored',
          description: 'Primary data source is now available. Performance improved.',
         icon: <ThunderboltOutlined style={{ color: '#52c41a' }} />,
          duration: 4
        });
      } else if (!newStatus.elasticsearch && prevStatus.elasticsearch) {
        notification.warning({
          message: 'Elasticsearch Unavailable',
          description: 'Switched to MySQL fallback. Data still available.',
          icon: <ToolOutlined style={{ color: '#faad14' }} />,
          duration: 6
        });
      }
    }
  }, [status]);

  // Start monitoring data sources
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    setIsMonitoring(true);
    
    const checkStatus = async () => {
      const newStatus = await testDataSources();
      handleStatusChange(newStatus);
    };

    // Initial check
    checkStatus();

    // Set up periodic checks every 30 seconds
    checkIntervalRef.current = setInterval(checkStatus, 30000);

    console.log('🔍 Data source monitoring started');
  }, [isMonitoring, testDataSources, handleStatusChange]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);
    
    if (checkIntervalRef.current) {
      clearInterval(checkIntervalRef.current);
      checkIntervalRef.current = null;
    }

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    console.log('🔍 Data source monitoring stopped');
  }, [isMonitoring]);

  // Retry Elasticsearch connection
  const retryElasticsearch = useCallback(async () => {
    if (status.retryCount >= 5) {
      notification.error({
        message: 'Elasticsearch Connection Failed',
        description: 'Maximum retry attempts reached. Please check the service.',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        duration: 8
      });
      return;
    }

    console.log(`🔄 Retrying Elasticsearch connection (attempt ${status.retryCount + 1}/5)`);
    
    const newStatus = await testDataSources();
    handleStatusChange(newStatus);

    // If still not available, schedule next retry with exponential backoff
    if (!newStatus.elasticsearch) {
      const delay = Math.min(1000 * Math.pow(2, status.retryCount), 30000); // Max 30 seconds
      retryTimeoutRef.current = setTimeout(retryElasticsearch, delay);
    }
  }, [status.retryCount, testDataSources, handleStatusChange]);

  // Manual refresh
  const refreshStatus = useCallback(async () => {
    const newStatus = await testDataSources();
    handleStatusChange(newStatus);
    return newStatus;
  }, [testDataSources, handleStatusChange]);

  // Auto-start monitoring on mount
  useEffect(() => {
    startMonitoring();
    return () => stopMonitoring();
  }, [startMonitoring, stopMonitoring]);

  // Auto-retry when Elasticsearch is down
  useEffect(() => {
    if (!status.elasticsearch && isMonitoring && status.retryCount < 5) {
      const delay = Math.min(5000 * Math.pow(1.5, status.retryCount), 30000);
      retryTimeoutRef.current = setTimeout(retryElasticsearch, delay);
    }

    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [status.elasticsearch, status.retryCount, isMonitoring, retryElasticsearch]);

  return {
    status,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    refreshStatus,
    retryElasticsearch
  };
};

export default useDataSourceMonitor;
