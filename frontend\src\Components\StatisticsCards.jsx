import React from "react";
import PropTypes from "prop-types";
import { Row, Col, Card, Statistic } from "antd";
import {
  RiseOutlined,
  FallOutlined,
  DashboardOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined
} from "@ant-design/icons";
import { formatStatValue } from "../utils/numberFormatter";
import SOMIPEM_COLORS from "../styles/brand-colors";

/**
 * StatisticsCards component to display production statistics
 * @param {Object} props - Component props
 * @param {number} props.goodQty - Good quantity
 * @param {number} props.rejetQty - Rejected quantity
 * @param {number} props.avgTRS - Average TRS
 * @param {number} props.avgAvailability - Average availability
 * @param {number} props.avgPerformance - Average performance
 * @param {number} props.rejectRate - Reject rate
 * @param {number} props.qualityRate - Quality rate
 * @returns {JSX.Element} - Rendered component
 */
const StatisticsCards = ({
  goodQty = 0,
  rejetQty = 0,
  avgTRS = 0,
  avgAvailability = 0,
  avgPerformance = 0,
  rejectRate = 0,
  qualityRate = 0
}) => {
  // Statistics data with French number formatting
  const stats = [
    {
      title: "Production Totale",
      value: formatStatValue(goodQty, "Pcs"),
      suffix: "Pcs",
      icon: <RiseOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Nombre total de pièces bonnes produites",
    },
    {
      title: "Rejet Total",
      value: formatStatValue(rejetQty, "Kg"),
      suffix: "Kg",
      icon: <FallOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Nombre total de pièces rejetées",
    },
    {
      title: "TRS Moyen",
      value: formatStatValue(avgTRS, "%"),
      suffix: "%",
      icon: <DashboardOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Taux de Rendement Synthétique moyen (OEE_Day)",
    },
    {
      title: "Disponibilité",
      value: formatStatValue(avgAvailability, "%"),
      suffix: "%",
      icon: <ClockCircleOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Taux de disponibilité moyen (Availability_Rate_Day)",
    },
    {
      title: "Performance",
      value: formatStatValue(avgPerformance, "%"),
      suffix: "%",
      icon: <ThunderboltOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Taux de performance moyen (Performance_Rate_Day)",
    },
    {
      title: "Taux de Rejet",
      value: formatStatValue(rejectRate, "%"),
      suffix: "%",
      icon: <CloseCircleOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Pourcentage de pièces rejetées sur la production totale",
    },
    {
      title: "Taux de Qualité",
      value: formatStatValue(qualityRate, "%"),
      suffix: "%",
      icon: <CheckCircleOutlined />,
      color: SOMIPEM_COLORS.PRIMARY_BLUE,
      description: "Pourcentage de pièces bonnes sur la production totale",
    },
  ];

  return (
    <Row gutter={[16, 16]}>
      {stats.map((stat, index) => (
        <Col xs={24} sm={12} md={8} lg={6} xl={6} key={index}>
          <Card hoverable>
            <Statistic
              title={stat.title}
              value={stat.value}
              suffix={stat.suffix}
              valueStyle={{ color: stat.color }}
              prefix={React.cloneElement(stat.icon, { style: { color: stat.color } })}
              formatter={(value) => value} // Use pre-formatted value
            />
            <div style={{ marginTop: 8, fontSize: 12, color: "#888" }}>{stat.description}</div>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

// PropTypes for type checking
StatisticsCards.propTypes = {
  goodQty: PropTypes.number,
  rejetQty: PropTypes.number,
  avgTRS: PropTypes.number,
  avgAvailability: PropTypes.number,
  avgPerformance: PropTypes.number,
  rejectRate: PropTypes.number,
  qualityRate: PropTypes.number
};

export default StatisticsCards;
