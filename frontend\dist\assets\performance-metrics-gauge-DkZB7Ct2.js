import{r as u,am as ue,al as ve,an as je,ao as ge,Q as Fe,U as Ue,V as $,ap as Xe,J as qe,aq as fe,q as Z,ar as Ve,as as Ke,R as t,E as F,T as V,m as Je,S as m,e as ee,b as Qe,c as te,C as re,d as ne}from"./index-N0wOiMt6.js";import{R as we,C as Se,X as Ce,Y as le,T as Re,a as $e,g as Me,f as Ae,d as Ze,B as et,b as tt}from"./PieChart-BZME-zsX.js";import{C as rt}from"./ComposedChart-Bx4Th9mw.js";import{d as be}from"./dayjs.min-BHt7dFLo.js";import{i as nt,g as at,d as it,e as ot,f as lt}from"./DownloadOutlined-C8TU0wLq.js";import{R as xe}from"./ClockCircleOutlined-C6SZLNSK.js";import{R as st}from"./InfoCircleOutlined-DImdGCrM.js";import{S as ae}from"./index-BP6n0Cjb.js";import{P as ie}from"./progress-CyD0QBQj.js";const dt=10,ct=20;function mt(e){const{fullscreen:r,validRange:o,generateConfig:a,locale:p,prefixCls:n,value:E,onChange:v,divRef:g}=e,d=a.getYear(E||a.getNow());let l=d-dt,c=l+ct;o&&(l=a.getYear(o[0]),c=a.getYear(o[1])+1);const R=p&&p.year==="年"?"年":"",w=[];for(let i=l;i<c;i++)w.push({label:`${i}${R}`,value:i});return u.createElement(ve,{size:r?void 0:"small",options:w,value:d,className:`${n}-year-select`,onChange:i=>{let x=a.setYear(E,i);if(o){const[f,b]=o,S=a.getYear(x),h=a.getMonth(x);S===a.getYear(b)&&h>a.getMonth(b)&&(x=a.setMonth(x,a.getMonth(b))),S===a.getYear(f)&&h<a.getMonth(f)&&(x=a.setMonth(x,a.getMonth(f)))}v(x)},getPopupContainer:()=>g.current})}function pt(e){const{prefixCls:r,fullscreen:o,validRange:a,value:p,generateConfig:n,locale:E,onChange:v,divRef:g}=e,d=n.getMonth(p||n.getNow());let l=0,c=11;if(a){const[i,x]=a,f=n.getYear(p);n.getYear(x)===f&&(c=n.getMonth(x)),n.getYear(i)===f&&(l=n.getMonth(i))}const R=E.shortMonths||n.locale.getShortMonths(E.locale),w=[];for(let i=l;i<=c;i+=1)w.push({label:R[i],value:i});return u.createElement(ve,{size:o?void 0:"small",className:`${r}-month-select`,value:d,options:w,onChange:i=>{v(n.setMonth(p,i))},getPopupContainer:()=>g.current})}function ut(e){const{prefixCls:r,locale:o,mode:a,fullscreen:p,onModeChange:n}=e;return u.createElement(je,{onChange:({target:{value:E}})=>{n(E)},value:a,size:p?void 0:"small",className:`${r}-mode-switch`},u.createElement(ge,{value:"month"},o.month),u.createElement(ge,{value:"year"},o.year))}function gt(e){const{prefixCls:r,fullscreen:o,mode:a,onChange:p,onModeChange:n}=e,E=u.useRef(null),v=u.useContext(ue),g=u.useMemo(()=>Object.assign(Object.assign({},v),{isFormItemInput:!1}),[v]),d=Object.assign(Object.assign({},e),{fullscreen:o,divRef:E});return u.createElement("div",{className:`${r}-header`,ref:E},u.createElement(ue.Provider,{value:g},u.createElement(mt,Object.assign({},d,{onChange:l=>{p(l,"year")}})),a==="month"&&u.createElement(pt,Object.assign({},d,{onChange:l=>{p(l,"month")}}))),u.createElement(ut,Object.assign({},d,{onModeChange:n})))}const ft=e=>{const{calendarCls:r,componentCls:o,fullBg:a,fullPanelBg:p,itemActiveBg:n}=e;return{[r]:Object.assign(Object.assign(Object.assign({},at(e)),Xe(e)),{background:a,"&-rtl":{direction:"rtl"},[`${r}-header`]:{display:"flex",justifyContent:"flex-end",padding:`${$(e.paddingSM)} 0`,[`${r}-year-select`]:{minWidth:e.yearControlWidth},[`${r}-month-select`]:{minWidth:e.monthControlWidth,marginInlineStart:e.marginXS},[`${r}-mode-switch`]:{marginInlineStart:e.marginXS}}}),[`${r} ${o}-panel`]:{background:p,border:0,borderTop:`${$(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,borderRadius:0,[`${o}-month-panel, ${o}-date-panel`]:{width:"auto"},[`${o}-body`]:{padding:`${$(e.paddingXS)} 0`},[`${o}-content`]:{width:"100%"}},[`${r}-mini`]:{borderRadius:e.borderRadiusLG,[`${r}-header`]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS},[`${o}-panel`]:{borderRadius:`0 0 ${$(e.borderRadiusLG)} ${$(e.borderRadiusLG)}`},[`${o}-content`]:{height:e.miniContentHeight,th:{height:"auto",padding:0,lineHeight:$(e.weekHeight)}},[`${o}-cell::before`]:{pointerEvents:"none"}},[`${r}${r}-full`]:{[`${o}-panel`]:{display:"block",width:"100%",textAlign:"end",background:a,border:0,[`${o}-body`]:{"th, td":{padding:0},th:{height:"auto",paddingInlineEnd:e.paddingSM,paddingBottom:e.paddingXXS,lineHeight:$(e.weekHeight)}}},[`${o}-cell-week ${o}-cell-inner`]:{display:"block",borderRadius:0,borderTop:`${$(e.lineWidthBold)} ${e.lineType} ${e.colorSplit}`,width:"100%",height:e.calc(e.dateValueHeight).add(e.dateContentHeight).add(e.calc(e.paddingXS).div(2)).add(e.lineWidthBold).equal()},[`${o}-cell`]:{"&::before":{display:"none"},"&:hover":{[`${r}-date`]:{background:e.controlItemBgHover}},[`${r}-date-today::before`]:{display:"none"},[`&-in-view${o}-cell-selected`]:{[`${r}-date, ${r}-date-today`]:{background:n}},"&-selected, &-selected:hover":{[`${r}-date, ${r}-date-today`]:{[`${r}-date-value`]:{color:e.colorPrimary}}}},[`${r}-date`]:{display:"block",width:"auto",height:"auto",margin:`0 ${$(e.calc(e.marginXS).div(2).equal())}`,padding:`${$(e.calc(e.paddingXS).div(2).equal())} ${$(e.paddingXS)} 0`,border:0,borderTop:`${$(e.lineWidthBold)} ${e.lineType} ${e.colorSplit}`,borderRadius:0,transition:`background ${e.motionDurationSlow}`,"&-value":{lineHeight:$(e.dateValueHeight),transition:`color ${e.motionDurationSlow}`},"&-content":{position:"static",width:"auto",height:e.dateContentHeight,overflowY:"auto",color:e.colorText,lineHeight:e.lineHeight,textAlign:"start"},"&-today":{borderColor:e.colorPrimary,[`${r}-date-value`]:{color:e.colorText}}}},[`@media only screen and (max-width: ${$(e.screenXS)}) `]:{[r]:{[`${r}-header`]:{display:"block",[`${r}-year-select`]:{width:"50%"},[`${r}-month-select`]:{width:`calc(50% - ${$(e.paddingXS)})`},[`${r}-mode-switch`]:{width:"100%",marginTop:e.marginXS,marginInlineStart:0,"> label":{width:"50%",textAlign:"center"}}}}}}},bt=e=>Object.assign({fullBg:e.colorBgContainer,fullPanelBg:e.colorBgContainer,itemActiveBg:e.controlItemBgActive,yearControlWidth:80,monthControlWidth:70,miniContentHeight:256},it(e)),xt=Fe("Calendar",e=>{const r=`${e.componentCls}-calendar`,o=Ue(e,nt(e),{calendarCls:r,pickerCellInnerCls:`${e.componentCls}-cell-inner`,dateValueHeight:e.controlHeightSM,weekHeight:e.calc(e.controlHeightSM).mul(.75).equal(),dateContentHeight:e.calc(e.calc(e.fontHeightSM).add(e.marginXS)).mul(3).add(e.calc(e.lineWidth).mul(2)).equal()});return[ft(o)]},bt),Te=(e,r,o)=>{const{getYear:a}=o;return e&&r&&a(e)===a(r)},se=(e,r,o)=>{const{getMonth:a}=o;return Te(e,r,o)&&a(e)===a(r)},he=(e,r,o)=>{const{getDate:a}=o;return se(e,r,o)&&a(e)===a(r)},Ie=e=>o=>{const{prefixCls:a,className:p,rootClassName:n,style:E,dateFullCellRender:v,dateCellRender:g,monthFullCellRender:d,monthCellRender:l,cellRender:c,fullCellRender:R,headerRender:w,value:i,defaultValue:x,disabledDate:f,mode:b,validRange:S,fullscreen:h=!0,showWeek:k,onChange:M,onPanelChange:D,onSelect:y}=o,{getPrefixCls:K,direction:T,className:de,style:J}=qe("calendar"),I=K("picker",a),A=`${I}-calendar`,[De,ke,Be]=xt(I,A),ce=e.getNow(),[N,Ne]=fe(()=>i||e.getNow(),{defaultValue:x,value:i}),[W,ze]=fe("month",{value:b}),G=u.useMemo(()=>W==="year"?"month":"date",[W]),Oe=u.useCallback(s=>(S?e.isAfter(S[0],s)||e.isAfter(s,S[1]):!1)||!!(f!=null&&f(s)),[f,S]),me=(s,C)=>{D==null||D(s,C)},Pe=s=>{Ne(s),he(s,N,e)||((G==="date"&&!se(s,N,e)||G==="month"&&!Te(s,N,e))&&me(s,W),M==null||M(s))},pe=s=>{ze(s),me(N,s)},Q=(s,C)=>{Pe(s),y==null||y(s,{source:C})},Le=u.useCallback((s,C)=>R?R(s,C):v?v(s):u.createElement("div",{className:Z(`${I}-cell-inner`,`${A}-date`,{[`${A}-date-today`]:he(ce,s,e)})},u.createElement("div",{className:`${A}-date-value`},String(e.getDate(s)).padStart(2,"0")),u.createElement("div",{className:`${A}-date-content`},c?c(s,C):g==null?void 0:g(s))),[v,g,c,R]),_e=u.useCallback((s,C)=>{if(R)return R(s,C);if(d)return d(s);const He=C.locale.shortMonths||e.locale.getShortMonths(C.locale.locale);return u.createElement("div",{className:Z(`${I}-cell-inner`,`${A}-date`,{[`${A}-date-today`]:se(ce,s,e)})},u.createElement("div",{className:`${A}-date-value`},He[e.getMonth(s)]),u.createElement("div",{className:`${A}-date-content`},c?c(s,C):l==null?void 0:l(s)))},[d,l,c,R]),[We]=Ve("Calendar",Ke),B=Object.assign(Object.assign({},We),o.locale),Ge=(s,C)=>{if(C.type==="date")return Le(s,C);if(C.type==="month")return _e(s,Object.assign(Object.assign({},C),{locale:B==null?void 0:B.lang}))};return De(u.createElement("div",{className:Z(A,{[`${A}-full`]:h,[`${A}-mini`]:!h,[`${A}-rtl`]:T==="rtl"},de,p,n,ke,Be),style:Object.assign(Object.assign({},J),E)},w?w({value:N,type:W,onChange:s=>{Q(s,"customize")},onTypeChange:pe}):u.createElement(gt,{prefixCls:A,value:N,generateConfig:e,mode:W,fullscreen:h,locale:B==null?void 0:B.lang,validRange:S,onChange:Q,onModeChange:pe}),u.createElement(ot,{value:N,prefixCls:I,locale:B==null?void 0:B.lang,generateConfig:e,cellRender:Ge,onSelect:s=>{Q(s,G)},mode:G,picker:G,disabledDate:Oe,hideHeader:!0,showWeek:k})))},Ye=Ie(lt);Ye.generateCalendar=Ie;const{Text:U}=V,X={danger:"#f5222d"},ye=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96"],ht=e=>{if(e==null)return"N/A";let r=Number(e);return!isNaN(r)&&r>0&&r<1&&(r=r*100),`${r.toFixed(1)}%`},yt=({data:e=[],selectedMachine:r="",selectedDate:o=null,dateRangeType:a="day",loading:p=!1})=>{const n=t.useMemo(()=>{if(!e||!Array.isArray(e)||e.length===0)return[];try{const l=[...e].sort((w,i)=>i.duration-w.duration),c=l.reduce((w,i)=>w+i.duration,0);let R=0;return l.map((w,i)=>{R+=w.duration;const x=R/c;return{...w,cumulativePercentage:x}})}catch(l){return console.error("Error processing downtime pareto data:",l),[]}},[e]),E=({active:l,payload:c,label:R})=>{if(!l||!c||!c.length)return null;const w=c[0].payload;return t.createElement("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}},t.createElement(U,{strong:!0},w.reason),t.createElement("div",null,t.createElement(U,null,"Durée: ",w.duration," min")),t.createElement("div",null,t.createElement(U,null,"Fréquence: ",w.count," arrêts")),t.createElement("div",null,t.createElement(U,null,"Impact cumulé: ",ht(w.cumulativePercentage))))},v=l=>l?l.length>15?`${l.substring(0,12)}...`:l:"";if(!n||n.length===0)return t.createElement(F,{description:"Aucune donnée disponible pour l'analyse Pareto"});const g=.8,d=n.filter(l=>l.cumulativePercentage<=g).slice(0,10).map(l=>({...l,cumulativePercentage:l.cumulativePercentage*100}));return t.createElement(we,{width:"100%",height:350},t.createElement(rt,{data:d,margin:{top:20,right:30,left:20,bottom:60}},t.createElement(Se,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),t.createElement(Ce,{dataKey:"reason",tickFormatter:v,tick:{fill:"#666",angle:-45,textAnchor:"end"},height:70,interval:0}),t.createElement(le,{yAxisId:"left",label:{value:"Durée d'arrêt (min)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),t.createElement(le,{yAxisId:"right",orientation:"right",domain:[0,100],tickFormatter:l=>`${l}%`,label:{value:"Pourcentage cumulé",angle:90,position:"insideRight",style:{fill:X.danger}},tick:{fill:X.danger}}),t.createElement(Re,{content:t.createElement(E,null)}),t.createElement($e,null),t.createElement(Me,{yAxisId:"left",dataKey:"duration",name:"Durée d'arrêt",barSize:30,isAnimationActive:!p},d.map((l,c)=>t.createElement(Ae,{key:`cell-${c}`,fill:ye[c%ye.length]}))),t.createElement(Ze,{yAxisId:"right",type:"monotone",dataKey:"cumulativePercentage",name:"Pourcentage cumulé",stroke:X.danger,strokeWidth:2,dot:{fill:X.danger,strokeWidth:2,r:4},isAnimationActive:!p})))},kt=t.memo(yt),{Text:H}=V,_={primary:"#1890ff",secondary:"#13c2c2",success:"#52c41a",warning:"#faad14",purple:"#722ed1"},oe={IPS:_.primary,CCM24:_.purple,default:_.secondary},Et=e=>{if(e==null)return"N/A";let r=Number(e);return!isNaN(r)&&r>0&&r<1&&(r=r*100),`${r.toFixed(1)}%`},vt=({data:e=[],selectedMachine:r="",selectedMachineModel:o="",targetValue:a=85,loading:p=!1})=>{const n=t.useMemo(()=>{if(!e||!Array.isArray(e)||e.length===0)return[];try{return[...e].map(d=>{const l={...d};if(l.disponibilite!==void 0&&l.disponibilite!==null){const c=Number(l.disponibilite);!isNaN(c)&&c>0&&c<1&&(l.disponibilite=c*100)}return l}).sort((d,l)=>l.disponibilite-d.disponibilite)}catch(g){return console.error("Error processing disponibilite data:",g),[]}},[e]),E=({active:g,payload:d,label:l})=>{if(!g||!d||!d.length)return null;const c=d[0].payload;return t.createElement("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}},t.createElement(H,{strong:!0},c.machine),t.createElement("div",null,t.createElement(H,null,"Disponibilité: ",Et(c.disponibilite))),t.createElement("div",null,t.createElement(H,null,"MTTR: ",Number(c.mttr).toFixed(1)," min")),t.createElement("div",null,t.createElement(H,null,"MTBF: ",Number(c.mtbf).toFixed(1)," min")),t.createElement("div",null,t.createElement(H,null,"Nombre d'arrêts: ",c.stops)))},v=(g,d)=>g===r?_.warning:d&&oe[d]?oe[d]:oe.default;return!n||n.length===0?t.createElement(F,{description:"Aucune donnée disponible pour la comparaison des machines"}):t.createElement(we,{width:"100%",height:350},t.createElement(et,{data:n,layout:"vertical",margin:{top:20,right:30,left:80,bottom:10}},t.createElement(Se,{strokeDasharray:"3 3",stroke:"#f0f0f0",horizontal:!0,vertical:!1}),t.createElement(Ce,{type:"number",domain:[0,100],tickFormatter:g=>`${g}%`,tick:{fill:"#666"}}),t.createElement(le,{dataKey:"machine",type:"category",tick:{fill:"#666"},width:70}),t.createElement(Re,{content:t.createElement(E,null)}),t.createElement($e,null),t.createElement(tt,{x:a,stroke:_.success,strokeDasharray:"3 3",label:{value:`Objectif: ${a}%`,position:"top",fill:_.success,fontSize:12}}),t.createElement(Me,{dataKey:"disponibilite",name:"Disponibilité",radius:[0,4,4,0],isAnimationActive:!p},n.map((g,d)=>t.createElement(Ae,{key:`cell-${d}`,fill:v(g.machine,g.model),stroke:g.machine===r?"#000":void 0,strokeWidth:g.machine===r?1:0})))))},Bt=t.memo(vt),{Text:j}=V,Y={low:m.SECONDARY_BLUE,medium:m.LIGHT_GRAY,high:m.DARK_GRAY,excellent:m.PRIMARY_BLUE,critical:"#1a1a1a",excellentGradient:`linear-gradient(135deg, ${m.PRIMARY_BLUE}, #1a365d)`,lowGradient:`linear-gradient(135deg, ${m.SECONDARY_BLUE}, ${m.PRIMARY_BLUE})`,mediumGradient:`linear-gradient(135deg, ${m.LIGHT_GRAY}, ${m.DARK_GRAY})`,highGradient:`linear-gradient(135deg, ${m.DARK_GRAY}, #1a1a1a)`,criticalGradient:"linear-gradient(135deg, #1a1a1a, #000000)",noneGradient:"linear-gradient(135deg, #fafafa, #f0f0f0)"},O={excellent:{boxShadow:"0 4px 20px rgba(30, 58, 138, 0.4), 0 0 20px rgba(30, 58, 138, 0.2)",border:"2px solid rgba(30, 58, 138, 0.3)"},low:{boxShadow:"0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)",border:"2px solid rgba(59, 130, 246, 0.3)"},medium:{boxShadow:"0 4px 20px rgba(107, 114, 128, 0.4), 0 0 20px rgba(107, 114, 128, 0.2)",border:"2px solid rgba(107, 114, 128, 0.3)"},high:{boxShadow:"0 4px 20px rgba(31, 41, 55, 0.4), 0 0 20px rgba(31, 41, 55, 0.2)",border:"2px solid rgba(31, 41, 55, 0.3)"},critical:{boxShadow:"0 4px 20px rgba(26, 26, 26, 0.5), 0 0 25px rgba(26, 26, 26, 0.3)",border:"2px solid rgba(26, 26, 26, 0.4)"},none:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",border:"1px solid rgba(0, 0, 0, 0.1)"}},q=e=>{if(e==null)return"N/A";let r=Number(e);return!isNaN(r)&&r>0&&r<1&&(r=r*100),r.toFixed(1)},Ee=(e,r)=>e==null?"Aucune donnée":e<=r.low*.5?"Excellent":e<=r.low?"Bon":e<=r.medium?"Moyen":e<=r.medium*1.5?"Mauvais":"Critique",wt=({data:e=[],selectedMachine:r="",selectedDate:o=null,dateRangeType:a="day",loading:p=!1,thresholds:n={low:15,medium:30}})=>{const E=u.useMemo(()=>{const i=new Map;if(!e||!Array.isArray(e))return console.error("MTTR Calendar data is not an array:",e),i;try{e.forEach(x=>{const f={...x};if(f.mttr!==void 0&&f.mttr!==null){const S=Number(f.mttr);!isNaN(S)&&S>0&&S<1&&(f.mttr=S*100)}const b=be(f.date).format("YYYY-MM-DD");i.set(b,f)})}catch(x){console.error("Error processing MTTR calendar data:",x)}return i},[e]),v=u.useMemo(()=>o||a==="day"||a==="week"?"month":a==="month"?"year":"month",[a,o]),g=u.useMemo(()=>o||be(),[o]),d=i=>i==null?Y.noneGradient:i<=n.low*.5?Y.excellentGradient:i<=n.low?Y.lowGradient:i<=n.medium?Y.mediumGradient:i<=n.medium*1.5?Y.highGradient:Y.criticalGradient,l=i=>i==null?O.none:i<=n.low*.5?O.excellent:i<=n.low?O.low:i<=n.medium?O.medium:i<=n.medium*1.5?O.high:O.critical,c=i=>{const x=i.format("YYYY-MM-DD"),f=E.get(x);if(!f)return null;const b=f.mttr,S=d(b),h=l(b),k=Ee(b,n),M=f.stops||0,D=f.availability||0;return t.createElement("div",{style:{position:"relative",height:"100%",padding:"2px"}},t.createElement(ee,{title:t.createElement("div",{style:{textAlign:"center",background:`linear-gradient(135deg, ${m.PRIMARY_BLUE}, ${m.DARK_GRAY})`,borderRadius:"8px",padding:"12px",border:"1px solid rgba(255,255,255,0.1)"}},t.createElement("div",{style:{fontWeight:"bold",marginBottom:"8px",fontSize:"14px",color:m.WHITE,textShadow:"0 1px 2px rgba(0,0,0,0.5)"}},"📅 ",i.format("DD/MM/YYYY")),t.createElement("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"⏱️ MTTR: ",t.createElement("span",{style:{fontWeight:"bold",color:m.SECONDARY_BLUE}},q(b)," min")),t.createElement("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"🔧 Statut: ",t.createElement("span",{style:{fontWeight:"bold",color:d(b).includes("excellent")?m.SECONDARY_BLUE:d(b).includes("critical")?m.LIGHT_GRAY:m.SECONDARY_BLUE}},k)),M>0&&t.createElement("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"🚨 Arrêts: ",t.createElement("span",{style:{fontWeight:"bold",color:m.LIGHT_GRAY}},M)),D>0&&t.createElement("div",{style:{padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"}},"📊 Disponibilité: ",t.createElement("span",{style:{fontWeight:"bold",color:m.SECONDARY_BLUE}},D.toFixed(1),"%"))),placement:"auto",autoAdjustOverflow:!0,getPopupContainer:y=>y.parentElement||document.body,overlayStyle:{maxWidth:"280px",zIndex:1050},overlayInnerStyle:{maxWidth:"280px",wordWrap:"break-word"},mouseEnterDelay:.3,mouseLeaveDelay:.1},t.createElement("div",{style:{position:"relative",width:"100%",height:"100%",minHeight:"50px",borderRadius:"12px",background:S,display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",fontWeight:"700",color:"white",textShadow:"0 1px 3px rgba(0,0,0,0.5)",cursor:"pointer",transition:"all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",transform:"scale(1)",...h,animation:b>n.medium*1.5?"pulseGlow 2s infinite, sparkle 3s infinite":b<=n.low*.5?"excellentGlow 3s infinite":"none",backgroundImage:`${S}, linear-gradient(145deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%)`,backgroundBlendMode:"normal, overlay",boxShadow:`${h.boxShadow}, inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1)`},onMouseEnter:y=>{y.target.style.transform="scale(1.15) rotate(3deg)",y.target.style.zIndex="10",y.target.style.filter="brightness(1.2) saturate(1.2)",y.target.style.boxShadow=h.boxShadow.replace(/0.4/g,"0.8").replace(/0.2/g,"0.6")+", 0 8px 32px rgba(0,0,0,0.2)",b<=n.low*.5&&(y.target.style.animation="excellentGlow 3s infinite, sparkle 1s infinite")},onMouseLeave:y=>{y.target.style.transform="scale(1) rotate(0deg)",y.target.style.zIndex="1",y.target.style.filter="brightness(1) saturate(1)",y.target.style.boxShadow=h.boxShadow,b<=n.low*.5?y.target.style.animation="excellentGlow 3s infinite":b>n.medium*1.5?y.target.style.animation="pulseGlow 2s infinite, sparkle 3s infinite":y.target.style.animation="none"}},"            ",t.createElement("div",{style:{position:"absolute",top:"2px",right:"2px",width:"8px",height:"8px",borderRadius:"50%",background:"radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 70%, transparent 100%)",animation:"sparkle 3s infinite",boxShadow:"0 0 6px rgba(255,255,255,0.6)"}}),b<=n.low*.5&&t.createElement(t.Fragment,null,t.createElement("div",{style:{position:"absolute",top:"1px",left:"1px",width:"3px",height:"3px",borderRadius:"50%",background:"rgba(255,255,255,0.9)",animation:"sparkle 2s infinite 0.5s"}}),t.createElement("div",{style:{position:"absolute",bottom:"1px",right:"1px",width:"2px",height:"2px",borderRadius:"50%",background:"rgba(255,255,255,0.7)",animation:"sparkle 2.5s infinite 1s"}})),b>n.medium*1.5&&t.createElement("div",{style:{position:"absolute",top:"-2px",right:"-2px",width:"12px",height:"12px",borderRadius:"50%",background:"linear-gradient(45deg, #ff4d4f, #f5222d)",border:"2px solid rgba(255,255,255,0.8)",animation:"pulseGlow 1.5s infinite",boxShadow:"0 0 12px rgba(245, 34, 45, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"8px",color:"white",fontWeight:"bold"}},"!"),t.createElement("div",{style:{position:"relative",zIndex:2,display:"flex",flexDirection:"column",alignItems:"center",gap:"2px"}},t.createElement("div",{style:{fontSize:"13px",lineHeight:1.2,fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.8)"}},q(b)),t.createElement("div",{style:{fontSize:"8px",opacity:.9,fontWeight:"600",textShadow:"0 1px 2px rgba(0,0,0,0.8)"}},"min")),t.createElement("div",{style:{position:"absolute",bottom:"2px",left:"2px",width:"4px",height:"4px",borderRadius:"50%",background:"rgba(255,255,255,0.6)",boxShadow:"0 0 4px rgba(255,255,255,0.8)"}}))),t.createElement("style",{jsx:!0},`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          @keyframes sparkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
          }
        `))},R=i=>{const x=i.startOf("month"),b=i.endOf("month").diff(x,"day")+1;let S=0,h=0,k=0;for(let T=0;T<b;T++){const J=x.add(T,"day").format("YYYY-MM-DD"),I=E.get(J);I&&I.mttr!==void 0&&I.mttr!==null&&(S+=I.mttr,k+=I.stops||0,h++)}const M=h>0?S/h:null,D=d(M),y=l(M),K=Ee(M,n);return t.createElement("div",{style:{position:"relative",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",padding:"8px"}},M!==null&&t.createElement(ee,{title:t.createElement("div",{style:{textAlign:"center",background:"linear-gradient(135deg, #001529, #002766)",borderRadius:"12px",padding:"16px",border:"1px solid rgba(255,255,255,0.1)",maxWidth:"300px"}},t.createElement("div",{style:{fontWeight:"bold",marginBottom:"12px",fontSize:"16px",color:"#fff",textShadow:"0 1px 2px rgba(0,0,0,0.5)",borderBottom:"1px solid rgba(255,255,255,0.2)",paddingBottom:"8px"}},"📅 ",i.format("MMMM YYYY")),t.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",marginBottom:"12px"}},t.createElement("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",textAlign:"center"}},t.createElement("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#ffc53d"}},q(M)),t.createElement("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.8)"}},"MTTR Moyen (min)")),t.createElement("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",textAlign:"center"}},t.createElement("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#ff7875"}},k),t.createElement("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.8)"}},"Total Arrêts"))),t.createElement("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",marginBottom:"8px"}},t.createElement("div",{style:{fontSize:"13px",marginBottom:"4px"}},"🔧 Statut: ",t.createElement("span",{style:{fontWeight:"bold",color:D.includes("excellent")?m.SECONDARY_BLUE:D.includes("critical")?m.LIGHT_GRAY:m.SECONDARY_BLUE}},K)),t.createElement("div",{style:{fontSize:"12px",opacity:.9}},"📊 Jours avec données: ",t.createElement("span",{style:{fontWeight:"bold"}},h)))),placement:"top"},t.createElement("div",{style:{background:D,borderRadius:"16px",padding:"12px 20px",fontSize:"13px",fontWeight:"bold",color:"white",textShadow:"0 1px 3px rgba(0,0,0,0.5)",cursor:"pointer",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",display:"flex",flexDirection:"column",alignItems:"center",gap:"4px",position:"relative",overflow:"hidden",...y,minWidth:"80px"},onMouseEnter:T=>{T.target.style.transform="scale(1.08) translateY(-2px)",T.target.style.boxShadow=y.boxShadow.replace(/0.4/g,"0.7").replace(/0.2/g,"0.5")},onMouseLeave:T=>{T.target.style.transform="scale(1) translateY(0px)",T.target.style.boxShadow=y.boxShadow}},t.createElement("div",{style:{position:"absolute",top:"-50%",right:"-50%",width:"100%",height:"100%",background:"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",borderRadius:"50%",animation:"float 6s ease-in-out infinite"}}),t.createElement("div",{style:{display:"flex",alignItems:"center",gap:"6px",position:"relative",zIndex:2}},t.createElement(xe,{style:{fontSize:"12px"}}),t.createElement("span",{style:{fontSize:"14px",fontWeight:"800"}},q(M)),t.createElement("span",{style:{fontSize:"10px",opacity:.8}},"min")),t.createElement("div",{style:{fontSize:"9px",opacity:.9,fontWeight:"600",position:"relative",zIndex:2}},h," jours • ",k," arrêts"),M<=n.low*.5&&t.createElement(t.Fragment,null,t.createElement("div",{style:{position:"absolute",top:"10%",left:"20%",width:"3px",height:"3px",borderRadius:"50%",background:"rgba(255,255,255,0.8)",animation:"sparkle 2s infinite 0.5s"}}),t.createElement("div",{style:{position:"absolute",top:"70%",right:"15%",width:"2px",height:"2px",borderRadius:"50%",background:"rgba(255,255,255,0.6)",animation:"sparkle 2s infinite 1.2s"}})))),t.createElement("style",{jsx:!0},`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
          }
        `))},w=({value:i,type:x,onChange:f,onTypeChange:b})=>{const S=r?`MTTR pour ${r}`:"Calendrier MTTR";return t.createElement("div",{style:{background:`linear-gradient(135deg, ${m.PRIMARY_BLUE} 0%, ${m.DARK_GRAY} 100%)`,borderRadius:"12px",padding:"12px",marginBottom:"8px",border:"none",boxShadow:"0 4px 16px rgba(30, 58, 138, 0.15), 0 0 15px rgba(31, 41, 55, 0.08)",position:"relative",overflow:"hidden"}},t.createElement("div",{style:{position:"absolute",top:"-20%",right:"-10%",width:"120px",height:"120px",background:"radial-gradient(circle, rgba(255,255,255,0.06) 0%, transparent 70%)",borderRadius:"50%",animation:"rotate 20s linear infinite"}}),t.createElement("div",{style:{position:"absolute",bottom:"-15%",left:"-5%",width:"100px",height:"100px",background:"radial-gradient(circle, rgba(255,255,255,0.04) 0%, transparent 70%)",borderRadius:"50%",animation:"rotate 15s linear infinite reverse"}}),"        ",t.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px",position:"relative",zIndex:2}},t.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},t.createElement("div",{style:{width:"32px",height:"32px",borderRadius:"8px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.3)",boxShadow:"0 2px 12px rgba(0,0,0,0.1)"}},t.createElement(xe,{style:{color:"#fff",fontSize:"16px"}})),t.createElement("div",null,t.createElement(j,{strong:!0,style:{fontSize:"16px",color:"#fff",textShadow:"0 1px 3px rgba(0,0,0,0.3)",fontWeight:"700"}},S),t.createElement("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.9)",marginTop:"1px",fontWeight:"500"}},"Surveillance temps réel"))),t.createElement(ee,{title:"Le MTTR (Mean Time To Repair) représente le temps moyen nécessaire pour réparer une panne. Un MTTR faible indique une maintenance efficace."},t.createElement("div",{style:{width:"28px",height:"28px",borderRadius:"8px",background:"rgba(255,255,255,0.15)",display:"flex",alignItems:"center",justifyContent:"center",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.2)",cursor:"help",transition:"all 0.3s ease"}},t.createElement(st,{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}}))),"        "),t.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",alignItems:"center",justifyContent:"center",padding:"12px",background:"rgba(255,255,255,0.1)",borderRadius:"12px",border:"1px solid rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",position:"relative",zIndex:2}},[{key:"excellent",label:`Excellent (≤ ${(n.low*.5).toFixed(0)}min)`,color:Y.excellent},{key:"low",label:`Bon (≤ ${n.low}min)`,color:Y.low},{key:"medium",label:`Moyen (≤ ${n.medium}min)`,color:Y.medium},{key:"high",label:`Mauvais (≤ ${(n.medium*1.5).toFixed(0)}min)`,color:Y.high},{key:"critical",label:`Critique (> ${(n.medium*1.5).toFixed(0)}min)`,color:Y.critical}].map((h,k)=>t.createElement("div",{key:h.key,style:{display:"flex",alignItems:"center",gap:"6px",background:"rgba(255,255,255,0.08)",borderRadius:"8px",padding:"6px 10px",border:"1px solid rgba(255,255,255,0.15)",animation:`fadeInUp 0.6s ease-out ${k*.1}s both`}},t.createElement("div",{style:{width:"14px",height:"14px",background:h.color.includes("gradient")?h.color:`linear-gradient(135deg, ${h.color}, ${h.color}dd)`,borderRadius:"4px",boxShadow:"0 1px 4px rgba(0,0,0,0.2)",border:"1px solid rgba(255,255,255,0.2)"}}),t.createElement(j,{style:{fontSize:"11px",fontWeight:"600",color:"#fff",textShadow:"0 1px 2px rgba(0,0,0,0.3)"}},h.label)))),t.createElement("style",{jsx:!0},`
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `))};return p?t.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"300px",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",borderRadius:"12px",border:"1px solid #e6f7ff"}},t.createElement(Je,{size:"large"}),t.createElement(j,{style:{marginTop:"16px",color:"#8c8c8c"}},"Chargement du calendrier MTTR...")):!e||e.length===0?t.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"300px",background:"linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)",borderRadius:"12px",border:"1px solid #e8e8e8"}},t.createElement(F,{image:F.PRESENTED_IMAGE_SIMPLE,description:t.createElement("div",{style:{textAlign:"center"}},t.createElement(j,{style:{fontSize:"16px",color:"#8c8c8c"}},"Aucune donnée MTTR disponible"),t.createElement("br",null),t.createElement(j,{type:"secondary",style:{fontSize:"14px"}},"Sélectionnez une machine et une période pour afficher les données"))})):t.createElement("div",{style:{height:"100%",width:"100%",minHeight:"500px",background:"linear-gradient(135deg, #f8f9fa 0%, #fff 25%, #f0f2f5 50%, #fff 75%, #fafafa 100%)",borderRadius:"16px",padding:"20px",border:"1px solid rgba(230,230,230,0.8)",boxShadow:"0 4px 20px rgba(0,0,0,0.06), 0 1px 4px rgba(0,0,0,0.02)",position:"relative",overflow:"visible",display:"flex",flexDirection:"column"}},t.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:`
          radial-gradient(circle at 20% 20%, rgba(103, 126, 234, 0.02) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.02) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.01) 0%, transparent 50%)
        `,animation:"floatUp 8s ease-in-out infinite",zIndex:0,borderRadius:"16px"}}),t.createElement("div",{style:{position:"relative",zIndex:1,flex:1,overflow:"visible"}},"  ",t.createElement(Ye,{value:g,mode:v,fullscreen:!1,dateCellRender:c,monthCellRender:R,headerRender:w,style:{backgroundColor:"transparent",borderRadius:"12px",overflow:"visible",height:"100%"},className:"custom-mttr-calendar"}),t.createElement("style",{jsx:!0,global:!0},`
          .custom-mttr-calendar .ant-picker-calendar {
            background: transparent !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date-content {
            height: 60px !important;  /* Increased cell height */
            min-height: 60px !important;
          }
          
          .custom-mttr-calendar .ant-picker-cell {
            padding: 4px !important;  /* Add padding to cells */
            position: relative !important;
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-cell-inner {
            min-height: 60px !important;  /* Ensure minimum cell height */
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: visible !important;
          }
          
          /* Ensure tooltips stay within bounds */
          .custom-mttr-calendar .ant-tooltip {
            z-index: 1060 !important;
          }
          
          .custom-mttr-calendar .ant-tooltip-inner {
            max-width: 280px !important;
            word-wrap: break-word !important;
            border-radius: 8px !important;
          }
          
          /* Make calendar container allow overflow for tooltips */
          .custom-mttr-calendar {
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-header,
          .custom-mttr-calendar .ant-picker-calendar-body {
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date {
            border: 1px solid rgba(0,0,0,0.06) !important;
            border-radius: 8px !important;
            margin: 2px !important;
            min-height: 56px !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date:hover {
            border-color: #40a9ff !important;
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date-value {
            font-size: 14px !important;
            font-weight: 600 !important;
            color: #262626 !important;
            position: absolute !important;
            top: 4px !important;
            right: 6px !important;
            z-index: 3 !important;
            background: rgba(255,255,255,0.9) !important;
            border-radius: 4px !important;
            padding: 2px 6px !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-header {
            margin-bottom: 16px !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-mode-switch {
            background: linear-gradient(135deg, ${m.PRIMARY_BLUE}, ${m.SECONDARY_BLUE}) !important;
            border: none !important;
            color: white !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            padding: 6px 16px !important;
            box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-mode-switch:hover {
            background: linear-gradient(135deg, ${m.SECONDARY_BLUE}, ${m.PRIMARY_BLUE}) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4) !important;
          }
          
          .custom-mttr-calendar .ant-select-selector {
            border: 2px solid #d9d9d9 !important;
            border-radius: 8px !important;
            background: white !important;
            min-height: 40px !important;
            font-weight: 600 !important;
          }
          
          .custom-mttr-calendar .ant-select-focused .ant-select-selector {
            border-color: ${m.SECONDARY_BLUE} !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
          }
          
          /* Enhanced responsiveness */
          @media (max-width: 768px) {
            .custom-mttr-calendar .ant-picker-calendar-date-content {
              height: 45px !important;
              min-height: 45px !important;
            }
            
            .custom-mttr-calendar .ant-picker-cell-inner {
              min-height: 45px !important;
            }
            
            .custom-mttr-calendar .ant-picker-calendar-date {
              min-height: 41px !important;
            }
          }
        `)))},Nt=t.memo(wt),{Text:P,Title:zt}=V,z={primary:m.PRIMARY_BLUE,success:m.PRIMARY_BLUE,warning:m.SECONDARY_BLUE,danger:m.CHART_TERTIARY},L=e=>{if(e==null)return 0;let r=Number(e);return!isNaN(r)&&r>0&&r<1&&(r=r*100),r},St=({data:e=null,selectedMachine:r="",loading:o=!1,thresholds:a={disponibilite:{low:70,medium:85},mttr:{low:45,medium:20},mtbf:{low:120,medium:240}}})=>{const p=(n,E)=>{const v=a[E];return v?E==="mttr"?n>v.low?z.danger:n>v.medium?z.warning:z.success:n<v.low?z.danger:n<v.medium?z.warning:z.success:z.primary};return e?t.createElement(Qe,{gutter:[16,16]},t.createElement(te,{xs:24,md:8},t.createElement(re,{bordered:!1,style:{height:"100%"}},t.createElement(ae,{title:t.createElement(ne,null,t.createElement("span",null,"Disponibilité"),r&&t.createElement(P,{type:"secondary"},"(",r,")")),value:L(e.disponibilite),precision:1,suffix:"%",valueStyle:{color:p(L(e.disponibilite),"disponibilite")}}),t.createElement(ie,{type:"dashboard",percent:L(e.disponibilite),strokeColor:p(L(e.disponibilite),"disponibilite"),format:n=>`${n==null?void 0:n.toFixed(1)}%`,width:120}),t.createElement("div",{style:{marginTop:16}},t.createElement(P,{type:"secondary"},L(e.disponibilite)>=a.disponibilite.medium?"Excellente disponibilité opérationnelle":L(e.disponibilite)>=a.disponibilite.low?"Disponibilité acceptable, des améliorations possibles":"Disponibilité insuffisante, action requise")))),t.createElement(te,{xs:24,md:8},t.createElement(re,{bordered:!1,style:{height:"100%"}},t.createElement(ae,{title:t.createElement(ne,null,t.createElement("span",null,"MTTR"),r&&t.createElement(P,{type:"secondary"},"(",r,")")),value:e.mttr,precision:1,suffix:"min",valueStyle:{color:p(e.mttr,"mttr")}}),t.createElement(ie,{type:"dashboard",percent:Math.min(100,e.mttr/60*100),strokeColor:p(e.mttr,"mttr"),format:n=>`${e.mttr.toFixed(1)} min`,width:120}),t.createElement("div",{style:{marginTop:16}},t.createElement(P,{type:"secondary"},e.mttr<=a.mttr.medium?"Excellent temps de réparation":e.mttr<=a.mttr.low?"Temps de réparation acceptable":"Temps de réparation trop long, action requise")))),t.createElement(te,{xs:24,md:8},t.createElement(re,{bordered:!1,style:{height:"100%"}},t.createElement(ae,{title:t.createElement(ne,null,t.createElement("span",null,"MTBF"),r&&t.createElement(P,{type:"secondary"},"(",r,")")),value:e.mtbf,precision:1,suffix:"min",valueStyle:{color:p(e.mtbf,"mtbf")}}),t.createElement(ie,{type:"dashboard",percent:Math.min(100,Math.max(0,e.mtbf/1440*100)),strokeColor:p(e.mtbf,"mtbf"),format:n=>`${e.mtbf.toFixed(1)} min`,width:120}),t.createElement("div",{style:{marginTop:16}},t.createElement(P,{type:"secondary"},e.mtbf>=a.mtbf.medium?"Excellente fiabilité entre pannes":e.mtbf>=a.mtbf.low?"Fiabilité acceptable entre pannes":"Fiabilité insuffisante, action requise"))))):t.createElement(F,{description:"Aucune donnée disponible pour les indicateurs de performance"})},Ot=t.memo(St);export{kt as D,Nt as M,Ot as P,Bt as a};
