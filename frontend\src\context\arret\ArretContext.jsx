import React, { createContext, useContext, useState, useEffect, useRef } from 'react'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

// Import optimized GraphQL hook
import useStopTableGraphQL from '../../hooks/useStopTableGraphQL'
import { isAbortError } from '../../utils/error_handler'

// Import modular components
import { 
  CHART_COLORS, 
  INITIAL_SKELETON_STATE, 
  INITIAL_DATA_STATE 
} from './modules/constants.jsx'
import { useSkeletonManager } from './modules/skeletonManager.jsx'
import { useDataManager } from './modules/dataManager.jsx'
import { useEventHandlers } from './modules/eventHandlers.jsx'
import { useComputedValues } from './modules/computedValues.jsx'

// Extend dayjs with required plugins
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)

/**
 * Modular ArretContext - Optimized and integrated with useStopTableGraphQL
 * 
 * This context has been refactored into focused modules:
 * - constants.js: Configuration and constants
 * - skeletonManager.js: Skeleton loading state management
 * - dataManager.js: Data fetching using optimized GraphQL hook
 * - eventHandlers.js: User interaction handlers
 * - computedValues.js: Derived data and calculations
 * - performanceCalculations.js: MTTR, MTBF, availability calculations
 * - dataProcessing.js: Data transformation utilities
 */

const ArretContext = createContext()

export const useArretContext = () => {
  const context = useContext(ArretContext)
  if (!context) {
    console.error('⚠️  useArretContext: Context not found!')
    return null
  }
  return context
}

export const ArretProvider = ({ children }) => {
  console.log('🚀 ArretProvider: Initializing modular context...');

  // Initialize the optimized GraphQL hook
  const graphQL = useStopTableGraphQL();
  
  // Core state management
  const [state, setState] = useState({
    ...INITIAL_DATA_STATE,
    // Additional UI states
    isChartModalVisible: false,
    chartModalContent: null
  });
  
  const [skeletonStates, setSkeletonStates] = useState(INITIAL_SKELETON_STATE);
  
  // Refs for component lifecycle and optimization
  const isMounted = useRef(true);
  
  // Initialize modular managers and handlers
  const skeletonManager = useSkeletonManager(skeletonStates, setSkeletonStates);
  const dataManager = useDataManager(graphQL, state, setState, skeletonManager);
  const eventHandlers = useEventHandlers(state, setState, dataManager, skeletonManager);
  const computedValues = useComputedValues(state);

  // Chart modal handlers
  const openChartModal = (content) => {
    setState(prev => ({
      ...prev,
      isChartModalVisible: true,
      chartModalContent: content
    }));
  };

  const closeChartModal = () => {
    setState(prev => ({
      ...prev,
      isChartModalVisible: false,
      chartModalContent: null
    }));
  };

  // Effects for component lifecycle and data management
  
  // Initial data load
  useEffect(() => {
    console.log('🔄 ArretProvider: Initial data load...');
    
    const initializeData = async () => {
      try {
        // Load machine models first
        await dataManager.fetchMachineModels();
        await dataManager.fetchMachineNames();
        
        // Initial data fetch with current filters - USE DIRECT FETCH, NOT DEBOUNCED
        await dataManager.fetchData(false);
        
        console.log('✅ ArretProvider: Initial data load completed');
      } catch (error) {
        // Check if this is an abort error (which can happen during navigation)
        if (isAbortError(error)) {
          console.log('ℹ️ ArretProvider: Initial data load aborted due to navigation.');
          
          // Set a special state for aborted requests
          setState(prev => ({ 
            ...prev,
            loading: false,
            essentialLoading: false,
            error: null // Don't show error for aborted requests
          }));
        } else {
          // This is a real error that should be displayed
          console.error('❌ ArretProvider: Initial data load failed:', error);
          
          // Set error state
          setState(prev => ({ 
            ...prev,
            loading: false,
            essentialLoading: false,
            error: error.message || 'Failed to load initial data'
          }));
        }
      }
    };

    initializeData();
  }, []); // Empty dependency array - only run once

  // Smart data fetching when filters change
  useEffect(() => {
    console.log('🔄 ArretProvider: Filters changed, triggering data fetch...');
    
    const cleanupFetch = dataManager.debouncedFetchData();
    
    return cleanupFetch;
  }, [
    state.selectedMachineModel,
    state.selectedMachine,
    state.selectedDate,
    state.dateRangeType
  ]);

  // Update filtered machine names when machine model changes
  useEffect(() => {
    console.log('🔄 Machine model changed, fetching appropriate machine names');
    
    if (state.selectedMachineModel) {
      // Use the selected model to fetch machine names for that model
      dataManager.fetchMachineNames(state.selectedMachineModel)
        .then(() => {
          console.log('✅ Fetched machine names for selected model:', state.selectedMachineModel);
        })
        .catch(error => {
          console.error('❌ Failed to fetch machine names for model:', error);
          
          // In case of error, try to use local filtering as fallback
          const filteredByClient = state.machineNames.filter(machine => 
            // Try different properties that might contain model info
            machine.model === state.selectedMachineModel ||
            machine.modele === state.selectedMachineModel ||
            (machine.Machine_Name && machine.Machine_Name.includes(state.selectedMachineModel))
          );
          
          setState(prev => ({ 
            ...prev, 
            filteredMachineNames: filteredByClient.length > 0 ? filteredByClient : []
          }));
        });
    } else {
      // When no model is selected, show all machines
      setState(prev => ({ 
        ...prev, 
        filteredMachineNames: state.machineNames 
      }));
    }
  }, [state.selectedMachineModel, state.machineNames, dataManager, setState]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      isMounted.current = false;
      console.log('🧹 ArretProvider: Component unmounted, cleanup completed');
    };
  }, []);

  // Build context value with all state, handlers, and computed values
  const contextValue = React.useMemo(() => ({
    // Core state
    ...state,
    
    // Skeleton states and management
    skeletonStates,
    ...skeletonManager,
    
    // Computed values
    ...computedValues,
    
    // Event handlers
    ...eventHandlers,
    
    // Chart modal handlers
    openChartModal,
    closeChartModal,
    setIsChartModalVisible: (visible) => setState(prev => ({ 
      ...prev, 
      isChartModalVisible: visible 
    })),
    setChartModalContent: (content) => setState(prev => ({ 
      ...prev, 
      chartModalContent: content 
    })),
    setChartOptions: (options) => setState(prev => ({ 
      ...prev, 
      chartOptions: options 
    })),
    
    // Data manager functions for direct access if needed
    dataManager,
    
    // Constants
    CHART_COLORS,
    
    // GraphQL hook access for advanced use cases
    graphQL,
    
    // Ensure error state is exposed to consumers
    error: state.error
    
  }), [
    // State dependencies
    state,
    skeletonStates,
    
    // Manager dependencies
    skeletonManager,
    computedValues,
    eventHandlers,
    dataManager,
    
    // GraphQL hook
    graphQL
  ]);

  console.log('🎯 ArretProvider: Context value prepared with', 
    Object.keys(contextValue).length, 'properties');

  // Debug logging for sidebarStats and computed values
  React.useEffect(() => {
    console.log('🔍 ArretProvider - ComputedValues debug:', {
      sidebarStatsLength: computedValues.sidebarStats?.length || 0,
      sidebarStatsType: typeof computedValues.sidebarStats,
      sidebarStatsPreview: computedValues.sidebarStats?.slice(0, 2),
      stopsDataLength: state.stopsData?.length || 0,
      contextValueKeys: Object.keys(contextValue)
    });
  }, [computedValues.sidebarStats, state.stopsData, contextValue]);

  return (
    <ArretContext.Provider value={contextValue}>
      {children}
    </ArretContext.Provider>
  );
};

export default ArretContext;
