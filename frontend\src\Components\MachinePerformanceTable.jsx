import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import {
  Table,
  Tag,
  Space,
  Typography,
  Progress,
  Tooltip,
  Button,
  Dropdown,
  Menu,
  ConfigProvider
} from "antd";
import {
  ToolOutlined,
  MoreOutlined,
  LineChartOutlined,
  SettingOutlined,
  DownloadOutlined
} from "@ant-design/icons";
import { VariableSizeGrid as Grid } from 'react-window';
import { normalizePercentage } from "../utils/dataUtils";

const { Text } = Typography;

// Palette de couleurs améliorée
const COLORS = [
  "#1890ff", // bleu
  "#13c2c2", // cyan
  "#52c41a", // vert
  "#faad14", // jaune
  "#f5222d", // rouge
  "#722ed1", // violet
  "#eb2f96", // rose
  "#fa8c16", // orange
  "#a0d911", // lime
  "#096dd9", // bleu foncé
];

// Virtual Table component for large datasets
const VirtualTable = (props) => {
  const { columns, scroll, data } = props;
  const gridRef = useRef();
  const [tableWidth, setTableWidth] = useState(0);
  const [connectObject] = useState(() => {
    const obj = {};
    Object.defineProperty(obj, 'scrollLeft', {
      get: () => {
        if (gridRef.current) {
          return gridRef.current.state.scrollLeft;
        }
        return null;
      },
      set: (scrollLeft) => {
        if (gridRef.current) {
          gridRef.current.scrollTo({ scrollLeft });
        }
      },
    });
    return obj;
  });

  // Calculate column widths
  const mergedColumns = columns.map((column) => {
    if (column.width) {
      return column;
    }
    return {
      ...column,
      width: 150, // Default width
    };
  });

  // Calculate total width
  const totalWidth = mergedColumns.reduce((total, { width }) => total + width, 0);

  // Reset grid when data changes
  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.resetAfterIndices({
        columnIndex: 0,
        rowIndex: 0,
        shouldForceUpdate: true,
      });
    }
  }, [data]);

  // Render cell content
  const renderCell = ({ columnIndex, rowIndex, style }) => {
    const column = mergedColumns[columnIndex];
    const record = data[rowIndex];

    // Handle fixed columns
    const fixedStyle = column.fixed ? { position: 'sticky', zIndex: 1, left: 0, overflow: 'hidden' } : {};

    // Render cell content based on column render function or dataIndex
    let cellContent;
    if (column.render) {
      cellContent = column.render(record[column.dataIndex], record, rowIndex);
    } else {
      cellContent = record[column.dataIndex];
    }

    return (
      <div
        className="virtual-table-cell"
        style={{
          ...style,
          ...fixedStyle,
          padding: '8px 12px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          boxSizing: 'border-box',
        }}
      >
        {cellContent}
      </div>
    );
  };

  // Render header cell
  const renderHeaderCell = ({ columnIndex, style }) => {
    const column = mergedColumns[columnIndex];

    // Handle fixed columns
    const fixedStyle = column.fixed ? { position: 'sticky', zIndex: 2, left: 0, overflow: 'hidden' } : {};

    return (
      <div
        className="virtual-table-header-cell"
        style={{
          ...style,
          ...fixedStyle,
          padding: '16px 12px',
          background: '#fafafa',
          fontWeight: 'bold',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          boxSizing: 'border-box',
        }}
      >
        {column.title}
      </div>
    );
  };

  return (
    <div style={{ width: '100%', height: scroll.y }}>
      {/* Header row */}
      <Grid
        className="virtual-table-header"
        columnCount={mergedColumns.length}
        columnWidth={(index) => mergedColumns[index].width}
        height={50}
        rowCount={1}
        rowHeight={() => 50}
        width={tableWidth}
        style={{ overflow: 'hidden' }}
      >
        {renderHeaderCell}
      </Grid>

      {/* Data grid */}
      <Grid
        ref={gridRef}
        className="virtual-table-body"
        columnCount={mergedColumns.length}
        columnWidth={(index) => mergedColumns[index].width}
        height={scroll.y - 50}
        rowCount={data.length}
        rowHeight={() => 54}
        width={tableWidth}
        onScroll={({ scrollLeft }) => {
          if (connectObject.scrollLeft !== scrollLeft) {
            connectObject.scrollLeft = scrollLeft;
          }
        }}
      >
        {renderCell}
      </Grid>
    </div>
  );
};

/**
 * MachinePerformanceTable component
 * @param {Object} props - Component props
 * @param {Array} props.data - Table data
 * @param {boolean} props.loading - Whether data is loading
 * @param {boolean} props.useVirtualization - Whether to use virtualization for large datasets
 * @returns {JSX.Element} - Rendered component
 */
const MachinePerformanceTable = ({ data, loading, useVirtualization = true }) => {
  // Use virtualization only for large datasets
  const shouldUseVirtualization = useVirtualization && data && data.length > 100;
  // Columns for the table of performance of machines
  const columns = [
    {
      title: "Machine",
      dataIndex: "Machine_Name",
      key: "machine",
      fixed: "left",
      render: (text) => (
        <Space>
          <ToolOutlined style={{ color: COLORS[0] }} />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: "Moule",
      dataIndex: "mould_number",
      key: "mould",
      render: (text) => <Tag color="cyan">{text || "N/A"}</Tag>,
    },
    {
      title: "Équipe",
      dataIndex: "Shift",
      key: "shift",
      render: (text) => <Tag color="blue">{text || "N/A"}</Tag>,
    },
    {
      title: "Production",
      dataIndex: "good",
      key: "good",
      render: (text) => <Tag color="green">{(text || 0).toLocaleString()} pcs</Tag>,
      sorter: (a, b) => (a.good || 0) - (b.good || 0),
    },
    {
      title: "Rejets",
      dataIndex: "reject",
      key: "reject",
      render: (text) => <Tag color="red">{(text || 0).toLocaleString()} kg</Tag>,
      sorter: (a, b) => (a.reject || 0) - (b.reject || 0),
    },
    {
      title: "Heures Prod.",
      dataIndex: "run_hours",
      key: "run_hours",
      render: (text) => <Tag color="green">{(text || 0).toFixed(2)} h</Tag>,
      sorter: (a, b) => (a.run_hours || 0) - (b.run_hours || 0),
    },
    {
      title: "Heures Arrêt",
      dataIndex: "down_hours",
      key: "down_hours",
      render: (text) => <Tag color="orange">{(text || 0).toFixed(2)} h</Tag>,
      sorter: (a, b) => (a.down_hours || 0) - (b.down_hours || 0),
    },
    {
      title: "Cycle Théorique",
      dataIndex: "cycle_theorique",
      key: "cycle_theorique",
      render: (text) => <Tag color="purple">{text || "N/A"}</Tag>,
    },
    {
      title: "Disponibilité",
      dataIndex: "availability",
      key: "availability",
      render: (value) => {
        // Normalize percentage value
        const numValue = normalizePercentage(value);

        return (
          <Tooltip title={`${numValue.toFixed(1)}% de disponibilité`}>
            <Progress
              percent={numValue}
              size="small"
              status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
              format={(percent) => {
                // Check if percent is a number before using toFixed
                return typeof percent === "number" && !isNaN(percent)
                  ? `${percent.toFixed(1)}%`
                  : "0.0%"
              }}
            />
          </Tooltip>
        );
      },
      sorter: (a, b) => {
        // Get values and normalize them
        const availA = normalizePercentage(a.availability);
        const availB = normalizePercentage(b.availability);
        return availA - availB;
      },
    },
    {
      title: "Performance",
      dataIndex: "performance",
      key: "performance",
      render: (value) => {
        // Normalize percentage value
        const numValue = normalizePercentage(value);

        return (
          <Tooltip title={`${numValue.toFixed(1)}% de performance`}>
            <Progress
              percent={numValue}
              size="small"
              status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
              format={(percent) => {
                // Check if percent is a number before using toFixed
                return typeof percent === "number" && !isNaN(percent)
                  ? `${percent.toFixed(1)}%`
                  : "0.0%"
              }}
            />
          </Tooltip>
        );
      },
      sorter: (a, b) => {
        // Get values and normalize them
        const perfA = normalizePercentage(a.performance);
        const perfB = normalizePercentage(b.performance);
        return perfA - perfB;
      },
    },
    {
      title: "TRS",
      dataIndex: "oee",
      key: "oee",
      render: (value) => {
        // Normalize percentage value
        const numValue = normalizePercentage(value);

        return (
          <Tooltip title={`${numValue.toFixed(1)}% de TRS`}>
            <Progress
              percent={numValue}
              size="small"
              status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
              format={(percent) => {
                // Check if percent is a number before using toFixed
                return typeof percent === "number" && !isNaN(percent)
                  ? `${percent.toFixed(1)}%`
                  : "0.0%"
              }}
            />
          </Tooltip>
        );
      },
      sorter: (a, b) => {
        // Get values and normalize them
        const oeeA = normalizePercentage(a.oee);
        const oeeB = normalizePercentage(b.oee);
        return oeeA - oeeB;
      },
      defaultSortOrder: "descend",
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Dropdown
          overlay={
            <Menu>
              <Menu.Item key="1" icon={<LineChartOutlined />}>
                Voir tendances
              </Menu.Item>
              <Menu.Item key="2" icon={<SettingOutlined />}>
                Paramètres
              </Menu.Item>
              <Menu.Item key="3" icon={<DownloadOutlined />}>
                Exporter données
              </Menu.Item>
            </Menu>
          }
          trigger={["click"]}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // Set up a ref to measure the container width
  const containerRef = useRef(null);

  // Update table width when container size changes
  useEffect(() => {
    if (containerRef.current && shouldUseVirtualization) {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          if (entry.target === containerRef.current) {
            setTableWidth(entry.contentRect.width);
          }
        }
      });

      resizeObserver.observe(containerRef.current);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [shouldUseVirtualization]);

  // Set initial table width
  const [tableWidth, setTableWidth] = useState(0);

  // Use effect to set initial width after component mounts
  useEffect(() => {
    if (containerRef.current && shouldUseVirtualization) {
      setTableWidth(containerRef.current.offsetWidth);
    }
  }, [shouldUseVirtualization]);

  return (
    <div ref={containerRef} style={{ width: '100%', height: '100%' }}>
      {shouldUseVirtualization ? (
        <ConfigProvider renderEmpty={() => <div style={{ padding: 20, textAlign: 'center' }}>Aucune donnée</div>}>
          <VirtualTable
            columns={columns}
            data={data || []}
            scroll={{ y: 500, x: '100%' }}
          />
        </ConfigProvider>
      ) : (
        <Table
          columns={columns}
          dataSource={data}
          rowKey={(record) => `${record.Machine_Name}-${record.date}-${record.Shift}`}
          loading={loading}
          scroll={{ x: 1300 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total) => `Total: ${total} enregistrements`,
          }}
        />
      )}
    </div>
  );
};

// PropTypes for VirtualTable
VirtualTable.propTypes = {
  columns: PropTypes.array.isRequired,
  scroll: PropTypes.object.isRequired,
  data: PropTypes.array.isRequired
};

// PropTypes for MachinePerformanceTable
MachinePerformanceTable.propTypes = {
  data: PropTypes.array,
  loading: PropTypes.bool,
  useVirtualization: PropTypes.bool
};

// Default props
MachinePerformanceTable.defaultProps = {
  data: [],
  loading: false,
  useVirtualization: true
};

export default MachinePerformanceTable;
