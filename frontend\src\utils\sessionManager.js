/**
 * Session management utility for handling session timeouts and re-authentication
 */

// Default session timeout in milliseconds (30 minutes)
const DEFAULT_TIMEOUT = 30 * 60 * 1000;

class SessionManager {
  constructor() {
    this.timeoutId = null;
    this.lastActivity = Date.now();
    this.timeoutDuration = DEFAULT_TIMEOUT;
    this.logoutCallback = null;
    this.warningCallback = null;
    this.warningThreshold = 0.9; // Show warning when 90% of timeout has elapsed
  }

  /**
   * Initialize the session manager
   * @param {Object} options - Configuration options
   * @param {number} [options.timeoutDuration] - Session timeout in milliseconds
   * @param {Function} options.logoutCallback - Function to call when session times out
   * @param {Function} [options.warningCallback] - Function to call when session is about to timeout
   * @param {number} [options.warningThreshold] - Percentage of timeout duration when warning should be shown (0-1)
   */
  init({ timeoutDuration, logoutCallback, warningCallback, warningThreshold }) {
    this.timeoutDuration = timeoutDuration || DEFAULT_TIMEOUT;
    this.logoutCallback = logoutCallback;
    this.warningCallback = warningCallback;
    
    if (warningThreshold !== undefined && warningThreshold >= 0 && warningThreshold <= 1) {
      this.warningThreshold = warningThreshold;
    }

    // Start tracking user activity
    this.startActivityTracking();
    
    // Start the initial timeout
    this.resetTimeout();
  }

  /**
   * Start tracking user activity
   */
  startActivityTracking() {
    // List of events to track for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    // Add event listeners for each activity event
    events.forEach(event => {
      document.addEventListener(event, this.handleUserActivity.bind(this), false);
    });
  }

  /**
   * Handle user activity event
   */
  handleUserActivity() {
    this.lastActivity = Date.now();
    this.resetTimeout();
  }

  /**
   * Reset the session timeout
   */
  resetTimeout() {
    // Clear existing timeout
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      clearTimeout(this.warningTimeoutId);
    }
    
    // Set warning timeout if callback provided
    if (this.warningCallback) {
      const warningTime = this.timeoutDuration * this.warningThreshold;
      this.warningTimeoutId = setTimeout(() => {
        this.warningCallback();
      }, warningTime);
    }
    
    // Set new timeout
    this.timeoutId = setTimeout(() => {
      if (this.logoutCallback) {
        this.logoutCallback();
      }
    }, this.timeoutDuration);
  }

  /**
   * Manually extend the session
   */
  extendSession() {
    this.lastActivity = Date.now();
    this.resetTimeout();
  }

  /**
   * Clean up event listeners and timeouts
   */
  cleanup() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.removeEventListener(event, this.handleUserActivity.bind(this));
    });
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
    
    if (this.warningTimeoutId) {
      clearTimeout(this.warningTimeoutId);
    }
  }

  /**
   * Get remaining session time in milliseconds
   * @returns {number} Remaining time in milliseconds
   */
  getRemainingTime() {
    const elapsedTime = Date.now() - this.lastActivity;
    return Math.max(0, this.timeoutDuration - elapsedTime);
  }
}

// Create singleton instance
const sessionManager = new SessionManager();

export default sessionManager;
