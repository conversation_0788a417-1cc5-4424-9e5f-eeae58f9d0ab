import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Slider, Switch, Alert, Stati<PERSON>, Row, Col } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import ExpandableChart from './ExpandableChart';
import { EnhancedQuantityBarChart, EnhancedTRSLineChart } from './EnhancedChartComponents';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

/**
 * Test component to verify chart performance improvements
 */
const ChartPerformanceTest = () => {
  const [dataSize, setDataSize] = useState(100);
  const [isGenerating, setIsGenerating] = useState(false);
  const [testData, setTestData] = useState([]);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    generationTime: 0,
    renderTime: 0,
    memoryUsage: 0,
  });
  const [enableOptimizations, setEnableOptimizations] = useState(true);

  // Generate test data with varying complexity
  const generateTestData = async (size) => {
    setIsGenerating(true);
    const startTime = performance.now();

    const data = [];
    const startDate = dayjs().subtract(size, 'days');

    for (let i = 0; i < size; i++) {
      const date = startDate.add(i, 'day').format('YYYY-MM-DD');
      data.push({
        date,
        good: Math.floor(Math.random() * 1000) + 500,
        reject: Math.floor(Math.random() * 100) + 10,
        oee: Math.random() * 100,
        speed: Math.random() * 60 + 20,
        Machine_Name: `Machine_${Math.floor(i / 10) + 1}`,
        Shift: i % 3 === 0 ? 'Morning' : i % 3 === 1 ? 'Afternoon' : 'Night',
      });
    }

    const generationTime = performance.now() - startTime;
    
    setTestData(data);
    setPerformanceMetrics(prev => ({
      ...prev,
      generationTime: Math.round(generationTime),
    }));
    setIsGenerating(false);
  };

  // Measure render performance
  useEffect(() => {
    if (testData.length > 0) {
      const startTime = performance.now();
      
      // Use requestAnimationFrame to measure after render
      requestAnimationFrame(() => {
        const renderTime = performance.now() - startTime;
        setPerformanceMetrics(prev => ({
          ...prev,
          renderTime: Math.round(renderTime),
        }));
      });
    }
  }, [testData]);

  // Monitor memory usage (approximate)
  useEffect(() => {
    if (performance.memory) {
      setPerformanceMetrics(prev => ({
        ...prev,
        memoryUsage: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      }));
    }
  }, [testData]);

  const handleDataSizeChange = (value) => {
    setDataSize(value);
  };

  const handleGenerateData = () => {
    generateTestData(dataSize);
  };

  const getPerformanceColor = (metric, value) => {
    switch (metric) {
      case 'generation':
        return value < 100 ? 'green' : value < 500 ? 'orange' : 'red';
      case 'render':
        return value < 50 ? 'green' : value < 200 ? 'orange' : 'red';
      case 'memory':
        return value < 50 ? 'green' : value < 100 ? 'orange' : 'red';
      default:
        return 'blue';
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>Chart Performance Test</Title>
      <Text type="secondary">
        Test the performance improvements for chart expansion with large datasets
      </Text>

      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        {/* Controls */}
        <Col span={24}>
          <Card title="Test Controls">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Data Size: {dataSize} points</Text>
                <Slider
                  min={10}
                  max={1000}
                  step={10}
                  value={dataSize}
                  onChange={handleDataSizeChange}
                  marks={{
                    10: '10',
                    100: '100',
                    500: '500',
                    1000: '1000',
                  }}
                />
              </div>
              
              <Space>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleGenerateData}
                  loading={isGenerating}
                >
                  Generate Test Data
                </Button>
                
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => setTestData([])}
                >
                  Clear Data
                </Button>
              </Space>

              <div>
                <Text strong>Enable Optimizations: </Text>
                <Switch
                  checked={enableOptimizations}
                  onChange={setEnableOptimizations}
                  checkedChildren="ON"
                  unCheckedChildren="OFF"
                />
              </div>
            </Space>
          </Card>
        </Col>

        {/* Performance Metrics */}
        <Col span={24}>
          <Card title="Performance Metrics">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="Data Generation"
                  value={performanceMetrics.generationTime}
                  suffix="ms"
                  valueStyle={{ 
                    color: getPerformanceColor('generation', performanceMetrics.generationTime) 
                  }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Render Time"
                  value={performanceMetrics.renderTime}
                  suffix="ms"
                  valueStyle={{ 
                    color: getPerformanceColor('render', performanceMetrics.renderTime) 
                  }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Memory Usage"
                  value={performanceMetrics.memoryUsage}
                  suffix="MB"
                  valueStyle={{ 
                    color: getPerformanceColor('memory', performanceMetrics.memoryUsage) 
                  }}
                />
              </Col>
            </Row>
            
            {testData.length > 500 && (
              <Alert
                message="Large Dataset Detected"
                description="Performance optimizations are automatically applied for datasets over 500 points."
                type="info"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </Card>
        </Col>

        {/* Test Charts */}
        {testData.length > 0 && (
          <>
            <Col span={12}>
              <ExpandableChart
                title={`Quantity Chart (${testData.length} points)`}
                data={enableOptimizations ? testData : testData}
                chartType="bar"
                expandMode="modal"
                exportEnabled={true}
                zoomEnabled={true}
              >
                <EnhancedQuantityBarChart
                  data={testData}
                  title="Test Quantity Data"
                  dataKey="good"
                  color="#1890ff"
                  tooltipLabel="Quantité bonne"
                />
              </ExpandableChart>
            </Col>

            <Col span={12}>
              <ExpandableChart
                title={`TRS Chart (${testData.length} points)`}
                data={enableOptimizations ? testData : testData}
                chartType="line"
                expandMode="modal"
                exportEnabled={true}
                zoomEnabled={true}
              >
                <EnhancedTRSLineChart
                  data={testData}
                  color="#52c41a"
                />
              </ExpandableChart>
            </Col>
          </>
        )}

        {/* Instructions */}
        <Col span={24}>
          <Card title="Test Instructions">
            <Space direction="vertical">
              <Text>
                <strong>1.</strong> Adjust the data size slider to test with different dataset sizes
              </Text>
              <Text>
                <strong>2.</strong> Click "Generate Test Data" to create sample data
              </Text>
              <Text>
                <strong>3.</strong> Click the expand button on charts to test modal performance
              </Text>
              <Text>
                <strong>4.</strong> Monitor the performance metrics above
              </Text>
              <Text>
                <strong>5.</strong> Toggle optimizations to see the difference
              </Text>
              <Text type="secondary">
                <strong>Expected Results:</strong> With optimizations enabled, large datasets should render smoothly 
                with proper date label formatting and no sidebar interference in modal mode.
              </Text>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ChartPerformanceTest;
