// PDF functionality temporarily disabled for Docker compatibility
// import puppeteer from 'puppeteer';
import path from 'path';
import fs from 'fs/promises';

/**
 * Modern PDF Generation Service using React + Tailwind + Puppeteer
 * 
 * Advantages over PDFDocument:
 * - HTML/CSS layout is more maintainable than programmatic PDF construction
 * - Perfect chart rendering using existing Chart.js components
 * - Consistent styling with web interface using Tailwind CSS
 * - Better typography and responsive design capabilities
 * - Easier debugging and iteration on PDF layout
 */
class PDFGenerationService {
  constructor() {
    this.browser = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Puppeteer browser instance
   * Reuse browser instance for better performance
   */
  async initialize() {
    // PDF functionality temporarily disabled for Docker compatibility
    console.log('⚠️ PDF Generation Service: Puppeteer functionality disabled');
    this.isInitialized = false;
    throw new Error('PDF generation temporarily disabled for Docker compatibility');
  }

  /**
   * Generate PDF from report data using React template
   * @param {Object} reportData - Complete report data structure
   * @param {Object} options - PDF generation options
   * @returns {Buffer} PDF buffer
   */
  async generateShiftReportPDF(reportData, options = {}) {
    // PDF functionality temporarily disabled for Docker compatibility
    console.log('⚠️ PDF generation requested but Puppeteer is disabled');
    throw new Error('PDF generation temporarily disabled for Docker compatibility');
  }

  /**
   * Cleanup browser instance
   */
  async cleanup() {
    // PDF functionality temporarily disabled for Docker compatibility
    console.log('🧹 PDF service cleanup (no-op - Puppeteer disabled)');
  }

  /**
   * Health check for PDF service
   */
  async healthCheck() {
    return {
      status: 'disabled',
      message: 'PDF generation temporarily disabled for Docker compatibility',
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
const pdfGenerationService = new PDFGenerationService();

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  await pdfGenerationService.cleanup();
});

process.on('SIGINT', async () => {
  await pdfGenerationService.cleanup();
});

export default pdfGenerationService;
