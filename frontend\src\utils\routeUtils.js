/**
 * Utilities for route handling
 * @module routeUtils
 */

import { STORAGE_KEYS } from './storageUtils';

/**
 * Route definitions
 * @type {Object}
 */
export const ROUTES = {
  // Public routes
  LOGIN: '/login',
  RESET_PASSWORD: '/reset-password',
  UNAUTHORIZED: '/unauthorized',
  
  // Protected routes
  HOME: '/home',
  DASHBOARD: '/home',
  PRODUCTION: '/production',
  PRODUCTION_OLD: '/production-old',
  ARRETS: '/arrets',
  PROFILE: '/profile',
  ADMIN: '/admin',
  ADMIN_USERS: '/admin/users',
  NOTIFICATIONS: '/notifications',

  REPORTS: '/reports',
  
  // Legacy routes
  OLD_DASHBOARD: '/old',
  TEST: '/Test',
};

/**
 * Route access levels
 * @type {Object}
 */
export const ACCESS_LEVELS = {
  PUBLIC: 'public',
  PROTECTED: 'protected',
  ADMIN: 'admin',
};

/**
 * Route definitions with metadata
 * @type {Array}
 */
export const ROUTE_DEFINITIONS = [
  // Public routes
  { path: ROUTES.LOGIN, access: ACCESS_LEVELS.PUBLIC, title: 'Connexion' },
  { path: ROUTES.RESET_PASSWORD + '/:token', access: ACCESS_LEVELS.PUBLIC, title: 'Réinitialisation du mot de passe' },
  { path: ROUTES.UNAUTHORIZED, access: ACCESS_LEVELS.PUBLIC, title: 'Non autorisé' },
  
  // Protected routes
  { path: ROUTES.HOME, access: ACCESS_LEVELS.PROTECTED, title: 'Tableau de bord', icon: 'dashboard' },
  { path: ROUTES.PRODUCTION, access: ACCESS_LEVELS.PROTECTED, title: 'Production', icon: 'bar-chart' },
  { path: ROUTES.PRODUCTION_OLD, access: ACCESS_LEVELS.PROTECTED, title: 'Production (Ancien)', icon: 'bar-chart' },
  { path: ROUTES.ARRETS, access: ACCESS_LEVELS.PROTECTED, title: 'Arrêts', icon: 'stop' },
  { path: ROUTES.PROFILE, access: ACCESS_LEVELS.PROTECTED, title: 'Profil', icon: 'user' },
  { path: ROUTES.NOTIFICATIONS, access: ACCESS_LEVELS.PROTECTED, title: 'Notifications', icon: 'bell' },

  { path: ROUTES.REPORTS, access: ACCESS_LEVELS.PROTECTED, title: 'Rapports', icon: 'file-text' },
  
  // Admin routes
  { path: ROUTES.ADMIN, access: ACCESS_LEVELS.ADMIN, title: 'Administration', icon: 'tool' },
  { path: ROUTES.ADMIN_USERS, access: ACCESS_LEVELS.ADMIN, title: 'Gestion des utilisateurs', icon: 'team' },
  
  // Legacy routes
  { path: ROUTES.OLD_DASHBOARD, access: ACCESS_LEVELS.PROTECTED, title: 'Ancien tableau de bord', icon: 'dashboard' },
  { path: ROUTES.TEST, access: ACCESS_LEVELS.PROTECTED, title: 'Test', icon: 'experiment' },
];

/**
 * Get route definition by path
 * @param {string} path - Route path
 * @returns {Object|undefined} Route definition
 */
export const getRouteByPath = (path) => {
  return ROUTE_DEFINITIONS.find(route => {
    // Handle routes with parameters
    const routePathBase = route.path.split('/:')[0];
    return path === route.path || path.startsWith(routePathBase + '/');
  });
};

/**
 * Get route title by path
 * @param {string} path - Route path
 * @returns {string} Route title
 */
export const getRouteTitle = (path) => {
  const route = getRouteByPath(path);
  return route ? route.title : 'Somipem';
};

/**
 * Check if route is public
 * @param {string} path - Route path
 * @returns {boolean} Whether route is public
 */
export const isPublicRoute = (path) => {
  const route = getRouteByPath(path);
  return route ? route.access === ACCESS_LEVELS.PUBLIC : false;
};

/**
 * Check if route requires admin access
 * @param {string} path - Route path
 * @returns {boolean} Whether route requires admin access
 */
export const isAdminRoute = (path) => {
  const route = getRouteByPath(path);
  return route ? route.access === ACCESS_LEVELS.ADMIN : false;
};

/**
 * Get navigation menu items based on user role
 * @param {string[]} userRoles - User roles
 * @returns {Array} Navigation menu items
 */
export const getNavMenuItems = (userRoles = []) => {
  const isAdmin = userRoles.includes('admin');
  
  return ROUTE_DEFINITIONS
    .filter(route => {
      // Filter out public routes and routes without icons
      if (route.access === ACCESS_LEVELS.PUBLIC || !route.icon) {
        return false;
      }
      
      // Filter out admin routes for non-admin users
      if (route.access === ACCESS_LEVELS.ADMIN && !isAdmin) {
        return false;
      }
      
      return true;
    })
    .map(route => ({
      key: route.path,
      path: route.path,
      label: route.title,
      icon: route.icon,
    }));
};

/**
 * Get default route after login based on user role
 * @param {string[]} userRoles - User roles
 * @returns {string} Default route path
 */
export const getDefaultRoute = (userRoles = []) => {
  const isAdmin = userRoles.includes('admin');
  
  // Check if there's a saved last route
  const lastRoute = localStorage.getItem(STORAGE_KEYS.LAST_ROUTE);
  if (lastRoute && lastRoute !== ROUTES.LOGIN) {
    const route = getRouteByPath(lastRoute);
    
    // Verify the route exists and user has access
    if (route) {
      if (route.access === ACCESS_LEVELS.PROTECTED) {
        return lastRoute;
      }
      
      if (route.access === ACCESS_LEVELS.ADMIN && isAdmin) {
        return lastRoute;
      }
    }
  }
  
  // Default routes
  return isAdmin ? ROUTES.ADMIN : ROUTES.HOME;
};

/**
 * Save current route to localStorage
 * @param {string} path - Current route path
 */
export const saveCurrentRoute = (path) => {
  if (path && path !== ROUTES.LOGIN && path !== ROUTES.UNAUTHORIZED) {
    localStorage.setItem(STORAGE_KEYS.LAST_ROUTE, path);
  }
};

/**
 * Build URL with query parameters
 * @param {string} baseUrl - Base URL
 * @param {Object} params - Query parameters
 * @returns {string} URL with query parameters
 */
export const buildUrl = (baseUrl, params = {}) => {
  const url = new URL(baseUrl, window.location.origin);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.append(key, value);
    }
  });
  
  return url.toString();
};

/**
 * Parse query parameters from URL
 * @param {string} [url=window.location.search] - URL or query string
 * @returns {Object} Parsed query parameters
 */
export const parseQueryParams = (url = window.location.search) => {
  const params = {};
  const searchParams = new URLSearchParams(url);
  
  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }
  
  return params;
};

export default {
  ROUTES,
  ACCESS_LEVELS,
  ROUTE_DEFINITIONS,
  getRouteByPath,
  getRouteTitle,
  isPublicRoute,
  isAdminRoute,
  getNavMenuItems,
  getDefaultRoute,
  saveCurrentRoute,
  buildUrl,
  parseQueryParams,
};