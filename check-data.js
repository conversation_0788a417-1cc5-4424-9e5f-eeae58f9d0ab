import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'testingarea51',
  port: 3306
};

async function checkData() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // Check recent data
    const [recentData] = await connection.execute(`
      SELECT COUNT(*) as count, MAX(Date_Insert_Day) as latest 
      FROM machine_daily_table_mould 
      WHERE Machine_Name = 'IPS01'
    `);
    
    console.log('Recent data check:', recentData[0]);
    
    // Check sample records
    const [sampleData] = await connection.execute(`
      SELECT Date_Insert_Day, Machine_Name, Shift, OEE_Day, Good_QTY_Day 
      FROM machine_daily_table_mould 
      WHERE Machine_Name = 'IPS01' 
      ORDER BY Date_Insert_Day DESC 
      LIMIT 5
    `);
    
    console.log('Sample records:');
    sampleData.forEach(row => {
      console.log(`  ${row.Date_Insert_Day} - ${row.Machine_Name} - ${row.Shift} - OEE: ${row.OEE_Day} - Qty: ${row.Good_QTY_Day}`);
    });
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkData();
