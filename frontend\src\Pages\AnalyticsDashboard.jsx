import React, { useState, useCallback, useMemo } from 'react';
import { Layout, Typography, Card, Row, Col, Tabs, Space, Button, Badge, Tooltip } from 'antd';
import { 
  DashboardOutlined,
  RobotOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  FundOutlined,
  <PERSON>Outlined,
  ToolOutlined,
  UserOutlined,
  <PERSON>boltOutlined,
  BulbOutlined,
  ExperimentOutlined,
  TrophyOutlined,
  RocketOutlined,
  EyeOutlined,
  StarOutlined
} from '@ant-design/icons';

// Import modular components
import AnalyticsHeader from '../Components/analytics/AnalyticsHeader';
import AnalyticsFilters from '../Components/analytics/AnalyticsFilters';
import AnalyticsStatsCards from '../Components/analytics/AnalyticsStatsCards';
import ProductionIntelligenceSection from '../Components/analytics/sections/ProductionIntelligenceSection';
import OperatorIntelligenceSection from '../Components/analytics/sections/OperatorIntelligenceSection';
import WeightAnalyticsSection from '../Components/analytics/sections/WeightAnalyticsSection';
import FilterIntelligenceSection from '../Components/analytics/sections/FilterIntelligenceSection';
import BusinessIntelligenceSection from '../Components/analytics/sections/BusinessIntelligenceSection';
import MaintenanceIntelligenceSection from '../Components/analytics/sections/MaintenanceIntelligenceSection';
import QualityIntelligenceSection from '../Components/analytics/sections/QualityIntelligenceSection';
import PerformanceOptimizationSection from '../Components/analytics/sections/PerformanceOptimizationSection';
import RealtimeIntelligenceSection from '../Components/analytics/sections/RealtimeIntelligenceSection';
import StrategicIntelligenceSection from '../Components/analytics/sections/StrategicIntelligenceSection';
import ContinuousImprovementSection from '../Components/analytics/sections/ContinuousImprovementSection';

const { Content } = Layout;
const { Title, Text } = Typography;

const AnalyticsDashboard = () => {
  // State management
  const [activeTab, setActiveTab] = useState("production");
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: null,
    machine: null,
    partNumber: null,
    operator: null,
    shift: null
  });

  // Filter change handler
  const handleFilterChange = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Tab change handler
  const handleTabChange = useCallback((key) => {
    setActiveTab(key);
  }, []);

  // Memoized tab items for performance
  const tabItems = useMemo(() => [
    {
      key: "production",
      label: (
        <Space>
          <RobotOutlined style={{ color: '#1890ff' }} />
          <span>Production Intelligence</span>
          <Badge count="AI" style={{ backgroundColor: '#52c41a' }} />
        </Space>
      ),
      children: <ProductionIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "operator",
      label: (
        <Space>
          <UserOutlined style={{ color: '#722ed1' }} />
          <span>Operator Intelligence</span>
          <Badge count="360°" style={{ backgroundColor: '#eb2f96' }} />
        </Space>
      ),
      children: <OperatorIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "weight",
      label: (
        <Space>
          <DatabaseOutlined style={{ color: '#fa8c16' }} />
          <span>Weight Analytics</span>
          <Badge count="ML" style={{ backgroundColor: '#faad14' }} />
        </Space>
      ),
      children: <WeightAnalyticsSection loading={loading} filters={filters} />
    },
    {
      key: "filters",
      label: (
        <Space>
          <ThunderboltOutlined style={{ color: '#13c2c2' }} />
          <span>Filter Intelligence</span>
          <Badge count="Smart" style={{ backgroundColor: '#1890ff' }} />
        </Space>
      ),
      children: <FilterIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "business",
      label: (
        <Space>
          <LineChartOutlined style={{ color: '#52c41a' }} />
          <span>Business Intelligence</span>
          <Badge count="ROI" style={{ backgroundColor: '#f5222d' }} />
        </Space>
      ),
      children: <BusinessIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "maintenance",
      label: (
        <Space>
          <ToolOutlined style={{ color: '#fa541c' }} />
          <span>Maintenance Intelligence</span>
          <Badge count="Predictive" style={{ backgroundColor: '#722ed1' }} />
        </Space>
      ),
      children: <MaintenanceIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "quality",
      label: (
        <Space>
          <TrophyOutlined style={{ color: '#eb2f96' }} />
          <span>Quality Intelligence</span>
          <Badge count="ML" style={{ backgroundColor: '#13c2c2' }} />
        </Space>
      ),
      children: <QualityIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "performance",
      label: (
        <Space>
          <RocketOutlined style={{ color: '#f5222d' }} />
          <span>Performance Optimization</span>
          <Badge count="Auto" style={{ backgroundColor: '#fa8c16' }} />
        </Space>
      ),
      children: <PerformanceOptimizationSection loading={loading} filters={filters} />
    },
    {
      key: "realtime",
      label: (
        <Space>
          <EyeOutlined style={{ color: '#faad14' }} />
          <span>Real-time Intelligence</span>
          <Badge count="Live" style={{ backgroundColor: '#f5222d' }} />
        </Space>
      ),
      children: <RealtimeIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "strategic",
      label: (
        <Space>
          <ExperimentOutlined style={{ color: '#096dd9' }} />
          <span>Strategic Intelligence</span>
          <Badge count="Future" style={{ backgroundColor: '#722ed1' }} />
        </Space>
      ),
      children: <StrategicIntelligenceSection loading={loading} filters={filters} />
    },
    {
      key: "improvement",
      label: (
        <Space>
          <BulbOutlined style={{ color: '#a0d911' }} />
          <span>Continuous Improvement</span>
          <Badge count="Learning" style={{ backgroundColor: '#52c41a' }} />
        </Space>
      ),
      children: <ContinuousImprovementSection loading={loading} filters={filters} />
    }
  ], [loading, filters]);

  return (
    <Layout style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'relative'
    }}>
      {/* Animated background pattern */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%)
        `,
        animation: 'float 20s ease-in-out infinite',
        zIndex: 0
      }} />
      
      <Content style={{ 
        padding: '24px',
        position: 'relative',
        zIndex: 1
      }}>
        <div style={{ 
          maxWidth: '1600px', 
          margin: '0 auto',
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '32px',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          
          {/* Header Section */}
          <AnalyticsHeader />
          
          {/* Filters Section */}
          <AnalyticsFilters 
            filters={filters}
            onFilterChange={handleFilterChange}
            loading={loading}
          />
          
          {/* Stats Cards Section */}
          <AnalyticsStatsCards loading={loading} filters={filters} />
          
          {/* Main Analytics Tabs */}
          <Card
            style={{
              marginTop: '24px',
              borderRadius: '16px',
              border: 'none',
              boxShadow: '0 8px 24px rgba(0,0,0,0.08)'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <Tabs
              activeKey={activeTab}
              onChange={handleTabChange}
              type="card"
              size="large"
              items={tabItems}
              tabBarStyle={{
                borderBottom: '2px solid #f0f0f0',
                marginBottom: '24px'
              }}
              tabBarGutter={8}
            />
          </Card>
        </div>
      </Content>
      
      {/* Global CSS animations */}
      <style jsx global>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          33% {
            transform: translateY(-10px) rotate(1deg);
          }
          66% {
            transform: translateY(5px) rotate(-1deg);
          }
        }
        
        @keyframes glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
          }
          50% {
            box-shadow: 0 0 30px rgba(24, 144, 255, 0.5);
          }
        }
        
        .ant-tabs-tab {
          border-radius: 12px !important;
          transition: all 0.3s ease !important;
        }
        
        .ant-tabs-tab:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        }
        
        .ant-tabs-tab-active {
          background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
          color: white !important;
          border-color: transparent !important;
        }
        
        .ant-tabs-tab-active .anticon,
        .ant-tabs-tab-active span {
          color: white !important;
        }
        
        .ant-card {
          transition: all 0.3s ease !important;
        }
        
        .ant-card:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 12px 28px rgba(0,0,0,0.12) !important;
        }
        
        .ant-badge-count {
          border-radius: 10px !important;
          font-size: 10px !important;
          line-height: 18px !important;
          min-width: 18px !important;
          height: 18px !important;
        }
      `}</style>
    </Layout>
  );
};

export default AnalyticsDashboard;
