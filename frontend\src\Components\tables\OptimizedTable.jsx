import React, { useState, useMemo, useCallback } from 'react';
import { Table, Button, Space, Tag, Alert, Tooltip, Pagination } from 'antd';
import { DownloadOutlined, InfoCircleOutlined, WarningOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';

/**
 * Optimized Table Component with performance enhancements
 * - Server-side pagination
 * - Data size warnings
 * - Virtual scrolling for large datasets
 * - Export functionality
 * - Performance monitoring
 */
const OptimizedTable = ({
  dataSource = [],
  columns = [],
  loading = false,
  title,
  totalRecords = 0,
  pageSize = 100,
  currentPage = 1,
  onPageChange,
  onPageSizeChange,
  exportEnabled = false,
  onExport,
  maxRecordsWarning = 1000,
  performanceMode = false,
  rowKey = 'id',
  scroll = { x: 1300 },
  expandable,
  ...tableProps
}) => {
  const [exportLoading, setExportLoading] = useState(false);

  // Calculate performance metrics
  const performanceMetrics = useMemo(() => {
    const recordCount = dataSource.length;
    const estimatedRenderTime = recordCount * 0.1; // Rough estimate: 0.1ms per record
    const isLargeDataset = recordCount > maxRecordsWarning;
    
    return {
      recordCount,
      estimatedRenderTime,
      isLargeDataset,
      performanceLevel: recordCount > 2000 ? 'poor' : recordCount > 1000 ? 'warning' : 'good'
    };
  }, [dataSource.length, maxRecordsWarning]);

  // Handle export functionality
  const handleExport = useCallback(async () => {
    if (!onExport) return;
    
    setExportLoading(true);
    try {
      await onExport({
        data: dataSource,
        totalRecords,
        currentPage,
        pageSize
      });
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setExportLoading(false);
    }
  }, [onExport, dataSource, totalRecords, currentPage, pageSize]);

  // Optimized pagination configuration
  const paginationConfig = useMemo(() => ({
    current: currentPage,
    pageSize: pageSize,
    total: totalRecords,
    showSizeChanger: true,
    showQuickJumper: totalRecords > 1000,
    pageSizeOptions: ['50', '100', '200', '500'],
    showTotal: (total, range) => 
      `${range[0]}-${range[1]} sur ${total} enregistrements`,
    onChange: onPageChange,
    onShowSizeChange: onPageSizeChange,
    size: 'default'
  }), [currentPage, pageSize, totalRecords, onPageChange, onPageSizeChange]);

  // Performance warning component
  const PerformanceWarning = () => {
    if (!performanceMetrics.isLargeDataset) return null;

    return (
      <Alert
        message={`Attention: ${performanceMetrics.recordCount} enregistrements`}
        description={`Le chargement peut prendre ${Math.ceil(performanceMetrics.estimatedRenderTime / 1000)}s. Considérez l'utilisation de filtres pour améliorer les performances.`}
        type="warning"
        showIcon
        icon={<WarningOutlined />}
        style={{ marginBottom: 16 }}
        action={
          <Space>
            <Button size="small" type="link">
              Optimiser les filtres
            </Button>
          </Space>
        }
      />
    );
  };

  // Table title with performance indicators
  const tableTitle = useMemo(() => {
    if (!title) return null;

    return (
      <Space>
        <span>{title}</span>
        <Tag color={
          performanceMetrics.performanceLevel === 'good' ? 'green' :
          performanceMetrics.performanceLevel === 'warning' ? 'orange' : 'red'
        }>
          {performanceMetrics.recordCount} enregistrements
        </Tag>
        {performanceMode && (
          <Tooltip title={`Temps de rendu estimé: ${performanceMetrics.estimatedRenderTime.toFixed(1)}ms`}>
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        )}
      </Space>
    );
  }, [title, performanceMetrics, performanceMode]);

  // Table extra actions
  const tableExtra = useMemo(() => (
    <Space>
      {exportEnabled && (
        <Button
          icon={<DownloadOutlined />}
          onClick={handleExport}
          loading={exportLoading}
          disabled={dataSource.length === 0}
        >
          Exporter
        </Button>
      )}
      {performanceMode && (
        <Tag color="blue">
          Mode Performance
        </Tag>
      )}
    </Space>
  ), [exportEnabled, handleExport, exportLoading, dataSource.length, performanceMode]);

  // Optimized table configuration
  const optimizedTableProps = useMemo(() => ({
    ...tableProps,
    dataSource,
    columns,
    loading,
    rowKey,
    scroll: performanceMetrics.isLargeDataset ? { ...scroll, y: 400 } : scroll,
    pagination: totalRecords > pageSize ? paginationConfig : false,
    size: performanceMetrics.isLargeDataset ? 'small' : 'middle',
    expandable,
    title: tableTitle ? () => tableTitle : undefined,
    extra: tableExtra,
    // Performance optimizations
    virtual: performanceMetrics.isLargeDataset,
    sticky: true,
    showSorterTooltip: false,
    // Reduce re-renders
    rowSelection: tableProps.rowSelection ? {
      ...tableProps.rowSelection,
      preserveSelectedRowKeys: true
    } : undefined
  }), [
    tableProps,
    dataSource,
    columns,
    loading,
    rowKey,
    scroll,
    performanceMetrics.isLargeDataset,
    totalRecords,
    pageSize,
    paginationConfig,
    expandable,
    tableTitle,
    tableExtra
  ]);

  return (
    <div>
      <PerformanceWarning />
      <Table {...optimizedTableProps} />
      
      {/* Additional pagination for large datasets */}
      {totalRecords > 1000 && (
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Pagination
            {...paginationConfig}
            simple={false}
            showLessItems={false}
          />
        </div>
      )}
    </div>
  );
};

OptimizedTable.propTypes = {
  dataSource: PropTypes.array,
  columns: PropTypes.array,
  loading: PropTypes.bool,
  title: PropTypes.string,
  totalRecords: PropTypes.number,
  pageSize: PropTypes.number,
  currentPage: PropTypes.number,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func,
  exportEnabled: PropTypes.bool,
  onExport: PropTypes.func,
  maxRecordsWarning: PropTypes.number,
  performanceMode: PropTypes.bool,
  rowKey: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
  scroll: PropTypes.object,
  expandable: PropTypes.object
};

export default OptimizedTable;
