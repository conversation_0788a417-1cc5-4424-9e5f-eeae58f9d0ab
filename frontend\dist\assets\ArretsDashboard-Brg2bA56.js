import{r as T,a2 as vt,ag as It,n as Ue,a as Nt,R as e,O as Ve,C as ne,m as K,b as V,c as H,T as oe,S as i,B as dt,d as j,g as U,A as ut,aj as Ie,P as wt,f as re,al as qe,e as xe,E as $,h as ye,aa as Ne,F as mt,i as Je,G as Yt}from"./index-O2xm1U_Z.js";import{d as W}from"./dayjs.min-CgAD4wBe.js";import{i as kt,R as Lt,G as Pt}from"./GlobalSearchModal-CB8P5fP4.js";import{R as be}from"./CheckCircleOutlined-BmO9kYsr.js";import{R as Ft}from"./SearchOutlined-CzaKf_7S.js";import{R as Bt,b as $t,D as We}from"./DownloadOutlined-ClmkhSDC.js";import{R as Ze}from"./ReloadOutlined-EeD9QNgc.js";import{R as Ot}from"./HistoryOutlined-kCKI-dwE.js";import{f as Te,a as ge,b as zt}from"./numberFormatter-CKFvf91F.js";import{R as _e}from"./CalendarOutlined-ry8TLVWh.js";import{R as pt}from"./AlertOutlined-BsPkpdqt.js";import{R as Ht}from"./WarningOutlined-BiatBBB-.js";import{R as X}from"./ClockCircleOutlined-D-iaV6k8.js";import{R as Ye}from"./ToolOutlined-BRYzAkU2.js";import{S as Me}from"./index-Dc91-n-S.js";import{R as ue}from"./InfoCircleOutlined-BSC7vzM8.js";import{P as Re}from"./progress-CVvjjq5H.js";import{S as Gt}from"./index-fmer6zpJ.js";import{R as Ut}from"./FilterOutlined-B3wNZQVB.js";import{u as ie,a as Wt}from"./chartColors-CyE5IZmc.js";import{R as we}from"./BarChartOutlined-CX9KDBHm.js";import{R as J,B as he,C as ee,X as te,Y as q,T as Z,a as se,g as ae,P as jt,e as Kt,f as et,L as tt,d as ce,b as Vt}from"./PieChart-CJMXTNKV.js";import{A as ke,a as Le,C as qt}from"./ComposedChart-Co3xUnR4.js";import{R as Pe,a as Zt}from"./TrophyOutlined-COlt6cAY.js";import{B as rt,C as Fe,b as Be,c as $e,d as Oe,p as ze,f as He,g as Ge,L as Qt,e as Xt,P as Jt}from"./index-D6Px96Y3.js";import{R as ft}from"./LineChartOutlined-CdWMpnra.js";import{M as er}from"./performance-metrics-gauge-A147G03C.js";import{u as gt,U as tr}from"./UnifiedChartExpansion-D6afXd9w.js";import{R as rr}from"./PieChartOutlined-zW9kEC3-.js";import{R as nr}from"./AreaChartOutlined-DTUo7GpA.js";import{R as ar}from"./DashboardOutlined-CC3QTJ5W.js";import{R as or}from"./AppstoreOutlined-BlZTSyy5.js";import{L as Qe}from"./index-CTwwobLF.js";import"./index-fv7kzFnJ.js";import"./FileTextOutlined-ZE845-EP.js";import"./FullscreenOutlined-DMf8_5Nq.js";import"./CloseOutlined-CLsA06b-.js";import"./ZoomOutOutlined-CdPjzMfa.js";var ir={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M855.7 210.8l-42.4-42.4a8.03 8.03 0 00-11.3 0L168.3 801.9a8.03 8.03 0 000 11.3l42.4 42.4c3.1 3.1 8.2 3.1 11.3 0L855.6 222c3.2-3 3.2-8.1.1-11.2zM304 448c79.4 0 144-64.6 144-144s-64.6-144-144-144-144 64.6-144 144 64.6 144 144 144zm0-216c39.7 0 72 32.3 72 72s-32.3 72-72 72-72-32.3-72-72 32.3-72 72-72zm416 344c-79.4 0-144 64.6-144 144s64.6 144 144 144 144-64.6 144-144-64.6-144-144-144zm0 216c-39.7 0-72-32.3-72-72s32.3-72 72-72 72 32.3 72 72-32.3 72-72 72z"}}]},name:"percentage",theme:"outlined"};function Xe(){return Xe=Object.assign?Object.assign.bind():function(a){for(var s=1;s<arguments.length;s++){var p=arguments[s];for(var f in p)Object.prototype.hasOwnProperty.call(p,f)&&(a[f]=p[f])}return a},Xe.apply(this,arguments)}const lr=(a,s)=>T.createElement(vt,Xe({},a,{ref:s,icon:ir})),sr=T.forwardRef(lr);var ve={exports:{}},cr=ve.exports,at;function dr(){return at||(at=1,function(a,s){(function(p,f){a.exports=f()})(cr,function(){return function(p,f){f.prototype.isSameOrBefore=function(o,u){return this.isSame(o,u)||this.isBefore(o,u)}}})}(ve)),ve.exports}var ur=dr();const mr=It(ur),L={STATS:"stats",CHARTS:"charts",TABLE:"table",PERFORMANCE:"performance",TOP_STOPS_CHART:"topStopsChart",DURATION_TREND_CHART:"durationTrendChart",MACHINE_COMPARISON_CHART:"machineComparisonChart",PARETO_CHART:"paretoChart",MTTR_HEATMAP:"mttrHeatmap",AVAILABILITY_CHART:"availabilityChart",FILTERS:"filters",HEADER:"header",SIDEBAR:"sidebar",PAGINATION:"pagination",INITIAL_LOAD:"initialLoad",DATA_REFRESH:"dataRefresh",FILTER_CHANGE:"filterChange"},pr=[{sections:[L.STATS],delay:200},{sections:[L.PERFORMANCE],delay:400},{sections:[L.TOP_STOPS_CHART,L.DURATION_TREND_CHART],delay:600},{sections:[L.MACHINE_COMPARISON_CHART,L.PARETO_CHART],delay:800},{sections:[L.TABLE,L.PAGINATION],delay:1e3},{sections:[L.MTTR_HEATMAP,L.AVAILABILITY_CHART],delay:1200},{sections:[L.INITIAL_LOAD,L.DATA_REFRESH,L.FILTER_CHANGE],delay:1400}],ot={MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,DAYS_PER_WEEK:7,APPROX_DAYS_PER_MONTH:30},ht={[L.STATS]:!1,[L.CHARTS]:!1,[L.TABLE]:!1,[L.PERFORMANCE]:!1,[L.TOP_STOPS_CHART]:!1,[L.DURATION_TREND_CHART]:!1,[L.MACHINE_COMPARISON_CHART]:!1,[L.PARETO_CHART]:!1,[L.MTTR_HEATMAP]:!1,[L.AVAILABILITY_CHART]:!1,[L.FILTERS]:!1,[L.HEADER]:!1,[L.SIDEBAR]:!1,[L.PAGINATION]:!1,[L.INITIAL_LOAD]:!1,[L.DATA_REFRESH]:!1,[L.FILTER_CHANGE]:!1},fr=(a,s)=>{const p=T.useCallback((c=[])=>{c.length===0&&(c=[L.STATS,L.CHARTS,L.TABLE,L.INITIAL_LOAD]);const d={};c.forEach(m=>{d[m]=!0}),s(m=>({...m,...d}))},[s]),f=T.useCallback((c=[])=>{if(c.length===0){s(ht);return}const d={};c.forEach(m=>{d[m]=!1}),s(m=>({...m,...d}))},[s]),o=T.useCallback((c=null)=>{(c||pr).forEach(({sections:m,delay:h})=>{setTimeout(()=>{f(m)},h)})},[f]),u=T.useCallback((c,d,m)=>{const h=[L.STATS];return d&&h.push(L.PERFORMANCE,L.MACHINE_COMPARISON_CHART),m&&h.push(L.DURATION_TREND_CHART,L.MTTR_HEATMAP),(c||d)&&h.push(L.TOP_STOPS_CHART,L.PARETO_CHART),c&&d&&m&&h.push(L.TABLE,L.AVAILABILITY_CHART,L.FILTER_CHANGE),p(h),h},[p]),r=T.useCallback(c=>a[c]||!1,[a]),t=T.useCallback(c=>c.some(d=>a[d]),[a]),l=T.useCallback(()=>Object.keys(a).filter(c=>a[c]),[a]),n=T.useCallback(()=>{const c=[{sections:[L.STATS],delay:100},{sections:[L.PERFORMANCE],delay:200},{sections:[L.TOP_STOPS_CHART,L.DURATION_TREND_CHART],delay:300},{sections:[L.TABLE,L.CHARTS],delay:400},{sections:[L.INITIAL_LOAD,L.DATA_REFRESH],delay:500}];o(c)},[o]);return{startSkeletonLoading:p,stopSkeletonLoading:f,progressiveSkeletonClear:o,smartSkeletonForFilters:u,isSkeletonActive:r,areSkeletonsActive:t,getActiveSkeletons:l,fastSkeletonClear:n}},gr=(a=[],s=5)=>{if(!a||a.length===0)return[];try{const p=a.reduce((o,u)=>{const r=u.type_arret||u.raison_arret||"Unknown";if(o[r]||(o[r]={reason:r,count:0,totalDuration:0,stops:[]}),o[r].count+=1,o[r].stops.push(u),u.duree_arret){const t=u.duree_arret.split(":"),l=parseInt(t[0])||0,n=parseInt(t[1])||0;o[r].totalDuration+=l*60+n}return o},{});return Object.values(p).sort((o,u)=>u.count-o.count).slice(0,s).map((o,u)=>({id:u+1,reason:o.reason,count:o.count,totalDuration:o.totalDuration,percentage:Math.round(o.count/a.length*100),stops:o.stops}))}catch(p){return console.error("❌ Error extracting top stops:",p),[]}},hr=a=>{const s=T.useMemo(()=>{const t={topStopsData:[],chartData:[],stopReasons:[]};try{a.stopsData&&a.stopsData.length>0&&(t.topStopsData=gr(a.stopsData,5),t.stopReasons=[...new Set(a.stopsData.map(l=>l.Code_Stop||l.type_arret||l.raison_arret).filter(l=>l&&l.trim()!==""))].map((l,n)=>({id:n+1,name:l,count:a.stopsData.filter(c=>(c.Code_Stop||c.type_arret||c.raison_arret)===l).length}))),t.chartData=a.rawChartData||[]}catch(l){console.error("❌ Error computing chart data:",l)}return t},[a.stopsData,a.rawChartData]),p=T.useMemo(()=>{if(!a.stopsData||a.stopsData.length===0)return[];let t=[...a.stopsData];if(a.selectedMachine&&(t=t.filter(l=>l.Machine_Name===a.selectedMachine)),a.selectedMachineModel&&!a.selectedMachine&&(t=t.filter(l=>{var d;const n=l.Machine_Name||"";let c="";return n.startsWith("IPSO")?c="IPSO":n.startsWith("IPS")?c="IPS":n.startsWith("CCM")?c="CCM":c=((d=n.match(/^[A-Za-z]+/))==null?void 0:d[0])||"",c===a.selectedMachineModel})),a.selectedDate){const l=a.selectedDate.format("YYYY-MM-DD");t=t.filter(n=>{if(!n.Date_Insert)return!1;let c;if(n.Date_Insert.includes("/")){const[d,m,h]=n.Date_Insert.split("/");c=`${h}-${m.padStart(2,"0")}-${d.padStart(2,"0")}`}else c=n.Date_Insert.split("T")[0];return c===l})}return t},[a.stopsData,a.selectedMachine,a.selectedMachineModel,a.selectedDate]),f=T.useMemo(()=>{const t={totalStops:0,totalDuration:0,averageDuration:0,topReasons:[],machineDistribution:[],timeDistribution:[]};try{if(p&&p.length>0){t.totalStops=p.length,t.totalDuration=p.reduce((d,m)=>{if(m.duration_minutes&&m.duration_minutes>0)return d+(parseFloat(m.duration_minutes)||0);if(m.duree_arret){const h=m.duree_arret.split(":"),g=parseInt(h[0])||0,y=parseInt(h[1])||0;return d+g*60+y}else if(m.Debut_Stop&&m.Fin_Stop_Time)try{const h=E=>{const[b,A]=E.split(" "),[D,x,_]=b.split("/"),[Y,N]=A.split(":");return new Date(_,x-1,D,Y,N)},g=h(m.Debut_Stop),y=h(m.Fin_Stop_Time);if(!isNaN(g.getTime())&&!isNaN(y.getTime())){const E=y-g,b=Math.max(0,Math.floor(E/(1e3*60)));return d+b}}catch(h){return console.warn("Error calculating duration for stop:",m,h),d}return d},0),t.averageDuration=t.totalStops>0?t.totalDuration/t.totalStops:0;const l={};p.forEach(d=>{const m=d.Code_Stop||d.type_arret||d.raison_arret||"Unknown";l[m]=(l[m]||0)+1}),t.topReasons=Object.entries(l).map(([d,m])=>({reason:d,count:m})).sort((d,m)=>m.count-d.count).slice(0,5);const n={};p.forEach(d=>{const m=d.Machine_Name||"Unknown";n[m]=(n[m]||0)+1}),t.machineDistribution=Object.entries(n).map(([d,m])=>({machine:d,count:m})).sort((d,m)=>m.count-d.count);const c=new Array(24).fill(0);p.forEach(d=>{if(d.Debut_Stop){const m=parseInt(d.Debut_Stop.split(":")[0])||0;m>=0&&m<24&&c[m]++}}),t.timeDistribution=c.map((d,m)=>({hour:`${m.toString().padStart(2,"0")}:00`,count:d}))}}catch(l){console.error("❌ Error computing chart calculations:",l)}return t},[p]),o=T.useMemo(()=>{const t={totalStops:0,totalDuration:0,averageDuration:0,totalMachines:0,criticalStops:0,declaredStops:0,undeclaredStops:0};try{if(a.stopsData&&a.stopsData.length>0){t.totalStops=a.stopsData.length,t.declaredStops=a.stopsData.filter(n=>{const c=n.Code_Stop||n.type_arret||n.raison_arret;return c&&c!=="Arrêt non déclaré"&&c!=="Non déclaré"&&c!=="Undeclared"}).length,t.undeclaredStops=t.totalStops-t.declaredStops,t.totalDuration=a.stopsData.reduce((n,c)=>{if(c.duration_minutes&&c.duration_minutes>0)return n+(parseFloat(c.duration_minutes)||0);if(c.duree_arret){const d=c.duree_arret.split(":"),m=parseInt(d[0])||0,h=parseInt(d[1])||0;return n+m*60+h}else if(c.Debut_Stop&&c.Fin_Stop_Time)try{const d=g=>{const[y,E]=g.split(" "),[b,A,D]=y.split("/"),[x,_]=E.split(":");return new Date(D,A-1,b,x,_)},m=d(c.Debut_Stop),h=d(c.Fin_Stop_Time);if(!isNaN(m.getTime())&&!isNaN(h.getTime())){const g=h-m,y=Math.max(0,Math.floor(g/(1e3*60)));return n+y}}catch(d){return console.warn("Error calculating duration for stop:",c,d),n}return n},0),t.averageDuration=t.totalStops>0?t.totalDuration/t.totalStops:0;const l=new Set(a.stopsData.map(n=>n.Machine_Name).filter(Boolean));t.totalMachines=l.size,t.criticalStops=a.stopsData.filter(n=>{if(n.duration_minutes&&n.duration_minutes>0)return parseFloat(n.duration_minutes)>60;if(n.duree_arret){const c=n.duree_arret.split(":");return(parseInt(c[0])||0)>=1}else if(n.Debut_Stop&&n.Fin_Stop_Time)try{const c=h=>{const[g,y]=h.split(" "),[E,b,A]=g.split("/"),[D,x]=y.split(":");return new Date(A,b-1,E,D,x)},d=c(n.Debut_Stop),m=c(n.Fin_Stop_Time);if(!isNaN(d.getTime())&&!isNaN(m.getTime())){const h=m-d;return Math.max(0,Math.floor(h/(1e3*60)))>60}}catch{return!1}return!1}).length}}catch(l){console.error("❌ Error computing global calculations:",l)}return t},[a.stopsData]),u=T.useMemo(()=>{var l;const t={totalMachines:0,activeMachines:0,totalStops:0,criticalStops:0,averageDowntime:0,availability:0};try{if(a.stopsData&&a.stopsData.length>0){const n=new Set(a.stopsData.map(d=>d.Machine_Name).filter(Boolean));t.totalMachines=n.size,t.activeMachines=n.size,t.totalStops=a.stopsData.length,t.criticalStops=a.stopsData.filter(d=>{if(d.duration_minutes&&d.duration_minutes>0)return parseFloat(d.duration_minutes)>60;if(d.duree_arret){const m=d.duree_arret.split(":");return(parseInt(m[0])||0)>=1}else if(d.Debut_Stop&&d.Fin_Stop_Time)try{const m=y=>{const[E,b]=y.split(" "),[A,D,x]=E.split("/"),[_,Y]=b.split(":");return new Date(x,D-1,A,_,Y)},h=m(d.Debut_Stop),g=m(d.Fin_Stop_Time);if(!isNaN(h.getTime())&&!isNaN(g.getTime())){const y=g-h;return Math.max(0,Math.floor(y/(1e3*60)))>60}}catch{return!1}return!1}).length;const c=a.stopsData.reduce((d,m)=>{if(m.duration_minutes&&m.duration_minutes>0)return d+(parseFloat(m.duration_minutes)||0);if(m.duree_arret){const h=m.duree_arret.split(":"),g=parseInt(h[0])||0,y=parseInt(h[1])||0;return d+g*60+y}else if(m.Debut_Stop&&m.Fin_Stop_Time)try{const h=E=>{const[b,A]=E.split(" "),[D,x,_]=b.split("/"),[Y,N]=A.split(":");return new Date(_,x-1,D,Y,N)},g=h(m.Debut_Stop),y=h(m.Fin_Stop_Time);if(!isNaN(g.getTime())&&!isNaN(y.getTime())){const E=y-g,b=Math.max(0,Math.floor(E/(1e3*60)));return d+b}}catch{return d}return d},0);t.averageDowntime=t.totalStops>0?c/t.totalStops:0,t.availability=a.doper||0}}catch(n){console.error("❌ Error computing sidebar stats:",n)}return[{title:"Total Arrêts",value:t.totalStops,suffix:"arrêts",icon:"AlertOutlined",color:"#f5222d"},{title:"Arrêts Non Déclarés",value:t.totalStops-(((l=a.stopsData)==null?void 0:l.filter(n=>n.Code_Stop||n.type_arret||n.raison_arret).length)||0),suffix:"arrêts",icon:"WarningOutlined",color:"#faad14"},{title:"Machines Concernées",value:t.totalMachines,suffix:"machines",icon:"ToolOutlined",color:"#1890ff"},{title:"Arrêts Critiques",value:t.criticalStops,suffix:"arrêts",icon:"ExclamationCircleOutlined",color:"#f5222d"},{title:"Temps Moyen d'Arrêt",value:Math.round(t.averageDowntime),suffix:"min",icon:"ClockCircleOutlined",color:"#722ed1"},{title:"Disponibilité",value:Math.round(t.availability*100)/100,suffix:"%",icon:"CheckCircleOutlined",color:"#52c41a"}]},[a.stopsData,a.doper]),r=T.useMemo(()=>({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{x:{display:!0,title:{display:!0}},y:{display:!0,title:{display:!0}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}),[]);return{computedChartData:s,filteredStopsData:p,chartDataCalculations:f,globalDataCalculations:o,sidebarStats:u,chartOptions:r,totalStops:o.totalStops,totalStopsGlobal:o.totalStops,undeclaredStops:o.undeclaredStops,totalStopsFiltered:(p==null?void 0:p.length)||0,undeclaredStopsFiltered:(()=>{const t=(p==null?void 0:p.length)||0,l=(p==null?void 0:p.filter(c=>{const d=c.Code_Stop||c.type_arret||c.raison_arret;return d&&d!=="Arrêt non déclaré"&&d!=="Non déclaré"&&d!=="Undeclared"}).length)||0,n=t-l;return console.log("🔍 Undeclared stops calculation (FILTERED):",{totalFiltered:t,declaredFiltered:l,undeclaredFiltered:n,hasFilters:!!(a.selectedMachine||a.selectedMachineModel||a.selectedDate)}),n})(),avgDuration:o.averageDuration,totalDuration:o.totalDuration}},yr=(a=[],s="day")=>{try{if(console.log("🔧 calculateAvailabilityTrendData called with:",{dataType:typeof a,isArray:Array.isArray(a),dataLength:(a==null?void 0:a.length)||0,dateRangeType:s,sampleData:(a==null?void 0:a.slice(0,2))||[]}),!a||a.length===0)return console.log("❌ No stops data provided to calculateAvailabilityTrendData"),[];const p=u=>{if(!u||typeof u!="string")return null;try{const r=u.trim();let t=r.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(t){const[n,c,d,m,h,g,y]=t,E=new Date(parseInt(m),parseInt(d)-1,parseInt(c),parseInt(h),parseInt(g),parseInt(y));if(!isNaN(E.getTime()))return E}if(t=r.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),t){const[n,c,d,m,h,g,y]=t;return new Date(parseInt(c),parseInt(g)-1,parseInt(y),parseInt(d),parseInt(m),parseInt(h))}const l=new Date(r);return isNaN(l.getTime())?null:l}catch{return null}},f={};a.forEach(u=>{const r=p(u.Debut_Stop||u.debut_stop||u.startTime);if(!r)return;const t=r.toISOString().split("T")[0];f[t]||(f[t]={date:t,stops:[],totalDowntime:0,stopCount:0}),f[t].stops.push(u),f[t].stopCount++;const l=p(u.Fin_Stop_Time||u.fin_stop_time||u.endTime||u.Fin_Stop);if(l&&r){const n=(l-r)/6e4;n>0&&(f[t].totalDowntime+=n)}}),console.log("📊 Grouped stops by date:",{uniqueDates:Object.keys(f).length,stopsByDate:Object.keys(f).slice(0,3).map(u=>({date:u,stopCount:f[u].stopCount,totalDowntime:f[u].totalDowntime}))});const o=Object.values(f).map(u=>{const r=ot.HOURS_PER_DAY*ot.MINUTES_PER_HOUR,t=r>0?(r-u.totalDowntime)/r*100:100;return{date:u.date,disponibilite:Math.round(t*100)/100,downtime:Math.round(u.totalDowntime*100)/100,stopCount:u.stopCount}});return o.sort((u,r)=>new Date(u.date)-new Date(r.date)),o}catch(p){return console.error("❌ Error calculating availability trend data:",p),[]}},Er=(a=[])=>{try{if(console.log("🔧 calculateMTTRCalendarData called with:",{dataType:typeof a,isArray:Array.isArray(a),dataLength:(a==null?void 0:a.length)||0,sampleData:(a==null?void 0:a.slice(0,2))||[]}),!a||a.length===0)return console.log("❌ No stops data provided to calculateMTTRCalendarData"),[];const s=o=>{if(!o||typeof o!="string")return null;try{const u=o.trim();let r=u.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(r){const[l,n,c,d,m,h,g]=r,y=new Date(parseInt(d),parseInt(c)-1,parseInt(n),parseInt(m),parseInt(h),parseInt(g));if(!isNaN(y.getTime()))return y}if(r=u.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),r){const[l,n,c,d,m,h,g]=r;return new Date(parseInt(n),parseInt(h)-1,parseInt(g),parseInt(c),parseInt(d),parseInt(m))}const t=new Date(u);return isNaN(t.getTime())?null:t}catch{return null}},p={};a.forEach(o=>{const u=s(o.Debut_Stop||o.debut_stop||o.startTime),r=s(o.Fin_Stop_Time||o.fin_stop_time||o.endTime||o.Fin_Stop);if(!u||!r)return;const t=u.toISOString().split("T")[0],l=(r-u)/(1e3*60);l>0&&(p[t]||(p[t]={date:t,totalDowntime:0,stopCount:0}),p[t].totalDowntime+=l,p[t].stopCount++)}),console.log("📊 MTTR grouped by date:",{uniqueDates:Object.keys(p).length,mttrByDate:Object.keys(p).slice(0,3).map(o=>({date:o,stopCount:p[o].stopCount,totalDowntime:p[o].totalDowntime}))});const f=Object.values(p).map(o=>{const u=o.stopCount>0?o.totalDowntime/o.stopCount:0;return{date:o.date,mttr:Math.round(u*100)/100,stopCount:o.stopCount,totalDowntime:Math.round(o.totalDowntime*100)/100}});return f.sort((o,u)=>new Date(o.date)-new Date(u.date)),f}catch(s){return console.error("❌ Error calculating MTTR calendar data:",s),[]}},br=(a=[])=>{try{if(console.log("🔧 calculateDowntimeParetoData called with:",{dataType:typeof a,isArray:Array.isArray(a),dataLength:(a==null?void 0:a.length)||0,sampleData:(a==null?void 0:a.slice(0,2))||[]}),!a||a.length===0)return console.log("❌ No stops data provided to calculateDowntimeParetoData"),[];const s=n=>{if(!n||typeof n!="string")return console.log("❌ Invalid date string:",n),null;try{const c=n.trim();let d=c.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(d){const[h,g,y,E,b,A,D]=d,x=new Date(parseInt(E),parseInt(y)-1,parseInt(g),parseInt(b),parseInt(A),parseInt(D));if(!isNaN(x.getTime()))return console.log("✅ Parsed date format 1:",c,"->",x),x}if(d=c.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),d){const[h,g,y,E,b,A,D]=d,x=new Date(parseInt(g),parseInt(A)-1,parseInt(D),parseInt(y),parseInt(E),parseInt(b));if(!isNaN(x.getTime()))return console.log("✅ Parsed date format 2:",c,"->",x),x}const m=new Date(c);return isNaN(m.getTime())?(console.log("❌ Could not parse date format:",c),null):(console.log("✅ Parsed ISO date:",c,"->",m),m)}catch(c){return console.log("❌ Date parsing error:",c,"for date:",n),null}},p={};let f=0,o=0;a.forEach((n,c)=>{console.log(`🔍 Processing stop ${c+1}:`,{Machine_Name:n.Machine_Name,Code_Stop:n.Code_Stop,Type_Arret:n.Type_Arret||"N/A",Debut_Stop:n.Debut_Stop,Fin_Stop_Time:n.Fin_Stop_Time,Fin_Stop:n.Fin_Stop});const d=s(n.Debut_Stop||n.debut_stop||n.startTime),m=s(n.Fin_Stop_Time||n.fin_stop_time||n.endTime||n.Fin_Stop);let h=n.Code_Stop||n.code_stop||n.stopCode||n.reason||n.cause;if((!h||h===null||h==="null"||h==="")&&(h=n.Type_Arret||n.type_arret),(!h||h===null||h==="null"||h==="")&&(h="Arrêt non déclaré"),console.log(`🔍 Stop reason determined: "${h}" from Code_Stop: "${n.Code_Stop}", Type_Arret: "${n.Type_Arret||"N/A"}"`),!d||!m){o++,console.log(`❌ Failed to parse dates for stop ${c+1}:`,{startDateString:n.Debut_Stop||n.debut_stop||n.startTime,endDateString:n.Fin_Stop_Time||n.fin_stop_time||n.endTime||n.Fin_Stop,parsedStart:d,parsedEnd:m});return}const g=(m-d)/(1e3*60);console.log(`✅ Calculated downtime for stop ${c+1}:`,{reason:h,startDate:d.toISOString(),endDate:m.toISOString(),downtimeMinutes:g}),g>0&&(f++,p[h]||(p[h]={reason:h,totalDowntime:0,stopCount:0}),p[h].totalDowntime+=g,p[h].stopCount++)}),console.log("📊 Parsing summary:",{totalStops:a.length,successfulParsing:f,failedParsing:o,uniqueReasons:Object.keys(p).length,downtimeByReason:p});const u=Object.values(p).sort((n,c)=>c.totalDowntime-n.totalDowntime),r=u.reduce((n,c)=>n+c.totalDowntime,0);let t=0;const l=u.map(n=>{t+=n.totalDowntime;const c=r>0?n.totalDowntime/r*100:0,d=r>0?t/r*100:0;return{reason:n.reason,value:Math.round(n.totalDowntime*100)/100,totalDowntime:Math.round(n.totalDowntime*100)/100,stopCount:n.stopCount,percentage:Math.round(c*100)/100,cumulativePercentage:Math.round(d*100)/100}});return console.log("🎯 Final Pareto data:",{totalDowntime:r,paretoDataLength:l.length,paretoData:l}),l}catch(s){return console.error("❌ Error calculating downtime Pareto data:",s),[]}},je=a=>{if(!a)return null;try{const s=String(a).trim();if(s.includes("/")){const f=s.split(" "),o=f[0],u=f[1]||"00:00:00",[r,t,l]=o.split("/");if(r&&t&&l&&r.length<=2&&t.length<=2&&l.length===4){const n=r.padStart(2,"0"),c=t.padStart(2,"0"),d=u.split(":"),m=parseInt(d[0])||0,h=parseInt(d[1])||0,g=parseInt(d[2])||0,y=new Date(parseInt(l),parseInt(t)-1,parseInt(r),m,h,g);if(!isNaN(y.getTime()))return y}}if(s.includes("-")&&s.includes(" ")){const f=s.indexOf(" "),o=s.substring(0,f),u=s.substring(f+1);if(u.includes("-")){const r=u.lastIndexOf("-"),t=u.substring(0,r),l=u.substring(r+1);if(l.includes("-")){const[n,c]=l.split("-");if(o&&n&&c&&t){const d=t.split(":"),m=parseInt(d[0])||0,h=parseInt(d[1])||0,g=parseInt(d[2])||0,y=new Date(parseInt(o),parseInt(n)-1,parseInt(c),m,h,g);if(!isNaN(y.getTime()))return y}}}}const p=new Date(s);return isNaN(p.getTime())?null:p}catch(s){return console.warn("Date parsing error:",s,"for date:",a),null}},xr=(a,s,p,f)=>{const o=T.useRef(!1),u=T.useRef(!0);T.useRef(Date.now());const r=T.useCallback(async(n=!1)=>{var d,m,h,g;if(o.current&&!n){console.log("⏸️ Fetch already in progress, skipping...");return}if(!u.current){console.log("⏸️ Component unmounted, skipping fetch");return}o.current=!0,console.log("🚀 Starting queued data fetch with state:",{selectedMachineModel:s.selectedMachineModel,selectedMachine:s.selectedMachine,selectedDate:s.selectedDate,dateRangeType:s.dateRangeType,forceRefresh:n}),s.selectedMachineModel&&s.selectedMachine&&s.selectedDate&&p(y=>({...y,complexFilterLoading:!0}));try{const y={model:s.selectedMachineModel||null,machine:s.selectedMachine||null,date:s.selectedDate?typeof s.selectedDate=="string"?s.selectedDate:s.selectedDate.format("YYYY-MM-DD"):null,startDate:s.selectedDate?typeof s.selectedDate=="string"?s.selectedDate:s.selectedDate.clone().startOf(s.dateRangeType).format("YYYY-MM-DD"):null,endDate:s.selectedDate?typeof s.selectedDate=="string"?s.selectedDate:s.selectedDate.clone().endOf(s.dateRangeType).format("YYYY-MM-DD"):null,dateRangeType:s.dateRangeType||"month"};console.log("🔍 Built filters for GraphQL queries:",{filters:y,selectedDateType:typeof s.selectedDate,selectedDateValue:s.selectedDate,selectedMachineModel:s.selectedMachineModel,selectedMachine:s.selectedMachine,dateRangeType:s.dateRangeType}),p(R=>({...R,loading:!0,essentialLoading:!0}));const E=await a.getEssentialData(y);if(!u.current)return;if(E.sidecards){const R=[{title:"Total Arrêts",value:E.sidecards.Arret_Totale||0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:E.sidecards.Arret_Totale_nondeclare||0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}];p(M=>({...M,arretStats:R,totalStops:E.sidecards.Arret_Totale||0,undeclaredStops:E.sidecards.Arret_Totale_nondeclare||0,essentialLoading:!1}))}if(await new Promise(R=>setTimeout(R,100)),y.machine){const R=await a.getPerformanceData(y);if(!u.current)return;p(M=>({...M,mttr:R.performance.mttr,mtbf:R.performance.mtbf,doper:R.performance.doper,showPerformanceMetrics:!0}))}else p(R=>({...R,mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1}));await new Promise(R=>setTimeout(R,200)),p(R=>({...R,detailedLoading:!0}));const b=await a.getChartData(y);if(console.log("🔍 Raw backend chart data:",b),console.log("🔍 Raw machine comparison data:",b.machineComparison),!u.current)return;if(b.topStops){console.log("🔍 Processing topStops data:",b.topStops);const R=b.topStops.reduce((v,B)=>v+B.count,0),M=b.topStops.map(v=>({...v,percentage:R>0?(v.count/R*100).toFixed(1):0}));console.log("📊 topStopsWithPercentage:",M);const w=(b.machineComparison||[]).map(v=>{const B=v.totalStops||v.stops||v.incidents||0,O=v.totalDuration||v.duration||0;return{Machine_Name:v.Machine_Name,machine:v.Machine_Name,name:v.Machine_Name,stops:B,totalStops:B,totalDuration:O,avgDuration:B>0?(O/B).toFixed(1):0}});console.log("🎯 Formatted machine comparison data for charts:",w);const S=M.map(v=>({reason:v.stopName||v.name||"Non défini",count:v.count||0,name:v.stopName||v.name||"Non défini",value:v.count||0,percentage:v.percentage,stopName:v.stopName||v.name||"Non défini"}));console.log("🎯 Formatted stopReasons for chart:",S),p(v=>({...v,topStopsData:M,machineComparison:w,stopReasons:S,chartData:[],durationTrend:M,disponibiliteTrendData:[],downtimeParetoData:M,mttrCalendarData:[],disponibiliteByMachineData:w}))}await new Promise(R=>setTimeout(R,300));const A=await a.getTableData(y);if(!u.current)return;p(R=>({...R,stopsData:A.stopsData}));const D=((d=A.stopsData)==null?void 0:d.filter(R=>{const M=R.Machine_Name||R.machineName;if(s.selectedMachine&&M!==s.selectedMachine)return!1;const w=R.Date_Insert||R.startTime;if(s.selectedDate&&w){let S;w.includes("T")&&w.includes("Z")?S=new Date(w):S=je(w);const v=new Date(s.selectedDate);if(console.log("🔍 Date filtering:",{stopDateString:w,parsedStopDate:S,filterDate:v,dateRangeType:s.dateRangeType}),!S||isNaN(S.getTime()))return console.warn("❌ Failed to parse stop date:",w),!1;if(s.dateRangeType==="day"){const B=S.toDateString()===v.toDateString();return console.log("📅 Day filter result:",B),B}else if(s.dateRangeType==="week"){const B=new Date(v);B.setDate(v.getDate()-v.getDay());const O=new Date(B);O.setDate(B.getDate()+6);const P=S>=B&&S<=O;return console.log("📅 Week filter result:",P,{weekStart:B.toDateString(),weekEnd:O.toDateString(),stopDate:S.toDateString()}),P}else if(s.dateRangeType==="month"){const B=S.getMonth()===v.getMonth()&&S.getFullYear()===v.getFullYear();return console.log("📅 Month filter result:",B,{stopMonth:S.getMonth(),stopYear:S.getFullYear(),filterMonth:v.getMonth(),filterYear:v.getFullYear()}),B}}return!0}))||[],x=D.length>0?D:A.stopsData||[];p(R=>({...R,filteredStopsData:x}));const _={};console.log("🔧 Starting evolution data generation with FILTERED data:",{finalFilteredStopsDataLength:(x==null?void 0:x.length)||0,totalStopsDataLength:((m=A.stopsData)==null?void 0:m.length)||0,sampleFilteredStops:(x==null?void 0:x.slice(0,3))||[],sampleFilteredDates:(x==null?void 0:x.slice(0,5).map(R=>R.Date_Insert))||[],currentFilters:{selectedMachine:s.selectedMachine,selectedDate:s.selectedDate,dateRangeType:s.dateRangeType}}),x.forEach(R=>{const M=R.Date_Insert||R.startTime;if(M&&typeof M=="string"){let w,S=M.toString();if(console.log("🔍 Processing date:",S),M.includes("T")&&M.includes("Z")?w=new Date(M):w=je(M),w&&!isNaN(w.getTime())){const v=w.toISOString().split("T")[0];_[v]||(_[v]={date:v,stops:0,duration:0}),_[v].stops++;const B=R.duration_minutes||R.duration||0;B&&B>0&&(_[v].duration+=parseFloat(B))}else console.warn("❌ Invalid date format, skipping:",M)}}),console.log("🔧 Daily stats generated:",_);let Y=Object.values(_).sort((R,M)=>new Date(R.date)-new Date(M.date));if(s.selectedDate&&s.dateRangeType)if(console.log("🎯 Using date filter for evolution chart:",{selectedDate:s.selectedDate,dateRangeType:s.dateRangeType}),s.dateRangeType==="month"){const R=new Date(s.selectedDate),M=R.getMonth(),w=R.getFullYear();Y=Y.filter(S=>{const v=new Date(S.date);return v.getMonth()===M&&v.getFullYear()===w})}else if(s.dateRangeType==="week"){const R=new Date(s.selectedDate),M=new Date(R);M.setDate(R.getDate()-R.getDay());const w=new Date(M);w.setDate(M.getDate()+6),Y=Y.filter(S=>{const v=new Date(S.date);return v>=M&&v<=w})}else{const R=new Date(s.selectedDate),M=new Date(R);M.setDate(R.getDate()-3);const w=new Date(R);w.setDate(R.getDate()+3),Y=Y.filter(S=>{const v=new Date(S.date);return v>=M&&v<=w})}else console.log("📊 No date filter active - showing all available data for selected model");Y=Y.map(R=>{console.log("🔄 Formatting displayDate for:",R.date);let M,w=!1,S;if(R.date&&typeof R.date=="string")if(R.date.match(/^\d{4}-\d{2}-\d{2}$/))M=new Date(R.date),w=!isNaN(M.getTime());else{const v=R.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(v){const[B,O,P,z]=v,G=`${O}-${P.padStart(2,"0")}-${z.padStart(2,"0")}`;M=new Date(G),w=!isNaN(M.getTime()),console.log("🔧 Re-parsed date:",R.date,"->",G,"Valid:",w)}}if(w&&M)S=M.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"}),console.log("✅ Generated displayDate:",S);else{const v=R.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(v){const[B,O,P,z]=v;S=`${z.padStart(2,"0")}/${P.padStart(2,"0")}`,console.log("🔧 Fallback displayDate:",S)}else S=R.date.slice(0,10),console.log("⚠️ Using raw fallback displayDate:",S)}return{...R,displayDate:S}}),console.log("📊 Generated evolution data:",{dailyStatsKeys:Object.keys(_),evolutionDataLength:Y.length,evolutionData:Y.slice(0,3),fullEvolutionData:Y});let N=Y;if(Y.length===0)if(console.log("⚠️ No evolution data found!"),console.log("🔍 Debug info for empty evolution data:",{finalFilteredStopsDataLength:(x==null?void 0:x.length)||0,dailyStatsCount:Object.keys(_).length,hasSelectedDate:!!s.selectedDate,hasSelectedMachine:!!s.selectedMachine,dateRangeType:s.dateRangeType,sampleFilteredStops:(x==null?void 0:x.slice(0,2))||[]}),(x==null?void 0:x.length)>0){console.log("🔧 Creating basic evolution data from available stops");const R=new Date;N=Array.from({length:7},(M,w)=>{const S=new Date(R);S.setDate(R.getDate()-(6-w));const v=x.filter(B=>{const O=B.Date_Insert||B.startTime;if(O){let P;return O.includes("T")&&O.includes("Z")?P=new Date(O):P=je(O),P&&!isNaN(P.getTime())&&P.toDateString()===S.toDateString()}return!1});return{date:S.toISOString().split("T")[0],displayDate:S.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"}),stops:v.length,duration:v.reduce((B,O)=>{const P=O.duration_minutes||O.duration||0;return B+parseFloat(P)},0)}}),console.log("🎲 Generated fallback evolution data:",N)}else N=[];else console.log("✅ Using real evolution data:",N.length,"items");p(R=>({...R,filteredStopsData:x,chartData:N}));const k=N.map(R=>({date:R.date,displayDate:R.displayDate,duration:R.duration||0,hour:new Date(R.date).getHours()||0,avgDuration:R.duration||0}));if(console.log("📊 Generated duration trend data:",k.length,"items"),p(R=>({...R,durationTrend:k})),((h=A.stopsData)==null?void 0:h.length)>0){const R={};A.stopsData.forEach(w=>{const S=w.Regleur_Prenom||"Non assigné";R[S]=(R[S]||0)+1});const M=Object.entries(R).map(([w,S])=>({operator:w,interventions:S}));p(w=>({...w,operatorStats:M}))}let C=[],I=[],F=[];(x==null?void 0:x.length)>0&&(console.log("🔧 Calculating advanced analytics:",{selectedMachine:s.selectedMachine,hasData:!!(x!=null&&x.length)}),console.log("🔍 Using FILTERED data for advanced analytics:",{totalStopsData:((g=A.stopsData)==null?void 0:g.length)||0,finalFilteredStopsData:(x==null?void 0:x.length)||0,selectedMachine:s.selectedMachine,selectedDate:s.selectedDate,dateRangeType:s.dateRangeType,sampleFilteredStops:(x==null?void 0:x.slice(0,2))||[]}),C=yr(x,s.dateRangeType),I=Er(x),F=br(x),console.log("🔧 Advanced analytics calculated with FILTERED data:",{disponibiliteTrendData:C.length,mttrCalendarData:I.length,downtimeParetoData:F.length,downtimeParetoSample:F.slice(0,3),downtimeParetoDataFull:F})),p(R=>({...R,loading:!1,detailedLoading:!1,complexFilterLoading:!1,disponibiliteTrendData:C,mttrCalendarData:I,downtimeParetoData:F,arretsByRange:A.stopsData||[],sidebarStats:R.arretStats}))}catch(y){if(console.error("❌ Error in queued data fetch:",y),!u.current)return;p(E=>({...E,loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:y.message||"Failed to fetch data",arretStats:[{title:"Total Arrêts",value:0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}]}))}finally{o.current=!1}},[s.selectedMachineModel,s.selectedMachine,s.selectedDate,s.dateRangeType,a,p]),t=T.useCallback(async()=>{try{const n=await a.getMachineModels(),c=await a.getMachineNames();if(u.current){const d=c.map(m=>{var y;const h=m.Machine_Name;let g="";return h.startsWith("IPSO")?g="IPSO":h.startsWith("IPS")?g="IPS":h.startsWith("CCM")?g="CCM":g=((y=h.match(/^[A-Za-z]+/))==null?void 0:y[0])||"UNKNOWN",{name:h,model:g}});p(m=>({...m,machineModels:n,machineNames:d,selectedMachineModel:"IPS"}))}}catch(n){console.error("❌ Error initializing machine data:",n),u.current&&p(c=>({...c,error:n.message}))}},[a,p]),l=T.useCallback(n=>{u.current=n},[]);return{fetchDataInQueue:r,initializeMachineData:t,setMounted:l,isMounted:u.current,pendingFetch:o.current}},Rr=(a,s,p,f)=>{const o=T.useCallback(m=>{s(h=>({...h,dateRangeType:m}))},[s]),u=T.useCallback(m=>{s(h=>({...h,selectedDate:m,dateFilterActive:m!==null}))},[s]),r=T.useCallback(m=>{s(h=>({...h,selectedMachineModel:m,selectedMachine:""}))},[s]),t=T.useCallback(m=>{s(h=>({...h,selectedMachine:m}))},[s]),l=T.useCallback(()=>{var m;s(h=>({...h,selectedMachineModel:"IPS",selectedMachine:"",selectedDate:null,dateRangeType:"month",dateFilterActive:!1,dateRangeDescription:"",arretStats:[],topStopsData:[],arretsByRange:[],stopsData:[],durationTrend:[],mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1})),(m=f==null?void 0:f.stopSkeletonLoading)==null||m.call(f),Ue.success("Filters reset successfully")},[s,f]),n=T.useCallback(async()=>{var m,h,g;try{(m=f==null?void 0:f.startSkeletonLoading)==null||m.call(f,["dataRefresh"]),await p.fetchDataInQueue(!0),(h=f==null?void 0:f.stopSkeletonLoading)==null||h.call(f,["dataRefresh"]),Ue.success("Data refreshed successfully")}catch(y){console.error("❌ Error refreshing data:",y),(g=f==null?void 0:f.stopSkeletonLoading)==null||g.call(f,["dataRefresh"]),Ue.error("Failed to refresh data")}},[p,f]),c=T.useCallback(()=>{s(m=>({...m,selectedDate:null,dateFilterActive:!1,dateRangeDescription:""}))},[s]),d=T.useCallback(()=>{s(m=>({...m,selectedMachineModel:"",selectedMachine:""}))},[s]);return{handleDateRangeTypeChange:o,handleDateChange:u,handleMachineModelChange:r,handleMachineChange:t,resetFilters:l,handleRefresh:n,resetDateFilter:c,handleResetMachineSelection:d}},Ar=a=>({async getEssentialData(p){const f=await a(`
        query($filters: StopFilterInput) {
          getStopSidecards(filters: $filters) {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `,{filters:p});return{sidecards:f==null?void 0:f.getStopSidecards,priority:1,loadingState:"essentialLoading"}},async getPerformanceData(p){const f=await a(`
        query($filters: EnhancedFilterInput) {
          enhancedGetAllMachineStops(filters: $filters) {
            stops {
              startTime
              endTime
              duration
              machineName
              stopCode
            }
            total
            dataSource
          }
        }
      `,{filters:p}),o=f==null?void 0:f.enhancedGetAllMachineStops,u=(o==null?void 0:o.stops)||[];let r=0,t=0,l=0;if(u.length>0){const n=u.reduce((g,y)=>{if(y.duration&&y.duration>0)return g+parseFloat(y.duration);if(y.startTime&&y.endTime)try{const E=new Date(y.startTime),b=new Date(y.endTime);if(!isNaN(E.getTime())&&!isNaN(b.getTime())){const A=b-E,D=Math.max(0,Math.floor(A/(1e3*60)));return g+D}}catch(E){console.warn("Error calculating duration for stop:",y,E)}return g},0);r=u.length>0?n/u.length:0;const c=u.length>0?30:1,d=u.length/c;t=d>0?1440/d:0;const m=n,h=c*24*60;l=h>0?(h-m)/h*100:0,r=Math.max(0,Math.min(r,1440)),t=Math.max(0,Math.min(t,10080)),l=Math.max(0,Math.min(l,100))}return{performance:{mttr:Number(r.toFixed(1)),mtbf:Number(t.toFixed(1)),doper:Number(l.toFixed(1))},priority:2,loadingState:"essentialLoading"}},async getChartData(p){var r,t;const[f,o,u]=await Promise.all([a(`
          query($filters: EnhancedFilterInput) {
            enhancedGetTop5Stops(filters: $filters) {
              reasons {
                reason
                count
                percentage
                totalDuration
              }
              dataSource
            }
          }
        `,{filters:p}),a(`
          query($filters: EnhancedFilterInput) {
            enhancedGetMachineStopComparison(filters: $filters) {
              machines {
                machineName
                totalStops
                totalDuration
              }
              dataSource
            }
          }
        `,{filters:p}),a(`
          query($filters: EnhancedFilterInput) {
            getDashboardStats(filters: $filters) {
              totalStops
              totalDuration
              averageDuration
              uniqueMachines
              dataSource
            }
          }
        `,{filters:p})]);return{topStops:(((r=f==null?void 0:f.enhancedGetTop5Stops)==null?void 0:r.reasons)||[]).map(l=>({...l,stopName:l.reason||l.stopName,name:l.reason||l.name,count:l.count||0,percentage:l.percentage||0})),machineComparison:(((t=o==null?void 0:o.enhancedGetMachineStopComparison)==null?void 0:t.machines)||[]).map(l=>({...l,Machine_Name:l.machineName||l.Machine_Name,stops:l.totalStops||l.stops,totalStops:l.totalStops||0,totalDuration:l.totalDuration||0})),dashboardStats:(u==null?void 0:u.getDashboardStats)||{},priority:3,loadingState:"detailedLoading"}},async getTableData(p){var u,r,t,l,n,c,d,m;const f=await a(`
        query($filters: EnhancedFilterInput) {
          enhancedGetAllMachineStops(filters: $filters) {
            stops {
              id
              machineId
              machineName
              stopReason
              stopCode
              startTime
              endTime
              duration
              operator
              description
              category
              severity
            }
            total
            dataSource
          }
        }
      `,{filters:p}),o=(((u=f==null?void 0:f.enhancedGetAllMachineStops)==null?void 0:u.stops)||[]).map(h=>({...h,Machine_Name:h.machineName||h.Machine_Name,Debut_Stop:h.startTime||h.Debut_Stop,Fin_Stop_Time:h.endTime||h.Fin_Stop_Time,duration_minutes:h.duration||h.duration_minutes,Date_Insert:h.startTime||h.Date_Insert,Stop_Name:h.stopReason||h.stopCode||h.Stop_Name||"Non défini"}));return console.log("🔄 GraphQL Interface - Transformed table data:",{originalCount:((t=(r=f==null?void 0:f.enhancedGetAllMachineStops)==null?void 0:r.stops)==null?void 0:t.length)||0,transformedCount:o.length,sampleOriginal:((n=(l=f==null?void 0:f.enhancedGetAllMachineStops)==null?void 0:l.stops)==null?void 0:n[0])||{},sampleTransformed:o[0]||{},dataSource:(c=f==null?void 0:f.enhancedGetAllMachineStops)==null?void 0:c.dataSource}),{stopsData:o,total:((d=f==null?void 0:f.enhancedGetAllMachineStops)==null?void 0:d.total)||0,dataSource:(m=f==null?void 0:f.enhancedGetAllMachineStops)==null?void 0:m.dataSource,priority:4,loadingState:"detailedLoading"}},async getMachineModels(){const p=await a(`
        query {
          getStopMachineModels {
            model
          }
        }
      `);return(p==null?void 0:p.getStopMachineModels)||[]},async getMachineNames(){const p=await a(`
        query {
          getStopMachineNames {
            Machine_Name
          }
        }
      `);return(p==null?void 0:p.getStopMachineNames)||[]}});W.extend(kt);W.extend(mr);const yt=T.createContext(),le=()=>{const a=T.useContext(yt);return a||(console.error("⚠️  useArretQueuedContext: Context not found!"),null)},Dr=({children:a})=>{const[s,p]=T.useState({machineModels:[],machineNames:[],selectedMachineModel:"",selectedMachine:"",filteredMachineNames:[],dateRangeType:"month",selectedDate:null,dateFilterActive:!1,dateRangeDescription:"",arretStats:[],stopsData:[],topStopsData:[],durationTrend:[],machineComparison:[],operatorStats:[],stopReasons:[],chartData:[],filteredStopsData:[],disponibiliteTrendData:[],downtimeParetoData:[],mttrCalendarData:[],disponibiliteByMachineData:[],loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:null,isChartModalVisible:!1,chartModalContent:null,chartOptions:{activeTab:"bar"},mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1,totalStops:0,undeclaredStops:0,avgDuration:0,totalDuration:0,sidebarStats:[],arretsByRange:[]}),[f,o]=T.useState(ht),u=fr(f,o),r=hr({stopsData:s.stopsData,rawChartData:s.durationTrend,selectedMachine:s.selectedMachine,selectedMachineModel:s.selectedMachineModel,selectedDate:s.selectedDate,doper:s.doper}),t=T.useRef(!0),l=T.useRef(!1),n=T.useCallback(async(y,E={})=>{const b=(()=>{if(typeof window<"u"){const x=window.location.origin;return x.includes("ngrok-free.app")||x.includes("ngrok.io")?x:"http://localhost:5000"}return"http://localhost:5000"})(),D=(await Nt.post(`${b}/api/graphql`).send({query:y,variables:E}).set("Content-Type","application/json").withCredentials().timeout(3e4).retry(2)).body;if(D.errors)throw new Error(D.errors[0].message);return D.data},[]),c=Ar(n),d=xr(c,s,p),m=Rr(s,p,d,u),h=T.useCallback((y,E)=>{if(!y)return{short:"",full:""};const b=W(y);if(E==="day")return{short:b.format("DD/MM"),full:b.format("DD/MM/YYYY")};if(E==="week"){const A=b.startOf("isoWeek"),D=b.endOf("isoWeek");return{short:`${A.format("DD/MM")} - ${D.format("DD/MM")}`,full:`Semaine du ${A.format("DD/MM/YYYY")} au ${D.format("DD/MM/YYYY")}`}}else if(E==="month")return{short:b.format("MM/YYYY"),full:b.format("MMMM YYYY")};return{short:"",full:""}},[]);T.useEffect(()=>{if(s.selectedMachineModel){const y=s.machineNames.filter(E=>E.model===s.selectedMachineModel||typeof E=="string"&&E.includes(s.selectedMachineModel));p(E=>({...E,filteredMachineNames:y})),s.selectedMachine&&!y.find(E=>(typeof E=="string"?E:E.name)===s.selectedMachine)&&p(E=>({...E,selectedMachine:""}))}else p(y=>({...y,filteredMachineNames:[]}))},[s.selectedMachineModel,s.machineNames,s.selectedMachine]),T.useEffect(()=>{(r.totalDuration||r.averageDuration||r.totalInterventions)&&p(y=>({...y,arretStats:y.arretStats.map(E=>E.title==="Durée Totale"?{...E,value:r.totalDuration}:E.title==="Durée Moyenne"?{...E,value:r.averageDuration}:E.title==="Interventions"?{...E,value:r.totalInterventions}:E)}))},[r.totalDuration,r.averageDuration,r.totalInterventions]),T.useEffect(()=>{if(l.current)return;(async()=>{try{d.setMounted(!0),await d.initializeMachineData(),l.current=!0}catch(E){console.error("❌ Error during initial load:",E),t.current&&p(b=>({...b,error:E.message}))}})()},[]),T.useEffect(()=>{console.log("🎯 Data fetch effect triggered:",{initialLoadComplete:l.current,selectedMachineModel:s.selectedMachineModel,selectedMachine:s.selectedMachine,selectedDate:s.selectedDate,selectedDateType:typeof s.selectedDate,selectedDateFormatted:s.selectedDate?typeof s.selectedDate=="string"?s.selectedDate:s.selectedDate.format("YYYY-MM-DD"):null,dateRangeType:s.dateRangeType,dateFilterActive:s.dateFilterActive}),l.current&&s.selectedMachineModel?(console.log("✅ Triggering data fetch with model:",s.selectedMachineModel),d.fetchDataInQueue()):console.log("⏸️ Data fetch skipped - waiting for initialization or machine model")},[s.selectedMachineModel,s.selectedMachine,s.selectedDate,s.dateRangeType]),T.useEffect(()=>()=>{t.current=!1,d.setMounted(!1)},[]);const g={...s,computedValues:r,formatDateRange:h,...m,refreshData:()=>d.fetchDataInQueue(!0),skeletonManager:u,showChartModal:y=>{p(E=>({...E,isChartModalVisible:!0,chartModalContent:y}))},hideChartModal:()=>{p(y=>({...y,isChartModalVisible:!1,chartModalContent:null}))},openChartModal:y=>{p(E=>({...E,isChartModalVisible:!0,chartModalContent:y}))},setChartOptions:y=>{p(E=>({...E,chartOptions:typeof y=="function"?y(E.chartOptions):y}))}};return e.createElement(yt.Provider,{value:g},a)},Et=(a=0,s=0)=>{const[p,f]=T.useState(a===0),[o,u]=T.useState(!1),r=T.useRef(null),t=T.useRef(null);return T.useEffect(()=>{if(t.current&&clearTimeout(t.current),a===0){f(!0);return}const l=s+a*200;return t.current=setTimeout(()=>{f(!0)},l),()=>{t.current&&clearTimeout(t.current)}},[a,s]),T.useEffect(()=>{if(!p||!r.current)return;const l=new IntersectionObserver(([n])=>{n.isIntersecting&&(u(!0),l.disconnect())},{threshold:.1,rootMargin:"50px"});return l.observe(r.current),()=>l.disconnect()},[p]),{shouldRender:p,isVisible:o,elementRef:r}},Cr={card:{avatar:!0,paragraph:{rows:2},title:!0},chart:{avatar:!1,paragraph:{rows:6},title:!0},table:{avatar:!1,paragraph:{rows:8},title:!1},stats:{avatar:!0,paragraph:{rows:1},title:!1},performance:{avatar:!1,paragraph:{rows:3},title:!0}},Sr=T.memo(({children:a,priority:s=1,delay:p=0,loading:f=!1,skeletonType:o="card",skeletonProps:u={},fallback:r=null,height:t=200,className:l="",showCard:n=!1,title:c=null})=>{const{shouldRender:d,isVisible:m,elementRef:h}=Et(s,p),g={...Cr[o],...u},y=()=>{const E=e.createElement("div",{style:{padding:n?"0":"16px"}},e.createElement(Ve,{active:!0,...g}),o==="chart"&&e.createElement("div",{style:{marginTop:"16px",height:"200px",background:"linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",backgroundSize:"200% 100%",animation:"shimmer 1.5s infinite",borderRadius:"4px"}}),o==="stats"&&e.createElement("div",{style:{marginTop:"8px",display:"flex",gap:"8px"}},[...Array(3)].map((b,A)=>e.createElement("div",{key:A,style:{width:"60px",height:"20px",background:"linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",backgroundSize:"200% 100%",animation:"shimmer 1.5s infinite",borderRadius:"4px",animationDelay:`${A*.2}s`}}))));return n?e.createElement(ne,{title:c,style:{minHeight:t},className:l},E):E};return f||!d?e.createElement("div",{ref:h,className:l,style:{minHeight:t}},r||y(),e.createElement("style",{jsx:!0},`
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `)):m?e.createElement("div",{ref:h,className:l},a):e.createElement("div",{ref:h,className:l,style:{minHeight:t}},r||y(),e.createElement("style",{jsx:!0},`
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
        `))});Sr.displayName="LazyComponentWrapper";const me=({children:a,priority:s=1,delay:p=0,loadingType:f="skeleton",height:o=200,className:u="",title:r="Loading..."})=>{const{shouldRender:t,isVisible:l,elementRef:n}=Et(s,p);return t?l?e.createElement("div",{ref:n,className:`lazy-component-rendered ${u}`},a):e.createElement("div",{ref:n,className:`lazy-component-loading ${u}`,style:{height:o,minHeight:o}},f==="skeleton"?e.createElement(Ve,{active:!0,paragraph:{rows:3}}):e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"}},e.createElement(K,{tip:"Rendering..."}))):e.createElement("div",{ref:n,className:`lazy-component-placeholder ${u}`,style:{height:o,minHeight:o}},f==="skeleton"?e.createElement(Ve,{active:!0,paragraph:{rows:4}}):e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"}},e.createElement(K,{size:"large",tip:r})))},{Title:Tr}=oe,_r=()=>{const{handleRefresh:a,setIsSearchModalVisible:s,exportToExcel:p,loading:f,error:o,essentialLoading:u,detailedLoading:r,complexFilterLoading:t,graphQL:l,dataManager:n}=le()||{};return T.useEffect(()=>{if(l&&l.getCacheStats){const c=setInterval(()=>{const d=l.getCacheStats();d&&console.log("📊 ArretHeader: GraphQL performance metrics",d)},6e4);return()=>clearInterval(c)}},[l]),e.createElement(e.Fragment,null,e.createElement(V,{justify:"space-between",align:"middle",style:{marginBottom:"24px"}},e.createElement(H,null,e.createElement(Tr,{level:2,style:{margin:0,color:i.PRIMARY_BLUE}},"🚨 Tableau de Bord des Arrêts de Machines",!f&&!o&&e.createElement(dt,{count:e.createElement(be,{style:{color:i.SECONDARY_BLUE}}),offset:[5,-3],title:"Connected to optimized GraphQL backend"}))),e.createElement(H,null,e.createElement(j,null,e.createElement(U,{icon:e.createElement(Ft,null),onClick:()=>s&&s(!0),disabled:f,style:{borderColor:i.PRIMARY_BLUE,color:i.PRIMARY_BLUE}},"Recherche Globale"),e.createElement(U,{icon:e.createElement(Bt,null),onClick:()=>p&&p(),type:"primary",disabled:f,style:{backgroundColor:i.PRIMARY_BLUE,borderColor:i.PRIMARY_BLUE}},"Exporter Excel"),e.createElement(U,{icon:e.createElement(Ze,null),onClick:()=>a&&a(),type:"default",loading:f||t,style:{borderColor:i.LIGHT_GRAY,color:i.DARK_GRAY}},f||t?"Chargement...":"Actualiser"),l&&l.getCacheStats&&e.createElement(U,{icon:e.createElement(Ot,null),onClick:()=>{const c=l.getCacheStats();console.log("📊 Cache Stats:",c),alert(`Cache Hits: ${c.cacheHits}
Cache Misses: ${c.cacheMisses}
Avg Response: ${c.avgResponseTime.toFixed(2)}ms`)},type:"text",size:"small",title:"Show cache statistics",style:{color:i.LIGHT_GRAY}})))),o&&e.createElement(ut,{message:"Erreur de chargement",description:e.createElement("div",null,e.createElement("p",null,o),e.createElement(U,{type:"primary",size:"small",icon:e.createElement(Ze,null),onClick:()=>a&&a(),loading:f},"Réessayer")),type:"error",icon:e.createElement(Ie,null),showIcon:!0,closable:!0,style:{marginBottom:"16px"}}))},Mr=()=>{const a=le();if(!a)return e.createElement("div",null,"Context not available");const{loading:s=!0,essentialLoading:p=!1,dateFilterActive:f=!1,dateRangeDescription:o="",selectedDate:u,dateRangeType:r,stopsData:t=[],selectedMachine:l,selectedMachineModel:n,totalStops:c=0,undeclaredStops:d=0,computedValues:m={},operatorStats:h=[]}=a,{chartDataCalculations:g={},globalDataCalculations:y={},filteredStopsData:E=[],avgDuration:b=0,totalDuration:A=0}=m,D=c,x=d,_=g.totalDuration||y.totalDuration||A,Y=g.averageDuration||y.averageDuration||b,N=T.useMemo(()=>D>0&&x>=0?Te(x/D*100,1):"0",[D,x]),k=T.useMemo(()=>{if(!u||!f)return"";const S=v=>v.format("DD/MM/YYYY");switch(r){case"day":return S(u);case"week":const v=u.clone().startOf("isoWeek"),B=u.clone().endOf("isoWeek");return`${S(v)} - ${S(B)}`;case"month":return u.format("MMMM YYYY");default:return S(u)}},[u,r,f]),C=T.useMemo(()=>E&&E.length>=0?E.length:f&&D>0?D:0,[E,f,D]),I=T.useMemo(()=>l||(n?`Modèle ${n}`:"Toutes les machines"),[l,n]),F=T.useMemo(()=>!f||C===0?null:{title:"Arrêts Filtrés",value:ge(C),icon:e.createElement(_e,null),color:i.SECONDARY_BLUE,suffix:"arrêts",isDateFilter:!0},[f,C]),R=T.useMemo(()=>[{title:"Arrêts Totaux",value:ge(D),suffix:"",icon:e.createElement(pt,null),color:i.PRIMARY_BLUE},{title:"Arrêts Non Déclarés",value:ge(x),suffix:"",icon:e.createElement(Ht,null),color:i.PRIMARY_BLUE},{title:"Durée Totale",value:ge(Math.round(_)),suffix:"min",icon:e.createElement(X,null),color:i.PRIMARY_BLUE},{title:"Durée Moyenne",value:zt(Y,1),suffix:"min",icon:e.createElement(X,null),color:i.PRIMARY_BLUE},{title:"Interventions",value:ge((h==null?void 0:h.reduce((v,B)=>v+(B.interventions||0),0))||0),suffix:"",icon:e.createElement(Ye,null),color:i.PRIMARY_BLUE}],[D,x,_,Y,h]),M=T.useMemo(()=>{if(!F)return R;const S=[...R];return S.splice(2,0,F),S},[R,F]);e.useEffect(()=>{},[c,d,D,x,_,Y,g,N,C,f,u,l,n,h,M]);const w=()=>{const S=M.length;return S===5?{xs:24,sm:12,md:8,lg:4,xl:4}:S===6?{xs:24,sm:12,md:8,lg:4,xl:4}:{xs:24,sm:12,md:6,lg:6,xl:6}};return e.createElement(V,{gutter:[16,16],style:{marginBottom:"24px"}},M.map((S,v)=>e.createElement(H,{key:v,...w()},e.createElement(ne,{bordered:!1,hoverable:!0,style:{backgroundColor:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`,borderTop:`3px solid ${S.color||i.PRIMARY_BLUE}`,height:"100%",minHeight:"120px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)",...S.isDateFilter&&{backgroundColor:"#FFFFFF",border:`1px solid ${i.SECONDARY_BLUE}`,borderTop:`3px solid ${i.SECONDARY_BLUE}`}}},e.createElement(K,{spinning:p||s},e.createElement(Me,{title:e.createElement(j,null,S.icon&&e.isValidElement(S.icon)?e.cloneElement(S.icon,{style:{color:S.color||i.PRIMARY_BLUE,fontSize:20}}):S.icon?e.createElement("span",{style:{color:S.color||i.PRIMARY_BLUE,fontSize:20}},S.icon):null,e.createElement("span",{style:{color:i.DARK_GRAY,fontWeight:600}},S.title),(S.title==="Total Arrêts"||S.title==="Arrêts Totaux")&&f&&e.createElement(wt,{content:`Nombre total d'arrêts ${o}`,title:"Période sélectionnée"},e.createElement(ue,{style:{color:i.LIGHT_GRAY,cursor:"pointer",fontSize:14}}))),value:S.value||"0",suffix:S.suffix,valueStyle:{fontSize:24,color:S.color||i.PRIMARY_BLUE,fontWeight:700},formatter:B=>B}),S.isDateFilter&&e.createElement("div",{style:{marginTop:8}},e.createElement(re,{color:"blue",style:{marginBottom:4,backgroundColor:i.SECONDARY_BLUE,borderColor:i.SECONDARY_BLUE,color:"#FFFFFF"}},k),e.createElement("div",{style:{color:i.LIGHT_GRAY,fontSize:"12px"}},I)),S.title==="Arrêts Non Déclarés"&&e.createElement("div",{style:{marginTop:8}},e.createElement("span",{style:{color:i.LIGHT_GRAY,fontSize:"14px"}},N,"% du total"),e.createElement(Re,{percent:D>0?x/D*100:0,showInfo:!1,strokeColor:i.SECONDARY_BLUE,trailColor:"#F3F4F6",size:"small",strokeWidth:4})))))))},{Option:it}=qe,vr=({onFilterChange:a})=>{const s=le();if(!s)return e.createElement("div",null,"Context not available");const{machineModels:p=[],filteredMachineNames:f=[],selectedMachineModel:o="",selectedMachine:u="",handleMachineModelChange:r,handleMachineChange:t,dateRangeType:l="day",selectedDate:n=null,dateFilterActive:c=!1,handleDateRangeTypeChange:d,handleDateChange:m,loading:h=!1,stopsData:g=[],resetFilters:y,handleRefresh:E,complexFilterLoading:b=!1,dataManager:A}=s;T.useEffect(()=>{const x={model:o,machine:u,date:n==null?void 0:n.format("YYYY-MM-DD"),dateType:l,dateFilterActive:c,hasAllFilters:o&&u&&c,dataCount:g==null?void 0:g.length};a&&typeof a=="function"&&a(x)},[o,u,n,l,c,g==null?void 0:g.length,a,p,f]);const D=()=>l==="day"?e.createElement(We,{value:n,onChange:m,format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!0,style:{width:"100%"}}):l==="week"?e.createElement(We,{value:n,onChange:m,picker:"week",format:"[Semaine] w YYYY",placeholder:"Sélectionner une semaine",allowClear:!0,style:{width:"100%"}}):l==="month"?e.createElement(We,{value:n,onChange:m,picker:"month",format:"MMMM YYYY",placeholder:"Sélectionner un mois",allowClear:!0,style:{width:"100%"}}):null;return e.createElement(j,{direction:"vertical",size:"middle",style:{width:"100%"}},e.createElement(V,{gutter:[16,16],align:"middle"},e.createElement(H,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:i.LIGHT_GRAY}},"Modèle de Machine"),e.createElement(qe,{placeholder:"Sélectionner un modèle",value:o,onChange:r,style:{width:"100%"},allowClear:!0,showSearch:!0,filterOption:(x,_)=>_.children.toLowerCase().includes(x.toLowerCase())},p.map(x=>{const _=typeof x=="object"?x.model:x;return e.createElement(it,{key:_,value:_},_)})))),e.createElement(H,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:i.LIGHT_GRAY}},"Machine Spécifique"),e.createElement(qe,{placeholder:"Sélectionner une machine",value:u,onChange:x=>{typeof t=="function"?t(x):console.error("handleMachineChange is not a function!",typeof t)},style:{width:"100%"},allowClear:!0,showSearch:!0,disabled:!o&&f.length===0,filterOption:(x,_)=>_.children.toLowerCase().includes(x.toLowerCase())},f.map(x=>{const _=x.name;return e.createElement(it,{key:_||`machine-${Math.random()}`,value:_},_||"Unknown Machine")})))),e.createElement(H,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:i.LIGHT_GRAY}},"Type de Période"),e.createElement(Gt,{value:l,onChange:d,options:[{label:"Jour",value:"day",icon:e.createElement(_e,null)},{label:"Semaine",value:"week",icon:e.createElement(_e,null)},{label:"Mois",value:"month",icon:e.createElement(_e,null)}],style:{width:"100%"}}))),e.createElement(H,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:i.LIGHT_GRAY}},"Sélection de Date"),D()))),e.createElement(V,{gutter:[16,16],align:"middle",justify:"space-between"},e.createElement(H,null,e.createElement(j,null,(o||u||c)&&e.createElement(j,{wrap:!0},o&&e.createElement(re,{color:"blue",closable:!0,onClose:()=>r("")},"Modèle: ",typeof o=="object"?o.model:o),u&&e.createElement(re,{color:"green",closable:!0,onClose:()=>t("")},"Machine: ",typeof u=="object"?u.Machine_Name:u),c&&n&&e.createElement(re,{color:"orange",closable:!0,onClose:()=>m(null)},e.createElement(X,{style:{marginRight:4}}),n.format(l==="day"?"DD/MM/YYYY":l==="week"?"[Semaine] w YYYY":"MMMM YYYY"))))),e.createElement(H,null,e.createElement(j,null,e.createElement(xe,{title:"Effacer tous les filtres"},e.createElement(U,{icon:e.createElement($t,null),onClick:y,disabled:!o&&!u&&!c},"Effacer")),e.createElement(xe,{title:"Forcer le rechargement manuel des données"},e.createElement(U,{type:"primary",icon:e.createElement(Ze,null),onClick:E,loading:h||b},h||b?"Chargement...":"Forcer Refresh"))))),e.createElement(V,null,e.createElement(H,{span:24},e.createElement(j,{wrap:!0},e.createElement(re,{icon:e.createElement(Ut,null),color:"processing"},g.length," arrêts trouvés",o&&!u&&` (modèle: ${o})`,u&&` (machine: ${u})`),(o||u||c)&&e.createElement(re,{color:"blue"},[o&&"Modèle",u&&"Machine",c&&"Date"].filter(Boolean).length," filtre(s) actif(s)"),o&&!u&&h&&e.createElement(re,{color:"processing"},e.createElement(X,{spin:!0})," Filtrage par modèle en cours..."),u&&h&&e.createElement(re,{color:"processing"},e.createElement(X,{spin:!0})," Filtrage par machine spécifique en cours..."),c&&h&&e.createElement(re,{color:"orange"},e.createElement(X,{spin:!0})," Filtrage par date en cours..."),h&&e.createElement(re,{color:"blue"},"Chargement en cours..."),b&&e.createElement(re,{color:"gold"},e.createElement(X,{spin:!0})," Traitement complexe..."),e.createElement(re,{color:"success",style:{marginLeft:"auto"}},"✓ Les changements de filtres actualisent automatiquement les données")))),s.error&&e.createElement(V,{style:{marginTop:"16px"}},e.createElement(H,{span:24},e.createElement("div",{style:{padding:"12px",backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${i.PRIMARY_BLUE}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ie,{style:{color:"#ff4d4f",fontSize:"16px",marginRight:"8px"}}),e.createElement("div",null,e.createElement("div",{style:{fontWeight:"bold",color:i.DARK_GRAY}},"Erreur de chargement des données"),e.createElement("div",{style:{fontSize:"12px",marginTop:"4px",color:i.LIGHT_GRAY}},s.error.includes&&s.error.includes("AbortError")?"La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page.":typeof s.error=="string"?s.error:"Une erreur est survenue lors du chargement des données. Veuillez réessayer."),e.createElement(j,{style:{marginTop:"8px"}},e.createElement(U,{size:"small",type:"primary",onClick:()=>{s.graphQL&&s.graphQL.invalidateCache&&s.graphQL.invalidateCache(),E()}},"Réessayer"),e.createElement(U,{size:"small",onClick:y},"Réinitialiser les filtres"))))))))},{Text:Ir}=oe,Nr=({data:a=[],loading:s=!1,title:p="Comparaison par Machine",colors:f})=>{const[o,u]=T.useState("stops"),r=ie({chartType:"bar"}),t=f||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE];if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"}},e.createElement(K,{size:"large"}),e.createElement(Ir,{type:"secondary"},"Chargement de la comparaison par machine..."));const l=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!l||l.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement($,{description:"Aucune donnée de machine disponible",style:{color:"#8c8c8c"}}));const n=l.map(g=>{const y=g.Machine_Name||g.machine||g.nom_machine||g.name||g.machineName||String(g.machine_id||g.id||"Unknown"),E=Number.parseInt(g.stops||g.totalStops||g.nombre_arrets||g.Total_Stops||g.count||g.frequency||g.incidents||0),b=Number.parseFloat(g.totalDuration||g.duree_totale||g.Total_Duration||g.duration||g.total_time||0),A=Number.parseFloat(g.avgDuration||g.duree_moyenne||g.average_duration||g.avg_time||(b>0&&E>0?b/E:0));return{machine:y,stops:E,totalDuration:b,avgDuration:A}}).filter(g=>g.machine&&g.machine!=="N/A"&&(g.stops>=0||g.totalDuration>=0)),c=n.length>0?n:[],d=({active:g,payload:y,label:E})=>g&&y&&y.length?e.createElement("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:"#262626"}},`Machine: ${E}`),y.map((b,A)=>e.createElement("p",{key:A,style:{margin:"4px 0",color:b.color}},`${b.name}: ${b.value}${b.dataKey==="totalDuration"||b.dataKey==="avgDuration"?" min":""}`))):null,m=()=>o==="stops"?e.createElement(ae,{dataKey:"stops",fill:t[0],name:"Nombre d'arrêts",radius:[4,4,0,0]}):o==="duration"?e.createElement(ae,{dataKey:"totalDuration",fill:t[1],name:"Durée totale (min)",radius:[4,4,0,0]}):null,h=(g,y,E)=>e.createElement(J,{width:"100%",height:"100%"},e.createElement(he,{data:c,margin:{top:5,right:15,left:15,bottom:35}},e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(te,{dataKey:"machine",angle:-45,textAnchor:"end",height:45,stroke:"#666",fontSize:10}),e.createElement(q,{stroke:"#666",fontSize:10}),e.createElement(Z,{content:e.createElement(d,null)}),e.createElement(ae,{dataKey:g,fill:y,name:E,radius:[4,4,0,0]})));return e.createElement("div",{style:{height:"100%",width:"100%"}},"      ",e.createElement(V,{style:{marginBottom:"12px"}},e.createElement(H,{span:24},e.createElement(j,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(U,{type:o==="stops"?"primary":"default",icon:e.createElement(we,null),onClick:()=>u("stops"),size:"small"},"Arrêts"),e.createElement(U,{type:o==="duration"?"primary":"default",icon:e.createElement(X,null),onClick:()=>u("duration"),size:"small"},"Durée"),e.createElement(U,{type:o==="both"?"primary":"default",onClick:()=>u("both"),size:"small"},"Les deux")))),o==="both"?e.createElement("div",{style:{height:"calc(100% - 70px)",display:"flex",flexDirection:"column"}},e.createElement("div",{style:{flex:"1",minHeight:"0",marginBottom:"12px"}},e.createElement("h4",{style:{textAlign:"center",margin:"0 0 6px 0",color:t[0],fontSize:"13px",fontWeight:"bold"}},"Nombre d'arrêts"),e.createElement("div",{style:{height:"calc(100% - 20px)"}},h("stops",t[0],"Nombre d'arrêts"))),e.createElement("div",{style:{flex:"1",minHeight:"0"}},e.createElement("h4",{style:{textAlign:"center",margin:"0 0 6px 0",color:t[1],fontSize:"13px",fontWeight:"bold"}},"Durée totale (min)"),e.createElement("div",{style:{height:"calc(100% - 20px)"}},h("totalDuration",t[1],"Durée totale (min)")))):e.createElement("div",{style:{height:"calc(100% - 70px)"}},e.createElement(J,{...r.responsiveContainerProps},e.createElement(he,{data:c,margin:r.margins},e.createElement(ee,{...r.gridConfig}),e.createElement(te,{dataKey:"machine",...r.axisConfig,angle:-45,textAnchor:"end",height:80,stroke:"#666"}),e.createElement(q,{...r.axisConfig,stroke:"#666"}),e.createElement(Z,{...r.tooltipConfig,content:e.createElement(d,null)}),r.displayConfig.showLegend&&e.createElement(se,{...r.legendConfig}),m()))))},wr=[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.DARK_GRAY,i.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB","#F3F4F6","#60A5FA","#1D4ED8"],Yr=T.memo(({data:a=[],loading:s,colors:p})=>{const f=ie({chartType:"pie"}),o=p||wr;if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(K,{size:"large"}));const u=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!u||u.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"}},e.createElement($,{description:"Aucune donnée d'arrêts disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const r=u.reduce((c,d)=>c+(d.count||0),0);if(r===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"}},e.createElement($,{description:"Aucun arrêt enregistré",image:$.PRESENTED_IMAGE_SIMPLE}));const t=u.map((c,d)=>({...c,percentage:c.count/r*100,color:o[d%o.length],name:c.reason||c.stopName||c.Stop_Reason||"Type non défini",count:c.count||c.frequency||0})).sort((c,d)=>d.count-c.count),l=30,n=t.filter(c=>c.percentage>=l);return e.createElement("div",{style:{height:"100%",padding:"16px",background:"transparent"}},e.createElement("div",{style:{textAlign:"center",marginBottom:"20px",fontSize:"18px",fontWeight:"600",color:i.PRIMARY_BLUE,letterSpacing:"0.3px"}},"Top 5 Causes d'Arrêts"),e.createElement("div",{style:{display:"grid",gridTemplateColumns:"1fr 350px",gap:"24px",height:"calc(100% - 60px)",alignItems:"stretch"}},e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",position:"relative",background:"#ffffff",borderRadius:"16px",padding:"20px",boxShadow:"0 4px 16px rgba(0,0,0,0.06)",border:"1px solid #f0f0f0"}},e.createElement(J,{...f.responsiveContainerProps},e.createElement(jt,null,e.createElement(Kt,{data:t,dataKey:"count",nameKey:"name",cx:"50%",cy:"50%",outerRadius:120,innerRadius:55,paddingAngle:2,stroke:"#fff",strokeWidth:2,label:f.displayConfig.showDataLabels?({name:c,percentage:d})=>`${c}: ${Te(d/100)}`:!1,labelLine:!1,...f.animationConfig},t.map((c,d)=>{const m=f.hoverEffectsConfig;return e.createElement(et,{key:`cell-${d}`,fill:c.color,style:{filter:"drop-shadow(0 1px 3px rgba(0,0,0,0.1))",cursor:m.cursor,transition:"all 0.2s ease"}})})),e.createElement(Z,{...f.tooltipConfig,contentStyle:{...f.tooltipConfig.contentStyle,backgroundColor:"#fff",border:`1px solid ${f.getPrimaryColor()}`,borderRadius:"8px",boxShadow:"0 4px 16px rgba(0,0,0,0.1)",fontSize:"12px",fontWeight:"500",color:f.getTextColor()},formatter:(c,d,m)=>[[`${ge(c)} arrêts (${Te(m.payload.percentage/100)})`,""],d]}))),e.createElement("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",textAlign:"center",backgroundColor:"#fff",borderRadius:"50%",width:"110px",height:"110px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",boxShadow:"0 4px 16px rgba(0,0,0,0.1)",border:`2px solid ${i.PRIMARY_BLUE}20`}},e.createElement("div",{style:{fontSize:"28px",fontWeight:"700",color:i.PRIMARY_BLUE,lineHeight:"1"}},r),e.createElement("div",{style:{fontSize:"12px",color:i.LIGHT_GRAY,marginTop:"2px",fontWeight:"600",letterSpacing:"0.5px"}},"TOTAL"))),e.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"16px",height:"100%"}},e.createElement("div",{style:{background:"#ffffff",borderRadius:"12px",padding:"16px",border:"1px solid #f0f0f0",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",flex:"1"}},e.createElement("h3",{style:{margin:"0 0 16px 0",fontSize:"14px",fontWeight:"600",color:i.DARK_GRAY,display:"flex",alignItems:"center",gap:"6px"}},e.createElement("div",{style:{width:"3px",height:"16px",background:`linear-gradient(135deg, ${i.PRIMARY_BLUE}, ${i.SECONDARY_BLUE})`,borderRadius:"2px"}}),"Répartition"),e.createElement("div",{style:{display:"flex",flexDirection:"column",gap:"8px",maxHeight:"320px",overflowY:"auto"}},t.map((c,d)=>e.createElement("div",{key:d,style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px",background:"#fafafa",borderRadius:"8px",border:"1px solid #f0f0f0",transition:"all 0.2s ease",cursor:"pointer"},onMouseEnter:m=>{m.target.style.background="#f0f9ff",m.target.style.borderColor=i.PRIMARY_BLUE,m.target.style.transform="translateY(-1px)",m.target.style.boxShadow=`0 2px 8px ${i.PRIMARY_BLUE}20`},onMouseLeave:m=>{m.target.style.background="#fafafa",m.target.style.borderColor="#f0f0f0",m.target.style.transform="translateY(0)",m.target.style.boxShadow="none"}},e.createElement("div",{style:{display:"flex",alignItems:"center",flex:"1"}},e.createElement("div",{style:{width:"10px",height:"10px",backgroundColor:c.color,borderRadius:"50%",marginRight:"10px",boxShadow:`0 0 0 2px ${c.color}20`,border:"1px solid #fff"}}),e.createElement("div",null,e.createElement("div",{style:{fontSize:"13px",color:i.DARK_GRAY,fontWeight:"500",marginBottom:"1px"}},c.name),e.createElement("div",{style:{fontSize:"11px",color:i.LIGHT_GRAY}},c.count," arrêts"))),e.createElement("div",{style:{fontSize:"14px",color:i.DARK_GRAY,fontWeight:"600"}},Te(c.percentage/100)))))),n.length>0&&e.createElement("div",{style:{background:"linear-gradient(135deg, #fff2f0, #ffebe8)",borderRadius:"12px",padding:"16px",border:"1px solid #ffccc7",boxShadow:"0 2px 8px rgba(245, 34, 45, 0.08)"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"}},e.createElement("div",{style:{width:"28px",height:"28px",borderRadius:"50%",background:"linear-gradient(135deg, #f5222d, #ff4d4f)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"10px",boxShadow:"0 2px 8px rgba(245, 34, 45, 0.25)"}},e.createElement(pt,{style:{color:"#fff",fontSize:"14px"}})),e.createElement("div",null,e.createElement("h4",{style:{margin:0,fontSize:"13px",fontWeight:"600",color:"#f5222d"}},"Alerte Critique"),e.createElement("p",{style:{margin:"1px 0 0 0",fontSize:"11px",color:"#8c1b1b",opacity:.8}},n.length," type(s)  30%")))))))}),{Text:kr}=oe,lt={primary:i.PRIMARY_BLUE},bt=T.memo(({data:a=[],loading:s=!1,title:p="Evolution des Arrêts",chartType:f,allowedTypes:o=["line","bar"],height:u=400,colors:r})=>{const t=Wt({fallbackType:"line",allowedTypes:o,propChartType:f}),l=r||t.colors||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY],n=t.chartType,c=T.useMemo(()=>{const g=Array.isArray(a)?a:(a==null?void 0:a.data)||[];return!Array.isArray(g)||g.length===0?[]:g.map(y=>{const E=y.Stop_Date||y.date||y.Date||y.day||y.Day||y.period,b=y.stops||y.count||y.frequency||y.total||0;return{...y,date:E,stops:Number(b)||0,originalData:y}}).filter(y=>y.date&&y.stops!==void 0)},[a]),d=({active:g,payload:y,label:E})=>{const b=t.theme||{};if(g&&y&&y.length){const D=y[0].value,x=W(E).format("DD/MM/YYYY");return e.createElement("div",{style:{backgroundColor:b.darkMode?"#1f1f1f":"#ffffff",border:`1px solid ${lt.primary}`,borderRadius:"8px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:b.darkMode?"#ffffff":"#000000"}},`Date: ${x}`),e.createElement("p",{style:{margin:0,color:lt.primary}},`Nombre d'arrêts: ${D}`))}return null};if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:u,flexDirection:"column",gap:"16px"}},e.createElement(K,{size:"large"}),e.createElement(kr,{type:"secondary"},"Chargement de l'évolution des arrêts..."));if(!c||c.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:u,width:"100%"}},e.createElement($,{description:"Aucune donnée d'évolution disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const m={data:c,margin:t.margins},h=()=>{var g,y;switch(n){case"bar":return e.createElement(he,{...m},e.createElement(ee,{...t.gridConfig}),e.createElement(te,{dataKey:"date",...t.axisConfig,tick:{fontSize:11},tickFormatter:E=>W(E).format("DD/MM"),angle:-45,textAnchor:"end",height:80}),e.createElement(q,{...t.axisConfig,tick:{fontSize:11},label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(Z,{content:e.createElement(d,null)}),((g=t.charts)==null?void 0:g.showLegend)&&e.createElement(se,{...t.legendConfig}),e.createElement(ae,{dataKey:"stops",name:"Nombre d'arrêts",...t.getBarElementConfig(l[0])}));case"line":default:return e.createElement(tt,{...m},e.createElement(ee,{...t.gridConfig}),e.createElement(te,{dataKey:"date",...t.axisConfig,tick:{fontSize:11},tickFormatter:E=>W(E).format("DD/MM"),angle:-45,textAnchor:"end",height:80}),e.createElement(q,{...t.axisConfig,tick:{fontSize:11},label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(Z,{content:e.createElement(d,null)}),((y=t.charts)==null?void 0:y.showLegend)&&e.createElement(se,{...t.legendConfig}),e.createElement(ce,{type:"monotone",dataKey:"stops",name:"Nombre d'arrêts",...t.getLineElementConfig(l[0])}))}};return e.createElement("div",{style:{height:"100%",padding:"16px",background:"transparent"}},e.createElement("div",{style:{textAlign:"center",marginBottom:"20px",fontSize:"18px",fontWeight:"600",color:i.PRIMARY_BLUE,letterSpacing:"0.3px"}},p),e.createElement("div",{style:{height:"calc(100% - 60px)",width:"100%"}},e.createElement(J,{...t.responsiveContainerProps},h())))});bt.displayName="ArretTrendChart";const Lr=T.memo(({data:a=[],loading:s,colors:p})=>{const{charts:f,theme:o}=ye(),u=ie({charts:f,theme:o}),r=p||u.colors||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY];if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(K,{size:"large"}));const t=Array.isArray(a)?a:(a==null?void 0:a.data)||[];console.log("🎯 ArretHeatmapChart - Data received:",{originalDataLength:(a==null?void 0:a.length)||0,processedDataLength:(t==null?void 0:t.length)||0,dataType:typeof a,isArray:Array.isArray(a),loading:s,sampleData:(t==null?void 0:t.slice(0,2))||[]});const n=(c=>{const d=Array.isArray(c)?c:(c==null?void 0:c.data)||[];if(!Array.isArray(d)||d.length===0)return console.log("🔍 ArretHeatmapChart - No valid data to process"),[];const m=480,h=1,g={};let y=0,E=0;for(let D=0;D<24;D++)g[D]={count:0,totalDuration:0,avgDuration:0,durations:[],outliers:[]};const b=D=>{if(!D)return null;try{const x=String(D).trim();if(x.includes("T")&&(x.includes("Z")||x.includes("+")||x.includes("-"))){const Y=new Date(x);if(!isNaN(Y.getTime()))return Y}if(x.includes("/")){const Y=x.split(" "),N=Y[0],k=Y[1]||"00:00:00",[C,I,F]=N.split("/");if(C&&I&&F&&C.length<=2&&I.length<=2&&F.length===4){const R=C.padStart(2,"0"),M=I.padStart(2,"0"),w=k.split(":"),S=parseInt(w[0])||0,v=parseInt(w[1])||0,B=parseInt(w[2])||0,O=new Date(parseInt(F),parseInt(I)-1,parseInt(C),S,v,B);if(!isNaN(O.getTime()))return O}}if(x.includes("-")&&x.includes(" ")){const Y=x.indexOf(" "),N=x.substring(0,Y),k=x.substring(Y+1);if(k.includes("-")){const C=k.lastIndexOf("-"),I=k.substring(0,C),F=k.substring(C+1);if(F.includes("-")){const[R,M]=F.split("-");if(N&&R&&M&&I){const w=I.split(":"),S=parseInt(w[0])||0,v=parseInt(w[1])||0,B=parseInt(w[2])||0,O=new Date(parseInt(N),parseInt(R)-1,parseInt(M),S,v,B);if(!isNaN(O.getTime()))return O}}}}const _=new Date(x);return isNaN(_.getTime())?null:_}catch(x){return console.warn("Date parsing error:",x,"for date:",D),null}};d.forEach(D=>{const x=D.Debut_Stop||D.debut_stop||D.startTime||D.start_time;if(x)try{const _=b(x);if(_&&!isNaN(_.getTime())){const Y=_.getHours();if(E++,g[Y]){let N=0;if(D.duration_minutes!==void 0&&D.duration_minutes!==null)N=parseFloat(D.duration_minutes);else if(D.duration!==void 0&&D.duration!==null)N=parseFloat(D.duration);else{const k=D.Fin_Stop_Time||D.fin_stop_time||D.endTime||D.end_time;if(k){const C=b(k);C&&!isNaN(C.getTime())&&(N=(C-_)/(1e3*60))}}N>0&&(N>=h&&N<=m?(g[Y].count+=1,g[Y].totalDuration+=N,g[Y].durations.push(N)):(g[Y].outliers.push(N),y++))}}}catch(_){console.warn("Error parsing time:",_)}}),Object.keys(g).forEach(D=>{const x=g[D];x.avgDuration=x.count>0?x.totalDuration/x.count:0});const A=[];for(let D=0;D<24;D++)A.push({hour:D,avgDuration:Math.round(g[D].avgDuration)});return A})(t);return!n||n.length===0||n.every(c=>c.avgDuration===0)?(console.warn("🚨 ArretHeatmapChart - No chart data available:",{chartDataLength:(n==null?void 0:n.length)||0,stopsDataLength:(t==null?void 0:t.length)||0,allZeroDurations:n==null?void 0:n.every(c=>c.avgDuration===0),sampleChartData:(n==null?void 0:n.slice(0,3))||[]}),e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"}},e.createElement($,{description:"Aucune donnée de durée par heure disponible",image:$.PRESENTED_IMAGE_SIMPLE}))):e.createElement(J,{...u.responsiveContainerProps,height:u.height||300},e.createElement(ke,{data:n,margin:u.margins},e.createElement(ee,{...u.gridConfig}),e.createElement(te,{dataKey:"hour",...u.axisConfig,label:{value:"Heure de la journée",position:"bottom",offset:0,style:{textAnchor:"middle",fill:i.LIGHT_GRAY}}}),e.createElement(q,{...u.axisConfig,label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:i.LIGHT_GRAY}}}),"        ",e.createElement(Z,{formatter:c=>{const d=typeof c=="number"?c:parseFloat(c);return[`${(isNaN(d)?0:d).toFixed(1)} min`,"Durée moyenne"]},contentStyle:{backgroundColor:"#fff",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",color:i.DARK_GRAY}}),e.createElement(Le,{type:"monotone",dataKey:"avgDuration",stroke:r[0],fill:`${r[0]}33`})))}),Pr=T.memo(({data:a=[],loading:s,colors:p})=>{console.log("🔍 ArretHorizontalBarChart received:",{dataType:typeof a,isArray:Array.isArray(a),dataLength:Array.isArray(a)?a.length:"N/A",rawData:a,firstItem:Array.isArray(a)&&a[0]?a[0]:"N/A"});const f=p||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE];if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(K,{size:"large"}));const o=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!o||o.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"}},e.createElement($,{description:"Aucune donnée de causes d'arrêt disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const u=o.map(t=>{const l=t.reason||t.stopName||t.Stop_Reason||t.name||"Non défini",n=t.count||t.frequency||t.value||0;return console.log("🔄 Processing stop reason item:",{originalItem:t,extractedReason:l,extractedCount:n}),{reason:l,count:n,duration:t.duration||0}}).sort((t,l)=>l.count-t.count).slice(0,10);console.log("📊 Final processed data for chart:",u);const r=(t,l)=>f[t%f.length];return e.createElement(J,{width:"100%",height:400},e.createElement(he,{data:u,layout:"vertical",margin:{top:20,right:30,left:150,bottom:20}},e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(te,{type:"number",tick:{fontSize:11,fill:i.LIGHT_GRAY},axisLine:{stroke:i.LIGHT_GRAY},tickLine:{stroke:i.LIGHT_GRAY}}),e.createElement(q,{type:"category",dataKey:"reason",width:140,tick:{fontSize:11,fill:i.DARK_GRAY},axisLine:{stroke:i.LIGHT_GRAY},tickLine:{stroke:i.LIGHT_GRAY},tickFormatter:t=>t.length>25?`${t.substring(0,22)}...`:t}),e.createElement(Z,{formatter:(t,l,n)=>[`${t} occurrence${t>1?"s":""}`,"Fréquence"],labelFormatter:t=>`Cause: ${t}`,contentStyle:{backgroundColor:"#fff",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px",color:i.DARK_GRAY}}),"        ",e.createElement(ae,{dataKey:"count",name:"Fréquence",barSize:24,radius:[0,6,6,0]},u.map((t,l)=>e.createElement(et,{key:`cell-${l}`,fill:r(l,u.length)})))))}),{Text:Fr}=oe,Ee={success:i.PRIMARY_BLUE};T.memo(({data:a=[],loading:s=!1,title:p="Durée Moyenne par Heure"})=>{if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"}},e.createElement(K,{size:"large"}),e.createElement(Fr,{type:"secondary"},"Chargement des données de tendance..."));const f=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!f||f.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement($,{description:"Aucune donnée de tendance disponible",style:{color:"#8c8c8c"}}));const o=f.map(r=>({hour:Number.parseInt(r.hour||r.heure||0),avgDuration:Number.parseFloat(r.avgDuration||r.duree_moyenne||0),count:Number.parseInt(r.count||r.nombre||0),label:`${r.hour||r.heure||0}h`})).filter(r=>!isNaN(r.hour)&&!isNaN(r.avgDuration)).sort((r,t)=>r.hour-t.hour);if(o.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement($,{description:"Données de tendance invalides",style:{color:"#8c8c8c"}}));const u=({active:r,payload:t,label:l})=>{var n,c;return r&&t&&t.length?e.createElement("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:"#262626"}},`Heure: ${l}h`),t.map((d,m)=>e.createElement("p",{key:m,style:{margin:"4px 0 0 0",color:d.color,fontSize:"13px"}},`${d.name}: ${d.value.toFixed(1)} min`)),((c=(n=t[0])==null?void 0:n.payload)==null?void 0:c.count)&&e.createElement("p",{style:{margin:"4px 0 0 0",color:"#8c8c8c",fontSize:"12px"}},`Nombre d'arrêts: ${t[0].payload.count}`)):null};return e.createElement("div",{style:{width:"100%",height:"100%"}},e.createElement(J,{width:"100%",height:"100%"},e.createElement(ke,{data:o,margin:{top:20,right:30,left:20,bottom:20}},e.createElement("defs",null,e.createElement("linearGradient",{id:"areaGradient",x1:"0",y1:"0",x2:"0",y2:"1"},e.createElement("stop",{offset:"5%",stopColor:Ee.success,stopOpacity:.8}),e.createElement("stop",{offset:"95%",stopColor:Ee.success,stopOpacity:.1}))),e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0",vertical:!1}),e.createElement(te,{dataKey:"hour",type:"number",scale:"linear",domain:["dataMin","dataMax"],tickFormatter:r=>`${r}h`,tick:{fill:"#666",fontSize:12},axisLine:{stroke:"#d9d9d9"},tickLine:{stroke:"#d9d9d9"},label:{value:"Heure de la journée",position:"insideBottom",offset:-10,style:{fill:"#666",fontSize:12}}}),e.createElement(q,{tick:{fill:"#666",fontSize:12},axisLine:{stroke:"#d9d9d9"},tickLine:{stroke:"#d9d9d9"},label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:12}}}),e.createElement(Z,{content:e.createElement(u,null)}),e.createElement(se,{wrapperStyle:{paddingTop:"20px",fontSize:"12px"}}),e.createElement(Le,{type:"monotone",dataKey:"avgDuration",stroke:Ee.success,strokeWidth:3,fill:"url(#areaGradient)",name:"Durée moyenne",dot:{fill:Ee.success,strokeWidth:2,r:4},activeDot:{r:6,fill:"#fff",stroke:Ee.success,strokeWidth:3}}))))});const{Text:Ce,Title:Ln}=oe,xt=T.memo(()=>{const{operatorStats:a=[],loading:s}=le(),p=[{title:"Opérateur",dataIndex:"operator",key:"operator",render:o=>e.createElement(j,null,e.createElement(Ne,{style:{color:"#1890ff"}}),e.createElement(Ce,{strong:!0},o||"Non assigné"))},{title:"Interventions",dataIndex:"interventions",key:"interventions",render:o=>e.createElement(j,null,e.createElement(Ye,{style:{color:"#52c41a"}}),e.createElement(Ce,null,o||0)),sorter:(o,u)=>(o.interventions||0)-(u.interventions||0)},{title:"Temps Total (min)",dataIndex:"totalTime",key:"totalTime",render:o=>e.createElement(j,null,e.createElement(X,{style:{color:"#faad14"}}),e.createElement(Ce,null,o||0," min")),sorter:(o,u)=>(o.totalTime||0)-(u.totalTime||0)},{title:"Temps Moyen (min)",dataIndex:"avgTime",key:"avgTime",render:o=>e.createElement(Ce,{type:"secondary"},o?o.toFixed(1):"0.0"," min"),sorter:(o,u)=>(o.avgTime||0)-(u.avgTime||0)},{title:"Efficacité",key:"efficiency",render:(o,u)=>{const r=Math.max(...a.map(n=>n.totalTime||0)),t=r>0?(u.totalTime||0)/r*100:0;let l="#52c41a";return t>75?l="#f5222d":t>50&&(l="#faad14"),e.createElement(Re,{percent:t,size:"small",strokeColor:l,format:n=>`${n.toFixed(0)}%`})}}],f=a.map((o,u)=>({key:u,operator:o.operator||o.Regleur_Prenom||"Non assigné",interventions:o.interventions||o.count||0,totalTime:o.totalTime||o.total_duration||0,avgTime:o.avgTime||(o.total_duration&&o.count?o.total_duration/o.count:0)}));return e.createElement(ne,{title:e.createElement(j,null,e.createElement(Ne,null),"Statistiques des Opérateurs"),bordered:!1},e.createElement(mt,{columns:p,dataSource:f,loading:s,pagination:{pageSize:8,showSizeChanger:!1,showTotal:o=>`Total ${o} opérateurs`},size:"middle",bordered:!0}))});xt.displayName="ArretOperatorStatsTable";const pe={success:"#52c41a",warning:"#faad14"},Br=T.memo(({data:a=[],loading:s=!1})=>{if(s)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{size:"large"}));const p=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!p||p.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement($,{description:"Aucune donnée de disponibilité disponible"}));const f=p.map(o=>{let u=parseFloat(o.disponibilite||o.availability||0);return u>0&&u<=1&&(u=u*100),{date:o.date||o.Stop_Date,disponibilite:u,mttr:parseFloat(o.mttr||0),mtbf:parseFloat(o.mtbf||0)}});return e.createElement(J,{width:"100%",height:"100%"},e.createElement(tt,{data:f,margin:{top:20,right:20,left:10,bottom:30}},e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.createElement(te,{dataKey:"date",tick:{fill:"#666",fontSize:11},height:30,tickFormatter:o=>{try{return new Date(o).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return o}},label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666",fontSize:12}}}),"        ",e.createElement(q,{label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",offset:0,style:{fill:"#666",fontSize:12}},tick:{fill:"#666",fontSize:11},domain:[0,100],width:40,tickCount:5}),e.createElement(q,{yAxisId:"right",orientation:"right",label:{value:"MTTR (min)",angle:90,position:"insideRight",offset:0,style:{fill:"#666",fontSize:12}},width:40,tick:{fill:"#666",fontSize:11}}),"        ",e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px"},formatter:(o,u)=>u==="disponibilite"?[`${o.toFixed(1)}%`,"Disponibilité"]:u==="mttr"?[`${o.toFixed(1)} min`,"MTTR"]:u==="mtbf"?[`${o.toFixed(1)} h`,"MTBF"]:[o,u]}),e.createElement(se,{verticalAlign:"top",height:30,iconSize:10,iconType:"circle",wrapperStyle:{paddingTop:"10px",fontSize:"12px"}}),e.createElement(ce,{type:"monotone",dataKey:"disponibilite",stroke:pe.success,strokeWidth:2,dot:{fill:pe.success,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:pe.success,strokeWidth:2},name:"Disponibilité",animationDuration:1e3}),e.createElement(ce,{type:"monotone",dataKey:"mttr",stroke:pe.warning,strokeWidth:2,dot:{fill:pe.warning,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:pe.warning,strokeWidth:2},name:"MTTR",yAxisId:"right",animationDuration:1e3})))});Br.displayName="ArretDisponibiliteChart";const Ke={primary:"#1890ff",danger:"#f5222d"},$r=T.memo(({data:a=[],loading:s=!1})=>{if(s)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{size:"large"}));if(!a||a.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement($,{description:"Aucune donnée Pareto disponible"}));const p=a.map(r=>({reason:r.reason||r.Code_Stop||r.stopName||"N/A",value:parseFloat(r.value||r.duration||r.count||0),percentage:parseFloat(r.percentage||0)})).sort((r,t)=>t.value-r.value),f=p.reduce((r,t)=>r+t.value,0);let o=0;const u=p.map(r=>{o+=r.value;const t=o/f*100;return{...r,cumulativePercentage:t}});return e.createElement(J,{width:"100%",height:"100%"},e.createElement(qt,{data:u,margin:{top:16,right:24,left:24,bottom:16}},e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(te,{dataKey:"reason",tick:{fill:"#666",fontSize:12},angle:-45,textAnchor:"end",height:80}),e.createElement(q,{yAxisId:"left",label:{value:"Durée (min)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.createElement(q,{yAxisId:"right",orientation:"right",label:{value:"Cumul (%)",angle:90,position:"insideRight",style:{fill:"#666"}},tick:{fill:"#666"},domain:[0,100]}),e.createElement(Z,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(r,t)=>t==="value"?[`${r.toFixed(1)} min`,"Durée"]:t==="cumulativePercentage"?[`${r.toFixed(1)}%`,"Cumul"]:[r,t]}),e.createElement(se,null),e.createElement(ae,{yAxisId:"left",dataKey:"value",fill:Ke.primary,name:"Durée",radius:[4,4,0,0]}),e.createElement(ce,{yAxisId:"right",type:"monotone",dataKey:"cumulativePercentage",stroke:Ke.danger,strokeWidth:3,dot:{fill:Ke.danger,strokeWidth:2,r:4},name:"Cumul %"})))});$r.displayName="ArretParetoChart";const{Text:fe,Title:Se}=oe,Rt=T.memo(({mttr:a=0,mtbf:s=0,doper:p=0,loading:f=!1})=>{if(f)return e.createElement("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement("div",null,"Chargement des métriques de performance..."));const o=Math.max(0,Math.min(100,100-a/2)),u=Math.max(0,Math.min(100,s/10)),r=Math.max(0,Math.min(100,p)),t=n=>n>=80?"#52c41a":n>=60?"#faad14":n>=40?"#fa8c16":"#f5222d",l=n=>n>=80?"Excellent":n>=60?"Bon":n>=40?"Moyen":"À améliorer";return e.createElement("div",{style:{height:300,padding:16}},e.createElement(V,{gutter:[24,24],style:{height:"100%"}},e.createElement(H,{xs:24,md:8},e.createElement(ne,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(X,{style:{fontSize:24,color:i.PRIMARY_BLUE,marginBottom:8}}),e.createElement(Se,{level:5,style:{margin:0,color:i.PRIMARY_BLUE}},"MTTR"),e.createElement(Re,{type:"circle",percent:o,strokeColor:t(o),size:80,format:()=>`${a.toFixed(0)}min`,style:{margin:"12px 0"}}),e.createElement(fe,{style:{display:"block",fontSize:12,color:i.LIGHT_GRAY}},l(o)),e.createElement(fe,{style:{display:"block",fontSize:11,color:i.LIGHT_GRAY}},"Temps Moyen de Réparation")))),e.createElement(H,{xs:24,md:8},e.createElement(ne,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Ye,{style:{fontSize:24,color:i.PRIMARY_BLUE,marginBottom:8}}),e.createElement(Se,{level:5,style:{margin:0,color:i.PRIMARY_BLUE}},"MTBF"),e.createElement(Re,{type:"circle",percent:u,strokeColor:t(u),size:80,format:()=>`${s.toFixed(0)}h`,style:{margin:"12px 0"}}),e.createElement(fe,{style:{display:"block",fontSize:12,color:i.LIGHT_GRAY}},l(u)),e.createElement(fe,{style:{display:"block",fontSize:11,color:i.LIGHT_GRAY}},"Temps Moyen Entre Pannes")))),e.createElement(H,{xs:24,md:8},e.createElement(ne,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Pe,{style:{fontSize:24,color:i.PRIMARY_BLUE,marginBottom:8}}),e.createElement(Se,{level:5,style:{margin:0,color:i.PRIMARY_BLUE}},"Disponibilité"),e.createElement(Re,{type:"circle",percent:r,strokeColor:t(r),size:80,format:()=>`${p.toFixed(1)}%`,style:{margin:"12px 0"}}),e.createElement(fe,{style:{display:"block",fontSize:12,color:i.LIGHT_GRAY}},l(r)),e.createElement(fe,{style:{display:"block",fontSize:11,color:i.LIGHT_GRAY}},"Disponibilité Opérationnelle"))))),e.createElement(ne,{size:"small",style:{marginTop:16,background:`linear-gradient(135deg, ${i.PRIMARY_BLUE}, ${i.SECONDARY_BLUE})`,border:"none",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Se,{level:5,style:{margin:0,color:"white"}},"Performance Globale: ",((o+u+r)/3).toFixed(0),"/100"))))});Rt.displayName="ArretPerformanceGauge";const At=T.memo(({data:a=[],loading:s,colors:p})=>{const f=p||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE];if(s)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},e.createElement(K,{size:"large"}));const o=T.useMemo(()=>{if(!a||a.length===0)return[];const r=a.reduce((l,n)=>{const c=n.Part_NO||n.partNo||n.part_no||"Non défini",d=parseFloat(n.duration_minutes)||parseFloat(n.Duree_Arret)||0;return l[c]||(l[c]={partNo:c,stopCount:0,totalDuration:0,avgDuration:0,machines:new Set}),l[c].stopCount+=1,l[c].totalDuration+=d,l[c].machines.add(n.Machine_Name||n.machine||"Inconnue"),l},{});return Object.values(r).map(l=>({partNo:l.partNo,stopCount:l.stopCount,totalDuration:Math.round(l.totalDuration),avgDuration:l.stopCount>0?Math.round(l.totalDuration/l.stopCount):0,machineCount:l.machines.size,efficiency:Math.max(0,100-l.stopCount*2)})).sort((l,n)=>n.stopCount-l.stopCount).slice(0,15)},[a]);if(!o||o.length===0)return e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"}},e.createElement($,{description:"Aucune donnée de production disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const u=(r,t)=>f[r%f.length];return e.createElement(J,{width:"100%",height:420},e.createElement(he,{data:o,margin:{top:20,right:30,left:40,bottom:60}},e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(te,{dataKey:"partNo",tick:{fontSize:10,fill:i.LIGHT_GRAY,angle:-45,textAnchor:"end"},axisLine:{stroke:i.LIGHT_GRAY},tickLine:{stroke:i.LIGHT_GRAY},height:80,interval:0,tickFormatter:r=>r.length>12?`${r.substring(0,10)}...`:r}),e.createElement(q,{tick:{fontSize:11,fill:i.LIGHT_GRAY},axisLine:{stroke:i.LIGHT_GRAY},tickLine:{stroke:i.LIGHT_GRAY},label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:i.DARK_GRAY}}}),e.createElement(Z,{formatter:(r,t,l)=>{const n=l.payload;return[e.createElement("div",{key:"tooltip",style:{color:i.DARK_GRAY}},e.createElement("div",null,e.createElement("strong",null,"Commande:")," ",n.partNo),e.createElement("div",null,e.createElement("strong",null,"Arrêts:")," ",n.stopCount),e.createElement("div",null,e.createElement("strong",null,"Durée totale:")," ",n.totalDuration," min"),e.createElement("div",null,e.createElement("strong",null,"Durée moyenne:")," ",n.avgDuration," min"),e.createElement("div",null,e.createElement("strong",null,"Machines:")," ",n.machineCount),e.createElement("div",null,e.createElement("strong",null,"Efficacité:")," ",n.efficiency,"%"))]},labelFormatter:()=>"",contentStyle:{backgroundColor:"#fff",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px",padding:"12px"}}),e.createElement(ae,{dataKey:"stopCount",name:"Nombre d'arrêts",radius:[4,4,0,0],maxBarSize:50},o.map((r,t)=>e.createElement(et,{key:`cell-${t}`,fill:u(t,o.length)})))))});At.displayName="ArretProductionOrderChart";const{Text:Pn}=oe;Fe.register(Be,$e,Oe,ze,He,Ge);const Or=({data:a=[],loading:s=!1,colors:p})=>{const f=T.useRef(),[o,u]=T.useState("stops"),r=p||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE],l=(d=>{if(!Array.isArray(d)||d.length===0)return{labels:[],stopCounts:[],avgDurations:[]};const m={};d.forEach(b=>{const A=b.Regleur_Prenom||"Non assigné";if(m[A]||(m[A]={count:0,totalDuration:0,avgDuration:0}),m[A].count+=1,b.Debut_Stop&&b.Fin_Stop_Time)try{const D=new Date(b.Debut_Stop),_=(new Date(b.Fin_Stop_Time)-D)/(1e3*60);_>0&&(m[A].totalDuration+=_)}catch(D){console.warn("Error calculating duration:",D)}}),Object.keys(m).forEach(b=>{const A=m[b];A.avgDuration=A.count>0?A.totalDuration/A.count:0});const h=Object.entries(m).sort(([,b],[,A])=>A.count-b.count).slice(0,10),g=h.map(([b])=>b),y=h.map(([,b])=>b.count),E=h.map(([,b])=>Math.round(b.avgDuration));return{labels:g,stopCounts:y,avgDurations:E}})(a),n=()=>o==="stops"?[{label:"Nombre d'Arrêts",data:l.stopCounts,backgroundColor:`${r[0]}99`,borderColor:r[0],borderWidth:2,borderRadius:6,borderSkipped:!1}]:o==="duration"?[{label:"Durée Moyenne (min)",data:l.avgDurations,backgroundColor:`${r[1]}99`,borderColor:r[1],borderWidth:2,borderRadius:6,borderSkipped:!1}]:[{label:"Nombre d'Arrêts",data:l.stopCounts,backgroundColor:`${r[0]}99`,borderColor:r[0],borderWidth:2,borderRadius:6,borderSkipped:!1,yAxisID:"y"},{label:"Durée Moyenne (min)",data:l.avgDurations,backgroundColor:`${r[1]}99`,borderColor:r[1],borderWidth:2,borderRadius:6,borderSkipped:!1,yAxisID:"y1"}],c=()=>{const d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:o==="stops"?"Nombre d'Arrêts par Opérateur":o==="duration"?"Durée Moyenne par Opérateur":"Performance des Opérateurs",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(m){const h=m.dataset.label||"",g=m.parsed.y;return h.includes("Nombre")?`${h}: ${g} arrêts`:`${h}: ${g} minutes`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return o==="both"?(d.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},d.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):d.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:o==="stops"?"Nombre d'Arrêts":"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},d};return s?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(K,{size:"large",tip:"Chargement des données opérateurs..."})):!l.labels||l.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement($,{description:"Aucune donnée d'opérateur disponible",image:$.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(V,{style:{marginBottom:"12px"}},e.createElement(H,{span:24},e.createElement(j,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(U,{type:o==="stops"?"primary":"default",icon:e.createElement(Ne,null),onClick:()=>u("stops"),size:"small"},"Arrêts"),e.createElement(U,{type:o==="duration"?"primary":"default",icon:e.createElement(X,null),onClick:()=>u("duration"),size:"small"},"Durée"),e.createElement(U,{type:o==="both"?"primary":"default",onClick:()=>u("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"}},e.createElement(rt,{ref:f,data:{labels:l.labels,datasets:n()},options:c()})))};Fe.register(Be,$e,Oe,ze,He,Ge);const zr=({data:a=[],loading:s=!1,colors:p})=>{const f=T.useRef(),[o,u]=T.useState("efficiency"),{charts:r,theme:t}=ye(),l=ie({charts:r,theme:t}),n=p||l.colors||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE],d=(g=>{console.log("🎯 ArretMachineEfficiencyChart - Data received:",{dataLength:(g==null?void 0:g.length)||0,dataType:typeof g,isArray:Array.isArray(g),loading:s,sampleData:(g==null?void 0:g.slice(0,2))||[]});const y=Array.isArray(g)?g:(g==null?void 0:g.data)||[];if(!Array.isArray(y)||y.length===0)return console.log("🔍 ArretMachineEfficiencyChart - No valid data to process"),{labels:[],datasets:[]};const E={};y.forEach(_=>{const Y=_.Machine_Name||_.machineName||_.machine_name||"Unknown";E[Y]||(E[Y]={stopCount:0,totalDuration:0,avgDuration:0,efficiency:100}),E[Y].stopCount+=1;let N=0;if(_.duration_minutes!==void 0&&_.duration_minutes!==null)N=parseFloat(_.duration_minutes);else if(_.duration!==void 0&&_.duration!==null)N=parseFloat(_.duration);else{const k=_.Debut_Stop||_.debut_stop||_.startTime||_.start_time,C=_.Fin_Stop_Time||_.fin_stop_time||_.endTime||_.end_time;if(k&&C)try{const I=new Date(k),F=new Date(C);!isNaN(I.getTime())&&!isNaN(F.getTime())&&(N=(F-I)/(1e3*60))}catch(I){console.warn("Error calculating duration:",I)}}N>0&&(E[Y].totalDuration+=N)}),Object.keys(E).forEach(_=>{const Y=E[_];Y.avgDuration=Y.stopCount>0?Y.totalDuration/Y.stopCount:0;const N=Math.max(...Object.values(E).map(C=>C.stopCount)),k=Math.max(...Object.values(E).map(C=>C.totalDuration));if(N>0&&k>0){const C=Y.stopCount/N*50,I=Y.totalDuration/k*50;Y.efficiency=Math.max(0,100-C-I)}});const b=Object.entries(E).sort(([,_],[,Y])=>Y.efficiency-_.efficiency),A=b.map(([_])=>_),D=b.map(([,_])=>Math.round(_.efficiency)),x=b.map(([,_])=>_.stopCount);return{labels:A,efficiencyScores:D,stopCounts:x,machineStats:Object.fromEntries(b)}})(a),m=()=>o==="efficiency"?[{label:"Score d'Efficacité (%)",data:d.efficiencyScores,backgroundColor:d.efficiencyScores.map((g,y)=>`${n[y%n.length]}CC`),borderColor:d.efficiencyScores.map((g,y)=>n[y%n.length]),borderWidth:2,borderRadius:8,borderSkipped:!1}]:o==="stops"?[{label:"Nombre d'Arrêts",data:d.stopCounts,backgroundColor:`${n[1]}99`,borderColor:n[1],borderWidth:2,borderRadius:8,borderSkipped:!1}]:[{label:"Score d'Efficacité (%)",data:d.efficiencyScores,backgroundColor:d.efficiencyScores.map((g,y)=>`${n[y%n.length]}CC`),borderColor:d.efficiencyScores.map((g,y)=>n[y%n.length]),borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y"},{label:"Nombre d'Arrêts",data:d.stopCounts,backgroundColor:`${n[1]}99`,borderColor:n[1],borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y1"}],h=()=>{var y;const g={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:o==="efficiency"?"Score d'Efficacité des Machines":o==="stops"?"Nombre d'Arrêts par Machine":"Efficacité et Arrêts des Machines",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(E){const b=E.dataset.label||"",A=E.parsed.y;return b.includes("Efficacité")?`${b}: ${A}%`:`${b}: ${A} arrêts`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:(y=l.animationConfig)!=null&&y.isAnimationActive?1e3:0,easing:"easeInOutQuart"}};return o==="both"?(g.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Score d'Efficacité (%)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:100,font:{size:11},callback:function(E){return E+"%"}}},g.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):g.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:o==="efficiency"?"Score d'Efficacité (%)":"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:o==="efficiency"?100:void 0,font:{size:11},callback:function(E){return o==="efficiency"?E+"%":E}}},g};return s?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(K,{size:"large",tip:"Chargement de l'efficacité des machines..."})):!d.labels||d.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement($,{description:"Aucune donnée d'efficacité disponible",image:$.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(V,{style:{marginBottom:"12px"}},e.createElement(H,{span:24},e.createElement(j,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(U,{type:o==="efficiency"?"primary":"default",icon:e.createElement(Pe,null),onClick:()=>u("efficiency"),size:"small"},"Efficacité"),e.createElement(U,{type:o==="stops"?"primary":"default",icon:e.createElement(Lt,null),onClick:()=>u("stops"),size:"small"},"Arrêts"),e.createElement(U,{type:o==="both"?"primary":"default",onClick:()=>u("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"400px",minHeight:"300px",width:"100%",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px",overflow:"hidden"}},e.createElement(rt,{ref:f,data:{labels:d.labels,datasets:m()},options:h()})))};Fe.register(Be,$e,Oe,ze,He,Ge,Xt,Jt);const Hr=({data:a=[],loading:s=!1,colors:p})=>{const f=T.useRef(),[o,u]=T.useState("count"),{charts:r,theme:t}=ye(),l=ie({charts:r,theme:t}),n=p||l.colors||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE],d=(g=>{console.log("🎯 ArretTimePatternChart - Data received:",{dataLength:(g==null?void 0:g.length)||0,dataType:typeof g,isArray:Array.isArray(g),loading:s,sampleData:(g==null?void 0:g.slice(0,2))||[]});const y=Array.isArray(g)?g:(g==null?void 0:g.data)||[];if(!Array.isArray(y)||y.length===0)return console.log("🔍 ArretTimePatternChart - No valid data to process"),{labels:[],stopCounts:[],avgDurations:[]};const E=480,b=1,A={};let D=0,x=0;for(let C=0;C<24;C++)A[C]={count:0,totalDuration:0,avgDuration:0,durations:[],outliers:[]};const _=C=>{if(!C)return null;try{const I=String(C).trim();if(I.includes("T")&&(I.includes("Z")||I.includes("+")||I.includes("-"))){const R=new Date(I);if(!isNaN(R.getTime()))return R}if(I.includes("/")){const R=I.split(/\s+/).filter(M=>M.length>0);if(R.length>=2){const M=R[0],w=R[1],S=M.split("/"),v=w.split(":");if(S.length===3&&v.length>=2){const[B,O,P]=S,[z,G,Ae]=v;if(B&&O&&P&&z&&G){const De=`${P}-${O.padStart(2,"0")}-${B.padStart(2,"0")}T${z.padStart(2,"0")}:${G.padStart(2,"0")}:${(Ae||"00").padStart(2,"0")}`,nt=new Date(De);if(!isNaN(nt.getTime()))return nt}}}}const F=new Date(I);return isNaN(F.getTime())?null:F}catch(I){return console.warn("Error parsing date:",C,I),null}};console.log("🕐 Processing time patterns for",y.length,"stops"),y.forEach(C=>{const I=C.Debut_Stop||C.debut_stop||C.startTime||C.start_time;if(I)try{const F=_(I);if(F&&!isNaN(F.getTime())){const R=F.getHours();if(x++,A[R]){let M=0;if(C.duration_minutes!==void 0&&C.duration_minutes!==null)M=parseFloat(C.duration_minutes);else if(C.duration!==void 0&&C.duration!==null)M=parseFloat(C.duration);else{const w=C.Fin_Stop_Time||C.fin_stop_time||C.endTime||C.end_time;if(w){const S=_(w);S&&!isNaN(S.getTime())&&(M=(S-F)/(1e3*60))}}M>0&&(M>=b&&M<=E?(A[R].count+=1,A[R].totalDuration+=M,A[R].durations.push(M)):(A[R].outliers.push(M),D++,console.warn(`🚨 Outlier detected at hour ${R}: ${M.toFixed(1)}min (${M>E?"too long":"too short"})`)))}}}catch(F){console.warn("Error parsing time:",F)}}),Object.keys(A).forEach(C=>{const I=A[C];I.avgDuration=I.count>0?I.totalDuration/I.count:0,(I.count>0||I.outliers.length>0)&&(console.log(`⏰ Hour ${C}:00 - ${I.count} normal stops, ${I.outliers.length} outliers`),console.log(`   Normal: total ${I.totalDuration.toFixed(1)}min, avg: ${I.avgDuration.toFixed(1)}min`),I.outliers.length>0&&console.log(`   Outliers: [${I.outliers.map(F=>F.toFixed(1)).join(", ")}]min`))});const Y=Object.keys(A).map(C=>`${parseInt(C).toString().padStart(2,"0")}:00`),N=Object.values(A).map(C=>C.count),k=Object.values(A).map(C=>Math.round(C.avgDuration));return console.log("📊 Time Pattern Processing Summary:",{totalStopsProcessed:x,outlierCount:D,outlierPercentage:(D/x*100).toFixed(1)+"%",totalHoursWithData:N.filter(C=>C>0).length,maxStopsInHour:Math.max(...N),maxAvgDuration:Math.max(...k),avgDurationsFiltered:k.filter(C=>C>0)}),{labels:Y,stopCounts:N,avgDurations:k}})(a),m=()=>o==="count"?[{label:"Nombre d'Arrêts par Heure",data:d.stopCounts,borderColor:n[0],backgroundColor:`${n[0]}1A`,borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:n[0],pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2}]:o==="duration"?[{label:"Durée Moyenne (min)",data:d.avgDurations,borderColor:n[1],backgroundColor:`${n[1]}1A`,borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:n[1],pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2}]:[{label:"Nombre d'Arrêts par Heure",data:d.stopCounts,borderColor:n[0],backgroundColor:`${n[0]}1A`,borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:n[0],pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2,yAxisID:"y"},{label:"Durée Moyenne (min)",data:d.avgDurations,borderColor:n[1],backgroundColor:`${n[1]}1A`,borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:n[1],pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2,yAxisID:"y1"}],h=()=>{var y;const g={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:o==="count"?"Nombre d'Arrêts par Heure":o==="duration"?"Durée Moyenne des Arrêts par Heure":"Motifs Temporels des Arrêts",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:function(E){return`Heure: ${E[0].label}`},label:function(E){const b=E.dataset.label||"",A=E.parsed.y;return b.includes("Nombre")?`${b}: ${A} arrêts`:`${b}: ${A} minutes`}}}},scales:{x:{grid:{display:!0,color:"rgba(0, 0, 0, 0.05)"},title:{display:!0,text:"Heure de la Journée",font:{size:12,weight:"bold"}},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:(y=l.animationConfig)!=null&&y.isAnimationActive?1e3:0,easing:"easeInOutQuart"}};return o==="both"?(g.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(54, 162, 235, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},g.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):g.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:o==="count"?"Nombre d'Arrêts":"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{color:o==="count"?"rgba(54, 162, 235, 0.1)":"rgba(255, 99, 132, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},g};return s?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(K,{size:"large",tip:"Chargement des motifs temporels..."})):!d.labels||d.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement($,{description:"Aucune donnée temporelle disponible",image:$.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(V,{style:{marginBottom:"12px"}},e.createElement(H,{span:24},e.createElement(j,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(U,{type:o==="count"?"primary":"default",icon:e.createElement(ft,null),onClick:()=>u("count"),size:"small"},"Arrêts"),e.createElement(U,{type:o==="duration"?"primary":"default",icon:e.createElement(X,null),onClick:()=>u("duration"),size:"small"},"Durée"),e.createElement(U,{type:o==="both"?"primary":"default",onClick:()=>u("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:"400px",minHeight:"300px",width:"100%",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px",overflow:"hidden"}},e.createElement(Qt,{ref:f,data:{labels:d.labels,datasets:m()},options:h()})))};Fe.register(Be,$e,Oe,ze,He,Ge);const Gr=({data:a=[],loading:s=!1,colors:p})=>{const f=T.useRef(),[o,u]=T.useState("count"),{charts:r,theme:t}=ye(),l=ie({charts:r,theme:t}),n=p||l.colors||[i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE],c=E=>{if(!E||typeof E!="string")return null;try{const b=String(E).trim();if(b.includes("T")&&(b.includes("Z")||b.includes("+")||b.includes("-"))){const D=new Date(b);if(!isNaN(D.getTime()))return D}if(b.includes("/")){const D=b.split(/\s+/);if(D.length!==2)return null;const[x,_]=D,[Y,N,k]=x.split("/"),[C,I,F]=_.split(":");return!Y||!N||!k||!C||!I||!F||isNaN(Y)||isNaN(N)||isNaN(k)||isNaN(C)||isNaN(I)||isNaN(F)?null:new Date(parseInt(k),parseInt(N)-1,parseInt(Y),parseInt(C),parseInt(I),parseInt(F))}const A=new Date(b);return isNaN(A.getTime())?null:A}catch(b){return console.warn("Date parsing error:",b,"for string:",E),null}};T.useEffect(()=>{console.log("🎯 ArretDurationDistributionChart - Data received:",{dataLength:(a==null?void 0:a.length)||0,dataType:typeof a,isArray:Array.isArray(a),loading:s,firstItem:(a==null?void 0:a[0])||null})},[a,s]);const m=(E=>{console.log("🔍 ArretDurationDistributionChart - Processing data:",{dataLength:(E==null?void 0:E.length)||0,isArray:Array.isArray(E),firstItem:(E==null?void 0:E[0])||null,sampleFields:E!=null&&E[0]?Object.keys(E[0]):[]});const b=Array.isArray(E)?E:(E==null?void 0:E.data)||[];if(!Array.isArray(b)||b.length===0)return console.log("🔍 ArretDurationDistributionChart - No valid data to process"),{labels:[],counts:[],percentages:[]};const A=[{label:"0-5 min",min:0,max:5,count:0},{label:"5-15 min",min:5,max:15,count:0},{label:"15-30 min",min:15,max:30,count:0},{label:"30-60 min",min:30,max:60,count:0},{label:"1-2 heures",min:60,max:120,count:0},{label:"2-4 heures",min:120,max:240,count:0},{label:"4+ heures",min:240,max:1/0,count:0}];let D=0,x=0,_=0;b.forEach((C,I)=>{D++;const F=C.Debut_Stop||C.debut_stop||C.startTime||C.start_time,R=C.Fin_Stop_Time||C.fin_stop_time||C.endTime||C.end_time||C.Fin_Stop;I<3&&console.log(`🔍 Stop ${I}:`,{startTime:F,endTime:R,duration:C.duration,duration_minutes:C.duration_minutes,allFields:Object.keys(C)});let M=0;if(C.duration_minutes!==void 0&&C.duration_minutes!==null)M=parseFloat(C.duration_minutes);else if(C.duration!==void 0&&C.duration!==null)M=parseFloat(C.duration);else if(F&&R)try{const w=c(F),S=c(R);w&&S&&!isNaN(w.getTime())&&!isNaN(S.getTime())?(M=(S-w)/(1e3*60),I<3&&console.log(`🔍 Duration calculation ${I}:`,{startTime:F,endTime:R,start:w.toISOString(),end:S.toISOString(),durationMinutes:M})):I<3&&console.log(`🔍 Failed to parse dates for stop ${I}:`,{startTime:F,endTime:R,parsedStart:w,parsedEnd:S})}catch(w){console.warn("Error calculating duration:",w)}if(M>0){_++,x++;const w=A.find(S=>M>=S.min&&M<S.max);w&&w.count++}}),console.log("🔍 Duration processing results:",{totalStops:D,processedStops:_,validDurations:x,buckets:A.map(C=>({label:C.label,count:C.count}))});const Y=A.map(C=>C.label),N=A.map(C=>C.count),k=A.map(C=>x>0?Math.round(C.count/x*100):0);return{labels:Y,counts:N,percentages:k,buckets:A}})(a),h=(E,b)=>{const A=n[E%n.length];return{background:`${A}B3`,border:A}},g=()=>o==="count"?[{label:"Nombre d'Arrêts",data:m.counts,backgroundColor:m.labels.map((E,b)=>h(b,m.labels.length).background),borderColor:m.labels.map((E,b)=>h(b,m.labels.length).border),borderWidth:2,borderRadius:8,borderSkipped:!1}]:o==="percentage"?[{label:"Pourcentage (%)",data:m.percentages,backgroundColor:`${n[1]}99`,borderColor:n[1],borderWidth:2,borderRadius:8,borderSkipped:!1}]:[{label:"Nombre d'Arrêts",data:m.counts,backgroundColor:m.labels.map((E,b)=>h(b,m.labels.length).background),borderColor:m.labels.map((E,b)=>h(b,m.labels.length).border),borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y"},{label:"Pourcentage (%)",data:m.percentages,backgroundColor:`${n[1]}99`,borderColor:n[1],borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y1"}],y=()=>{var b;const E={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:o==="count"?"Distribution - Nombre d'Arrêts":o==="percentage"?"Distribution - Pourcentage":"Distribution des Durées d'Arrêt",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:function(A){return`Durée: ${A[0].label}`},label:function(A){const D=A.dataset.label||"",x=A.parsed.y;return D.includes("Nombre")?`${D}: ${x} arrêts`:`${D}: ${x}%`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:(b=l.animationConfig)!=null&&b.isAnimationActive?1e3:0,easing:"easeInOutQuart"}};return o==="both"?(E.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},E.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Pourcentage (%)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,max:100,font:{size:11},callback:function(A){return A+"%"}}}):E.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:o==="count"?"Nombre d'Arrêts":"Pourcentage (%)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:o==="percentage"?100:void 0,font:{size:11},callback:function(A){return o==="percentage"?A+"%":A}}},E};return s?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement(K,{size:"large",tip:"Chargement de la distribution des durées..."})):!m.labels||m.labels.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"}},e.createElement($,{description:"Aucune donnée de durée disponible",image:$.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{height:"100%",width:"100%"}},e.createElement(V,{style:{marginBottom:"12px"}},e.createElement(H,{span:24},e.createElement(j,{size:"small",style:{width:"100%",justifyContent:"center"}},e.createElement(U,{type:o==="count"?"primary":"default",icon:e.createElement(we,null),onClick:()=>u("count"),size:"small"},"Nombre"),e.createElement(U,{type:o==="percentage"?"primary":"default",icon:e.createElement(sr,null),onClick:()=>u("percentage"),size:"small"},"Pourcentage"),e.createElement(U,{type:o==="both"?"primary":"default",onClick:()=>u("both"),size:"small"},"Les deux")))),e.createElement("div",{style:{height:l.height||"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"}},e.createElement(rt,{ref:f,data:{labels:m.labels,datasets:g()},options:y()})))},Dt=T.memo(({data:a=[],loading:s=!1})=>{const p=ie({chartType:"area"});if(s)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{size:"large"}));const f=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!f||f.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement($,{description:"Aucune donnée de disponibilité disponible"}),"      ");const o=f.map(r=>{let t=parseFloat(r.disponibilite||r.availability||0);return t>0&&t<=1&&(t=t*100),{date:r.date||r.Stop_Date,disponibilite:Math.round(t*100)/100,downtime:r.downtime||0,stopCount:r.stopCount||0}}).filter(r=>r.date),u=o.reduce((r,t)=>r+t.disponibilite,0)/o.length;return e.createElement(J,{...p.responsiveContainerProps},e.createElement(ke,{data:o,margin:p.margins},e.createElement("defs",null,e.createElement("linearGradient",{id:"availabilityGradient",x1:"0",y1:"0",x2:"0",y2:"1"},e.createElement("stop",{offset:"5%",stopColor:p.getPrimaryColor(),stopOpacity:.4}),e.createElement("stop",{offset:"95%",stopColor:p.getPrimaryColor(),stopOpacity:.1}))),e.createElement(ee,{...p.gridConfig}),e.createElement(te,{dataKey:"date",...p.axisConfig,height:30,angle:-30,textAnchor:"end",tickFormatter:r=>{try{return new Date(r).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return r}}}),"        ",e.createElement(q,{...p.axisConfig,width:40,domain:[0,100],tickCount:5,tickFormatter:r=>`${r}%`,label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",style:{fill:p.getTextColor(),fontSize:12},offset:0}}),e.createElement(Z,{...p.tooltipConfig}),p.displayConfig.showLegend&&e.createElement(se,{...p.legendConfig}),"        ",e.createElement(Le,{type:"monotone",dataKey:"disponibilite",stroke:p.getPrimaryColor(),strokeWidth:2,fill:"url(#availabilityGradient)",dot:{fill:p.getPrimaryColor(),strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:p.getPrimaryColor(),strokeWidth:2},...p.animationConfig}),e.createElement(ce,{type:"monotone",dataKey:()=>u,stroke:p.colors[1]||p.getPrimaryColor(),strokeWidth:2,strokeDasharray:"5 5",dot:!1,name:`Moyenne: ${u.toFixed(1)}%`})))});Dt.displayName="AvailabilityTrendChart";const Ct=T.memo(({data:a=[],loading:s=!1})=>{if(s)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{size:"large"}));const p=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!p||p.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement($,{description:"Aucune donnée MTTR disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const f=p.map(r=>{const t=parseFloat(r.mttr||r.avg_repair_time||0);return{date:r.date||r.Stop_Date||r.repair_date,mttr:Math.round(t*100)/100,stops:r.stops||r.stopCount||0,totalRepairTime:r.totalRepairTime||0}}).filter(r=>r.date&&!isNaN(r.mttr)&&r.mttr>0),o=f.reduce((r,t)=>r+t.mttr,0)/f.length,u=r=>r<=30?i.SECONDARY_BLUE:r<=60?i.PRIMARY_BLUE:r<=120?i.CHART_TERTIARY:"#f5222d";return e.createElement(J,{width:"100%",height:"100%"},"      ",e.createElement(ke,{data:f,margin:{top:20,right:20,left:10,bottom:30}},e.createElement("defs",null,e.createElement("linearGradient",{id:"mttrGradient",x1:"0",y1:"0",x2:"0",y2:"1"},e.createElement("stop",{offset:"5%",stopColor:i.PRIMARY_BLUE,stopOpacity:.4}),e.createElement("stop",{offset:"95%",stopColor:i.PRIMARY_BLUE,stopOpacity:.1}))),e.createElement(ee,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.createElement(te,{dataKey:"date",tick:{fill:i.LIGHT_GRAY,fontSize:11},height:30,angle:-45,textAnchor:"end",tickFormatter:r=>{try{return new Date(r).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return r}}}),"        ",e.createElement(q,{tick:{fill:i.LIGHT_GRAY,fontSize:11},width:40,tickFormatter:r=>`${r}min`,tickCount:5}),e.createElement(Z,{contentStyle:{backgroundColor:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px"},formatter:(r,t)=>{const l=u(r);return t==="MTTR"?[e.createElement("span",{style:{color:l}},`${r.toFixed(1)} min`),"MTTR"]:[r,t]},labelFormatter:r=>{try{return new Date(r).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}catch{return r}}}),"        ",e.createElement(Le,{type:"monotone",dataKey:"mttr",stroke:i.PRIMARY_BLUE,strokeWidth:2,fill:"url(#mttrGradient)",dot:{fill:i.PRIMARY_BLUE,strokeWidth:1,r:3},activeDot:{r:5,fill:"#FFFFFF",stroke:i.PRIMARY_BLUE,strokeWidth:2}}),e.createElement(ce,{type:"monotone",dataKey:()=>o,stroke:i.SECONDARY_BLUE,strokeWidth:1.5,strokeDasharray:"4 4",dot:!1,name:`Moyenne: ${o.toFixed(1)} min`}),e.createElement(ce,{type:"monotone",dataKey:()=>30,stroke:"#52c41a",strokeWidth:1.5,strokeDasharray:"4 4",dot:!1,name:"Objectif: 30 min"})))});Ct.displayName="MTTRTrendChart";const St=T.memo(({data:a=[],loading:s=!1})=>{const p=ie({chartType:"bar"});if(console.log("🔧 DowntimeDurationChart received data:",{dataType:typeof a,isArray:Array.isArray(a),dataLength:(a==null?void 0:a.length)||0,sampleData:(a==null?void 0:a.slice(0,3))||[]}),s)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{size:"large"}));const f=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!f||f.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement($,{description:"Aucune donnée de temps d'arrêt disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const o=f.map(t=>({reason:t.reason||t.Code_Stop||t.stopName||"N/A",value:parseFloat(t.value||t.duration||t.count||0),percentage:parseFloat(t.percentage||0)})).sort((t,l)=>l.value-t.value).slice(0,8).map(t=>({...t,reason:t.reason.length>12?t.reason.substring(0,10)+"...":t.reason})),u=(t,l)=>{const n=t/l;return n>.7?"#f5222d":n>.4?"#fa541c":n>.2?"#fa8c16":"#73d13d"},r=Math.max(...o.map(t=>t.value));return e.createElement(J,{...p.responsiveContainerProps},e.createElement(he,{data:o,margin:p.margins},e.createElement(ee,{...p.gridConfig}),e.createElement(te,{dataKey:"reason",...p.axisConfig,angle:-25,textAnchor:"end",height:60,interval:0}),e.createElement(q,{...p.axisConfig,width:35,tickCount:4,tickFormatter:t=>`${t}`}),e.createElement(Z,{...p.tooltipConfig}),"        ",e.createElement(ae,{dataKey:"value",fill:t=>u(t.value,r),radius:[4,4,0,0],name:"Durée",barSize:38,maxBarSize:40},o.map((t,l)=>e.createElement(ae,{key:`bar-${l}`,fill:u(t.value,r)})))))});St.displayName="DowntimeDurationChart";const Tt=T.memo(({data:a=[],loading:s=!1})=>{const p=ie({chartType:"line"});if(console.log("🔧 CumulativeImpactChart received data:",{dataType:typeof a,isArray:Array.isArray(a),dataLength:(a==null?void 0:a.length)||0,sampleData:(a==null?void 0:a.slice(0,3))||[]}),s)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(K,{size:"large"}));const f=Array.isArray(a)?a:(a==null?void 0:a.data)||[];if(!f||f.length===0)return e.createElement("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement($,{description:"Aucune donnée d'impact cumulé disponible",image:$.PRESENTED_IMAGE_SIMPLE}));const o=f.map(l=>({reason:l.reason||l.Code_Stop||l.stopName||"N/A",value:parseFloat(l.value||l.duration||l.count||0)})).sort((l,n)=>n.value-l.value),u=o.reduce((l,n)=>l+n.value,0);let r=0;const t=o.map((l,n)=>{r+=l.value;const c=r/u*100;return{index:n+1,reason:l.reason.length>12?l.reason.substring(0,10)+"...":l.reason,fullReason:l.reason,value:l.value,cumulativePercentage:c}}).slice(0,10);return e.createElement(J,{...p.responsiveContainerProps},e.createElement(tt,{data:t,margin:p.margins},e.createElement(ee,{...p.gridConfig}),e.createElement(te,{dataKey:"reason",...p.axisConfig,angle:-25,textAnchor:"end",height:50,interval:0}),e.createElement(q,{...p.axisConfig,width:25,domain:[0,100],tickCount:5,tickFormatter:l=>`${l}%`}),"        ",e.createElement(Vt,{y:80,stroke:"#f5222d",strokeDasharray:"5 3",strokeWidth:1.5,label:{value:"80%",position:"right",style:{fill:"#f5222d",fontSize:"10px",fontWeight:"bold"}}}),e.createElement(Z,{...p.tooltipConfig}),p.displayConfig.showLegend&&e.createElement(se,{...p.legendConfig}),e.createElement(ce,{type:"monotone",dataKey:"cumulativePercentage",...p.getLineElementConfig(p.getPrimaryColor()),name:"Impact Cumulé"})))});Tt.displayName="CumulativeImpactChart";class _t extends T.Component{constructor(s){super(s),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(s){return{hasError:!0}}componentDidCatch(s,p){console.error("🚨 ArretErrorBoundary caught an error:",s,p),this.setState({error:s,errorInfo:p})}render(){return this.state.hasError?e.createElement("div",{style:{padding:"20px",background:"#ffebee",border:"1px solid #f44336",margin:"20px"}},e.createElement("h2",null,"🚨 Something went wrong in ArretsDashboard"),e.createElement("details",{style:{whiteSpace:"pre-wrap",marginTop:"10px"}},e.createElement("summary",null,"Error Details"),e.createElement("p",null,e.createElement("strong",null,"Error:")," ",this.state.error&&this.state.error.toString()),e.createElement("p",null,e.createElement("strong",null,"Component Stack:")," ",this.state.errorInfo&&this.state.errorInfo.componentStack))):this.props.children}}const{TabPane:Fn}=Je,Ur=()=>{var B,O;const a=gt(),{getChartColors:s}=a,{charts:p}=ye();(B=p.interactions)==null||B.clickToExpand;const f=le();if(!f)return e.createElement("div",null,"Context not available");const{chartData:o=[],topStopsData:u=[],durationTrend:r=[],machineComparison:t=[],stopReasons:l=[],stopsData:n=[],filteredStopsData:c=[],operatorStats:d=[],disponibiliteTrendData:m=[],downtimeParetoData:h=[],mttrCalendarData:g=[],disponibiliteByMachineData:y=[],selectedMachine:E="",selectedMachineModel:b="",selectedDate:A=null,dateRangeType:D="day",mttr:x=0,mtbf:_=0,doper:Y=0,loading:N=!0,chartOptions:k={activeTab:"bar"},setChartOptions:C,dateFilterActive:I=!1,dateRangeDescription:F=""}=f;e.useEffect(()=>{console.log("🎯 ArretChartsSection - Context Data Debug (Updated):",{topStopsData:(u==null?void 0:u.length)||0,machineComparison:(t==null?void 0:t.length)||0,stopReasons:(l==null?void 0:l.length)||0,chartData:(o==null?void 0:o.length)||0,filteredStopsData:(c==null?void 0:c.length)||0,stopsData:(n==null?void 0:n.length)||0,disponibiliteTrendData:(m==null?void 0:m.length)||0,mttrCalendarData:(g==null?void 0:g.length)||0,downtimeParetoData:(h==null?void 0:h.length)||0,operatorStats:(d==null?void 0:d.length)||0,disponibiliteByMachineData:(y==null?void 0:y.length)||0,loading:N,activeTab:k==null?void 0:k.activeTab,selectedMachine:E,selectedMachineModel:b,dateFilterActive:I,dateRangeType:D,sampleStopsData:(n==null?void 0:n.slice(0,2))||[]});const P=M(k==null?void 0:k.activeTab);console.log(`🔍 ArretChartsSection - Active Tab "${k==null?void 0:k.activeTab}" Data:`,{dataLength:(P==null?void 0:P.length)||0,dataType:typeof P,isArray:Array.isArray(P),sampleData:(P==null?void 0:P.slice(0,2))||"No data",chartTitle:S(k==null?void 0:k.activeTab),chartType:w(k==null?void 0:k.activeTab)}),console.log("🔍 ArretChartsSection - stopsData sample:",n==null?void 0:n.slice(0,2)),console.log("🔍 ArretChartsSection - disponibiliteTrendData sample:",m==null?void 0:m.slice(0,2)),console.log("🔍 ArretChartsSection - downtimeParetoData sample:",h==null?void 0:h.slice(0,2)),console.log("🔍 ArretChartsSection - operatorStats sample:",d==null?void 0:d.slice(0,2)),console.log("🔍 ArretChartsSection - disponibiliteByMachineData sample:",y==null?void 0:y.slice(0,2))},[u,t,l,o,c,n,m,g,h,d,y,N,k,E,b,I,D]);const R=P=>{console.log("🎯 Chart type change requested:",P,"Current:",k==null?void 0:k.activeTab),C?C(z=>({...z,activeTab:P})):console.warn("⚠️ setChartOptions function not available")},M=P=>{let z,G;switch(P){case"bar":z=t,G=n;break;case"pie":z=u,G=n;break;case"trend":z=o,G=n;break;case"heatmap":z=c,G=n;break;case"horizontalBar":z=l,G=n;break;case"area":z=r,G=n;break;case"productionOrders":z=h,G=n;break;case"operators":z=d,G=n;break;case"machineEfficiency":z=y,G=n;break;case"timePatterns":z=g,G=n;break;case"durationDistribution":z=m,G=n;break;default:return[]}const Ae=Array.isArray(z)&&z.length>0,De=Array.isArray(G)&&G.length>0;return Ae?z:De?(console.log(`🔄 ArretChartsSection - Using fallback data for ${P}:`,{primaryDataLength:(z==null?void 0:z.length)||0,fallbackDataLength:(G==null?void 0:G.length)||0}),G):(console.warn(`⚠️ ArretChartsSection - No valid data for ${P}:`,{primaryData:(z==null?void 0:z.length)||0,fallbackData:(G==null?void 0:G.length)||0,hasValidPrimaryData:Ae,hasValidFallbackData:De,primaryDataType:typeof z,fallbackDataType:typeof G}),[])},w=P=>{switch(P){case"bar":return"bar";case"pie":return"pie";case"trend":return"line";case"heatmap":return"heatmap";case"horizontalBar":return"bar";case"area":return"area";default:return"bar"}},S=P=>{switch(P){case"bar":return"Comparaison Machines";case"pie":return"Top 5 Causes";case"trend":return"Evolution Arrêts";case"heatmap":return"Durée par Heure";case"horizontalBar":return"Causes d'Arrêt";case"area":return"Tendance Durée";case"productionOrders":return"Analyse Production";case"operators":return"Performance Opérateurs";case"machineEfficiency":return"Efficacité Machines";case"timePatterns":return"Motifs Temporels";case"durationDistribution":return"Distribution Durées";default:return"Graphique"}},v=[{key:"bar",tab:e.createElement("span",null,e.createElement(we,null),"Comparaison Machines"),content:e.createElement(Nr,{data:t,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"pie",tab:e.createElement("span",null,e.createElement(rr,null),"Top 5 Causes"),content:e.createElement(Yr,{data:u,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"trend",tab:e.createElement("span",null,e.createElement(ft,null),"Evolution Arrêts"),content:e.createElement(bt,{data:o,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY])})},{key:"heatmap",tab:e.createElement("span",null,e.createElement(nr,null),"Durée par Heure"),content:e.createElement(Lr,{data:c.length>0?c:n,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"horizontalBar",tab:e.createElement("span",null,e.createElement(we,null),"Causes d'Arrêt"),content:e.createElement(Pr,{data:l,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"productionOrders",tab:e.createElement("span",null,e.createElement(ar,null),"Analyse Production"),content:e.createElement(At,{data:h.length>0?h:n,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"operators",tab:e.createElement("span",null,e.createElement(Ne,null),"Performance Opérateurs"),content:e.createElement(Or,{data:d.length>0?d:n,loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"machineEfficiency",tab:e.createElement("span",null,e.createElement(Zt,null),"Efficacité Machines"),content:e.createElement(zr,{data:y.length>0?y:n.length>0?n:[],loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"timePatterns",tab:e.createElement("span",null,e.createElement(X,null),"Motifs Temporels"),content:e.createElement(Hr,{data:g.length>0?g:n.length>0?n:[],loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})},{key:"durationDistribution",tab:e.createElement("span",null,e.createElement(or,null),"Distribution Durées"),content:e.createElement(Gr,{data:m.length>0?m:n.length>0?n:[],loading:N,colors:s([i.PRIMARY_BLUE,i.SECONDARY_BLUE,i.CHART_TERTIARY,i.SUCCESS_GREEN,i.WARNING_ORANGE])})}];return e.createElement("div",{style:{marginBottom:"24px"}},e.createElement(V,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(H,{span:24},"          ",e.createElement(ne,{size:"small",style:{background:"#fafafa"}},e.createElement(j,{wrap:!0,size:"middle",style:{width:"100%",justifyContent:"center"}},v.map(P=>e.createElement(U,{key:P.key,type:k.activeTab===P.key?"primary":"default",icon:P.tab.props.children[0],onClick:()=>R(P.key),size:"large",style:{height:"52px",minWidth:"180px",borderRadius:"10px",fontWeight:k.activeTab===P.key?"bold":"normal",fontSize:"14px",boxShadow:k.activeTab===P.key?`0 4px 12px ${i.PRIMARY_BLUE}40`:"0 2px 6px rgba(0,0,0,0.1)",border:k.activeTab===P.key?`2px solid ${i.PRIMARY_BLUE}`:"1px solid #d9d9d9",transition:"all 0.3s ease"}},P.tab.props.children[1])))))),e.createElement(V,{gutter:[16,16]},e.createElement(H,{span:24},!1,e.createElement(_t,null,e.createElement(tr,{title:S(k.activeTab),data:M(k.activeTab),chartType:w(k.activeTab),expandMode:"modal",exportEnabled:!0,style:{borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",border:"1px solid #e8e8e8"},cardProps:{styles:{body:{padding:"24px"}}}},(O=v.find(P=>P.key===k.activeTab))==null?void 0:O.content)))),e.createElement("div",{style:{marginTop:"48px"}},e.createElement("div",{style:{textAlign:"center",marginBottom:"40px",padding:"32px",background:`linear-gradient(135deg, ${i.PRIMARY_BLUE} 0%, ${i.SECONDARY_BLUE} 100%)`,borderRadius:"24px",color:"white",boxShadow:`0 20px 60px ${i.PRIMARY_BLUE}40`,position:"relative",overflow:"hidden"}},e.createElement("div",{style:{position:"absolute",top:"-50%",right:"-10%",width:"300px",height:"300px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(40px)"}}),e.createElement("div",{style:{position:"absolute",bottom:"-30%",left:"-5%",width:"200px",height:"200px",background:"rgba(255,255,255,0.08)",borderRadius:"50%",filter:"blur(30px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement(Pe,{style:{fontSize:"36px",marginBottom:"16px",color:"#fff"}}),e.createElement("h2",{style:{margin:0,fontSize:"32px",fontWeight:"700",color:"white",letterSpacing:"0.5px"}},"Analyses Avancées"),e.createElement("p",{style:{margin:"12px 0 0 0",opacity:.95,fontSize:"18px",color:"white",fontWeight:"300"}},"Indicateurs clés de performance et analyses prédictives en temps réel"),"          ")),"        ",E?e.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(800px, 1fr))",gap:"40px",marginBottom:"50px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden",minHeight:"750px"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${i.PRIMARY_BLUE}, ${i.SECONDARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"📈")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Tendance de Disponibilité"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Évolution des performances de ",E))))),e.createElement("div",{style:{padding:"32px"}},m.length>0?e.createElement("div",{style:{display:"grid",gridTemplateRows:"1fr 1fr",gap:"30px",height:"600px"}},e.createElement("div",{style:{background:`linear-gradient(145deg, ${i.PRIMARY_BLUE}10, ${i.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"20px",border:`1px solid ${i.PRIMARY_BLUE}20`,position:"relative"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px"}},e.createElement("div",{style:{width:"4px",height:"28px",background:`linear-gradient(to bottom, ${i.PRIMARY_BLUE}, ${i.SECONDARY_BLUE})`,borderRadius:"2px",marginRight:"12px"}}),e.createElement("h4",{style:{margin:0,color:i.PRIMARY_BLUE,fontSize:"18px",fontWeight:"600"}},"Disponibilité (%)")),e.createElement("div",{style:{height:"calc(100% - 50px)"}},e.createElement(Dt,{data:m,loading:N}))),e.createElement("div",{style:{background:`linear-gradient(145deg, ${i.LIGHT_GRAY}10, ${i.DARK_GRAY}05)`,borderRadius:"16px",padding:"20px",border:`1px solid ${i.LIGHT_GRAY}20`,position:"relative"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px"}},e.createElement("div",{style:{width:"4px",height:"28px",background:`linear-gradient(to bottom, ${i.SECONDARY_BLUE}, ${i.PRIMARY_BLUE})`,borderRadius:"2px",marginRight:"12px"}}),e.createElement("h4",{style:{margin:0,color:i.SECONDARY_BLUE,fontSize:"18px",fontWeight:"600"}},"Temps Moyen de Réparation (MTTR)")),"                    ",e.createElement("div",{style:{height:"calc(100% - 50px)"}},e.createElement(Ct,{data:g,loading:N})))):e.createElement("div",{style:{height:"600px",display:"flex",alignItems:"center",justifyContent:"center",background:`linear-gradient(145deg, ${i.PRIMARY_BLUE}10, ${i.SECONDARY_BLUE}10)`,borderRadius:"16px"}},e.createElement($,{description:"Aucune donnée de disponibilité disponible",style:{color:"#8c8c8c"}})))),"          ",e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden",minHeight:"750px"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${i.DARK_GRAY}, ${i.LIGHT_GRAY})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"📊")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Analyse Pareto des Temps d'Arrêt"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Identification des causes principales"))))),"            ",e.createElement("div",{style:{padding:"32px"}},"              ",h.length>0?e.createElement("div",{style:{display:"grid",gridTemplateRows:"1fr 1fr",gap:"12px",height:"780px"}},"                  ",e.createElement("div",{style:{background:`linear-gradient(145deg, ${i.PRIMARY_BLUE}10, ${i.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"12px",border:`1px solid ${i.PRIMARY_BLUE}20`,position:"relative"}},"                    ",e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"}},"                      ",e.createElement("div",{style:{width:"4px",height:"18px",background:`linear-gradient(to bottom, ${i.PRIMARY_BLUE}, ${i.SECONDARY_BLUE})`,borderRadius:"2px",marginRight:"8px"}}),e.createElement("h4",{style:{margin:0,color:i.PRIMARY_BLUE,fontSize:"14px",fontWeight:"600"}},"Durée des Temps d'Arrêt (min)"),"                    "),"                    ",e.createElement("div",{style:{height:"calc(100% - 36px)"}},e.createElement(St,{data:h,loading:N}))),"                  ",e.createElement("div",{style:{background:`linear-gradient(145deg, ${i.SECONDARY_BLUE}10, ${i.PRIMARY_BLUE}10)`,borderRadius:"16px",padding:"12px",border:`1px solid ${i.SECONDARY_BLUE}20`,position:"relative"}},"                    ",e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"}},"                      ",e.createElement("div",{style:{width:"4px",height:"24px",background:`linear-gradient(to bottom, ${i.SECONDARY_BLUE}, ${i.PRIMARY_BLUE})`,borderRadius:"2px",marginRight:"10px"}}),e.createElement("h4",{style:{margin:0,color:i.SECONDARY_BLUE,fontSize:"16px",fontWeight:"600"}},"Impact Cumulé (Pareto %)"),"                    "),"                    ",e.createElement("div",{style:{height:"calc(100% - 36px)"}},e.createElement(Tt,{data:h,loading:N})))):e.createElement("div",{style:{height:"600px",display:"flex",alignItems:"center",justifyContent:"center",background:`linear-gradient(145deg, ${i.PRIMARY_BLUE}10, ${i.SECONDARY_BLUE}10)`,borderRadius:"16px"}},e.createElement($,{description:"Aucune donnée de temps d'arrêt disponible",style:{color:"#8c8c8c"}}))),"          ")):e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"80px 40px",marginBottom:"50px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"60px 80px",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"2px dashed #d9d9d9",textAlign:"center",maxWidth:"600px",width:"100%"}},e.createElement("div",{style:{fontSize:"64px",marginBottom:"24px",opacity:.6}},"🏭"),e.createElement("h3",{style:{margin:"0 0 16px 0",fontSize:"24px",fontWeight:"600",color:"#595959"}},"Sélectionnez une Machine"),e.createElement("p",{style:{margin:0,fontSize:"16px",color:"#8c8c8c",lineHeight:"1.6"}},"Pour afficher les analyses avancées de disponibilité, MTTR et métriques de performance,",e.createElement("br",null),"veuillez sélectionner une machine spécifique dans les filtres ci-dessus."))),E?e.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(700px, 1fr))",gap:"40px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${i.PRIMARY_BLUE}, ${i.SECONDARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"🎯")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Métriques de Performance"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Indicateurs clés temps réel"))))),"            ",e.createElement("div",{style:{padding:"32px",height:"500px"}},e.createElement("div",{style:{height:"100%",background:`linear-gradient(145deg, ${i.PRIMARY_BLUE}10, ${i.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"20px",border:`1px solid ${i.PRIMARY_BLUE}20`}},e.createElement(Rt,{mttr:x,mtbf:_,doper:Y,loading:N})))),"          ",e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden"}},e.createElement("div",{style:{background:`linear-gradient(135deg, ${i.SECONDARY_BLUE}, ${i.PRIMARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.createElement("div",{style:{position:"relative",zIndex:1}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},e.createElement("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"}},e.createElement("span",{style:{fontSize:"24px"}},"📅")),e.createElement("div",null,e.createElement("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"}},"Calendrier MTTR"),e.createElement("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"}},"Vue mensuelle des réparations"))))),"            ",e.createElement("div",{style:{padding:"24px",height:"650px"}},"              ",e.createElement("div",{style:{height:"100%",background:"linear-gradient(145deg, #e6fffb, #e0f7fa)",borderRadius:"16px",padding:"20px",border:"1px solid rgba(19, 194, 194, 0.1)",overflow:"hidden"}},e.createElement(er,{data:g,loading:N,selectedDate:A,selectedMachine:E,dateRangeType:D}))))):e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"40px"}},e.createElement("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"16px",padding:"40px 60px",boxShadow:"0 10px 30px rgba(0,0,0,0.05)",border:"1px dashed #d9d9d9",textAlign:"center",maxWidth:"500px"}},e.createElement("div",{style:{fontSize:"48px",marginBottom:"16px",opacity:.5}},"📊"),e.createElement("h4",{style:{margin:"0 0 12px 0",fontSize:"18px",fontWeight:"600",color:"#595959"}},"Métriques de Performance Indisponibles"),e.createElement("p",{style:{margin:0,fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5"}},"Les métriques de performance et le calendrier MTTR nécessitent la sélection d'une machine.")))))},{Text:Q}=oe,{useBreakpoint:Wr}=Yt,{TabPane:st}=Je,Mt=T.memo(()=>{const{stopsData:a,loading:s}=le(),p=Wr(),f=r=>r?r.toLowerCase().includes("non déclaré")?"error":r.toLowerCase().includes("maintenance")?"warning":r.toLowerCase().includes("changement")?"processing":r.toLowerCase().includes("réglage")?"cyan":r.toLowerCase().includes("problème")?"orange":"default":"default",o=a.map(r=>{let t=r.Date_Insert,l=r.Debut_Stop,n=r.Fin_Stop_Time;const c=d=>{if(!d)return null;const m=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const g of m)if(W(d,g).isValid())return d;return W(d).isValid()?d:null};return t=c(t),l=c(l),n=c(n),{...r,Date_Insert:t,Machine_Name:r.Machine_Name||"N/A",Part_No:r.Part_NO||"N/A",Code_Stop:r.Code_Stop||"N/A",Debut_Stop:l,Fin_Stop_Time:n,Regleur_Prenom:r.Regleur_Prenom||"Non assigné",duration_minutes:r.duration_minutes||null}}),u=[{title:"Date",dataIndex:"Date_Insert",key:"Date_Insert",render:r=>{if(!r)return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let l=null;for(const n of t){const c=W(r,n);if(c.isValid()){l=c;break}}return l||(l=W(r)),l&&l.isValid()?l.format("DD/MM/YYYY"):e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}},sorter:(r,t)=>{try{if(!r.Date_Insert||!t.Date_Insert)return 0;const l=d=>{if(!d)return null;const m=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const g of m){const y=W(d,g);if(y.isValid())return y}const h=W(d);return h.isValid()?h:null},n=l(r.Date_Insert),c=l(t.Date_Insert);return!n||!c||!n.isValid()||!c.isValid()?0:n.unix()-c.unix()}catch{return 0}}},{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:r=>e.createElement(re,{color:"blue"},r||"N/A"),filters:[...new Set(o.map(r=>r.Machine_Name).filter(Boolean))].map(r=>({text:r,value:r})),onFilter:(r,t)=>t.Machine_Name===r},{title:"OF",dataIndex:"Part_No",key:"Part_No",render:r=>r&&r!=="N/A"?r:e.createElement(Q,{type:"secondary"},"Non spécifié"),responsive:["md"]},{title:"Code Arrêt",dataIndex:"Code_Stop",key:"Code_Stop",render:r=>e.createElement(dt,{status:f(r),text:r||"N/A"}),filters:[...new Set(o.map(r=>r.Code_Stop).filter(Boolean))].map(r=>({text:r,value:r})),onFilter:(r,t)=>t.Code_Stop===r},{title:"Début",dataIndex:"Debut_Stop",key:"Debut_Stop",render:r=>{if(!r)return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let l=null;for(const n of t){const c=W(r,n);if(c.isValid()){l=c;break}}return l||(l=W(r)),l&&l.isValid()?l.format("HH:mm"):e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}}},{title:"Fin",dataIndex:"Fin_Stop_Time",key:"Fin_Stop_Time",render:r=>{if(!r)return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let l=null;for(const n of t){const c=W(r,n);if(c.isValid()){l=c;break}}return l||(l=W(r)),l&&l.isValid()?l.format("HH:mm"):e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}}},{title:"Durée",key:"duration",render:(r,t)=>{if(t.duration_minutes!==null&&t.duration_minutes!==void 0)return`${t.duration_minutes} min`;if(!t.Debut_Stop||!t.Fin_Stop_Time)return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable");try{const l=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let n=null,c=null;for(const m of l){const h=W(t.Debut_Stop,m);if(h.isValid()){n=h;break}}n||(n=W(t.Debut_Stop));for(const m of l){const h=W(t.Fin_Stop_Time,m);if(h.isValid()){c=h;break}}if(c||(c=W(t.Fin_Stop_Time)),!n.isValid()||!c.isValid())return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable");const d=c.diff(n,"minute");return d<0?e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable"):`${d} min`}catch{return e.createElement(Q,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable")}},sorter:(r,t)=>{if(r.duration_minutes!==null&&r.duration_minutes!==void 0&&t.duration_minutes!==null&&t.duration_minutes!==void 0)return r.duration_minutes-t.duration_minutes;try{if(!r.Debut_Stop||!r.Fin_Stop_Time||!t.Debut_Stop||!t.Fin_Stop_Time)return 0;const l=y=>{if(!y)return null;const E=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const A of E){const D=W(y,A);if(D.isValid())return D}const b=W(y);return b.isValid()?b:null},n=l(r.Debut_Stop),c=l(r.Fin_Stop_Time),d=l(t.Debut_Stop),m=l(t.Fin_Stop_Time);if(!n||!c||!d||!m||!n.isValid()||!c.isValid()||!d.isValid()||!m.isValid())return 0;const h=c.diff(n,"minute"),g=m.diff(d,"minute");return h-g}catch{return 0}}},{title:"Responsable",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:r=>r||"Non assigné",filters:[...new Set(o.map(r=>r.Regleur_Prenom||"Non assigné").filter(Boolean))].map(r=>({text:r,value:r==="Non assigné"?null:r})),onFilter:(r,t)=>r?t.Regleur_Prenom===r:!t.Regleur_Prenom}];return e.createElement(ne,{title:"Tableaux de Données",bordered:!1,className:"arret-data-table"},e.createElement(Je,{defaultActiveKey:"arrets",size:"large"},e.createElement(st,{tab:"📋 Tableau des Arrêts",key:"arrets"},e.createElement(mt,{columns:u,dataSource:o,loading:s,pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:r=>`Total ${r} arrêts`},scroll:{x:p.md?void 0:1e3},bordered:!0,size:"middle",rowKey:(r,t)=>`${r.Date_Insert}-${t}`,rowClassName:r=>r.Code_Stop&&r.Code_Stop.toLowerCase().includes("non déclaré")?"table-row-error":""})),e.createElement(st,{tab:"👥 Statistiques des Opérateurs",key:"operators"},e.createElement(xt,null))),e.createElement("style",{jsx:!0},`
        .table-row-error {
          background-color: rgba(245, 34, 45, 0.05);
        }
        .ant-table-row:hover {
          cursor: pointer;
          background-color: rgba(24, 144, 255, 0.05) !important;
        }
      `))});Mt.displayName="ArretDataTable";const jr=()=>{const{isSearchModalVisible:a=!1,setIsSearchModalVisible:s,searchResults:p=[],performGlobalSearch:f,searchLoading:o=!1}=le()||{},u=async r=>{f&&await f(r)};return e.createElement(Pt,{visible:a,onClose:()=>s&&s(!1),onSearch:u,results:p,loading:o,searchContext:"arrets"})},{Text:de,Title:Kr}=oe,Vr=()=>{const a=le();if(!a)return e.createElement("div",null,"Context not available");const{mttr:s=0,mtbf:p=0,doper:f=0,showPerformanceMetrics:o=!1,selectedMachine:u,loading:r=!1}=a;if(!u||!o&&s===0&&p===0&&f===0)return null;const t=(m,h)=>{switch(h){case"mttr":return m<=30?"success":m<=60?"warning":"error";case"mtbf":return m>=120?"success":m>=60?"warning":"error";case"doper":return m>=85?"success":m>=75?"warning":"error";default:return"default"}},l=m=>{switch(m){case"success":return e.createElement(be,{style:{color:"#52c41a"}});case"warning":return e.createElement(Ie,{style:{color:i.SECONDARY_BLUE}});case"error":return e.createElement(Ie,{style:{color:"#ff4d4f"}});default:return e.createElement(ue,{style:{color:i.LIGHT_GRAY}})}},n=t(s,"mttr"),c=t(p,"mtbf"),d=t(f,"doper");return e.createElement("div",{style:{marginBottom:"24px"}},"      ",e.createElement(ut,{message:e.createElement(j,null,e.createElement(ue,{style:{color:i.PRIMARY_BLUE}}),e.createElement(de,{strong:!0,style:{color:i.DARK_GRAY}},"Indicateurs de Performance - Machine ",u||"Sélectionnée")),description:e.createElement("span",{style:{color:i.LIGHT_GRAY}},"Ces métriques évaluent la performance et la disponibilité de la machine sélectionnée"),type:"info",showIcon:!1,style:{marginBottom:"16px",borderRadius:"8px",backgroundColor:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`}}),e.createElement(V,{gutter:[16,16]},e.createElement(H,{xs:24,sm:8},e.createElement(ne,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${i.PRIMARY_BLUE}`,borderTop:`3px solid ${n==="success"?"#52c41a":n==="warning"?i.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"}},e.createElement(Me,{title:e.createElement(j,null,e.createElement(X,{style:{color:i.PRIMARY_BLUE}})," ",e.createElement("span",{style:{color:i.DARK_GRAY}},"MTTR (Temps Moyen de Réparation)")," ",e.createElement(xe,{title:"Temps moyen nécessaire pour réparer une panne. Plus faible = mieux."},e.createElement(ue,{style:{color:i.LIGHT_GRAY}})," ")),value:s,precision:1,suffix:"min",loading:r,valueStyle:{color:n==="success"?"#52c41a":n==="warning"?i.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:l(n)}),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(de,{style:{fontSize:"12px",color:i.LIGHT_GRAY}},n==="success"&&"Excellent - Réparations rapides",n==="warning"&&"Correct - Peut être amélioré",n==="error"&&"Attention - Réparations lentes")))),e.createElement(H,{xs:24,sm:8},e.createElement(ne,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${i.PRIMARY_BLUE}`,borderTop:`3px solid ${c==="success"?"#52c41a":c==="warning"?i.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"}},e.createElement(Me,{title:e.createElement(j,null,e.createElement(Ye,{style:{color:i.PRIMARY_BLUE}})," ",e.createElement("span",{style:{color:i.DARK_GRAY}},"MTBF (Temps Moyen Entre Pannes)")," ",e.createElement(xe,{title:"Temps moyen de fonctionnement entre deux pannes. Plus élevé = mieux."},e.createElement(ue,{style:{color:i.LIGHT_GRAY}})," ")),value:p,precision:1,suffix:"min",loading:r,valueStyle:{color:c==="success"?"#52c41a":c==="warning"?i.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:l(c)}),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(de,{style:{fontSize:"12px",color:i.LIGHT_GRAY}},c==="success"&&"Excellent - Machine fiable",c==="warning"&&"Correct - Surveillance recommandée",c==="error"&&"Attention - Pannes fréquentes")))),e.createElement(H,{xs:24,sm:8},e.createElement(ne,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${i.PRIMARY_BLUE}`,borderTop:`3px solid ${d==="success"?"#52c41a":d==="warning"?i.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"}},e.createElement(Me,{title:e.createElement(j,null,e.createElement(Pe,{style:{color:i.PRIMARY_BLUE}})," ",e.createElement("span",{style:{color:i.DARK_GRAY}},"DOPER (Disponibilité)")," ",e.createElement(xe,{title:"Pourcentage de temps où la machine est opérationnelle. Plus élevé = mieux."},e.createElement(ue,{style:{color:i.LIGHT_GRAY}})," ")),value:f,precision:1,suffix:"%",loading:r,valueStyle:{color:d==="success"?"#52c41a":d==="warning"?i.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:l(d)}),e.createElement("div",{style:{marginTop:"8px"}},e.createElement(de,{style:{fontSize:"12px",color:i.LIGHT_GRAY}},d==="success"&&"Excellent - Très disponible",d==="warning"&&"Correct - Peut être optimisé",d==="error"&&"Attention - Disponibilité faible"))))),e.createElement(V,{style:{marginTop:"16px"}},e.createElement(H,{span:24},e.createElement(ne,{size:"small",style:{backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${i.LIGHT_GRAY}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement(Kr,{level:5,style:{margin:0,color:i.DARK_GRAY}},e.createElement(ue,{style:{marginRight:"8px",color:i.PRIMARY_BLUE}}),"Guide d'interprétation"),e.createElement(V,{gutter:[16,8],style:{marginTop:"12px"}},e.createElement(H,{xs:24,md:8},e.createElement(de,{style:{fontSize:"12px",color:i.DARK_GRAY}},e.createElement(be,{style:{color:"#52c41a",marginRight:"4px"}}),e.createElement("strong",null,"MTTR optimal:")," < 30 min")),e.createElement(H,{xs:24,md:8},e.createElement(de,{style:{fontSize:"12px",color:i.DARK_GRAY}},e.createElement(be,{style:{color:"#52c41a",marginRight:"4px"}}),e.createElement("strong",null,"MTBF optimal:")," > 120 min")),e.createElement(H,{xs:24,md:8},e.createElement(de,{style:{fontSize:"12px",color:i.DARK_GRAY}},e.createElement(be,{style:{color:"#52c41a",marginRight:"4px"}}),e.createElement("strong",null,"DOPER optimal:")," > 85%")))))))},{Content:ct}=Qe,qr=()=>{gt(),ye();const a=le(),s=T.useCallback(I=>{if((I==null?void 0:I.model)&&(I==null?void 0:I.machine)&&(I==null?void 0:I.dateFilterActive)){const R=setTimeout(()=>{console.error("🚨 Potential freeze detected - page unresponsive for 10 seconds with triple filters"),console.warn("Performance issue detected with triple filters")},1e4),M=()=>{clearTimeout(R)};window.clearFreezeDetection=M,a&&a.graphQL&&a.graphQL.getCacheStats&&a.graphQL.getCacheStats()}},[a]);if(!a)return e.createElement("div",null,"Chargement du contexte...");const{loading:p,essentialLoading:f,detailedLoading:o,complexFilterLoading:u,error:r,totalStops:t,undeclaredStops:l,avgDuration:n,totalDuration:c,sidebarStats:d,arretStats:m,topStopsData:h,arretsByRange:g,stopReasons:y,stopsData:E,selectedMachine:b,selectedMachineModel:A,selectedDate:D,dateRangeType:x,dateFilterActive:_,handleRefresh:Y}=a;if(e.useEffect(()=>{if(A&&b&&D){window.clearFreezeDetection&&window.clearFreezeDetection();const F=performance.now();setTimeout(()=>{const R=performance.now()-F;R>1e3&&(console.warn(`🐌 Slow render detected: ${R.toFixed(2)}ms with triple filters`),a.graphQL&&a.graphQL.getCacheStats&&a.graphQL.getCacheStats())},0)}},[A,b,D,x,_,E==null?void 0:E.length,p,f,o,u,a.graphQL]),r)return e.createElement(Qe,{style:{minHeight:"100vh",background:"#f0f2f5"}},e.createElement(ct,{style:{padding:"24px"}},e.createElement("div",{style:{maxWidth:"1400px",margin:"0 auto",textAlign:"center",paddingTop:"50px"}},e.createElement("div",{style:{backgroundColor:"#FFFFFF",border:`1px solid ${i.PRIMARY_BLUE}`,borderRadius:"8px",padding:"24px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("h3",{style:{color:i.DARK_GRAY,marginBottom:"16px"}},"Erreur de chargement"),e.createElement("p",{style:{color:i.LIGHT_GRAY,marginBottom:"20px"}},r),e.createElement("button",{onClick:Y,style:{marginTop:"10px",backgroundColor:i.PRIMARY_BLUE,color:"#FFFFFF",border:"none",padding:"8px 16px",borderRadius:"6px",cursor:"pointer"}},"Réessayer"))),"        "));const N=f||p,C=A&&b&&D||u;return e.createElement(Qe,{style:{minHeight:"100vh",background:"#f0f2f5"}},e.createElement(ct,{style:{padding:"24px"}},e.createElement("div",{style:{maxWidth:"1400px",margin:"0 auto"}},C&&(u||p)&&e.createElement("div",{style:{position:"fixed",top:"20px",right:"20px",background:"#FFFFFF",padding:"12px 20px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",zIndex:1e3,border:`1px solid ${i.PRIMARY_BLUE}`}},e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},e.createElement("div",{style:{width:"16px",height:"16px",border:"2px solid #f3f3f3",borderTop:`2px solid ${i.PRIMARY_BLUE}`,borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.createElement("span",{style:{fontSize:"14px",color:i.DARK_GRAY}},"Processing complex filters..."))),e.createElement(me,{priority:0},e.createElement(_r,null)),e.createElement(me,{priority:0},e.createElement(vr,{onFilterChange:s})),e.createElement(me,{priority:1,delay:100,height:120,loadingType:"skeleton",title:"Loading statistics..."},e.createElement(Mr,{loading:f})),b&&e.createElement(me,{priority:2,delay:200,height:180,loadingType:"skeleton",title:"Loading performance metrics..."},e.createElement(Vr,{loading:N})),e.createElement(me,{priority:3,delay:300,height:400,loadingType:"skeleton",title:"Loading charts..."},e.createElement(Ur,{loading:o||C&&p})),e.createElement(me,{priority:4,delay:400,height:500,loadingType:"skeleton",title:"Loading data table..."},e.createElement(Mt,{loading:o||C&&p})),e.createElement(jr,null))),e.createElement("style",{jsx:!0},`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `))},Bn=()=>e.createElement(_t,null,e.createElement(Dr,null,e.createElement(qr,null)));export{Bn as default};
