var Zn=Object.defineProperty;var Jn=(i,t,e)=>t in i?Zn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var w=(i,t,e)=>Jn(i,typeof t!="symbol"?t+"":t,e);import{r as ot,R as en}from"./index-LbZyOyVE.js";/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka <PERSON>
 * Released under the MIT License
 */function fe(i){return i+.5|0}const mt=(i,t,e)=>Math.max(Math.min(i,e),t);function Gt(i){return mt(fe(i*2.55),0,255)}function _t(i){return mt(fe(i*255),0,255)}function ft(i){return mt(fe(i/2.55)/100,0,1)}function Fi(i){return mt(fe(i*100),0,100)}const J={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ai=[..."0123456789ABCDEF"],Qn=i=>ai[i&15],to=i=>ai[(i&240)>>4]+ai[i&15],ge=i=>(i&240)>>4===(i&15),eo=i=>ge(i.r)&&ge(i.g)&&ge(i.b)&&ge(i.a);function io(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&J[i[1]]*17,g:255&J[i[2]]*17,b:255&J[i[3]]*17,a:t===5?J[i[4]]*17:255}:(t===7||t===9)&&(e={r:J[i[1]]<<4|J[i[2]],g:J[i[3]]<<4|J[i[4]],b:J[i[5]]<<4|J[i[6]],a:t===9?J[i[7]]<<4|J[i[8]]:255})),e}const so=(i,t)=>i<255?t(i):"";function no(i){var t=eo(i)?Qn:to;return i?"#"+t(i.r)+t(i.g)+t(i.b)+so(i.a,t):void 0}const oo=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function sn(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function ro(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function ao(i,t,e){const s=sn(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function lo(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function mi(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=lo(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function bi(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(_t)}function _i(i,t,e){return bi(sn,i,t,e)}function co(i,t,e){return bi(ao,i,t,e)}function ho(i,t,e){return bi(ro,i,t,e)}function nn(i){return(i%360+360)%360}function fo(i){const t=oo.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?Gt(+t[5]):_t(+t[5]));const n=nn(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=co(n,o,r):t[1]==="hsv"?s=ho(n,o,r):s=_i(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function uo(i,t){var e=mi(i);e[0]=nn(e[0]+t),e=_i(e),i.r=e[0],i.g=e[1],i.b=e[2]}function go(i){if(!i)return;const t=mi(i),e=t[0],s=Fi(t[1]),n=Fi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${ft(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const Ii={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Ei={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function po(){const i={},t=Object.keys(Ei),e=Object.keys(Ii);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,Ii[o]);o=parseInt(Ei[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let pe;function mo(i){pe||(pe=po(),pe.transparent=[0,0,0,0]);const t=pe[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const bo=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function _o(i){const t=bo.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?Gt(r):mt(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?Gt(s):mt(s,0,255)),n=255&(t[4]?Gt(n):mt(n,0,255)),o=255&(t[6]?Gt(o):mt(o,0,255)),{r:s,g:n,b:o,a:e}}}function xo(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${ft(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const Xe=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Et=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function yo(i,t,e){const s=Et(ft(i.r)),n=Et(ft(i.g)),o=Et(ft(i.b));return{r:_t(Xe(s+e*(Et(ft(t.r))-s))),g:_t(Xe(n+e*(Et(ft(t.g))-n))),b:_t(Xe(o+e*(Et(ft(t.b))-o))),a:i.a+e*(t.a-i.a)}}function me(i,t,e){if(i){let s=mi(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=_i(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function on(i,t){return i&&Object.assign(t||{},i)}function zi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=_t(i[3]))):(t=on(i,{r:0,g:0,b:0,a:1}),t.a=_t(t.a)),t}function vo(i){return i.charAt(0)==="r"?_o(i):fo(i)}class ne{constructor(t){if(t instanceof ne)return t;const e=typeof t;let s;e==="object"?s=zi(t):e==="string"&&(s=io(t)||mo(t)||vo(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=on(this._rgb);return t&&(t.a=ft(t.a)),t}set rgb(t){this._rgb=zi(t)}rgbString(){return this._valid?xo(this._rgb):void 0}hexString(){return this._valid?no(this._rgb):void 0}hslString(){return this._valid?go(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=yo(this._rgb,t._rgb,e)),this}clone(){return new ne(this.rgb)}alpha(t){return this._rgb.a=_t(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=fe(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return me(this._rgb,2,t),this}darken(t){return me(this._rgb,2,-t),this}saturate(t){return me(this._rgb,1,t),this}desaturate(t){return me(this._rgb,1,-t),this}rotate(t){return uo(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function ct(){}const Mo=(()=>{let i=0;return()=>i++})();function R(i){return i==null}function H(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function A(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function K(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function st(i,t){return K(i)?i:t}function O(i,t){return typeof i>"u"?t:i}const ko=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,rn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function I(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function L(i,t,e,s){let n,o,r;if(H(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(A(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function Le(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Fe(i){if(H(i))return i.map(Fe);if(A(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=Fe(i[e[n]]);return t}return i}function an(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function So(i,t,e,s){if(!an(i))return;const n=t[i],o=e[i];A(n)&&A(o)?oe(n,o,s):t[i]=Fe(o)}function oe(i,t,e){const s=H(t)?t:[t],n=s.length;if(!A(i))return i;e=e||{};const o=e.merger||So;let r;for(let a=0;a<n;++a){if(r=s[a],!A(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function te(i,t){return oe(i,t,{merger:wo})}function wo(i,t,e){if(!an(i))return;const s=t[i],n=e[i];A(s)&&A(n)?te(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=Fe(n))}const Bi={"":i=>i,x:i=>i.x,y:i=>i.y};function Po(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Do(i){const t=Po(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function Tt(i,t){return(Bi[t]||(Bi[t]=Do(t)))(i)}function xi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const re=i=>typeof i<"u",xt=i=>typeof i=="function",Wi=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Oo(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const B=Math.PI,z=2*B,Co=z+B,Ie=Number.POSITIVE_INFINITY,Ao=B/180,N=B/2,Mt=B/4,Hi=B*2/3,ln=Math.log10,lt=Math.sign;function ee(i,t,e){return Math.abs(i-t)<e}function Vi(i){const t=Math.round(i);i=ee(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(ln(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function To(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Ro(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function ae(i){return!Ro(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function Lo(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Fo(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ut(i){return i*(B/180)}function Io(i){return i*(180/B)}function Ni(i){if(!K(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function cn(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*B&&(o+=z),{angle:o,distance:n}}function li(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Eo(i,t){return(i-t+Co)%z-B}function at(i){return(i%z+z)%z}function le(i,t,e,s){const n=at(i),o=at(t),r=at(e),a=at(o-n),l=at(r-n),c=at(n-o),h=at(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function X(i,t,e){return Math.max(t,Math.min(e,i))}function zo(i){return X(i,-32768,32767)}function gt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function yi(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Dt=(i,t,e,s)=>yi(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Bo=(i,t,e)=>yi(i,e,s=>i[s][t]>=e);function Wo(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const hn=["push","pop","shift","splice","unshift"];function Ho(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),hn.forEach(e=>{const s="_onData"+xi(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function ji(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(hn.forEach(o=>{delete i[o]}),delete i._chartjs)}function dn(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const fn=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function un(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,fn.call(window,()=>{s=!1,i.apply(t,e)}))}}function Vo(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const vi=i=>i==="start"?"left":i==="end"?"right":"center",Y=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,No=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function jo(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:r,vScale:a,_parsed:l}=i,c=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=r.axis,{min:d,max:f,minDefined:u,maxDefined:g}=r.getUserBounds();if(u){if(n=Math.min(Dt(l,h,d).lo,e?s:Dt(t,h,r.getPixelForValue(d)).lo),c){const p=l.slice(0,n+1).reverse().findIndex(m=>!R(m[a.axis]));n-=Math.max(0,p)}n=X(n,0,s-1)}if(g){let p=Math.max(Dt(l,r.axis,f,!0).hi+1,e?0:Dt(t,h,r.getPixelForValue(f),!0).hi+1);if(c){const m=l.slice(p-1).findIndex(b=>!R(b[a.axis]));p+=Math.max(0,m)}o=X(p,n,s)-n}else o=s-n}return{start:n,count:o}}function $o(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const be=i=>i===0||i===1,$i=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*z/e)),Yi=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*z/e)+1,ie={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*N)+1,easeOutSine:i=>Math.sin(i*N),easeInOutSine:i=>-.5*(Math.cos(B*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>be(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>be(i)?i:$i(i,.075,.3),easeOutElastic:i=>be(i)?i:Yi(i,.075,.3),easeInOutElastic(i){return be(i)?i:i<.5?.5*$i(i*2,.1125,.45):.5+.5*Yi(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-ie.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?ie.easeInBounce(i*2)*.5:ie.easeOutBounce(i*2-1)*.5+.5};function Mi(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Xi(i){return Mi(i)?i:new ne(i)}function Ue(i){return Mi(i)?i:new ne(i).saturate(.5).darken(.1).hexString()}const Yo=["x","y","borderWidth","radius","tension"],Xo=["color","borderColor","backgroundColor"];function Uo(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:Xo},numbers:{type:"number",properties:Yo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Ko(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Ui=new Map;function qo(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Ui.get(e);return s||(s=new Intl.NumberFormat(i,t),Ui.set(e,s)),s}function ki(i,t,e){return qo(t,e).format(i)}const Go={values(i){return H(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Zo(i,e)}const r=ln(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ki(i,s,l)}};function Zo(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var gn={formatters:Go};function Jo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:gn.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Rt=Object.create(null),ci=Object.create(null);function se(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ke(i,t,e){return typeof t=="string"?oe(se(i,t),e):oe(se(i,""),t)}class Qo{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Ue(n.backgroundColor),this.hoverBorderColor=(s,n)=>Ue(n.borderColor),this.hoverColor=(s,n)=>Ue(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ke(this,t,e)}get(t){return se(this,t)}describe(t,e){return Ke(ci,t,e)}override(t,e){return Ke(Rt,t,e)}route(t,e,s,n){const o=se(this,t),r=se(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return A(l)?Object.assign({},c,l):O(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var V=new Qo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Uo,Ko,Jo]);function tr(i){return!i||R(i.size)||R(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Ki(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function kt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function qi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function hi(i,t,e,s){pn(i,t,e,s,null)}function pn(i,t,e,s,n){let o,r,a,l,c,h,d,f;const u=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*Ao;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,p,0,0,z):i.arc(e,s,p,0,z),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=Hi,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=Hi,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+Mt)*l,d=Math.cos(m+Mt)*(n?n/2-c:l),a=Math.sin(m+Mt)*l,f=Math.sin(m+Mt)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-B,m-N),i.arc(e+f,s-r,c,m-N,m),i.arc(e+d,s+a,c,m,m+N),i.arc(e-f,s+r,c,m+N,m+B),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=Mt;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+f,s-r),i.lineTo(e+d,s+a),i.lineTo(e-f,s+r),i.closePath();break;case"crossRot":m+=Mt;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r),m+=Mt,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function ce(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Ve(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function Ne(i){i.restore()}function er(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function ir(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function sr(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),R(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function nr(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function or(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function he(i,t,e,s,n,o={}){const r=H(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,sr(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&or(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),R(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),nr(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function Ee(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*B,B,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,B,N,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,N,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-N,!0),i.lineTo(e+r.topLeft,s)}const rr=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ar=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function lr(i,t){const e=(""+i).match(rr);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const cr=i=>+i||0;function Si(i,t){const e={},s=A(t),n=s?Object.keys(t):t,o=A(i)?s?r=>O(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=cr(o(r));return e}function mn(i){return Si(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Bt(i){return Si(i,["topLeft","topRight","bottomLeft","bottomRight"])}function tt(i){const t=mn(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function U(i,t){i=i||{},t=t||V.font;let e=O(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=O(i.style,t.style);s&&!(""+s).match(ar)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:O(i.family,t.family),lineHeight:lr(O(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:O(i.weight,t.weight),string:""};return n.string=tr(n),n}function _e(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&(t!==void 0&&typeof r=="function"&&(r=r(t)),e!==void 0&&H(r)&&(r=r[e%r.length]),r!==void 0))return r}function hr(i,t,e){const{min:s,max:n}=i,o=rn(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Lt(i,t){return Object.assign(Object.create(i),t)}function wi(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=yn("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>wi([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return _n(a,l,()=>_r(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return Zi(a).includes(l)},ownKeys(a){return Zi(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Ht(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:bn(i,s),setContext:o=>Ht(i,o,e,s),override:o=>Ht(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return _n(o,r,()=>fr(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function bn(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:xt(e)?e:()=>e,isIndexable:xt(s)?s:()=>s}}const dr=(i,t)=>i?i+xi(t):t,Pi=(i,t)=>A(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function _n(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function fr(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return xt(a)&&r.isScriptable(t)&&(a=ur(t,a,i,e)),H(a)&&a.length&&(a=gr(t,a,i,r.isIndexable)),Pi(t,a)&&(a=Ht(a,n,o&&o[t],r)),a}function ur(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),Pi(i,l)&&(l=Di(n._scopes,n,i,l)),l}function gr(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(A(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=Di(c,n,i,h);t.push(Ht(d,o,r&&r[i],a))}}return t}function xn(i,t,e){return xt(i)?i(t,e):i}const pr=(i,t)=>i===!0?t:typeof i=="string"?Tt(t,i):void 0;function mr(i,t,e,s,n){for(const o of t){const r=pr(e,o);if(r){i.add(r);const a=xn(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function Di(i,t,e,s){const n=t._rootScopes,o=xn(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=Gi(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Gi(a,r,o,l,s),l===null)?!1:wi(Array.from(a),[""],n,o,()=>br(t,e,s))}function Gi(i,t,e,s,n){for(;e;)e=mr(i,t,e,s,n);return e}function br(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return H(n)&&A(e)?e:n||{}}function _r(i,t,e,s){let n;for(const o of t)if(n=yn(dr(o,i),e),typeof n<"u")return Pi(i,n)?Di(e,s,i,n):n}function yn(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Zi(i){let t=i._keys;return t||(t=i._keys=xr(i._scopes)),t}function xr(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const yr=Number.EPSILON||1e-14,Vt=(i,t)=>t<i.length&&!i[t].skip&&i[t],vn=i=>i==="x"?"y":"x";function vr(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=li(o,n),l=li(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,f=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+f*(r.x-n.x),y:o.y+f*(r.y-n.y)}}}function Mr(i,t,e){const s=i.length;let n,o,r,a,l,c=Vt(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Vt(i,h+1),!(!l||!c)){if(ee(t[h],0,yr)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function kr(i,t,e="x"){const s=vn(e),n=i.length;let o,r,a,l=Vt(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Vt(i,c+1),!a)continue;const h=a[e],d=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=d+o*t[c])}}function Sr(i,t="x"){const e=vn(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=Vt(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=Vt(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?lt(n[r-1])!==lt(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}Mr(i,n,o),kr(i,o,t)}function xe(i,t,e){return Math.max(Math.min(i,e),t)}function wr(i,t){let e,s,n,o,r,a=ce(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&ce(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=xe(n.cp1x,t.left,t.right),n.cp1y=xe(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=xe(n.cp2x,t.left,t.right),n.cp2y=xe(n.cp2y,t.top,t.bottom)))}function Pr(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")Sr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=vr(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&wr(i,e)}function Oi(){return typeof window<"u"&&typeof document<"u"}function Ci(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function ze(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const je=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function Dr(i,t){return je(i).getPropertyValue(t)}const Or=["top","right","bottom","left"];function Ct(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=Or[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Cr=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Ar(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(Cr(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function wt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=je(e),o=n.boxSizing==="border-box",r=Ct(n,"padding"),a=Ct(n,"border","width"),{x:l,y:c,box:h}=Ar(i,e),d=r.left+(h&&a.left),f=r.top+(h&&a.top);let{width:u,height:g}=t;return o&&(u-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/u*e.width/s),y:Math.round((c-f)/g*e.height/s)}}function Tr(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&Ci(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=je(o),l=Ct(a,"border","width"),c=Ct(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=ze(a.maxWidth,o,"clientWidth"),n=ze(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Ie,maxHeight:n||Ie}}const ye=i=>Math.round(i*10)/10;function Rr(i,t,e,s){const n=je(i),o=Ct(n,"margin"),r=ze(n.maxWidth,i,"clientWidth")||Ie,a=ze(n.maxHeight,i,"clientHeight")||Ie,l=Tr(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const f=Ct(n,"border","width"),u=Ct(n,"padding");c-=u.width+f.width,h-=u.height+f.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=ye(Math.min(c,r,l.maxWidth)),h=ye(Math.min(h,a,l.maxHeight)),c&&!h&&(h=ye(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=ye(Math.floor(h*s))),{width:c,height:h}}function Ji(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Lr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};Oi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Qi(i,t){const e=Dr(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Pt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Fr(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Ir(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=Pt(i,n,e),a=Pt(n,o,e),l=Pt(o,t,e),c=Pt(r,a,e),h=Pt(a,l,e);return Pt(c,h,e)}const Er=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},zr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Wt(i,t,e){return i?Er(t,e):zr()}function Mn(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function kn(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function Sn(i){return i==="angle"?{between:le,compare:Eo,normalize:at}:{between:gt,compare:(t,e)=>t-e,normalize:t=>t}}function ts({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function Br(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=Sn(s),l=t.length;let{start:c,end:h,loop:d}=i,f,u;if(d){for(c+=l,h+=l,f=0,u=l;f<u&&r(a(t[c%l][s]),n,o);++f)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function wn(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=Sn(s),{start:h,end:d,loop:f,style:u}=Br(i,t,e),g=[];let p=!1,m=null,b,_,y;const v=()=>l(n,y,b)&&a(n,y)!==0,x=()=>a(o,b)===0||l(o,y,b),k=()=>p||v(),S=()=>!p||x();for(let M=h,P=h;M<=d;++M)_=t[M%r],!_.skip&&(b=c(_[s]),b!==y&&(p=l(b,n,o),m===null&&k()&&(m=a(b,n)===0?M:P),m!==null&&S()&&(g.push(ts({start:m,end:M,loop:f,count:r,style:u})),m=null),P=M,y=b));return m!==null&&g.push(ts({start:m,end:d,loop:f,count:r,style:u})),g}function Pn(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=wn(s[n],i.points,t);o.length&&e.push(...o)}return e}function Wr(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function Hr(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function Vr(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=Wr(e,n,o,s);if(s===!0)return es(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return es(i,Hr(e,r,l,c),e,t)}function es(i,t,e,s){return!s||!s.setContext||!e?t:Nr(i,t,e,s)}function Nr(i,t,e,s){const n=i._chart.getContext(),o=is(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,d=t[0].start,f=d;function u(g,p,m,b){const _=a?-1:1;if(g!==p){for(g+=l;e[g%l].skip;)g-=_;for(;e[p%l].skip;)p+=_;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of t){d=a?d:g.start;let p=e[d%l],m;for(f=d+1;f<=g.end;f++){const b=e[f%l];m=is(s.setContext(Lt(n,{type:"segment",p0:p,p1:b,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:r}))),jr(m,h)&&u(d,f-1,g.loop,h),p=b,h=m}d<f-1&&u(d,f-1,g.loop,h)}return c}function is(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function jr(i,t){if(!t)return!1;const e=[],s=function(n,o){return Mi(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}function ve(i,t,e){return i.options.clip?i[e]:t[e]}function $r(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:ve(e,t,"left"),right:ve(e,t,"right"),top:ve(s,t,"top"),bottom:ve(s,t,"bottom")}:t}function Dn(i,t){const e=t._clip;if(e.disabled)return!1;const s=$r(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Yr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=fn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var ht=new Yr;const ss="transparent",Xr={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Xi(i||ss),n=s.valid&&Xi(t||ss);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Ur{constructor(t,e,s,n){const o=e[s];n=_e([t.to,n,o,t.from]);const r=_e([t.from,o,n]);this._active=!0,this._fn=t.fn||Xr[t.type||typeof r],this._easing=ie[t.easing]||ie.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=_e([t.to,e,n,t.from]),this._from=_e([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class On{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!A(t))return;const e=Object.keys(V.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!A(o))return;const r={};for(const a of e)r[a]=o[a];(H(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=qr(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Kr(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const f=s.get(c);if(d)if(f&&d.active()){d.update(f,h,a);continue}else d.cancel();if(!f||!f.duration){t[c]=h;continue}o[c]=d=new Ur(f,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return ht.add(this._chart,s),!0}}function Kr(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function qr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function ns(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Gr(i,t,e){if(e===!1)return!1;const s=ns(i,e),n=ns(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Zr(i){let t,e,s,n;return A(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Cn(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function os(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],K(c)&&(o||t===0||lt(t)===lt(c))&&(t+=c)}return!h&&!s.all?0:t}function Jr(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function qe(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Qr(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function ta(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function ea(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function rs(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function as(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=Qr(o,r,s),d=t.length;let f;for(let u=0;u<d;++u){const g=t[u],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});f=b[c]=ea(n,h,p),f[a]=m,f._top=rs(f,r,!0,s.type),f._bottom=rs(f,r,!1,s.type);const _=f._visualValues||(f._visualValues={});_[a]=m}}function Ge(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function ia(i,t){return Lt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function sa(i,t,e){return Lt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Yt(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Ze=i=>i==="reset"||i==="none",ls=(i,t)=>t?i:Object.assign({},i),na=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Cn(e,!0),values:null};class At{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=qe(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Yt(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,f,u,g)=>d==="x"?f:d==="r"?g:u,o=e.xAxisID=O(s.xAxisID,Ge(t,"x")),r=e.yAxisID=O(s.yAxisID,Ge(t,"y")),a=e.rAxisID=O(s.rAxisID,Ge(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&ji(this._data,this),t._stacked&&Yt(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(A(e)){const n=this._cachedMeta;this._data=Jr(e,n)}else if(s!==e){if(s){ji(s,this);const n=this._cachedMeta;Yt(n),n._parsed=[]}e&&Object.isExtensible(e)&&Ho(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=qe(e.vScale,e),e.stack!==s.stack&&(n=!0,Yt(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(as(this,e._parsed),e._stacked=qe(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,f;if(this._parsing===!1)s._parsed=n,s._sorted=!0,f=n;else{H(n[t])?f=this.parseArrayData(s,n,t,e):A(n[t])?f=this.parseObjectData(s,n,t,e):f=this.parsePrimitiveData(s,n,t,e);const u=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=f[h],l&&(u()&&(l=!1),c=d);s._sorted=l}r&&as(this,f)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let f,u,g;for(f=0,u=n;f<u;++f)g=f+s,d[f]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,f,u;for(h=0,d=n;h<d;++h)f=h+s,u=e[f],c[h]={x:o.parse(Tt(u,a),f),y:r.parse(Tt(u,l),f)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Cn(n,!0),values:e._stacks[t.axis]._visualValues};return os(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=os(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=na(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=ta(a);let f,u;function g(){u=n[f];const p=u[a.axis];return!K(u[t.axis])||h>p||d<p}for(f=0;f<r&&!(!g()&&(this.updateRangeFromParsed(c,t,u,l),o));++f);if(o){for(f=r-1;f>=0;--f)if(!g()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],K(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Zr(O(this.options.clip,Gr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=sa(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=ia(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&re(s);if(a)return ls(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],f=c.getOptionScopes(this.getDataset(),h),u=Object.keys(V.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(f,u,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(ls(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),f=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(f,this.getContext(t,s,e))}const c=new On(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Ze(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Ze(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Ze(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Yt(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}w(At,"defaults",{}),w(At,"datasetElementType",null),w(At,"dataElementType",null);function oa(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=dn(s.sort((n,o)=>n-o))}return i._cache.$bar}function ra(i){const t=i.iScale,e=oa(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(re(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function aa(i,t,e,s){const n=e.barThickness;let o,r;return R(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function la(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function ca(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function An(i,t,e,s){return H(i)?ca(i,t,e,s):t[e.axis]=e.parse(i,s),t}function cs(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,f;for(c=e,h=e+s;c<h;++c)f=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(An(f,d,o,c));return l}function Je(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function ha(i,t,e){return i!==0?lt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function da(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function fa(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=da(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[hs(h,r,a,l)]=!0,n=c)),o[hs(n,r,a,l)]=!0,i.borderSkipped=o}function hs(i,t,e,s){return s?(i=ua(i,t,e),i=ds(i,e,t)):i=ds(i,t,e),i}function ua(i,t,e){return i===t?e:i===e?t:i}function ds(i,t,e){return i==="start"?t:i==="end"?e:i}function ga(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class Ce extends At{parsePrimitiveData(t,e,s,n){return cs(t,e,s,n)}parseArrayData(t,e,s,n){return cs(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let f,u,g,p;for(f=s,u=s+n;f<u;++f)p=e[f],g={},g[o.axis]=o.parse(Tt(p,c),f),d.push(An(Tt(p,h),g,r,f));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=Je(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:f}=this._getSharedOptions(e,n);for(let u=e;u<e+s;u++){const g=this.getParsed(u),p=o||R(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(u),m=this._calculateBarIndexPixels(u,h),b=(g._stacks||{})[a.axis],_={horizontal:c,base:p.base,enableBorderRadius:!b||Je(g._custom)||r===b._top||r===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};f&&(_.options=d||this.resolveDataElementOptions(u,t[u].active?"active":n));const y=_.options||t[u].options;fa(_,y,b,r),ga(_,y,h.ratio),this.updateElement(t[u],u,_,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[s.axis],c=h=>{const d=h._parsed.find(u=>u[s.axis]===l),f=d&&d[h.vScale.axis];if(R(f)||isNaN(f))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||ra(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=Je(c);let d=l[e.axis],f=0,u=s?this.applyStack(e,l,s):d,g,p;u!==d&&(f=u-d,u=d),h&&(d=c.barStart,u=c.barEnd-c.barStart,d!==0&&lt(d)!==lt(c.barEnd)&&(f=0),f+=d);const m=!R(o)&&!h?o:f;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(f+u):g=b,p=g-b,Math.abs(p)<r){p=ha(p,e,a)*r,d===a&&(b-=p/2);const _=e.getPixelForDecimal(0),y=e.getPixelForDecimal(1),v=Math.min(_,y),x=Math.max(_,y);b=Math.max(Math.min(b,x),v),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const _=lt(p)*e.getLineWidthForValue(a)/2;b+=_,p-=_}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=O(n.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?la(t,e,n,c):aa(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}w(Ce,"id","bar"),w(Ce,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),w(Ce,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function pa(i,t,e){let s=1,n=1,o=0,r=0;if(t<z){const a=i,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),f=Math.sin(l),u=(y,v,x)=>le(y,a,l,!0)?1:Math.max(v,v*e,x,x*e),g=(y,v,x)=>le(y,a,l,!0)?-1:Math.min(v,v*e,x,x*e),p=u(0,c,d),m=u(N,h,f),b=g(B,c,d),_=g(B+N,h,f);s=(p-b)/2,n=(m-_)/2,o=-(p+b)/2,r=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}class Zt extends At{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(A(s[t])){const{key:l="value"}=this._parsing;o=c=>+Tt(s[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return ut(this.options.rotation-90)}_getCircumference(){return ut(this.options.circumference)}_getRotationExtents(){let t=z,e=-z;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(ko(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:f,ratioY:u,offsetX:g,offsetY:p}=pa(d,h,l),m=(s.width-r)/f,b=(s.height-r)/u,_=Math.max(Math.min(m,b)/2,0),y=rn(this.options.radius,_),v=Math.max(y*l,0),x=(y-v)/this._getVisibleDatasetWeightTotal();this.offsetX=g*y,this.offsetY=p*y,n.total=this.calculateTotal(),this.outerRadius=y-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/z)}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,f=o&&c.animateScale,u=f?0:this.innerRadius,g=f?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),_;for(_=0;_<e;++_)b+=this._circumference(_,o);for(_=e;_<e+s;++_){const y=this._circumference(_,o),v=t[_],x={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+y,circumference:y,outerRadius:g,innerRadius:u};m&&(x.options=p||this.resolveDataElementOptions(_,v.active?"active":n)),b+=y,this.updateElement(v,_,x,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?z*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=ki(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,r,a,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){r=s.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(O(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}w(Zt,"id","doughnut"),w(Zt,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),w(Zt,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),w(Zt,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}});class Ae extends At{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,r=this.chart._animationsDisabled;let{start:a,count:l}=jo(e,n,r);this._drawStart=a,this._drawCount=l,$o(e)&&(a=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),f=r.axis,u=a.axis,{spanGaps:g,segment:p}=this.options,m=ae(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",_=e+s,y=t.length;let v=e>0&&this.getParsed(e-1);for(let x=0;x<y;++x){const k=t[x],S=b?k:{};if(x<e||x>=_){S.skip=!0;continue}const M=this.getParsed(x),P=R(M[u]),C=S[f]=r.getPixelForValue(M[f],x),D=S[u]=o||P?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,M,l):M[u],x);S.skip=isNaN(C)||isNaN(D)||P,S.stop=x>0&&Math.abs(M[f]-v[f])>m,p&&(S.parsed=M,S.raw=c.data[x]),d&&(S.options=h||this.resolveDataElementOptions(x,k.active?"active":n)),b||this.updateElement(k,x,S,n),v=M}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}w(Ae,"id","line"),w(Ae,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),w(Ae,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});function St(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ai{constructor(t){w(this,"options");this.options=t||{}}static override(t){Object.assign(Ai.prototype,t)}init(){}formats(){return St()}parse(){return St()}format(){return St()}add(){return St()}diff(){return St()}startOf(){return St()}endOf(){return St()}}var ma={_date:Ai};function ba(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?Bo:Dt;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const f=c(o,t,e-d),u=c(o,t,e+d);return{lo:f.lo,hi:u.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:f}=i,u=f.slice(0,h.lo+1).reverse().findIndex(p=>!R(p[d.axis]));h.lo-=Math.max(0,u);const g=f.slice(h.hi).findIndex(p=>!R(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function $e(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:f}=ba(o[a],t,r,n);for(let u=d;u<=f;++u){const g=h[u];g.skip||s(g,c,u)}}}function _a(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Qe(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||$e(i,e,t,function(a,l,c){!n&&!ce(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function xa(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=cn(r,{x:t.x,y:t.y});le(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return $e(i,e,t,o),n}function ya(i,t,e,s,n,o){let r=[];const a=_a(e);let l=Number.POSITIVE_INFINITY;function c(h,d,f){const u=h.inRange(t.x,t.y,n);if(s&&!u)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!u)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:f}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:f})}return $e(i,e,t,c),r}function ti(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?xa(i,t,e,n):ya(i,t,e,s,n,o)}function fs(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return $e(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var va={modes:{index(i,t,e,s){const n=wt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?Qe(i,n,o,s,r):ti(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=wt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?Qe(i,n,o,s,r):ti(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=wt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Qe(i,n,o,s,r)},nearest(i,t,e,s){const n=wt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return ti(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=wt(t,i);return fs(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=wt(t,i);return fs(i,n,"y",e.intersect,s)}}};const Tn=["left","top","right","bottom"];function Xt(i,t){return i.filter(e=>e.pos===t)}function us(i,t){return i.filter(e=>Tn.indexOf(e.pos)===-1&&e.box.axis===t)}function Ut(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Ma(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function ka(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!Tn.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Sa(i,t){const e=ka(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function wa(i){const t=Ma(i),e=Ut(t.filter(c=>c.box.fullSize),!0),s=Ut(Xt(t,"left"),!0),n=Ut(Xt(t,"right")),o=Ut(Xt(t,"top"),!0),r=Ut(Xt(t,"bottom")),a=us(t,"x"),l=us(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Xt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function gs(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Rn(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Pa(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!A(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&Rn(r,o.getPadding());const a=Math.max(0,t.outerWidth-gs(r,i,"left","right")),l=Math.max(0,t.outerHeight-gs(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function Da(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Oa(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function Jt(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Oa(a.horizontal,t));const{same:d,other:f}=Pa(t,e,a,s);c|=d&&n.length,h=h||f,l.fullSize||n.push(a)}return c&&Jt(n,t,e,s)||h}function Me(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function ps(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,f=c.size||l.height;re(c.start)&&(r=c.start),l.fullSize?Me(l,n.left,r,e.outerWidth-n.right-n.left,f):Me(l,t.left+c.placed,r,d,f),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,f=c.size||l.width;re(c.start)&&(o=c.start),l.fullSize?Me(l,o,n.top,f,e.outerHeight-n.bottom-n.top):Me(l,o,t.top+c.placed,f,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var Q={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=tt(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=wa(i.boxes),l=a.vertical,c=a.horizontal;L(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),f=Object.assign({},n);Rn(f,tt(s));const u=Object.assign({maxPadding:f,w:o,h:r,x:n.left,y:n.top},n),g=Sa(l.concat(c),d);Jt(a.fullSize,u,d,g),Jt(l,u,d,g),Jt(c,u,d,g)&&Jt(l,u,d,g),Da(u),ps(a.leftAndTop,u,d,g),u.x+=u.w,u.y+=u.h,ps(a.rightAndBottom,u,d,g),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},L(a.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class Ln{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Ca extends Ln{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Te="$chartjs",Aa={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},ms=i=>i===null||i==="";function Ta(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[Te]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",ms(n)){const o=Qi(i,"width");o!==void 0&&(i.width=o)}if(ms(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Qi(i,"height");o!==void 0&&(i.height=o)}return i}const Fn=Lr?{passive:!0}:!1;function Ra(i,t,e){i&&i.addEventListener(t,e,Fn)}function La(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,Fn)}function Fa(i,t){const e=Aa[i.type]||i.type,{x:s,y:n}=wt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Be(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Ia(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Be(a.addedNodes,s),r=r&&!Be(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Ea(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Be(a.removedNodes,s),r=r&&!Be(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const de=new Map;let bs=0;function In(){const i=window.devicePixelRatio;i!==bs&&(bs=i,de.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function za(i,t){de.size||window.addEventListener("resize",In),de.set(i,t)}function Ba(i){de.delete(i),de.size||window.removeEventListener("resize",In)}function Wa(i,t,e){const s=i.canvas,n=s&&Ci(s);if(!n)return;const o=un((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),za(i,o),r}function ei(i,t,e){e&&e.disconnect(),t==="resize"&&Ba(i)}function Ha(i,t,e){const s=i.canvas,n=un(o=>{i.ctx!==null&&e(Fa(o,i))},i);return Ra(s,t,n),n}class Va extends Ln{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Ta(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[Te])return!1;const s=e[Te].initial;["height","width"].forEach(o=>{const r=s[o];R(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[Te],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Ia,detach:Ea,resize:Wa}[e]||Ha;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:ei,detach:ei,resize:ei}[e]||La)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Rr(t,e,s,n)}isAttached(t){const e=t&&Ci(t);return!!(e&&e.isConnected)}}function Na(i){return!Oi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Ca:Va}class it{constructor(){w(this,"x");w(this,"y");w(this,"active",!1);w(this,"options");w(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return ae(this.x)&&ae(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}w(it,"defaults",{}),w(it,"defaultRoutes");function ja(i,t){const e=i.options.ticks,s=$a(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?Xa(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return Ua(t,c,o,r/n),c;const h=Ya(o,t,n);if(r>0){let d,f;const u=r>1?Math.round((l-a)/(r-1)):null;for(ke(t,c,h,R(u)?0:a-u,a),d=0,f=r-1;d<f;d++)ke(t,c,h,o[d],o[d+1]);return ke(t,c,h,l,R(u)?t.length:l+u),c}return ke(t,c,h),c}function $a(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Ya(i,t,e){const s=Ka(i),n=t.length/e;if(!s)return Math.max(n,1);const o=To(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function Xa(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function Ua(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function ke(i,t,e,s,n){const o=O(s,0),r=Math.min(O(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function Ka(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const qa=i=>i==="left"?"right":i==="right"?"left":i,_s=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,xs=(i,t)=>Math.min(t||i,i);function ys(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function Ga(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function Za(i,t){L(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Kt(i){return i.drawTicks?i.tickLength:0}function vs(i,t){if(!i.display)return 0;const e=U(i.font,t),s=tt(i.padding);return(H(i.text)?i.text.length:1)*e.lineHeight+s.height}function Ja(i,t){return Lt(i,{scale:t,type:"scale"})}function Qa(i,t,e){return Lt(i,{tick:e,index:t,type:"tick"})}function tl(i,t,e){let s=vi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=qa(s)),s}function el(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,f,u,g;const p=r-n,m=a-o;if(i.isHorizontal()){if(u=Y(s,o,a),A(e)){const b=Object.keys(e)[0],_=e[b];g=h[b].getPixelForValue(_)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=_s(i,e,t);f=a-o}else{if(A(e)){const b=Object.keys(e)[0],_=e[b];u=h[b].getPixelForValue(_)-m+t}else e==="center"?u=(c.left+c.right)/2-m+t:u=_s(i,e,t);g=Y(s,r,n),d=e==="left"?-N:N}return{titleX:u,titleY:g,maxWidth:f,rotation:d}}class Nt extends it{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=st(t,Number.POSITIVE_INFINITY),e=st(e,Number.NEGATIVE_INFINITY),s=st(s,Number.POSITIVE_INFINITY),n=st(n,Number.NEGATIVE_INFINITY),{min:st(t,s),max:st(e,n),minDefined:K(t),maxDefined:K(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:st(e,st(s,e)),max:st(s,st(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=hr(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?ys(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=ja(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),I(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=I(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=xs(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,f=h.highest.height,u=X(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1),d+6>a&&(a=u/(s-(t.offset?.5:1)),l=this.maxHeight-Kt(t.grid)-e.padding-vs(t.title,this.chart.options.font),c=Math.sqrt(d*d+f*f),r=Io(Math.min(Math.asin(X((h.highest.height+6)/a,-1,1)),Math.asin(X(l/c,-1,1))-Math.asin(X(f/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=vs(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Kt(o)+l):(t.height=this.maxHeight,t.width=Kt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,g=ut(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=s.mirror?0:m*d.width+p*f.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:p*d.width+m*f.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,u=0;l?c?(f=n*t.width,u=s*e.height):(f=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?f=t.width:o!=="inner"&&(f=t.width/2,u=e.width/2),this.paddingLeft=Math.max((f-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)R(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=ys(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/xs(e,s));let c=0,h=0,d,f,u,g,p,m,b,_,y,v,x;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},_=p.lineHeight,y=v=0,!R(g)&&!H(g))y=Ki(n,b.data,b.gc,y,g),v=_;else if(H(g))for(f=0,u=g.length;f<u;++f)x=g[f],!R(x)&&!H(x)&&(y=Ki(n,b.data,b.gc,y,x),v+=_);r.push(y),a.push(v),c=Math.max(y,c),h=Math.max(v,h)}Za(o,e);const k=r.indexOf(c),S=a.indexOf(h),M=P=>({width:r[P]||0,height:a[P]||0});return{first:M(0),last:M(e-1),widest:M(k),highest:M(S),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return zo(this._alignToPixels?kt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Qa(this.getContext(),t,s))}return this.$context||(this.$context=Ja(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=ut(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=Kt(o),u=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(E){return kt(s,E,p)};let _,y,v,x,k,S,M,P,C,D,T,j;if(r==="top")_=b(this.bottom),S=this.bottom-f,P=_-m,D=b(t.top)+m,j=t.bottom;else if(r==="bottom")_=b(this.top),D=t.top,j=b(t.bottom)-m,S=_+m,P=this.top+f;else if(r==="left")_=b(this.right),k=this.right-f,M=_-m,C=b(t.left)+m,T=t.right;else if(r==="right")_=b(this.left),C=t.left,T=b(t.right)-m,k=_+m,M=this.left+f;else if(e==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(A(r)){const E=Object.keys(r)[0],W=r[E];_=b(this.chart.scales[E].getPixelForValue(W))}D=t.top,j=t.bottom,S=_+m,P=S+f}else if(e==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(A(r)){const E=Object.keys(r)[0],W=r[E];_=b(this.chart.scales[E].getPixelForValue(W))}k=_-m,M=k-f,C=t.left,T=t.right}const Z=O(n.ticks.maxTicksLimit,d),F=Math.max(1,Math.ceil(d/Z));for(y=0;y<d;y+=F){const E=this.getContext(y),W=o.setContext(E),et=a.setContext(E),$=W.lineWidth,Ft=W.color,ue=et.dash||[],It=et.dashOffset,jt=W.tickWidth,yt=W.tickColor,$t=W.tickBorderDash||[],vt=W.tickBorderDashOffset;v=Ga(this,y,l),v!==void 0&&(x=kt(s,v,$),c?k=M=C=T=x:S=P=D=j=x,u.push({tx1:k,ty1:S,tx2:M,ty2:P,x1:C,y1:D,x2:T,y2:j,width:$,color:Ft,borderDash:ue,borderDashOffset:It,tickWidth:jt,tickColor:yt,tickBorderDash:$t,tickBorderDashOffset:vt}))}return this._ticksLength=d,this._borderValue=_,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,f=Kt(s.grid),u=f+h,g=d?-h:u,p=-ut(this.labelRotation),m=[];let b,_,y,v,x,k,S,M,P,C,D,T,j="middle";if(n==="top")k=this.bottom-g,S=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+g,S=this._getXAxisLabelAlignment();else if(n==="left"){const F=this._getYAxisLabelAlignment(f);S=F.textAlign,x=F.x}else if(n==="right"){const F=this._getYAxisLabelAlignment(f);S=F.textAlign,x=F.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+u;else if(A(n)){const F=Object.keys(n)[0],E=n[F];k=this.chart.scales[F].getPixelForValue(E)+u}S=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-u;else if(A(n)){const F=Object.keys(n)[0],E=n[F];x=this.chart.scales[F].getPixelForValue(E)}S=this._getYAxisLabelAlignment(f).textAlign}e==="y"&&(l==="start"?j="top":l==="end"&&(j="bottom"));const Z=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){y=a[b],v=y.label;const F=o.setContext(this.getContext(b));M=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),C=P.lineHeight,D=H(v)?v.length:1;const E=D/2,W=F.color,et=F.textStrokeColor,$=F.textStrokeWidth;let Ft=S;r?(x=M,S==="inner"&&(b===_-1?Ft=this.options.reverse?"left":"right":b===0?Ft=this.options.reverse?"right":"left":Ft="center"),n==="top"?c==="near"||p!==0?T=-D*C+C/2:c==="center"?T=-Z.highest.height/2-E*C+C:T=-Z.highest.height+C/2:c==="near"||p!==0?T=C/2:c==="center"?T=Z.highest.height/2-E*C:T=Z.highest.height-D*C,d&&(T*=-1),p!==0&&!F.showLabelBackdrop&&(x+=C/2*Math.sin(p))):(k=M,T=(1-D)*C/2);let ue;if(F.showLabelBackdrop){const It=tt(F.backdropPadding),jt=Z.heights[b],yt=Z.widths[b];let $t=T-It.top,vt=0-It.left;switch(j){case"middle":$t-=jt/2;break;case"bottom":$t-=jt;break}switch(S){case"center":vt-=yt/2;break;case"right":vt-=yt;break;case"inner":b===_-1?vt-=yt:b>0&&(vt-=yt/2);break}ue={left:vt,top:$t,width:yt+It.width,height:jt+It.height,color:F.backdropColor}}m.push({label:v,font:P,textOffset:T,options:{rotation:p,color:W,strokeColor:et,strokeWidth:$,textAlign:Ft,textBaseline:j,translation:[x,k],backdrop:ue}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-ut(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,f;this.isHorizontal()?(c=kt(t,this.left,r)-r/2,h=kt(t,this.right,a)+a/2,d=f=l):(d=kt(t,this.top,r)-r/2,f=kt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Ve(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;he(s,c,0,h,l,a)}n&&Ne(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=U(s.font),r=tt(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||A(e)?(l+=r.bottom,H(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:f}=el(this,l,e,a);he(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:tl(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=O(t.grid&&t.grid.z,-1),n=O(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Nt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return U(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Se{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;nl(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,il(t,r,s),this.override&&V.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in V[n]&&(delete V[n][s],this.override&&delete Rt[s])}}function il(i,t,e){const s=oe(Object.create(null),[e?V.get(e):{},V.get(t),i.defaults]);V.set(t,s),i.defaultRoutes&&sl(t,i.defaultRoutes),i.descriptors&&V.describe(t,i.descriptors)}function sl(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");V.route(o,n,l,a)})}function nl(i){return"id"in i&&"defaults"in i}class ol{constructor(){this.controllers=new Se(At,"datasets",!0),this.elements=new Se(it,"elements"),this.plugins=new Se(Object,"plugins"),this.scales=new Se(Nt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):L(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=xi(t);I(s["before"+n],[],s),e[t](s),I(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var rt=new ol;class rl{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(I(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){R(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=O(s.options&&s.options.plugins,{}),o=al(s);return n===!1&&!e?[]:cl(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function al(i){const t={},e=[],s=Object.keys(rt.plugins.items);for(let o=0;o<s.length;o++)e.push(rt.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function ll(i,t){return!t&&i===!1?null:i===!0?{}:i}function cl(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=ll(s[l],n);c!==null&&o.push({plugin:a,options:hl(i.config,{plugin:a,local:e[l]},c,r)})}return o}function hl(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function di(i,t){const e=V.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function dl(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function fl(i,t){return i===t?"_index_":"_value_"}function Ms(i){if(i==="x"||i==="y"||i==="r")return i}function ul(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function fi(i,...t){if(Ms(i))return i;for(const e of t){const s=e.axis||ul(e.position)||i.length>1&&Ms(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function ks(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function gl(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return ks(i,"x",e[0])||ks(i,"y",e[0])}return{}}function pl(i,t){const e=Rt[i.type]||{scales:{}},s=t.scales||{},n=di(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!A(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=fi(r,a,gl(r,i),V.scales[a.type]),c=fl(l,n),h=e.scales||{};o[r]=te(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||di(a,t),h=(Rt[a]||{}).scales||{};Object.keys(h).forEach(d=>{const f=dl(d,l),u=r[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),te(o[u],[{axis:f},s[u],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];te(a,[V.scales[a.type],V.scale])}),o}function En(i){const t=i.options||(i.options={});t.plugins=O(t.plugins,{}),t.scales=pl(i,t)}function zn(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function ml(i){return i=i||{},i.data=zn(i.data),En(i),i}const Ss=new Map,Bn=new Set;function we(i,t){let e=Ss.get(i);return e||(e=t(),Ss.set(i,e),Bn.add(e)),e}const qt=(i,t,e)=>{const s=Tt(t,e);s!==void 0&&i.add(s)};class bl{constructor(t){this._config=ml(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=zn(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),En(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return we(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return we(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return we(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return we(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>qt(l,t,d))),h.forEach(d=>qt(l,n,d)),h.forEach(d=>qt(l,Rt[o]||{},d)),h.forEach(d=>qt(l,V,d)),h.forEach(d=>qt(l,ci,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Bn.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,Rt[e]||{},V.datasets[e]||{},{type:e},V,ci]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ws(this._resolverCache,t,n);let l=r;if(xl(r,e)){o.$shared=!1,s=xt(s)?s():s;const c=this.createResolver(t,s,a);l=Ht(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=ws(this._resolverCache,t,s);return A(e)?Ht(o,e,void 0,n):o}}function ws(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:wi(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const _l=i=>A(i)&&Object.getOwnPropertyNames(i).some(t=>xt(i[t]));function xl(i,t){const{isScriptable:e,isIndexable:s}=bn(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(xt(a)||_l(a))||r&&H(a))return!0}return!1}var yl="4.4.9";const vl=["top","bottom","left","right","chartArea"];function Ps(i,t){return i==="top"||i==="bottom"||vl.indexOf(i)===-1&&t==="x"}function Ds(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function Os(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),I(e&&e.onComplete,[i],t)}function Ml(i){const t=i.chart,e=t.options.animation;I(e&&e.onProgress,[i],t)}function Wn(i){return Oi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const Re={},Cs=i=>{const t=Wn(i);return Object.values(Re).filter(e=>e.canvas===t).pop()};function kl(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Sl(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}var pt;let Ti=(pt=class{static register(...t){rt.add(...t),As()}static unregister(...t){rt.remove(...t),As()}constructor(t,e){const s=this.config=new bl(e),n=Wn(t),o=Cs(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Na(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Mo(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new rl,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Vo(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],Re[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}ht.listen(this,"complete",Os),ht.listen(this,"progress",Ml),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return R(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return rt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Ji(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return qi(this.canvas,this.ctx),this}stop(){return ht.stop(this),this}resize(t,e){ht.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Ji(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),I(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};L(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=fi(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),L(o,r=>{const a=r.options,l=a.id,c=fi(l,a),h=O(a.type,r.dtype);(a.position===void 0||Ps(a.position,c)!==Ps(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const f=rt.getScale(h);d=new f({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),L(n,(r,a)=>{r||delete s[a]}),L(s,r=>{Q.configure(this,r,r.options),Q.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(Ds("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||di(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=rt.getController(a),{datasetElementType:c,dataElementType:h}=V.datasets[a];Object.assign(l,{dataElementType:rt.getElement(h),datasetElementType:c&&rt.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){L(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),f=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(f),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||L(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Ds("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){L(this.scales,t=>{Q.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Wi(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;kl(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!Wi(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Q.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],L(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,xt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(ht.has(this)?this.attached&&!ht.running(this)&&ht.start(this):(this.draw(),Os({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=Dn(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&Ve(e,n),t.controller.draw(),n&&Ne(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return ce(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=va.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Lt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);re(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),ht.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),qi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Re[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};L(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){L(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},L(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!Le(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=Oo(t),c=Sl(t,this._lastEvent,s,l);s&&(this._lastEvent=null,I(o.onHover,[t,a,this],this),l&&I(o.onClick,[t,a,this],this));const h=!Le(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}},w(pt,"defaults",V),w(pt,"instances",Re),w(pt,"overrides",Rt),w(pt,"registry",rt),w(pt,"version",yl),w(pt,"getChart",Cs),pt);function As(){return L(Ti.instances,i=>i._plugins.invalidate())}function wl(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=n/a;i.beginPath(),i.arc(o,r,a,s-c,e+c),l>n?(c=n/l,i.arc(o,r,l,e+c,s-c,!0)):i.arc(o,r,n,e+N,s-N),i.closePath(),i.clip()}function Pl(i){return Si(i,["outerStart","outerEnd","innerStart","innerEnd"])}function Dl(i,t,e,s){const n=Pl(i.options.borderRadius),o=(e-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(e-Math.min(o,l))*s/2;return X(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:X(n.innerStart,0,r),innerEnd:X(n.innerEnd,0,r)}}function zt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function We(i,t,e,s,n,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),f=h>0?h+s+e+c:0;let u=0;const g=n-l;if(s){const F=h>0?h-s:0,E=d>0?d-s:0,W=(F+E)/2,et=W!==0?g*W/(W+s):g;u=(g-et)/2}const p=Math.max(.001,g*d-e/B)/d,m=(g-p)/2,b=l+m+u,_=n-m-u,{outerStart:y,outerEnd:v,innerStart:x,innerEnd:k}=Dl(t,f,d,_-b),S=d-y,M=d-v,P=b+y/S,C=_-v/M,D=f+x,T=f+k,j=b+x/D,Z=_-k/T;if(i.beginPath(),o){const F=(P+C)/2;if(i.arc(r,a,d,P,F),i.arc(r,a,d,F,C),v>0){const $=zt(M,C,r,a);i.arc($.x,$.y,v,C,_+N)}const E=zt(T,_,r,a);if(i.lineTo(E.x,E.y),k>0){const $=zt(T,Z,r,a);i.arc($.x,$.y,k,_+N,Z+Math.PI)}const W=(_-k/f+(b+x/f))/2;if(i.arc(r,a,f,_-k/f,W,!0),i.arc(r,a,f,W,b+x/f,!0),x>0){const $=zt(D,j,r,a);i.arc($.x,$.y,x,j+Math.PI,b-N)}const et=zt(S,b,r,a);if(i.lineTo(et.x,et.y),y>0){const $=zt(S,P,r,a);i.arc($.x,$.y,y,b-N,P)}}else{i.moveTo(r,a);const F=Math.cos(P)*d+r,E=Math.sin(P)*d+a;i.lineTo(F,E);const W=Math.cos(C)*d+r,et=Math.sin(C)*d+a;i.lineTo(W,et)}i.closePath()}function Ol(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){We(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(a)||(l=r+(a%z||z))}return We(i,t,e,s,l,n),i.fill(),l}function Cl(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:f}=l,u=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=f,u?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){We(i,t,e,s,g,n);for(let p=0;p<o;++p)i.stroke();isNaN(a)||(g=r+(a%z||z))}u&&wl(i,t,g),o||(We(i,t,e,s,g,n),i.stroke())}class Pe extends it{constructor(e){super();w(this,"circumference");w(this,"endAngle");w(this,"fullCircles");w(this,"innerRadius");w(this,"outerRadius");w(this,"pixelMargin");w(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.getProps(["x","y"],n),{angle:r,distance:a}=cn(o,{x:e,y:s}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:d,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),u=(this.options.spacing+this.options.borderWidth)/2,g=O(f,c-l),p=le(r,l,c)&&l!==c,m=g>=z||p,b=gt(a,h+u,d+u);return m&&b}getCenterPoint(e){const{x:s,y:n,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,d=(o+r)/2,f=(a+l+h+c)/2;return{x:s+Math.cos(d)*f,y:n+Math.sin(d)*f}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:s,circumference:n}=this,o=(s.offset||0)/4,r=(s.spacing||0)/2,a=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=n>z?Math.floor(n/z):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();const l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(B,n||0)),h=o*c;e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,Ol(e,this,h,r,a),Cl(e,this,h,r,a),e.restore()}}w(Pe,"id","arc"),w(Pe,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),w(Pe,"defaultRoutes",{backgroundColor:"backgroundColor"}),w(Pe,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function Hn(i,t,e=t){i.lineCap=O(e.borderCapStyle,t.borderCapStyle),i.setLineDash(O(e.borderDash,t.borderDash)),i.lineDashOffset=O(e.borderDashOffset,t.borderDashOffset),i.lineJoin=O(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=O(e.borderWidth,t.borderWidth),i.strokeStyle=O(e.borderColor,t.borderColor)}function Al(i,t,e){i.lineTo(e.x,e.y)}function Tl(i){return i.stepped?er:i.tension||i.cubicInterpolationMode==="monotone"?ir:Al}function Vn(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function Rl(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Vn(n,e,s),h=Tl(o);let{move:d=!0,reverse:f}=s||{},u,g,p;for(u=0;u<=c;++u)g=n[(a+(f?c-u:u))%r],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,f,o.stepped),p=g);return l&&(g=n[(a+(f?c:0))%r],h(i,p,g,f,o.stepped)),!!l}function Ll(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=Vn(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,f,u,g,p,m,b;const _=v=>(r+(c?a-v:v))%o,y=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(u=n[_(0)],i.moveTo(u.x,u.y)),f=0;f<=a;++f){if(u=n[_(f)],u.skip)continue;const v=u.x,x=u.y,k=v|0;k===g?(x<p?p=x:x>m&&(m=x),h=(d*h+v)/++d):(y(),i.lineTo(v,x),g=k,d=0,p=m=x),b=x}y()}function ui(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Ll:Rl}function Fl(i){return i.stepped?Fr:i.tension||i.cubicInterpolationMode==="monotone"?Ir:Pt}function Il(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),Hn(i,t.options),i.stroke(n)}function El(i,t,e,s){const{segments:n,options:o}=t,r=ui(t);for(const a of n)Hn(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const zl=typeof Path2D=="function";function Bl(i,t,e,s){zl&&!t.options.segment?Il(i,t,e,s):El(i,t,e,s)}class Ot extends it{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;Pr(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Vr(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=Pn(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=Fl(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:f}=r[c],u=o[d],g=o[f];if(u===g){a.push(u);continue}const p=Math.abs((n-u[e])/(g[e]-u[e])),m=l(u,g,p,s.stepped);m[e]=t[e],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,e,s){return ui(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=ui(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Bl(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}w(Ot,"id","line"),w(Ot,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),w(Ot,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),w(Ot,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function Ts(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class ii extends it{constructor(e){super();w(this,"parsed");w(this,"skip");w(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return Ts(this,e,"x",s)}inYRange(e,s){return Ts(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!ce(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,hi(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}w(ii,"id","point"),w(ii,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),w(ii,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Nn(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function bt(i,t,e,s){return i?0:X(t,e,s)}function Wl(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=mn(s);return{t:bt(n.top,o.top,0,e),r:bt(n.right,o.right,0,t),b:bt(n.bottom,o.bottom,0,e),l:bt(n.left,o.left,0,t)}}function Hl(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Bt(n),r=Math.min(t,e),a=i.borderSkipped,l=s||A(n);return{topLeft:bt(!l||a.top||a.left,o.topLeft,0,r),topRight:bt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:bt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:bt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Vl(i){const t=Nn(i),e=t.right-t.left,s=t.bottom-t.top,n=Wl(i,e/2,s/2),o=Hl(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function si(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&Nn(i,s);return a&&(n||gt(t,a.left,a.right))&&(o||gt(e,a.top,a.bottom))}function Nl(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function jl(i,t){i.rect(t.x,t.y,t.w,t.h)}function ni(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class oi extends it{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=Vl(this),a=Nl(r.radius)?Ee:jl;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,ni(r,e,o)),t.clip(),a(t,ni(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,ni(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return si(this,t,e,s)}inXRange(t,e){return si(this,t,null,e)}inYRange(t,e){return si(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}w(oi,"id","bar"),w(oi,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),w(oi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function $l(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=Ri(l,c,n);const h=gi(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const d=Pn(t,h);for(const f of d){const u=gi(e,o[f.start],o[f.end],f.loop),g=wn(a,n,u);for(const p of g)r.push({source:p,target:f,start:{[e]:Rs(h,u,"start",Math.max)},end:{[e]:Rs(h,u,"end",Math.min)}})}}return r}function gi(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=at(n),o=at(o)),{property:i,start:n,end:o}}function Yl(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=Ri(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function Ri(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function Rs(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function jn(i,t){let e=[],s=!1;return H(i)?(s=!0,e=i):e=Yl(i,t),e.length?new Ot({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function Ls(i){return i&&i.fill!==!1}function Xl(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!K(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function Ul(i,t,e){const s=Zl(i);if(A(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return K(n)&&Math.floor(n)===n?Kl(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Kl(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function ql(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:A(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function Gl(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:A(i)?s=i.value:s=t.getBaseValue(),s}function Zl(i){const t=i.options,e=t.fill;let s=O(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Jl(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=Ql(t,e);a.push(jn({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)tc(n,r[h],a)}return new Ot({points:n,options:{}})}function Ql(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function tc(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=ec(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function ec(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][e],f=r[h.end][e];if(gt(n,d,f)){a=n===d,l=n===f;break}}return{first:a,last:l,point:s}}class $n{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:z},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function ic(i){const{chart:t,fill:e,line:s}=i;if(K(e))return sc(t,e);if(e==="stack")return Jl(i);if(e==="shape")return!0;const n=nc(i);return n instanceof $n?n:jn(n,s)}function sc(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function nc(i){return(i.scale||{}).getPointPositionForValue?rc(i):oc(i)}function oc(i){const{scale:t={},fill:e}=i,s=ql(e,t);if(K(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function rc(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=Gl(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new $n({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function ri(i,t,e){const s=ic(t),{chart:n,index:o,line:r,scale:a,axis:l}=t,c=r.options,h=c.fill,d=c.backgroundColor,{above:f=d,below:u=d}=h||{},g=n.getDatasetMeta(o),p=Dn(n,g);s&&r.points.length&&(Ve(i,e),ac(i,{line:r,target:s,above:f,below:u,area:e,scale:a,axis:l,clip:p}),Ne(i))}function ac(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;i.save(),c==="x"&&o!==n&&(Fs(i,s,r.top),Is(i,{line:e,target:s,color:n,scale:a,property:c,clip:l}),i.restore(),i.save(),Fs(i,s,r.bottom)),Is(i,{line:e,target:s,color:o,scale:a,property:c,clip:l}),i.restore()}function Fs(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[Ri(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function Is(i,t){const{line:e,target:s,property:n,color:o,scale:r,clip:a}=t,l=$l(e,s,n);for(const{source:c,target:h,start:d,end:f}of l){const{style:{backgroundColor:u=o}={}}=c,g=s!==!0;i.save(),i.fillStyle=u,lc(i,r,a,g&&gi(n,d,f)),i.beginPath();const p=!!e.pathSegment(i,c);let m;if(g){p?i.closePath():Es(i,s,f,n);const b=!!s.pathSegment(i,h,{move:p,reverse:!0});m=p&&b,m||Es(i,s,d,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function lc(i,t,e,s){const n=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=r,c=n.top,h=a,d=n.bottom):(l=n.left,c=r,h=n.right,d=a),i.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function Es(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var zc={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof Ot&&(l={visible:i.isDatasetVisible(r),index:r,fill:Ul(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=Xl(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&ri(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;Ls(o)&&ri(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!Ls(s)||e.drawTime!=="beforeDatasetDraw"||ri(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const zs=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},cc=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class Bs extends it{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=I(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=U(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=zs(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,u+=h,f++),l[p]={left:0,top:u,row:f,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,f=0,u=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:y}=hc(s,e,o,m,n);b>0&&u+y+2*a>h&&(d+=f+a,c.push({width:f,height:u}),g+=f+a,p++,f=u=0),l[b]={left:g,top:u,col:p,width:_,height:y},f=Math.max(f,_),u+=y+a}),d+=f,c.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Wt(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=Y(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=Y(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=Y(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=Y(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Ve(t,this),this._draw(),Ne(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=V.color,l=Wt(t.rtl,this.left,this.width),c=U(r.font),{padding:h}=r,d=c.size,f=d/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=zs(r,d),b=function(k,S,M){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const P=O(M.lineWidth,1);if(n.fillStyle=O(M.fillStyle,a),n.lineCap=O(M.lineCap,"butt"),n.lineDashOffset=O(M.lineDashOffset,0),n.lineJoin=O(M.lineJoin,"miter"),n.lineWidth=P,n.strokeStyle=O(M.strokeStyle,a),n.setLineDash(O(M.lineDash,[])),r.usePointStyle){const C={radius:p*Math.SQRT2/2,pointStyle:M.pointStyle,rotation:M.rotation,borderWidth:P},D=l.xPlus(k,g/2),T=S+f;pn(n,C,D,T,r.pointStyleWidth&&g)}else{const C=S+Math.max((d-p)/2,0),D=l.leftForLtr(k,g),T=Bt(M.borderRadius);n.beginPath(),Object.values(T).some(j=>j!==0)?Ee(n,{x:D,y:C,w:g,h:p,radius:T}):n.rect(D,C,g,p),n.fill(),P!==0&&n.stroke()}n.restore()},_=function(k,S,M){he(n,M.text,k,S+m/2,c,{strikethrough:M.hidden,textAlign:l.textAlign(M.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?u={x:Y(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:u={x:this.left+h,y:Y(o,this.top+v+h,this.bottom-e[0].height),line:0},Mn(this.ctx,t.textDirection);const x=m+h;this.legendItems.forEach((k,S)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;const M=n.measureText(k.text).width,P=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),C=g+f+M;let D=u.x,T=u.y;l.setWidth(this.width),y?S>0&&D+C+h>this.right&&(T=u.y+=x,u.line++,D=u.x=Y(o,this.left+h,this.right-s[u.line])):S>0&&T+x>this.bottom&&(D=u.x=D+e[u.line].width+h,u.line++,T=u.y=Y(o,this.top+v+h,this.bottom-e[u.line].height));const j=l.x(D);if(b(j,T,k),D=No(P,D+g+f,y?D+C:this.right,t.rtl),_(l.x(D),T,k),y)u.x+=C+h;else if(typeof k.text!="string"){const Z=c.lineHeight;u.y+=Yn(k,Z)+h}else u.y+=x}),kn(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=U(e.font),n=tt(e.padding);if(!e.display)return;const o=Wt(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+c,d=Y(t.align,d,this.right-f);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+Y(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const u=Y(a,d,d+f);r.textAlign=o.textAlign(vi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,he(r,e.text,u,h,s)}_computeTitleHeight(){const t=this.options.title,e=U(t.font),s=tt(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(gt(t,this.left,this.right)&&gt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],gt(t,n.left,n.left+n.width)&&gt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!uc(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=cc(n,s);n&&!o&&I(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&I(e.onHover,[t,s,this],this)}else s&&I(e.onClick,[t,s,this],this)}}function hc(i,t,e,s,n){const o=dc(s,i,t,e),r=fc(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function dc(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function fc(i,t,e){let s=i;return typeof t.text!="string"&&(s=Yn(t,e)),s}function Yn(i,t){const e=i.text?i.text.length:0;return t*e}function uc(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Bc={id:"legend",_element:Bs,start(i,t,e){const s=i.legend=new Bs({ctx:i.ctx,options:e,chart:i});Q.configure(i,s,e),Q.addBox(i,s)},stop(i){Q.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;Q.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=tt(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class Xn extends it{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=H(s.text)?s.text.length:1;this._padding=tt(s.padding);const o=n*U(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=Y(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=Y(a,n,e),l=B*-.5):(h=o-t,d=Y(a,e,n),l=B*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=U(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);he(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:vi(e.align),textBaseline:"middle",translation:[r,a]})}}function gc(i,t){const e=new Xn({ctx:i.ctx,options:t,chart:i});Q.configure(i,e,t),Q.addBox(i,e),i.titleBlock=e}var Wc={id:"title",_element:Xn,start(i,t,e){gc(i,e)},stop(i){const t=i.titleBlock;Q.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;Q.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Qt={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=li(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function nt(i,t){return t&&(H(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function dt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function pc(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function Ws(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=U(t.bodyFont),c=U(t.titleFont),h=U(t.footerFont),d=o.length,f=n.length,u=s.length,g=tt(t.padding);let p=g.height,m=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=u*v+(b-u)*l.lineHeight+(b-1)*t.bodySpacing}f&&(p+=t.footerMarginTop+f*h.lineHeight+(f-1)*t.footerSpacing);let _=0;const y=function(v){m=Math.max(m,e.measureText(v).width+_)};return e.save(),e.font=c.string,L(i.title,y),e.font=l.string,L(i.beforeBody.concat(i.afterBody),y),_=t.displayColors?r+2+t.boxPadding:0,L(s,v=>{L(v.before,y),L(v.lines,y),L(v.after,y)}),_=0,e.font=h.string,L(i.footer,y),e.restore(),m+=g.width,{width:m,height:p}}function mc(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function bc(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function _c(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),bc(c,i,t,e)&&(c="center"),c}function Hs(i,t,e){const s=e.yAlign||t.yAlign||mc(i,e);return{xAlign:e.xAlign||t.xAlign||_c(i,t,e,s),yAlign:s}}function xc(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function yc(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function Vs(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:f,bottomRight:u}=Bt(r);let g=xc(t,a);const p=yc(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,f)+n:a==="right"&&(g+=Math.max(d,u)+n),{x:X(g,0,s.width-t.width),y:X(p,0,s.height-t.height)}}function De(i,t,e){const s=tt(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function Ns(i){return nt([],dt(i))}function vc(i,t,e){return Lt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function js(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const Un={beforeTitle:ct,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:ct,beforeBody:ct,beforeLabel:ct,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return R(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:ct,afterBody:ct,beforeFooter:ct,footer:ct,afterFooter:ct};function q(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?Un[t].call(e,s):n}class pi extends it{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new On(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=vc(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=q(s,"beforeTitle",this,t),o=q(s,"title",this,t),r=q(s,"afterTitle",this,t);let a=[];return a=nt(a,dt(n)),a=nt(a,dt(o)),a=nt(a,dt(r)),a}getBeforeBody(t,e){return Ns(q(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return L(t,o=>{const r={before:[],lines:[],after:[]},a=js(s,o);nt(r.before,dt(q(a,"beforeLabel",this,o))),nt(r.lines,q(a,"label",this,o)),nt(r.after,dt(q(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return Ns(q(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=q(s,"beforeFooter",this,t),o=q(s,"footer",this,t),r=q(s,"afterFooter",this,t);let a=[];return a=nt(a,dt(n)),a=nt(a,dt(o)),a=nt(a,dt(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(pc(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,f)=>t.filter(h,d,f,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),L(a,h=>{const d=js(t.callbacks,h);n.push(q(d,"labelColor",this,h)),o.push(q(d,"labelPointStyle",this,h)),r.push(q(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=Qt[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=Ws(this,s),c=Object.assign({},a,l),h=Hs(this.chart,s,c),d=Vs(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Bt(a),{x:f,y:u}=t,{width:g,height:p}=e;let m,b,_,y,v,x;return o==="center"?(v=u+p/2,n==="left"?(m=f,b=m-r,y=v+r,x=v-r):(m=f+g,b=m+r,y=v-r,x=v+r),_=m):(n==="left"?b=f+Math.max(l,h)+r:n==="right"?b=f+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(y=u,v=y-r,m=b-r,_=b+r):(y=u+p,v=y+r,m=b+r,_=b-r),x=y),{x1:m,x2:b,x3:_,y1:y,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Wt(s.rtl,this.x,this.width);for(t.x=De(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=U(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=U(o.bodyFont),d=De(this,"left",o),f=n.x(d),u=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+u;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(f,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,hi(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,hi(t,p,m,b)}else{t.lineWidth=A(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=n.leftForLtr(f,c),m=n.leftForLtr(n.xPlus(f,1),c-2),b=Bt(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Ee(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Ee(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=U(s.bodyFont);let f=d.lineHeight,u=0;const g=Wt(s.rtl,this.x,this.width),p=function(M){e.fillText(M,g.x(t.x+u),t.y+f/2),t.y+=f+o},m=g.textAlign(r);let b,_,y,v,x,k,S;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=De(this,m,s),e.fillStyle=s.bodyColor,L(this.beforeBody,p),u=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,k=n.length;v<k;++v){for(b=n[v],_=this.labelTextColors[v],e.fillStyle=_,L(b.before,p),y=b.lines,a&&y.length&&(this._drawColorBox(e,t,v,g,s),f=Math.max(d.lineHeight,l)),x=0,S=y.length;x<S;++x)p(y[x]),f=d.lineHeight;L(b.after,p)}u=0,f=d.lineHeight,L(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Wt(s.rtl,this.x,this.width);for(t.x=De(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=U(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:f,bottomLeft:u,bottomRight:g}=Bt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-f,l),e.quadraticCurveTo(a+c,l,a+c,l+f),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+u,l+h),e.quadraticCurveTo(a,l+h,a,l+h-u),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=Qt[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Ws(this,t),l=Object.assign({},r,this._size),c=Hs(e,t,l),h=Vs(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=tt(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),Mn(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),kn(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!Le(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!Le(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=Qt[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}w(pi,"positioners",Qt);var Hc={id:"tooltip",_element:pi,positioners:Qt,afterInit(i,t,e){e&&(i.tooltip=new pi({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Un},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const Mc=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function kc(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Mc(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Sc=(i,t)=>i===null?null:X(Math.round(i),0,t);function $s(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ys extends Nt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(R(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:kc(s,t,O(e,t),this._addedLabels),Sc(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return $s.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}w(Ys,"id","category"),w(Ys,"defaults",{ticks:{callback:$s}});function wc(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:f}=i,u=o||1,g=h-1,{min:p,max:m}=t,b=!R(r),_=!R(a),y=!R(c),v=(m-p)/(d+1);let x=Vi((m-p)/g/u)*u,k,S,M,P;if(x<1e-14&&!b&&!_)return[{value:p},{value:m}];P=Math.ceil(m/x)-Math.floor(p/x),P>g&&(x=Vi(P*x/g/u)*u),R(l)||(k=Math.pow(10,l),x=Math.ceil(x*k)/k),n==="ticks"?(S=Math.floor(p/x)*x,M=Math.ceil(m/x)*x):(S=p,M=m),b&&_&&o&&Lo((a-r)/o,x/1e3)?(P=Math.round(Math.min((a-r)/x,h)),x=(a-r)/P,S=r,M=a):y?(S=b?r:S,M=_?a:M,P=c-1,x=(M-S)/P):(P=(M-S)/x,ee(P,Math.round(P),x/1e3)?P=Math.round(P):P=Math.ceil(P));const C=Math.max(Ni(x),Ni(S));k=Math.pow(10,R(l)?C:l),S=Math.round(S*k)/k,M=Math.round(M*k)/k;let D=0;for(b&&(f&&S!==r?(e.push({value:r}),S<r&&D++,ee(Math.round((S+D*x)*k)/k,r,Xs(r,v,i))&&D++):S<r&&D++);D<P;++D){const T=Math.round((S+D*x)*k)/k;if(_&&T>a)break;e.push({value:T})}return _&&f&&M!==a?e.length&&ee(e[e.length-1].value,a,Xs(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!_||M===a)&&e.push({value:M}),e}function Xs(i,t,{horizontal:e,minRotation:s}){const n=ut(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class Pc extends Nt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return R(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=lt(n),c=lt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=wc(n,o);return t.bounds==="ticks"&&Fo(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return ki(t,this.chart.options.locale,this.options.ticks.format)}}class Us extends Pc{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=K(t)?t:0,this.max=K(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=ut(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}w(Us,"id","linear"),w(Us,"defaults",{ticks:{callback:gn.formatters.numeric}});const Ye={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},G=Object.keys(Ye);function Ks(i,t){return i-t}function qs(i,t){if(R(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),K(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(ae(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function Gs(i,t,e,s){const n=G.length;for(let o=G.indexOf(i);o<n-1;++o){const r=Ye[G[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return G[o]}return G[n-1]}function Dc(i,t,e,s,n){for(let o=G.length-1;o>=G.indexOf(e);o--){const r=G[o];if(Ye[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return G[e?G.indexOf(e):0]}function Oc(i){for(let t=G.indexOf(i)+1,e=G.length;t<e;++t)if(Ye[G[t]].common)return G[t]}function Zs(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=yi(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Cc(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function Js(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Cc(i,s,n,e)}class He extends Nt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new ma._date(t.adapters.date);n.init(e),te(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:qs(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=K(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=K(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=Wo(n,o,r);return this._unit=e.unit||(s.autoSkip?Gs(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Dc(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Oc(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Js(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=X(e,0,r),s=X(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||Gs(o.minUnit,e,s,this._getLabelCapacity(e)),a=O(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=ae(l)||l===!0,h={};let d=e,f,u;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(f=d,u=0;f<s;f=+t.add(f,a,r),u++)Zs(h,f,g);return(f===s||n.bounds==="ticks"||u===1)&&Zs(h,f,g),Object.keys(h).sort(Ks).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return I(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],f=s[e],u=c&&d&&f&&f.major;return this._adapter.format(t,n||(u?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=ut(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Js(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(qs(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return dn(t.sort(Ks))}}w(He,"id","time"),w(He,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Oe(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Dt(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Dt(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class Qs extends He{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Oe(e,this.min),this._tableRange=Oe(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(Oe(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return Oe(this._table,s*this._tableRange+this._minPos,!0)}}w(Qs,"id","timeseries"),w(Qs,"defaults",He.defaults);const Kn="label";function tn(i,t){typeof i=="function"?i(t):i&&(i.current=t)}function Ac(i,t){const e=i.options;e&&t&&Object.assign(e,t)}function qn(i,t){i.labels=t}function Gn(i,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Kn;const s=[];i.datasets=t.map(n=>{const o=i.datasets.find(r=>r[e]===n[e]);return!o||!n.data||s.includes(o)?{...n}:(s.push(o),Object.assign(o,n),o)})}function Tc(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Kn;const e={labels:[],datasets:[]};return qn(e,i.labels),Gn(e,i.datasets,t),e}function Rc(i,t){const{height:e=150,width:s=300,redraw:n=!1,datasetIdKey:o,type:r,data:a,options:l,plugins:c=[],fallbackContent:h,updateMode:d,...f}=i,u=ot.useRef(null),g=ot.useRef(null),p=()=>{u.current&&(g.current=new Ti(u.current,{type:r,data:Tc(a,o),options:l&&{...l},plugins:c}),tn(t,g.current))},m=()=>{tn(t,null),g.current&&(g.current.destroy(),g.current=null)};return ot.useEffect(()=>{!n&&g.current&&l&&Ac(g.current,l)},[n,l]),ot.useEffect(()=>{!n&&g.current&&qn(g.current.config.data,a.labels)},[n,a.labels]),ot.useEffect(()=>{!n&&g.current&&a.datasets&&Gn(g.current.config.data,a.datasets,o)},[n,a.datasets]),ot.useEffect(()=>{g.current&&(n?(m(),setTimeout(p)):g.current.update(d))},[n,l,a.labels,a.datasets,d]),ot.useEffect(()=>{g.current&&(m(),setTimeout(p))},[r]),ot.useEffect(()=>(p(),()=>m()),[]),en.createElement("canvas",{ref:u,role:"img",height:e,width:s,...f},h)}const Lc=ot.forwardRef(Rc);function Li(i,t){return Ti.register(t),ot.forwardRef((e,s)=>en.createElement(Lc,{...e,ref:s,type:i}))}const Vc=Li("line",Ae),Nc=Li("bar",Ce),jc=Li("doughnut",Zt);export{Pe as A,Nc as B,Ti as C,jc as D,Vc as L,ii as P,He as T,ce as _,ma as a,Ys as b,Us as c,oi as d,Ot as e,Hc as f,Bc as g,L as h,I as i,wt as j,ee as k,R as l,oe as m,V as n,tt as o,Wc as p,A as q,_e as r,lt as s,U as t,zc as u,O as v,Ae as w,Ce as x};
