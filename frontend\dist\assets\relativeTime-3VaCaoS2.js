import{r as c,a2 as j,b8 as B,b9 as P,ag as _}from"./index-LbZyOyVE.js";var q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M723 620.5C666.8 571.6 593.4 542 513 542s-153.8 29.6-210.1 78.6a8.1 8.1 0 00-.8 11.2l36 42.9c2.9 3.4 8 3.8 11.4.9C393.1 637.2 450.3 614 513 614s119.9 23.2 163.5 61.5c3.4 2.9 8.5 2.5 11.4-.9l36-42.9c2.8-3.3 2.4-8.3-.9-11.2zm117.4-140.1C751.7 406.5 637.6 362 513 362s-238.7 44.5-327.5 118.4a8.05 8.05 0 00-1 11.3l36 42.9c2.8 3.4 7.9 3.8 11.2 1C308 472.2 406.1 434 513 434s205 38.2 281.2 101.6c3.4 2.8 8.4 2.4 11.2-1l36-42.9c2.8-3.4 2.4-8.5-1-11.3zm116.7-139C835.7 241.8 680.3 182 511 182c-168.2 0-322.6 59-443.7 157.4a8 8 0 00-1.1 11.4l36 42.9c2.8 3.3 7.8 3.8 11.1 1.1C222 306.7 360.3 254 511 254c151.8 0 291 53.5 400 142.7 3.4 2.8 8.4 2.3 11.2-1.1l36-42.9c2.9-3.4 2.4-8.5-1.1-11.3zM448 778a64 64 0 10128 0 64 64 0 10-128 0z"}}]},name:"wifi",theme:"outlined"};function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},y.apply(this,arguments)}const L=(e,t)=>c.createElement(j,y({},e,{ref:t,icon:B})),H=c.forwardRef(L);function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},M.apply(this,arguments)}const N=(e,t)=>c.createElement(j,M({},e,{ref:t,icon:P})),J=c.forwardRef(N);function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$.apply(this,arguments)}const W=(e,t)=>c.createElement(j,$({},e,{ref:t,icon:q})),K=c.forwardRef(W);var h={exports:{}},D=h.exports,z;function F(){return z||(z=1,function(e,t){(function(r,n){e.exports=n()})(D,function(){return function(r,n,d){r=r||{};var s=n.prototype,R={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function T(a,o,f,p){return s.fromToBase(a,o,f,p)}d.en.relativeTime=R,s.fromToBase=function(a,o,f,p,E){for(var v,m,g,b=f.$locale().relativeTime||R,O=r.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],I=O.length,l=0;l<I;l+=1){var i=O[l];i.d&&(v=p?d(a).diff(f,i.d,!0):f.diff(a,i.d,!0));var u=(r.rounding||Math.round)(Math.abs(v));if(g=v>0,u<=i.r||!i.r){u<=1&&l>0&&(i=O[l-1]);var x=b[i.l];E&&(u=E(""+u)),m=typeof x=="string"?x.replace("%d",u):x(u,o,i.l,g);break}}if(o)return m;var w=g?b.future:b.past;return typeof w=="function"?w(m):w.replace("%s",m)},s.to=function(a,o){return T(a,o,this,!0)},s.from=function(a,o){return T(a,o,this)};var C=function(a){return a.$u?d.utc():d()};s.toNow=function(a){return this.to(C(this),a)},s.fromNow=function(a){return this.from(C(this),a)}}})}(h)),h.exports}var A=F();const Q=_(A);export{H as R,J as a,K as b,Q as r};
