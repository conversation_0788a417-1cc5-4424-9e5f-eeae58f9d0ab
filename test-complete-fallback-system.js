#!/usr/bin/env node
/**
 * Comprehensive Redis Fallback System Test
 * Tests all aspects of the Redis fallback implementation
 */

import http from 'http';
import https from 'https';
import { URL } from 'url';

/**
 * Make HTTP request using Node.js built-in modules
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const httpModule = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      timeout: options.timeout || 10000,
      headers: options.headers || {}
    };
    
    const req = httpModule.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const responseData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}
const BASE_URL = 'http://localhost:5000';
const HEALTH_ENDPOINTS = [
  '/api/health',
  '/api/health/redis',
  '/api/health/elasticsearch',
  '/api/health/pubsub'
];

const API_ENDPOINTS = [
  '/api/production',
  '/api/analytics',
  '/api/arrets',
  '/api/sidecards-prod-rejet'
];

/**
 * Test Results Tracking
 */
const testResults = {
  healthChecks: [],
  apiTests: [],
  fallbackValidation: [],
  performanceMetrics: []
};

/**
 * Color codes for console output
 */
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Test server connectivity
 */
async function testServerConnectivity() {
  console.log('\n' + '='.repeat(80));
  colorLog('cyan', '🚀 Testing Server Connectivity');
  console.log('='.repeat(80));
  
  try {
    const response = await makeRequest(`${BASE_URL}/api/health`, { timeout: 5000 });
    const serverHealth = response.data;
    
    colorLog('green', '✅ Server is responding');
    console.log(`📊 Server Status: ${serverHealth.status}`);
    console.log(`⏱️ Response Time: ${response.headers['x-response-time'] || 'N/A'}`);
    
    return true;
  } catch (error) {
    colorLog('red', '❌ Server connectivity failed');
    console.error('Error:', error.message);
    return false;
  }
}

/**
 * Test all health endpoints
 */
async function testHealthEndpoints() {
  console.log('\n' + '='.repeat(80));
  colorLog('cyan', '🏥 Testing Health Monitoring Endpoints');
  console.log('='.repeat(80));
  
  for (const endpoint of HEALTH_ENDPOINTS) {
    try {
      const startTime = Date.now();
      const response = await makeRequest(`${BASE_URL}${endpoint}`, { timeout: 10000 });
      const responseTime = Date.now() - startTime;
      
      const healthData = response.data;
      const status = healthData.status || healthData.redis?.status || 'unknown';
      
      testResults.healthChecks.push({
        endpoint,
        status,
        responseTime,
        success: true
      });
      
      colorLog('green', `✅ ${endpoint}`);
      console.log(`   Status: ${status}`);
      console.log(`   Response Time: ${responseTime}ms`);
      
      // Special handling for Redis health check
      if (endpoint === '/api/health/redis') {
        const redisStatus = healthData.redis || healthData;
        if (redisStatus.fallbackMode) {
          colorLog('yellow', `   🔄 Redis Fallback Mode: ${redisStatus.fallbackMode}`);
        }
        if (redisStatus.metrics) {
          console.log(`   📊 Operations: ${redisStatus.metrics.operations || 0}`);
          console.log(`   📈 Cache Hits: ${redisStatus.metrics.hits || 0}`);
        }
      }
      
    } catch (error) {
      testResults.healthChecks.push({
        endpoint,
        status: 'error',
        error: error.message,
        success: false
      });
      
      colorLog('red', `❌ ${endpoint}`);
      console.log(`   Error: ${error.message}`);
    }
  }
}

/**
 * Test API endpoints functionality
 */
async function testAPIEndpoints() {
  console.log('\n' + '='.repeat(80));
  colorLog('cyan', '🔌 Testing API Endpoints');
  console.log('='.repeat(80));
  
  for (const endpoint of API_ENDPOINTS) {
    try {
      const startTime = Date.now();
      const response = await makeRequest(`${BASE_URL}${endpoint}?limit=10`, { 
        timeout: 15000
      });
      const responseTime = Date.now() - startTime;
      
      const hasData = response.data && 
        (Array.isArray(response.data) ? response.data.length > 0 : Object.keys(response.data).length > 0);
      
      testResults.apiTests.push({
        endpoint,
        responseTime,
        dataReceived: hasData,
        statusCode: response.status,
        success: true
      });
      
      colorLog('green', `✅ ${endpoint}`);
      console.log(`   Status Code: ${response.status}`);
      console.log(`   Response Time: ${responseTime}ms`);
      console.log(`   Data Received: ${hasData ? 'Yes' : 'No'}`);
      
    } catch (error) {
      testResults.apiTests.push({
        endpoint,
        statusCode: error.status || 'timeout',
        error: error.message,
        success: false
      });
      
      colorLog('red', `❌ ${endpoint}`);
      console.log(`   Error: ${error.message}`);
    }
  }
}

/**
 * Test Redis fallback validation
 */
async function testRedisFallbackValidation() {
  console.log('\n' + '='.repeat(80));
  colorLog('cyan', '🔄 Validating Redis Fallback System');
  console.log('='.repeat(80));
  
  try {
    // Test Redis health endpoint for fallback status
    const response = await makeRequest(`${BASE_URL}/api/health/redis`);
    const redisHealth = response.data.redis || response.data;
    
    testResults.fallbackValidation.push({
      test: 'Redis Fallback Detection',
      fallbackMode: redisHealth.fallbackMode || false,
      connectionStatus: redisHealth.status,
      success: true
    });
    
    if (redisHealth.fallbackMode) {
      colorLog('yellow', '🔄 Redis Fallback Mode Confirmed: ACTIVE');
      console.log('   ✓ Application running without Redis dependency');
      console.log('   ✓ In-memory fallback system operational');
      
      if (redisHealth.metrics) {
        console.log(`   📊 Fallback Operations: ${redisHealth.metrics.operations || 0}`);
        console.log(`   💾 Cache Entries: ${redisHealth.metrics.entries || 0}`);
      }
    } else {
      colorLog('green', '✅ Redis Connection: ACTIVE');
      console.log('   ✓ Direct Redis connection established');
    }
    
    // Test multiple API calls to validate caching fallback
    colorLog('blue', '\n🧪 Testing Cache Fallback Performance...');
    
    const cacheTestEndpoint = `${BASE_URL}/api/sidecards-prod-rejet`;
    const cacheTests = [];
    
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now();
      await makeRequest(cacheTestEndpoint, { timeout: 10000 });
      const responseTime = Date.now() - startTime;
      cacheTests.push(responseTime);
      console.log(`   Test ${i + 1}: ${responseTime}ms`);
    }
    
    const avgResponseTime = cacheTests.reduce((a, b) => a + b, 0) / cacheTests.length;
    testResults.performanceMetrics.push({
      test: 'Cache Performance',
      averageTime: avgResponseTime,
      samples: cacheTests.length,
      success: true
    });
    
    colorLog('blue', `   📊 Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
    
  } catch (error) {
    testResults.fallbackValidation.push({
      test: 'Redis Fallback Detection',
      error: error.message,
      success: false
    });
    
    colorLog('red', '❌ Fallback validation failed');
    console.error('Error:', error.message);
  }
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
  console.log('\n' + '='.repeat(80));
  colorLog('bold', '📋 COMPREHENSIVE TEST REPORT');
  console.log('='.repeat(80));
  
  // Health Checks Summary
  const healthChecksPassed = testResults.healthChecks.filter(t => t.success).length;
  const healthChecksTotal = testResults.healthChecks.length;
  
  colorLog('cyan', '\n🏥 Health Monitoring Summary:');
  console.log(`   ✅ Passed: ${healthChecksPassed}/${healthChecksTotal}`);
  console.log(`   📊 Success Rate: ${((healthChecksPassed / healthChecksTotal) * 100).toFixed(1)}%`);
  
  // API Tests Summary
  const apiTestsPassed = testResults.apiTests.filter(t => t.success).length;
  const apiTestsTotal = testResults.apiTests.length;
  
  colorLog('cyan', '\n🔌 API Functionality Summary:');
  console.log(`   ✅ Passed: ${apiTestsPassed}/${apiTestsTotal}`);
  console.log(`   📊 Success Rate: ${((apiTestsPassed / apiTestsTotal) * 100).toFixed(1)}%`);
  
  // Fallback Validation Summary
  const fallbackTestsPassed = testResults.fallbackValidation.filter(t => t.success).length;
  const fallbackTestsTotal = testResults.fallbackValidation.length;
  
  colorLog('cyan', '\n🔄 Fallback System Summary:');
  console.log(`   ✅ Passed: ${fallbackTestsPassed}/${fallbackTestsTotal}`);
  console.log(`   📊 Success Rate: ${((fallbackTestsPassed / fallbackTestsTotal) * 100).toFixed(1)}%`);
  
  // Performance Summary
  if (testResults.performanceMetrics.length > 0) {
    colorLog('cyan', '\n⚡ Performance Summary:');
    testResults.performanceMetrics.forEach(metric => {
      if (metric.success) {
        console.log(`   ${metric.test}: ${metric.averageTime.toFixed(2)}ms avg`);
      }
    });
  }
  
  // Overall Status
  const totalTests = healthChecksTotal + apiTestsTotal + fallbackTestsTotal;
  const totalPassed = healthChecksPassed + apiTestsPassed + fallbackTestsPassed;
  const overallSuccessRate = (totalPassed / totalTests) * 100;
  
  console.log('\n' + '='.repeat(80));
  if (overallSuccessRate >= 90) {
    colorLog('green', '🎉 COMPREHENSIVE FALLBACK SYSTEM: FULLY OPERATIONAL');
  } else if (overallSuccessRate >= 70) {
    colorLog('yellow', '⚠️ FALLBACK SYSTEM: MOSTLY OPERATIONAL');
  } else {
    colorLog('red', '❌ FALLBACK SYSTEM: NEEDS ATTENTION');
  }
  
  console.log(`📊 Overall Success Rate: ${overallSuccessRate.toFixed(1)}%`);
  console.log(`✅ Tests Passed: ${totalPassed}/${totalTests}`);
  console.log('='.repeat(80));
  
  return overallSuccessRate;
}

/**
 * Main test execution
 */
async function runComprehensiveTests() {
  colorLog('bold', '🚀 REDIS FALLBACK SYSTEM COMPREHENSIVE TEST');
  colorLog('cyan', '📅 ' + new Date().toISOString());
  
  // Test server connectivity first
  const serverUp = await testServerConnectivity();
  if (!serverUp) {
    colorLog('red', '❌ Cannot proceed - server is not responding');
    process.exit(1);
  }
  
  // Run all tests
  await testHealthEndpoints();
  await testAPIEndpoints();
  await testRedisFallbackValidation();
  
  // Generate final report
  const successRate = generateTestReport();
  
  // Exit with appropriate code
  process.exit(successRate >= 90 ? 0 : 1);
}

// Start testing
runComprehensiveTests().catch(error => {
  colorLog('red', '❌ Test execution failed');
  console.error(error);
  process.exit(1);
});
