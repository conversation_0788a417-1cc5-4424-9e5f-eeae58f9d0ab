/**
 * Unified Dashboard GraphQL Resolvers
 * 
 * Provides GraphQL resolvers that can work with both Elasticsearch (primary)
 * and MySQL (fallback) data sources with automatic switching.
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

import { executeQuery } from '../../utils/dbUtils.js';
import redisConfig from '../../config/redisConfig.js';
import stopTableResolvers from './stopTableResolvers.js';
import elasticsearchDashboardService from '../../services/elasticsearchDashboardService.js';
import dashboardDataIndexer from '../../services/dashboardDataIndexer.js';
import crypto from 'crypto';

class UnifiedDashboardResolvers {
  constructor() {
    this.cacheTimeout = 300; // 5 minutes cache
  }

  /**
   * Direct MySQL query for machine stops (fallback)
   */
  async getMachineStopsFromMySQL(filters = {}) {
    try {
      let query = `
        SELECT 
          Machine_Name as machine_name,
          Date_Insert as date_insert,
          Part_NO as part_no,
          Code_Stop as stop_code,
          Debut_Stop as start_time,
          Fin_Stop_Time as end_time,
          Regleur_Prenom as operator
        FROM machine_stop_table_mould 
        WHERE 1=1
      `;
      
      const queryParams = [];
      
      // Add filters
      if (filters.machineModel) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`%${filters.machineModel}%`);
      }
      
      if (filters.machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(filters.machine);
      }
      
      if (filters.startDate) {
        query += ` AND STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i') >= ?`;
        queryParams.push(filters.startDate);
      }
      
      if (filters.endDate) {
        query += ` AND STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i') <= ?`;
        queryParams.push(filters.endDate);
      }
      
      query += ` ORDER BY Date_Insert DESC LIMIT 1000`;
      
      const result = await executeQuery(query, queryParams);
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching machine stops from MySQL:', error);
      return [];
    }
  }

  /**
   * Get top 5 stop reasons from MySQL (fallback)
   */
  async getTop5StopsFromMySQL(filters = {}) {
    try {
      let query = `
        SELECT 
          Code_Stop as stopName,
          COUNT(*) as count,
          SUM(CASE 
            WHEN Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL 
            THEN TIMESTAMPDIFF(MINUTE, 
              STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i'), 
              STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i')
            ) 
            ELSE 0 
          END) as totalDuration
        FROM machine_stop_table_mould 
        WHERE Code_Stop IS NOT NULL
      `;
      
      const queryParams = [];
      
      // Add filters
      if (filters.machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(filters.machine);
      }
      
      query += `
        GROUP BY Code_Stop 
        ORDER BY count DESC 
        LIMIT 5
      `;
      
      const result = await executeQuery(query, queryParams);
      const data = result.success ? result.data : [];
      
      // Calculate percentages
      const totalCount = data.reduce((sum, item) => sum + item.count, 0);
      return {
        reasons: data.map(item => ({
          ...item,
          percentage: totalCount > 0 ? Math.round((item.count / totalCount) * 100) : 0
        }))
      };
    } catch (error) {
      console.error('Error fetching top 5 stops from MySQL:', error);
      return { reasons: [] };
    }
  }

  /**
   * Get machine comparison from MySQL (fallback)
   */
  async getMachineComparisonFromMySQL(filters = {}) {
    try {
      let query = `
        SELECT 
          Machine_Name,
          COUNT(*) as stops,
          SUM(CASE 
            WHEN Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL 
            THEN TIMESTAMPDIFF(MINUTE, 
              STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i'), 
              STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i')
            ) 
            ELSE 0 
          END) as totalDuration
        FROM machine_stop_table_mould 
        WHERE Machine_Name IS NOT NULL
      `;
      
      const queryParams = [];
      
      // Add filters
      if (filters.machineModel) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`%${filters.machineModel}%`);
      }
      
      query += `
        GROUP BY Machine_Name 
        ORDER BY stops DESC
      `;
      
      const result = await executeQuery(query, queryParams);
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching machine comparison from MySQL:', error);
      return [];
    }
  }
  async selectDataSource() {
    try {
      const isElasticsearchAvailable = await elasticsearchDashboardService.isAvailable();
      
      if (isElasticsearchAvailable) {
        console.log('Using Elasticsearch as primary data source');
        return 'elasticsearch';
      } else {
        console.warn('Elasticsearch unavailable, falling back to MySQL');
        return 'mysql';
      }
    } catch (error) {
      console.error('Error selecting data source:', error);
      return 'mysql';
    }
  }

  /**
   * Cache key generator
   */
  generateCacheKey(operation, filters = {}) {
    const filtersStr = JSON.stringify(filters);
    const hash = crypto.createHash('md5').update(filtersStr).digest('hex');
    return `dashboard:${operation}:${hash}`;
  }

  /**
   * Get data with caching
   */
  async getCachedData(cacheKey, dataFetcher) {
    try {
      // Try to get from cache first
      if (redisConfig.isConnected) {
        const client = redisConfig.getClient();
        const cached = await client.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }
      
      // Fetch fresh data
      const data = await dataFetcher();
      
      // Cache the result
      if (redisConfig.isConnected && data) {
        const client = redisConfig.getClient();
        await client.setex(cacheKey, this.cacheTimeout, JSON.stringify(data));
      }
      
      return data;
    } catch (error) {
      console.error('Error with cached data operation:', error);
      // Return fresh data without caching on error
      return await dataFetcher();
    }
  }

  /**
   * Transform MySQL data to match Elasticsearch format
   */
  transformMySQLToElasticsearchFormat(mysqlData) {
    if (!mysqlData || !Array.isArray(mysqlData)) {
      return mysqlData;
    }

    return mysqlData.map(record => {
      // Parse dates with flexible format handling
      const parseDate = (dateStr) => {
        if (!dateStr) return null;
        const cleaned = dateStr.toString().trim();
        
        // Handle DD/MM/YYYY HH:MM:SS format
        const ddmmyyyyMatch = cleaned.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2}):(\d{2})/);
        if (ddmmyyyyMatch) {
          const [, day, month, year, hour, minute, second] = ddmmyyyyMatch;
          return new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute}:${second}`);
        }
        
        // Fallback to direct parsing
        const fallbackDate = new Date(cleaned);
        return isNaN(fallbackDate.getTime()) ? null : fallbackDate;
      };

      const startTime = parseDate(record.start_time || record.startTime);
      const endTime = parseDate(record.end_time || record.endTime);
      
      // Calculate duration
      let duration = null;
      if (startTime && endTime) {
        duration = Math.round((endTime - startTime) / (1000 * 60)); // minutes
      }

      return {
        id: record.id,
        machineId: record.machine_id || record.machineId || 'IPS01',
        machineName: record.machine_name || record.machineName || record.machine_id || 'IPS01',
        stopReason: record.stop_reason || record.stopReason || record.reason || 'Unknown',
        stopCode: record.stop_code || record.stopCode || record.code,
        startTime: startTime ? startTime.toISOString() : null,
        endTime: endTime ? endTime.toISOString() : null,
        duration: duration,
        shift: record.shift || 'Unknown',
        operator: record.operator || record.operateur || 'Unknown',
        description: record.description || record.commentaire || '',
        category: this.categorizeStopReason(record.stop_reason || record.stopReason),
        severity: this.calculateSeverity(duration)
      };
    });
  }

  /**
   * Categorize stop reasons (same logic as indexer)
   */
  categorizeStopReason(reason) {
    if (!reason) return 'Unknown';
    
    const reasonLower = reason.toLowerCase();
    
    if (reasonLower.includes('maintenance') || reasonLower.includes('maint')) {
      return 'Maintenance';
    } else if (reasonLower.includes('panne') || reasonLower.includes('breakdown') || reasonLower.includes('failure')) {
      return 'Equipment Failure';
    } else if (reasonLower.includes('matière') || reasonLower.includes('material') || reasonLower.includes('approvisionnement')) {
      return 'Material Issue';
    } else if (reasonLower.includes('qualité') || reasonLower.includes('quality') || reasonLower.includes('defect')) {
      return 'Quality Issue';
    } else if (reasonLower.includes('changement') || reasonLower.includes('setup') || reasonLower.includes('réglage')) {
      return 'Setup/Changeover';
    } else if (reasonLower.includes('pause') || reasonLower.includes('break') || reasonLower.includes('repas')) {
      return 'Scheduled Break';
    } else {
      return 'Other';
    }
  }

  /**
   * Calculate severity based on duration
   */
  calculateSeverity(durationMinutes) {
    if (!durationMinutes || durationMinutes < 0) return 'Unknown';
    
    if (durationMinutes <= 5) return 'Low';
    if (durationMinutes <= 30) return 'Medium';
    if (durationMinutes <= 120) return 'High';
    return 'Critical';
  }

  /**
   * Get all machine stops with unified data source
   */
  async getAllMachineStops(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('allStops', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const result = await elasticsearchDashboardService.getDetailedStops(filters, { page: 1, size: 1000 });
          return {
            stops: result.stops,
            total: result.total,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback to MySQL
          const mysqlData = await this.getMachineStopsFromMySQL(filters);
          const transformedStops = this.transformMySQLToElasticsearchFormat(mysqlData);
          
          return {
            stops: transformedStops,
            total: transformedStops.length,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source:`, error);
        
        // Try fallback if primary fails
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, trying MySQL fallback...');
          const mysqlData = await this.getMachineStopsFromMySQL(filters);
          const transformedStops = this.transformMySQLToElasticsearchFormat(mysqlData);
          
          return {
            stops: transformedStops,
            total: transformedStops.length,
            dataSource: 'mysql-fallback'
          };
        }
        throw error;
      }
    });
  }

  /**
   * Get top 5 stop reasons
   */
  async getTop5Stops(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('top5Stops', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const reasons = await elasticsearchDashboardService.getTopStopReasons(filters, 5);
          
          // Calculate percentages
          const totalDuration = reasons.reduce((sum, reason) => sum + reason.totalDuration, 0);
          const processedReasons = reasons.map(reason => ({
            ...reason,
            percentage: totalDuration > 0 ? Math.round((reason.totalDuration / totalDuration) * 100) : 0
          }));
          
          return {
            reasons: processedReasons,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback to MySQL
          const mysqlResult = await this.getTop5StopsFromMySQL(filters);
          return {
            ...mysqlResult,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source:`, error);
        
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, trying MySQL fallback...');
          const mysqlResult = await this.getTop5StopsFromMySQL(filters);
          return {
            ...mysqlResult,
            dataSource: 'mysql-fallback'
          };
        }
        throw error;
      }
    });
  }

  /**
   * Get machine comparison data
   */
  async getMachineStopComparison(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('machineComparison', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const machines = await elasticsearchDashboardService.getMachineComparison(filters);
          return {
            machines,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback to MySQL
          const mysqlResult = await this.getMachineComparisonFromMySQL(filters);
          return {
            machines: mysqlResult,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source:`, error);
        
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, trying MySQL fallback...');
          const mysqlResult = await this.getMachineComparisonFromMySQL(filters);
          return {
            machines: mysqlResult,
            dataSource: 'mysql-fallback'
          };
        }
        throw error;
      }
    });
  }

  /**
   * Get dashboard essential statistics
   */
  async getDashboardStats(_, { filters = {} }) {
    const cacheKey = this.generateCacheKey('dashboardStats', filters);
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const stats = await elasticsearchDashboardService.getEssentialStats(filters);
          return {
            ...stats,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback: calculate stats from MySQL data
          const stopsResult = await this.getAllMachineStops(_, { filters });
          const stops = stopsResult.stops || [];
          
          const totalStops = stops.length;
          const totalDuration = stops.reduce((sum, stop) => sum + (stop.duration || 0), 0);
          const averageDuration = totalStops > 0 ? Math.round(totalDuration / totalStops) : 0;
          const uniqueMachines = [...new Set(stops.map(stop => stop.machineId))].length;
          
          // Group by severity
          const severityGroups = stops.reduce((acc, stop) => {
            const severity = stop.severity || 'Unknown';
            acc[severity] = (acc[severity] || 0) + 1;
            return acc;
          }, {});
          
          // Group by category
          const categoryGroups = stops.reduce((acc, stop) => {
            const category = stop.category || 'Unknown';
            acc[category] = (acc[category] || 0) + 1;
            return acc;
          }, {});
          
          return {
            totalStops,
            totalDuration,
            averageDuration,
            uniqueMachines,
            stopsBySeverity: Object.entries(severityGroups).map(([severity, count]) => ({ severity, count })),
            stopsByCategory: Object.entries(categoryGroups).map(([category, count]) => ({ category, count })),
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source:`, error);
        
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, calculating stats from MySQL...');
          // Recursive call will use MySQL
          return await this.getDashboardStats(_, { filters });
        }
        throw error;
      }
    });
  }

  /**
   * Get time evolution data for charts
   */
  async getStopEvolution(_, { filters = {}, interval = 'day' }) {
    const cacheKey = this.generateCacheKey('stopEvolution', { ...filters, interval });
    
    return await this.getCachedData(cacheKey, async () => {
      const dataSource = await this.selectDataSource();
      
      try {
        if (dataSource === 'elasticsearch') {
          const evolution = await elasticsearchDashboardService.getEvolutionData(filters, interval);
          return {
            evolution,
            dataSource: 'elasticsearch'
          };
        } else {
          // Fallback: calculate evolution from MySQL data
          const stopsResult = await this.getAllMachineStops(_, { filters });
          const stops = stopsResult.stops || [];
          
          // Group by date interval
          const grouped = stops.reduce((acc, stop) => {
            if (!stop.startTime) return acc;
            
            const date = new Date(stop.startTime);
            let key;
            
            if (interval === 'hour') {
              key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}T${date.getHours().toString().padStart(2, '0')}:00:00`;
            } else if (interval === 'week') {
              const week = this.getWeekNumber(date);
              key = `${date.getFullYear()}-W${week.toString().padStart(2, '0')}`;
            } else {
              key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
            }
            
            if (!acc[key]) {
              acc[key] = {
                date: key,
                stopsCount: 0,
                totalDuration: 0,
                categories: {}
              };
            }
            
            acc[key].stopsCount++;
            acc[key].totalDuration += stop.duration || 0;
            
            const category = stop.category || 'Unknown';
            acc[key].categories[category] = (acc[key].categories[category] || 0) + 1;
            
            return acc;
          }, {});
          
          // Convert to array and sort
          const evolution = Object.values(grouped)
            .map(item => ({
              ...item,
              categories: Object.entries(item.categories).map(([category, count]) => ({ category, count }))
            }))
            .sort((a, b) => a.date.localeCompare(b.date));
          
          return {
            evolution,
            dataSource: 'mysql'
          };
        }
      } catch (error) {
        console.error(`Error with ${dataSource} data source:`, error);
        
        if (dataSource === 'elasticsearch') {
          console.log('Elasticsearch failed, calculating evolution from MySQL...');
          // Recursive call will use MySQL
          return await this.getStopEvolution(_, { filters, interval });
        }
        throw error;
      }
    });
  }

  /**
   * Get week number (same as indexer)
   */
  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }

  /**
   * Get data source status for monitoring
   */
  async getDataSourceStatus() {
    try {
      const isElasticsearchAvailable = await elasticsearchDashboardService.isAvailable();
      let elasticsearchStats = null;
      
      if (isElasticsearchAvailable) {
        elasticsearchStats = await elasticsearchDashboardService.getIndexStats();
      }
      
      return {
        primarySource: isElasticsearchAvailable ? 'elasticsearch' : 'mysql',
        elasticsearch: {
          available: isElasticsearchAvailable,
          stats: elasticsearchStats
        },
        mysql: {
          available: true, // Assume MySQL is always available if we reach here
          stats: null // Could add MySQL stats here
        }
      };
    } catch (error) {
      console.error('Error getting data source status:', error);
      return {
        primarySource: 'mysql',
        elasticsearch: {
          available: false,
          error: error.message
        },
        mysql: {
          available: true
        }
      };
    }
  }
}

// Create instance and export resolvers
const unifiedResolvers = new UnifiedDashboardResolvers();

const resolvers = {
  getAllMachineStops: (parent, args, context) => unifiedResolvers.getAllMachineStops(parent, args, context),
  getTop5Stops: (parent, args, context) => unifiedResolvers.getTop5Stops(parent, args, context),
  getMachineStopComparison: (parent, args, context) => unifiedResolvers.getMachineStopComparison(parent, args, context),
  getDashboardStats: (parent, args, context) => unifiedResolvers.getDashboardStats(parent, args, context),
  getStopEvolution: (parent, args, context) => unifiedResolvers.getStopEvolution(parent, args, context),
  getDataSourceStatus: (parent, args, context) => unifiedResolvers.getDataSourceStatus(parent, args, context),
  selectDataSource: () => unifiedResolvers.selectDataSource()
};

export default resolvers;
