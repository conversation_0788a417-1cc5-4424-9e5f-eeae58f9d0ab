/**
 * Role Hierarchy and Permission Namespace System
 *
 * This module defines the role hierarchy and permission namespaces for the application.
 * It supports both vertical inheritance (higher roles inherit from lower roles)
 * and horizontal separation (department-specific permissions).
 */

/**
 * Role hierarchy definition
 * Higher roles inherit all permissions from roles below them in their branch
 *
 * Format:
 * {
 *   roleName: {
 *     inherits: ['role1', 'role2'], // Roles this role inherits from
 *     level: number,                // Hierarchy level (higher number = higher position)
 *     description: 'string'         // Role description
 *   }
 * }
 */
export const ROLE_HIERARCHY = {
  // Top-level role
  'admin': {
    inherits: ['head_manager'],
    level: 100,
    description: 'Administrator with full access to all systems'
  },

  // Mid-level roles
  'head_manager': {
    inherits: ['finance_manager', 'hr_manager', 'operations_manager', 'production_manager'],
    level: 80,
    description: 'Head manager with access to all departments but not user management'
  },

  // Department manager roles
  'finance_manager': {
    inherits: ['finance_staff'],
    level: 60,
    description: 'Finance department manager'
  },
  'hr_manager': {
    inherits: ['hr_staff'],
    level: 60,
    description: 'Human resources department manager'
  },
  'operations_manager': {
    inherits: ['operations_staff'],
    level: 60,
    description: 'Operations department manager'
  },
  'production_manager': {
    inherits: ['production_staff'],
    level: 60,
    description: 'Production department manager'
  },

  // Staff-level roles
  'finance_staff': {
    inherits: ['user'],
    level: 40,
    description: 'Finance department staff'
  },
  'hr_staff': {
    inherits: ['user'],
    level: 40,
    description: 'Human resources department staff'
  },
  'operations_staff': {
    inherits: ['user'],
    level: 40,
    description: 'Operations department staff'
  },
  'production_staff': {
    inherits: ['user'],
    level: 40,
    description: 'Production department staff'
  },

  // Test role for permission testing
  'RoleTest': {
    inherits: ['user'],
    level: 50,
    description: 'Test role for permission testing'
  },

  // Base role
  'user': {
    inherits: [],
    level: 10,
    description: 'Basic user with minimal access'
  }
};

/**
 * Permission namespace definitions
 *
 * Format:
 * {
 *   namespace: {
 *     description: 'string',
 *     permissions: {
 *       permissionName: 'description'
 *     }
 *   }
 * }
 */
export const PERMISSION_NAMESPACES = {
  // System-wide permissions (no namespace prefix)
  'system': {
    description: 'System-wide permissions',
    permissions: {
      'view_dashboard': 'View main dashboard',
      'admin': 'Full administrative access',
      'manage_users': 'Manage user accounts',
      'manage_roles': 'Manage roles and permissions',
      'view_all_departments': 'View data from all departments',
      'view_reports': 'View system reports',
      'create_reports': 'Create system reports'
    }
  },

  // Finance department permissions
  'finance': {
    description: 'Finance department permissions',
    permissions: {
      'view_reports': 'View financial reports',
      'create_reports': 'Create financial reports',
      'manage_budgets': 'Manage department budgets',
      'approve_expenses': 'Approve expense reports',
      'view_transactions': 'View financial transactions',
      'manage_transactions': 'Manage financial transactions'
    }
  },

  // HR department permissions
  'hr': {
    description: 'Human Resources department permissions',
    permissions: {
      'view_employees': 'View employee records',
      'manage_employees': 'Manage employee records',
      'view_payroll': 'View payroll information',
      'manage_payroll': 'Manage payroll information',
      'view_performance': 'View employee performance data',
      'manage_performance': 'Manage employee performance data'
    }
  },

  // Operations department permissions
  'operations': {
    description: 'Operations department permissions',
    permissions: {
      'view_inventory': 'View inventory data',
      'manage_inventory': 'Manage inventory',
      'view_logistics': 'View logistics data',
      'manage_logistics': 'Manage logistics operations',
      'view_suppliers': 'View supplier information',
      'manage_suppliers': 'Manage supplier relationships'
    }
  },

  // Production department permissions
  'production': {
    description: 'Production department permissions',
    permissions: {
      'view_production': 'View production data',
      'manage_production': 'Manage production operations',
      'view_quality': 'View quality control data',
      'manage_quality': 'Manage quality control processes',
      'view_maintenance': 'View maintenance data',
      'manage_maintenance': 'Manage maintenance operations'
    }
  }
};

/**
 * Default role permissions mapping
 * Defines the default permissions assigned to each role
 */
export const DEFAULT_ROLE_PERMISSIONS = {
  // Admin has all permissions
  'admin': [
    'system:admin',
    'system:manage_users',
    'system:manage_roles',
    'system:view_all_departments',
    'system:view_dashboard'
  ],

  // Head manager has access to all departments but not user management
  'head_manager': [
    'system:view_all_departments',
    'system:view_dashboard'
  ],

  // Department managers have full access to their department
  'finance_manager': [
    'system:view_dashboard',
    'finance:view_reports',
    'finance:create_reports',
    'finance:manage_budgets',
    'finance:approve_expenses',
    'finance:view_transactions',
    'finance:manage_transactions'
  ],

  'hr_manager': [
    'system:view_dashboard',
    'hr:view_employees',
    'hr:manage_employees',
    'hr:view_payroll',
    'hr:manage_payroll',
    'hr:view_performance',
    'hr:manage_performance'
  ],

  'operations_manager': [
    'system:view_dashboard',
    'operations:view_inventory',
    'operations:manage_inventory',
    'operations:view_logistics',
    'operations:manage_logistics',
    'operations:view_suppliers',
    'operations:manage_suppliers'
  ],

  'production_manager': [
    'system:view_dashboard',
    'production:view_production',
    'production:manage_production',
    'production:view_quality',
    'production:manage_quality',
    'production:view_maintenance',
    'production:manage_maintenance'
  ],

  // Department staff have view access to their department
  'finance_staff': [
    'system:view_dashboard',
    'finance:view_reports',
    'finance:view_transactions'
  ],

  'hr_staff': [
    'system:view_dashboard',
    'hr:view_employees',
    'hr:view_payroll',
    'hr:view_performance'
  ],

  'operations_staff': [
    'system:view_dashboard',
    'operations:view_inventory',
    'operations:view_logistics',
    'operations:view_suppliers'
  ],

  'production_staff': [
    'system:view_dashboard',
    'production:view_production',
    'production:view_quality',
    'production:view_maintenance'
  ],

  // Test role for permission testing
  'RoleTest': [
    'system:view_dashboard',
    'system:view_reports',
    'system:create_reports',
    'production:view_production',
    'production:manage_production'
  ],

  // Base user role has minimal access
  'user': [
    'system:view_dashboard'
  ]
};

/**
 * Get all permissions for a role including inherited permissions
 * @param {string} roleName - Name of the role
 * @returns {string[]} Array of permissions
 */
export function getAllRolePermissions(roleName) {
  const role = ROLE_HIERARCHY[roleName];
  if (!role) return [];

  // Get direct permissions
  const directPermissions = DEFAULT_ROLE_PERMISSIONS[roleName] || [];

  // Get inherited permissions
  const inheritedPermissions = role.inherits.flatMap(inheritedRole =>
    getAllRolePermissions(inheritedRole)
  );

  // Combine and remove duplicates
  return [...new Set([...directPermissions, ...inheritedPermissions])];
}

/**
 * Check if a role has a specific permission
 * @param {string} roleName - Name of the role
 * @param {string} permission - Permission to check
 * @returns {boolean} True if role has permission
 */
export function roleHasPermission(roleName, permission) {
  const allPermissions = getAllRolePermissions(roleName);

  // Check for exact permission match
  if (allPermissions.includes(permission)) return true;

  // Check for namespace wildcard (e.g., "finance:*")
  const namespace = permission.split(':')[0];
  if (allPermissions.includes(`${namespace}:*`)) return true;

  // Check for system admin permission
  if (allPermissions.includes('system:admin')) return true;

  return false;
}

/**
 * Get role level in the hierarchy
 * @param {string} roleName - Name of the role
 * @returns {number} Role level (higher = more access)
 */
export function getRoleLevel(roleName) {
  return ROLE_HIERARCHY[roleName]?.level || 0;
}

/**
 * Check if one role is higher than another in the hierarchy
 * @param {string} role1 - First role
 * @param {string} role2 - Second role
 * @returns {boolean} True if role1 is higher than role2
 */
export function isRoleHigher(role1, role2) {
  return getRoleLevel(role1) > getRoleLevel(role2);
}

export default {
  ROLE_HIERARCHY,
  PERMISSION_NAMESPACES,
  DEFAULT_ROLE_PERMISSIONS,
  getAllRolePermissions,
  roleHasPermission,
  getRoleLevel,
  isRoleHigher
};
