import React, { memo } from 'react';
import { Responsive<PERSON><PERSON>r, <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Area, AreaChart } from 'recharts';
import { Empty, Spin } from 'antd';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const MTTRTrendChart = memo(({ data = [], loading = false }) => {
  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }
  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée MTTR disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  // Process data to focus only on MTTR
  const processedData = safeData.map(item => {
    const mttrValue = parseFloat(item.mttr || item.avg_repair_time || 0);    
    return {
      date: item.date || item.Stop_Date || item.repair_date,
      mttr: Math.round(mttrValue * 100) / 100, // Round to 2 decimals
      stops: item.stops || item.stopCount || 0,
      totalRepairTime: item.totalRepairTime || 0
    };
  }).filter(item => item.date && !isNaN(item.mttr) && item.mttr > 0); // Filter out invalid entries

  // Calculate average MTTR for reference line
  const avgMTTR = processedData.reduce((sum, item) => sum + item.mttr, 0) / processedData.length;

  // Define color based on MTTR value - keep performance indicators but use SOMIPEM blues
  const getMTTRColor = (value) => {
    if (value <= 30) return SOMIPEM_COLORS.SECONDARY_BLUE; // Secondary Blue - Good
    if (value <= 60) return SOMIPEM_COLORS.PRIMARY_BLUE; // Primary Blue - OK
    if (value <= 120) return SOMIPEM_COLORS.CHART_TERTIARY; // Tertiary Blue - Warning
    return "#f5222d"; // Keep red for critical - status indicator
  };return (
    <ResponsiveContainer width="100%" height="100%">      <AreaChart data={processedData} margin={{ top: 20, right: 20, left: 10, bottom: 30 }}>
        <defs>
          <linearGradient id="mttrGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={SOMIPEM_COLORS.PRIMARY_BLUE} stopOpacity={0.4}/>
            <stop offset="95%" stopColor={SOMIPEM_COLORS.PRIMARY_BLUE} stopOpacity={0.1}/>
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" strokeWidth={1} />
        <XAxis
          dataKey="date"
          tick={{ fill: SOMIPEM_COLORS.LIGHT_GRAY, fontSize: 11 }} // Light Gray for X-axis labels
          height={30}
          angle={-45}
          textAnchor="end"
          tickFormatter={(date) => {
            try {
              return new Date(date).toLocaleDateString('fr-FR', { 
                day: '2-digit', 
                month: '2-digit' 
              });
            } catch {
              return date;
            }
          }}
        />        <YAxis
          tick={{ fill: SOMIPEM_COLORS.LIGHT_GRAY, fontSize: 11 }} // Light Gray for Y-axis labels
          width={40}
          tickFormatter={(value) => `${value}min`}
          tickCount={5}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "#FFFFFF", // White background
            border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            fontSize: "12px"
          }}          formatter={(value, name) => {
            const color = getMTTRColor(value);
            if (name === 'MTTR') {
              return [
                <span style={{ color }}>{`${value.toFixed(1)} min`}</span>, 
                'MTTR'
              ];
            }
            return [value, name];
          }}
          labelFormatter={(date) => {
            try {
              return new Date(date).toLocaleDateString('fr-FR', { 
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
            } catch {
              return date;
            }
          }}
        />        <Area
          type="monotone"
          dataKey="mttr"
          stroke={SOMIPEM_COLORS.PRIMARY_BLUE} // Primary Blue for line
          strokeWidth={2}
          fill="url(#mttrGradient)"
          dot={{ fill: SOMIPEM_COLORS.PRIMARY_BLUE, strokeWidth: 1, r: 3 }} // Primary Blue dots
          activeDot={{ r: 5, fill: "#FFFFFF", stroke: SOMIPEM_COLORS.PRIMARY_BLUE, strokeWidth: 2 }} // White center with Primary Blue border
        />
        {/* Reference line for average */}
        <Line
          type="monotone"
          dataKey={() => avgMTTR}
          stroke={SOMIPEM_COLORS.SECONDARY_BLUE} // Secondary Blue for reference line
          strokeWidth={1.5}
          strokeDasharray="4 4"
          dot={false}
          name={`Moyenne: ${avgMTTR.toFixed(1)} min`}
        />
        {/* Target line (30 minutes - good target) */}
        <Line
          type="monotone"
          dataKey={() => 30}
          stroke="#52c41a" // Keep green for target/success indicator
          strokeWidth={1.5}
          strokeDasharray="4 4"
          dot={false}
          name="Objectif: 30 min"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
});

MTTRTrendChart.displayName = 'MTTRTrendChart';

export default MTTRTrendChart;
