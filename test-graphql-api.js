import fetch from 'node-fetch';

/**
 * GraphQL API Test
 * Test the GraphQL endpoints to understand data availability and structure
 */

const GRAPHQL_ENDPOINT = 'http://localhost:5000/api/graphql';

class GraphQLTester {
  async query(query, variables = {}) {
    try {
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          variables
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        console.error('GraphQL Errors:', result.errors);
        return null;
      }

      return result.data;
    } catch (error) {
      console.error('Network error:', error.message);
      return null;
    }
  }

  async testDashboardData() {
    console.log('\n📊 === TESTING DASHBOARD DATA ===');
    
    // Test Production Chart
    const chartQuery = `
      query GetProductionChart($filters: EnhancedFilterInput) {
        enhancedGetProductionChart(filters: $filters) {
          data {
            Date_Insert_Day
            Total_Good_Qty_Day
            Total_Rejects_Qty_Day
            OEE_Day
            Availability_Rate_Day
            Performance_Rate_Day
            Quality_Rate_Day
          }
          dataSource
        }
      }
    `;

    const chartResult = await this.query(chartQuery, { filters: {} });

    // Test Sidecards
    const sidecardsQuery = `
      query GetProductionSidecards($filters: EnhancedFilterInput) {
        enhancedGetProductionSidecards(filters: $filters) {
          goodqty
          rejetqty
          dataSource
        }
      }
    `;

    const sidecardsResult = await this.query(sidecardsQuery, { filters: {} });

    // Test Machine Performance
    const machineQuery = `
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          data {
            Machine_Name
            Shift
            production
            rejects
            oee
            availability
            performance
            quality
            downtime
          }
          dataSource
        }
      }
    `;

    const machineResult = await this.query(machineQuery, { filters: {} });

    // Combine results
    const result = {
      getDashboardData: {
        productionChart: chartResult?.enhancedGetProductionChart,
        sidecards: sidecardsResult?.enhancedGetProductionSidecards,
        machinePerformance: machineResult?.enhancedGetMachinePerformance
      }
    };
    
    if (result?.getDashboardData) {
      const { productionChart, sidecards, machinePerformance } = result.getDashboardData;
      
      console.log(`📈 Production Chart: ${productionChart?.data?.length || 0} records (source: ${productionChart?.dataSource})`);
      if (productionChart?.data?.length > 0) {
        const sample = productionChart.data[0];
        console.log(`   Sample: ${sample.Date_Insert_Day} | TRS: ${sample.OEE_Day} | Good: ${sample.Total_Good_Qty_Day}`);
      }

      console.log(`📊 Sidecards: Good=${sidecards?.goodqty}, Rejects=${sidecards?.rejetqty} (source: ${sidecards?.dataSource})`);
      
      console.log(`🏭 Machine Performance: ${machinePerformance?.data?.length || 0} records (source: ${machinePerformance?.dataSource})`);
      if (machinePerformance?.data?.length > 0) {
        const sample = machinePerformance.data[0];
        console.log(`   Sample: ${sample.Machine_Name} (${sample.Shift}) | Production: ${sample.production} | TRS: ${sample.oee}%`);
      }

      return result.getDashboardData;
    }

    return null;
  }

  async testWithFilters() {
    console.log('\n🔍 === TESTING WITH FILTERS ===');
    
    // Test machine model filter
    console.log('🏭 Testing machine model filter (IPS):');
    const machineQuery = `
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          data {
            Machine_Name
            Shift
            oee
          }
          dataSource
        }
      }
    `;

    const ipsResult = await this.query(machineQuery, { 
      filters: { model: 'IPS' } 
    });
    
    if (ipsResult?.enhancedGetMachinePerformance) {
      const machines = ipsResult.enhancedGetMachinePerformance;
      console.log(`   Machine records: ${machines?.data?.length || 0}`);
      
      if (machines?.data?.length > 0) {
        const ipsOnly = machines.data.filter(m => m.Machine_Name?.includes('IPS'));
        console.log(`   IPS machines found: ${ipsOnly.length}`);
        ipsOnly.forEach(m => console.log(`     - ${m.Machine_Name} (${m.Shift})`));
      }
    }

    return ipsResult;
  }

  async testAllQueries() {
    console.log('🚀 GRAPHQL API TESTING STARTED\n');
    
    try {
      const dashboardData = await this.testDashboardData();
      const filterData = await this.testWithFilters();
      
      console.log('\n✅ GraphQL API testing completed');
      return { dashboardData, filterData };
    } catch (error) {
      console.error('❌ GraphQL testing failed:', error);
      return null;
    }
  }
}

// Run the tests
const tester = new GraphQLTester();
tester.testAllQueries()
  .then(results => {
    console.log('\n🏁 Testing finished');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Testing failed:', error);
    process.exit(1);
  });
