# Docker Compose for LOCQL Unified Container - ngrok Integration
# Simple architecture: LOCQL Application + ngrok tunnel for external access
#
# 🎯 ARCHITECTURE:
# Internet → ngrok tunnel → LOCQL Application (port 5000)
#
# ✅ BENEFITS:
# - Simple, lightweight architecture
# - Direct access to LOCQL application
# - No proxy layers or complex routing
# - Easy to configure and maintain
# - Automatic HTTPS with ngrok

services:

  # LOCQL Unified Container (from Docker Hub) - ngrok configured
  locql:
    image: mayahinasr/locql-unified:1.3.0
    container_name: locql-app
    ports:
      - "5000:5000"    # Direct access to LOCQL application
    environment:
      # Application Environment
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=5000
      - DOCKER_ENV=true

      # Database Configuration
      - DB_HOST=${DB_HOST:-host.docker.internal}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASS=${DB_PASS:-root}
      - DB_NAME=${DB_NAME:-Testingarea51}

      # Authentication
      - JWT_SECRET=${JWT_SECRET:-dadfba2957b57678f0bdf5bb2f27b1938b9a0b472cd95a49a7f01397af5db005b9072f3d5b0a42268571b735187e2a7aedc532ae79797206fe5789718711d719}
      - JWT_EXPIRE=${JWT_EXPIRE:-8h}
      - JWT_COOKIE_EXPIRE=${JWT_COOKIE_EXPIRE:-1}

      # ngrok Configuration
      - NGROK_ENABLED=${NGROK_ENABLED:-true}
      - VITE_NGROK_ENABLED=${VITE_NGROK_ENABLED:-true}

      # Frontend Configuration (ngrok)
      - FRONTEND_URL=${FRONTEND_URL:-https://eternal-friendly-chigger.ngrok-free.app}
      - VITE_API_URL=${VITE_API_URL:-https://eternal-friendly-chigger.ngrok-free.app}
      - VITE_WS_URL=${VITE_WS_URL:-wss://eternal-friendly-chigger.ngrok-free.app}

      # CORS Configuration (ngrok)
      - CORS_ORIGIN=${CORS_ORIGIN:-https://eternal-friendly-chigger.ngrok-free.app}
      - CORS_CREDENTIALS=${CORS_CREDENTIALS:-true}

      # WebSocket Configuration (ngrok)
      - WS_EXTERNAL_URL=${WS_EXTERNAL_URL:-wss://eternal-friendly-chigger.ngrok-free.app}
      - HTTP_EXTERNAL_URL=${HTTP_EXTERNAL_URL:-https://eternal-friendly-chigger.ngrok-free.app}

      # Session Configuration (ngrok domain)
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret-change-this-in-production}
      - SESSION_DOMAIN=${SESSION_DOMAIN:-eternal-friendly-chigger.ngrok-free.app}

      # Security Settings (Configured for ngrok HTTPS)
      - SECURE_COOKIES=${SECURE_COOKIES:-true}
      - SAME_SITE_COOKIES=${SAME_SITE_COOKIES:-lax}
      - TRUST_PROXY=${TRUST_PROXY:-true}

      # Logging and Debug
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - AUTH_DEBUG=${AUTH_DEBUG:-false}
      - DISABLE_AUTH_CACHE=${DISABLE_AUTH_CACHE:-false}

      # Cache Configuration
      - DISABLE_CACHE=${DISABLE_CACHE:-false}

      # Email Configuration (optional)
      - SMTP_HOST=${SMTP_HOST:-sandbox.smtp.mailtrap.io}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_EMAIL=${SMTP_EMAIL:-b43b394d59fa77}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-60513ef904592c}
      - FROM_NAME=${FROM_NAME:-LOCQL Dashboard}
      - FROM_EMAIL=${FROM_EMAIL:-<EMAIL>}

    restart: unless-stopped

    # Docker Desktop networking compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

    # Volume mounts for production
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/backend/uploads
      - ./reports:/app/backend/reports

    # Network configuration
    networks:
      - locql-network

    # Security options
    security_opt:
      - no-new-privileges:true

    # User configuration (non-root)
    user: "1001:1001"

  # ============================================================================
  # 🌐 TUNNEL SERVICE - External Internet Access
  # ============================================================================

  # ngrok with reserved domain and authentication
  ngrok:
    image: ngrok/ngrok:latest
    container_name: locql-ngrok-tunnel-local
    restart: unless-stopped
    command: ["http", "--url=eternal-friendly-chigger.ngrok-free.app", "locql:5000"]
    ports:
      - "4040:4040"  # ngrok web interface
    environment:
      - NGROK_AUTHTOKEN=${NGROK_AUTHTOKEN}
    depends_on:
      - locql
    networks:
      - locql-network
    profiles:
      - ngrok

# ============================================================================
# 🌐 NETWORKS
# ============================================================================

networks:
  locql-network:
    driver: bridge
    name: locql-network

# ============================================================================
# 📊 VOLUMES
# ============================================================================

volumes:
  locql-logs:
    driver: local
  locql-uploads:
    driver: local
  locql-reports:
    driver: local

# ============================================================================
# 🚀 USAGE INSTRUCTIONS
# ============================================================================
#
# 1. Start LOCQL application only:
#    docker-compose -f docker-compose.hub-ngrok.yml up -d locql
#
# 2. Start with ngrok tunnel:
#    docker-compose -f docker-compose.hub-ngrok.yml --profile ngrok up -d
#
# 3. View logs:
#    docker-compose -f docker-compose.hub-ngrok.yml logs -f
#
# 4. Stop services:
#    docker-compose -f docker-compose.hub-ngrok.yml down
#
# 5. Access URLs:
#    - Local: http://localhost:5000
#    - External: https://eternal-friendly-chigger.ngrok-free.app
#
# ============================================================================
