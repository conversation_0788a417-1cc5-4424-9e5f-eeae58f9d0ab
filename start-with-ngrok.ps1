# LOCQL Docker + ngrok Startup Script (PowerShell)
# This script ensures ngrok tunnel is active before starting Docker containers

Write-Host "🚀 LOCQL Docker + ngrok Startup (Windows)" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

# Configuration - Updated for ngrok tunnel
$NGROK_DOMAIN = "eternal-friendly-chigger.ngrok-free.app"
$NGROK_HTTP_URL = "https://$NGROK_DOMAIN"
$NGROK_API_URL = "https://$NGROK_DOMAIN"
$NGROK_WS_URL = "wss://$NGROK_DOMAIN"
$BACKEND_PORT = 5000
$FRONTEND_PORT = 5173

# Function to print colored status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
        return $false
    }
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check if ngrok tunnel is active
function Test-NgrokTunnel {
    Write-Info "Checking ngrok tunnel status..."
    
    try {
        $response = Invoke-WebRequest -Uri "$NGROK_HTTP_URL/api/health/ping" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            return Write-Status $true "ngrok HTTP tunnel is active: $NGROK_HTTP_URL"
        } else {
            return Write-Status $false "ngrok HTTP tunnel returned status: $($response.StatusCode)"
        }
    } catch {
        return Write-Status $false "ngrok HTTP tunnel is not accessible: $NGROK_HTTP_URL"
    }
}

# Check if Docker is running
function Test-Docker {
    Write-Info "Checking Docker status..."
    
    try {
        $dockerVersion = docker --version 2>$null
        $dockerPs = docker ps 2>$null
        if ($dockerVersion -and $dockerPs) {
            return Write-Status $true "Docker is running"
        } else {
            return Write-Status $false "Docker is not running or accessible"
        }
    } catch {
        return Write-Status $false "Docker is not running or accessible"
    }
}

# Check if MySQL is accessible
function Test-MySQL {
    Write-Info "Checking MySQL connectivity..."
    
    try {
        $mysqlPath = Get-Command mysql -ErrorAction SilentlyContinue
        if ($mysqlPath) {
            $mysqlTest = mysql -h localhost -u root -proot -e "SELECT 1;" 2>$null
            if ($LASTEXITCODE -eq 0) {
                return Write-Status $true "MySQL is accessible"
            } else {
                return Write-Status $false "MySQL connection failed"
            }
        } else {
            Write-Warning "MySQL client not found, skipping database check"
            return $true
        }
    } catch {
        Write-Warning "MySQL client not found, skipping database check"
        return $true
    }
}

# Check if ports are available
function Test-Ports {
    Write-Info "Checking port availability..."
    
    $portsOk = $true
    
    # Check backend port
    $port5000 = Get-NetTCPConnection -LocalPort $BACKEND_PORT -ErrorAction SilentlyContinue
    if (-not $port5000) {
        Write-Status $true "Port $BACKEND_PORT (backend) is available"
    } else {
        Write-Warning "Port $BACKEND_PORT is already in use"
        $portsOk = $false
    }
    
    # Check frontend port
    $port5173 = Get-NetTCPConnection -LocalPort $FRONTEND_PORT -ErrorAction SilentlyContinue
    if (-not $port5173) {
        Write-Status $true "Port $FRONTEND_PORT (frontend) is available"
    } else {
        Write-Warning "Port $FRONTEND_PORT is already in use"
        $portsOk = $false
    }
    
    return $portsOk
}

# Start Docker containers
function Start-Containers {
    Write-Info "Starting Docker containers..."
    
    # Stop any existing containers
    docker-compose -f docker-compose.app.yml down 2>$null | Out-Null
    
    # Start containers
    try {
        docker-compose -f docker-compose.app.yml up --build -d
        if ($LASTEXITCODE -eq 0) {
            return Write-Status $true "Docker containers started successfully"
        } else {
            return Write-Status $false "Failed to start Docker containers"
        }
    } catch {
        return Write-Status $false "Failed to start Docker containers"
    }
}

# Wait for services to be ready
function Wait-ForServices {
    Write-Info "Waiting for services to be ready..."
    
    $maxAttempts = 30
    $attempt = 1
    
    # Wait for backend
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$BACKEND_PORT/api/health/ping" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Status $true "Backend service is ready"
                break
            }
        } catch {
            # Service not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Status $false "Backend service failed to start within timeout"
        return $false
    }
    
    # Wait for frontend
    $attempt = 1
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$FRONTEND_PORT/" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Status $true "Frontend service is ready"
                break
            }
        } catch {
            # Service not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Status $false "Frontend service failed to start within timeout"
        return $false
    }
    
    return $true
}

# Main execution
function Main {
    Write-Host ""
    Write-Info "Starting pre-flight checks..."
    
    # Check Docker
    if (-not (Test-Docker)) {
        Write-Host ""
        Write-Warning "Please start Docker Desktop and try again"
        exit 1
    }
    
    # Check MySQL
    Test-MySQL | Out-Null
    
    # Check ports
    Test-Ports | Out-Null
    
    # Check ngrok tunnel
    if (-not (Test-NgrokTunnel)) {
        Write-Host ""
        Write-Warning "ngrok tunnel is not active. Please start ngrok tunnel:"
        Write-Info "ngrok http $BACKEND_PORT --domain=$NGROK_DOMAIN"
        Write-Host ""
        
        $continue = Read-Host "Press Enter after starting ngrok tunnel, or 'q' to exit"
        if ($continue -eq 'q') {
            exit 1
        }
        
        # Re-check tunnel
        if (-not (Test-NgrokTunnel)) {
            Write-Status $false "ngrok tunnel is still not accessible"
            exit 1
        }
    }
    
    Write-Host ""
    Write-Info "All checks passed! Starting containers..."
    
    # Start containers
    if (-not (Start-Containers)) {
        exit 1
    }
    
    # Wait for services
    if (-not (Wait-ForServices)) {
        Write-Warning "Some services may not be fully ready"
    }
    
    Write-Host ""
    Write-Status $true "LOCQL application is running!"
    Write-Host ""
    Write-Info "Access URLs:"
    Write-Host "  • Frontend (Local):   http://localhost:$FRONTEND_PORT" -ForegroundColor White
    Write-Host "  • Backend (Local):    http://localhost:$BACKEND_PORT" -ForegroundColor White
    Write-Host "  • Frontend (ngrok): $NGROK_HTTP_URL" -ForegroundColor White
    Write-Host "  • API (ngrok):     $NGROK_API_URL" -ForegroundColor White
    Write-Host "  • WebSocket (ngrok): $NGROK_WS_URL/api/machine-data-ws" -ForegroundColor White
    Write-Host ""
    Write-Info "To stop the application:"
    Write-Host "  docker-compose -f docker-compose.app.yml down" -ForegroundColor White
    Write-Host ""
    Write-Info "To view logs:"
    Write-Host "  docker-compose -f docker-compose.app.yml logs -f" -ForegroundColor White
}

# Run main function
Main
