import React, { useMemo } from "react"
import {
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ReferenceL<PERSON>,
  ReferenceArea,
} from "recharts"
import { Typography, Empty } from "antd"
import dayjs from "dayjs"

const { Text } = Typography

const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
}

const COLORS = [
  "#1890ff", // Primary
  "#13c2c2", // Cyan
  "#52c41a", // Success
  "#faad14", // Warning
  "#f5222d", // Danger
  "#722ed1", // Purple
  "#eb2f96", // Pink
  "#fa8c16", // Orange
  "#a0d911", // Lime
]

const DisponibiliteTrendChart = ({
  data = [],
  selectedMachine = "",
  selectedDate = null,
  dateRangeType = "day",
  targetValue = 85,
  loading = false,
}) => {
  // Format X-axis labels
  const formatXAxis = (dateStr) => {
    if (!dateStr) return ""
    const date = dayjs(dateStr)
    if (!date.isValid()) return dateStr

    switch (dateRangeType) {
      case "day":
        return date.format("HH:mm")
      case "week":
        return date.format("ddd DD")
      case "month":
        return date.format("DD/MM")
      default:
        return date.format("DD/MM/YYYY")
    }
  }

  // Compute reference area based on selected date
  const referenceArea = useMemo(() => {
    if (!selectedDate) return null
    let start, end

    switch (dateRangeType) {
      case "day":
        start = selectedDate.startOf("day").format("YYYY-MM-DD HH:mm")
        end = selectedDate.endOf("day").format("YYYY-MM-DD HH:mm")
        break
      case "week":
        start = selectedDate.startOf("isoWeek").format("YYYY-MM-DD")
        end = selectedDate.endOf("isoWeek").format("YYYY-MM-DD")
        break
      case "month":
        start = selectedDate.startOf("month").format("YYYY-MM-DD")
        end = selectedDate.endOf("month").format("YYYY-MM-DD")
        break
      default:
        return null
    }

    return { start, end }
  }, [selectedDate, dateRangeType])

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null

    return (
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      >
        <Text strong>{formatXAxis(label)}</Text>
        {payload.map((entry, index) => {
          let value = parseFloat(entry.value)
          // Check if value is between 0 and 1, and convert to percentage if needed
          if (!isNaN(value) && value > 0 && value < 1) {
            value = value * 100;
          }
          return (
            <div key={index} style={{ color: entry.color }}>
              <Text>
                {entry.name}: {isNaN(value) ? "N/A" : `${value.toFixed(1)}%`}
              </Text>
            </div>
          )
        })}
      </div>
    )
  }

  // Handle empty state
  if (!data || data.length === 0) {
    return <Empty description="Aucune donnée disponible pour la tendance de disponibilité" />
  }

  // Process data to convert disponibilite values from 0-1 to 0-100 if needed
  const processedData = React.useMemo(() => {
    return data.map(item => {
      // Create a new object to avoid mutating the original data
      const newItem = { ...item };

      // Process all keys that might contain disponibilite values
      Object.keys(newItem).forEach(key => {
        if (key === 'disponibilite' || key.startsWith('disponibilite_')) {
          if (newItem[key] !== undefined && newItem[key] !== null) {
            const value = Number(newItem[key]);
            if (!isNaN(value) && value > 0 && value < 1) {
              newItem[key] = value * 100;
            }
          }
        }
      });

      return newItem;
    });
  }, [data]);

  return (
    <ResponsiveContainer width="100%" height={350}>
      <LineChart data={processedData} margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="date"
          tickFormatter={formatXAxis}
          tick={{ fill: "#666" }}
          label={{
            value: "Période",
            position: "insideBottomRight",
            offset: -5,
            style: { fill: "#666" },
          }}
        />
        <YAxis
          domain={[0, 100]}
          tickFormatter={(value) => `${value}%`}
          label={{
            value: "Disponibilité (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
          tick={{ fill: "#666" }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />

        {/* Target line */}
        <ReferenceLine
          y={targetValue}
          stroke={CHART_COLORS.success}
          strokeDasharray="3 3"
          label={{
            value: `Objectif: ${targetValue}%`,
            position: "right",
            fill: CHART_COLORS.success,
            fontSize: 12,
          }}
        />

        {/* Highlight selected date range */}
        {referenceArea && (
          <ReferenceArea
            x1={referenceArea.start}
            x2={referenceArea.end}
            fill={CHART_COLORS.primary}
            fillOpacity={0.1}
          />
        )}

        {/* Render line(s) based on machine selection */}
        {selectedMachine ? (
          <Line
            type="monotone"
            dataKey="disponibilite"
            name={`Disponibilité ${selectedMachine}`}
            stroke={CHART_COLORS.primary}
            strokeWidth={3}
            dot={{ fill: CHART_COLORS.primary, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, fill: "#fff", stroke: CHART_COLORS.primary, strokeWidth: 2 }}
            isAnimationActive={!loading}
          />
        ) : (
          data[0] &&
          Object.keys(data[0])
            .filter((key) => key !== "date" && key.startsWith("disponibilite_"))
            .map((key, index) => {
              const machineName = key.replace("disponibilite_", "")
              return (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  name={`Disponibilité ${machineName}`}
                  stroke={COLORS[index % COLORS.length]}
                  strokeWidth={2}
                  dot={{ fill: COLORS[index % COLORS.length], strokeWidth: 2, r: 3 }}
                  activeDot={{ r: 5, fill: "#fff", stroke: COLORS[index % COLORS.length], strokeWidth: 2 }}
                  isAnimationActive={!loading}
                />
              )
            })
        )}
      </LineChart>
    </ResponsiveContainer>
  )
}

export default React.memo(DisponibiliteTrendChart)
