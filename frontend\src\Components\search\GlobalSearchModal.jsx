import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Input,
  List,
  Typography,
  Tag,
  Empty,
  Spin,
  Alert,
  Divider,
  Space,
  Button,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  FileTextOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  HighlightOutlined,
  ExportOutlined
} from '@ant-design/icons';
import searchService from '../../services/searchService';
import dayjs from 'dayjs';

const { Text, Title } = Typography;
const { Search } = Input;

const GlobalSearchModal = ({ visible, onClose, onResultSelect }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchStats, setSearchStats] = useState(null);

  // Debounced search function
  const debouncedSearch = useCallback(
    searchService.createDebouncedSearch(searchService.globalSearch.bind(searchService), 300),
    []
  );

  // Perform search when query changes
  useEffect(() => {
    if (searchQuery.trim().length >= 2) {
      performSearch(searchQuery.trim());
    } else {
      setSearchResults([]);
      setSearchStats(null);
      setError(null);
    }
  }, [searchQuery]);

  const performSearch = async (query) => {
    setLoading(true);
    setError(null);

    try {
      const results = await debouncedSearch(query, { size: 20 });
      const formattedResults = searchService.formatSearchResults(results, 'global');
      
      setSearchResults(formattedResults);
      setSearchStats({
        total: results.total,
        query: query,
        timestamp: new Date()
      });
    } catch (err) {
      setError(err.message || 'Search failed');
      setSearchResults([]);
      setSearchStats(null);
    } finally {
      setLoading(false);
    }
  };

  const handleResultClick = (result) => {
    if (onResultSelect) {
      onResultSelect(result);
    }
    onClose();
  };

  const getResultIcon = (type) => {
    switch (type) {
      case 'production-data':
        return <SettingOutlined style={{ color: '#52c41a' }} />;
      case 'machine-stop':
        return <ClockCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'machine-session':
        return <SettingOutlined style={{ color: '#1890ff' }} />;
      case 'report':
        return <FileTextOutlined style={{ color: '#722ed1' }} />;
      default:
        return <SearchOutlined style={{ color: '#666' }} />;
    }
  };

  const getResultTypeTag = (type) => {
    const typeConfig = {
      'production-data': { color: 'green', text: 'Production' },
      'machine-stop': { color: 'red', text: 'Arrêt' },
      'machine-session': { color: 'blue', text: 'Session' },
      'report': { color: 'purple', text: 'Rapport' },
      'maintenance-log': { color: 'orange', text: 'Maintenance' }
    };

    const config = typeConfig[type] || { color: 'default', text: 'Inconnu' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderHighlight = (highlight) => {
    if (!highlight) return null;

    const highlightFields = Object.keys(highlight);
    if (highlightFields.length === 0) return null;

    return (
      <div style={{ marginTop: 8 }}>
        {highlightFields.map(field => (
          <div key={field} style={{ marginBottom: 4 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <HighlightOutlined /> {field}:
            </Text>
            <div
              style={{ fontSize: '12px', marginLeft: 16 }}
              dangerouslySetInnerHTML={{
                __html: highlight[field].join(' ... ')
              }}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderSearchResult = (result) => (
    <List.Item
      key={result.id}
      onClick={() => handleResultClick(result)}
      style={{
        cursor: 'pointer',
        padding: '12px 16px',
        borderRadius: '6px',
        margin: '4px 0',
        transition: 'all 0.2s',
        border: '1px solid transparent'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = '#d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = 'transparent';
      }}
    >
      <List.Item.Meta
        avatar={getResultIcon(result.type)}
        title={
          <Space>
            <Text strong>{result.title}</Text>
            {getResultTypeTag(result.type)}
            {result.score && (
              <Tooltip title={`Relevance score: ${result.score.toFixed(2)}`}>
                <Tag color="purple" style={{ fontSize: '10px' }}>
                  {Math.round(result.score * 100)}%
                </Tag>
              </Tooltip>
            )}
          </Space>
        }
        description={
          <div>
            <Text type="secondary">{result.description}</Text>
            {result.timestamp && (
              <div style={{ marginTop: 4 }}>
                <ClockCircleOutlined style={{ marginRight: 4 }} />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {dayjs(result.timestamp).format('DD/MM/YYYY HH:mm')}
                </Text>
              </div>
            )}
            {renderHighlight(result.highlight)}
          </div>
        }
      />
      <div>
        <Button
          type="text"
          size="small"
          icon={<ExportOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            handleResultClick(result);
          }}
        />
      </div>
    </List.Item>
  );

  const handleModalClose = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSearchStats(null);
    setError(null);
    onClose();
  };

  return (
    <Modal
      title={
        <Space>
          <SearchOutlined />
          <span>Global Search</span>
        </Space>
      }
      open={visible}
      onCancel={handleModalClose}
      footer={null}
      width={800}
      style={{ top: 50 }}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="Rechercher dans les données de production, arrêts, sessions et rapports..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          size="large"
          allowClear
          autoFocus
        />
      </div>

      {searchStats && (
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Found {searchStats.total} results for "{searchStats.query}" 
            ({((Date.now() - searchStats.timestamp.getTime()) / 1000).toFixed(2)}s)
          </Text>
        </div>
      )}

      {error && (
        <Alert
          message="Search Error"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">Searching...</Text>
            </div>
          </div>
        ) : searchResults.length > 0 ? (
          <List
            dataSource={searchResults}
            renderItem={renderSearchResult}
            split={false}
          />
        ) : searchQuery.trim().length >= 2 ? (
          <Empty
            description="No results found"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <SearchOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">
                Type at least 2 characters to start searching
              </Text>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Search across machine sessions, reports, and maintenance logs
              </Text>
            </div>
          </div>
        )}
      </div>

      {searchResults.length > 0 && (
        <>
          <Divider />
          <div style={{ textAlign: 'center' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Click on any result to view details
            </Text>
          </div>
        </>
      )}
    </Modal>
  );
};

export default GlobalSearchModal;
