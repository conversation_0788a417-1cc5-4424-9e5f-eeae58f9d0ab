import{r as c,a2 as P,R as e,g as d,M as A,al as p,m as M,d as N,n as l,a as x}from"./index-O2xm1U_Z.js";import{d as q}from"./dayjs.min-CgAD4wBe.js";import{R as C}from"./FilePdfOutlined-Cew2Jbhk.js";import{D as O,R as Y}from"./DownloadOutlined-ClmkhSDC.js";var T={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};function h(){return h=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},h.apply(this,arguments)}const z=(t,r)=>c.createElement(P,h({},t,{ref:r,icon:T})),j=c.forwardRef(z),$=({machineId:t,machineName:r,shift:i})=>{const[n,y]=c.useState(!1),[m,D]=c.useState(i||"Current"),[g,v]=c.useState(q()),[E,f]=c.useState(!1),[u,R]=c.useState(null),S=()=>{y(!0)},_=()=>{y(!1)},b=async()=>{var o;try{f(!0),console.log("Starting report generation with params:",{machineId:t,date:g.format("YYYY-MM-DD"),shift:m});const a=setTimeout(()=>{E&&(console.log("Safety timeout triggered after 90 seconds"),f(!1),l.error("La génération du rapport a pris trop de temps. Veuillez réessayer."))},9e4);console.log("Sending API request to /api/shift-reports/generate");const s=await x.post("/api/shift-reports/generate").withCredentials().send({machineId:t,date:g.format("YYYY-MM-DD"),shift:m}).timeout(12e4).retry(2);console.log("Received response:",s.status,s.statusText),clearTimeout(a),s.body&&s.body.success?(R(s.body),l.success("Rapport généré avec succès"),s.body.filePath&&(console.log("Auto-opening PDF in new tab:",s.data.filePath),window.open(s.data.filePath,"_blank"))):l.error("Erreur lors de la génération du rapport")}catch(a){console.error("Error generating report:",a),console.log("Error details:",{code:a.code,message:a.message,response:a.response?{status:a.response.status,statusText:a.response.statusText,data:a.response.data}:"No response",request:a.request?"Request exists":"No request"}),a.code==="ECONNABORTED"?l.error("La génération du rapport a pris trop de temps. Veuillez réessayer. Le serveur n'a pas répondu dans le délai imparti."):a.response?l.error(`Erreur ${a.response.status}: ${((o=a.response.data)==null?void 0:o.error)||a.response.statusText||"Erreur inconnue"}`):a.request?l.error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):l.error(`Erreur: ${a.message}`)}finally{console.log("Request completed (success or error), resetting loading state"),f(!1)}},w=()=>{if(!u||!u.filePath){l.error("Aucun rapport disponible pour téléchargement");return}const o=u.downloadPath||u.filePath;console.log("Downloading report from:",o),window.open(o,"_blank")};return e.createElement(e.Fragment,null,e.createElement(d,{type:"primary",icon:e.createElement(C,null),onClick:S,style:{marginLeft:8}},"Rapport de Shift"),e.createElement(A,{title:"Générer un Rapport de Quart",open:n,onCancel:_,footer:null},e.createElement("p",null,"Générer un rapport de performance pour la machine"," ",e.createElement("strong",null,r)," basé sur les données du quart sélectionné. Le rapport combine les données de la dernière ligne de la table machine_daily_table_mould et les données agrégées de la table machine_sessions pour la période de 8 heures du quart."),e.createElement("div",{style:{marginBottom:16}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},e.createElement("label",{style:{marginRight:8,width:60}},"Date:"),e.createElement(O,{value:g,onChange:o=>v(o),style:{width:200},format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!1})),e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement("label",{style:{marginRight:8,width:60}},"Quart:"),e.createElement(p,{value:m,onChange:o=>D(o),style:{width:200}},e.createElement(p.Option,{value:"Matin"},"Shift 1 (06:00 - 14:00)"),e.createElement(p.Option,{value:"Après-midi"},"Shift 2 (14:00 - 22:00)"),e.createElement(p.Option,{value:"Nuit"},"Shift 3 (22:00 - 06:00)"),e.createElement(p.Option,{value:"Current"},"Shift Actuel")))),E?e.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},e.createElement(M,{size:"large"}),e.createElement("p",{style:{marginTop:16}},"Génération du rapport en cours...")):u?e.createElement("div",{style:{textAlign:"center",padding:"20px 0"}},e.createElement("p",{style:{color:"green",fontSize:16}},"Rapport généré avec succès! Le PDF a été ouvert dans un nouvel onglet."),e.createElement("p",{style:{fontSize:14,marginBottom:16}},"Si le PDF ne s'est pas ouvert automatiquement, ou pour le télécharger, cliquez ci-dessous:"),e.createElement(d,{type:"primary",icon:e.createElement(Y,null),onClick:w},"Ouvrir/Télécharger le PDF")):e.createElement("div",{style:{textAlign:"right"}},e.createElement(N,null,e.createElement(d,{onClick:_},"Annuler"),e.createElement(d,{type:"primary",onClick:b},"Générer le Rapport")))))},k=t=>{const r=parseFloat(t);return isNaN(r)?0:r<=1&&r>0?r*100:r},G=t=>{const r=i=>{const n=parseFloat(i);return isNaN(n)?0:n<=1&&n>0?n*100:n};return{date:t.Date_Insert_Day,Machine_Name:t.Machine_Name||"N/A",Shift:t.Shift||"N/A",good:parseFloat(t.Good_QTY_Day)||0,reject:parseFloat(t.Rejects_QTY_Day)||0,oee:r(t.OEE_Day),speed:parseFloat(t.Speed_Day)||0,run_hours:parseFloat(t.Run_Hours_Day)||0,down_hours:parseFloat(t.Down_Hours_Day)||0,availability:r(t.Availability_Rate_Day),performance:r(t.Performance_Rate_Day),quality:r(t.Quality_Rate_Day),mould_number:t.Part_Number||"N/A",poid_unitaire:t.Poid_Unitaire||"N/A",cycle_theorique:t.Cycle_Theorique||"N/A",poid_purge:t.Poid_Purge||"N/A"}};export{j as R,$ as S,k as n,G as t};
