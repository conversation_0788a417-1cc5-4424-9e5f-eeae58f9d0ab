/**
 * Redis-Enhanced GraphQL Resolvers for Manufacturing Intelligence Platform
 * High-performance caching layer for dailyTableResolvers.js and stopTableResolvers.js
 * 
 * Performance Targets:
 * - 80% database query load reduction
 * - Sub-100ms cache hit response times
 * - Smart cache invalidation strategies
 */

import redisService from './RedisService.js';
import { executeQuery } from '../utils/dbUtils.js';

class RedisEnhancedResolvers {
  constructor() {
    this.initialized = false;
    this.performanceMetrics = {
      cacheHits: 0,
      cacheMisses: 0,
      avgCacheResponseTime: 0,
      avgDbResponseTime: 0,
      totalQueries: 0
    };
  }

  /**
   * Initialize Redis service
   */
  async initialize() {
    if (this.initialized) return true;
    
    try {
      await redisService.initialize();
      this.initialized = true;
      console.log('✅ Redis Enhanced Resolvers initialized');
      return true;
    } catch (error) {
      console.error('❌ Redis Enhanced Resolvers initialization failed:', error);
      return false;
    }
  }

  /**
   * Enhanced resolver wrapper with Redis caching
   */
  createCachedResolver(resolverName, originalResolver, options = {}) {
    const {
      ttl = redisService.TTL.DASHBOARD_DATA,
      keyPrefix = 'resolver',
      skipCache = false,
      invalidatePatterns = []
    } = options;

    return {
      ...originalResolver,
      resolve: async (parent, args, context, info) => {
        const startTime = Date.now();
        
        try {
          // Initialize Redis if needed
          if (!this.initialized) {
            await this.initialize();
          }

          // Skip cache if disabled or in development mode
          if (skipCache || process.env.DISABLE_CACHE === 'true') {
            console.log(`🔄 Cache SKIP for ${resolverName} (disabled)`);
            return await originalResolver.resolve(parent, args, context, info);
          }

          // Try to get from cache first
          const cached = await redisService.getCachedResolverResult(resolverName, args);
          
          if (cached) {
            const responseTime = Date.now() - startTime;
            this.updateMetrics('hit', responseTime);
            
            console.log(`📦 Cache HIT for ${resolverName} (${responseTime}ms)`);
            return cached;
          }

          // Cache miss - execute original resolver
          console.log(`🔄 Cache MISS for ${resolverName} - executing query`);
          const dbStartTime = Date.now();
          
          const result = await originalResolver.resolve(parent, args, context, info);
          
          const dbResponseTime = Date.now() - dbStartTime;
          const totalResponseTime = Date.now() - startTime;
          
          // Cache the result
          await redisService.cacheResolverResult(resolverName, args, result, ttl);
          
          this.updateMetrics('miss', totalResponseTime, dbResponseTime);
          
          console.log(`💾 Cached ${resolverName} result (DB: ${dbResponseTime}ms, Total: ${totalResponseTime}ms)`);
          return result;

        } catch (error) {
          console.error(`❌ Error in cached resolver ${resolverName}:`, error);
          
          // Fallback to original resolver on cache errors
          try {
            return await originalResolver.resolve(parent, args, context, info);
          } catch (fallbackError) {
            console.error(`❌ Fallback resolver also failed for ${resolverName}:`, fallbackError);
            throw fallbackError;
          }
        }
      }
    };
  }

  /**
   * Enhanced dashboard data resolver with component-level caching
   */
  createEnhancedDashboardResolver(originalResolver) {
    return {
      ...originalResolver,
      resolve: async (parent, args, context, info) => {
        const startTime = Date.now();
        const { filters = {} } = args;
        
        try {
          if (!this.initialized) {
            await this.initialize();
          }

          // Try to get complete dashboard data from cache
          const cachedDashboard = await redisService.getCachedDashboardData(filters);
          
          if (cachedDashboard) {
            const responseTime = Date.now() - startTime;
            console.log(`📦 Dashboard Cache HIT (${responseTime}ms)`);
            return cachedDashboard;
          }

          console.log('🔄 Dashboard Cache MISS - fetching components');
          
          // Try to get individual components from cache
          const [
            cachedProductionChart,
            cachedSidecards,
            cachedMachinePerformance,
            cachedAvailabilityTrend
          ] = await Promise.all([
            redisService.getCache(redisService.generateCacheKey('production', 'chart', filters)),
            redisService.getCache(redisService.generateCacheKey('dashboard', 'sidecards', filters)),
            redisService.getCache(redisService.generateCacheKey('machines', 'performance', filters)),
            redisService.getCache(redisService.generateCacheKey('dashboard', 'availability', filters))
          ]);

          // Count cache hits vs misses for components
          const componentCacheHits = [
            cachedProductionChart,
            cachedSidecards,
            cachedMachinePerformance,
            cachedAvailabilityTrend
          ].filter(Boolean).length;

          console.log(`📊 Component cache hits: ${componentCacheHits}/4`);

          // If we have all components cached, use them
          if (componentCacheHits === 4) {
            const dashboardData = {
              productionChart: cachedProductionChart,
              sidecards: cachedSidecards,
              machinePerformance: cachedMachinePerformance,
              availabilityTrend: cachedAvailabilityTrend
            };

            // Cache the complete dashboard
            await redisService.cacheDashboardData(filters, dashboardData);
            
            const responseTime = Date.now() - startTime;
            console.log(`📦 Dashboard assembled from components (${responseTime}ms)`);
            return dashboardData;
          }

          // Fallback to original resolver for missing components
          const dbStartTime = Date.now();
          const result = await originalResolver.resolve(parent, args, context, info);
          const dbResponseTime = Date.now() - dbStartTime;

          // Cache the complete result and components
          await redisService.cacheDashboardData(filters, result);
          
          const totalResponseTime = Date.now() - startTime;
          console.log(`💾 Dashboard cached (DB: ${dbResponseTime}ms, Total: ${totalResponseTime}ms)`);
          
          return result;

        } catch (error) {
          console.error('❌ Error in enhanced dashboard resolver:', error);
          
          // Fallback to original resolver
          try {
            return await originalResolver.resolve(parent, args, context, info);
          } catch (fallbackError) {
            console.error('❌ Dashboard fallback resolver failed:', fallbackError);
            throw fallbackError;
          }
        }
      }
    };
  }

  /**
   * Create optimized resolver for heavy aggregation queries
   */
  createOptimizedAggregationResolver(resolverName, originalResolver, options = {}) {
    const {
      ttl = redisService.TTL.PRODUCTION_CHART,
      precomputeKey = null,
      aggregationLevel = 'high'
    } = options;

    return {
      ...originalResolver,
      resolve: async (parent, args, context, info) => {
        const startTime = Date.now();
        
        try {
          if (!this.initialized) {
            await this.initialize();
          }

          // For high-level aggregations, use longer TTL
          const cacheTTL = aggregationLevel === 'high' ? ttl * 2 : ttl;
          
          // Try cache first
          const cached = await redisService.getCachedResolverResult(resolverName, args);
          
          if (cached) {
            const responseTime = Date.now() - startTime;
            console.log(`📦 Aggregation Cache HIT for ${resolverName} (${responseTime}ms)`);
            return cached;
          }

          console.log(`🔄 Aggregation Cache MISS for ${resolverName} - executing heavy query`);
          
          // Execute with performance monitoring
          const dbStartTime = Date.now();
          const result = await originalResolver.resolve(parent, args, context, info);
          const dbResponseTime = Date.now() - dbStartTime;

          // Cache with extended TTL for heavy aggregations
          await redisService.cacheResolverResult(resolverName, args, result, cacheTTL);
          
          // Log performance for monitoring
          if (dbResponseTime > 1000) {
            console.warn(`⚠️ Slow aggregation query ${resolverName}: ${dbResponseTime}ms`);
          }

          const totalResponseTime = Date.now() - startTime;
          console.log(`💾 Aggregation cached ${resolverName} (DB: ${dbResponseTime}ms, Total: ${totalResponseTime}ms)`);
          
          return result;

        } catch (error) {
          console.error(`❌ Error in optimized aggregation resolver ${resolverName}:`, error);
          throw error;
        }
      }
    };
  }

  /**
   * Update performance metrics
   */
  updateMetrics(type, responseTime, dbResponseTime = 0) {
    this.performanceMetrics.totalQueries++;
    
    if (type === 'hit') {
      this.performanceMetrics.cacheHits++;
      this.performanceMetrics.avgCacheResponseTime = 
        (this.performanceMetrics.avgCacheResponseTime + responseTime) / 2;
    } else {
      this.performanceMetrics.cacheMisses++;
      this.performanceMetrics.avgDbResponseTime = 
        (this.performanceMetrics.avgDbResponseTime + dbResponseTime) / 2;
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const hitRate = this.performanceMetrics.totalQueries > 0 
      ? ((this.performanceMetrics.cacheHits / this.performanceMetrics.totalQueries) * 100).toFixed(2)
      : 0;

    return {
      ...this.performanceMetrics,
      hitRate: parseFloat(hitRate),
      cacheEfficiency: this.performanceMetrics.avgCacheResponseTime > 0 
        ? (this.performanceMetrics.avgDbResponseTime / this.performanceMetrics.avgCacheResponseTime).toFixed(2)
        : 0
    };
  }

  /**
   * Invalidate cache for specific patterns
   */
  async invalidateCache(patterns) {
    if (!Array.isArray(patterns)) {
      patterns = [patterns];
    }

    let totalInvalidated = 0;
    for (const pattern of patterns) {
      const count = await redisService.invalidateCache(pattern);
      totalInvalidated += count;
    }

    console.log(`🗑️ Invalidated ${totalInvalidated} cache entries`);
    return totalInvalidated;
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmUpCache(resolvers, commonFilters = []) {
    console.log('🔥 Starting cache warm-up...');
    
    const warmUpPromises = [];
    
    for (const [resolverName, resolver] of Object.entries(resolvers)) {
      for (const filters of commonFilters) {
        warmUpPromises.push(
          resolver.resolve(null, { filters }, null, null)
            .then(() => console.log(`✅ Warmed up ${resolverName} with filters:`, filters))
            .catch(error => console.error(`❌ Failed to warm up ${resolverName}:`, error))
        );
      }
    }

    await Promise.allSettled(warmUpPromises);
    console.log('🔥 Cache warm-up completed');
  }
}

// Create singleton instance
const redisEnhancedResolvers = new RedisEnhancedResolvers();

export default redisEnhancedResolvers;
