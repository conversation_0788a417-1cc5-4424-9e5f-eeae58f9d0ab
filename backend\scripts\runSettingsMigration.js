import { executeQuery } from '../utils/dbUtils.js';
import fs from 'fs';
import path from 'path';

/**
 * Run the settings migration using Node.js
 */
async function runSettingsMigration() {
  try {
    console.log('🚀 Running settings migration...');
    
    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'backend/migrations/003_new_settings_system.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip delimiter statements and trigger definitions for now
      if (statement.includes('DELIMITER') || statement.includes('CREATE TRIGGER')) {
        console.log(`⏭️  Skipping statement ${i + 1}: ${statement.substring(0, 50)}...`);
        continue;
      }
      
      console.log(`🔄 Executing statement ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
      
      try {
        const result = await executeQuery(statement);
        if (result.success) {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        } else {
          console.error(`❌ Statement ${i + 1} failed:`, result.error);
        }
      } catch (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error.message);
        // Continue with other statements
      }
    }
    
    // Verify the table was created
    console.log('🔍 Verifying table creation...');
    const verifyResult = await executeQuery('SHOW TABLES LIKE "user_settings"');
    
    if (verifyResult.success && verifyResult.data.length > 0) {
      console.log('✅ user_settings table created successfully');
      
      // Check if data was inserted
      const countResult = await executeQuery('SELECT COUNT(*) as count FROM user_settings');
      if (countResult.success) {
        console.log(`✅ Settings records created: ${countResult.data[0].count}`);
      }
    } else {
      console.error('❌ user_settings table was not created');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the migration
runSettingsMigration();
