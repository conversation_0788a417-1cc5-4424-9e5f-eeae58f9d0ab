/**
 * Utility functions for date handling and query building
 */

/**
 * Builds date filter conditions for SQL queries based on date range type
 * @param {string} dateParam - The date parameter in YYYY-MM-DD format
 * @param {string} dateRangeType - The type of date range (day, week, month)
 * @param {string} dateField - The database field containing the date (default: 'Date_Insert_Day')
 * @param {string} dateFormat - The format of the date in the database (default: '%d/%m/%Y')
 * @returns {Object} Object containing the SQL condition and parameters
 */
export const buildDateFilter = (dateParam, dateRangeType = 'day', dateField = 'Date_Insert_Day', dateFormat = '%d/%m/%Y') => {
  if (!dateParam) {
    return { condition: '', params: [] };
  }

  let condition = '';
  const params = [];

  switch (dateRangeType) {
    case 'day':
      condition = ` AND STR_TO_DATE(${dateField}, '${dateFormat}') = STR_TO_DATE(?, '%Y-%m-%d')`;
      params.push(dateParam);
      break;
    case 'week':
      // Use a simpler approach for week filtering
      // Get the first day of the week (Monday) and last day (Sunday)
      const weekDate = new Date(dateParam);
      const dayOfWeek = weekDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

      // Calculate the Monday of the week (first day)
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // If Sunday, go back 6 days, otherwise go back to Monday
      const mondayDate = new Date(weekDate);
      mondayDate.setDate(weekDate.getDate() + mondayOffset);

      // Calculate the Sunday of the week (last day)
      const sundayDate = new Date(mondayDate);
      sundayDate.setDate(mondayDate.getDate() + 6);

      // Format dates to YYYY-MM-DD
      const mondayStr = mondayDate.toISOString().split('T')[0];
      const sundayStr = sundayDate.toISOString().split('T')[0];

      // Use a date range for more precise filtering
      condition = ` AND STR_TO_DATE(${dateField}, '${dateFormat}') >= STR_TO_DATE(?, '%Y-%m-%d')
                  AND STR_TO_DATE(${dateField}, '${dateFormat}') <= STR_TO_DATE(?, '%Y-%m-%d')`;
      params.push(mondayStr, sundayStr);
      break;
    case 'month':
      // For month view, get the first and last day of the month
      const date = new Date(dateParam);
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      // Format dates to YYYY-MM-DD
      const firstDayStr = firstDay.toISOString().split('T')[0];
      const lastDayStr = lastDay.toISOString().split('T')[0];

      // Use a date range for more precise filtering
      condition = ` AND STR_TO_DATE(${dateField}, '${dateFormat}') >= STR_TO_DATE(?, '%Y-%m-%d')
                  AND STR_TO_DATE(${dateField}, '${dateFormat}') <= STR_TO_DATE(?, '%Y-%m-%d')`;
      params.push(firstDayStr, lastDayStr);
      break;
    default:
      // No filter for unknown date range types
      break;
  }

  return { condition, params };
};

/**
 * Builds machine filter conditions for SQL queries
 * @param {string} machineModel - The machine model to filter by
 * @param {string} machineName - The specific machine name to filter by
 * @param {string} machineField - The database field containing the machine name (default: 'Machine_Name')
 * @returns {Object} Object containing the SQL condition and parameters
 */
export const buildMachineFilter = (machineModel, machineName, machineField = 'Machine_Name') => {
  let condition = '';
  const params = [];

  if (machineModel) {
    condition = ` AND ${machineField} LIKE ?`;
    params.push(`${machineModel}%`);
  } else if (machineName) {
    condition = ` AND ${machineField} = ?`;
    params.push(machineName);
  }

  return { condition, params };
};

/**
 * Formats a date object to DD/MM/YYYY format
 * @param {Date} date - The date to format
 * @returns {string} Formatted date string
 */
export const formatDateDDMMYYYY = (date) => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

/**
 * Formats a date object to YYYY-MM-DD format
 * @param {Date} date - The date to format
 * @returns {string} Formatted date string
 */
export const formatDateYYYYMMDD = (date) => {
  return date.toISOString().split('T')[0];
};

/**
 * Gets a date from N days ago
 * @param {number} days - Number of days to go back
 * @returns {Date} Date object from N days ago
 */
export const getDateDaysAgo = (days) => {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date;
};
