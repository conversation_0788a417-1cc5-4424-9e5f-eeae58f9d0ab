import React from 'react';
import { Card, Space } from 'antd';
import { BulbOutlined } from '@ant-design/icons';

const ContinuousImprovementSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <BulbOutlined style={{ fontSize: '64px', color: '#a0d911' }} />
          <h2 style={{ color: '#a0d911', margin: 0 }}>Continuous Improvement</h2>
          <p style={{ color: '#8c8c8c' }}>Self-learning systems and continuous optimization coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default ContinuousImprovementSection;
