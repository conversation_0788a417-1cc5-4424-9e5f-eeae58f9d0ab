import React, { memo } from "react";
import {
  Respons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Legend,
  Bar,
  Line
} from "recharts";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// SOMIPEM Brand Color Palette for Charts (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Bar chart component for production data with OEE line
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @returns {JSX.Element} - Rendered chart component
 */
const ProductionBarChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={400}>
    <ComposedChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
      <XAxis
        dataKey="date"
        tick={{ fill: "#666" }}
        tickFormatter={(date) => dayjs(date).format("DD/MM")}
        label={{
          value: "Date",
          position: "bottom",
          offset: 0,
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="left"
        tickFormatter={(value) => value.toLocaleString()}
        label={{
          value: "Quantité",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        tickFormatter={(value) => `${value}%`}
        domain={[0, 100]}
        label={{
          value: "TRS",
          angle: 90,
          position: "insideRight",
          style: { fill: "#666" },
        }}
      />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)
          const formattedValue = isValidNumber
            ? Number.isInteger(value)
              ? value.toLocaleString()
              : value.toFixed(2)
            : value

          const labels = {
            good: "Quantité bonne",
            reject: "Quantité rejetée (kg)",
            oee: "TRS",
            speed: "Cycle De Temps",
          }

          return [formattedValue, labels[name] || name]
        }}
        labelFormatter={(label) => `Date: ${dayjs(label).format("DD/MM/YYYY")}`}
      />
      <Legend
        wrapperStyle={{ paddingTop: 20 }}
        formatter={(value) => {
          const labels = {
            good: "Quantité bonne",
            reject: "Quantité rejetée (kg)",
            oee: "TRS",
            speed: "Cycle De Temps",
          }
          return <span style={{ color: "#666" }}>{labels[value] || value}</span>
        }}
      />
      <Bar yAxisId="left" dataKey="good" name="good" fill={COLORS[2]} maxBarSize={40} stackId="production" />
      <Bar yAxisId="left" dataKey="reject" name="reject" fill={COLORS[4]} maxBarSize={40} stackId="production" />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="oee"
        name="oee"
        stroke={COLORS[0]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[0] }}
        activeDot={{ r: 6, fill: "#fff", stroke: COLORS[0], strokeWidth: 2 }}
      />
    </ComposedChart>
  </ResponsiveContainer>
));

ProductionBarChart.displayName = 'ProductionBarChart';

export default ProductionBarChart;
