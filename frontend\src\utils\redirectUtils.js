/**
 * Utility functions for handling redirections based on user permissions
 */
import { routePermissions } from '../config/permissionConfig';

/**
 * Priority order for redirection after login
 * This defines the order of importance for pages a user might be redirected to
 */
const REDIRECT_PRIORITY = [
  '/home',           // Dashboard (highest priority)
  '/production',     // Production page
  '/arrets',         // Stops page
  '/reports',        // Reports page
  '/analytics',      // Analytics page
  '/notifications',  // Notifications page
  '/maintenance',    // Maintenance page

  '/admin',          // Admin page
  '/profile'         // Profile page (lowest priority, fallback)
];

/**
 * Determines the best page to redirect a user to based on their permissions
 * 
 * @param {Object} user - The user object containing permissions, roles, etc.
 * @param {Function} hasPermission - Function to check if user has a specific permission
 * @param {Function} hasRole - Function to check if user has a specific role
 * @returns {string} The path to redirect to
 */
export const getRedirectPathByPermissions = (user, hasPermission, hasRole) => {
  // Default fallback is profile page (all authenticated users can access)
  let redirectPath = '/profile';
  
  // If no user or permission checking functions, return default
  if (!user || !hasPermission || !hasRole) {
    return redirectPath;
  }
  
  // Check each path in priority order
  for (const path of REDIRECT_PRIORITY) {
    const config = routePermissions[path];
    
    // Skip if no config exists for this path
    if (!config) continue;
    
    // Check if user has required permissions and roles for this path
    const hasRequiredPermissions = !config.permissions || hasPermission(config.permissions);
    const hasRequiredRoles = !config.roles || hasRole(config.roles);
    
    // If user has access to this path, use it for redirection
    if (hasRequiredPermissions && hasRequiredRoles) {
      redirectPath = path;
      break; // Stop at the highest priority path the user can access
    }
  }
  
  return redirectPath;
};

export default {
  getRedirectPathByPermissions
};
