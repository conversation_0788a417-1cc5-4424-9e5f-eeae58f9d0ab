/**
 * Real-time Production Dashboard Component
 * Enhanced version of Production.jsx with WebSocket-based real-time updates
 * 
 * Features:
 * - Real-time data synchronization via WebSocket
 * - Cross-device state consistency
 * - Optimistic updates with conflict resolution
 * - Automatic reconnection handling
 * - Backward compatibility with existing REST APIs
 */

import React, { useState, useEffect, useCallback, memo } from 'react';
import superagent from 'superagent';
import { 
  Layout, 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Table, 
  DatePicker,
  Tag, 
  Spin, 
  Typography,
  Progress,
  Grid,
  Empty,
  Alert,
  Badge,
  Tooltip
} from 'antd';
import { 
  RiseOutlined, 
  FallOutlined,
  DashboardOutlined,
  PieChartOutlined,
  LineChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  AreaChartOutlined,
  ClockCircleOutlined,
  UserOutlined,
  WifiOutlined,
  DisconnectOutlined,
  SyncOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { 
  ResponsiveContainer,
  Bar<PERSON>hart, 
  Bar, 
  <PERSON>Axis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  Legend, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { format } from 'd3-format';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';

// Real-time state management
import { useRealtimeStateContext } from '../contexts/RealtimeStateContext';
import { formatFrenchNumber, formatFrenchInteger, formatFrenchPercentage } from '../utils/numberFormatter';

const { Title, Text } = Typography;
const { useBreakpoint } = Grid;

const ProductionRealtime = memo(() => {
  // Real-time state management
  const {
    productionData,
    connectionStatus,
    isConnected,
    conflicts,
    updateProductionData,
    resolveConflict,
    reconnect,
    SYNC_STRATEGIES
  } = useRealtimeStateContext();

  // Local component state
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDateData, setSelectedDateData] = useState([]);
  const [lastFetchTime, setLastFetchTime] = useState(null);
  const [fallbackMode, setFallbackMode] = useState(false);

  // Responsive breakpoints
  const screens = useBreakpoint();

  // Base URL configuration
  const baseURL = (() => {
    if (typeof window !== 'undefined') {
      return `${window.location.protocol}//${window.location.host}`;
    }
    return 'http://localhost:5000';
  })();

  /**
   * Fetch initial data and fallback data when WebSocket is unavailable
   */
  const fetchData = useCallback(async (forceFallback = false) => {
    setLoading(true);
    
    try {
      const [
        chartRes, 
        datesRes, 
        sidecardsRes,
        sidecardsRejetRes,
        machinePerformanceRes,
        hourlyTrendsRes,
        operatorsRes,
        oeeTrendsRes,
        speedTrendsRes,
        shiftRes
      ] = await Promise.all([
        superagent.get(baseURL + '/api/testing-chart-production').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/unique-dates-production').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/sidecards-prod').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/sidecards-prod-rejet').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/machine-performance').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/hourly-trends').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/operator-stats').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/machine-oee-trends').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/speed-trends').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/shift-comparison').withCredentials().timeout(30000).retry(2),
      ]);

      // Process OEE data
      const oeeMap = oeeTrendsRes.data.reduce((acc, item) => {
        acc[item.date] = item.oee;
        return acc;
      }, {});

      // Combine chart data with OEE
      const enhancedChartData = chartRes.data.map(item => ({
        ...item,
        oee: oeeMap[item.date] || 0
      }));

      // Prepare production data object
      const newProductionData = {
        chartData: enhancedChartData,
        uniqueDates: datesRes.data,
        sidecards: sidecardsRes.data,
        sidecardsRejet: sidecardsRejetRes.data,
        machinePerformance: machinePerformanceRes.data,
        hourlyTrends: hourlyTrendsRes.data,
        operatorStats: operatorsRes.data,
        oeeTrends: oeeTrendsRes.data,
        speedTrends: speedTrendsRes.data,
        shiftComparison: shiftRes.data,
        lastUpdated: Date.now()
      };

      // Update via real-time state management or local state
      if (isConnected && !forceFallback) {
        updateProductionData(newProductionData, SYNC_STRATEGIES.IMMEDIATE);
      } else {
        // Fallback mode - update local state directly
        setFallbackMode(true);
        // You would set local state here if not using context
      }

      setLastFetchTime(Date.now());
      console.log('✅ Production data fetched successfully');

    } catch (error) {
      console.error('❌ Error fetching production data:', error);
      
      // Enable fallback mode if WebSocket fails
      if (!forceFallback) {
        setFallbackMode(true);
        console.log('🔄 Switching to fallback mode due to fetch error');
      }
    } finally {
      setLoading(false);
    }
  }, [isConnected, updateProductionData, SYNC_STRATEGIES, baseURL]);

  /**
   * Handle date selection for detailed view
   */
  const handleDateChange = useCallback(debounce(async (date) => {
    if (!date) return;
    
    try {
      const formattedDate = date.format('YYYY-MM-DD');
      const response = await superagent
        .get(baseURL + `/api/arrets-production/${formattedDate}`)
        .withCredentials()
        .timeout(30000)
        .retry(2);
      
      setSelectedDateData(response.body);
      setSelectedDate(date);
      
      console.log(`📅 Date-specific data loaded for ${formattedDate}`);
    } catch (error) {
      console.error('❌ Error fetching date data:', error);
    }
  }, 300), [baseURL]);

  /**
   * Handle connection status changes
   */
  useEffect(() => {
    if (connectionStatus === 'connected' && fallbackMode) {
      console.log('🔄 WebSocket reconnected - switching back to real-time mode');
      setFallbackMode(false);
    } else if (connectionStatus === 'disconnected' && !fallbackMode) {
      console.log('⚠️ WebSocket disconnected - enabling fallback mode');
      setFallbackMode(true);
    }
  }, [connectionStatus, fallbackMode]);

  /**
   * Initial data fetch and fallback polling
   */
  useEffect(() => {
    // Initial fetch
    fetchData();

    // Set up fallback polling when WebSocket is unavailable
    let pollInterval;
    if (fallbackMode) {
      pollInterval = setInterval(() => {
        fetchData(true);
      }, 5000); // Poll every 5 seconds in fallback mode
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [fetchData, fallbackMode]);

  /**
   * Render connection status indicator
   */
  const renderConnectionStatus = () => {
    const getStatusConfig = () => {
      switch (connectionStatus) {
        case 'connected':
          return {
            status: 'success',
            icon: <WifiOutlined />,
            text: 'Real-time Connected',
            color: '#52c41a'
          };
        case 'connecting':
          return {
            status: 'processing',
            icon: <SyncOutlined spin />,
            text: 'Connecting...',
            color: '#1890ff'
          };
        case 'disconnected':
          return {
            status: 'error',
            icon: <DisconnectOutlined />,
            text: 'Disconnected',
            color: '#ff4d4f'
          };
        default:
          return {
            status: 'default',
            icon: <ExclamationCircleOutlined />,
            text: 'Unknown',
            color: '#d9d9d9'
          };
      }
    };

    const config = getStatusConfig();

    return (
      <Tooltip title={`Connection Status: ${config.text}${fallbackMode ? ' (Fallback Mode)' : ''}`}>
        <Badge 
          status={config.status} 
          text={
            <span style={{ color: config.color }}>
              {config.icon} {config.text}
              {fallbackMode && <Tag color="orange" size="small" style={{ marginLeft: 8 }}>Fallback</Tag>}
            </span>
          }
        />
      </Tooltip>
    );
  };

  /**
   * Render conflict resolution alerts
   */
  const renderConflicts = () => {
    if (conflicts.length === 0) return null;

    return (
      <Alert
        message="Data Conflicts Detected"
        description={
          <div>
            <p>Some data conflicts were detected during synchronization:</p>
            {conflicts.map((conflict, index) => (
              <div key={index} style={{ marginBottom: 8 }}>
                <Text code>{conflict.type}</Text>
                <span style={{ marginLeft: 8 }}>
                  <a onClick={() => resolveConflict(conflict.id, 'last_write_wins')}>
                    Resolve
                  </a>
                </span>
              </div>
            ))}
          </div>
        }
        type="warning"
        showIcon
        closable
        style={{ marginBottom: 16 }}
      />
    );
  };

  // Extract data from productionData or use defaults
  const {
    chartData = [],
    sidecards = [],
    machinePerformance = [],
    hourlyTrends = [],
    operatorStats = [],
    oeeTrends = [],
    speedTrends = [],
    shiftComparison = [],
    lastUpdated
  } = productionData || {};

  // Calculate statistics from sidecards
  const goodQty = sidecards.find(card => card.label === 'Bonne Quantité')?.value || 0;
  const totalQty = sidecards.find(card => card.label === 'Quantité Totale')?.value || 0;
  const rejectedQty = sidecards.find(card => card.label === 'Quantité Rejetée')?.value || 0;
  const efficiency = totalQty > 0 ? ((goodQty / totalQty) * 100) : 0;

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Layout.Content style={{ padding: '24px' }}>
        {/* Header with connection status */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ margin: 0 }}>
            <DashboardOutlined /> Production Dashboard
          </Title>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            {renderConnectionStatus()}
            {lastUpdated && (
              <Text type="secondary">
                Last updated: {dayjs(lastUpdated).format('HH:mm:ss')}
              </Text>
            )}
            {!isConnected && (
              <a onClick={reconnect}>
                <SyncOutlined /> Reconnect
              </a>
            )}
          </div>
        </div>

        {/* Conflict resolution alerts */}
        {renderConflicts()}

        {/* Loading spinner */}
        <Spin spinning={loading} size="large">
          {/* Statistics Cards */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="Production Totale"
                  value={goodQty}
                  formatter={(value) => formatFrenchInteger(value)}
                  prefix={<RiseOutlined style={{ color: '#3f8600' }} />}
                  suffix="pcs"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="Quantité Totale"
                  value={totalQty}
                  formatter={(value) => formatFrenchInteger(value)}
                  prefix={<BarChartOutlined style={{ color: '#1890ff' }} />}
                  suffix="pcs"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="Quantité Rejetée"
                  value={rejectedQty}
                  formatter={(value) => formatFrenchInteger(value)}
                  prefix={<FallOutlined style={{ color: '#cf1322' }} />}
                  suffix="pcs"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="Efficacité"
                  value={efficiency}
                  formatter={(value) => formatFrenchPercentage(value / 100)}
                  prefix={<PieChartOutlined style={{ color: '#722ed1' }} />}
                />
                <Progress 
                  percent={efficiency} 
                  size="small" 
                  status={efficiency >= 90 ? 'success' : efficiency >= 70 ? 'normal' : 'exception'}
                  style={{ marginTop: 8 }}
                />
              </Card>
            </Col>
          </Row>

          {/* Date Picker */}
          <Card style={{ marginBottom: 24 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
              <ClockCircleOutlined />
              <Text strong>Sélectionner une date pour analyse détaillée:</Text>
              <DatePicker 
                onChange={handleDateChange}
                format="DD/MM/YYYY"
                placeholder="Choisir une date"
              />
            </div>
          </Card>

          {/* Main Charts */}
          <Row gutter={[16, 16]}>
            {/* Production Chart */}
            <Col xs={24} lg={12}>
              <Card title={<><AreaChartOutlined /> Production par Date</>}>
                {chartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="bonne_quantite" 
                        stackId="1"
                        stroke="#52c41a" 
                        fill="#52c41a" 
                        name="Bonne Quantité"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="quantite_rejetee" 
                        stackId="1"
                        stroke="#ff4d4f" 
                        fill="#ff4d4f" 
                        name="Quantité Rejetée"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <Empty description="Aucune donnée disponible" />
                )}
              </Card>
            </Col>

            {/* OEE Trends */}
            <Col xs={24} lg={12}>
              <Card title={<><LineChartOutlined /> Tendances OEE</>}>
                {oeeTrends.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={oeeTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="oee" 
                        stroke="#1890ff" 
                        strokeWidth={2}
                        name="OEE (%)"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <Empty description="Aucune donnée OEE disponible" />
                )}
              </Card>
            </Col>
          </Row>
        </Spin>
      </Layout.Content>
    </Layout>
  );
});

ProductionRealtime.displayName = 'ProductionRealtime';

export default ProductionRealtime;
