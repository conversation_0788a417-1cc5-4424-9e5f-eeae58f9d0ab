import React from "react";
import {
  ToolOutlined,
  MoreOutlined,
  LineChartOutlined,
  SettingOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { Space, Tag, Progress, Tooltip, Button, Dropdown, Typography } from "antd";
import { formatFrenchTime, formatFrenchInteger, formatFrenchPercentage } from "../../utils/numberFormatter";
const { Text } = Typography;

/**
 * Generate machine performance columns configuration for the dashboard table
 * Based on machine_daily_table_mould database structure
 * @param {Array} COLORS - Array of colors for styling
 * @param {Function} normalizePercentage - Function to normalize percentage values
 * @returns {Array} Array of column configuration objects for the table
 */
const getMachinePerformanceColumns = (COLORS, normalizePercentage) => [
  {
    title: "Machine",
    dataIndex: "Machine_Name",
    key: "Machine_Name",
    fixed: "left",
    width: 120,
    render: (text) => (
      <Space>
        <ToolOutlined style={{ color: COLORS[0] }} />
        <Text strong>{text || "N/A"}</Text>
      </Space>
    ),
    sorter: (a, b) => (a.Machine_Name || "").localeCompare(b.Machine_Name || ""),
  },
  {
    title: "Date d'Insertion",
    dataIndex: "Date_Insert_Day",
    key: "Date_Insert_Day",
    width: 160,
    render: (text) => {
      if (!text) return <Text>N/A</Text>;
      // Handle the date format from your database
      const date = new Date(text);
      return (
        <Text>
          {date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </Text>
      );
    },
    sorter: (a, b) => {
      const dateA = new Date(a.Date_Insert_Day || 0);
      const dateB = new Date(b.Date_Insert_Day || 0);
      return dateA - dateB;
    },
  },
  {
    title: "Heures de Fonctionnement",
    dataIndex: "Run_Hours_Day",
    key: "Run_Hours_Day",
    width: 150,
    render: (text) => <Tag color="green">{formatFrenchTime(parseFloat(text) || 0)} h</Tag>,
    sorter: (a, b) => (parseFloat(a.Run_Hours_Day) || 0) - (parseFloat(b.Run_Hours_Day) || 0),
  },
  {
    title: "Heures d'Arrêt",
    dataIndex: "Down_Hours_Day",
    key: "Down_Hours_Day",
    width: 130,
    render: (text) => <Tag color="orange">{formatFrenchTime(parseFloat(text) || 0)} h</Tag>,
    sorter: (a, b) => (parseFloat(a.Down_Hours_Day) || 0) - (parseFloat(b.Down_Hours_Day) || 0),
  },
  {
    title: "Quantité Bonne",
    dataIndex: "Good_QTY_Day",
    key: "Good_QTY_Day",
    width: 140,
    render: (text) => <Tag color="green">{formatFrenchInteger(parseInt(text) || 0)} pcs</Tag>,
    sorter: (a, b) => (parseInt(a.Good_QTY_Day) || 0) - (parseInt(b.Good_QTY_Day) || 0),
  },
  {
    title: "Quantité Rejetée",
    dataIndex: "Rejects_QTY_Day",
    key: "Rejects_QTY_Day",
    width: 140,
    render: (text) => <Tag color="red">{formatFrenchInteger(parseInt(text) || 0)} pcs</Tag>,
    sorter: (a, b) => (parseInt(a.Rejects_QTY_Day) || 0) - (parseInt(b.Rejects_QTY_Day) || 0),
  },
  {
    title: "Vitesse",
    dataIndex: "Speed_Day",
    key: "Speed_Day",
    width: 100,
    render: (text) => <Tag color="blue">{formatFrenchTime(parseFloat(text) || 0)}</Tag>,
    sorter: (a, b) => (parseFloat(a.Speed_Day) || 0) - (parseFloat(b.Speed_Day) || 0),
  },
  {
    title: "Taux de Disponibilité",
    dataIndex: "Availability_Rate_Day",
    key: "Availability_Rate_Day",
    width: 160,
    render: (value) => {
      const numValue = normalizePercentage(value);
      return (
        <Tooltip title={`${formatFrenchPercentage(numValue, 1)}% de disponibilité`}>
          <Progress
            percent={numValue}
            size="small"
            status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
            format={(percent) => {
              return typeof percent === "number" && !isNaN(percent)
                ? `${formatFrenchPercentage(percent, 1)}%`
                : "0,0%"
            }}
          />
        </Tooltip>
      );
    },
    sorter: (a, b) => normalizePercentage(a.Availability_Rate_Day) - normalizePercentage(b.Availability_Rate_Day),
  },
  {
    title: "Taux de Performance",
    dataIndex: "Performance_Rate_Day",
    key: "Performance_Rate_Day",
    width: 160,
    render: (value) => {
      const numValue = normalizePercentage(value);
      return (
        <Tooltip title={`${numValue.toFixed(1)}% de performance`}>
          <Progress
            percent={numValue}
            size="small"
            status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
            format={(percent) => {
              return typeof percent === "number" && !isNaN(percent)
                ? `${percent.toFixed(1)}%`
                : "0.0%"
            }}
          />
        </Tooltip>
      );
    },
    sorter: (a, b) => normalizePercentage(a.Performance_Rate_Day) - normalizePercentage(b.Performance_Rate_Day),
  },
  {
    title: "Taux de Qualité",
    dataIndex: "Quality_Rate_Day",
    key: "Quality_Rate_Day",
    width: 140,
    render: (value) => {
      const numValue = normalizePercentage(value);
      return (
        <Tooltip title={`${numValue.toFixed(1)}% de qualité`}>
          <Progress
            percent={numValue}
            size="small"
            status={numValue > 90 ? "success" : numValue > 80 ? "normal" : "exception"}
            format={(percent) => {
              return typeof percent === "number" && !isNaN(percent)
                ? `${percent.toFixed(1)}%`
                : "0.0%"
            }}
          />
        </Tooltip>
      );
    },
    sorter: (a, b) => normalizePercentage(a.Quality_Rate_Day) - normalizePercentage(b.Quality_Rate_Day),
  },
  {
    title: "TRS",
    dataIndex: "OEE_Day",
    key: "OEE_Day",
    width: 120,
    render: (value) => {
      const numValue = normalizePercentage(value);
      return (
        <Tooltip title={`${numValue.toFixed(1)}% de TRS`}>
          <Progress
            percent={numValue}
            size="small"
            status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
            format={(percent) => {
              return typeof percent === "number" && !isNaN(percent)
                ? `${percent.toFixed(1)}%`
                : "0.0%"
            }}
          />
        </Tooltip>
      );
    },
    sorter: (a, b) => normalizePercentage(a.OEE_Day) - normalizePercentage(b.OEE_Day),
    defaultSortOrder: "descend",
  },
  {
    title: "Équipe",
    dataIndex: "Shift",
    key: "Shift",
    width: 100,
    render: (text) => <Tag color="blue">{text || "N/A"}</Tag>,
    filters: [
      { text: "Shift 1", value: "Shift 1" },
      { text: "Shift 2", value: "Shift 2" },
      { text: "Shift 3", value: "Shift 3" },
    ],
    onFilter: (value, record) => record.Shift === value,
  },
  {
    title: "Numéro de Pièce",
    dataIndex: "Part_Number",
    key: "Part_Number",
    width: 140,
    render: (text) => <Tag color="purple">{text || "N/A"}</Tag>,
  },
  {
    title: "Poids Unitaire",
    dataIndex: "Poid_Unitaire",
    key: "Poid_Unitaire",
    width: 120,
    render: (text) => <Tag color="cyan">{text || "N/A"}</Tag>,
  },
  {
    title: "Cycle Théorique",
    dataIndex: "Cycle_Theorique",
    key: "Cycle_Theorique",
    width: 130,
    render: (text) => <Tag color="magenta">{text || "N/A"}</Tag>,
  },
  {
    title: "Poids Purge",
    dataIndex: "Poid_Purge",
    key: "Poid_Purge",
    width: 110,
    render: (text) => <Tag color="gold">{text || "N/A"}</Tag>,
  },

  {
    title: "Actions",
    key: "actions",
    fixed: "right",
    width: 80,
    render: () => (
      <Dropdown
        menu={{
          items: [
            {
              key: '1',
              icon: <LineChartOutlined />,
              label: 'Voir tendances',
            },
            {
              key: '2',
              icon: <SettingOutlined />,
              label: 'Paramètres',
            },
            {
              key: '3',
              icon: <DownloadOutlined />,
              label: 'Exporter données',
            },
          ],
        }}
        trigger={["click"]}
      >
        <Button type="text" icon={<MoreOutlined />} />
      </Dropdown>
    ),
  },
];

export default getMachinePerformanceColumns;
