#!/usr/bin/env node

/**
 * Test GraphQL schema compilation
 */

import { GraphQLSchema } from 'graphql';

async function testSchema() {
  console.log('🔍 Testing GraphQL schema compilation...\n');

  try {
    console.log('1. Testing dailyTableResolvers import...');
    const { dailyTableQueries, dailyTableTypes } = await import('./backend/routes/graphql/dailyTableResolvers.js');
    console.log('✅ dailyTableResolvers imported successfully');
    console.log('   Queries:', Object.keys(dailyTableQueries).length);
    console.log('   Types:', Object.keys(dailyTableTypes).length);
  } catch (error) {
    console.log('❌ dailyTableResolvers import failed:', error.message);
    return;
  }

  try {
    console.log('\n2. Testing stopTableResolvers import...');
    const { stopTableQueries, stopTableTypes } = await import('./backend/routes/graphql/stopTableResolvers.js');
    console.log('✅ stopTableResolvers imported successfully');
    console.log('   Queries:', Object.keys(stopTableQueries).length);
    console.log('   Types:', Object.keys(stopTableTypes).length);
  } catch (error) {
    console.log('❌ stopTableResolvers import failed:', error.message);
    return;
  }

  try {
    console.log('\n3. Testing externalApiResolvers import...');
    const { externalApiResolvers } = await import('./backend/routes/graphql/externalApiResolvers.js');
    console.log('✅ externalApiResolvers imported successfully');
  } catch (error) {
    console.log('❌ externalApiResolvers import failed:', error.message);
    return;
  }

  try {
    console.log('\n4. Testing schema compilation...');
    const { schema } = await import('./backend/routes/graphql/schema.js');
    console.log('✅ Schema compiled successfully');
    console.log('   Schema type:', schema instanceof GraphQLSchema);
    
    // Test schema introspection
    const queryType = schema.getQueryType();
    const fields = queryType.getFields();
    console.log('   Query fields:', Object.keys(fields).length);
    console.log('   Available queries:', Object.keys(fields).slice(0, 10).join(', '), '...');
    
  } catch (error) {
    console.log('❌ Schema compilation failed:', error.message);
    console.log('   Stack:', error.stack);
    return;
  }

  console.log('\n🏁 Schema testing completed successfully!');
}

testSchema().catch(console.error);
