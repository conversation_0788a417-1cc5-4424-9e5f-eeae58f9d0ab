/**
 * Data Synchronization GraphQL Resolvers
 * Provides GraphQL endpoints for monitoring and controlling data sync
 */

import dataSyncService from '../../services/DataSyncService.js';
import { enhancedTypes } from './enhancedDashboardTypes.js';

const dataSyncResolvers = {
  // Get current sync statistics
  getDataSyncStats: {
    type: enhancedTypes.DataSyncStatsType,
    resolve: async () => {
      try {
        const stats = await dataSyncService.getSyncStats();
        
        // Calculate success rate
        const successRate = stats.totalSyncs > 0 
          ? (stats.successfulSyncs / stats.totalSyncs) * 100 
          : 0;
        
        // Estimate next sync (assuming 15-minute intervals)
        const nextSyncEstimate = stats.lastSync 
          ? new Date(new Date(stats.lastSync).getTime() + 15 * 60 * 1000).toISOString()
          : null;
        
        return {
          ...stats,
          successRate: Math.round(successRate * 100) / 100,
          nextSyncEstimate
        };
      } catch (error) {
        console.error('Error getting sync stats:', error);
        return {
          isRunning: false,
          lastSync: null,
          totalSyncs: 0,
          successfulSyncs: 0,
          failedSyncs: 0,
          conflictsResolved: 0,
          recordsSynced: 0,
          successRate: 0,
          nextSyncEstimate: null
        };
      }
    }
  },

  // Trigger manual synchronization
  triggerManualSync: {
    type: enhancedTypes.ManualSyncResultType,
    resolve: async () => {
      const startTime = Date.now();
      
      try {
        console.log('🔄 Manual sync triggered via GraphQL');
        
        const result = await dataSyncService.triggerManualSync();
        const duration = Date.now() - startTime;
        
        if (result) {
          return {
            success: true,
            message: 'Manual synchronization completed successfully',
            stats: result,
            duration
          };
        } else {
          return {
            success: false,
            message: 'Synchronization already in progress',
            stats: await dataSyncService.getSyncStats(),
            duration
          };
        }
      } catch (error) {
        console.error('Error during manual sync:', error);
        const duration = Date.now() - startTime;
        
        return {
          success: false,
          message: `Synchronization failed: ${error.message}`,
          stats: await dataSyncService.getSyncStats(),
          duration
        };
      }
    }
  }
};

export default dataSyncResolvers;
