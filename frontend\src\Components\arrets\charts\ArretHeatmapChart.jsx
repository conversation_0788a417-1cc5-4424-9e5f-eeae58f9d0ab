import React, { memo } from 'react';
import { Spin, Empty } from 'antd';
import { ResponsiveContainer, AreaChart, Area, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip } from 'recharts';
import SOMIPEM_COLORS from '../../../styles/brand-colors';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';
import { useSettings } from '../../../hooks/useSettings';

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE,
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to use SOMIPEM Primary Blue instead of green
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOMIPEM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
};

const ArretHeatmapChart = memo(({ data = [], loading, colors }) => {
  // Get settings for enhanced chart configuration
  const { settings, charts, theme } = useSettings();

  // Enhanced chart configuration
  const enhancedChartConfig = useUnifiedChartConfig({
    charts,
    theme
  });

  // Use colors prop if provided, otherwise fall back to chart config colors
  const chartColors = colors || enhancedChartConfig.colors || [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Ensure data is an array - handle both direct arrays and response objects
  const stopsData = Array.isArray(data) ? data : (data?.data || []);

  console.log('🎯 ArretHeatmapChart - Data received:', {
    originalDataLength: data?.length || 0,
    processedDataLength: stopsData?.length || 0,
    dataType: typeof data,
    isArray: Array.isArray(data),
    loading,
    sampleData: stopsData?.slice(0, 2) || []
  });

  // Process data using the same logic as ArretTimePatternChart
  const processTimePatternData = (stopsData) => {
    // Ensure data is an array - handle both direct arrays and response objects
    const processedData = Array.isArray(stopsData) ? stopsData : (stopsData?.data || []);
    
    if (!Array.isArray(processedData) || processedData.length === 0) {
      console.log('🔍 ArretHeatmapChart - No valid data to process');
      return [];
    }
    
    const MAX_REASONABLE_DURATION = 480; // 8 hours maximum
    const MIN_REASONABLE_DURATION = 1;   // 1 minute minimum
    
    // Group by hour of day
    const hourlyStats = {};
    let outlierCount = 0;
    let totalStopsProcessed = 0;
    
    // Initialize all hours
    for (let hour = 0; hour < 24; hour++) {
      hourlyStats[hour] = {
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        durations: [], // Track individual durations for debugging
        outliers: []   // Track outliers separately
      };
    }
    
    // Helper function to parse date in multiple formats
    const parseCustomDate = (dateStr) => {
      if (!dateStr) return null;
      try {
        const str = String(dateStr).trim();
        
        // Handle ISO format first (from GraphQL: "2024-12-07T14:30:00Z")
        if (str.includes('T') && (str.includes('Z') || str.includes('+') || str.includes('-'))) {
          const date = new Date(str);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
        
        // Handle DD/MM/YYYY HH:MM:SS format (legacy database)
        if (str.includes('/')) {
          const parts = str.split(' ');
          const datePart = parts[0]; // "DD/MM/YYYY"
          const timePart = parts[1] || '00:00:00'; // "HH:MM:SS" or default
          
          const [day, month, year] = datePart.split('/');
          
          if (day && month && year && 
              day.length <= 2 && month.length <= 2 && year.length === 4) {
            
            const paddedDay = day.padStart(2, '0');
            const paddedMonth = month.padStart(2, '0');
            
            // Create Date object using year, month-1 (0-indexed), day, hours, minutes, seconds
            const timeParts = timePart.split(':');
            const hours = parseInt(timeParts[0]) || 0;
            const minutes = parseInt(timeParts[1]) || 0;
            const seconds = parseInt(timeParts[2]) || 0;
            
            const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), hours, minutes, seconds);
            
            if (!isNaN(date.getTime())) {
              return date;
            }
          }
        }
        
        // Handle YYYY HH:MM:SS-MM-DD format (alternative database format)
        if (str.includes('-') && str.includes(' ')) {
          const spaceIndex = str.indexOf(' ');
          const year = str.substring(0, spaceIndex);
          const remaining = str.substring(spaceIndex + 1);
          
          if (remaining.includes('-')) {
            const dashIndex = remaining.lastIndexOf('-');
            const time = remaining.substring(0, dashIndex);
            const monthDay = remaining.substring(dashIndex + 1);
            
            if (monthDay.includes('-')) {
              const [month, day] = monthDay.split('-');
              if (year && month && day && time) {
                const timeParts = time.split(':');
                const hours = parseInt(timeParts[0]) || 0;
                const minutes = parseInt(timeParts[1]) || 0;
                const seconds = parseInt(timeParts[2]) || 0;
                
                const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), hours, minutes, seconds);
                
                if (!isNaN(date.getTime())) {
                  return date;
                }
              }
            }
          }
        }
        
        // Standard Date constructor fallback
        const date = new Date(str);
        if (!isNaN(date.getTime())) {
          return date;
        }
        
        return null;
      } catch (error) {
        console.warn('Date parsing error:', error, 'for date:', dateStr);
        return null;
      }
    };
    
    processedData.forEach(stop => {
      // Handle multiple possible field names for start time
      const startTime = stop.Debut_Stop || stop.debut_stop || stop.startTime || stop.start_time;
      
      if (startTime) {
        try {
          const startDate = parseCustomDate(startTime);
          if (startDate && !isNaN(startDate.getTime())) {
            const hour = startDate.getHours();
            totalStopsProcessed++;
            
            if (hourlyStats[hour]) {
              // Calculate duration - prefer pre-calculated duration_minutes from GraphQL or legacy
              let duration = 0;
              
              if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
                // Use pre-calculated duration from GraphQL resolver or legacy field
                duration = parseFloat(stop.duration_minutes);
              } else if (stop.duration !== undefined && stop.duration !== null) {
                // Use duration from GraphQL
                duration = parseFloat(stop.duration);
              } else {
                // Fallback to calculating from start/end times
                const endTime = stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.end_time;
                if (endTime) {
                  const endDate = parseCustomDate(endTime);
                  if (endDate && !isNaN(endDate.getTime())) {
                    duration = (endDate - startDate) / (1000 * 60); // Duration in minutes
                  }
                }
              }
              
              if (duration > 0) {
                // Check if duration is within reasonable bounds
                if (duration >= MIN_REASONABLE_DURATION && duration <= MAX_REASONABLE_DURATION) {
                  // Normal operational stop
                  hourlyStats[hour].count += 1;
                  hourlyStats[hour].totalDuration += duration;
                  hourlyStats[hour].durations.push(duration);
                } else {
                  // Outlier - track but don't include in average
                  hourlyStats[hour].outliers.push(duration);
                  outlierCount++;
                }
              }
            }
          }
        } catch (error) {
          console.warn('Error parsing time:', error);
        }
      }
    });

    // Calculate averages
    Object.keys(hourlyStats).forEach(hour => {
      const stats = hourlyStats[hour];
      stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
    });    // Transform to format expected by the chart
    const chartData = [];
    for (let hour = 0; hour < 24; hour++) {
      chartData.push({
        hour: hour,
        avgDuration: Math.round(hourlyStats[hour].avgDuration) // Match ArretTimePatternChart rounding
      });
    }

    return chartData;
  };

  const chartData = processTimePatternData(stopsData);

  // Check if data is empty
  if (!chartData || chartData.length === 0 || chartData.every(item => item.avgDuration === 0)) {
    console.warn('🚨 ArretHeatmapChart - No chart data available:', {
      chartDataLength: chartData?.length || 0,
      stopsDataLength: stopsData?.length || 0,
      allZeroDurations: chartData?.every(item => item.avgDuration === 0),
      sampleChartData: chartData?.slice(0, 3) || []
    });
    
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '300px' }}>
        <Empty description="Aucune donnée de durée par heure disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  return (
    <ResponsiveContainer {...enhancedChartConfig.responsiveContainerProps} height={enhancedChartConfig.height || 300}>
      <AreaChart data={chartData} margin={enhancedChartConfig.margins}>
        <CartesianGrid {...enhancedChartConfig.gridConfig} />
        <XAxis
          dataKey="hour"
          {...enhancedChartConfig.axisConfig}
          label={{
            value: "Heure de la journée",
            position: "bottom",
            offset: 0,
            style: { textAnchor: 'middle', fill: SOMIPEM_COLORS.LIGHT_GRAY }
          }}
        />
        <YAxis
          {...enhancedChartConfig.axisConfig}
          label={{
            value: "Durée moyenne (min)",
            angle: -90,
            position: "insideLeft",
            style: { textAnchor: 'middle', fill: SOMIPEM_COLORS.LIGHT_GRAY }
          }}
        />        <Tooltip
          formatter={(value) => {
            const numValue = typeof value === 'number' ? value : parseFloat(value);
            const safeValue = isNaN(numValue) ? 0 : numValue;
            return [`${safeValue.toFixed(1)} min`, "Durée moyenne"];
          }}
          contentStyle={{
            backgroundColor: "#fff",
            border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // SOMIPEM Primary Blue border
            borderRadius: 4,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            color: SOMIPEM_COLORS.DARK_GRAY // SOMIPEM Dark Gray text
          }}
        />
        <Area
          type="monotone"
          dataKey="avgDuration"
          stroke={chartColors[0]}
          fill={`${chartColors[0]}33`}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
});

export default ArretHeatmapChart;
