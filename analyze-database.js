import mysql from 'mysql2/promise';

(async () => {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'testingarea51'
    });

    console.log('🔍 Analyzing database structure for shifts vs machines...\n');

    const dateCondition = `
      STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y %H:%i') >= DATE_SUB(CURDATE(), INTERVAL 365 DAY)
    `;

    const [rows] = await connection.execute(`
      SELECT 
        Machine_Name,
        Shift,
        Date_Insert_Day,
        Good_QTY_Day,
        OEE_Day
      FROM machine_daily_table_mould 
      WHERE ${dateCondition}
      ORDER BY STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y %H:%i') DESC, Shift ASC
      LIMIT 10
    `);

    console.log('📊 Sample data structure:');
    console.table(rows);

    const [shiftStats] = await connection.execute(`
      SELECT 
        Shift,
        COUNT(*) as record_count,
        COUNT(DISTINCT Machine_Name) as unique_machines,
        COUNT(DISTINCT Date_Insert_Day) as unique_dates
      FROM machine_daily_table_mould 
      WHERE ${dateCondition}
      GROUP BY Shift
      ORDER BY Shift
    `);

    console.log('\n🔢 Shift statistics:');
    console.table(shiftStats);

    const [shiftAggregation] = await connection.execute(`
      SELECT 
        Shift,
        SUM(
          CASE 
            WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
              CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
            ELSE 0 
          END
        ) AS total_production,
        AVG(
          CASE 
            WHEN OEE_Day LIKE '%\\%' THEN
              CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
            WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
              CASE 
                WHEN CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) <= 1 THEN
                  CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4)) * 100
                ELSE
                  CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
              END
            ELSE 0 
          END
        ) AS avg_oee
      FROM machine_daily_table_mould 
      WHERE ${dateCondition}
      GROUP BY Shift
      ORDER BY Shift
    `);

    console.log('\n📈 Shift-based aggregation (for team comparison):');
    console.table(shiftAggregation);

    await connection.end();
  } catch (error) {
    console.error('Error:', error);
  }
})();
