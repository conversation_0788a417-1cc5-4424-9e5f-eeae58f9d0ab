import React, { memo } from 'react';
import { <PERSON>sponsive<PERSON><PERSON>r, BarChart, Bar, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Empty, Spin } from 'antd';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';

const DowntimeDurationChart = memo(({ data = [], loading = false }) => {
  // Get unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    chartType: 'bar'
  });

  console.log('🔧 DowntimeDurationChart received data:', {
    dataType: typeof data,
    isArray: Array.isArray(data),
    dataLength: data?.length || 0,
    sampleData: data?.slice(0, 3) || []
  });

  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée de temps d'arrêt disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  // Process and sort data to focus on duration
  const processedData = safeData
    .map(item => ({
      reason: item.reason || item.Code_Stop || item.stopName || 'N/A',
      value: parseFloat(item.value || item.duration || item.count || 0),
      percentage: parseFloat(item.percentage || 0),
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 8) // Show only top 8 reasons for better visibility
    .map(item => ({
      ...item,
      reason: item.reason.length > 12 ? item.reason.substring(0, 10) + '...' : item.reason
    }));
  // Color coding based on impact level with stronger colors
  const getBarColor = (value, maxValue) => {
    const ratio = value / maxValue;
    if (ratio > 0.7) return "#f5222d"; // Red - High impact
    if (ratio > 0.4) return "#fa541c"; // Orange - Medium impact
    if (ratio > 0.2) return "#fa8c16"; // Yellow-orange - Low-medium impact
    return "#73d13d"; // Green - Low impact
  };

  const maxValue = Math.max(...processedData.map(item => item.value));  return (
    <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
      <BarChart data={processedData} margin={chartConfig.margins}>
        <CartesianGrid {...chartConfig.gridConfig} />
        <XAxis
          dataKey="reason"
          {...chartConfig.axisConfig}
          angle={-25}
          textAnchor="end"
          height={60}
          interval={0}
        />
        <YAxis
          {...chartConfig.axisConfig}
          width={35}
          tickCount={4}
          tickFormatter={(value) => `${value}`}
        />
        <Tooltip {...chartConfig.tooltipConfig} />        <Bar
          dataKey="value"
          fill={(entry) => getBarColor(entry.value, maxValue)}
          radius={[4, 4, 0, 0]}
          name="Durée"
          barSize={38}
          maxBarSize={40}
        >
          {processedData.map((entry, index) => (
            <Bar key={`bar-${index}`} fill={getBarColor(entry.value, maxValue)} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
});

DowntimeDurationChart.displayName = 'DowntimeDurationChart';

export default DowntimeDurationChart;
