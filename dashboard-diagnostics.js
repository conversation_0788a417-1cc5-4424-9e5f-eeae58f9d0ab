import mysql from 'mysql2/promise';
import { config } from 'dotenv';

// Load environment configuration
config();

/**
 * Comprehensive Dashboard Diagnostics
 * This script analyzes the issues visible in the provided screenshots:
 * 1. TRS chart data format and values
 * 2. Filter functionality 
 * 3. Chart display issues
 * 4. Data consistency between display and backend
 */

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'Testingarea51',
  port:  3306
};

class DashboardDiagnostics {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ Database connection established');
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('🔌 Database connection closed');
    }
  }

  /**
   * Diagnostic 1: TRS Data Format Analysis
   * Analyze TRS values and format to understand display issues
   */
  async diagnoseTRSDataFormat() {
    console.log('\n📊 === TRS DATA FORMAT ANALYSIS ===');
    
    try {
      const query = `
        SELECT 
          Machine_Name,
          Date_Insert_Day,
          OEE_Day,
          Availability_Rate_Day,
          Performance_Rate_Day,
          Quality_Rate_Day,
          CASE 
            WHEN OEE_Day LIKE '%\\%%' THEN 'percentage_with_symbol'
            WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN 'decimal_number'
            WHEN OEE_Day REGEXP '^[0-1]([,.][0-9]+)?$' THEN 'decimal_0_to_1'
            ELSE 'other_format'
          END as oee_format,
          CASE 
            WHEN OEE_Day LIKE '%\\%%' THEN
              CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
            WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
              CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
            ELSE 0 
          END as oee_parsed
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ORDER BY Date_Insert_Day DESC, Machine_Name
        LIMIT 20
      `;

      const [rows] = await this.connection.execute(query);

      console.log('📈 TRS Value Formats Found:');
      const formatCounts = {};
      const valueRanges = { min: Infinity, max: -Infinity };

      rows.forEach(row => {
        const format = row.oee_format;
        formatCounts[format] = (formatCounts[format] || 0) + 1;
        
        const parsedValue = parseFloat(row.oee_parsed);
        if (!isNaN(parsedValue)) {
          valueRanges.min = Math.min(valueRanges.min, parsedValue);
          valueRanges.max = Math.max(valueRanges.max, parsedValue);
        }

        console.log(`  • ${row.Machine_Name} (${row.Date_Insert_Day}): ${row.OEE_Day} → ${row.oee_parsed} (${format})`);
      });

      console.log('\n📋 Format Distribution:');
      Object.entries(formatCounts).forEach(([format, count]) => {
        console.log(`  • ${format}: ${count} records`);
      });

      console.log(`\n📏 Value Range: ${valueRanges.min.toFixed(4)} to ${valueRanges.max.toFixed(4)}`);

      // Check if values are in 0-1 range (decimals) or 0-100 range (percentages)
      if (valueRanges.max <= 1) {
        console.log('⚠️  TRS values appear to be in decimal format (0-1), may need conversion to percentage (0-100)');
      } else {
        console.log('✅ TRS values appear to be in percentage format (0-100)');
      }

      return { formatCounts, valueRanges, samples: rows };
    } catch (error) {
      console.error('❌ TRS format analysis failed:', error.message);
      return null;
    }
  }

  /**
   * Diagnostic 2: Machine Duplication Analysis  
   * Check for machine name duplication issues
   */
  async diagnoseMachineDuplication() {
    console.log('\n🔍 === MACHINE DUPLICATION ANALYSIS ===');
    
    try {
      const query = `
        SELECT 
          Machine_Name,
          COUNT(*) as record_count,
          COUNT(DISTINCT Date_Insert_Day) as unique_dates,
          COUNT(DISTINCT Shift) as unique_shifts,
          GROUP_CONCAT(DISTINCT Shift ORDER BY Shift) as shifts_found
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY Machine_Name
        ORDER BY record_count DESC
      `;

      const [rows] = await this.connection.execute(query);

      console.log('🏭 Machine Record Distribution:');
      rows.forEach(row => {
        console.log(`  • ${row.Machine_Name}: ${row.record_count} records, ${row.unique_dates} dates, ${row.unique_shifts} shifts [${row.shifts_found}]`);
        
        if (row.record_count > row.unique_dates * row.unique_shifts) {
          console.log(`    ⚠️  Potential duplication: Expected max ${row.unique_dates * row.unique_shifts}, found ${row.record_count}`);
        }
      });

      // Check specifically for IPS01 (mentioned in the issue)
      const ips01Check = rows.find(row => row.Machine_Name.includes('IPS01'));
      if (ips01Check) {
        console.log(`\n🎯 IPS01 Analysis: ${ips01Check.record_count} records found`);
        
        // Get detailed IPS01 breakdown
        const detailQuery = `
          SELECT 
            Machine_Name,
            Date_Insert_Day,
            Shift,
            Good_QTY_Day,
            OEE_Day
          FROM machine_daily_table_mould 
          WHERE Machine_Name LIKE '%IPS01%'
            AND Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
          ORDER BY Date_Insert_Day DESC, Shift
        `;
        
        const [detailRows] = await this.connection.execute(detailQuery);
        console.log('📋 IPS01 Recent Records:');
        detailRows.forEach(row => {
          console.log(`    ${row.Date_Insert_Day} | ${row.Shift} | ${row.Good_QTY_Day} | ${row.OEE_Day}`);
        });
      }

      return rows;
    } catch (error) {
      console.error('❌ Machine duplication analysis failed:', error.message);
      return null;
    }
  }

  /**
   * Diagnostic 3: Data Aggregation Check
   * Verify data aggregation for charts works correctly
   */
  async diagnoseDataAggregation() {
    console.log('\n📈 === DATA AGGREGATION ANALYSIS ===');
    
    try {
      // Test the same aggregation logic used in GraphQL resolvers
      const query = `
        SELECT 
          DATE_FORMAT(
            CASE 
              WHEN Date_Insert_Day LIKE '%/%' THEN
                STR_TO_DATE(
                  SUBSTRING_INDEX(Date_Insert_Day, ' ', 1), 
                  '%d/%m/%Y'
                )
              ELSE
                STR_TO_DATE(Date_Insert_Day, '%Y-%m-%d')
            END,
            '%Y-%m-%d'
          ) AS Date_Insert_Day,
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS Total_Good_Qty_Day,
          SUM(
            CASE 
              WHEN Rejects_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Rejects_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS Total_Rejects_Qty_Day,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
              ELSE 0 
            END
          ) AS OEE_Day,
          COUNT(*) as record_count
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY Date_Insert_Day 
        ORDER BY Date_Insert_Day DESC
        LIMIT 10
      `;

      const [rows] = await this.connection.execute(query);

      console.log('📊 Daily Aggregated Data:');
      rows.forEach(row => {
        console.log(`  • ${row.Date_Insert_Day}: Good=${row.Total_Good_Qty_Day}, Rejects=${row.Total_Rejects_Qty_Day}, TRS=${row.OEE_Day}%, Records=${row.record_count}`);
      });

      // Calculate overall statistics
      const totalGood = rows.reduce((sum, row) => sum + parseFloat(row.Total_Good_Qty_Day || 0), 0);
      const totalRejects = rows.reduce((sum, row) => sum + parseFloat(row.Total_Rejects_Qty_Day || 0), 0);
      const avgTRS = rows.reduce((sum, row) => sum + parseFloat(row.OEE_Day || 0), 0) / rows.length;

      console.log(`\n📋 Summary Statistics:`);
      console.log(`  • Total Good: ${totalGood.toLocaleString()}`);
      console.log(`  • Total Rejects: ${totalRejects.toLocaleString()}`);
      console.log(`  • Average TRS: ${avgTRS.toFixed(2)}%`);
      console.log(`  • Days analyzed: ${rows.length}`);

      return { rows, totalGood, totalRejects, avgTRS };
    } catch (error) {
      console.error('❌ Data aggregation analysis failed:', error.message);
      return null;
    }
  }

  /**
   * Diagnostic 4: Chart Data Structure Check
   * Verify data structure matches what charts expect
   */
  async diagnoseChartDataStructure() {
    console.log('\n🎨 === CHART DATA STRUCTURE ANALYSIS ===');
    
    try {
      // Simulate machine performance data structure (for machine charts)
      const machineQuery = `
        SELECT 
          Machine_Name,
          Shift,
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS production,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4))
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
              ELSE 0 
            END
          ) AS oee
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        GROUP BY Machine_Name, Shift 
        ORDER BY Machine_Name, Shift
        LIMIT 15
      `;

      const [machineRows] = await this.connection.execute(machineQuery);

      console.log('🏭 Machine Chart Data Structure:');
      console.log('Expected fields: Machine_Name, production, oee');
      
      machineRows.forEach(row => {
        console.log(`  • ${row.Machine_Name} (${row.Shift}): production=${row.production}, oee=${row.oee}%`);
      });

      // Check for missing Machine_Name fields (chart mapping issue)
      const missingMachineNames = machineRows.filter(row => !row.Machine_Name || row.Machine_Name === 'N/A');
      if (missingMachineNames.length > 0) {
        console.log(`⚠️  Found ${missingMachineNames.length} records with missing/invalid Machine_Name`);
      }

      return machineRows;
    } catch (error) {
      console.error('❌ Chart data structure analysis failed:', error.message);
      return null;
    }
  }

  /**
   * Diagnostic 5: Filter Impact Analysis
   * Test how filters affect data results
   */
  async diagnoseFilterImpact() {
    console.log('\n🔍 === FILTER IMPACT ANALYSIS ===');
    
    try {
      // Test 1: No filters
      const noFilterQuery = `
        SELECT COUNT(*) as total_records
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      `;
      const [noFilterResult] = await this.connection.execute(noFilterQuery);
      console.log(`📊 No filters: ${noFilterResult[0].total_records} records`);

      // Test 2: Machine model filter (IPS)
      const modelFilterQuery = `
        SELECT COUNT(*) as total_records
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
          AND Machine_Name LIKE 'IPS%'
      `;
      const [modelFilterResult] = await this.connection.execute(modelFilterQuery);
      console.log(`🏭 Model filter (IPS): ${modelFilterResult[0].total_records} records`);

      // Test 3: Date filter
      const dateFilterQuery = `
        SELECT COUNT(*) as total_records
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day = CURDATE()
      `;
      const [dateFilterResult] = await this.connection.execute(dateFilterQuery);
      console.log(`📅 Today's date filter: ${dateFilterResult[0].total_records} records`);

      // Test 4: Combined filters
      const combinedFilterQuery = `
        SELECT COUNT(*) as total_records
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day = CURDATE()
          AND Machine_Name LIKE 'IPS%'
      `;
      const [combinedFilterResult] = await this.connection.execute(combinedFilterQuery);
      console.log(`🔗 Combined filters: ${combinedFilterResult[0].total_records} records`);

      return {
        noFilter: noFilterResult[0].total_records,
        modelFilter: modelFilterResult[0].total_records,
        dateFilter: dateFilterResult[0].total_records,
        combinedFilter: combinedFilterResult[0].total_records
      };
    } catch (error) {
      console.error('❌ Filter impact analysis failed:', error.message);
      return null;
    }
  }

  /**
   * Run all diagnostics
   */
  async runAllDiagnostics() {
    console.log('🔍 DASHBOARD DIAGNOSTICS STARTING...\n');
    
    const connected = await this.connect();
    if (!connected) return;

    try {
      const results = {
        trsFormat: await this.diagnoseTRSDataFormat(),
        machineDuplication: await this.diagnoseMachineDuplication(),
        dataAggregation: await this.diagnoseDataAggregation(),
        chartDataStructure: await this.diagnoseChartDataStructure(),
        filterImpact: await this.diagnoseFilterImpact()
      };

      console.log('\n🎯 === DIAGNOSTIC SUMMARY ===');
      
      // TRS Issues
      if (results.trsFormat?.valueRanges?.max <= 1) {
        console.log('❌ TRS FORMAT ISSUE: Values in 0-1 range, need percentage conversion');
      } else {
        console.log('✅ TRS FORMAT: Values correctly in percentage range');
      }

      // Machine Duplication Issues
      const ips01Issue = results.machineDuplication?.find(m => m.Machine_Name.includes('IPS01'));
      if (ips01Issue && ips01Issue.record_count > ips01Issue.unique_dates * ips01Issue.unique_shifts) {
        console.log('❌ MACHINE DUPLICATION: IPS01 has duplicate records in charts');
      } else {
        console.log('✅ MACHINE DUPLICATION: No obvious duplication issues');
      }

      // Filter Issues
      if (results.filterImpact) {
        console.log(`✅ FILTER IMPACT: Filters working (${results.filterImpact.noFilter} → ${results.filterImpact.modelFilter} records)`);
      }

      console.log('\n✅ DIAGNOSTICS COMPLETE');
      
      return results;
    } finally {
      await this.disconnect();
    }
  }
}

// Run diagnostics
const diagnostics = new DashboardDiagnostics();
diagnostics.runAllDiagnostics()
  .then(results => {
    console.log('\n🏁 Diagnostics finished successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Diagnostics failed:', error);
    process.exit(1);
  });
