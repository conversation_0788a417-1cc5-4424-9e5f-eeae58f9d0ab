/**
 * Utility functions for database operations
 */
import pool from '../db.js';

/**
 * Executes a database query with error handling
 * @param {string} query - SQL query to execute
 * @param {Array} params - Query parameters
 * @param {boolean} returnSingle - If true, returns the first result only
 * @returns {Promise<Object>} Query results or error object
 */
export const executeQuery = async (query, params = [], returnSingle = false) => {
  try {
    const [results] = await pool.execute(query, params);
    if (returnSingle && results.length > 0) {
      return { success: true, data: results[0] };
    }
    return { success: true, data: results };
  } catch (error) {
    console.error('Database error:', error);
    return { 
      success: false, 
      error: 'Database query failed', 
      details: process.env.NODE_ENV === 'development' ? error.message : undefined 
    };
  }
};

/**
 * Builds a complete SQL query with filters
 * @param {string} baseQuery - Base SQL query
 * @param {Array} conditions - Array of condition strings to add to WHERE clause
 * @param {Array} params - Array of parameters for the query
 * @param {string} groupBy - GROUP BY clause
 * @param {string} orderBy - ORDER BY clause
 * @param {number} limit - LIMIT clause
 * @returns {Object} Object containing the complete query and parameters
 */
export const buildQuery = (baseQuery, conditions = [], params = [], groupBy = '', orderBy = '', limit = 0) => {
  let query = baseQuery;
  
  // Add WHERE clause if there are conditions
  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }
  
  // Add GROUP BY clause if provided
  if (groupBy) {
    query += ` GROUP BY ${groupBy}`;
  }
  
  // Add ORDER BY clause if provided
  if (orderBy) {
    query += ` ORDER BY ${orderBy}`;
  }
  
  // Add LIMIT clause if provided
  if (limit > 0) {
    query += ` LIMIT ${limit}`;
  }
  
  return { query, params };
};

/**
 * Handles database errors and returns appropriate response
 * @param {Error} error - The error object
 * @param {Response} res - Express response object
 * @param {number} statusCode - HTTP status code to return (default: 500)
 * @returns {Response} Express response with error details
 */
export const handleDbError = (error, res, statusCode = 500) => {
  console.error('Database error:', error);
  return res.status(statusCode).json({
    success: false,
    error: 'Database query failed',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
};

/**
 * Checks if a table exists in the database
 * @param {string} tableName - Name of the table to check
 * @returns {Promise<boolean>} True if table exists, false otherwise
 */
export const tableExists = async (tableName) => {
  try {
    const [results] = await pool.execute(`SHOW TABLES LIKE ?`, [tableName]);
    return results.length > 0;
  } catch (error) {
    console.error(`Error checking if table ${tableName} exists:`, error);
    return false;
  }
};

/**
 * Gets the count of records in a table
 * @param {string} tableName - Name of the table
 * @param {string} condition - Optional WHERE condition
 * @param {Array} params - Parameters for the condition
 * @returns {Promise<number>} Number of records
 */
export const getRecordCount = async (tableName, condition = '', params = []) => {
  try {
    const query = `SELECT COUNT(*) as count FROM ${tableName}${condition ? ` WHERE ${condition}` : ''}`;
    const [results] = await pool.execute(query, params);
    return results[0].count;
  } catch (error) {
    console.error(`Error getting record count for ${tableName}:`, error);
    return 0;
  }
};
