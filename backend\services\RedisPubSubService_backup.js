/**
 * Redis Pub/Sub Service for Real-time WebSocket Optimization
 * Replaces polling mechanism with event-driven updates
 * 
 * Performance Targets:
 * - 70% reduction in WebSocket latency
 * - Event-driven updates instead of 3-second polling
 * - Efficient multi-client state synchronization
 */

import Redis from 'ioredis';
import redisConfig from '../config/redisConfig.js';

class RedisPubSubService {
  constructor() {
    this.publisher = null;
    this.subscriber = null;
    this.initialized = false;
    this.subscriptions = new Map();
    this.metrics = {
      messagesPublished: 0,
      messagesReceived: 0,
      activeSubscriptions: 0,
      avgPublishTime: 0,
      lastActivity: null
    };

    // Channel names for different data types
    this.CHANNELS = {
      MACHINE_DATA: 'machine:data:updates',
      MACHINE_ALERTS: 'machine:alerts',
      PRODUCTION_UPDATES: 'production:updates',
      SYSTEM_EVENTS: 'system:events',
      STATE_UPDATES: 'state:updates',
      USER_PRESENCE: 'user:presence'
    };
  }

  /**
   * Initialize Redis pub/sub connections with fallback support
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    console.log('🔄 Initializing Redis Pub/Sub Service with fallback support...');

    try {
      // Use the centralized Redis config with fallback
      await redisConfig.initialize();
      
      this.publisher = redisConfig.getPublisher();
      this.subscriber = redisConfig.getSubscriber();

      // Set up error handlers that don't crash the application
      this.publisher.on('error', (error) => {
        console.log('❌ Redis Publisher error:', error.message);
        // Don't set initialized to false - let fallback handle it
      });

      this.subscriber.on('error', (error) => {
        console.log('❌ Redis Subscriber error:', error.message);
        // Don't set initialized to false - let fallback handle it
      });

      // Set up connection event handlers
      this.publisher.on('connect', () => {
        console.log('✅ Redis Publisher connected');
      });

      this.subscriber.on('connect', () => {
        console.log('✅ Redis Subscriber connected');
      });

      this.initialized = true;
      this.metrics.lastActivity = Date.now();

      if (redisConfig.isInFallbackMode()) {
        console.log('✅ Redis Pub/Sub Service initialized with in-memory fallback');
      } else {
        console.log('✅ Redis Pub/Sub Service initialized with Redis connection');
      }

      return true;

    } catch (error) {
      console.error('❌ Redis Pub/Sub Service initialization failed:', error.message);
      console.log('⚠️ Continuing with fallback mode - pub/sub will use in-memory simulation');
      this.initialized = true; // Mark as initialized even in fallback mode
      return true;
    }
  }

        // Clean up failed connections
        if (this.publisher) {
          try {
            this.publisher.removeAllListeners();
            await this.publisher.disconnect();
          } catch (e) {}
          this.publisher = null;
        }
        if (this.subscriber) {
          try {
            this.subscriber.removeAllListeners();
            await this.subscriber.disconnect();
          } catch (e) {}
          this.subscriber = null;
        }

        if (attempt === maxRetries) {
          console.error('❌ Failed to initialize Redis Pub/Sub Service after all attempts');
          console.error('⚠️ Continuing without Redis - some features may be limited');
          return false; // Don't throw, allow server to continue
        }

        console.log(`⏳ Retrying Redis connection in ${retryDelay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    return false;
  }

  /**
   * Publish machine data updates
   */
  async publishMachineDataUpdate(machineData) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - skipping publish');
      return false;
    }

    try {
      const startTime = Date.now();
      
      const message = {
        type: 'MACHINE_DATA_UPDATE',
        timestamp: new Date().toISOString(),
        data: machineData,
        source: 'database_poll'
      };

      const published = await this.publisher.publish(
        this.CHANNELS.MACHINE_DATA,
        JSON.stringify(message)
      );

      const publishTime = Date.now() - startTime;
      this.updateMetrics('publish', publishTime);

      console.log(`📡 Published machine data update to ${published} subscribers (${publishTime}ms)`);
      return published > 0;

    } catch (error) {
      console.error('❌ Failed to publish machine data update:', error);
      return false;
    }
  }

  /**
   * Publish machine alerts
   */
  async publishMachineAlert(alertData) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - skipping alert publish');
      return false;
    }

    try {
      const message = {
        type: 'MACHINE_ALERT',
        timestamp: new Date().toISOString(),
        data: alertData,
        priority: alertData.severity || 'medium'
      };

      const published = await this.publisher.publish(
        this.CHANNELS.MACHINE_ALERTS,
        JSON.stringify(message)
      );

      console.log(`🚨 Published machine alert to ${published} subscribers`);
      return published > 0;

    } catch (error) {
      console.error('❌ Failed to publish machine alert:', error);
      return false;
    }
  }

  /**
   * Subscribe to machine data updates
   */
  async subscribeToMachineData(callback) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - cannot subscribe');
      return false;
    }

    try {
      const channel = this.CHANNELS.MACHINE_DATA;
      
      // Store the callback for this subscription
      this.subscriptions.set(channel, callback);

      // Subscribe to the channel
      await this.subscriber.subscribe(channel);

      // Set up message handler
      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            this.updateMetrics('receive');
            
            console.log(`📨 Received machine data update: ${parsedMessage.type}`);
            callback(parsedMessage);

          } catch (error) {
            console.error('❌ Failed to parse received message:', error);
          }
        }
      });

      this.metrics.activeSubscriptions++;
      console.log(`✅ Subscribed to machine data updates on channel: ${channel}`);
      return true;

    } catch (error) {
      console.error('❌ Failed to subscribe to machine data:', error);
      return false;
    }
  }

  /**
   * Subscribe to machine alerts
   */
  async subscribeToMachineAlerts(callback) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - cannot subscribe');
      return false;
    }

    try {
      const channel = this.CHANNELS.MACHINE_ALERTS;
      
      this.subscriptions.set(channel, callback);
      await this.subscriber.subscribe(channel);

      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            this.updateMetrics('receive');
            
            console.log(`🚨 Received machine alert: ${parsedMessage.data?.type || 'unknown'}`);
            callback(parsedMessage);

          } catch (error) {
            console.error('❌ Failed to parse alert message:', error);
          }
        }
      });

      this.metrics.activeSubscriptions++;
      console.log(`✅ Subscribed to machine alerts on channel: ${channel}`);
      return true;

    } catch (error) {
      console.error('❌ Failed to subscribe to machine alerts:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from a channel
   */
  async unsubscribe(channelName) {
    if (!this.initialized) {
      return false;
    }

    try {
      await this.subscriber.unsubscribe(channelName);
      this.subscriptions.delete(channelName);
      this.metrics.activeSubscriptions = Math.max(0, this.metrics.activeSubscriptions - 1);
      
      console.log(`✅ Unsubscribed from channel: ${channelName}`);
      return true;

    } catch (error) {
      console.error(`❌ Failed to unsubscribe from ${channelName}:`, error);
      return false;
    }
  }

  /**
   * Update performance metrics
   */
  updateMetrics(type, responseTime = 0) {
    this.metrics.lastActivity = new Date().toISOString();

    if (type === 'publish') {
      this.metrics.messagesPublished++;
      this.metrics.avgPublishTime = 
        (this.metrics.avgPublishTime * (this.metrics.messagesPublished - 1) + responseTime) / 
        this.metrics.messagesPublished;
    } else if (type === 'receive') {
      this.metrics.messagesReceived++;
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      initialized: this.initialized,
      activeChannels: Array.from(this.subscriptions.keys()),
      publisherConnected: this.publisher?.status === 'ready',
      subscriberConnected: this.subscriber?.status === 'ready'
    };
  }

  /**
   * Health check for pub/sub service
   */
  async healthCheck() {
    if (!this.initialized) {
      return { healthy: false, reason: 'Not initialized' };
    }

    try {
      // Test publisher
      await this.publisher.ping();
      
      // Test subscriber
      await this.subscriber.ping();

      return {
        healthy: true,
        publisher: this.publisher.status,
        subscriber: this.subscriber.status,
        activeSubscriptions: this.metrics.activeSubscriptions
      };

    } catch (error) {
      return {
        healthy: false,
        reason: error.message,
        publisher: this.publisher?.status || 'disconnected',
        subscriber: this.subscriber?.status || 'disconnected'
      };
    }
  }

  /**
   * Publish state update for multi-client synchronization
   */
  async publishStateUpdate(stateType, stateKey, stateData) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - skipping state update publish');
      return false;
    }

    try {
      const startTime = Date.now();

      const message = {
        type: 'STATE_UPDATE',
        stateType,
        stateKey,
        timestamp: new Date().toISOString(),
        data: stateData
      };

      // Publish to specific state channel
      const stateChannel = `state:${stateType}:${stateKey}`;
      const published = await this.publisher.publish(
        stateChannel,
        JSON.stringify(message)
      );

      // Also publish to general state updates channel
      const generalPublished = await this.publisher.publish(
        this.CHANNELS.STATE_UPDATES,
        JSON.stringify(message)
      );

      const publishTime = Date.now() - startTime;
      this.updateMetrics('publish', publishTime);

      console.log(`📡 Published state update to ${published + generalPublished} subscribers (${publishTime}ms)`);
      return (published + generalPublished) > 0;

    } catch (error) {
      console.error('❌ Failed to publish state update:', error);
      return false;
    }
  }

  /**
   * Subscribe to state updates with pattern matching
   */
  async subscribe(pattern, callback) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - cannot subscribe');
      return false;
    }

    try {
      // Store the callback for this subscription
      this.subscriptions.set(pattern, callback);

      // Use psubscribe for pattern matching
      await this.subscriber.psubscribe(pattern);

      // Set up pattern message handler
      this.subscriber.on('pmessage', (receivedPattern, channel, message) => {
        if (receivedPattern === pattern) {
          try {
            const parsedMessage = JSON.parse(message);
            this.updateMetrics('receive');

            console.log(`📨 Received state update on ${channel}: ${parsedMessage.type}`);
            callback(channel, parsedMessage);

          } catch (error) {
            console.error('❌ Failed to parse received state message:', error);
          }
        }
      });

      this.metrics.activeSubscriptions++;
      console.log(`✅ Subscribed to state updates with pattern: ${pattern}`);
      return true;

    } catch (error) {
      console.error('❌ Failed to subscribe to state updates:', error);
      return false;
    }
  }

  /**
   * Publish user presence update
   */
  async publishUserPresence(userId, status, sessionInfo) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - skipping presence publish');
      return false;
    }

    try {
      const message = {
        type: 'USER_PRESENCE',
        userId,
        status,
        sessionInfo,
        timestamp: new Date().toISOString()
      };

      const published = await this.publisher.publish(
        this.CHANNELS.USER_PRESENCE,
        JSON.stringify(message)
      );

      console.log(`👤 Published user presence update to ${published} subscribers`);
      return published > 0;

    } catch (error) {
      console.error('❌ Failed to publish user presence:', error);
      return false;
    }
  }

  /**
   * Subscribe to user presence updates
   */
  async subscribeToUserPresence(callback) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Pub/Sub not initialized - cannot subscribe');
      return false;
    }

    try {
      const channel = this.CHANNELS.USER_PRESENCE;

      // Store the callback for this subscription
      this.subscriptions.set(channel, callback);

      // Subscribe to the channel
      await this.subscriber.subscribe(channel);

      // Set up message handler
      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            this.updateMetrics('receive');

            console.log(`👤 Received user presence update: ${parsedMessage.userId} - ${parsedMessage.status}`);
            callback(parsedMessage);

          } catch (error) {
            console.error('❌ Failed to parse received presence message:', error);
          }
        }
      });

      this.metrics.activeSubscriptions++;
      console.log(`✅ Subscribed to user presence updates on channel: ${channel}`);
      return true;

    } catch (error) {
      console.error('❌ Failed to subscribe to user presence:', error);
      return false;
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🔄 Shutting down Redis Pub/Sub service...');

    try {
      // Unsubscribe from all channels
      for (const channel of this.subscriptions.keys()) {
        await this.unsubscribe(channel);
      }

      // Close connections
      if (this.publisher) {
        await this.publisher.quit();
      }

      if (this.subscriber) {
        await this.subscriber.quit();
      }

      this.initialized = false;
      console.log('✅ Redis Pub/Sub service shut down gracefully');

    } catch (error) {
      console.error('❌ Error during Redis Pub/Sub shutdown:', error);
    }
  }
}

// Create singleton instance
const redisPubSubService = new RedisPubSubService();

export default redisPubSubService;
