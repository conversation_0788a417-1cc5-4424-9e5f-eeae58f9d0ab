There is a JavaScript error occurring in the FilterPanel component that needs to be debugged and fixed:

**Error Details:**
- **File**: `frontend/src/Components/FilterPanel.jsx` (line 111)
- **Function**: `checkElasticsearchHealth` (line 109, column 52)
- **Error Type**: `TypeError: Cannot read properties of undefined (reading 'status')`
- **Pattern**: Error is occurring twice (duplicate calls)

**Required Actions:**
1. **Investigate the `checkElasticsearchHealth` function** in `FilterPanel.jsx` around line 109
2. **Identify the undefined object** that should have a `status` property
3. **Add proper error handling** to prevent the TypeError when the response object is undefined
4. **Fix the root cause** - likely a failed API call or missing response validation
5. **Add defensive programming** to handle cases where Elasticsearch service is unavailable
6. **Prevent duplicate error logging** if the function is being called multiple times

**Context**: This appears to be related to Elasticsearch health checking functionality that was mentioned in our previous conversation about analytics platform assessment and Redis/Elasticsearch integration.

**Expected Outcome**: The FilterPanel should gracefully handle Elasticsearch unavailability without throwing JavaScript errors, and provide appropriate fallback behavior or user feedback. 