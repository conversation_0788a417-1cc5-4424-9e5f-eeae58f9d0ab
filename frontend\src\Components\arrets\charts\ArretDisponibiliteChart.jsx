import React, { memo } from 'react';
import { <PERSON>sponsive<PERSON><PERSON>r, <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Empty, Spin } from 'antd';

const CHART_COLORS = {
  primary: "#1890ff",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
};

const ArretDisponibiliteChart = memo(({ data = [], loading = false }) => {
  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }
  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée de disponibilité disponible" />
      </div>
    );
  }
  // Debug: Log raw data and processed data
  // Process data to ensure proper format
  const processedData = safeData.map(item => {
    let disponibilite = parseFloat(item.disponibilite || item.availability || 0);
    
    // If the value is between 0 and 1, it's likely a decimal (e.g., 0.85 for 85%)
    // Convert it to percentage for proper display
    if (disponibilite > 0 && disponibilite <= 1) {
      disponibilite = disponibilite * 100;
    }
    
    return {
      date: item.date || item.Stop_Date,
      disponibilite: disponibilite,
      mttr: parseFloat(item.mttr || 0),
      mtbf: parseFloat(item.mtbf || 0),
    };
  });

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={processedData} margin={{ top: 20, right: 20, left: 10, bottom: 30 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" strokeWidth={1} />
        <XAxis
          dataKey="date"
          tick={{ fill: "#666", fontSize: 11 }}
          height={30}
          tickFormatter={(date) => {
            try {
              return new Date(date).toLocaleDateString('fr-FR', { 
                day: '2-digit', 
                month: '2-digit' 
              });
            } catch {
              return date;
            }
          }}
          label={{            value: "Date",
            position: "bottom",
            offset: 0,
            style: { fill: "#666", fontSize: 12 },
          }}
        />        <YAxis
          label={{
            value: "Disponibilité (%)",
            angle: -90,
            position: "insideLeft",
            offset: 0,
            style: { fill: "#666", fontSize: 12 },
          }}
          tick={{ fill: "#666", fontSize: 11 }}
          domain={[0, 100]}
          width={40}
          tickCount={5}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          label={{
            value: "MTTR (min)",
            angle: 90,
            position: "insideRight",
            offset: 0,
            style: { fill: "#666", fontSize: 12 },
          }}
          width={40}
          tick={{ fill: "#666", fontSize: 11 }}
        />        <Tooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 4,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            fontSize: "12px"
          }}
          formatter={(value, name) => {
            if (name === 'disponibilite') return [`${value.toFixed(1)}%`, 'Disponibilité'];
            if (name === 'mttr') return [`${value.toFixed(1)} min`, 'MTTR'];
            if (name === 'mtbf') return [`${value.toFixed(1)} h`, 'MTBF'];
            return [value, name];
          }}
        />
        <Legend 
          verticalAlign="top"
          height={30}
          iconSize={10}
          iconType="circle"
          wrapperStyle={{
            paddingTop: "10px", 
            fontSize: "12px"
          }}
        />
        <Line
          type="monotone"
          dataKey="disponibilite"
          stroke={CHART_COLORS.success}
          strokeWidth={2}          dot={{ fill: CHART_COLORS.success, strokeWidth: 1, r: 3 }}
          activeDot={{ r: 5, fill: "#fff", stroke: CHART_COLORS.success, strokeWidth: 2 }}
          name="Disponibilité"
          animationDuration={1000}
        />
        <Line
          type="monotone"
          dataKey="mttr"
          stroke={CHART_COLORS.warning}
          strokeWidth={2}
          dot={{ fill: CHART_COLORS.warning, strokeWidth: 1, r: 3 }}
          activeDot={{ r: 5, fill: "#fff", stroke: CHART_COLORS.warning, strokeWidth: 2 }}
          name="MTTR"
          yAxisId="right"
          animationDuration={1000}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

ArretDisponibiliteChart.displayName = 'ArretDisponibiliteChart';

export default ArretDisponibiliteChart;
