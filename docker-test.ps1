# LOCQL Docker Test Script (PowerShell)
# This script tests the Docker setup and connectivity for Windows

Write-Host "🐳 LOCQL Docker Setup Test (Windows)" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Function to print colored status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
    }
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check if Docker is running
Write-Host "1. Checking Docker..." -ForegroundColor White
try {
    $dockerVersion = docker --version 2>$null
    $dockerPs = docker ps 2>$null
    if ($dockerVersion -and $dockerPs) {
        Write-Status $true "Docker is installed and running"
    } else {
        Write-Status $false "Docker is not running or accessible"
    }
} catch {
    Write-Status $false "Docker is not installed or accessible"
}

# Check if Docker Compose is available
Write-Host "2. Checking Docker Compose..." -ForegroundColor White
try {
    $composeVersion = docker-compose --version 2>$null
    if ($composeVersion) {
        Write-Status $true "Docker Compose is available"
    } else {
        Write-Status $false "Docker Compose is not available"
    }
} catch {
    Write-Status $false "Docker Compose is not available"
}

# Check ngrok tunnel
Write-Host "3. Checking ngrok tunnel..." -ForegroundColor White
try {
    $response = Invoke-WebRequest -Uri "https://eternal-friendly-chigger.ngrok-free.app/api/health/ping" -TimeoutSec 5 -UseBasicParsing 2>$null
    if ($response.StatusCode -eq 200) {
        Write-Status $true "ngrok tunnel is active and accessible"
    } else {
        Write-Status $false "ngrok tunnel returned status code: $($response.StatusCode)"
    }
} catch {
    Write-Warning "ngrok tunnel is not accessible. Please ensure ngrok is running."
}

# Check MySQL connectivity
Write-Host "4. Checking MySQL connectivity..." -ForegroundColor White
try {
    $mysqlPath = Get-Command mysql -ErrorAction SilentlyContinue
    if ($mysqlPath) {
        $mysqlTest = mysql -h localhost -u root -proot -e "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status $true "MySQL is accessible with configured credentials"
        } else {
            Write-Status $false "MySQL connection failed"
        }
    } else {
        Write-Warning "MySQL client not found. Cannot test database connectivity."
    }
} catch {
    Write-Warning "MySQL client not found. Cannot test database connectivity."
}

# Check port availability
Write-Host "5. Checking port availability..." -ForegroundColor White

# Check port 5000 (backend)
$port5000 = Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue
if (-not $port5000) {
    Write-Status $true "Port 5000 (backend) is available"
} else {
    Write-Warning "Port 5000 is already in use"
}

# Check port 5173 (frontend)
$port5173 = Get-NetTCPConnection -LocalPort 5173 -ErrorAction SilentlyContinue
if (-not $port5173) {
    Write-Status $true "Port 5173 (frontend) is available"
} else {
    Write-Warning "Port 5173 is already in use"
}

# Check if required files exist
Write-Host "6. Checking Docker configuration files..." -ForegroundColor White

$requiredFiles = @(
    "docker-compose.app.yml",
    "docker.env",
    "backend/Dockerfile",
    "frontend/Dockerfile"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Status $true "$file exists"
    } else {
        Write-Status $false "$file is missing"
    }
}

Write-Host ""
Write-Info "Test completed. If all checks passed, you can run:"
Write-Info "docker-compose -f docker-compose.app.yml up --build"

Write-Host ""
Write-Info "To monitor the application:"
Write-Info "- Frontend (Local): http://localhost:5173"
Write-Info "- Backend API (Local): http://localhost:5000"
Write-Info "- External Access (ngrok): https://eternal-friendly-chigger.ngrok-free.app"
Write-Info "- Health Check: http://localhost:5000/api/health/ping"
Write-Info "- WebSocket (ngrok): wss://charming-hermit-intense.ngrok-free.app/api/machine-data-ws"

Write-Host ""
Write-Info "To run this test script: .\docker-test.ps1"
Write-Info "To start containers: docker-compose -f docker-compose.app.yml up --build"
