#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Comprehensive Multi-Client State Management Test Script
    
.DESCRIPTION
    Tests the complete multi-client state management system including:
    - Real-time WebSocket synchronization
    - Sub-500ms latency verification
    - Network disconnection/reconnection handling
    - Conflict resolution for concurrent modifications
    - Backward compatibility with REST APIs
    - Cross-device consistency
    
.PARAMETER TestMode
    Specify test mode: 'full', 'performance', 'connectivity', 'compatibility'
    
.PARAMETER Verbose
    Enable verbose output for detailed test information
    
.EXAMPLE
    .\test-multi-client-state-management.ps1 -TestMode full -VerboseOutput

.EXAMPLE
    .\test-multi-client-state-management.ps1 -TestMode performance
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet('full', 'performance', 'connectivity', 'compatibility')]
    [string]$TestMode = 'full',

    [Parameter(Mandatory=$false)]
    [switch]$VerboseOutput
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Colors for output
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Header = "Magenta"
}

# Test configuration
$Config = @{
    ServerURL = "http://localhost:5000"
    WebSocketURL = "ws://localhost:5000/api/state-sync-ws"
    TestTimeout = 30
    MaxRetries = 3
    ExpectedLatency = 500
}

# Test results tracking
$TestResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    Errors = @()
    StartTime = Get-Date
    PerformanceMetrics = @{}
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White",
        [switch]$NoNewline
    )
    
    if ($NoNewline) {
        Write-Host $Message -ForegroundColor $Color -NoNewline
    } else {
        Write-Host $Message -ForegroundColor $Color
    }
}

function Write-TestHeader {
    param([string]$Title)
    
    Write-ColorOutput "`n$('=' * 60)" -Color $Colors.Header
    Write-ColorOutput "🧪 $Title" -Color $Colors.Header
    Write-ColorOutput "$('=' * 60)" -Color $Colors.Header
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Details = ""
    )
    
    $TestResults.TotalTests++
    
    if ($Success) {
        $TestResults.PassedTests++
        Write-ColorOutput "✅ $TestName - PASSED" -Color $Colors.Success
    } else {
        $TestResults.FailedTests++
        Write-ColorOutput "❌ $TestName - FAILED" -Color $Colors.Error
        if ($Details) {
            $TestResults.Errors += "$TestName`: $Details"
        }
    }
    
    if ($Details -and $VerboseOutput) {
        Write-ColorOutput "   Details: $Details" -Color $Colors.Info
    }
}

function Test-ServerConnectivity {
    Write-ColorOutput "`n🔍 Testing server connectivity..." -Color $Colors.Info
    
    try {
        $response = Invoke-RestMethod -Uri "$($Config.ServerURL)/api/health" -Method GET -TimeoutSec 10
        Write-TestResult "Server Health Check" $true "Server is responding"
        return $true
    } catch {
        Write-TestResult "Server Health Check" $false "Server not responding: $($_.Exception.Message)"
        return $false
    }
}

function Test-RedisConnectivity {
    Write-ColorOutput "`n🔍 Testing Redis connectivity..." -Color $Colors.Info
    
    try {
        $response = Invoke-RestMethod -Uri "$($Config.ServerURL)/api/health/redis" -Method GET -TimeoutSec 10
        if ($response.redis -and $response.redis.status -eq "connected") {
            Write-TestResult "Redis Connectivity" $true "Redis is connected and operational"
            return $true
        } else {
            Write-TestResult "Redis Connectivity" $false "Redis status: $($response.redis.status)"
            return $false
        }
    } catch {
        Write-TestResult "Redis Connectivity" $false "Redis health check failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-WebSocketServer {
    Write-ColorOutput "`n🔍 Testing WebSocket server..." -Color $Colors.Info
    
    try {
        # Test WebSocket endpoint availability
        $response = Invoke-RestMethod -Uri "$($Config.ServerURL)/api/health/websocket" -Method GET -TimeoutSec 10
        Write-TestResult "WebSocket Server" $true "WebSocket server is running"
        return $true
    } catch {
        Write-TestResult "WebSocket Server" $false "WebSocket server check failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-MultiClientStateManagement {
    Write-ColorOutput "`n🔍 Running comprehensive multi-client state management tests..." -Color $Colors.Info
    
    try {
        # Change to backend directory and run the Node.js test script
        $originalLocation = Get-Location
        Set-Location "backend"
        
        Write-ColorOutput "Executing Node.js test script..." -Color $Colors.Info
        $testOutput = node scripts/testMultiClientStateManagement.js 2>&1
        
        # Parse test results from output
        $testPassed = $testOutput -match "ALL TESTS PASSED"
        $latencyMatch = $testOutput | Select-String "Average latency: ([\d.]+)ms"
        
        if ($latencyMatch) {
            $avgLatency = [double]$latencyMatch.Matches[0].Groups[1].Value
            $TestResults.PerformanceMetrics.AverageLatency = $avgLatency
            
            if ($avgLatency -lt $Config.ExpectedLatency) {
                Write-TestResult "Latency Performance" $true "Average latency: ${avgLatency}ms (< ${Config.ExpectedLatency}ms)"
            } else {
                Write-TestResult "Latency Performance" $false "Average latency: ${avgLatency}ms (>= ${Config.ExpectedLatency}ms)"
            }
        }
        
        if ($testPassed) {
            Write-TestResult "Multi-Client State Management" $true "All state management tests passed"
        } else {
            Write-TestResult "Multi-Client State Management" $false "Some state management tests failed"
        }
        
        if ($VerboseOutput) {
            Write-ColorOutput "`nDetailed test output:" -Color $Colors.Info
            $testOutput | ForEach-Object { Write-ColorOutput "  $_" -Color $Colors.Info }
        }
        
        return $testPassed
        
    } catch {
        Write-TestResult "Multi-Client State Management" $false "Test execution failed: $($_.Exception.Message)"
        return $false
    } finally {
        Set-Location $originalLocation
    }
}

function Test-RESTAPICompatibility {
    Write-ColorOutput "`n🔍 Testing REST API backward compatibility..." -Color $Colors.Info
    
    $endpoints = @(
        "/api/testing-chart-production",
        "/api/unique-dates-production", 
        "/api/sidecards-prod",
        "/api/machine-performance",
        "/api/hourly-trends"
    )
    
    $successCount = 0
    
    foreach ($endpoint in $endpoints) {
        try {
            $response = Invoke-RestMethod -Uri "$($Config.ServerURL)$endpoint" -Method GET -TimeoutSec 10
            $successCount++
            if ($VerboseOutput) {
                Write-ColorOutput "  ✅ $endpoint - OK" -Color $Colors.Success
            }
        } catch {
            if ($VerboseOutput) {
                Write-ColorOutput "  ❌ $endpoint - Failed: $($_.Exception.Message)" -Color $Colors.Error
            }
        }
    }
    
    $allPassed = $successCount -eq $endpoints.Count
    Write-TestResult "REST API Compatibility" $allPassed "$successCount/$($endpoints.Count) endpoints working"
    
    return $allPassed
}

function Test-GraphQLCompatibility {
    Write-ColorOutput "`n🔍 Testing GraphQL compatibility..." -Color $Colors.Info
    
    try {
        $query = @{
            query = "query { getAllMachineStops { id machine_name start_time } }"
        }
        
        $response = Invoke-RestMethod -Uri "$($Config.ServerURL)/api/graphql" -Method POST -Body ($query | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 10
        
        if ($response.data) {
            Write-TestResult "GraphQL Compatibility" $true "GraphQL queries working correctly"
            return $true
        } else {
            Write-TestResult "GraphQL Compatibility" $false "GraphQL returned no data"
            return $false
        }
    } catch {
        Write-TestResult "GraphQL Compatibility" $false "GraphQL test failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-PerformanceMetrics {
    Write-ColorOutput "`n🔍 Testing performance metrics..." -Color $Colors.Info
    
    $performanceTests = @()
    
    # Test Redis cache performance
    try {
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri "$($Config.ServerURL)/api/sidecards-prod" -Method GET -TimeoutSec 10
        $endTime = Get-Date
        $responseTime = ($endTime - $startTime).TotalMilliseconds
        
        $TestResults.PerformanceMetrics.RESTResponseTime = $responseTime
        
        if ($responseTime -lt 100) {
            Write-TestResult "REST API Response Time" $true "Response time: ${responseTime}ms (< 100ms)"
        } else {
            Write-TestResult "REST API Response Time" $false "Response time: ${responseTime}ms (>= 100ms)"
        }
    } catch {
        Write-TestResult "REST API Response Time" $false "Performance test failed: $($_.Exception.Message)"
    }
    
    # Test GraphQL performance
    try {
        $startTime = Get-Date
        $query = @{ query = "query { getAllMachineStops { id machine_name } }" }
        $response = Invoke-RestMethod -Uri "$($Config.ServerURL)/api/graphql" -Method POST -Body ($query | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 10
        $endTime = Get-Date
        $responseTime = ($endTime - $startTime).TotalMilliseconds
        
        $TestResults.PerformanceMetrics.GraphQLResponseTime = $responseTime
        
        if ($responseTime -lt 200) {
            Write-TestResult "GraphQL Response Time" $true "Response time: ${responseTime}ms (< 200ms)"
        } else {
            Write-TestResult "GraphQL Response Time" $false "Response time: ${responseTime}ms (>= 200ms)"
        }
    } catch {
        Write-TestResult "GraphQL Response Time" $false "GraphQL performance test failed: $($_.Exception.Message)"
    }
}

function Show-TestSummary {
    $endTime = Get-Date
    $totalTime = ($endTime - $TestResults.StartTime).TotalSeconds
    
    Write-TestHeader "TEST SUMMARY"
    
    Write-ColorOutput "📊 Test Results:" -Color $Colors.Info
    Write-ColorOutput "   Total Tests: $($TestResults.TotalTests)" -Color $Colors.Info
    Write-ColorOutput "   Passed: $($TestResults.PassedTests)" -Color $Colors.Success
    Write-ColorOutput "   Failed: $($TestResults.FailedTests)" -Color $Colors.Error
    
    $successRate = if ($TestResults.TotalTests -gt 0) { 
        [math]::Round(($TestResults.PassedTests / $TestResults.TotalTests) * 100, 1) 
    } else { 0 }
    Write-ColorOutput "   Success Rate: $successRate%" -Color $Colors.Info
    Write-ColorOutput "   Total Time: $([math]::Round($totalTime, 2))s" -Color $Colors.Info
    
    if ($TestResults.PerformanceMetrics.Count -gt 0) {
        Write-ColorOutput "`n📈 Performance Metrics:" -Color $Colors.Info
        foreach ($metric in $TestResults.PerformanceMetrics.GetEnumerator()) {
            Write-ColorOutput "   $($metric.Key): $([math]::Round($metric.Value, 2))ms" -Color $Colors.Info
        }
    }
    
    if ($TestResults.Errors.Count -gt 0) {
        Write-ColorOutput "`n❌ Errors:" -Color $Colors.Error
        for ($i = 0; $i -lt $TestResults.Errors.Count; $i++) {
            Write-ColorOutput "   $($i + 1). $($TestResults.Errors[$i])" -Color $Colors.Error
        }
    }
    
    # Overall result
    if ($TestResults.FailedTests -eq 0) {
        Write-ColorOutput "`n🎉 ALL TESTS PASSED - Multi-Client State Management is working correctly!" -Color $Colors.Success
        exit 0
    } else {
        Write-ColorOutput "`n⚠️ SOME TESTS FAILED - Please review the errors above" -Color $Colors.Warning
        exit 1
    }
}

# Main execution
try {
    Write-TestHeader "MULTI-CLIENT STATE MANAGEMENT TEST SUITE"
    Write-ColorOutput "Test Mode: $TestMode" -Color $Colors.Info
    Write-ColorOutput "Server URL: $($Config.ServerURL)" -Color $Colors.Info
    Write-ColorOutput "WebSocket URL: $($Config.WebSocketURL)" -Color $Colors.Info
    
    # Pre-flight checks
    Write-ColorOutput "`n🚀 Running pre-flight checks..." -Color $Colors.Info
    
    $serverOK = Test-ServerConnectivity
    if (-not $serverOK) {
        Write-ColorOutput "❌ Server connectivity failed - aborting tests" -Color $Colors.Error
        exit 1
    }
    
    $redisOK = Test-RedisConnectivity
    $wsOK = Test-WebSocketServer
    
    # Run tests based on mode
    switch ($TestMode) {
        'full' {
            Test-MultiClientStateManagement
            Test-RESTAPICompatibility
            Test-GraphQLCompatibility
            Test-PerformanceMetrics
        }
        'performance' {
            Test-PerformanceMetrics
            Test-MultiClientStateManagement
        }
        'connectivity' {
            Test-MultiClientStateManagement
        }
        'compatibility' {
            Test-RESTAPICompatibility
            Test-GraphQLCompatibility
        }
    }
    
} catch {
    Write-ColorOutput "❌ Test execution failed: $($_.Exception.Message)" -Color $Colors.Error
    exit 1
} finally {
    Show-TestSummary
}
