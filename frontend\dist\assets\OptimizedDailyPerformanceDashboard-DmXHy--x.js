import{u as Qe,r as h,a as Ge,s as we,R as e,C as U,S as b,T as Ae,B as xe,b as M,c as y,D as ue,d as ke,e as J,f as V,g as D,h as ct,M as dt,E as re,i as ve,F as qe,j as ut,A as Te,k as Fe,l as $e,P as mt,m as gt,n as G}from"./index-O2xm1U_Z.js";import{R as X}from"./ReloadOutlined-EeD9QNgc.js";import{R as me}from"./CloseCircleOutlined-CKZmJH6e.js";import{R as ce}from"./DashboardOutlined-CC3QTJ5W.js";import{P as Ie}from"./progress-CVvjjq5H.js";import{S as q}from"./index-Dc91-n-S.js";import{R as Ce}from"./ClockCircleOutlined-D-iaV6k8.js";import{R as ge}from"./CheckCircleOutlined-BmO9kYsr.js";import{R as Re}from"./InfoCircleOutlined-BSC7vzM8.js";import{R as _e}from"./SyncOutlined-Cf4x7e7l.js";import{R as pt}from"./WarningOutlined-BiatBBB-.js";import{L as ft,B as De}from"./index-D6Px96Y3.js";import{l as Et,g as he,a as be,b as ye,r as ht}from"./chart-config-D1XBSr-Y.js";import{R as ze}from"./LineChartOutlined-CdWMpnra.js";import{R as Ue}from"./HistoryOutlined-kCKI-dwE.js";import"./SSENotificationBell-DCstoitw.js";import{R as bt}from"./FilterOutlined-B3wNZQVB.js";import{R as yt}from"./FileTextOutlined-ZE845-EP.js";import"./dayjs.min-CgAD4wBe.js";import"./relativeTime-UhGToeYb.js";import"./fr-D5QIIXkH.js";import"./index-fv7kzFnJ.js";import"./BellOutlined-D8NZd8pV.js";import"./AlertOutlined-BsPkpdqt.js";import"./AppstoreOutlined-BlZTSyy5.js";import"./ToolOutlined-BRYzAkU2.js";class St{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectTimeout=null,this.pingInterval=null,this.connectionTimeout=null,this.defaultWsUrl=this.getOptimalWebSocketUrl(),this.listeners={initialData:[],update:[],sessionUpdate:[],connect:[],disconnect:[],error:[]},this._setupNetworkListeners()}getOptimalWebSocketUrl(){const n=window.location.origin,r=n.includes("localhost")||n.includes("127.0.0.1"),i=n.includes("ngrok-free.app")||n.includes("ngrok.io");return r?`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}`:i?`wss://${window.location.host}`:"wss://eternal-friendly-chigger.ngrok-free.app"}_setupNetworkListeners(){window.addEventListener("offline",()=>{console.warn("Browser went offline. WebSocket connections may be interrupted."),this._notifyListeners("error",{type:"network",message:"Network connection lost"})}),window.addEventListener("online",()=>{console.log("Browser back online. Checking WebSocket connection..."),this.socket&&(this.socket.readyState===WebSocket.CLOSED||this.socket.readyState===WebSocket.CLOSING)&&(console.log("Reconnecting WebSocket after network recovery..."),this.connect())})}connect(){if(this.socket){if(this.socket.readyState===WebSocket.OPEN){console.log("WebSocket already connected"),this._notifyListeners("connect");return}if(this.socket.readyState===WebSocket.CONNECTING){console.log("WebSocket already connecting");return}(this.socket.readyState===WebSocket.CLOSING||this.socket.readyState===WebSocket.CLOSED)&&(console.log("Cleaning up existing WebSocket before reconnecting"),this.socket=null)}this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null);let n=this.defaultWsUrl;console.log("🔌 Using WebSocket base URL:",n);const r=`${n}/api/machine-data-ws`;console.log(`Attempting WebSocket connection to ${r}`);try{this.socket=new WebSocket(r),this.connectionTimeout&&clearTimeout(this.connectionTimeout),this.connectionTimeout=setTimeout(()=>{if(this.socket&&this.socket.readyState!==WebSocket.OPEN){console.warn("WebSocket connection timeout - closing socket");try{console.log(`WebSocket state before timeout close: ${this.getState()}`),this.socket.close(),this.socket=null,this.isConnected=!1}catch(i){console.error("Error closing timed out socket:",i)}this._handleConnectionFailure("Connection timeout"),this._notifyListeners("error",{type:"timeout",message:"WebSocket connection timed out after 15 seconds"})}this.connectionTimeout=null},15e3),this.socket.onopen=()=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),console.log("WebSocket connection established successfully"),this.isConnected=!0,this.reconnectAttempts=0,this._notifyListeners("connect"),this.pingInterval=setInterval(()=>{this.socket&&this.socket.readyState===WebSocket.OPEN&&this.send({type:"ping"})},3e4)},this.socket.onmessage=i=>{try{const d=JSON.parse(i.data);if(d.type!=="ping"&&d.type!=="pong"&&console.log("WebSocket message received:",d.type),d.type==="pong")return;d.type&&this.listeners[d.type]&&this._notifyListeners(d.type,d)}catch(d){console.error("Error processing WebSocket message:",d,i.data)}},this.socket.onclose=i=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this._clearPingInterval(),this.isConnected=!1;const d=i.reason?` - ${i.reason}`:"";console.log(`WebSocket connection closed: Code ${i.code}${d}`),this._notifyListeners("disconnect",i),!i.wasClean&&document.visibilityState==="visible"&&i.code!==1e3&&i.code!==1001&&console.log("Unexpected connection close. Will attempt to reconnect if needed.")},this.socket.onerror=i=>{console.error("WebSocket error occurred:",i),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this.isConnected=!1,this._notifyListeners("error",i),console.log("WebSocket error - application will handle reconnection if needed")}}catch(i){console.error("Error creating WebSocket connection:",i),this._notifyListeners("error",i),this._handleConnectionFailure("Failed to create WebSocket")}}_handleConnectionFailure(n){this.isConnected=!1,console.log(`Connection failed: ${n}. Application will handle reconnection if needed.`)}_clearPingInterval(){this.pingInterval&&(clearInterval(this.pingInterval),this.pingInterval=null)}disconnect(){if(this._clearPingInterval(),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket)try{this.socket.readyState!==WebSocket.CLOSED&&this.socket.readyState!==WebSocket.CLOSING&&this.socket.close(1e3,"Disconnected by user")}catch(n){console.error("Error closing WebSocket:",n)}finally{this.socket=null}this.isConnected=!1,console.log("WebSocket disconnected")}send(n){if(!this.socket)return console.warn("Cannot send message, WebSocket instance does not exist"),!1;switch(this.socket.readyState){case WebSocket.CONNECTING:return console.warn("Cannot send message, WebSocket is still connecting"),!1;case WebSocket.OPEN:try{const r=typeof n=="string"?n:JSON.stringify(n);return this.socket.send(r),!0}catch(r){return console.error("Error sending WebSocket message:",r),!1}case WebSocket.CLOSING:return console.warn("Cannot send message, WebSocket is closing"),!1;case WebSocket.CLOSED:return console.warn("Cannot send message, WebSocket is closed"),this.reconnectAttempts<this.maxReconnectAttempts&&(console.log("Attempting to reconnect..."),this.connect()),!1;default:return console.error("Unknown WebSocket state:",this.socket.readyState),!1}}requestUpdate(){return this.ensureConnection(),this.send({type:"requestUpdate"})}ensureConnection(){return navigator.onLine===!1?(console.warn("Cannot ensure connection - browser reports offline status"),!1):this.socket&&this.socket.readyState===WebSocket.OPEN?(console.log("WebSocket already connected"),!0):this.socket&&this.socket.readyState===WebSocket.CONNECTING?(console.log("WebSocket is currently connecting..."),!1):!this.socket||this.socket.readyState===WebSocket.CLOSED||this.socket.readyState===WebSocket.CLOSING?(console.log("WebSocket not connected, attempting to connect..."),this.connect(),!1):!0}getState(){if(!this.socket)return"DISCONNECTED";switch(this.socket.readyState){case WebSocket.CONNECTING:return"CONNECTING";case WebSocket.OPEN:return"CONNECTED";case WebSocket.CLOSING:return"CLOSING";case WebSocket.CLOSED:return"DISCONNECTED";default:return"UNKNOWN"}}addEventListener(n,r){return this.listeners[n]?this.listeners[n].push(r):console.warn(`Unknown event type: ${n}`),()=>this.removeEventListener(n,r)}removeEventListener(n,r){this.listeners[n]&&(this.listeners[n]=this.listeners[n].filter(i=>i!==r))}_notifyListeners(n,r){this.listeners[n]&&this.listeners[n].forEach(i=>{try{i(r)}catch(d){console.error(`Error in ${n} listener:`,d)}})}_scheduleReconnect(){this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null);const n=1e3*Math.pow(2,this.reconnectAttempts),r=Math.random()*1e3,i=Math.min(n+r,3e4);console.log(`Scheduling reconnect attempt ${this.reconnectAttempts+1}/${this.maxReconnectAttempts} in ${Math.round(i)}ms`),this.reconnectTimeout=setTimeout(()=>{if(this.reconnectAttempts++,console.log(`Executing reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`),navigator.onLine===!1){console.warn("Browser reports network is offline. Waiting for online status...");const d=()=>{console.log("Network is back online. Attempting to reconnect..."),window.removeEventListener("online",d),this.connect()};window.addEventListener("online",d),this._scheduleReconnect()}else this.connect()},i)}isOnline(){return navigator.onLine!==!1}setDefaultUrl(n){if(n)return!n.startsWith("ws:")&&!n.startsWith("wss:")&&(n=`wss://${n}`),this.defaultWsUrl=n,console.log(`WebSocket default URL set to: ${n}`),this.defaultWsUrl}}const _=new St,Ne=t=>{const n=parseFloat(t);return isNaN(n)?0:n},xt=()=>{const{isAuthenticated:t}=Qe(),n=h.useCallback((S,L)=>{const $=(()=>{if(typeof window<"u"){const W=window.location.origin;return W.includes("ngrok-free.app")||W.includes("ngrok.io")?W:"http://localhost:5000"}return"http://localhost:5000"})();return Ge[S](`${$}${L}`).retry(2).withCredentials().timeout(3e4)},[]),[r,i]=h.useState([]),[d,x]=h.useState([]),[u,l]=h.useState({}),[a,w]=h.useState([]),[p,O]=h.useState(null),[I,A]=h.useState([]),[se,B]=h.useState(!0),[H,c]=h.useState(null),[E,R]=h.useState(new Date),[F,s]=h.useState(!1),[g,T]=h.useState(null),[o,m]=h.useState(!1),[k,N]=h.useState({connected:!1,connecting:!1,updating:!1,reconnecting:!1}),[Ve,ae]=h.useState({}),[Ye,Ke]=h.useState([]),[Ze,Je]=h.useState(null),[Xe,He]=h.useState(new Date),[et,tt]=h.useState("all"),[nt,ot]=h.useState("machines"),ie=h.useCallback(S=>S.map(L=>{const $=Ne(L.TRS||"0"),W=$>80?"success":$>60?"warning":"error",ee=Ne(L.Quantite_Bon||"0"),te=Ne(L.Quantite_Planifier||"0"),f=ee/(te||1)*100;return{...L,status:W,progress:f}}),[]),pe=h.useCallback(async(S,L)=>{try{if(!t){console.log("🔐 Skipping machine sessions update - user not authenticated");return}const W=(await n("get","/api/activeSessions")).body,ee={};W.forEach(f=>{ee[f.machine_id]=f});const te={};L.forEach(f=>{f.id&&(te[f.id]=f)});for(const f of S){if(!f.id)continue;const fe={...f,Regleur_Prenom:f.Regleur_Prenom||"0",Quantite_Planifier:f.Quantite_Planifier||"0",Quantite_Bon:f.Quantite_Bon||"0",Quantite_Rejet:f.Quantite_Rejet||"0",TRS:f.TRS||"0",Poid_unitaire:f.Poid_unitaire||"0",cycle_theorique:f.cycle_theorique||"0",empreint:f.empreint||"0",Etat:f.Etat||"off",Code_arret:f.Code_arret||""},de=!!ee[f.id];f.Etat==="on"&&!de?(await n("post","/api/createSession").send({machineId:f.id,machineData:fe}),ae(ne=>({...ne,[f.id]:{active:!0,startTime:new Date,lastUpdate:new Date}})),console.log(`New session started for ${f.Machine_Name}`)):f.Etat==="on"&&de?(await n("post","/api/updateSession").send({machineId:f.id,machineData:fe}),ae(ne=>({...ne,[f.id]:{...ne[f.id],lastUpdate:new Date}}))):f.Etat==="off"&&de&&(await n("post","/api/stopSession").send({machineId:f.id}),ae(ne=>{const Ee={...ne};return delete Ee[f.id],Ee}),console.log(`Session ended for ${f.Machine_Name}`))}}catch($){console.error("Error handling machine sessions:",$)}},[t,n]),rt=h.useCallback(async()=>{try{if(!t){console.log("🔐 Skipping data fetch - user not authenticated"),B(!1),c("Authentication required");return}B(!0);const S=await Promise.all([n("get","/api/RealTimeTable"),n("get","/api/MachineCard"),n("get","/api/sidecards"),n("get","/api/dailyStats")]),[L,$,W,ee]=S;x([...r]);const te=ie($.body);i(te),l(W.body[0]||{}),w(ee.body),c(null),B(!1),R(new Date),await pe(te,r)}catch(S){console.error("Error fetching data:",S),c(S.message||"Failed to fetch data"),B(!1),R(new Date)}},[t,n,r,ie,pe]),st=h.useCallback(async S=>{if(S)try{if(!t){console.log("🔐 Skipping machine history fetch - user not authenticated"),T("Authentication required");return}s(!0),T(null);const L=await n("get",`/api/machineSessions/${S}`);if(Array.isArray(L.body)&&L.body.length>0){const $=L.body.map(W=>({...W,timestamp:new Date(W.session_start),isActive:!W.session_end,highlight:!W.session_end}));A($)}else console.log("No sessions found for this machine"),A([]),T("No sessions found for this machine");s(!1)}catch(L){console.error("Error fetching machine history:",L),T("No history data available for this machine"),s(!1),A([])}},[t,n]),at=h.useCallback(()=>{k.connected?(_.requestUpdate(),console.log("Requesting data update from server")):k.connecting||k.reconnecting?console.log("Connection already in progress"):(N(S=>({...S,connecting:!0})),we.info({message:"Reconnecting",description:"Attempting to establish WebSocket connection",icon:e.createElement(X,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-reconnecting"}),_.connect())},[k.connected,k.connecting,k.reconnecting]),Pe=h.useCallback(async()=>{try{if(!t){console.log("🔐 Skipping operator stats fetch - user not authenticated");return}const S=await n("get","/api/operator-stats");Ke(S.body)}catch(S){console.error("Error fetching operator stats:",S)}},[t,n]),Oe=h.useCallback(async()=>{try{if(!t){console.log("🔐 Skipping production stats fetch - user not authenticated");return}const S=await n("get","/api/production-stats");Je(S.body)}catch(S){console.error("Error fetching production stats:",S)}},[t,n]);return h.useEffect(()=>{_.isConnected?(N(C=>({...C,connected:!0,connecting:!1})),_.requestUpdate()):(N(C=>({...C,connecting:!0})),_.connect());const S=C=>{console.log("Received initial data from WebSocket"),N(P=>({...P,connecting:!1,updating:!1}));const z=ie(C.machineData),Y={};C.activeSessions.forEach(P=>{Y[P.machine_id]={active:!0,startTime:new Date(P.session_start),lastUpdate:new Date(P.last_updated),sessionId:P.id}}),ae(Y),i(z),x([...z]),l(C.sideCardData||{}),w(C.dailyStats||[]),c(null),B(!1),R(new Date),console.log("Initial data loaded successfully")},L=C=>{console.log("Received update from WebSocket",C),N(oe=>({...oe,updating:!0})),setTimeout(()=>{N(oe=>({...oe,updating:!1}))},500),x([...r]);const z=C.data.changedMachines||[],Y=C.data.fullData||[],P=ie(Y);i(P),R(new Date),pe(P,r),console.log(`${z.length} machine(s) updated`)},$=C=>{console.log("Received session update from WebSocket",C);const{sessionData:z,updateType:Y}=C,P=z.machine_id;Y==="created"||Y==="updated"?(ae(oe=>({...oe,[P]:{active:!0,startTime:new Date(z.session_start),lastUpdate:new Date(z.last_updated),sessionId:z.id}})),console.log(`Session ${Y} for ${z.Machine_Name||"machine "+P}`)):Y==="stopped"&&(ae(oe=>{const Be={...oe};return delete Be[P],Be}),console.log(`Session stopped for ${z.Machine_Name||"machine "+P}`))},W=()=>{console.log("WebSocket connected"),N(C=>({...C,connected:!0,connecting:!1})),_.requestUpdate(),console.log("WebSocket connection established successfully")},ee=()=>{console.log("WebSocket disconnected"),N(C=>({...C,connected:!1,connecting:!1})),document.visibilityState==="visible"&&we.warning({message:"Connexion perdue",description:"La connexion WebSocket a été interrompue",icon:e.createElement(X,{style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-disconnected"}),console.log("WebSocket disconnected - No automatic reconnection")},te=C=>{console.error("WebSocket error:",C),N(z=>({...z,connected:!1,connecting:!1})),we.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:e.createElement(me,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),c("Erreur de connexion WebSocket"),console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)")},f=_.addEventListener("initialData",S),fe=_.addEventListener("update",L),de=_.addEventListener("sessionUpdate",$),ne=_.addEventListener("connect",W),Ee=_.addEventListener("disconnect",ee),it=_.addEventListener("error",te);console.log("Establishing WebSocket connection...");const lt=setTimeout(()=>{_.isConnected||(console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)"),N(C=>({...C,connecting:!1})),console.log("WebSocket connection timeout - still attempting to connect"))},1e4);return()=>{clearTimeout(lt),f(),fe(),de(),ne(),Ee(),it(),console.log("useDashboardData unmounting - keeping WebSocket connection alive"),window.addEventListener("beforeunload",()=>{console.log("Page unloading, disconnecting WebSocket"),_.disconnect()},{once:!0})}},[ie,pe,rt,r]),h.useEffect(()=>{Pe(),Oe()},[Pe,Oe]),{machineData:r,previousMachineData:d,sideCardData:u,dailyStats:a,selectedMachine:p,machineHistory:I,operatorStats:Ye,productionStats:Ze,sessionStatus:Ve,loading:se,error:H,lastUpdate:E,historyLoading:F,historyError:g,wsStatus:k,isHistoricalView:o,selectedDate:Xe,selectedShift:et,selectedView:nt,setSelectedMachine:O,fetchMachineHistory:st,handleRefresh:at,setSelectedDate:He,setSelectedShift:tt,setSelectedView:ot,formatMachineData:ie}},{Title:kt,Text:Se}=Ae,v={primary:b.PRIMARY_BLUE,primaryLight:b.SELECTED_BG,success:b.SUCCESS,warning:b.WARNING,error:b.ERROR,gray:b.LIGHT_GRAY,textSecondary:b.LIGHT_GRAY,bgLight:b.LIGHT_BLUE_BG},Z=t=>{if(t==null||t==="")return 0;const n=String(t).replace(/,/g,"."),r=Number.parseFloat(n);return isNaN(r)?0:r},Ct=(t,n=1)=>t==null||t===""?"0":Z(t).toFixed(n).replace(/\./g,","),Rt=t=>t==null||t===""?"0":Z(t).toLocaleString("en-US").replace(/,/g,"."),le=({title:t,value:n,suffix:r="",color:i="inherit",style:d={},useDecimalComma:x=!1,useThousandComma:u=!1})=>{let l=n;return x?l=Ct(n):u&&(l=Rt(n)),e.createElement(U,{size:"small",style:{background:v.bgLight,borderRadius:"8px",height:"100%",padding:"12px",...d}},e.createElement("div",{style:{fontSize:"12px",marginBottom:"4px",color:v.textSecondary}},t),e.createElement("div",{style:{fontSize:"14px",fontWeight:"bold",color:i}},l," ",r))},vt=({machine:t,handleMachineClick:n,getStatusColor:r})=>{const i=u=>u>90?v.success:u>75?v.warning:v.error,d=Z(t.TRS||"0").toFixed(1).replace(".",","),x=(Z(t.Quantite_Bon)||0)/(Z(t.Quantite_Planifier)||1)*100;return e.createElement(U,{hoverable:!0,onClick:()=>n(t),className:"machine-card",style:{borderRadius:"12px",overflow:"hidden",height:"100%",transition:"all 0.3s",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",position:"relative"}},e.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,height:"6px",background:t.id?r(t.status,t):v.gray}}),e.createElement("div",{style:{padding:"16px 16px 0",marginTop:"6px"}},e.createElement("div",{style:{display:"flex",alignItems:"flex-start",marginBottom:"16px"}},e.createElement("div",{style:{width:"48px",height:"48px",borderRadius:"12px",background:v.primaryLight,display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"}},e.createElement(ce,{style:{fontSize:"24px",color:v.primary}})),e.createElement("div",{style:{flex:1}},e.createElement(kt,{level:4,style:{margin:0,fontSize:"18px"}},t.Machine_Name||"IPS01"),e.createElement(Se,{type:"secondary",style:{fontSize:"14px"}},"Chef de poste: ",t.Regleur_Prenom||"Non assigné"),t.Etat==="off"&&t.Code_arret&&e.createElement(Se,{type:"danger",style:{fontSize:"16px",display:"block",marginTop:"4px"}},t.Code_arret)),e.createElement("div",{style:{textAlign:"right",display:"flex",flexDirection:"column",alignItems:"flex-end"}},t.Etat==="on"&&e.createElement(xe,{color:"#1890ff",text:e.createElement("span",{style:{color:"#1890ff",fontWeight:500}},"Session active"),style:{fontSize:"12px",marginBottom:"8px"}}),e.createElement("div",{style:{marginTop:t.Etat==="on"?"0":"12px"}},e.createElement("div",{style:{width:"80px",height:"80px",position:"relative"}},e.createElement(Ie,{type:"circle",percent:Number.parseFloat(d),width:80,strokeColor:i(Number.parseFloat(d)),strokeWidth:8,format:()=>e.createElement("div",null,e.createElement("div",{style:{fontSize:"18px",fontWeight:"bold"}},d,"%"),e.createElement("div",{style:{fontSize:"12px",marginTop:"-2px"}},"TRS"))}))))),e.createElement(M,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(y,{span:8},e.createElement(le,{title:"Ordre de fabrication",value:t.Ordre_Fabrication||"N/A",style:{textAlign:"center"}})),e.createElement(y,{span:8},e.createElement(le,{title:"Article",value:t.Article||"N/A",style:{textAlign:"center"}})),e.createElement(y,{span:8},e.createElement(le,{title:"Empreintes",value:t.empreint+"/"+t.empreint||"N/A",style:{textAlign:"center"}}))),e.createElement(M,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(y,{span:8},e.createElement(le,{title:"Rejet",value:t.Quantite_Rejet||"0",suffix:"kg",style:{textAlign:"center"},useDecimalComma:!0})),e.createElement(y,{span:8},e.createElement(le,{title:"Purge",value:t.Poids_Purge||"0",suffix:"Kg",style:{textAlign:"center"},useDecimalComma:!0})),e.createElement(y,{span:8},e.createElement(le,{title:"Temps de cycle",value:Z(t.cycle||"0").toFixed(2).replace(".",",")+"/"+Z(t.cycle_theorique||"0").toFixed(2).replace(".",","),suffix:"sec",style:{textAlign:"center"}}))),e.createElement("div",{style:{marginBottom:"16px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"4px"}},e.createElement(Se,{strong:!0},"Progression"),e.createElement(Se,{type:"secondary"},x.toFixed(1).replace(".",","),"% d'objectif")),e.createElement(Ie,{percent:x,strokeColor:i(x),showInfo:!1,strokeWidth:8,trailColor:"rgba(0,0,0,0.04)"})),e.createElement("div",{style:{marginTop:"16px"}},e.createElement(ue,{style:{margin:"16px 0"}}),e.createElement(M,{gutter:16},e.createElement(y,{span:8},e.createElement(q,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ce,{style:{marginRight:"4px",color:v.textSecondary}}),e.createElement("span",{style:{fontSize:"13px",color:v.textSecondary}},"Planifié")),value:Z(t.Quantite_Planifier)||0,formatter:u=>u.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold"}})),e.createElement(y,{span:8},e.createElement(q,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ge,{style:{marginRight:"4px",color:t.id?v.success:v.gray}}),e.createElement("span",{style:{fontSize:"13px",color:v.textSecondary}},"Bon")),value:Z(t.Quantite_Bon)||0,formatter:u=>u.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold",color:v.success}})),e.createElement(y,{span:8},e.createElement(q,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(me,{style:{marginRight:"4px",color:t.id?v.error:v.gray}}),e.createElement("span",{style:{fontSize:"13px",color:v.textSecondary}},"Rejet")),value:Math.trunc(t.Quantite_Rejet*1e3/t.Poid_unitaire||"0")||0,formatter:u=>u.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold",color:v.error}}))),e.createElement("div",{style:{marginBottom:"24px"}}))),e.createElement("div",{style:{position:"absolute",bottom:"12px",right:"12px",background:v.primaryLight,borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:u=>{u.stopPropagation(),n(t)}},e.createElement(Re,{style:{color:v.primary}})))},wt=({wsStatus:t,onRefresh:n})=>{const r=()=>t.connected?e.createElement(J,{title:"Data is being updated in real-time"},e.createElement(V,{className:"ws-status-tag",color:"success"},e.createElement(ge,null)," Real-time connected")):t.connecting?e.createElement(J,{title:"Attempting to establish real-time connection"},e.createElement(V,{className:"ws-status-tag",color:"processing"},e.createElement(_e,{spin:!0})," Connecting...")):t.reconnecting?e.createElement(J,{title:"Connection lost, attempting to reconnect"},e.createElement(V,{className:"ws-status-tag",color:"warning"},e.createElement(_e,{spin:!0})," Reconnecting...")):e.createElement(J,{title:"WebSocket connection lost - click Refresh to reconnect"},e.createElement(V,{className:"ws-status-tag",color:"error"},e.createElement(pt,null)," Disconnected")),i=()=>t.updating?e.createElement(J,{title:"Receiving new data from server"},e.createElement(V,{className:"ws-status-tag ws-status-updating",color:"processing"},e.createElement(_e,{spin:!0})," Updating")):null,d=()=>{const x=!t.connected&&t.connecting,u=t.updating;let l="Refresh data from server",a="Refresh Data";return x?l="Connection in progress...":u?l="Data is currently being updated":!t.connected&&!t.reconnecting?(l="Click to attempt WebSocket reconnection",a="Reconnect"):t.reconnecting&&(l="Reconnection in progress...",a="Reconnecting..."),e.createElement(J,{title:l},e.createElement(D,{type:!t.connected&&!t.connecting&&!t.reconnecting?"danger":"primary",icon:e.createElement(X,{spin:t.updating||t.reconnecting}),onClick:n,size:"small",loading:x,disabled:u||t.reconnecting},a))};return e.createElement(ke,null,r(),i(),d())};Et.throttle((t,n)=>{t&&t.data&&(t.data=n,t.update("none"))},500);const K=window.innerWidth<768,Tt=t=>{const n=t?b.DARK.BORDER:b.ACCENT_BORDER,r=t?b.DARK.TEXT_SECONDARY:b.LIGHT_GRAY,i=t?b.DARK.TEXT:b.DARK_GRAY,d=t?b.DARK.BACKGROUND:b.WHITE,x=t?b.DARK.BORDER:b.ACCENT_BORDER,u=[b.PRIMARY_BLUE,b.SECONDARY_BLUE,b.CHART_TERTIARY,b.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3"],l=t?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.1)";return{responsive:!0,maintainAspectRatio:!1,animation:!1,devicePixelRatio:1,plugins:{legend:{position:"top",align:"center",labels:{color:r,padding:15,usePointStyle:!0,pointStyle:"circle",boxWidth:10,font:{weight:500}},display:!K},tooltip:{enabled:!K||window.innerWidth>480,backgroundColor:d,titleColor:t?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",bodyColor:r,borderColor:x,borderWidth:1,padding:10,cornerRadius:6,boxPadding:5,displayColors:!0,boxShadow:l,callbacks:{label:function(a){let w=a.dataset.label||"";return w&&(w+=": "),w+=Math.round(a.parsed.y*100)/100,w},title:function(a){return a[0].label}}},title:{display:!1,color:i,font:{weight:600,size:16}}},scales:{x:{grid:{display:!1,color:n,z:-1},border:{color:t?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:r,maxRotation:0,autoSkipPadding:10,maxTicksLimit:K?5:10,padding:8,font:{size:K?10:12}},title:{display:!1,color:i,font:{weight:500}}},y:{beginAtZero:!0,grid:{color:n,z:-1,lineWidth:1,drawBorder:!0},border:{color:t?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:r,precision:0,maxTicksLimit:K?5:8,padding:8,font:{size:K?10:12}},title:{display:!1,color:i,font:{weight:500}}}},elements:{point:{radius:K?0:3,hoverRadius:K?3:6,backgroundColor:function(a){const w=a.datasetIndex%u.length;return u[w]},borderColor:t?"#141414":"white",borderWidth:2,hoverBorderWidth:2,hoverBorderColor:t?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)"},line:{borderWidth:K?2:3,tension:.2,fill:!1,borderColor:function(a){const w=a.datasetIndex%u.length;return u[w]},borderCapStyle:"round"},bar:{backgroundColor:function(a){const w=a.datasetIndex%u.length;return u[w]},borderWidth:0,borderRadius:4,hoverBackgroundColor:function(a){return a.datasetIndex%u.length,t?b.DARK.SECONDARY_BLUE:b.SECONDARY_BLUE}}}}},{Title:_t,Text:j}=Ae,{TabPane:Le}=ve,Dt=({visible:t,machine:n,machineHistory:r,loading:i,error:d,onClose:x,onRefresh:u,darkMode:l})=>{const{settings:a}=ct();if(!n)return null;const w=[{title:"Start Time",dataIndex:"session_start",key:"session_start",render:c=>new Date(c).toLocaleString(),sorter:(c,E)=>new Date(E.session_start)-new Date(c.session_start)},{title:"End Time",dataIndex:"session_end",key:"session_end",render:c=>c?new Date(c).toLocaleString():e.createElement(V,{color:"processing"},"Active")},{title:"Duration",key:"duration",render:(c,E)=>{const R=new Date(E.session_start),s=(E.session_end?new Date(E.session_end):new Date)-R,g=Math.floor(s/36e5),T=Math.floor(s%36e5/6e4);return`${g}h ${T}m`}},{title:"TRS",dataIndex:"TRS",key:"TRS",render:c=>{const E=parseFloat(c||0);let R="red";return E>80?R="green":E>60&&(R="orange"),e.createElement("span",{style:{color:R}},E.toFixed(1),"%")},sorter:(c,E)=>parseFloat(c.TRS||0)-parseFloat(E.TRS||0)},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(c,E)=>parseFloat(c.Quantite_Bon||0)-parseFloat(E.Quantite_Bon||0)},{title:"Rejects",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(c,E)=>parseFloat(c.Quantite_Rejet||0)-parseFloat(E.Quantite_Rejet||0)},{title:"Status",key:"status",render:(c,E)=>e.createElement(V,{color:E.session_end?"default":"processing"},E.session_end?"Completed":"Active")}],p=Tt(l),O=()=>{const c=new Date().getHours();return c>=6&&c<14?"morning":c>=14&&c<22?"afternoon":"night"},I=(c,E)=>{if(E==="all")return!0;const F=new Date(c.session_start).getHours();return E==="morning"&&F>=6&&F<14||E==="afternoon"&&F>=14&&F<22||E==="night"&&(F>=22||F<6)},A=c=>{if(c==null||c==="")return 0;const E=String(c).replace(/,/g,"."),R=Number.parseFloat(E);return isNaN(R)?0:R},se=(c,E=1)=>c==null||c===""?"0":A(c).toFixed(E).replace(/\./g,","),B=c=>c==null||c===""?"0":A(c).toLocaleString("en-US").replace(/,/g,"."),H=()=>{if(i)return e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement("div",{className:"ant-spin ant-spin-lg ant-spin-spinning"},e.createElement("span",{className:"ant-spin-dot ant-spin-dot-spin"},e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}),e.createElement("i",{className:"ant-spin-dot-item"}))),e.createElement("div",{style:{marginTop:16}},"Chargement de l'historique..."));if(d)return e.createElement(re,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Erreur de chargement: ",d),e.createElement(D,{type:"primary",icon:e.createElement(X,null),onClick:u},"Réessayer"))});if(!r||r.length===0)return e.createElement(re,{description:e.createElement(e.Fragment,null,e.createElement("p",null,"Aucune session trouvée pour cette machine"),e.createElement("p",null,"La table machine_sessions est vide ou aucune donnée n'est disponible"),e.createElement(D,{type:"primary",icon:e.createElement(X,null),onClick:u},"Rafraîchir")),image:re.PRESENTED_IMAGE_SIMPLE});const c={labels:r.map(s=>{const g=new Date(s.session_start);return g.toLocaleDateString()+" "+g.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"TRS (%)",data:r.map(s=>parseFloat(s.TRS)||0),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},E={labels:r.map(s=>{const g=new Date(s.session_start);return g.toLocaleDateString()+" "+g.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Good Production",data:r.map(s=>parseFloat(s.Quantite_Bon)||0),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},R={labels:r.map(s=>{const g=new Date(s.session_start);return g.toLocaleDateString()+" "+g.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Rejected Production",data:r.map(s=>parseFloat(s.Quantite_Rejet)||0),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},F={labels:r.map(s=>{const g=new Date(s.session_start);return g.toLocaleDateString()+" "+g.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),datasets:[{label:"Session Duration (min)",data:r.map(s=>{const g=new Date(s.session_start),T=s.session_end?new Date(s.session_end):new Date;return Math.round((T-g)/6e4)}),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return e.createElement(ve,{defaultActiveKey:"1",className:l?"dark-mode":""},e.createElement(Le,{tab:"Sessions",key:"1"},e.createElement(qe,{columns:w,dataSource:r.map((s,g)=>({...s,key:g})),pagination:{pageSize:5},scroll:{x:!0}})),e.createElement(Le,{tab:"Charts",key:"2"},e.createElement(M,{gutter:[16,16]},e.createElement(y,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(ze,null)," TRS (%)"),e.createElement("div",{style:{height:he(a)}},e.createElement(ft,{data:ye(c,a),options:be(a,"line",{...p,scales:{...p.scales,y:{...p.scales.y,beginAtZero:!0,max:100,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...p.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...p.plugins,legend:{...p.plugins.legend,labels:{...p.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}})})))),e.createElement(y,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(ge,null)," Production (pcs)"),e.createElement("div",{style:{height:he(a)}},e.createElement(De,{data:ye(E,a),options:be(a,"bar",{...p,scales:{...p.scales,y:{...p.scales.y,beginAtZero:!0,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...p.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...p.plugins,legend:{...p.plugins.legend,labels:{...p.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}})})))),e.createElement(y,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(me,null)," Rejects (pcs)"),e.createElement("div",{style:{height:he(a)}},e.createElement(De,{data:ye(R,a),options:be(a,"bar",{...p,scales:{...p.scales,y:{...p.scales.y,beginAtZero:!0,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...p.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...p.plugins,legend:{...p.plugins.legend,labels:{...p.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}})})))),e.createElement(y,{xs:24,md:12},e.createElement("div",{className:"chart-container"},e.createElement("h3",{className:"chart-title"},e.createElement(Ce,null)," Session Duration (min)"),e.createElement("div",{style:{height:he(a)}},e.createElement(De,{data:ye(F,a),options:be(a,"bar",{...p,scales:{...p.scales,y:{...p.scales.y,beginAtZero:!0,grid:{color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...p.scales.x,grid:{display:!1,color:l?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...p.plugins,legend:{...p.plugins.legend,labels:{...p.plugins.legend.labels,color:l?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}})})))))),e.createElement(Le,{tab:e.createElement("span",null,e.createElement(Re,{style:{marginRight:8}}),"Informations"),key:"3"},e.createElement("div",{style:{padding:"16px 0"}},e.createElement(M,{gutter:[24,24]},e.createElement(y,{xs:24,md:12},e.createElement(U,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ce,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Détails de la machine")),bordered:!0,style:{height:"100%"}},e.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:16}},e.createElement("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16}},e.createElement(ce,{style:{fontSize:32,color:"#1890ff"}})),e.createElement("div",null,e.createElement(_t,{level:4,style:{margin:0}},n.Machine_Name),e.createElement(j,{type:"secondary"},r.length>0&&r[0].Ordre_Fabrication?`OF : ${r[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"))),e.createElement(ue,{style:{margin:"16px 0"}}),e.createElement(M,{gutter:[16,16]},e.createElement(y,{span:12},e.createElement(q,{title:e.createElement(j,{style:{fontSize:14}},"Sessions ",O()==="morning"?"matin":O()==="afternoon"?"après-midi":"nuit"),value:B(r.filter(s=>I(s,O())).length),prefix:e.createElement(Ue,null),valueStyle:{color:"#1890ff",fontSize:20}})),e.createElement(y,{span:12},e.createElement(q,{title:e.createElement(j,{style:{fontSize:14}},"Sessions actives ",O()==="morning"?"matin":O()==="afternoon"?"après-midi":"nuit"),value:B(r.filter(s=>!s.session_end&&I(s,O())).length),prefix:e.createElement(Ce,null),valueStyle:{color:r.filter(s=>!s.session_end&&I(s,O())).length>0?"#52c41a":"#8c8c8c",fontSize:20}}))))),e.createElement(y,{xs:24,md:12},e.createElement(U,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(Ue,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Historique des sessions")),bordered:!0,style:{height:"100%"}},r.length>0?e.createElement(e.Fragment,null,e.createElement("div",{style:{marginBottom:16}},e.createElement(j,{strong:!0},"Dernière session :"),e.createElement("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8}},e.createElement(j,null,"Début :"),e.createElement(j,{strong:!0},r[0].session_start?new Date(r[0].session_start).toLocaleString("fr-FR"):"")),e.createElement("div",{style:{display:"flex",justifyContent:"space-between"}},e.createElement(j,null,"Fin :"),e.createElement(j,{strong:!0},r[0].session_end?new Date(r[0].session_end).toLocaleString("fr-FR"):e.createElement(V,{color:"processing"},"En cours"))))),e.createElement(ue,{style:{margin:"16px 0"}}),e.createElement(M,{gutter:[16,16]},e.createElement(y,{span:8},e.createElement(q,{title:e.createElement(j,{style:{fontSize:14}},"TRS moyen"),value:(()=>{const s=r.map(g=>A(g.TRS||0)).filter(g=>!isNaN(g));return s.length?se(s.reduce((g,T)=>g+T,0)/s.length):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})),e.createElement(y,{span:8},e.createElement(q,{title:e.createElement(j,{style:{fontSize:14}},"Bons produits"),value:B(r.reduce((s,g)=>s+A(g.Quantite_Bon||0),0)),valueStyle:{color:"#52c41a",fontSize:18}})),e.createElement(y,{span:8},e.createElement(q,{title:e.createElement(j,{style:{fontSize:14}},"Produits rejetés"),value:B(r.reduce((s,g)=>s+A(g.Quantite_Rejet||0),0)),valueStyle:{color:"#ff4d4f",fontSize:18}})))):e.createElement(re,{description:"Aucune donnée de session disponible",image:re.PRESENTED_IMAGE_SIMPLE}))),e.createElement(y,{xs:24},e.createElement(U,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ze,{style:{color:"#1890ff",marginRight:8}}),e.createElement("span",null,"Métriques de performance")),bordered:!0},r.length>0?e.createElement(M,{gutter:[24,24]},e.createElement(y,{xs:24,md:8},e.createElement(U,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(q,{title:"Durée moyenne des sessions",value:(()=>{const s=r.map(m=>{const k=new Date(m.session_start);return(m.session_end?new Date(m.session_end):new Date)-k}),g=s.reduce((m,k)=>m+k,0)/s.length,T=Math.floor(g/36e5),o=Math.floor(g%36e5/6e4);return`${T}h ${o}m`})(),prefix:e.createElement(Ce,null)}))),e.createElement(y,{xs:24,md:8},e.createElement(U,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(q,{title:"Taux de rejet moyen",value:(()=>{const s=r.reduce((T,o)=>T+A(o.Quantite_Bon||0),0),g=r.reduce((T,o)=>T+A(o.Quantite_Rejet||0),0);return s+g>0?se(g/(s+g)*100):"0,0"})(),suffix:"%",prefix:e.createElement(me,null),valueStyle:{color:"#ff4d4f"}}))),e.createElement(y,{xs:24,md:8},e.createElement(U,{style:{background:"rgba(0,0,0,0.02)"}},e.createElement(q,{title:"Production totale",value:B(r.reduce((s,g)=>s+A(g.Quantite_Bon||0),0)),prefix:e.createElement(ge,null),valueStyle:{color:"#52c41a"}})))):e.createElement(re,{description:"Aucune donnée de performance disponible",image:re.PRESENTED_IMAGE_SIMPLE})))))))};return e.createElement(dt,{title:`Sessions de ${n.Machine_Name}`,open:t,width:800,onCancel:x,footer:[e.createElement(D,{key:"close",onClick:x},"Fermer"),e.createElement(D,{key:"allSessions",type:"primary",onClick:()=>window.open("/sessions-report","_blank")},"Voir toutes les sessions")],destroyOnClose:!0},H())},{Title:Me,Text:We}=Ae,{TabPane:je}=ve,Q={primary:b.PRIMARY_BLUE,success:b.SUCCESS,warning:b.WARNING,error:b.ERROR,gray:b.LIGHT_GRAY},Nt=()=>{const{darkMode:t}=ut(),[n,r]=h.useState(!1),[i,d]=h.useState("machines"),x=o=>{let m="Connecté",k=Q.success,N=e.createElement(ge,null);return o.fallbackMode?(m="Mode de secours",k=Q.warning,N=e.createElement(Re,null)):o.reconnecting?(m="Reconnexion...",k=Q.warning,N=e.createElement(X,{spin:!0})):o.connecting?(m="Connexion...",k=Q.primary,N=e.createElement(X,{spin:!0})):o.connected||(m="Déconnecté",k=Q.error,N=e.createElement(me,null)),e.createElement("div",{style:{display:"flex",alignItems:"center",marginRight:"16px"}},e.createElement(xe,{status:o.connected?"success":o.fallbackMode?"warning":o.reconnecting?"processing":"error",text:e.createElement(J,{title:o.fallbackMode?"Utilisation des mises à jour périodiques au lieu de la connexion en temps réel":""},e.createElement("span",{style:{color:k,display:"flex",alignItems:"center"}},e.createElement("span",{style:{marginRight:"4px"}},N),m))}))},{machineData:u,selectedMachine:l,machineHistory:a,loading:w,error:p,lastUpdate:O,wsStatus:I,historyLoading:A,historyError:se,setSelectedMachine:B,handleRefresh:H,fetchMachineHistory:c}=xt(),E=()=>O.toLocaleTimeString(),R=o=>{if(!o.id){G.info("Cette machine n'est pas encore configurée");return}B(o),c(o.id),r(!0)},F=o=>{d(o.target.value)},s=(o,m)=>m&&m.id!==1?Q.gray:m&&m.Etat==="off"?Q.error:m&&m.Etat==="on"||o==="success"?Q.success:o==="warning"?Q.warning:Q.error,g=()=>w&&!I.connected&&!u.length?e.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},e.createElement(gt,{size:"large"}),e.createElement("div",{style:{marginTop:16}},"Chargement des données...")):p&&!u.length?e.createElement(Te,{message:"Erreur de connexion",description:e.createElement(e.Fragment,null,p,I.fallbackMode&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Mode de secours activé:")," Les données seront actualisées périodiquement au lieu des mises à jour en temps réel."),!I.fallbackMode&&e.createElement("div",{style:{marginTop:"10px"}},e.createElement("strong",null,"Dépannage:"),e.createElement("ul",null,e.createElement("li",null,"Vérifiez votre connexion réseau"),e.createElement("li",null,"Le serveur peut être temporairement indisponible"),e.createElement("li",null,"Essayez de rafraîchir la page")))),type:"error",showIcon:!0,action:e.createElement(D,{type:"primary",onClick:H},"Réessayer")}):!u||u.length===0?e.createElement(Te,{message:"Aucune donnée",description:"Aucune donnée de machine disponible",type:"info",showIcon:!0}):e.createElement(e.Fragment,null,e.createElement(M,{gutter:[16,16]},u.map(o=>e.createElement(y,{xs:24,sm:24,md:12,key:o.id||o.Machine_Name},e.createElement("div",{className:"machine-card-container",style:{position:"relative"}},e.createElement(vt,{machine:o,handleMachineClick:R,getStatusColor:s}),o.id!==1&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(D,{type:"primary",ghost:!0,size:"small",icon:e.createElement($e,null)},"Configuration requise")),!o.id&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(D,{type:"default",size:"small"},"Configuration requise"))))))),T=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",sorter:(o,m)=>o.Machine_Name.localeCompare(m.Machine_Name)},{title:"Statut",dataIndex:"Etat",key:"Etat",render:o=>e.createElement(V,{color:o==="on"?"success":"error"},o==="on"?"En ligne":"Hors ligne"),filters:[{text:"En ligne",value:"on"},{text:"Hors ligne",value:"off"}],onFilter:(o,m)=>m.Etat===o},{title:"TRS",dataIndex:"TRS",key:"TRS",sorter:(o,m)=>parseFloat(o.TRS||0)-parseFloat(m.TRS||0),render:o=>{const m=parseFloat(o||0);let k="red";return m>80?k="green":m>60&&(k="orange"),e.createElement("span",{style:{color:k}},m.toFixed(1),"%")}},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(o,m)=>parseFloat(o.Quantite_Bon||0)-parseFloat(m.Quantite_Bon||0)},{title:"Rejets",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(o,m)=>parseFloat(o.Quantite_Rejet||0)-parseFloat(m.Quantite_Rejet||0)},{title:"Opérateur",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:o=>o||"Non assigné"},{title:"Actions",key:"actions",render:(o,m)=>e.createElement(D,{type:"primary",size:"small",disabled:m.Etat==="off",onClick:k=>{k.stopPropagation(),m.Etat!=="off"&&R(m)},title:m.Etat==="off"?"Machine hors ligne. Détails non disponibles.":"Voir les détails de la machine"},"Détails")}];return e.createElement("div",{style:{padding:"24px"}},e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24}},e.createElement("div",null,e.createElement(Me,{level:2},e.createElement(ce,null)," Tableau de Bord"),e.createElement(We,{type:"secondary"},new Date().toLocaleDateString())),e.createElement(ke,null,x(I),e.createElement(D,{type:"primary",icon:e.createElement(X,null),onClick:H},"Actualiser"))),p&&e.createElement(Te,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${p} | Dernière mise à jour: ${E()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),e.createElement(ue,null),e.createElement("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement(Fe.Group,{value:i,onChange:F,buttonStyle:"solid"},e.createElement(Fe.Button,{value:"machines"},e.createElement(ce,null)," Machines")),e.createElement(ke,null,e.createElement(J,{title:"Filtrer les données"},e.createElement(D,{icon:e.createElement(bt,null)},"Filtres")),e.createElement(J,{title:"Exporter les données"},e.createElement(D,{icon:e.createElement(yt,null)},"Exporter")))),e.createElement(M,{gutter:[16,16],style:{marginBottom:"16px"}},e.createElement(y,{span:24},e.createElement(U,null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(We,{strong:!0,style:{marginRight:"10px"}},"Dernière mise à jour: ",E()),e.createElement(wt,{wsStatus:I,onRefresh:H})))))),i==="machines"&&e.createElement(M,{gutter:[18,18]},e.createElement(y,{xs:24,lg:24},e.createElement(U,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(ce,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Statistiques des machines")),extra:e.createElement(xe,{count:u.length,style:{backgroundColor:"#1890ff"}})},g()))),e.createElement(ue,null),e.createElement(U,{title:e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement($e,{style:{fontSize:20,marginRight:8}}),e.createElement("span",null,"Détails des machines")),extra:e.createElement(ke,null,e.createElement(D,{type:"primary",icon:e.createElement(X,null),onClick:H,size:"small"},"Actualiser"),e.createElement(V,{color:"processing"},u.filter(o=>o.Etat==="on").length," sessions actives"))},e.createElement(ve,{defaultActiveKey:"1",className:t?"dark-mode":""},e.createElement(je,{tab:"Tableau",key:"1"},e.createElement(qe,{columns:T,dataSource:u.map((o,m)=>({...o,key:m})),pagination:{pageSize:10},scroll:{x:!0},onRow:o=>({onClick:()=>o.Etat!=="off"&&R(o),style:{cursor:o.Etat==="off"?"not-allowed":"pointer",opacity:o.Etat==="off"?.7:1}})})),e.createElement(je,{tab:"Cartes",key:"2"},e.createElement(M,{gutter:[16,16]},u.map((o,m)=>e.createElement(y,{key:m,xs:24,sm:12,md:8,lg:6},e.createElement("div",{style:{position:"relative"}},e.createElement(U,{hoverable:!!o.id&&o.Etat!=="off",onClick:()=>o.id&&o.Etat!=="off"&&R(o),style:{borderTop:`2px solid ${s(o.status,o)}`,opacity:o.Etat==="off"?.7:1,cursor:o.Etat==="off"?"not-allowed":"pointer"},title:o.Etat==="off"?"Machine hors ligne. Détails non disponibles.":""},e.createElement("div",{style:{textAlign:"center"}},e.createElement(Me,{level:4},o.Machine_Name||"Machine"),e.createElement(Ie,{type:"dashboard",percent:parseFloat(o.TRS||"0"),status:parseFloat(o.TRS)>80?"success":parseFloat(o.TRS)>60?"normal":"exception"}),o.Etat==="on"&&e.createElement(xe,{status:"processing",text:"Session active",style:{marginTop:8}}),e.createElement("div",{style:{marginTop:8}},e.createElement(We,null,"Production: ",parseFloat(o.Quantite_Bon||0))))),o.id!==1&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(D,{type:"default",size:"small"},"Configuration requise")),!o.id&&e.createElement("div",{className:"soon-overlay"},e.createElement("div",{className:"soon-text"},"En développement..."),e.createElement(D,{type:"default",size:"small"},"Configuration requise"))))))))),e.createElement("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3}},e.createElement(mt,{content:e.createElement("div",{style:{width:250}},e.createElement("p",null,e.createElement("strong",null,"Available tools:")),e.createElement("ul",null,e.createElement("li",null,"Machine view"),e.createElement("li",null,"Detailed performance analysis"),e.createElement("li",null,"Data export")),e.createElement(D,{type:"primary",block:!0},"User Guide")),title:"Help and tools",trigger:"click",placement:"topRight"},e.createElement(D,{type:"primary",shape:"circle",icon:e.createElement(Re,null),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}))),e.createElement(Dt,{visible:n,machine:l,machineHistory:a,loading:A,error:se,onClose:()=>r(!1),onRefresh:()=>c(l==null?void 0:l.id),darkMode:t}))};typeof window<"u"&&ht();const on=()=>{const{isAuthenticated:t,loading:n}=Qe(),r=(d,x)=>{const u=(()=>{if(typeof window<"u"){const l=window.location.origin;return l.includes("ngrok-free.app")||l.includes("ngrok.io")?l:"http://localhost:5000"}return"http://localhost:5000"})();return Ge[d](`${u}${x}`).retry(2).withCredentials().timeout(3e4)};h.useEffect(()=>{console.log("Initializing WebSocket connection from OptimizedDailyPerformanceDashboard"),_.connect();const d=()=>{document.visibilityState==="visible"&&(_.isConnected||(console.log("Tab is visible again, checking WebSocket connection..."),_.ensureConnection()))};return document.addEventListener("visibilitychange",d),()=>{document.removeEventListener("visibilitychange",d),_.disconnect()}},[]);const i=async()=>{var d,x,u,l;try{if(!t){G.error("Please log in to create test notifications",3);return}G.loading("Creating test notification...",1);const a=await r("post","/api/notifications/test-sse").send({});if(console.log("✅ Test SSE notification response:",a.body),a.body.success){const{notification:w,broadcast_result:p,database_saved:O,database_error:I}=a.body;O?(G.success(`✅ Test notification created and saved! ${w.title}`,4),console.log("📊 Database saved successfully. Notification will persist after reload.")):(G.warning(`⚠️ Notification broadcast but not saved to database: ${I}. Will disappear on reload.`,6),console.log("⚠️ Database error:",I),console.log("📡 SSE broadcast successful but notification is temporary")),console.log("📊 SSE Broadcast metrics:",p)}else G.warning("Test notification created but may not have been broadcast properly",3)}catch(a){console.error("❌ Failed to create test notification via SSE:",a),console.error("❌ Error details:",{message:a.message,status:a.status,response:((d=a.response)==null?void 0:d.body)||a.response,code:a.code}),a.code==="ECONNABORTED"?G.error("Test notification timed out - server may be busy",3):a.status===401||((x=a.response)==null?void 0:x.status)===401?(G.error("Authentication required - please login again",3),console.error("🔐 Authentication failed - user may need to re-login")):a.code==="ECONNREFUSED"?(G.error("Cannot connect to server - please check if the application is running",4),console.error("🔌 Connection refused - backend server may not be running")):a.status>=500?(G.error("Server error - please try again later",3),console.error("🚨 Server error (5xx) - check backend logs")):G.error(`Failed to create test notification: ${((l=(u=a.response)==null?void 0:u.body)==null?void 0:l.message)||a.message}`,3)}};return e.createElement("div",{className:"optimized-dashboard-wrapper"},e.createElement("div",{style:{position:"absolute",top:"10px",right:"10px",zIndex:2e3}},e.createElement(D,{type:"primary",onClick:i,disabled:n||!t,loading:n,style:{backgroundColor:t?"#52c41a":"#d9d9d9",borderColor:t?"#52c41a":"#d9d9d9",fontWeight:"bold"},title:t?"Create a test SSE notification":"Please log in to use this feature"},"🧪 Test SSE Notification")),e.createElement(Nt,null))};export{on as default};
