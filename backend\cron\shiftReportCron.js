import cron from "node-cron";
import ShiftReportService from "../utils/shiftReportService.js";

// Schedule tasks to run at the end of each shift
// DISABLED: These automatic reports were causing server issues
// Uncomment when the server is ready to handle these reports

/*
// 14:00 (2:00 PM) - End of morning shift
cron.schedule("0 14 * * *", async () => {
  console.log("Running end of morning shift report task")
  await ShiftReportService.processEndOfShiftReports(1)
})

// 22:00 (10:00 PM) - End of afternoon shift
cron.schedule("0 22 * * *", async () => {
  console.log("Running end of afternoon shift report task")
  await ShiftReportService.processEndOfShiftReports(2)
})

// 6:00 (6:00 AM) - End of night shift
cron.schedule("0 6 * * *", async () => {
  console.log("Running end of night shift report task")
  await ShiftReportService.processEndOfShiftReports(3)
})
*/

console.log("Shift report cron jobs disabled - server stability issue")

const shiftReportCron = {
  start: () => {
    console.log("Shift report scheduler started")
  },
}

export default shiftReportCron;

