import React from 'react';
import { Space } from 'antd';
import { EyeOutlined } from '@ant-design/icons';

const YieldOptimizationCard = ({ loading, filters }) => {
  return (
    <div style={{ 
      height: '300px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)',
      borderRadius: '12px'
    }}>
      <Space direction="vertical" align="center">
        <EyeOutlined style={{ fontSize: '48px', color: '#a0d911' }} />
        <h3 style={{ color: '#a0d911', margin: 0 }}>Yield Optimization AI</h3>
        <p style={{ color: '#8c8c8c', textAlign: 'center' }}>
          Maximize production yield through AI insights
        </p>
      </Space>
    </div>
  );
};

export default YieldOptimizationCard;
