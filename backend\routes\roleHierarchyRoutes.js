import express from 'express';
import { sendSuccess, sendError } from '../utils/responseUtils.js';
import auth from '../middleware/auth.js';
import { checkPermission } from '../middleware/permission.js';
import { ROLE_HIERARCHY, PERMISSION_NAMESPACES, DEFAULT_ROLE_PERMISSIONS, getAllRolePermissions } from '../utils/roleHierarchy.js';

const router = express.Router();

/**
 * @route   GET /api/role-hierarchy/hierarchy
 * @desc    Get role hierarchy and permissions data
 * @access  Private (requires system:view_dashboard permission)
 */
router.get('/hierarchy', auth, checkPermission(['system:view_dashboard']), async (req, res) => {
  try {
    // Prepare response data
    const responseData = {
      hierarchy: ROLE_HIERARCHY,
      permissions: PERMISSION_NAMESPACES,
      rolePermissions: {}
    };

    // Get all permissions for each role including inherited permissions
    Object.keys(ROLE_HIERARCHY).forEach(roleName => {
      responseData.rolePermissions[roleName] = getAllRolePermissions(roleName);
    });

    return sendSuccess(res, responseData);
  } catch (error) {
    console.error('Error fetching role hierarchy:', error);
    return sendError(res, 'Server error', 500, error);
  }
});

/**
 * @route   GET /api/role-hierarchy/permissions/:roleName
 * @desc    Get all permissions for a specific role including inherited permissions
 * @access  Private (requires system:manage_roles permission)
 */
router.get('/permissions/:roleName', auth, checkPermission(['system:manage_roles']), async (req, res) => {
  try {
    const { roleName } = req.params;

    // Check if role exists
    if (!ROLE_HIERARCHY[roleName]) {
      return sendError(res, 'Role not found', 404);
    }

    // Get all permissions for the role including inherited permissions
    const permissions = getAllRolePermissions(roleName);

    return sendSuccess(res, {
      role: roleName,
      permissions,
      inherits: ROLE_HIERARCHY[roleName].inherits,
      level: ROLE_HIERARCHY[roleName].level,
      description: ROLE_HIERARCHY[roleName].description
    });
  } catch (error) {
    console.error(`Error fetching permissions for role ${req.params.roleName}:`, error);
    return sendError(res, 'Server error', 500, error);
  }
});

/**
 * @route   GET /api/role-hierarchy/user-permissions
 * @desc    Get all permissions for the current user including role hierarchy
 * @access  Private (any authenticated user)
 */
router.get('/user-permissions', auth, async (req, res) => {
  try {
    // Get user context from request (set by permission middleware)
    const { userContext } = req;

    // If userContext is available, use it
    if (userContext) {
      return sendSuccess(res, {
        roleName: userContext.roleName,
        departmentId: userContext.departmentId,
        departmentName: userContext.departmentName,
        permissions: userContext.permissions
      });
    }

    // If userContext is not available, create it from the user object
    const { user } = req;

    if (!user) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Get user's role permissions from the role hierarchy
    let rolePermissions = [];
    if (user.role && ROLE_HIERARCHY[user.role]) {
      rolePermissions = getAllRolePermissions(user.role);
    }

    // Combine user's direct permissions and role permissions
    const userPermissions = [
      ...(Array.isArray(user.permissions) ? user.permissions : []),
      ...(Array.isArray(user.role_permissions) ? user.role_permissions : []),
      ...rolePermissions
    ];

    // Remove duplicates
    const uniquePermissions = [...new Set(userPermissions)];

    return sendSuccess(res, {
      roleName: user.role || user.role_name || 'Unknown',
      departmentId: user.department_id || null,
      departmentName: user.department_name || 'Unknown',
      permissions: uniquePermissions
    });
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return sendError(res, 'Server error', 500, error);
  }
});

export default router;
