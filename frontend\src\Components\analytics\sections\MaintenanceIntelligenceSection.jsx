import React from 'react';
import { Card, Space } from 'antd';
import { ToolOutlined } from '@ant-design/icons';

const MaintenanceIntelligenceSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <ToolOutlined style={{ fontSize: '64px', color: '#fa541c' }} />
          <h2 style={{ color: '#fa541c', margin: 0 }}>Maintenance Intelligence</h2>
          <p style={{ color: '#8c8c8c' }}>Predictive maintenance and equipment optimization coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default MaintenanceIntelligenceSection;
