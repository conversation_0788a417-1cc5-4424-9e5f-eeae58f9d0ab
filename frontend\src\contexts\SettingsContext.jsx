import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import SettingsService from '../services/SettingsService';
import { useTheme } from '../theme-context';

// Settings Context
const SettingsContext = createContext();

// Settings Actions
const SETTINGS_ACTIONS = {
  LOAD_START: 'LOAD_START',
  LOAD_SUCCESS: 'LOAD_SUCCESS',
  LOAD_ERROR: 'LOAD_ERROR',
  UPDATE_START: 'UPDATE_START',
  UPDATE_SUCCESS: 'UPDATE_SUCCESS',
  UPDATE_ERROR: 'UPDATE_ERROR',
  RESET_START: 'RESET_START',
  RESET_SUCCESS: 'RESET_SUCCESS',
  RESET_ERROR: 'RESET_ERROR',
  UPDATE_SETTING: 'UPDATE_SETTING'
};

// Initial state
const initialState = {
  settings: null,
  loading: false,
  error: null,
  updating: false,
  updateError: null,
  lastUpdated: null
};

// Settings reducer
function settingsReducer(state, action) {
  switch (action.type) {
    case SETTINGS_ACTIONS.LOAD_START:
      return {
        ...state,
        loading: true,
        error: null
      };

    case SETTINGS_ACTIONS.LOAD_SUCCESS:
      return {
        ...state,
        loading: false,
        settings: action.payload,
        error: null,
        lastUpdated: new Date()
      };

    case SETTINGS_ACTIONS.LOAD_ERROR:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    case SETTINGS_ACTIONS.UPDATE_START:
      return {
        ...state,
        updating: true,
        updateError: null
      };

    case SETTINGS_ACTIONS.UPDATE_SUCCESS:
      return {
        ...state,
        updating: false,
        settings: action.payload,
        updateError: null,
        lastUpdated: new Date()
      };

    case SETTINGS_ACTIONS.UPDATE_ERROR:
      return {
        ...state,
        updating: false,
        updateError: action.payload
      };

    case SETTINGS_ACTIONS.RESET_START:
      return {
        ...state,
        updating: true,
        updateError: null
      };

    case SETTINGS_ACTIONS.RESET_SUCCESS:
      return {
        ...state,
        updating: false,
        settings: action.payload,
        updateError: null,
        lastUpdated: new Date()
      };

    case SETTINGS_ACTIONS.RESET_ERROR:
      return {
        ...state,
        updating: false,
        updateError: action.payload
      };

    case SETTINGS_ACTIONS.UPDATE_SETTING:
      return {
        ...state,
        settings: action.payload
      };

    default:
      return state;
  }
}

// Settings Provider Component
export function SettingsProvider({ children }) {
  const [state, dispatch] = useReducer(settingsReducer, initialState);

  // Get MainLayout theme for synchronization
  const { darkMode: mainDarkMode } = useTheme();

  // Load settings from server
  const loadSettings = useCallback(async () => {
    dispatch({ type: SETTINGS_ACTIONS.LOAD_START });
    
    try {
      const settings = await SettingsService.getSettings();
      dispatch({ 
        type: SETTINGS_ACTIONS.LOAD_SUCCESS, 
        payload: settings 
      });
    } catch (error) {
      console.error('Failed to load settings:', error);
      dispatch({ 
        type: SETTINGS_ACTIONS.LOAD_ERROR, 
        payload: error.message 
      });
    }
  }, []);

  // Update settings on server
  const updateSettings = useCallback(async (newSettings) => {
    dispatch({ type: SETTINGS_ACTIONS.UPDATE_START });
    
    try {
      const updatedSettings = await SettingsService.updateSettings(newSettings);
      dispatch({ 
        type: SETTINGS_ACTIONS.UPDATE_SUCCESS, 
        payload: updatedSettings 
      });
      return updatedSettings;
    } catch (error) {
      console.error('Failed to update settings:', error);
      dispatch({ 
        type: SETTINGS_ACTIONS.UPDATE_ERROR, 
        payload: error.message 
      });
      throw error;
    }
  }, []);

  // Update a specific setting (optimistic update with enhanced error handling)
  const updateSetting = useCallback((path, value) => {
    if (!state.settings) {
      console.warn('⚠️ Cannot update setting: settings not loaded');
      return;
    }

    const pathArray = path.split('.');
    const newSettings = { ...state.settings };

    // Navigate to the nested property and update it
    let current = newSettings;
    for (let i = 0; i < pathArray.length - 1; i++) {
      current[pathArray[i]] = { ...current[pathArray[i]] };
      current = current[pathArray[i]];
    }
    current[pathArray[pathArray.length - 1]] = value;

    console.log(`🔄 Optimistically updating setting: ${path} = ${value}`);

    // Update local state immediately (optimistic update)
    dispatch({
      type: SETTINGS_ACTIONS.UPDATE_SETTING,
      payload: newSettings
    });

    // Update on server with enhanced error handling
    updateSettings(newSettings).catch(error => {
      console.error(`❌ Failed to persist setting update for ${path}:`, error);

      // Show user-friendly error message
      if (error.message.includes('timeout')) {
        console.error('⏰ Settings update timed out - this may indicate server connectivity issues');
      } else if (error.message.includes('Network Error')) {
        console.error('🌐 Network error - check your internet connection');
      } else if (error.status === 401) {
        console.error('🔐 Authentication error - please log in again');
      } else {
        console.error('💥 Unexpected error during settings update');
      }

      // Revert optimistic update on error
      console.log(`🔄 Reverting optimistic update for ${path}`);
      dispatch({
        type: SETTINGS_ACTIONS.UPDATE_SETTING,
        payload: state.settings
      });
    });
  }, [state.settings, updateSettings]);

  // Reset settings to defaults
  const resetSettings = useCallback(async () => {
    dispatch({ type: SETTINGS_ACTIONS.RESET_START });
    
    try {
      const defaultSettings = await SettingsService.resetSettings();
      dispatch({ 
        type: SETTINGS_ACTIONS.RESET_SUCCESS, 
        payload: defaultSettings 
      });
      return defaultSettings;
    } catch (error) {
      console.error('Failed to reset settings:', error);
      dispatch({ 
        type: SETTINGS_ACTIONS.RESET_ERROR, 
        payload: error.message 
      });
      throw error;
    }
  }, []);

  // Get a specific setting value
  const getSetting = useCallback((path, defaultValue = null) => {
    if (!state.settings) return defaultValue;

    const pathArray = path.split('.');
    let current = state.settings;
    
    for (const key of pathArray) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }, [state.settings]);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // Synchronize settings theme with MainLayout theme
  useEffect(() => {
    if (state.settings && state.settings.theme && state.settings.theme.darkMode !== mainDarkMode) {
      console.log(`🔄 Synchronizing settings theme with MainLayout: ${mainDarkMode}`);

      const updatedSettings = {
        ...state.settings,
        theme: {
          ...state.settings.theme,
          darkMode: mainDarkMode
        }
      };

      // Update local state immediately (optimistic update)
      dispatch({
        type: SETTINGS_ACTIONS.UPDATE_SETTING,
        payload: updatedSettings
      });

      // Update on server in background
      updateSettings(updatedSettings).catch(error => {
        console.error('Failed to synchronize theme with server:', error);
        // Don't revert - keep the UI synchronized even if server update fails
      });
    }
  }, [mainDarkMode, state.settings, updateSettings]);

  // Context value
  const contextValue = {
    // State
    settings: state.settings,
    loading: state.loading,
    error: state.error,
    updating: state.updating,
    updateError: state.updateError,
    lastUpdated: state.lastUpdated,
    
    // Actions
    loadSettings,
    updateSettings,
    updateSetting,
    resetSettings,
    getSetting
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
}

// Custom hook to use settings
export function useSettings() {
  const context = useContext(SettingsContext);
  
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  
  return context;
}

// Export context for advanced usage
export { SettingsContext };
