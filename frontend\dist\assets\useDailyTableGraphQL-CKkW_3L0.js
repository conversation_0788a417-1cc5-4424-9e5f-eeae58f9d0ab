import{r as a}from"./index-O2xm1U_Z.js";const w="/api/graphql",E=()=>{const[b,l]=a.useState(!1),[m,f]=a.useState(null),t=a.useCallback(async(e,r={})=>{l(!0),f(null);try{const n=await fetch(w,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:r})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const s=await n.json();if(s.errors)throw new Error(s.errors.map(u=>u.message).join(", "));return l(!1),s.data}catch(n){throw f(n.message),l(!1),n}},[]),p=a.useCallback(async(e={})=>t(`
      query GetAllDailyProduction($filters: FilterInput) {
        getAllDailyProduction(filters: $filters) {
          Machine_Name
          Date_Insert_Day
          Run_Hours_Day
          Down_Hours_Day
          Good_QTY_Day
          Rejects_QTY_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
          OEE_Day
          Shift
        }
      }
    `,{filters:e}),[t]),y=a.useCallback(async(e={})=>t(`
      query GetProductionChart($filters: EnhancedFilterInput) {
        enhancedGetProductionChart(filters: $filters) {
          data {
            Date_Insert_Day
            Total_Good_Qty_Day
            Total_Rejects_Qty_Day
            OEE_Day
            Speed_Day
            Availability_Rate_Day
            Performance_Rate_Day
            Quality_Rate_Day
          }
          dataSource
        }
      }
    `,{filters:e}),[t]),d=a.useCallback(async(e={})=>t(`
      query GetProductionSidecards($filters: EnhancedFilterInput) {
        enhancedGetProductionSidecards(filters: $filters) {
          goodqty
          rejetqty
          dataSource
        }
      }
    `,{filters:e}),[t]),S=a.useCallback(async()=>t(`
      query {
        getUniqueDates
      }
    `),[t]),G=a.useCallback(async()=>t(`
      query {
        getMachineModels {
          model
        }
      }
    `),[t]),M=a.useCallback(async()=>t(`
      query {
        getMachineNames {
          Machine_Name
        }
      }
    `),[t]),h=a.useCallback(async(e={})=>t(`
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          data {
            Machine_Name
            Shift
            production
            rejects
            downtime
            availability
            performance
            oee
            quality
            disponibilite
          }
          dataSource
        }
      }
    `,{filters:e}),[t]),_=a.useCallback(async(e={})=>t(`
      query GetAvailabilityTrend($filters: FilterInput) {
        getAvailabilityTrend(filters: $filters) {
          date
          machine
          disponibilite
        }
      }
    `,{filters:e}),[t]),k=a.useCallback(async(e={})=>t(`
      query GetPerformanceMetrics($filters: FilterInput) {
        getPerformanceMetrics(filters: $filters) {
          machine
          model
          disponibilite
          stops
          mttr
          mtbf
        }
      }
    `,{filters:e}),[t]),C=a.useCallback(async(e={})=>{var r,n,s,u,D,g,P;try{const[o,c,i,q]=await Promise.all([y(e),d(e),h(e),_(e)]);return{productionChart:{data:((r=o==null?void 0:o.enhancedGetProductionChart)==null?void 0:r.data)||[],dataSource:((n=o==null?void 0:o.enhancedGetProductionChart)==null?void 0:n.dataSource)||"unknown"},sidecards:{goodqty:((s=c==null?void 0:c.enhancedGetProductionSidecards)==null?void 0:s.goodqty)||0,rejetqty:((u=c==null?void 0:c.enhancedGetProductionSidecards)==null?void 0:u.rejetqty)||0,dataSource:((D=c==null?void 0:c.enhancedGetProductionSidecards)==null?void 0:D.dataSource)||"unknown"},machinePerformance:{data:((g=i==null?void 0:i.enhancedGetMachinePerformance)==null?void 0:g.data)||[],dataSource:((P=i==null?void 0:i.enhancedGetMachinePerformance)==null?void 0:P.dataSource)||"unknown"},availabilityTrend:(q==null?void 0:q.getAvailabilityTrend)||[]}}catch(o){throw console.error("Error fetching dashboard data:",o),o}},[y,d,h,_]);return{loading:b,error:m,getAllDailyProduction:p,getProductionChart:y,getProductionSidecards:d,getUniqueDates:S,getMachineModels:G,getMachineNames:M,getMachinePerformance:h,getAvailabilityTrend:_,getPerformanceMetrics:k,getDashboardData:C,executeQuery:t}};export{E as u};
