import React, { memo } from "react";
import {
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as RechartsTooltip,
  Bar,
  Line
} from "recharts";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// SOMIPEM Brand Color Palette (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Chart component for shift comparison
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @returns {JSX.Element} - Rendered chart component
 */
const ShiftComparisonChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <BarChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
      <YAxis
        yAxisId="left"
        tickFormatter={(value) => value.toLocaleString()}
        label={{
          value: "Production",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        tickFormatter={(value) => `${value}%`}
        domain={[0, 100]}
        label={{
          value: "Performance",
          angle: 90,
          position: "insideRight",
          style: { fill: "#666" },
        }}
      />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)

          if (name === "production") return [isValidNumber ? value.toLocaleString() : value, "Production"]
          if (name === "downtime") return [isValidNumber ? value.toLocaleString() : value, "Temps d'arrêt"]
          if (name === "oee") return [isValidNumber ? `${value.toFixed(1)}%` : `${value}%`, "TRS"]
          if (name === "performance") return [isValidNumber ? `${value.toFixed(1)}%` : `${value}%`, "Performance"]
          return [value, name]
        }}
        labelFormatter={(label) => `Équipe: ${label}`}
      />

      <Bar yAxisId="left" dataKey="production" name="Production" fill={COLORS[2]} maxBarSize={40} />
      <Bar yAxisId="left" dataKey="downtime" name="Temps d'arrêt" fill={COLORS[4]} maxBarSize={40} />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="oee"
        name="TRS"
        stroke={COLORS[0]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[0] }}
      />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="performance"
        name="Performance"
        stroke={COLORS[5]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[5] }}
      />
    </BarChart>
  </ResponsiveContainer>
));

ShiftComparisonChart.displayName = 'ShiftComparisonChart';

export default ShiftComparisonChart;
