import{r as l,a2 as P,h as oe,G as _,a7 as le,R as n,m as re,d as V,e as C,g as w,M as ie,C as se}from"./index-O2xm1U_Z.js";import{g as ce,u as de}from"./chartColors-CyE5IZmc.js";import{i as k}from"./PieChart-CJMXTNKV.js";import{R as Z}from"./FullscreenOutlined-DMf8_5Nq.js";import{R as G}from"./DownloadOutlined-ClmkhSDC.js";import{R as me}from"./CloseOutlined-CLsA06b-.js";import{Z as pe,a as ue}from"./ZoomOutOutlined-CdPjzMfa.js";var fe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M326 664H104c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v176c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V696c0-17.7-14.3-32-32-32zm16-576h-48c-8.8 0-16 7.2-16 16v176H104c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222c17.7 0 32-14.3 32-32V104c0-8.8-7.2-16-16-16zm578 576H698c-17.7 0-32 14.3-32 32v224c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V744h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm0-384H746V104c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v224c0 17.7 14.3 32 32 32h222c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16z"}}]},name:"compress",theme:"outlined"},he={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M342 88H120c-17.7 0-32 14.3-32 32v224c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 576h-48c-8.8 0-16 7.2-16 16v176H682c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222c17.7 0 32-14.3 32-32V680c0-8.8-7.2-16-16-16zM342 856H168V680c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v224c0 17.7 14.3 32 32 32h222c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zM904 88H682c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v176c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32z"}}]},name:"expand",theme:"outlined"},ge={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M391 240.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L200 146.3a8.03 8.03 0 00-11.3 0l-42.4 42.3a8.03 8.03 0 000 11.3L280 333.6l-43.9 43.9a8.01 8.01 0 004.7 13.6L401 410c5.1.6 9.5-3.7 8.9-8.9L391 240.9zm10.1 373.2L240.8 633c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L146.3 824a8.03 8.03 0 000 11.3l42.4 42.3c3.1 3.1 8.2 3.1 11.3 0L333.7 744l43.7 43.7A8.01 8.01 0 00391 783l18.9-160.1c.6-5.1-3.7-9.4-8.8-8.8zm221.8-204.2L783.2 391c6.6-.8 9.4-8.9 4.7-13.6L744 333.6 877.7 200c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.3a8.03 8.03 0 00-11.3 0L690.3 279.9l-43.7-43.7a8.01 8.01 0 00-13.6 4.7L614.1 401c-.6 5.2 3.7 9.5 8.8 8.9zM744 690.4l43.9-43.9a8.01 8.01 0 00-4.7-13.6L623 614c-5.1-.6-9.5 3.7-8.9 8.9L633 783.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L824 877.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L744 690.4z"}}]},name:"fullscreen-exit",theme:"outlined"};function D(){return D=Object.assign?Object.assign.bind():function(a){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(a[i]=o[i])}return a},D.apply(this,arguments)}const xe=(a,t)=>l.createElement(P,D({},a,{ref:t,icon:fe})),be=l.forwardRef(xe);function F(){return F=Object.assign?Object.assign.bind():function(a){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(a[i]=o[i])}return a},F.apply(this,arguments)}const Ee=(a,t)=>l.createElement(P,F({},a,{ref:t,icon:he})),ve=l.forwardRef(Ee);function W(){return W=Object.assign?Object.assign.bind():function(a){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(a[i]=o[i])}return a},W.apply(this,arguments)}const ye=(a,t)=>l.createElement(P,W({},a,{ref:t,icon:ge})),Ce=l.forwardRef(ye);function B(){return B=Object.assign?Object.assign.bind():function(a){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(a[i]=o[i])}return a},B.apply(this,arguments)}const we=(a,t)=>l.createElement(P,B({},a,{ref:t,icon:pe})),ke=l.forwardRef(we);function q(){return q=Object.assign?Object.assign.bind():function(a){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(a[i]=o[i])}return a},q.apply(this,arguments)}const ze=(a,t)=>l.createElement(P,q({},a,{ref:t,icon:ue})),Me=l.forwardRef(ze),Ue=()=>{const{settings:a,charts:t,theme:o}=oe(),i=l.useMemo(()=>ce(a),[a]),x=l.useCallback((u,v=0)=>i===null?u:i[v%i.length],[i]),d=l.useCallback(u=>i===null?u:i,[i]),f=l.useMemo(()=>{var u;return((u=t==null?void 0:t.interactions)==null?void 0:u.clickToExpand)!==!1},[t]),z=l.useMemo(()=>({showLegend:(t==null?void 0:t.showLegend)!==!1,performanceMode:(t==null?void 0:t.performanceMode)===!0,animationsEnabled:(o==null?void 0:o.animationsEnabled)&&(o==null?void 0:o.chartAnimations),colorScheme:(t==null?void 0:t.colorScheme)||"brand"}),[t,o]);return{getChartColor:x,getChartColors:d,colors:i,expandEnabled:f,...z,settings:a,charts:t,theme:o}},Te=(a,t,o="bar",i={})=>({title:a,data:t,chartType:o,expandMode:"modal",exportEnabled:!0,...i}),{useBreakpoint:Oe}=_,K=({visible:a,onClose:t,title:o,data:i,chartType:x,chartConfig:d,children:f,exportEnabled:z=!1})=>{const[u,v]=l.useState(!1),[s,M]=l.useState(!1);Oe();const{isMobile:e,isTablet:O,isDesktop:R,getModalDimensions:H,getModalZIndex:U,getModalMaskZIndex:S,getModalContainer:b}=le(),E=l.useCallback(()=>{console.log("UnifiedChartModal: handleClose triggered"),console.log("UnifiedChartModal: onClose callback type:",typeof t),console.log("UnifiedChartModal: visible prop:",a),v(!1),M(!1),typeof t=="function"?(console.log("UnifiedChartModal: Calling onClose callback"),t()):console.error("UnifiedChartModal: onClose is not a function!",t)},[t,a]);l.useEffect(()=>{const m=y=>{if(a)switch(y.key){case"Escape":s?M(!1):E();break;case"F11":y.preventDefault(),M(!s);break}};return a&&document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}},[a,E,s]);const $=l.useCallback(()=>{v(!0);try{console.log("Exporting chart:",o)}finally{setTimeout(()=>v(!1),1e3)}},[o]),r=l.useCallback(()=>{M(!s)},[s]),p=l.useMemo(()=>H(s),[H,s]),I=l.useCallback(()=>{const m=window.innerHeight,y=e?60:80,T=e?16:32;return s||e?m-y-T:Math.floor(O?m*.75:m*.7)},[e,O,s]),h=l.useCallback(()=>{if(u)return n.createElement("div",{style:{height:I(),display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"16px"}},n.createElement(re,{size:"large",tip:e?"Chargement...":"Chargement du graphique..."}),!e&&n.createElement("div",{style:{color:"#666",fontSize:"14px"}},"Préparation de l'affichage optimisé..."));if(!i||i.length===0)return n.createElement("div",{style:{height:I(),display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",color:"#999",fontSize:e?"14px":"16px",gap:"8px"}},n.createElement("div",null,"Aucune donnée disponible"),!e&&n.createElement("div",{style:{fontSize:"12px",opacity:.7}},"Vérifiez vos filtres ou la période sélectionnée"));const m=I();return n.createElement("div",{style:{height:m,width:"100%",position:"relative",overflow:"hidden"}},n.createElement("div",{id:"modal-chart",style:{height:"100%",width:"100%",transition:"all 0.3s ease"}},n.cloneElement(f,{...f.props,data:i,height:m,enhanced:!0,expanded:!0,isModal:!0,isMobile:e,isTablet:O,isFullscreen:s,chartConfig:{...d,showAllLabels:!e,labelInterval:e?1:0,maxBarSize:e?40:60,strokeWidth:e?2:3,dotSize:e?4:6,fontSize:e?10:12,touchEnabled:e,gestureHandling:e?"cooperative":"auto"}})))},[u,i,I,e,O,s,f,d]),g=l.useMemo(()=>{var m;return n.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%",background:`linear-gradient(135deg, ${(d==null?void 0:d.getPrimaryColor())||"#1890ff"} 0%, ${((m=d==null?void 0:d.colors)==null?void 0:m[1])||"#096dd9"} 100%)`,padding:e?"8px 12px":"12px 20px",margin:e?"-8px -12px 0 -12px":"-16px -24px 0 -24px",borderRadius:s||e?"0":"8px 8px 0 0",minHeight:e?"48px":"60px",position:"relative",zIndex:10},onClick:y=>y.stopPropagation()},n.createElement("div",{style:{display:"flex",alignItems:"center",flex:1,minWidth:0}},n.createElement("span",{style:{color:"white",fontSize:e?"14px":"18px",fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.3)",marginRight:"8px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},o),!e&&n.createElement(Z,{style:{color:"white",fontSize:"16px",textShadow:"0 1px 2px rgba(0,0,0,0.3)"}})),n.createElement(V,{size:e?4:8},!e&&!s&&n.createElement(C,{title:"Mode plein écran (F11)"},n.createElement(w,{icon:n.createElement(Z,null),onClick:r,size:e?"small":"middle",style:{background:"rgba(255,255,255,0.2)",border:"1px solid rgba(255,255,255,0.3)",color:"white",minWidth:e?"32px":"40px"}})),s&&n.createElement(C,{title:"Quitter le plein écran (F11)"},n.createElement(w,{icon:n.createElement(Ce,null),onClick:r,size:e?"small":"middle",style:{background:"rgba(255,255,255,0.2)",border:"1px solid rgba(255,255,255,0.3)",color:"white",minWidth:e?"32px":"40px"}})),z&&n.createElement(C,{title:e?"Exporter":"Exporter le graphique"},n.createElement(w,{icon:n.createElement(G,null),onClick:$,loading:u,size:e?"small":"middle",style:{background:"rgba(255,255,255,0.2)",border:"1px solid rgba(255,255,255,0.3)",color:"white",minWidth:e?"32px":"40px"}},!e&&"Exporter")),n.createElement(C,{title:e?"Fermer":"Fermer (Échap)"},n.createElement(w,{icon:n.createElement(me,null),onClick:E,size:e?"small":"middle",type:"text",style:{background:"rgba(255,255,255,0.2)",border:"1px solid rgba(255,255,255,0.3)",color:"white",minWidth:e?"32px":"40px",position:"relative",zIndex:10,cursor:"pointer"}}))))},[o,d,e,s,z,u,r,$,E]);return l.useEffect(()=>{console.log("UnifiedChartModal: visible prop changed to:",a)},[a]),l.useEffect(()=>{a||(console.log("UnifiedChartModal: Modal should be closed, visible is false"),v(!1),M(!1))},[a]),n.createElement(ie,{key:`unified-chart-modal-${a?"open":"closed"}`,title:g,open:a,onCancel:E,onClose:E,width:p.width,style:{top:s?0:p.top,left:s?0:p.left,maxWidth:s?"100vw":p.width,padding:s?0:p.padding,margin:0,zIndex:s?10011:U()},styles:{body:{height:`calc(${p.height} - ${e?"48px":"60px"})`,padding:e?"8px":"16px",margin:0,overflow:"hidden",background:"#fafafa"},mask:{backgroundColor:"transparent",zIndex:S()}},footer:null,destroyOnClose:!0,maskClosable:!s,keyboard:!0,centered:!1,getContainer:s?()=>document.body:b,className:`unified-chart-modal ${e?"mobile":""} ${O?"tablet":""} ${s?"fullscreen":""}`,wrapClassName:"unified-chart-modal-wrap",transitionName:"unified-chart-modal",maskTransitionName:"unified-chart-modal-mask"},n.createElement("div",{style:{height:"100%",width:"100%",background:"white",borderRadius:s||e?"0":"8px",padding:e?"8px":"16px",boxShadow:s?"none":"0 4px 16px rgba(0,0,0,0.15)",overflow:"hidden",display:"flex",flexDirection:"column",position:"relative",zIndex:1}},h()))};K.propTypes={visible:k.bool.isRequired,onClose:k.func.isRequired,title:k.string.isRequired,data:k.array.isRequired,chartType:k.string,chartConfig:k.object,children:k.element.isRequired,exportEnabled:k.bool};const{useBreakpoint:$e}=_,Ve=({children:a,title:t,data:o,chartType:i="bar",expandMode:x="modal",onExpand:d,onCollapse:f,exportEnabled:z=!1,className:u="",style:v={},cardProps:s={},...M})=>{var N,A;const[e,O]=l.useState(!1),[R,H]=l.useState(!1),[U,S]=l.useState(!1),[b,E]=l.useState(1);l.useEffect(()=>{console.log("UnifiedChartExpansion: isModalVisible changed to:",R)},[R]);const $=$e(),r=!$.md,p=$.md&&!$.lg,I=$.lg,h=de({chartType:i,customOptions:{responsive:!0,mobile:r,tablet:p,desktop:I}}),g=((N=h==null?void 0:h.interactionConfig)==null?void 0:N.clickToExpand)!==!1,m=l.useCallback(async c=>{if(g){c&&(c.preventDefault(),c.stopPropagation()),S(!0);try{const L=r?50:o&&o.length>100?100:0;if(L>0&&await new Promise(j=>setTimeout(j,L)),x==="modal")H(!0),d&&d();else{const j=!e;O(j),j&&d?d():!j&&f&&f()}}catch(L){console.error("❌ UnifiedChartExpansion - Error during expansion:",L)}finally{S(!1)}}},[x,e,d,f,o,g,r]),y=l.useCallback(()=>{E(c=>Math.min(c+.2,2))},[]),T=l.useCallback(()=>{E(c=>Math.max(c-.2,.5))},[]),Q=l.useCallback(()=>{E(1)},[]),Y=l.useCallback(()=>{S(!0);try{console.log("Exporting chart:",t)}finally{setTimeout(()=>S(!1),1e3)}},[t]),J=l.useCallback(()=>{console.log("UnifiedChartExpansion: handleModalClose called, current isModalVisible:",R),H(!1),console.log("UnifiedChartExpansion: setIsModalVisible(false) called"),f&&f()},[f,R]),X=l.useCallback(c=>{c.target.closest(".ant-card-extra")||c.target.closest(".chart-container")||c.target.closest(".expand-button")||g&&!r&&m(c)},[g,m,r]),ee=()=>!e||x==="modal"?null:n.createElement(V,{size:r?"small":"middle"},!r&&n.createElement(n.Fragment,null,n.createElement(C,{title:"Zoom avant"},n.createElement(w,{size:"small",icon:n.createElement(ke,null),onClick:c=>{c.stopPropagation(),y()},disabled:b>=2})),n.createElement(C,{title:"Zoom arrière"},n.createElement(w,{size:"small",icon:n.createElement(Me,null),onClick:c=>{c.stopPropagation(),T()},disabled:b<=.5})),n.createElement(C,{title:"Réinitialiser le zoom"},n.createElement(w,{size:"small",onClick:c=>{c.stopPropagation(),Q()},disabled:b===1},Math.round(b*100),"%"))),z&&n.createElement(C,{title:r?"Exporter":"Exporter le graphique"},n.createElement(w,{size:"small",icon:n.createElement(G,null),onClick:c=>{c.stopPropagation(),Y()},loading:U},!r&&"Exporter"))),te=l.useMemo(()=>{if(x==="modal")return{height:h.height,width:"100%"};const c=r?250:p?300:h.height,L=r?400:p?500:Math.max(600,h.height*1.5);return{height:e?L*b:c,width:"100%"}},[e,x,h.height,r,p,b]),ne=c=>a?n.createElement("div",{className:`chart-container ${e?"expanded":""} ${r?"mobile":""} ${p?"tablet":""}`,style:{height:c,width:"100%",position:"relative",overflow:e?"auto":"hidden",transform:e?`scale(${b})`:"scale(1)",transformOrigin:"top left",transition:"all 0.3s ease"}},n.cloneElement(a,{...a.props,height:c,data:o,enhanced:e,expanded:e,isModal:!1,isMobile:r,isTablet:p,isDesktop:I,zoomLevel:b,chartConfig:{...h,showAllLabels:!r||e,labelInterval:r&&!e?1:0,maxBarSize:r?30:40,strokeWidth:r?2:3,dotSize:r?4:6,fontSize:r?10:12,touchEnabled:r,gestureHandling:r?"cooperative":"auto"},...M})):null,ae=l.useMemo(()=>({cursor:g?"pointer":"default",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",borderRadius:r?"8px":"12px",...e&&{position:"relative",zIndex:10,boxShadow:r?"0 4px 16px rgba(0,0,0,0.12)":"0 8px 24px rgba(0,0,0,0.15)",transform:r?"none":"translateY(-2px)"},...r&&{margin:"8px 0"},...v}),[g,e,r,v]);return n.createElement(n.Fragment,null,n.createElement(se,{...s,title:t,className:`unified-chart-expansion ${e?"expanded":""} ${r?"mobile":""} ${p?"tablet":""} ${u}`,extra:n.createElement(V,{size:r?"small":"middle"},ee(),n.createElement(C,{title:g?x==="modal"?r?"Plein écran":"Ouvrir en plein écran":e?"Réduire":"Agrandir":"Expansion désactivée dans les paramètres"},n.createElement(w,{icon:x==="modal"?n.createElement(Z,null):e?n.createElement(be,null):n.createElement(ve,null),onClick:c=>{m(c)},type:e?"primary":"default",loading:U,disabled:!g,className:"expand-button",size:r?"small":"middle",style:{minWidth:r?"32px":"40px",minHeight:r?"32px":"40px"}}))),hoverable:g&&!r,style:ae,onClick:r?void 0:X,styles:{body:{padding:r?"12px":"16px",...(A=s.styles)==null?void 0:A.body}}},ne(te.height)),n.createElement(K,{visible:R,onClose:J,title:t,data:o,chartType:i,chartConfig:h,exportEnabled:z},a))};export{Ve as U,Te as g,Ue as u};
