/**
 * Comprehensive Production Dashboard Integration Test
 * Tests all enhanced features: GraphQL, data sources, monitoring, and sync
 */

const testComprehensiveIntegration = async () => {
  const GRAPHQL_ENDPOINT = 'http://localhost:5000/api/graphql';
  let passedTests = 0;
  let totalTests = 0;

  const runTest = async (name, testFn) => {
    totalTests++;
    console.log(`\n🧪 ${name}...`);
    try {
      const result = await testFn();
      if (result) {
        console.log(`✅ ${name} - PASSED`);
        passedTests++;
        return true;
      } else {
        console.log(`❌ ${name} - FAILED`);
        return false;
      }
    } catch (error) {
      console.log(`❌ ${name} - ERROR: ${error.message}`);
      return false;
    }
  };

  // Test 1: Enhanced Production Chart with DataSource
  await runTest('Enhanced Production Chart Query', async () => {
    const query = `
      query TestEnhancedProductionChart {
        enhancedGetProductionChart(filters: { dateRangeType: "day" }) {
          data {
            Date_Insert_Day
            Total_Good_Qty_Day
            Total_Rejects_Qty_Day
            OEE_Day
          }
          dataSource
        }
      }
    `;
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    const result = await response.json();
    
    if (result.errors) {
      console.log('   Errors:', result.errors.map(e => e.message));
      return false;
    }
    
    const data = result.data?.enhancedGetProductionChart;
    console.log(`   📊 Data Source: ${data?.dataSource}`);
    console.log(`   📈 Records: ${data?.data?.length || 0}`);
    
    return data && data.dataSource && data.data;
  });

  // Test 2: Enhanced Production Sidecards with DataSource
  await runTest('Enhanced Production Sidecards Query', async () => {
    const query = `
      query TestEnhancedProductionSidecards {
        enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) {
          goodqty
          rejetqty
          dataSource
        }
      }
    `;
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    const result = await response.json();
    
    if (result.errors) {
      console.log('   Errors:', result.errors.map(e => e.message));
      return false;
    }
    
    const data = result.data?.enhancedGetProductionSidecards;
    console.log(`   📊 Data Source: ${data?.dataSource}`);
    console.log(`   ✅ Good Qty: ${data?.goodqty || 0}`);
    console.log(`   ❌ Reject Qty: ${data?.rejetqty || 0}`);
    
    return data && data.dataSource !== undefined;
  });

  // Test 3: Enhanced Machine Performance with DataSource
  await runTest('Enhanced Machine Performance Query', async () => {
    const query = `
      query TestEnhancedMachinePerformance {
        enhancedGetMachinePerformance(filters: { dateRangeType: "day" }) {
          data {
            Machine_Name
            production
            oee
            availability
          }
          dataSource
        }
      }
    `;
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    const result = await response.json();
    
    if (result.errors) {
      console.log('   Errors:', result.errors.map(e => e.message));
      return false;
    }
    
    const data = result.data?.enhancedGetMachinePerformance;
    console.log(`   📊 Data Source: ${data?.dataSource}`);
    console.log(`   🏭 Machines: ${data?.data?.length || 0}`);
    
    return data && data.dataSource && data.data;
  });

  // Test 4: Data Sync Statistics
  await runTest('Data Sync Statistics Query', async () => {
    const query = `
      query TestDataSyncStats {
        getDataSyncStats {
          isRunning
          lastSync
          totalSyncs
          successfulSyncs
          failedSyncs
          successRate
        }
      }
    `;
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    const result = await response.json();
    
    if (result.errors) {
      console.log('   Errors:', result.errors.map(e => e.message));
      return false;
    }
    
    const stats = result.data?.getDataSyncStats;
    console.log(`   🔄 Sync Running: ${stats?.isRunning}`);
    console.log(`   📊 Total Syncs: ${stats?.totalSyncs || 0}`);
    console.log(`   ✅ Success Rate: ${stats?.successRate || 0}%`);
    
    return stats !== undefined;
  });

  // Test 5: Data Source Fallback Resilience
  await runTest('Data Source Fallback Resilience', async () => {
    // Test multiple queries rapidly to ensure consistent fallback behavior
    const queries = [
      {
        name: 'enhancedGetProductionChart',
        query: `query { enhancedGetProductionChart(filters: { dateRangeType: "day" }) { dataSource data { Date_Insert_Day } } }`
      },
      {
        name: 'enhancedGetProductionSidecards',
        query: `query { enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) { dataSource goodqty } }`
      },
      {
        name: 'enhancedGetMachinePerformance',
        query: `query { enhancedGetMachinePerformance(filters: { dateRangeType: "day" }) { dataSource data { Machine_Name } } }`
      }
    ];
    
    const results = [];
    
    for (const { name, query } of queries) {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
      });
      
      const result = await response.json();
      
      if (!result.errors) {
        const dataSource = result.data?.[name]?.dataSource;
        if (dataSource) {
          results.push(dataSource);
        }
      } else {
        console.log(`   Error in ${name}:`, result.errors[0]?.message);
      }
    }
    
    // Check that all queries use the same data source (consistency)
    const uniqueSources = [...new Set(results)];
    console.log(`   📊 Data Sources Used: ${uniqueSources.join(', ')}`);
    console.log(`   🔄 Consistency: ${uniqueSources.length === 1 ? 'CONSISTENT' : 'INCONSISTENT'}`);
    console.log(`   📈 Successful Queries: ${results.length}/${queries.length}`);
    
    return results.length >= 2 && uniqueSources.length === 1; // Allow for some tolerance
  });

  // Test 6: Performance Benchmarking
  await runTest('Performance Benchmarking', async () => {
    const startTime = Date.now();
    
    // Run all production queries in parallel
    const queries = [
      `query { enhancedGetProductionChart(filters: {}) { dataSource data { Date_Insert_Day } } }`,
      `query { enhancedGetProductionSidecards(filters: {}) { dataSource goodqty } }`,
      `query { enhancedGetMachinePerformance(filters: {}) { dataSource data { Machine_Name } } }`
    ];
    
    const fetch = (await import('node-fetch')).default;
    const promises = queries.map(query =>
      fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
      }).then(r => r.json())
    );
    
    const results = await Promise.all(promises);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    const successfulQueries = results.filter(r => !r.errors).length;
    const avgTime = totalTime / queries.length;
    
    console.log(`   ⏱️ Total Time: ${totalTime}ms`);
    console.log(`   📊 Average Query Time: ${avgTime.toFixed(1)}ms`);
    console.log(`   ✅ Successful Queries: ${successfulQueries}/${queries.length}`);
    
    // Performance should be under 2 seconds total, all queries successful
    return totalTime < 2000 && successfulQueries === queries.length;
  });

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPREHENSIVE INTEGRATION TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! 🎉');
    console.log('✅ Production Dashboard Elasticsearch integration is fully operational');
    console.log('✅ Data source fallback system is working correctly');
    console.log('✅ Real-time monitoring and sync are functional');
    console.log('✅ Performance benchmarks are meeting requirements');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the implementation.');
  }
  
  return passedTests === totalTests;
};

// Execute comprehensive test
testComprehensiveIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('🚨 Test execution failed:', error);
    process.exit(1);
  });
