/**
 * 🛑 Elasticsearch Stops Service
 * 
 * Phase 3: Elasticsearch Optimization for Arrets Dashboard
 * Implements advanced stop analysis, downtime analytics, and real-time indexing
 * for manufacturing machine stops and downtime events
 */

import { esClient } from '../config/elasticsearch.js';
import redisConfig from '../config/redisConfig.js';

class ElasticsearchStopsService {
  constructor() {
    this.indexName = 'machine-stops';
    this.redisClient = null;
    this.redisConfig = redisConfig;
    this.cachePrefix = 'es:stops:';
    this.cacheTTL = 300; // 5 minutes
  }

  /**
   * Initialize the service
   */
  async initialize() {
    try {
      // Initialize Redis if not already connected
      if (!this.redisConfig.isConnected) {
        await this.redisConfig.initialize();
      }
      this.redisClient = this.redisConfig.getClient();
      console.log('✅ Elasticsearch Stops Service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Elasticsearch Stops Service:', error);
      console.warn('⚠️ Service will operate without Redis caching');
      return false;
    }
  }

  /**
   * Index machine stops data from machine_stop_table_mould
   * @param {Array} stopsData - Array of stop records
   */
  async indexStopsData(stopsData) {
    try {
      if (!Array.isArray(stopsData) || stopsData.length === 0) {
        return { success: false, message: 'No data to index' };
      }

      const body = [];
      
      for (const record of stopsData) {
        // Index operation
        body.push({
          index: {
            _index: this.indexName,
            _id: `${record.id || record.machine_id}_${record.date_insert || Date.now()}`
          }
        });
        
        // Document data - optimized for stop analysis
        body.push({
          stopId: record.id,
          machineId: record.machine_id,
          machineName: record.machine_name || `Machine ${record.machine_id}`,
          stopCode: record.stop_code || 'UNKNOWN',
          stopDescription: record.stop_description || 'No description',
          stopCategory: record.stop_category || 'uncategorized',
          severity: this.calculateSeverity(record),
          
          // Timing data
          dateInsert: record.date_insert,
          startTime: record.start_time,
          endTime: record.end_time,
          duration: this.calculateDuration(record.start_time, record.end_time),
          
          // Context data
          operator: record.operator || 'unknown',
          shift: record.shift || 'unknown',
          partNumber: record.part_number || '',
          orderNumber: record.order_number || '',
          
          // Resolution tracking
          resolution: {
            resolved: record.resolved || false,
            resolvedBy: record.resolved_by || null,
            resolvedAt: record.resolved_at || null,
            notes: record.resolution_notes || ''
          },
          
          // Maintenance flags
          maintenance: {
            required: record.maintenance_required || false,
            type: record.maintenance_type || null,
            priority: record.maintenance_priority || 'normal'
          },
          
          // Analytics fields
          impactScore: this.calculateImpactScore(record),
          downtimeCategory: this.categorizeDowntime(record),
          
          // Indexing metadata
          indexedAt: new Date().toISOString(),
          dataSource: 'machine_stop_table_mould'
        });
      }

      const response = await esClient.bulk({ body });
      
      if (response.errors) {
        console.error('❌ Elasticsearch bulk indexing errors:', response.items.filter(item => item.index.error));
        return { success: false, message: 'Bulk indexing completed with errors', errors: response.errors };
      }

      console.log(`✅ Indexed ${stopsData.length} stop records to Elasticsearch`);
      
      // Clear related cache entries
      await this.clearStopsCache();
      
      return { success: true, indexed: stopsData.length };
      
    } catch (error) {
      console.error('❌ Error indexing stops data:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Advanced stops analytics for Arrets Dashboard
   * @param {Object} filters - Query filters
   * @param {Object} options - Query options
   */
  async getStopsAnalytics(filters = {}, options = {}) {
    try {
      const cacheKey = `${this.cachePrefix}analytics:${JSON.stringify({ filters, options })}`;
      
      // Try cache first
      if (this.redisClient) {
        const cached = await this.redisClient.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const query = this.buildStopsQuery(filters);
      const aggregations = this.buildStopsAggregations(filters);
      
      const searchBody = {
        query,
        aggs: aggregations,
        size: options.size || 100,
        from: options.from || 0,
        sort: options.sort || [{ dateInsert: { order: 'desc' } }]
      };

      const response = await esClient.search({
        index: this.indexName,
        body: searchBody
      });

      const result = {
        total: response.hits.total.value,
        stops: response.hits.hits.map(hit => ({
          id: hit._id,
          ...hit._source
        })),
        analytics: this.processStopsAggregations(response.aggregations),
        query_time_ms: response.took
      };

      // Cache the result
      if (this.redisClient) {
        await this.redisClient.setex(cacheKey, this.cacheTTL, JSON.stringify(result));
      }

      return result;
      
    } catch (error) {
      console.error('❌ Error querying stops analytics:', error);
      throw error;
    }
  }

  /**
   * Real-time downtime dashboard data
   * Optimized for Arrets Dashboard performance
   */
  async getDowntimeDashboard(machineIds = [], dateRange = {}) {
    try {
      const cacheKey = `${this.cachePrefix}dashboard:${machineIds.join(',')}_${JSON.stringify(dateRange)}`;
      
      // Try cache first
      if (this.redisClient) {
        const cached = await this.redisClient.get(cacheKey);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const query = this.buildDashboardQuery(machineIds, dateRange);
      
      const searchBody = {
        query,
        aggs: {
          // Machine downtime summary
          machines: {
            terms: { field: 'machineId', size: 50 },
            aggs: {
              total_stops: { value_count: { field: 'stopId' } },
              total_downtime: { sum: { field: 'duration' } },
              avg_stop_duration: { avg: { field: 'duration' } },
              top_stop_codes: {
                terms: { field: 'stopCode', size: 5 },
                aggs: {
                  total_duration: { sum: { field: 'duration' } }
                }
              }
            }
          },
          
          // Stop categories analysis
          stop_categories: {
            terms: { field: 'stopCategory', size: 20 },
            aggs: {
              total_stops: { value_count: { field: 'stopId' } },
              total_duration: { sum: { field: 'duration' } },
              avg_duration: { avg: { field: 'duration' } }
            }
          },
          
          // Daily downtime trends
          daily_downtime: {
            date_histogram: {
              field: 'dateInsert',
              calendar_interval: 'day'
            },
            aggs: {
              total_stops: { value_count: { field: 'stopId' } },
              total_downtime: { sum: { field: 'duration' } }
            }
          },
          
          // Severity distribution
          severity_distribution: {
            terms: { field: 'severity' },
            aggs: {
              total_stops: { value_count: { field: 'stopId' } },
              total_duration: { sum: { field: 'duration' } }
            }
          },
          
          // Resolution status
          resolution_status: {
            terms: { field: 'resolution.resolved' },
            aggs: {
              count: { value_count: { field: 'stopId' } }
            }
          }
        },
        size: 0 // Only aggregations
      };

      const response = await esClient.search({
        index: this.indexName,
        body: searchBody
      });

      const result = {
        machines: response.aggregations.machines.buckets.map(bucket => ({
          machineId: bucket.key,
          totalStops: bucket.total_stops.value,
          totalDowntime: Math.round(bucket.total_downtime.value),
          avgStopDuration: Math.round(bucket.avg_stop_duration.value * 100) / 100,
          topStopCodes: bucket.top_stop_codes.buckets.map(code => ({
            code: code.key,
            count: code.doc_count,
            duration: Math.round(code.total_duration.value)
          }))
        })),
        stopCategories: response.aggregations.stop_categories.buckets.map(bucket => ({
          category: bucket.key,
          totalStops: bucket.total_stops.value,
          totalDuration: Math.round(bucket.total_duration.value),
          avgDuration: Math.round(bucket.avg_duration.value * 100) / 100
        })),
        dailyTrends: response.aggregations.daily_downtime.buckets.map(bucket => ({
          date: bucket.key_as_string,
          totalStops: bucket.total_stops.value,
          totalDowntime: Math.round(bucket.total_downtime.value)
        })),
        severityDistribution: response.aggregations.severity_distribution.buckets.map(bucket => ({
          severity: bucket.key,
          count: bucket.total_stops.value,
          duration: Math.round(bucket.total_duration.value)
        })),
        resolutionStatus: {
          resolved: response.aggregations.resolution_status.buckets.find(b => b.key === true)?.count.value || 0,
          unresolved: response.aggregations.resolution_status.buckets.find(b => b.key === false)?.count.value || 0
        },
        query_time_ms: response.took
      };

      // Cache for 2 minutes
      if (this.redisClient) {
        await this.redisClient.setex(cacheKey, 120, JSON.stringify(result));
      }

      return result;
      
    } catch (error) {
      console.error('❌ Error getting downtime dashboard data:', error);
      throw error;
    }
  }

  /**
   * Build stops query from filters
   */
  buildStopsQuery(filters) {
    const query = { bool: { must: [] } };

    if (filters.machineIds && filters.machineIds.length > 0) {
      query.bool.must.push({ terms: { machineId: filters.machineIds } });
    }

    if (filters.dateRange) {
      const dateFilter = { range: { dateInsert: {} } };
      if (filters.dateRange.start) dateFilter.range.dateInsert.gte = filters.dateRange.start;
      if (filters.dateRange.end) dateFilter.range.dateInsert.lte = filters.dateRange.end;
      query.bool.must.push(dateFilter);
    }

    if (filters.stopCategory) {
      query.bool.must.push({ term: { stopCategory: filters.stopCategory } });
    }

    if (filters.severity) {
      query.bool.must.push({ term: { severity: filters.severity } });
    }

    if (filters.resolved !== undefined) {
      query.bool.must.push({ term: { 'resolution.resolved': filters.resolved } });
    }

    return query.bool.must.length > 0 ? query : { match_all: {} };
  }

  /**
   * Build dashboard query
   */
  buildDashboardQuery(machineIds, dateRange) {
    const query = { bool: { must: [] } };

    if (machineIds.length > 0) {
      query.bool.must.push({ terms: { machineId: machineIds } });
    }

    if (dateRange.start || dateRange.end) {
      const dateFilter = { range: { dateInsert: {} } };
      if (dateRange.start) dateFilter.range.dateInsert.gte = dateRange.start;
      if (dateRange.end) dateFilter.range.dateInsert.lte = dateRange.end;
      query.bool.must.push(dateFilter);
    }

    return query.bool.must.length > 0 ? query : { match_all: {} };
  }

  /**
   * Build stops aggregations
   */
  buildStopsAggregations(filters) {
    return {
      total_stops: { value_count: { field: 'stopId' } },
      total_downtime: { sum: { field: 'duration' } },
      avg_duration: { avg: { field: 'duration' } },
      max_duration: { max: { field: 'duration' } },
      stop_categories: {
        terms: { field: 'stopCategory', size: 10 }
      }
    };
  }

  /**
   * Process stops aggregations
   */
  processStopsAggregations(aggregations) {
    if (!aggregations) return {};

    return {
      totalStops: aggregations.total_stops?.value || 0,
      totalDowntime: Math.round(aggregations.total_downtime?.value || 0),
      avgDuration: Math.round((aggregations.avg_duration?.value || 0) * 100) / 100,
      maxDuration: Math.round(aggregations.max_duration?.value || 0),
      categories: aggregations.stop_categories?.buckets.map(bucket => ({
        category: bucket.key,
        count: bucket.doc_count
      })) || []
    };
  }

  /**
   * Calculate stop severity based on duration and impact
   */
  calculateSeverity(record) {
    const duration = this.calculateDuration(record.start_time, record.end_time);
    
    if (duration > 240) return 'critical'; // > 4 hours
    if (duration > 60) return 'high';      // > 1 hour
    if (duration > 15) return 'medium';    // > 15 minutes
    return 'low';
  }

  /**
   * Calculate duration in minutes
   */
  calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return 0;
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    return Math.round((end - start) / (1000 * 60)); // minutes
  }

  /**
   * Calculate impact score for prioritization
   */
  calculateImpactScore(record) {
    const duration = this.calculateDuration(record.start_time, record.end_time);
    const baseScore = duration * 0.1; // Base score from duration
    
    // Adjust based on stop category
    const categoryMultipliers = {
      'mechanical': 1.5,
      'electrical': 1.3,
      'quality': 1.2,
      'material': 1.1,
      'operator': 0.8,
      'planned': 0.5
    };
    
    const multiplier = categoryMultipliers[record.stop_category] || 1.0;
    return Math.round(baseScore * multiplier * 100) / 100;
  }

  /**
   * Categorize downtime for analysis
   */
  categorizeDowntime(record) {
    const duration = this.calculateDuration(record.start_time, record.end_time);
    
    if (duration < 5) return 'micro';      // < 5 minutes
    if (duration < 30) return 'short';     // < 30 minutes
    if (duration < 120) return 'medium';   // < 2 hours
    return 'extended';                     // >= 2 hours
  }

  /**
   * Clear stops-related cache entries
   */
  async clearStopsCache() {
    if (!this.redisClient) return;
    
    try {
      const keys = await this.redisClient.keys(`${this.cachePrefix}*`);
      if (keys.length > 0) {
        await this.redisClient.del(...keys);
        console.log(`🧹 Cleared ${keys.length} stops cache entries`);
      }
    } catch (error) {
      console.error('⚠️ Error clearing stops cache:', error);
    }
  }
}

export default new ElasticsearchStopsService();
