import db from '../db.js';
import { executeQuery } from '../utils/dbUtils.js';
import emailNotificationService from './EmailNotificationService.js';

/**
 * EmailRetryService - Manages email delivery retry logic and queue management
 * Handles failed email deliveries with exponential backoff and retry mechanisms
 */
class EmailRetryService {
  constructor() {
    this.isProcessing = false;
    this.retryIntervals = [1, 5, 15, 60]; // Minutes: 1min, 5min, 15min, 1hour
    this.maxRetries = 3;
    
    // Start the retry processor
    this.startRetryProcessor();
  }

  /**
   * Add email to retry queue
   * @param {Object} emailData - Email data for retry
   * @returns {Promise<boolean>} Success status
   */
  async addToRetryQueue(emailData) {
    try {
      const {
        notificationId,
        userId,
        emailAddress,
        subject,
        htmlContent,
        textContent,
        priority = 'medium',
        error = null
      } = emailData;

      console.log(`📧 Adding email to retry queue for user ${userId}`);

      // Calculate next retry time (1 minute from now for first retry)
      const nextRetryAt = new Date();
      nextRetryAt.setMinutes(nextRetryAt.getMinutes() + this.retryIntervals[0]);

      const query = `
        INSERT INTO email_retry_queue 
        (notification_id, user_id, email_address, email_subject, email_content_html, 
         email_content_text, priority, retry_count, max_retries, next_retry_at, last_error, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?, ?, 'pending')
      `;

      const result = await executeQuery(query, [
        notificationId,
        userId,
        emailAddress,
        subject,
        htmlContent,
        textContent,
        priority,
        this.maxRetries,
        nextRetryAt,
        error
      ]);

      if (result.success) {
        console.log(`✅ Email added to retry queue (ID: ${result.insertId})`);
        return true;
      } else {
        console.error('❌ Failed to add email to retry queue:', result.error);
        return false;
      }

    } catch (error) {
      console.error('❌ Error adding email to retry queue:', error);
      return false;
    }
  }

  /**
   * Process retry queue - send pending emails
   * @returns {Promise<Object>} Processing results
   */
  async processRetryQueue() {
    if (this.isProcessing) {
      console.log('⏳ Retry queue already being processed, skipping...');
      return { processed: 0, success: 0, failed: 0 };
    }

    this.isProcessing = true;
    console.log('🔄 Processing email retry queue...');

    try {
      // Get pending emails ready for retry
      const query = `
        SELECT * FROM email_retry_queue 
        WHERE status = 'pending' 
        AND next_retry_at <= NOW()
        AND retry_count < max_retries
        ORDER BY priority DESC, next_retry_at ASC
        LIMIT 50
      `;

      const result = await executeQuery(query);

      if (!result.success) {
        console.error('❌ Failed to get retry queue:', result.error);
        return { processed: 0, success: 0, failed: 0 };
      }

      const pendingEmails = result.data;
      console.log(`📧 Found ${pendingEmails.length} emails to retry`);

      let processed = 0;
      let success = 0;
      let failed = 0;

      for (const email of pendingEmails) {
        try {
          processed++;
          
          // Mark as processing
          await this.updateRetryStatus(email.id, 'processing');

          // Attempt to send email
          const sendResult = await this.attemptEmailSend(email);

          if (sendResult.success) {
            success++;
            // Mark as completed and log delivery
            await this.updateRetryStatus(email.id, 'completed');
            await this.logEmailDelivery(email, 'sent', sendResult.messageId);
            console.log(`✅ Retry email sent successfully (Queue ID: ${email.id})`);
          } else {
            failed++;
            // Update retry count and schedule next retry
            await this.handleRetryFailure(email, sendResult.error);
            console.log(`❌ Retry email failed (Queue ID: ${email.id}): ${sendResult.error}`);
          }

        } catch (error) {
          failed++;
          console.error(`❌ Error processing retry email ${email.id}:`, error);
          await this.handleRetryFailure(email, error.message);
        }
      }

      console.log(`✅ Retry queue processing complete: ${processed} processed, ${success} success, ${failed} failed`);
      return { processed, success, failed };

    } catch (error) {
      console.error('❌ Error processing retry queue:', error);
      return { processed: 0, success: 0, failed: 0 };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Attempt to send email from retry queue
   * @param {Object} email - Email data from retry queue
   * @returns {Promise<Object>} Send result
   */
  async attemptEmailSend(email) {
    try {
      console.log(`📧 Attempting to send retry email to ${email.email_address}`);

      const mailOptions = {
        from: `"LOCQL Performance System" <<EMAIL>>`,
        to: email.email_address,
        subject: email.email_subject,
        text: email.email_content_text,
        html: email.email_content_html,
        priority: email.priority === 'critical' ? 'high' : 'normal'
      };

      // Use the email service transporter directly
      await emailNotificationService.initializationPromise;
      const result = await emailNotificationService.transporter.sendMail(mailOptions);

      return {
        success: true,
        messageId: result.messageId,
        response: result.response
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle retry failure - update retry count and schedule next retry
   * @param {Object} email - Email data from retry queue
   * @param {string} error - Error message
   */
  async handleRetryFailure(email, error) {
    try {
      const newRetryCount = email.retry_count + 1;

      if (newRetryCount >= email.max_retries) {
        // Max retries reached, mark as failed
        await this.updateRetryStatus(email.id, 'failed', error);
        await this.logEmailDelivery(email, 'failed', null, error);
        console.log(`❌ Email retry failed permanently (Queue ID: ${email.id})`);
      } else {
        // Schedule next retry with exponential backoff
        const retryInterval = this.retryIntervals[Math.min(newRetryCount, this.retryIntervals.length - 1)];
        const nextRetryAt = new Date();
        nextRetryAt.setMinutes(nextRetryAt.getMinutes() + retryInterval);

        const query = `
          UPDATE email_retry_queue 
          SET retry_count = ?, next_retry_at = ?, last_error = ?, status = 'pending'
          WHERE id = ?
        `;

        await executeQuery(query, [newRetryCount, nextRetryAt, error, email.id]);
        console.log(`⏰ Email retry scheduled for ${nextRetryAt.toISOString()} (Queue ID: ${email.id})`);
      }

    } catch (updateError) {
      console.error('❌ Error handling retry failure:', updateError);
    }
  }

  /**
   * Update retry queue status
   * @param {number} queueId - Queue entry ID
   * @param {string} status - New status
   * @param {string} error - Error message (optional)
   */
  async updateRetryStatus(queueId, status, error = null) {
    try {
      const query = `
        UPDATE email_retry_queue 
        SET status = ?, last_error = ?
        WHERE id = ?
      `;

      await executeQuery(query, [status, error, queueId]);

    } catch (updateError) {
      console.error('❌ Error updating retry status:', updateError);
    }
  }

  /**
   * Log email delivery attempt
   * @param {Object} email - Email data
   * @param {string} status - Delivery status
   * @param {string} messageId - Email message ID (if successful)
   * @param {string} error - Error message (if failed)
   */
  async logEmailDelivery(email, status, messageId = null, error = null) {
    try {
      const query = `
        INSERT INTO notification_delivery_log 
        (notification_id, user_id, delivery_method, email_status, email_attempts, 
         email_message_id, email_error, delivery_time)
        VALUES (?, ?, 'email', ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        email_status = VALUES(email_status),
        email_attempts = email_attempts + 1,
        email_message_id = VALUES(email_message_id),
        email_error = VALUES(email_error)
      `;

      await executeQuery(query, [
        email.notification_id,
        email.user_id,
        status,
        email.retry_count + 1,
        messageId,
        error
      ]);

    } catch (logError) {
      console.error('❌ Error logging email delivery:', logError);
    }
  }

  /**
   * Start the retry processor (runs every 2 minutes)
   */
  startRetryProcessor() {
    console.log('🚫 Email retry processor is DISABLED for debugging notification system');
    console.log('🔧 To re-enable, remove the return statement in EmailRetryService.startRetryProcessor()');
    return; // TEMPORARILY DISABLED FOR DEBUGGING

    console.log('🚀 Starting email retry processor...');

    // Process immediately on startup
    setTimeout(() => this.processRetryQueue(), 5000);

    // Then process every 2 minutes
    setInterval(() => {
      this.processRetryQueue();
    }, 2 * 60 * 1000); // 2 minutes
  }

  /**
   * Get retry queue statistics
   * @returns {Promise<Object>} Queue statistics
   */
  async getRetryQueueStats() {
    try {
      const query = `
        SELECT 
          status,
          priority,
          COUNT(*) as count,
          MIN(created_at) as oldest,
          MAX(created_at) as newest
        FROM email_retry_queue 
        GROUP BY status, priority
        ORDER BY status, priority
      `;

      const result = await executeQuery(query);

      if (result.success) {
        return {
          success: true,
          stats: result.data,
          isProcessing: this.isProcessing
        };
      } else {
        return { success: false, error: result.error };
      }

    } catch (error) {
      console.error('❌ Error getting retry queue stats:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Clean up old completed/failed entries from retry queue
   * @param {number} daysOld - Remove entries older than this many days
   * @returns {Promise<number>} Number of entries removed
   */
  async cleanupRetryQueue(daysOld = 7) {
    try {
      console.log(`🧹 Cleaning up retry queue entries older than ${daysOld} days...`);

      const query = `
        DELETE FROM email_retry_queue 
        WHERE status IN ('completed', 'failed', 'cancelled')
        AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `;

      const result = await executeQuery(query, [daysOld]);

      if (result.success) {
        console.log(`✅ Cleaned up ${result.affectedRows} old retry queue entries`);
        return result.affectedRows;
      } else {
        console.error('❌ Failed to cleanup retry queue:', result.error);
        return 0;
      }

    } catch (error) {
      console.error('❌ Error cleaning up retry queue:', error);
      return 0;
    }
  }
}

// Create and export singleton instance
const emailRetryService = new EmailRetryService();
export default emailRetryService;
