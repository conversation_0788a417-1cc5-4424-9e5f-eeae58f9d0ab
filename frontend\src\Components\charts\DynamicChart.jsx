import React, { useMemo } from 'react';
import { Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { useSettings } from '../../hooks/useSettings';
import { useUnifiedChartConfig } from '../../hooks/useUnifiedChartConfig';

/**
 * Dynamic Chart Component
 * Renders different chart types based on user settings or props
 * Suitable for data that works well in multiple visualization formats
 */
const DynamicChart = ({
  data = [],
  title,
  dataKey,
  color,
  chartType: propChartType, // Override setting if provided
  allowedTypes = ['bar', 'line'], // Restrict which types are allowed
  xAxisKey = 'name',
  yAxisLabel,
  tooltipLabel,
  loading = false,
  height = 300,
  ...otherProps
}) => {
  // Get settings for chart configuration
  const { charts, theme } = useSettings();
  
  // Enhanced chart configuration
  const enhancedChartConfig = useUnifiedChartConfig({
    charts,
    theme
  });

  // Determine chart type to use
  const chartType = useMemo(() => {
    // 1. Use prop override if provided and allowed
    if (propChartType && allowedTypes.includes(propChartType)) {
      return propChartType;
    }

    // 2. Fall back to first allowed type (defaultType setting removed)
    return allowedTypes[0] || 'bar';
  }, [propChartType, allowedTypes]);

  // Prepare chart props
  const chartProps = {
    data,
    margin: enhancedChartConfig.getChartMargins(),
    ...otherProps
  };

  // Render appropriate chart type
  const renderChart = () => {
    switch (chartType) {
      case 'line':
        return (
          <LineChart {...chartProps}>
            <CartesianGrid {...enhancedChartConfig.getGridConfig()} />
            <XAxis 
              dataKey={xAxisKey} 
              {...enhancedChartConfig.getAxisConfig()} 
            />
            <YAxis 
              {...enhancedChartConfig.getAxisConfig()}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            <Tooltip 
              {...enhancedChartConfig.getTooltipConfig()}
              formatter={(value) => [value, tooltipLabel || dataKey]}
            />
            {charts.showLegend && <Legend {...enhancedChartConfig.getLegendConfig()} />}
            <Line
              type="monotone"
              dataKey={dataKey}
              name={tooltipLabel || title}
              {...enhancedChartConfig.getLineElementConfig(color)}
            />
          </LineChart>
        );

      case 'bar':
      default:
        return (
          <BarChart {...chartProps}>
            <CartesianGrid {...enhancedChartConfig.getGridConfig()} />
            <XAxis 
              dataKey={xAxisKey} 
              {...enhancedChartConfig.getAxisConfig()} 
            />
            <YAxis 
              {...enhancedChartConfig.getAxisConfig()}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            <Tooltip 
              {...enhancedChartConfig.getTooltipConfig()}
              formatter={(value) => [value, tooltipLabel || dataKey]}
            />
            {charts.showLegend && <Legend {...enhancedChartConfig.getLegendConfig()} />}
            <Bar
              dataKey={dataKey}
              name={tooltipLabel || title}
              {...enhancedChartConfig.getBarElementConfig(color)}
            />
          </BarChart>
        );
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: height,
        fontSize: '14px',
        color: '#666'
      }}>
        Chargement...
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: height,
        fontSize: '14px',
        color: '#666'
      }}>
        Aucune donnée disponible
      </div>
    );
  }

  return (
    <div style={{ width: '100%', height: height }}>
      <ResponsiveContainer {...enhancedChartConfig.getResponsiveContainerProps()}>
        {renderChart()}
      </ResponsiveContainer>
    </div>
  );
};

export default DynamicChart;
