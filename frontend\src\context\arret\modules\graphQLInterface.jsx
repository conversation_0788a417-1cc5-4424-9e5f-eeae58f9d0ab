/**
 * GraphQL Interface Module for Queued Data Loading
 * Provides structured GraphQL queries organized by priority for progressive loading
 */
export const useGraphQLInterface = (fetchGraphQL) => {
  
  const graphQLInterface = {
    // Priority 1: Essential data (stats cards)
    async getEssentialData(filters) {
      
      const sidecardsData = await fetchGraphQL(`
        query($filters: StopFilterInput) {
          getStopSidecards(filters: $filters) {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `, { filters });
      
      return {
        sidecards: sidecardsData?.getStopSidecards,
        priority: 1,
        loadingState: 'essentialLoading'
      };
    },

    // Priority 2: Performance metrics data
    async getPerformanceData(filters) {
      // Fetch stop data to calculate performance metrics using enhanced query
      const stopsData = await fetchGraphQL(`
        query($filters: EnhancedFilterInput) {
          enhancedGetAllMachineStops(filters: $filters) {
            stops {
              startTime
              endTime
              duration
              machineName
              stopCode
            }
            total
            dataSource
          }
        }
      `, { filters });

      const stopsResult = stopsData?.enhancedGetAllMachineStops;
      const stops = stopsResult?.stops || [];
      
      // Calculate performance metrics
      let mttr = 0;
      let mtbf = 0;
      let doper = 0;
      
      if (stops.length > 0) {
        // MTTR: Average repair time (duration of stops)
        const totalDuration = stops.reduce((sum, stop) => {
          if (stop.duration && stop.duration > 0) {
            return sum + parseFloat(stop.duration);
          } else if (stop.startTime && stop.endTime) {
            // Calculate duration from start and end times if duration is not available
            try {
              const startTime = new Date(stop.startTime);
              const endTime = new Date(stop.endTime);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return sum + durationMinutes;
              }
            } catch (error) {
              console.warn('Error calculating duration for stop:', stop, error);
            }
          }
          return sum;
        }, 0);
        
        mttr = stops.length > 0 ? totalDuration / stops.length : 0;
        
        // MTBF: Simple approximation - 24 hours / number of stops per day
        // This is a simplified calculation - in reality would need operating hours data
        const totalDays = stops.length > 0 ? 30 : 1; // Assume 30-day period
        const stopsPerDay = stops.length / totalDays;
        mtbf = stopsPerDay > 0 ? (24 * 60) / stopsPerDay : 0;
        
        // DOPER: Availability - simplified calculation
        // Assume 24*60 minutes per day, subtract total downtime
        const totalDowntime = totalDuration;
        const totalTime = totalDays * 24 * 60; // Total minutes in period
        doper = totalTime > 0 ? ((totalTime - totalDowntime) / totalTime) * 100 : 0;
        
        // Ensure reasonable bounds
        mttr = Math.max(0, Math.min(mttr, 1440)); // Max 24 hours
        mtbf = Math.max(0, Math.min(mtbf, 10080)); // Max 1 week
        doper = Math.max(0, Math.min(doper, 100)); // 0-100%
      }
      
      return {
        performance: { 
          mttr: Number(mttr.toFixed(1)), 
          mtbf: Number(mtbf.toFixed(1)), 
          doper: Number(doper.toFixed(1)) 
        },
        priority: 2,
        loadingState: 'essentialLoading'
      };
    },

    // Priority 3: Chart data
    async getChartData(filters) {
      
      const [topStopsData, machineComparisonData, dashboardStats] = await Promise.all([
        fetchGraphQL(`
          query($filters: EnhancedFilterInput) {
            enhancedGetTop5Stops(filters: $filters) {
              reasons {
                reason
                count
                percentage
                totalDuration
              }
              dataSource
            }
          }
        `, { filters }),
        
        fetchGraphQL(`
          query($filters: EnhancedFilterInput) {
            enhancedGetMachineStopComparison(filters: $filters) {
              machines {
                machineName
                totalStops
                totalDuration
              }
              dataSource
            }
          }
        `, { filters }),

        fetchGraphQL(`
          query($filters: EnhancedFilterInput) {
            getDashboardStats(filters: $filters) {
              totalStops
              totalDuration
              averageDuration
              uniqueMachines
              dataSource
            }
          }
        `, { filters })
      ]);
      
      return {
        topStops: (topStopsData?.enhancedGetTop5Stops?.reasons || []).map(reason => ({
          ...reason,
          // Legacy format mapping for charts
          stopName: reason.reason || reason.stopName,
          name: reason.reason || reason.name,
          count: reason.count || 0,
          percentage: reason.percentage || 0
        })),
        machineComparison: (machineComparisonData?.enhancedGetMachineStopComparison?.machines || []).map(machine => ({
          ...machine,
          // Legacy format mapping for charts  
          Machine_Name: machine.machineName || machine.Machine_Name,
          stops: machine.totalStops || machine.stops, // Map totalStops to stops for chart compatibility
          totalStops: machine.totalStops || 0,
          totalDuration: machine.totalDuration || 0
        })),
        dashboardStats: dashboardStats?.getDashboardStats || {},
        priority: 3,
        loadingState: 'detailedLoading'
      };
    },

    // Priority 4: Heavy table data
    async getTableData(filters) {
      
      const tableData = await fetchGraphQL(`
        query($filters: EnhancedFilterInput) {
          enhancedGetAllMachineStops(filters: $filters) {
            stops {
              id
              machineId
              machineName
              stopReason
              stopCode
              startTime
              endTime
              duration
              operator
              description
              category
              severity
            }
            total
            dataSource
          }
        }
      `, { filters });
      
      // Transform GraphQL data to match expected chart format
      const transformedStops = (tableData?.enhancedGetAllMachineStops?.stops || []).map(stop => ({
        // Original GraphQL fields
        ...stop,
        
        // Legacy format mapping for chart compatibility
        Machine_Name: stop.machineName || stop.Machine_Name,
        Debut_Stop: stop.startTime || stop.Debut_Stop,
        Fin_Stop_Time: stop.endTime || stop.Fin_Stop_Time,
        duration_minutes: stop.duration || stop.duration_minutes,
        
        // Additional transformations
        Date_Insert: stop.startTime || stop.Date_Insert, // For date filtering
        Stop_Name: stop.stopReason || stop.stopCode || stop.Stop_Name || 'Non défini'
      }));
      
      console.log('🔄 GraphQL Interface - Transformed table data:', {
        originalCount: tableData?.enhancedGetAllMachineStops?.stops?.length || 0,
        transformedCount: transformedStops.length,
        sampleOriginal: tableData?.enhancedGetAllMachineStops?.stops?.[0] || {},
        sampleTransformed: transformedStops[0] || {},
        dataSource: tableData?.enhancedGetAllMachineStops?.dataSource
      });
      
      return {
        stopsData: transformedStops,
        total: tableData?.enhancedGetAllMachineStops?.total || 0,
        dataSource: tableData?.enhancedGetAllMachineStops?.dataSource,
        priority: 4,
        loadingState: 'detailedLoading'
      };
    },

    // Machine metadata
    async getMachineModels() {
      const data = await fetchGraphQL(`
        query {
          getStopMachineModels {
            model
          }
        }
      `);
      return data?.getStopMachineModels || [];
    },

    async getMachineNames() {
      const data = await fetchGraphQL(`
        query {
          getStopMachineNames {
            Machine_Name
          }
        }
      `);
      return data?.getStopMachineNames || [];
    }
  };

  return graphQLInterface;
};
