import{r as D,ah as H,R as e,d as s,T as U,e as E,B as R,g as i,E as w,f as g,ad as O,ai as C,aj as V,ak as F,S as l}from"./index-y9W4UQPd.js";import{d as m}from"./dayjs.min-D8Lc9v5x.js";import{R as G,a as W,b as Y,r as Q}from"./relativeTime-D5nVX57i.js";import"./fr-DGSiljjZ.js";import{R as J}from"./ReloadOutlined-Cyu5KuEL.js";import{L as x}from"./index-Rdb88Q3H.js";import{R as K}from"./BellOutlined-CN3CDS2V.js";import{R as X}from"./InfoCircleOutlined-DzeHyv3U.js";import{R as I}from"./AlertOutlined-ByJiI9cI.js";import{R as Z}from"./AppstoreOutlined-CD3bDheK.js";import{R as ee}from"./ToolOutlined-bXQU0KMP.js";m.extend(Q);m.locale("fr");const{Text:c}=U,de=()=>{const[z,b]=D.useState(!1),{notifications:n,unreadCount:u,connectionStatus:d,connectionStats:f,markAsRead:h,acknowledgeNotification:k,connect:v,isConnected:p,isConnecting:S,hasError:y}=H();e.useEffect(()=>{console.log("🔔 SSENotificationBell - Connection status changed:",{connectionStatus:d,isConnected:p,isConnecting:S,hasError:y,notificationsCount:n.length,unreadCount:u})},[d,p,S,y,n.length,u]);const A=(t,r)=>{const a=M(r);switch(t){case"alert":case"machine_alert":return e.createElement(I,{style:a});case"maintenance":return e.createElement(ee,{style:a});case"update":return e.createElement(Z,{style:a});case"production":return e.createElement(F,{style:a});case"quality":return e.createElement(I,{style:a});case"info":default:return e.createElement(X,{style:a})}},M=t=>{switch(t){case"critical":return{color:l.ERROR,fontSize:"16px"};case"high":return{color:l.WARNING,fontSize:"15px"};case"medium":return{color:l.PRIMARY_BLUE,fontSize:"14px"};case"low":return{color:l.SUCCESS,fontSize:"14px"};default:return{color:l.PRIMARY_BLUE,fontSize:"14px"}}},_=t=>{switch(t){case"critical":return"red";case"high":return"orange";case"medium":return"blue";case"low":return"green";default:return"default"}},B=t=>{switch(t){case"critical":return"Critique";case"high":return"Élevée";case"medium":return"Moyenne";case"low":return"Faible";default:return"Moyenne"}},N=t=>{switch(t){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";case"info":default:return"Information"}},$=()=>{switch(d){case"connected":return{icon:e.createElement(Y,null),color:"#52c41a",text:"Connecté",status:"success"};case"connecting":return{icon:e.createElement(W,{spin:!0}),color:"#1890ff",text:"Connexion...",status:"processing"};case"error":return{icon:e.createElement(V,null),color:"#faad14",text:"Erreur",status:"warning"};case"failed":return{icon:e.createElement(C,null),color:"#ff4d4f",text:"Échec",status:"error"};default:return{icon:e.createElement(C,null),color:"#d9d9d9",text:"Déconnecté",status:"default"}}},P=t=>{if(t.read_at)return"transparent";switch(t.priority){case"critical":return"#fff2f0";case"high":return"#fff7e6";case"medium":return"#f0f7ff";case"low":return"#f6ffed";default:return"#f0f7ff"}},T=t=>t.priority==="critical"?"2px solid #ff7875":"none",L=async t=>{t.isUnread&&await h(t.id)},q=async(t,r)=>{t.stopPropagation(),await k(r.id)},o=$(),j=e.createElement("div",{style:{width:380,maxHeight:500,overflow:"hidden"}},e.createElement("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",backgroundColor:"#fafafa"}},e.createElement(s,{style:{width:"100%",justifyContent:"space-between"}},e.createElement(c,{strong:!0},"Notifications"),e.createElement(s,null,e.createElement(E,{title:`SSE: ${o.text}`},e.createElement(R,{status:o.status,text:e.createElement("span",{style:{color:o.color,fontSize:"12px"}},o.icon," ",o.text)})),y&&e.createElement(i,{size:"small",type:"link",icon:e.createElement(J,null),onClick:v,style:{padding:0}},"Reconnecter"))),f.connectedAt&&e.createElement(c,{type:"secondary",style:{fontSize:"11px"}},"Connecté: ",m(f.connectedAt).format("HH:mm:ss")," • Messages: ",f.messagesReceived)),e.createElement("div",{style:{maxHeight:400,overflow:"auto"}},n.length===0?e.createElement("div",{style:{padding:20,textAlign:"center"}},e.createElement(w,{image:w.PRESENTED_IMAGE_SIMPLE,description:"Aucune notification"})):e.createElement(x,{dataSource:n.slice(0,15),renderItem:t=>e.createElement(x.Item,{style:{padding:"12px 16px",backgroundColor:P(t),borderLeft:T(t),cursor:"pointer",transition:"background-color 0.2s"},onClick:()=>L(t),actions:[!t.read_at&&e.createElement(E,{title:"Marquer comme lu"},e.createElement(i,{type:"text",size:"small",icon:e.createElement(G,null),onClick:r=>{r.stopPropagation(),h(t.id)}})),(t.priority==="critical"||t.priority==="high")&&!t.acknowledged_at&&e.createElement(E,{title:"Acquitter"},e.createElement(i,{type:"text",size:"small",onClick:r=>q(r,t),style:{color:"#fa8c16"}},"Acquitter"))].filter(Boolean)},e.createElement(x.Item.Meta,{avatar:A(t.category,t.priority),title:e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"}},e.createElement(c,{strong:t.isUnread,style:{color:t.priority==="critical"?"#ff4d4f":"inherit",fontSize:"14px",lineHeight:"1.4"}},t.title),e.createElement("div",{style:{display:"flex",gap:"4px",flexShrink:0,marginLeft:"8px"}},e.createElement(g,{color:_(t.priority),size:"small",style:{fontSize:"10px",fontWeight:t.priority==="critical"?"bold":"normal"}},B(t.priority)))),description:e.createElement("div",null,e.createElement("div",{style:{marginBottom:"6px",fontSize:"13px",color:"#666"}},t.message),e.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",fontSize:"11px"}},e.createElement(s,{size:4},e.createElement(c,{type:"secondary"},t.timeAgo||m(t.created_at).fromNow()),t.machine_id&&e.createElement(e.Fragment,null,e.createElement("span",{style:{color:"#d9d9d9"}},"•"),e.createElement(c,{type:"secondary"},"Machine ",t.machine_id))),e.createElement(s,{size:4},e.createElement(g,{size:"small",style:{fontSize:"10px"}},N(t.category)),t.acknowledged_at&&e.createElement(g,{color:"green",size:"small",style:{fontSize:"10px"}},"Acquittée"))))}))})),n.length>15&&e.createElement("div",{style:{padding:"8px 16px",borderTop:"1px solid #f0f0f0",textAlign:"center"}},e.createElement(c,{type:"secondary",style:{fontSize:"12px"}},n.length-15," notifications supplémentaires...")));return e.createElement(O,{overlay:j,trigger:["click"],placement:"bottomRight",visible:z,onVisibleChange:b},e.createElement(R,{count:u,size:"small",offset:[-2,2]},e.createElement(i,{type:"text",icon:e.createElement(K,null),style:{color:p?"inherit":"#ff4d4f",fontSize:"16px"}})))};export{de as S};
