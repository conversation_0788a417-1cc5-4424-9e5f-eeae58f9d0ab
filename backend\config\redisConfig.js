/**
 * Redis Configuration for Manufacturing Intelligence Platform
 * Production-grade Redis setup with connection pooling, clustering, and health monitoring
 * 
 * Performance Targets:
 * - Sub-100ms cache hit response times
 * - 80% database query load reduction
 * - High availability with automatic failover
 */

import Redis from 'ioredis';
import { EventEmitter } from 'events';

class RedisConfig extends EventEmitter {
  constructor() {
    super();
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 5;
    this.healthCheckInterval = null;
    
    // Performance metrics
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      totalQueries: 0,
      avgResponseTime: 0,
      lastHealthCheck: null
    };
  }

  /**
   * Get Redis connection configuration based on environment
   */
  getRedisConfig() {
    const baseConfig = {
      // Connection settings
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB) || 0,
      
      // Connection pool settings
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxLoadingTimeout: 5000,
      
      // Performance optimization
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
      
      // Key prefix for manufacturing data
      keyPrefix: 'manufacturing:',
      
      // Connection pool
      family: 4,
      
      // Retry strategy
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        console.log(`🔄 Redis retry attempt ${times}, delay: ${delay}ms`);
        return delay;
      },
      
      // Reconnect on error
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        return err.message.includes(targetError);
      }
    };

    // Production cluster configuration
    if (process.env.NODE_ENV === 'production' && process.env.REDIS_CLUSTER_NODES) {
      return {
        ...baseConfig,
        enableOfflineQueue: false,
        redisOptions: baseConfig
      };
    }

    return baseConfig;
  }

  /**
   * Initialize Redis connections with health monitoring
   */
  async initialize() {
    try {
      console.log('🚀 Initializing Redis infrastructure...');
      
      const config = this.getRedisConfig();
      
      // Create main client for caching operations
      this.client = new Redis(config);
      
      // Create separate connections for pub/sub to avoid blocking
      this.subscriber = new Redis({
        ...config,
        keyPrefix: '' // No prefix for pub/sub channels
      });
      
      this.publisher = new Redis({
        ...config,
        keyPrefix: '' // No prefix for pub/sub channels
      });

      // Setup event handlers
      this.setupEventHandlers();
      
      // Connect all clients
      await Promise.all([
        this.client.connect(),
        this.subscriber.connect(),
        this.publisher.connect()
      ]);

      this.isConnected = true;
      this.connectionAttempts = 0;
      
      console.log('✅ Redis infrastructure initialized successfully');
      console.log(`📊 Connected to Redis at ${config.host}:${config.port}`);
      
      // Start health monitoring
      this.startHealthMonitoring();
      
      // Emit ready event
      this.emit('ready');
      
      return true;
    } catch (error) {
      console.error('❌ Redis initialization failed:', error);
      this.connectionAttempts++;
      
      if (this.connectionAttempts < this.maxConnectionAttempts) {
        console.log(`🔄 Retrying Redis connection (${this.connectionAttempts}/${this.maxConnectionAttempts})...`);
        setTimeout(() => this.initialize(), 5000);
      } else {
        console.error('💥 Max Redis connection attempts reached. Operating without cache.');
        this.emit('error', error);
      }
      
      return false;
    }
  }

  /**
   * Setup Redis event handlers for monitoring and error handling
   */
  setupEventHandlers() {
    // Main client events
    this.client.on('connect', () => {
      console.log('🔗 Redis main client connected');
    });

    this.client.on('ready', () => {
      console.log('✅ Redis main client ready');
      this.isConnected = true;
    });

    this.client.on('error', (error) => {
      console.error('❌ Redis main client error:', error);
      this.isConnected = false;
      this.emit('error', error);
    });

    this.client.on('close', () => {
      console.log('🔌 Redis main client connection closed');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      console.log('🔄 Redis main client reconnecting...');
    });

    // Subscriber events
    this.subscriber.on('connect', () => {
      console.log('📡 Redis subscriber connected');
    });

    this.subscriber.on('error', (error) => {
      console.error('❌ Redis subscriber error:', error);
    });

    // Publisher events
    this.publisher.on('connect', () => {
      console.log('📤 Redis publisher connected');
    });

    this.publisher.on('error', (error) => {
      console.error('❌ Redis publisher error:', error);
    });
  }

  /**
   * Start health monitoring with performance metrics
   */
  startHealthMonitoring() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const start = Date.now();
        await this.client.ping();
        const responseTime = Date.now() - start;
        
        this.metrics.lastHealthCheck = new Date();
        this.metrics.avgResponseTime = (this.metrics.avgResponseTime + responseTime) / 2;
        
        // Log performance metrics every 5 minutes
        if (this.metrics.totalQueries > 0 && this.metrics.totalQueries % 100 === 0) {
          this.logPerformanceMetrics();
        }
        
      } catch (error) {
        console.error('❌ Redis health check failed:', error);
        this.isConnected = false;
      }
    }, 30000); // Health check every 30 seconds
  }

  /**
   * Log performance metrics for monitoring
   */
  logPerformanceMetrics() {
    const hitRate = this.metrics.totalQueries > 0 
      ? ((this.metrics.cacheHits / this.metrics.totalQueries) * 100).toFixed(2)
      : 0;

    console.log('📊 Redis Performance Metrics:');
    console.log(`   Cache Hit Rate: ${hitRate}%`);
    console.log(`   Total Queries: ${this.metrics.totalQueries}`);
    console.log(`   Cache Hits: ${this.metrics.cacheHits}`);
    console.log(`   Cache Misses: ${this.metrics.cacheMisses}`);
    console.log(`   Avg Response Time: ${this.metrics.avgResponseTime.toFixed(2)}ms`);
    console.log(`   Last Health Check: ${this.metrics.lastHealthCheck}`);
  }

  /**
   * Get Redis client instances
   */
  getClient() {
    if (!this.isConnected || !this.client) {
      throw new Error('Redis client not connected. Initialize first.');
    }
    return this.client;
  }

  getSubscriber() {
    if (!this.isConnected || !this.subscriber) {
      throw new Error('Redis subscriber not connected. Initialize first.');
    }
    return this.subscriber;
  }

  getPublisher() {
    if (!this.isConnected || !this.publisher) {
      throw new Error('Redis publisher not connected. Initialize first.');
    }
    return this.publisher;
  }

  /**
   * Update performance metrics
   */
  recordCacheHit() {
    this.metrics.cacheHits++;
    this.metrics.totalQueries++;
  }

  recordCacheMiss() {
    this.metrics.cacheMisses++;
    this.metrics.totalQueries++;
  }

  /**
   * Get current performance metrics
   */
  getMetrics() {
    const hitRate = this.metrics.totalQueries > 0 
      ? ((this.metrics.cacheHits / this.metrics.totalQueries) * 100).toFixed(2)
      : 0;

    return {
      ...this.metrics,
      hitRate: parseFloat(hitRate),
      isConnected: this.isConnected
    };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🔄 Shutting down Redis connections...');
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    try {
      await Promise.all([
        this.client?.quit(),
        this.subscriber?.quit(),
        this.publisher?.quit()
      ]);
      
      console.log('✅ Redis connections closed gracefully');
    } catch (error) {
      console.error('❌ Error during Redis shutdown:', error);
    }
    
    this.isConnected = false;
  }
}

// Create singleton instance
const redisConfig = new RedisConfig();

export default redisConfig;
