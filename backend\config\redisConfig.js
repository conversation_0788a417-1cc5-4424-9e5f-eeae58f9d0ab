/**
 * Redis Configuration for Manufacturing Intelligence Platform
 * Production-grade Redis setup with connection pooling, clustering, and health monitoring
 * Enhanced with comprehensive fallback system for Redis unavailability
 * 
 * Performance Targets:
 * - Sub-100ms cache hit response times
 * - 80% database query load reduction
 * - High availability with automatic failover
 * - Graceful degradation when Redis unavailable
 */

import Redis from 'ioredis';
import { EventEmitter } from 'events';
import InMemoryRedisFallback from '../services/InMemoryRedisFallback.js';

class RedisConfig extends EventEmitter {
  constructor() {
    super();
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
    this.isConnected = false;
    this.isFallbackMode = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 3; // Reduced for faster fallback
    this.healthCheckInterval = null;
    this.reconnectInterval = null;
    this.circuitBreakerOpen = false;
    this.lastFailureTime = null;
    this.circuitBreakerTimeout = 30000; // 30 seconds
    
    // Fallback instances
    this.fallbackClient = null;
    this.fallbackSubscriber = null;
    this.fallbackPublisher = null;
    
    // Performance metrics
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      totalQueries: 0,
      avgResponseTime: 0,
      lastHealthCheck: null,
      fallbackOperations: 0,
      reconnectAttempts: 0
    };
  }

  /**
   * Get Redis connection configuration based on environment
   */
  getRedisConfig() {
    const baseConfig = {
      // Connection settings
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB) || 0,
      
      // Connection pool settings
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxLoadingTimeout: 5000,
      
      // Performance optimization
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
      
      // Key prefix for manufacturing data
      keyPrefix: 'manufacturing:',
      
      // Connection pool
      family: 4,
      
      // Retry strategy
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        console.log(`🔄 Redis retry attempt ${times}, delay: ${delay}ms`);
        return delay;
      },
      
      // Reconnect on error
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        return err.message.includes(targetError);
      }
    };

    // Production cluster configuration
    if (process.env.NODE_ENV === 'production' && process.env.REDIS_CLUSTER_NODES) {
      return {
        ...baseConfig,
        enableOfflineQueue: false,
        redisOptions: baseConfig
      };
    }

    return baseConfig;
  }

    /**
   * Initialize Redis connections with automatic fallback
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    console.log('🚀 Initializing Redis infrastructure with fallback capability...');
    
    // Always start in fallback mode first to prevent crashes
    await this.switchToFallbackMode();
    
    // Try to connect to Redis in the background
    this.attemptRedisConnection();
    
    this.isInitialized = true;
    
    if (this.fallbackMode) {
      console.log('✅ Redis Service initialized with in-memory fallback');
    } else {
      console.log('✅ Redis Service initialized with Redis connection');
    }
  }

  /**
   * Connect to actual Redis server
   */
  async connectToRedis() {
    const clientConfig = {
      host: this.host,
      port: this.port,
      password: this.password,
      retry_strategy: (options) => {
        if (options.error && options.error.code === 'ECONNREFUSED') {
          return new Error('Redis server refused connection');
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
          return new Error('Retry time exhausted');
        }
        if (options.attempt > 3) {
          return undefined;
        }
        return Math.min(options.attempt * 100, 3000);
      },
      connect_timeout: 60000,
      lazyConnect: true
    };

    this.client = redis.createClient(clientConfig);
    this.subscriber = redis.createClient(clientConfig);
    this.publisher = redis.createClient(clientConfig);

    // Handle errors without crashing
    this.client.on('error', (err) => {
      console.log('❌ Redis main client error:', err.message);
      if (!this.fallbackMode) {
        this.switchToFallbackMode();
      }
    });

    this.subscriber.on('error', (err) => {
      console.log('❌ Redis subscriber error:', err.message);
    });

    this.publisher.on('error', (err) => {
      console.log('❌ Redis publisher error:', err.message);
    });

    // Connect all clients
    await Promise.all([
      this.client.connect(),
      this.subscriber.connect(),
      this.publisher.connect()
    ]);

    this.fallbackMode = false;
    this.lastFailureTime = null;
    this.consecutiveFailures = 0;
    
    console.log('✅ Redis connection established');
    this.startHealthMonitoring();
  }

  /**
   * Attempt Redis connection in background without throwing
   */
  async attemptRedisConnection() {
    try {
      const testConnection = redis.createClient({
        host: this.host,
        port: this.port,
        password: this.password,
        retry_strategy: () => null, // Don't retry on test connection
        connect_timeout: 3000
      });

      // Test connection
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 3000);

        testConnection.on('connect', () => {
          clearTimeout(timeout);
          resolve();
        });

        testConnection.on('error', (err) => {
          clearTimeout(timeout);
          reject(err);
        });
      });

      // If we get here, Redis is available
      await testConnection.quit();
      await this.connectToRedis();
      
    } catch (error) {
      console.log('� Redis unavailable, continuing with fallback mode');
      this.startBackgroundReconnection();
    }
  }

  /**
   * Initialize fallback mode with in-memory cache
   */
  async initializeFallback() {
    try {
      console.log('🔄 Initializing Redis fallback mode (in-memory cache)...');
      
      // Create fallback instances
      this.fallbackClient = new InMemoryRedisFallback();
      this.fallbackSubscriber = new InMemoryRedisFallback();
      this.fallbackPublisher = new InMemoryRedisFallback();
      
      // Initialize fallback connections
      await Promise.all([
        this.fallbackClient.connect(),
        this.fallbackSubscriber.connect(),
        this.fallbackPublisher.connect()
      ]);
      
      this.isConnected = true; // Technically connected to fallback
      this.isFallbackMode = true;
      
      console.log('✅ Redis fallback mode initialized successfully');
      console.log('⚠️  Operating in degraded mode - some features may be limited');
      
      // Start health monitoring for potential recovery
      this.startHealthMonitoring();
      
      // Start background reconnection attempts
      this.startBackgroundReconnection();
      
      // Emit ready event
      this.emit('ready');
      
      return true;
    } catch (error) {
      console.error('❌ Redis fallback initialization failed:', error);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Disconnect existing Redis clients gracefully
   */
  async disconnectClients() {
    try {
      if (this.client && this.client.disconnect) {
        await this.client.disconnect();
      }
      if (this.subscriber && this.subscriber.disconnect) {
        await this.subscriber.disconnect();
      }
      if (this.publisher && this.publisher.disconnect) {
        await this.publisher.disconnect();
      }
    } catch (error) {
      console.log('⚠️ Error disconnecting Redis clients (continuing anyway):', error.message);
    }
    
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
  }

  /**
   * Switch to fallback mode gracefully
   */
  async switchToFallbackMode() {
    if (this.fallbackMode) {
      return; // Already in fallback mode
    }

    console.log('🔄 Switching to Redis fallback mode...');
    
    // Disconnect existing connections gracefully
    await this.disconnectClients();
    
    // Initialize fallback system
    this.fallbackService = new InMemoryRedisFallback();
    await this.fallbackService.initialize();
    
    // Update connections to use fallback
    this.client = this.fallbackService;
    this.subscriber = this.fallbackService;
    this.publisher = this.fallbackService;
    
    this.fallbackMode = true;
    this.isConnected = true; // Mark as connected in fallback mode
    
    console.log('✅ Redis fallback mode activated');
  }

  /**
   * Check if currently in fallback mode
   */
  isInFallbackMode() {
    return this.fallbackMode;
  }

  /**
   * Circuit breaker management
   */
  isCircuitBreakerOpen() {
    if (!this.circuitBreakerOpen) return false;
    
    const timeSinceFailure = Date.now() - (this.lastFailureTime || 0);
    if (timeSinceFailure > this.circuitBreakerTimeout) {
      console.log('🔄 Circuit breaker timeout reached, attempting reconnection...');
      this.circuitBreakerOpen = false;
      return false;
    }
    
    return true;
  }

  openCircuitBreaker() {
    this.circuitBreakerOpen = true;
    this.lastFailureTime = Date.now();
    console.log('⚡ Circuit breaker opened - Redis connections suspended');
  }

  closeCircuitBreaker() {
    this.circuitBreakerOpen = false;
    this.lastFailureTime = null;
    console.log('✅ Circuit breaker closed - Redis connections restored');
  }

  /**
   * Start background reconnection attempts when in fallback mode
   */
  startBackgroundReconnection() {
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
    }

    this.reconnectInterval = setInterval(async () => {
      if (this.isFallbackMode && !this.isCircuitBreakerOpen()) {
        console.log('🔄 Background: Attempting Redis reconnection...');
        this.metrics.reconnectAttempts++;
        
        try {
          // Test Redis connection
          const testClient = new Redis(this.getRedisConfig());
          await testClient.ping();
          await testClient.quit();
          
          console.log('✅ Background: Redis is available, switching back from fallback mode');
          await this.switchFromFallbackToRedis();
        } catch (error) {
          console.log('⚠️ Background: Redis still unavailable, continuing in fallback mode');
        }
      }
    }, 30000); // Try every 30 seconds
  }

  /**
   * Switch from fallback mode back to Redis when it becomes available
   */
  async switchFromFallbackToRedis() {
    try {
      console.log('🔄 Switching from fallback mode to Redis...');
      
      // Clean up fallback instances
      if (this.fallbackClient) {
        await this.fallbackClient.quit();
      }
      if (this.fallbackSubscriber) {
        await this.fallbackSubscriber.quit();
      }
      if (this.fallbackPublisher) {
        await this.fallbackPublisher.quit();
      }
      
      // Reset state
      this.isFallbackMode = false;
      this.connectionAttempts = 0;
      this.closeCircuitBreaker();
      
      // Initialize Redis connections
      const success = await this.initialize();
      
      if (success) {
        console.log('✅ Successfully switched back to Redis from fallback mode');
        this.emit('reconnected');
      } else {
        console.log('❌ Failed to switch back to Redis, reverting to fallback mode');
        await this.initializeFallback();
      }
      
    } catch (error) {
      console.error('❌ Error switching from fallback to Redis:', error);
      await this.initializeFallback();
    }
  }

  /**
   * Setup Redis event handlers for monitoring and error handling
   */
  setupEventHandlers() {
    // Main client events
    this.client.on('connect', () => {
      console.log('🔗 Redis main client connected');
    });

    this.client.on('ready', () => {
      console.log('✅ Redis main client ready');
      this.isConnected = true;
    });

    this.client.on('error', async (error) => {
      console.error('❌ Redis main client error:', error.message);
      if (!this.isFallbackMode) {
        this.isConnected = false;
        await this.handleConnectionFailure();
      }
    });

    this.client.on('close', () => {
      console.log('🔌 Redis main client connection closed');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      console.log('🔄 Redis main client reconnecting...');
    });

    // Subscriber events
    this.subscriber.on('connect', () => {
      console.log('📡 Redis subscriber connected');
    });

    this.subscriber.on('error', async (error) => {
      console.error('❌ Redis subscriber error:', error.message);
      if (!this.isFallbackMode) {
        await this.handleConnectionFailure();
      }
    });

    // Publisher events
    this.publisher.on('connect', () => {
      console.log('📤 Redis publisher connected');
    });

    this.publisher.on('error', async (error) => {
      console.error('❌ Redis publisher error:', error.message);
      if (!this.isFallbackMode) {
        await this.handleConnectionFailure();
      }
    });
  }

  /**
   * Start reconnection monitoring for existing connections
   */
  startReconnectionMonitoring() {
    // Monitor for connection drops and attempt recovery
    if (this.client) {
      this.client.on('error', async (error) => {
        console.error('❌ Redis client error, switching to fallback:', error.message);
        if (!this.isFallbackMode) {
          await this.handleConnectionFailure();
        }
      });
    }
  }

  /**
   * Handle connection failure by switching to fallback mode
   */
  async handleConnectionFailure() {
    console.log('🔄 Handling Redis connection failure...');
    
    this.isConnected = false;
    this.openCircuitBreaker();
    
    // Clean up existing connections
    try {
      if (this.client && !this.client.status.includes('disconnected')) {
        await this.client.quit();
      }
      if (this.subscriber && !this.subscriber.status.includes('disconnected')) {
        await this.subscriber.quit();
      }
      if (this.publisher && !this.publisher.status.includes('disconnected')) {
        await this.publisher.quit();
      }
    } catch (error) {
      console.log('⚠️ Error cleaning up Redis connections:', error.message);
    }
    
    // Switch to fallback mode
    await this.initializeFallback();
  }
  setupEventHandlers() {
    // Main client events
    this.client.on('connect', () => {
      console.log('🔗 Redis main client connected');
    });

    this.client.on('ready', () => {
      console.log('✅ Redis main client ready');
      this.isConnected = true;
    });

    this.client.on('error', (error) => {
      console.error('❌ Redis main client error:', error);
      this.isConnected = false;
      this.emit('error', error);
    });

    this.client.on('close', () => {
      console.log('🔌 Redis main client connection closed');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      console.log('🔄 Redis main client reconnecting...');
    });

    // Subscriber events
    this.subscriber.on('connect', () => {
      console.log('📡 Redis subscriber connected');
    });

    this.subscriber.on('error', (error) => {
      console.error('❌ Redis subscriber error:', error);
    });

    // Publisher events
    this.publisher.on('connect', () => {
      console.log('📤 Redis publisher connected');
    });

    this.publisher.on('error', (error) => {
      console.error('❌ Redis publisher error:', error);
    });
  }

  /**
   * Start health monitoring with fallback support
   */
  startHealthMonitoring() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const start = Date.now();
        
        if (this.isFallbackMode) {
          // Health check for fallback mode
          const result = await this.fallbackClient.healthCheck();
          this.metrics.lastHealthCheck = new Date();
          this.metrics.avgResponseTime = result.responseTime;
        } else {
          // Health check for Redis mode
          await this.client.ping();
          const responseTime = Date.now() - start;
          this.metrics.lastHealthCheck = new Date();
          this.metrics.avgResponseTime = (this.metrics.avgResponseTime + responseTime) / 2;
        }
        
        // Log performance metrics every 5 minutes
        if (this.metrics.totalQueries > 0 && this.metrics.totalQueries % 100 === 0) {
          this.logPerformanceMetrics();
        }
        
      } catch (error) {
        console.error('❌ Health check failed:', error.message);
        if (!this.isFallbackMode) {
          this.isConnected = false;
          await this.handleConnectionFailure();
        }
      }
    }, 30000); // Health check every 30 seconds
  }

  /**
   * Log performance metrics for monitoring with fallback information
   */
  logPerformanceMetrics() {
    const hitRate = this.metrics.totalQueries > 0 
      ? ((this.metrics.cacheHits / this.metrics.totalQueries) * 100).toFixed(2)
      : 0;

    const mode = this.isFallbackMode ? 'FALLBACK' : 'REDIS';
    console.log(`📊 ${mode} Performance Metrics:`);
    console.log(`   Mode: ${mode}`);
    console.log(`   Cache Hit Rate: ${hitRate}%`);
    console.log(`   Total Queries: ${this.metrics.totalQueries}`);
    console.log(`   Cache Hits: ${this.metrics.cacheHits}`);
    console.log(`   Cache Misses: ${this.metrics.cacheMisses}`);
    console.log(`   Fallback Operations: ${this.metrics.fallbackOperations}`);
    console.log(`   Reconnect Attempts: ${this.metrics.reconnectAttempts}`);
    console.log(`   Avg Response Time: ${this.metrics.avgResponseTime.toFixed(2)}ms`);
    console.log(`   Last Health Check: ${this.metrics.lastHealthCheck}`);
    console.log(`   Circuit Breaker: ${this.circuitBreakerOpen ? 'OPEN' : 'CLOSED'}`);
  }

  /**
   * Get Redis client instances with fallback support
   */
  getClient() {
    if (!this.isConnected) {
      throw new Error('Redis service not available. Check connection or fallback initialization.');
    }
    
    if (this.isFallbackMode) {
      if (!this.fallbackClient) {
        throw new Error('Fallback client not initialized.');
      }
      return this.fallbackClient;
    } else {
      if (!this.client) {
        throw new Error('Redis client not connected. Initialize first.');
      }
      return this.client;
    }
  }

  getSubscriber() {
    if (!this.isConnected) {
      throw new Error('Redis service not available. Check connection or fallback initialization.');
    }
    
    if (this.isFallbackMode) {
      if (!this.fallbackSubscriber) {
        throw new Error('Fallback subscriber not initialized.');
      }
      return this.fallbackSubscriber;
    } else {
      if (!this.subscriber) {
        throw new Error('Redis subscriber not connected. Initialize first.');
      }
      return this.subscriber;
    }
  }

  getPublisher() {
    if (!this.isConnected) {
      throw new Error('Redis service not available. Check connection or fallback initialization.');
    }
    
    if (this.isFallbackMode) {
      if (!this.fallbackPublisher) {
        throw new Error('Fallback publisher not initialized.');
      }
      return this.fallbackPublisher;
    } else {
      if (!this.publisher) {
        throw new Error('Redis publisher not connected. Initialize first.');
      }
      return this.publisher;
    }
  }

  /**
   * Update performance metrics with fallback tracking
   */
  recordCacheHit() {
    this.metrics.cacheHits++;
    this.metrics.totalQueries++;
    if (this.isFallbackMode) {
      this.metrics.fallbackOperations++;
    }
  }

  recordCacheMiss() {
    this.metrics.cacheMisses++;
    this.metrics.totalQueries++;
    if (this.isFallbackMode) {
      this.metrics.fallbackOperations++;
    }
  }

  /**
   * Get current performance metrics with fallback information
   */
  getMetrics() {
    const hitRate = this.metrics.totalQueries > 0 
      ? ((this.metrics.cacheHits / this.metrics.totalQueries) * 100).toFixed(2)
      : 0;

    return {
      ...this.metrics,
      hitRate: parseFloat(hitRate),
      isConnected: this.isConnected,
      isFallbackMode: this.isFallbackMode,
      circuitBreakerOpen: this.circuitBreakerOpen,
      mode: this.isFallbackMode ? 'fallback' : 'redis'
    };
  }

  /**
   * Graceful shutdown with fallback cleanup
   */
  async shutdown() {
    console.log('🔄 Shutting down Redis connections and fallback systems...');
    
    // Clear intervals
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
    }

    try {
      // Close Redis connections if they exist
      const redisCleanup = [];
      if (this.client) redisCleanup.push(this.client.quit());
      if (this.subscriber) redisCleanup.push(this.subscriber.quit());
      if (this.publisher) redisCleanup.push(this.publisher.quit());
      
      if (redisCleanup.length > 0) {
        await Promise.all(redisCleanup);
        console.log('✅ Redis connections closed gracefully');
      }

      // Close fallback connections if they exist
      const fallbackCleanup = [];
      if (this.fallbackClient) fallbackCleanup.push(this.fallbackClient.quit());
      if (this.fallbackSubscriber) fallbackCleanup.push(this.fallbackSubscriber.quit());
      if (this.fallbackPublisher) fallbackCleanup.push(this.fallbackPublisher.quit());
      
      if (fallbackCleanup.length > 0) {
        await Promise.all(fallbackCleanup);
        console.log('✅ Fallback connections closed gracefully');
      }
      
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
    }
    
    this.isConnected = false;
    this.isFallbackMode = false;
  }
}

// Create singleton instance
const redisConfig = new RedisConfig();

export default redisConfig;
