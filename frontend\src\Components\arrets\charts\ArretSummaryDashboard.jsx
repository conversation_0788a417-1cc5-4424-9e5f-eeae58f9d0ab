import React, { useEffect, useRef } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { Doughnut, Bar } from 'react-chartjs-2';
import { Row, Col, Card, Statistic, Progress, Spin, Empty } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, ClockCircleOutlined, AlertOutlined } from '@ant-design/icons';
import { getEnhancedChartOptions, getChartHeight, applyEnhancedSettingsToData } from '../../chart-config';
import { useSettings } from '../../../hooks/useSettings';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const ArretSummaryDashboard = ({ data = [], loading = false }) => {
  const chartRef = useRef();

  // Get settings for enhanced chart configuration
  const { settings } = useSettings();

  // Process data to create comprehensive summary
  const processSummaryData = (stopsData) => {
    if (!Array.isArray(stopsData) || stopsData.length === 0) {
      return {
        totalStops: 0,
        avgDuration: 0,
        mostProblematicMachine: 'N/A',
        peakHour: 'N/A',
        topOperator: 'N/A',
        efficiencyScore: 0,
        stopsByMachine: { labels: [], data: [] },
        stopsByHour: { labels: [], data: [] }
      };
    }

    let totalStops = stopsData.length;
    let totalDuration = 0;
    let validDurations = 0;
    
    // Group by machine
    const machineStats = {};
    const hourStats = {};
    const operatorStats = {};
    
    // Initialize hours
    for (let i = 0; i < 24; i++) {
      hourStats[i] = 0;
    }
    
    stopsData.forEach(stop => {
      const machine = stop.Machine_Name || 'Unknown';
      const operator = stop.Regleur_Prenom || 'Non assigné';
      
      // Machine stats
      machineStats[machine] = (machineStats[machine] || 0) + 1;
      
      // Operator stats
      operatorStats[operator] = (operatorStats[operator] || 0) + 1;
      
      // Hour stats
      if (stop.Debut_Stop) {
        try {
          const hour = new Date(stop.Debut_Stop).getHours();
          hourStats[hour] = (hourStats[hour] || 0) + 1;
        } catch (e) {
          // Ignore invalid dates
        }
      }
      
      // Duration calculation
      if (stop.Debut_Stop && stop.Fin_Stop_Time) {
        try {
          const start = new Date(stop.Debut_Stop);
          const end = new Date(stop.Fin_Stop_Time);
          const duration = (end - start) / (1000 * 60); // Minutes
          
          if (duration > 0) {
            totalDuration += duration;
            validDurations++;
          }
        } catch (e) {
          // Ignore invalid dates
        }
      }
    });
    
    // Calculate key metrics
    const avgDuration = validDurations > 0 ? totalDuration / validDurations : 0;
    
    // Find most problematic machine
    const mostProblematicMachine = Object.entries(machineStats)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
    
    // Find peak hour
    const peakHour = Object.entries(hourStats)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
    
    // Find top operator (most stops handled)
    const topOperator = Object.entries(operatorStats)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
    
    // Calculate efficiency score (inverse of stops and duration)
    const maxStops = Math.max(...Object.values(machineStats));
    const efficiencyScore = maxStops > 0 ? Math.max(0, 100 - (totalStops / maxStops) * 10) : 100;
    
    // Prepare chart data
    const topMachines = Object.entries(machineStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
    
    const peakHours = Object.entries(hourStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8)
      .map(([hour, count]) => [`${hour}:00`, count]);
    
    return {
      totalStops,
      avgDuration: Math.round(avgDuration),
      mostProblematicMachine,
      peakHour: peakHour !== 'N/A' ? `${peakHour}:00` : 'N/A',
      topOperator,
      efficiencyScore: Math.round(efficiencyScore),
      stopsByMachine: {
        labels: topMachines.map(([machine]) => machine),
        data: topMachines.map(([, count]) => count)
      },
      stopsByHour: {
        labels: peakHours.map(([hour]) => hour),
        data: peakHours.map(([, count]) => count)
      }
    };
  };

  const summaryData = processSummaryData(data);

  // Doughnut chart for machines
  const machineChartData = {
    labels: summaryData.stopsByMachine.labels,
    datasets: [{
      data: summaryData.stopsByMachine.data,
      backgroundColor: [
        '#FF6384',
        '#36A2EB',
        '#FFCE56',
        '#4BC0C0',
        '#9966FF'
      ],
      borderWidth: 2,
      borderColor: '#ffffff'
    }]
  };

  // Bar chart for hours
  const hourChartData = {
    labels: summaryData.stopsByHour.labels,
    datasets: [{
      label: 'Arrêts par Heure',
      data: summaryData.stopsByHour.data,
      backgroundColor: 'rgba(54, 162, 235, 0.6)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 2,
      borderRadius: 4
    }]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 15,
          usePointStyle: true,
          font: {
            size: 11
          }
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            size: 10
          }
        }
      },
      x: {
        ticks: {
          font: {
            size: 10
          }
        }
      }
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Spin size="large" tip="Chargement du tableau de bord..." />
      </div>
    );
  }

  if (summaryData.totalStops === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Empty 
          description="Aucune donnée disponible pour le tableau de bord"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div style={{ 
      height: '100%',
      background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
      borderRadius: '12px',
      padding: '20px',
      overflow: 'auto'
    }}>
      <Row gutter={[16, 16]} style={{ height: '100%' }}>
        {/* Key Statistics */}
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Total Arrêts"
                  value={summaryData.totalStops}
                  prefix={<AlertOutlined style={{ color: '#ff4d4f' }} />}
                  valueStyle={{ color: '#ff4d4f', fontSize: '18px' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Durée Moyenne"
                  value={summaryData.avgDuration}
                  suffix="min"
                  prefix={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
                  valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Score d'Efficacité"
                  value={summaryData.efficiencyScore}
                  suffix="%"
                  prefix={summaryData.efficiencyScore >= 80 ? 
                    <ArrowUpOutlined style={{ color: '#52c41a' }} /> : 
                    <ArrowDownOutlined style={{ color: '#ff4d4f' }} />
                  }
                  valueStyle={{ 
                    color: summaryData.efficiencyScore >= 80 ? '#52c41a' : '#ff4d4f',
                    fontSize: '18px'
                  }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '8px' }}>
                  Heure de Pointe
                </div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                  {summaryData.peakHour}
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Key Information Cards */}
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Card 
                title="Machine la Plus Problématique" 
                size="small" 
                style={{ borderRadius: '8px', height: '80px' }}
                bodyStyle={{ padding: '12px' }}
              >
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: 'bold', 
                  color: '#ff4d4f',
                  textAlign: 'center'
                }}>
                  {summaryData.mostProblematicMachine}
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12}>
              <Card 
                title="Opérateur Principal" 
                size="small" 
                style={{ borderRadius: '8px', height: '80px' }}
                bodyStyle={{ padding: '12px' }}
              >
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: 'bold', 
                  color: '#52c41a',
                  textAlign: 'center'
                }}>
                  {summaryData.topOperator}
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Charts Section */}
        <Col span={24} style={{ flex: 1 }}>
          <Row gutter={[16, 16]} style={{ height: '300px' }}>
            <Col xs={24} md={12}>
              <Card 
                title="Top 5 Machines" 
                size="small" 
                style={{ height: '100%', borderRadius: '8px' }}
                bodyStyle={{ padding: '16px' }}
              >
                <div style={{ height: getChartHeight(settings) }}>
                  <Doughnut
                    data={applyEnhancedSettingsToData(machineChartData, settings)}
                    options={getEnhancedChartOptions(settings, 'pie', chartOptions)}
                  />
                </div>
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card 
                title="Heures de Pointe" 
                size="small" 
                style={{ height: '100%', borderRadius: '8px' }}
                bodyStyle={{ padding: '16px' }}
              >
                <div style={{ height: getChartHeight(settings) }}>
                  <Bar
                    data={applyEnhancedSettingsToData(hourChartData, settings)}
                    options={getEnhancedChartOptions(settings, 'bar', barOptions)}
                  />
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Efficiency Progress */}
        <Col span={24}>
          <Card 
            title="Indicateur d'Efficacité Globale" 
            size="small" 
            style={{ borderRadius: '8px' }}
          >
            <Progress
              percent={summaryData.efficiencyScore}
              status={summaryData.efficiencyScore >= 80 ? 'success' : 
                     summaryData.efficiencyScore >= 60 ? 'normal' : 'exception'}
              strokeColor={summaryData.efficiencyScore >= 80 ? '#52c41a' : 
                          summaryData.efficiencyScore >= 60 ? '#1890ff' : '#ff4d4f'}
              strokeWidth={8}
              showInfo={true}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ArretSummaryDashboard;
