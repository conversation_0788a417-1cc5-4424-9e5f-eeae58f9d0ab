import React from 'react';
import { Link } from 'react-router-dom';
import { usePermission } from '../hooks/usePermission';

/**
 * Permission-based navigation link component
 * Only renders if the user has the required permissions
 * 
 * @param {Object} props - Component props
 * @param {string} props.to - Link destination
 * @param {string|string[]} [props.permissions] - Required permission(s)
 * @param {string|string[]} [props.roles] - Required role(s)
 * @param {number|number[]} [props.departments] - Required department(s)
 * @param {React.ReactNode} props.children - Link content
 * @returns {React.ReactNode} Link if authorized, null if not
 */
const PermissionNavLink = ({ 
  to, 
  permissions, 
  roles, 
  departments, 
  children 
}) => {
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  
  // Check if user meets all specified conditions
  const isAuthorized = (
    // If permissions specified, user must have them
    (!permissions || hasPermission(permissions)) &&
    // If roles specified, user must have one
    (!roles || hasRole(roles)) &&
    // If departments specified, user must have access
    (!departments || hasDepartmentAccess(departments))
  );
  
  // If not authorized, don't render anything
  if (!isAuthorized) {
    return null;
  }
  
  // If authorized, render the link
  return <Link to={to}>{children}</Link>;
};

export default PermissionNavLink;
