/**
 * Redis Service Layer for Manufacturing Intelligence Platform
 * Comprehensive caching strategies with TTL management and error handling
 * 
 * Features:
 * - GraphQL resolver caching
 * - Real-time data caching
 * - Pub/Sub for WebSocket optimization
 * - Smart cache invalidation
 * - Performance monitoring
 */

import redisConfig from '../config/redisConfig.js';
import crypto from 'crypto';

class RedisService {
  constructor() {
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
    this.isInitialized = false;
    
    // Cache TTL settings (in seconds)
    this.TTL = {
      DASHBOARD_DATA: 300,      // 5 minutes
      PRODUCTION_CHART: 180,    // 3 minutes
      STOP_DATA: 240,          // 4 minutes
      MACHINE_PERFORMANCE: 120, // 2 minutes
      REAL_TIME_DATA: 60,      // 1 minute
      SIDECARDS: 90,           // 1.5 minutes
      HOURLY_TRENDS: 600,      // 10 minutes
      DAILY_STATS: 1800,       // 30 minutes
      MACHINE_SESSIONS: 30     // 30 seconds
    };

    // Cache key prefixes
    this.KEYS = {
      DASHBOARD: 'dashboard',
      PRODUCTION: 'production',
      STOPS: 'stops',
      MACHINES: 'machines',
      SESSIONS: 'sessions',
      REALTIME: 'realtime',
      GRAPHQL: 'graphql'
    };
  }

  /**
   * Initialize Redis service
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        return true;
      }

      console.log('🚀 Initializing Redis Service...');
      
      // Wait for Redis config to be ready
      if (!redisConfig.isConnected) {
        await redisConfig.initialize();
      }

      this.client = redisConfig.getClient();
      this.subscriber = redisConfig.getSubscriber();
      this.publisher = redisConfig.getPublisher();
      
      this.isInitialized = true;
      console.log('✅ Redis Service initialized successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Redis Service initialization failed:', error);
      return false;
    }
  }

  /**
   * Generate cache key with consistent hashing
   */
  generateCacheKey(prefix, identifier, filters = {}) {
    const filterString = Object.keys(filters).length > 0 
      ? JSON.stringify(filters) 
      : '';
    
    const keyData = `${prefix}:${identifier}:${filterString}`;
    const hash = crypto.createHash('md5').update(keyData).digest('hex').substring(0, 8);
    
    return `${prefix}:${identifier}:${hash}`;
  }

  /**
   * Set cache with TTL and error handling
   */
  async setCache(key, data, ttl = this.TTL.DASHBOARD_DATA) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const serializedData = JSON.stringify({
        data,
        timestamp: Date.now(),
        ttl
      });

      await this.client.setex(key, ttl, serializedData);
      redisConfig.recordCacheHit();
      
      console.log(`📦 Cache SET: ${key} (TTL: ${ttl}s)`);
      return true;
    } catch (error) {
      console.error(`❌ Cache SET failed for ${key}:`, error);
      return false;
    }
  }

  /**
   * Get cache with automatic deserialization
   */
  async getCache(key) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const cached = await this.client.get(key);
      
      if (!cached) {
        redisConfig.recordCacheMiss();
        console.log(`📭 Cache MISS: ${key}`);
        return null;
      }

      const parsedData = JSON.parse(cached);
      redisConfig.recordCacheHit();
      
      console.log(`📦 Cache HIT: ${key}`);
      return parsedData.data;
    } catch (error) {
      console.error(`❌ Cache GET failed for ${key}:`, error);
      redisConfig.recordCacheMiss();
      return null;
    }
  }

  /**
   * Cache GraphQL resolver results
   */
  async cacheResolverResult(resolverName, args, result, ttl = this.TTL.DASHBOARD_DATA) {
    const cacheKey = this.generateCacheKey(this.KEYS.GRAPHQL, resolverName, args);
    return await this.setCache(cacheKey, result, ttl);
  }

  /**
   * Get cached GraphQL resolver result
   */
  async getCachedResolverResult(resolverName, args) {
    const cacheKey = this.generateCacheKey(this.KEYS.GRAPHQL, resolverName, args);
    return await this.getCache(cacheKey);
  }

  /**
   * Cache dashboard data with component-specific TTLs
   */
  async cacheDashboardData(filters, data) {
    const baseKey = this.generateCacheKey(this.KEYS.DASHBOARD, 'main', filters);
    
    // Cache complete dashboard data
    await this.setCache(baseKey, data, this.TTL.DASHBOARD_DATA);
    
    // Cache individual components with optimized TTLs
    if (data.productionChart) {
      const productionKey = this.generateCacheKey(this.KEYS.PRODUCTION, 'chart', filters);
      await this.setCache(productionKey, data.productionChart, this.TTL.PRODUCTION_CHART);
    }
    
    if (data.sidecards) {
      const sidecardsKey = this.generateCacheKey(this.KEYS.DASHBOARD, 'sidecards', filters);
      await this.setCache(sidecardsKey, data.sidecards, this.TTL.SIDECARDS);
    }
    
    if (data.machinePerformance) {
      const performanceKey = this.generateCacheKey(this.KEYS.MACHINES, 'performance', filters);
      await this.setCache(performanceKey, data.machinePerformance, this.TTL.MACHINE_PERFORMANCE);
    }

    return data;
  }

  /**
   * Get cached dashboard data
   */
  async getCachedDashboardData(filters) {
    const baseKey = this.generateCacheKey(this.KEYS.DASHBOARD, 'main', filters);
    return await this.getCache(baseKey);
  }

  /**
   * Cache stop table data with optimized keys
   */
  async cacheStopData(resolverName, filters, data) {
    const cacheKey = this.generateCacheKey(this.KEYS.STOPS, resolverName, filters);
    return await this.setCache(cacheKey, data, this.TTL.STOP_DATA);
  }

  /**
   * Get cached stop data
   */
  async getCachedStopData(resolverName, filters) {
    const cacheKey = this.generateCacheKey(this.KEYS.STOPS, resolverName, filters);
    return await this.getCache(cacheKey);
  }

  /**
   * Cache real-time machine data
   */
  async cacheRealTimeData(data) {
    const cacheKey = `${this.KEYS.REALTIME}:machines`;
    await this.setCache(cacheKey, data, this.TTL.REAL_TIME_DATA);
    
    // Cache individual machine data
    if (Array.isArray(data)) {
      for (const machine of data) {
        if (machine.Machine_Name) {
          const machineKey = `${this.KEYS.MACHINES}:${machine.Machine_Name}`;
          await this.setCache(machineKey, machine, this.TTL.REAL_TIME_DATA);
        }
      }
    }
    
    return data;
  }

  /**
   * Get cached real-time data
   */
  async getCachedRealTimeData() {
    const cacheKey = `${this.KEYS.REALTIME}:machines`;
    return await this.getCache(cacheKey);
  }

  /**
   * Cache machine sessions
   */
  async cacheMachineSessions(sessions) {
    const cacheKey = `${this.KEYS.SESSIONS}:active`;
    return await this.setCache(cacheKey, sessions, this.TTL.MACHINE_SESSIONS);
  }

  /**
   * Get cached machine sessions
   */
  async getCachedMachineSessions() {
    const cacheKey = `${this.KEYS.SESSIONS}:active`;
    return await this.getCache(cacheKey);
  }

  /**
   * Publish real-time updates via Redis pub/sub
   */
  async publishUpdate(channel, data) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const message = JSON.stringify({
        data,
        timestamp: Date.now(),
        channel
      });

      await this.publisher.publish(channel, message);
      console.log(`📡 Published to channel: ${channel}`);
      return true;
    } catch (error) {
      console.error(`❌ Publish failed for channel ${channel}:`, error);
      return false;
    }
  }

  /**
   * Subscribe to real-time updates
   */
  async subscribeToUpdates(channels, callback) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      await this.subscriber.subscribe(...channels);
      
      this.subscriber.on('message', (channel, message) => {
        try {
          const parsedMessage = JSON.parse(message);
          callback(channel, parsedMessage);
        } catch (error) {
          console.error(`❌ Error parsing message from ${channel}:`, error);
        }
      });

      console.log(`📡 Subscribed to channels: ${channels.join(', ')}`);
      return true;
    } catch (error) {
      console.error('❌ Subscription failed:', error);
      return false;
    }
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidateCache(pattern) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const keys = await this.client.keys(`*${pattern}*`);
      
      if (keys.length > 0) {
        await this.client.del(...keys);
        console.log(`🗑️ Invalidated ${keys.length} cache entries matching: ${pattern}`);
      }
      
      return keys.length;
    } catch (error) {
      console.error(`❌ Cache invalidation failed for pattern ${pattern}:`, error);
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const info = await this.client.info('memory');
      const keyCount = await this.client.dbsize();
      const metrics = redisConfig.getMetrics();

      return {
        ...metrics,
        keyCount,
        memoryInfo: this.parseRedisInfo(info)
      };
    } catch (error) {
      console.error('❌ Failed to get cache stats:', error);
      return null;
    }
  }

  /**
   * Parse Redis INFO command output
   */
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const result = {};
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    }
    
    return result;
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return { status: 'disconnected', error: 'Service not initialized' };
      }

      const start = Date.now();
      await this.client.ping();
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        responseTime,
        isConnected: redisConfig.isConnected,
        metrics: redisConfig.getMetrics()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        isConnected: false
      };
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return redisConfig.getMetrics();
  }
}

// Create singleton instance
const redisService = new RedisService();

export default redisService;
