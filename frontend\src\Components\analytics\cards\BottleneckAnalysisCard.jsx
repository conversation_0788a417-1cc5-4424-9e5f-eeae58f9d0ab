import React from 'react';
import { Space } from 'antd';
import { ExperimentOutlined } from '@ant-design/icons';

const BottleneckAnalysisCard = ({ loading, filters }) => {
  return (
    <div style={{ 
      height: '300px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)',
      borderRadius: '12px'
    }}>
      <Space direction="vertical" align="center">
        <ExperimentOutlined style={{ fontSize: '48px', color: '#eb2f96' }} />
        <h3 style={{ color: '#eb2f96', margin: 0 }}>Bottleneck Detection AI</h3>
        <p style={{ color: '#8c8c8c', textAlign: 'center' }}>
          Automated bottleneck identification and resolution
        </p>
      </Space>
    </div>
  );
};

export default BottleneckAnalysisCard;
