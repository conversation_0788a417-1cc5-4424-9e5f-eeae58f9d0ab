import React from "react";

import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import { usePermission } from "../hooks/usePermission";
import { Spin, App } from "antd";
import { useEffect, useMemo } from "react";

/**
 * Component for protecting routes based on user permissions
 * 
 * @param {Object} props - Component props
 * @param {string|string[]} [props.permissions] - Required permission(s) to access the route
 * @param {string|string[]} [props.roles] - Required role(s) to access the route
 * @param {number|number[]} [props.departments] - Required department(s) to access the route
 * @param {string} [props.redirectPath="/unauthorized"] - Path to redirect to if unauthorized
 * @param {boolean} [props.showNotification=true] - Whether to show a notification when redirecting
 * @returns {React.ReactNode} Outlet if authorized, Navigate if not
 */
const PermissionRoute = ({
  permissions,
  roles,
  departments,
  redirectPath = "/unauthorized",
  showNotification = true
}) => {
  const { isAuthenticated, user, loading } = useAuth();
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  const location = useLocation();
  const { notification } = App.useApp();

  // Memoize authorization check to prevent unnecessary re-renders
  const isAuthorized = useMemo(() => {
    if (!isAuthenticated || loading) return false;

    return (
      // If permissions specified, user must have them
      (!permissions || hasPermission(permissions)) &&
      // If roles specified, user must have one
      (!roles || hasRole(roles)) &&
      // If departments specified, user must have access
      (!departments || hasDepartmentAccess(departments))
    );
  }, [isAuthenticated, loading, permissions, roles, departments, hasPermission, hasRole, hasDepartmentAccess]);

  // Show notification when not authorized (MOVED TO TOP - always called)
  useEffect(() => {
    // Only show notification if all conditions are met
    if (!isAuthorized && showNotification && !loading && isAuthenticated) {
      notification.error({
        message: "Accès refusé",
        description: "Vous n'avez pas les permissions nécessaires pour accéder à cette page.",
        duration: 4,
      });
    }
  }, [isAuthorized, showNotification, loading, isAuthenticated, notification]);

  // If still loading, show spinner
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" tip="Vérification de l'authentification..." />
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  // If not authorized, redirect to unauthorized page
  if (!isAuthorized) {
    return <Navigate to={redirectPath} replace state={{ from: location }} />;
  }

  // If authorized, render the protected route
  return <Outlet />;
};

export default PermissionRoute;
