/**
 * Middleware to bypass permission checks for specific routes
 * This middleware is used to temporarily disable the RBAC system for report generation and viewing
 * while keeping the existing code intact
 */
const bypassPermission = (req, res, next) => {
  // Store original user permissions
  if (req.user) {
    // Create a backup of the original user object
    req._originalUser = { ...req.user };
    
    // Add admin permission to bypass all permission checks
    if (!req.user.permissions) {
      req.user.permissions = ['admin'];
    } else if (Array.isArray(req.user.permissions)) {
      if (!req.user.permissions.includes('admin')) {
        req.user.permissions.push('admin');
      }
    } else {
      // Handle case where permissions might be a string or object
      req.user.permissions = ['admin'];
    }
    
    // Also ensure role_permissions includes admin if it exists
    if (req.user.role_permissions) {
      if (typeof req.user.role_permissions === 'string') {
        try {
          let permissions = JSON.parse(req.user.role_permissions);
          if (Array.isArray(permissions) && !permissions.includes('admin')) {
            permissions.push('admin');
            req.user.role_permissions = JSON.stringify(permissions);
          }
        } catch (e) {
          // If parsing fails, set it directly
          req.user.role_permissions = JSON.stringify(['admin']);
        }
      } else if (Array.isArray(req.user.role_permissions) && !req.user.role_permissions.includes('admin')) {
        req.user.role_permissions.push('admin');
      }
    }
  }
  
  next();
};

export default bypassPermission;