import superAgentService from '../services/SuperAgentService.js';

/**
 * External notification service using SuperAgent for webhooks, Slack, Teams, etc.
 * Provides centralized external notification functionality
 */
class ExternalNotificationService {
  constructor() {
    this.webhookEndpoints = new Map();
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000, // 1 second base delay
      backoffMultiplier: 2
    };
  }

  /**
   * Register a webhook endpoint
   * @param {string} name - Webhook identifier
   * @param {Object} config - Webhook configuration
   */
  registerWebhook(name, config) {
    this.webhookEndpoints.set(name, {
      url: config.url,
      headers: config.headers || {},
      auth: config.auth || null,
      timeout: config.timeout || 30000,
      retries: config.retries || this.retryConfig.maxRetries,
      format: config.format || 'json', // json, form, raw
      active: config.active !== false
    });
  }

  /**
   * Send Slack notification using SuperAgent
   * @param {string} webhookUrl - Slack webhook URL
   * @param {Object} message - Slack message object
   * @returns {Promise<Object>} Send result
   */
  async sendSlackNotification(webhookUrl, message) {
    try {
      const slackPayload = {
        text: message.text || message.message,
        username: message.username || 'LocQL System',
        icon_emoji: message.icon || ':factory:',
        channel: message.channel || null,
        attachments: message.attachments || [],
        blocks: message.blocks || []
      };

      // Add color coding for different alert types
      if (message.type) {
        const colors = {
          error: 'danger',
          warning: 'warning', 
          success: 'good',
          info: '#36a64f'
        };
        
        if (slackPayload.attachments.length === 0) {
          slackPayload.attachments.push({
            color: colors[message.type] || colors.info,
            text: message.details || '',
            ts: Math.floor(Date.now() / 1000)
          });
        }
      }

      const result = await superAgentService.post(webhookUrl, slackPayload, {
        timeout: 15000,
        retries: 2,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: result.success,
        platform: 'slack',
        webhook: webhookUrl.substring(0, 50) + '...',
        timestamp: new Date().toISOString(),
        response: result.success ? 'Message sent' : result.error
      };
    } catch (error) {
      console.error('Slack notification failed:', error);
      return {
        success: false,
        platform: 'slack',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Send Microsoft Teams notification using SuperAgent
   * @param {string} webhookUrl - Teams webhook URL
   * @param {Object} message - Teams message object
   * @returns {Promise<Object>} Send result
   */
  async sendTeamsNotification(webhookUrl, message) {
    try {
      const teamsPayload = {
        '@type': 'MessageCard',
        '@context': 'http://schema.org/extensions',
        summary: message.summary || message.text || 'LocQL Notification',
        themeColor: this.getTeamsColor(message.type),
        sections: [{
          activityTitle: message.title || 'System Notification',
          activitySubtitle: message.subtitle || 'LocQL Manufacturing System',
          activityImage: message.image || null,
          facts: message.facts || [],
          text: message.text || message.message
        }],
        potentialAction: message.actions || []
      };

      // Add timestamp fact
      if (!teamsPayload.sections[0].facts.find(f => f.name === 'Timestamp')) {
        teamsPayload.sections[0].facts.push({
          name: 'Timestamp',
          value: new Date().toLocaleString()
        });
      }

      const result = await superAgentService.post(webhookUrl, teamsPayload, {
        timeout: 15000,
        retries: 2,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: result.success,
        platform: 'teams',
        webhook: webhookUrl.substring(0, 50) + '...',
        timestamp: new Date().toISOString(),
        response: result.success ? 'Message sent' : result.error
      };
    } catch (error) {
      console.error('Teams notification failed:', error);
      return {
        success: false,
        platform: 'teams',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Send Discord notification using SuperAgent
   * @param {string} webhookUrl - Discord webhook URL
   * @param {Object} message - Discord message object
   * @returns {Promise<Object>} Send result
   */
  async sendDiscordNotification(webhookUrl, message) {
    try {
      const discordPayload = {
        content: message.content || null,
        username: message.username || 'LocQL System',
        avatar_url: message.avatarUrl || null,
        tts: message.tts || false,
        embeds: message.embeds || []
      };

      // Create embed if simple message provided
      if (!discordPayload.embeds.length && (message.text || message.message)) {
        discordPayload.embeds.push({
          title: message.title || 'System Notification',
          description: message.text || message.message,
          color: this.getDiscordColor(message.type),
          timestamp: new Date().toISOString(),
          footer: {
            text: 'LocQL Manufacturing System'
          }
        });
      }

      const result = await superAgentService.post(webhookUrl, discordPayload, {
        timeout: 15000,
        retries: 2,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: result.success,
        platform: 'discord',
        webhook: webhookUrl.substring(0, 50) + '...',
        timestamp: new Date().toISOString(),
        response: result.success ? 'Message sent' : result.error
      };
    } catch (error) {
      console.error('Discord notification failed:', error);
      return {
        success: false,
        platform: 'discord',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Send generic webhook notification using SuperAgent
   * @param {string} webhookName - Registered webhook name
   * @param {Object} payload - Notification payload
   * @returns {Promise<Object>} Send result
   */
  async sendWebhookNotification(webhookName, payload) {
    try {
      const webhook = this.webhookEndpoints.get(webhookName);
      
      if (!webhook) {
        throw new Error(`Webhook '${webhookName}' not registered`);
      }

      if (!webhook.active) {
        return {
          success: false,
          webhook: webhookName,
          error: 'Webhook is disabled',
          timestamp: new Date().toISOString()
        };
      }

      // Prepare payload based on format
      let requestPayload = payload;
      let contentType = 'application/json';

      switch (webhook.format) {
        case 'form':
          requestPayload = new URLSearchParams(payload).toString();
          contentType = 'application/x-www-form-urlencoded';
          break;
        case 'raw':
          requestPayload = payload.toString();
          contentType = 'text/plain';
          break;
        default: // json
          requestPayload = payload;
          contentType = 'application/json';
      }

      const result = await superAgentService.post(webhook.url, requestPayload, {
        timeout: webhook.timeout,
        retries: webhook.retries,
        headers: {
          'Content-Type': contentType,
          ...webhook.headers
        },
        auth: webhook.auth
      });

      return {
        success: result.success,
        webhook: webhookName,
        url: webhook.url,
        timestamp: new Date().toISOString(),
        response: result.success ? result.data : result.error,
        status: result.status
      };
    } catch (error) {
      console.error(`Webhook notification failed for '${webhookName}':`, error);
      return {
        success: false,
        webhook: webhookName,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Send notifications to multiple platforms simultaneously
   * @param {Object} message - Universal message object
   * @param {Array} platforms - Array of platform configurations
   * @returns {Promise<Object>} Batch send results
   */
  async sendMultiPlatformNotification(message, platforms) {
    const startTime = Date.now();
    
    try {
      const sendPromises = platforms.map(async platform => {
        try {
          switch (platform.type) {
            case 'slack':
              return await this.sendSlackNotification(platform.webhook, message);
            case 'teams':
              return await this.sendTeamsNotification(platform.webhook, message);
            case 'discord':
              return await this.sendDiscordNotification(platform.webhook, message);
            case 'webhook':
              return await this.sendWebhookNotification(platform.name, message);
            default:
              return {
                success: false,
                platform: platform.type,
                error: `Unknown platform type: ${platform.type}`,
                timestamp: new Date().toISOString()
              };
          }
        } catch (error) {
          return {
            success: false,
            platform: platform.type,
            error: error.message,
            timestamp: new Date().toISOString()
          };
        }
      });

      const results = await Promise.allSettled(sendPromises);
      const totalTime = Date.now() - startTime;

      const processedResults = results.map(result => 
        result.status === 'fulfilled' ? result.value : {
          success: false,
          error: result.reason.message,
          timestamp: new Date().toISOString()
        }
      );

      const successCount = processedResults.filter(r => r.success).length;

      return {
        overall: {
          success: successCount > 0,
          totalPlatforms: platforms.length,
          successfulSends: successCount,
          failedSends: platforms.length - successCount,
          totalTime,
          timestamp: new Date().toISOString()
        },
        results: processedResults
      };
    } catch (error) {
      return {
        overall: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        },
        results: []
      };
    }
  }

  /**
   * Test webhook connectivity
   * @param {string} webhookName - Registered webhook name
   * @returns {Promise<Object>} Test result
   */
  async testWebhook(webhookName) {
    try {
      const webhook = this.webhookEndpoints.get(webhookName);
      
      if (!webhook) {
        return {
          success: false,
          error: `Webhook '${webhookName}' not registered`,
          timestamp: new Date().toISOString()
        };
      }

      const testPayload = {
        test: true,
        message: 'Webhook connectivity test',
        timestamp: new Date().toISOString(),
        source: 'LocQL Health Check'
      };

      const result = await this.sendWebhookNotification(webhookName, testPayload);
      
      return {
        ...result,
        test: true,
        webhook_config: {
          url: webhook.url,
          active: webhook.active,
          format: webhook.format,
          timeout: webhook.timeout
        }
      };
    } catch (error) {
      return {
        success: false,
        webhook: webhookName,
        error: error.message,
        test: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get Teams color based on message type
   * @param {string} type - Message type
   * @returns {string} Hex color
   */
  getTeamsColor(type) {
    const colors = {
      error: 'FF0000',
      warning: 'FFA500',
      success: '00FF00',
      info: '0078D4',
      critical: 'DC143C'
    };
    return colors[type] || colors.info;
  }

  /**
   * Get Discord color based on message type
   * @param {string} type - Message type
   * @returns {number} Discord color integer
   */
  getDiscordColor(type) {
    const colors = {
      error: 0xFF0000,
      warning: 0xFFA500,
      success: 0x00FF00,
      info: 0x0078D4,
      critical: 0xDC143C
    };
    return colors[type] || colors.info;
  }

  /**
   * Get registered webhooks
   * @returns {Array} List of webhook configurations
   */
  getRegisteredWebhooks() {
    return Array.from(this.webhookEndpoints.entries()).map(([name, config]) => ({
      name,
      url: config.url.substring(0, 50) + '...',
      active: config.active,
      format: config.format,
      timeout: config.timeout
    }));
  }

  /**
   * Enable/disable webhook
   * @param {string} webhookName - Webhook name
   * @param {boolean} active - Active state
   */
  setWebhookStatus(webhookName, active) {
    const webhook = this.webhookEndpoints.get(webhookName);
    if (webhook) {
      webhook.active = active;
      return true;
    }
    return false;
  }
}

// Export singleton instance
const externalNotificationService = new ExternalNotificationService();

// Register default webhooks from environment variables
if (process.env.SLACK_WEBHOOK_URL) {
  externalNotificationService.registerWebhook('default-slack', {
    url: process.env.SLACK_WEBHOOK_URL,
    timeout: 15000,
    active: true
  });
}

if (process.env.TEAMS_WEBHOOK_URL) {
  externalNotificationService.registerWebhook('default-teams', {
    url: process.env.TEAMS_WEBHOOK_URL,
    timeout: 15000,
    active: true
  });
}

if (process.env.DISCORD_WEBHOOK_URL) {
  externalNotificationService.registerWebhook('default-discord', {
    url: process.env.DISCORD_WEBHOOK_URL,
    timeout: 15000,
    active: true
  });
}

export default externalNotificationService;
