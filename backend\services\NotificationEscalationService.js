import db from '../db.js';
import { executeQuery } from '../utils/dbUtils.js';
import emailNotificationService from './EmailNotificationService.js';


/**
 * NotificationEscalationService - Manages notification escalation rules and time-based escalations
 * Handles automatic escalation of unacknowledged notifications to supervisors and managers
 */
class NotificationEscalationService {
  constructor() {
    this.isProcessing = false;

    // Start the escalation processor - TEMPORARILY DISABLED
    // this.startEscalationProcessor();
    console.log('🔔 NotificationEscalationService automatic processing DISABLED for debugging');
  }

  /**
   * Get escalation rule for notification category and priority
   * @param {string} category - Notification category
   * @param {string} priority - Notification priority
   * @returns {Promise<Object|null>} Escalation rule or null
   */
  async getEscalationRule(category, priority) {
    try {
      const query = `
        SELECT * FROM notification_escalation_rules 
        WHERE notification_category = ? 
        AND notification_priority = ? 
        AND is_active = 1
      `;

      const result = await executeQuery(query, [category, priority]);

      if (result.success && result.data.length > 0) {
        const rule = result.data[0];
        
        // Handle target_roles (already parsed by MySQL2)
        if (rule.target_roles && typeof rule.target_roles === 'string') {
          try {
            rule.target_roles = JSON.parse(rule.target_roles);
          } catch (error) {
            console.warn('Failed to parse target_roles:', error);
            rule.target_roles = [];
          }
        } else if (!rule.target_roles) {
          rule.target_roles = [];
        }
        
        return rule;
      }

      return null;

    } catch (error) {
      console.error('❌ Error getting escalation rule:', error);
      return null;
    }
  }

  /**
   * Check if notification should be escalated immediately (critical alerts)
   * @param {Object} notification - Notification object
   * @returns {Promise<boolean>} True if should escalate immediately
   */
  async shouldEscalateImmediately(notification) {
    const rule = await this.getEscalationRule(notification.category, notification.priority);
    
    if (!rule) {
      return false;
    }

    // Critical machine alerts and system alerts escalate immediately
    return (
      (notification.category === 'machine_alert' && notification.priority === 'critical') ||
      (notification.category === 'alert' && notification.priority === 'critical') ||
      (notification.category === 'production' && notification.priority === 'critical')
    );
  }

  /**
   * Escalate notification immediately
   * @param {Object} notification - Notification object
   * @returns {Promise<boolean>} Success status
   */
  async escalateImmediately(notification) {
    try {
      console.log(`🚨 Escalating notification immediately: ${notification.title}`);

      const rule = await this.getEscalationRule(notification.category, notification.priority);
      
      if (!rule) {
        console.log('⚠️ No escalation rule found for immediate escalation');
        return false;
      }

      // Get target users based on escalation action
      const targetUsers = await this.getEscalationTargets(rule, notification);

      if (targetUsers.length === 0) {
        console.log('⚠️ No target users found for escalation');
        return false;
      }

      // Send escalation emails
      const escalationResult = await this.sendEscalationEmails(notification, targetUsers, rule);

      // Log the escalation
      await this.logEscalation(notification.id, rule.id, targetUsers, 'immediate');

      console.log(`✅ Immediate escalation completed for notification ${notification.id}`);
      return escalationResult;

    } catch (error) {
      console.error('❌ Error in immediate escalation:', error);
      return false;
    }
  }

  /**
   * Process time-based escalations for unacknowledged notifications
   * @returns {Promise<Object>} Processing results
   */
  async processEscalations() {
    if (this.isProcessing) {
      console.log('⏳ Escalation processor already running, skipping...');
      return { processed: 0, escalated: 0 };
    }

    this.isProcessing = true;
    console.log('🔄 Processing notification escalations...');

    try {
      // Get unacknowledged notifications that need escalation
      // Using explicit COLLATE clauses to fix collation mismatch between tables
      const query = `
        SELECT n.*,
               ner.escalation_minutes,
               ner.escalation_action,
               ner.target_roles,
               ner.id as rule_id
        FROM notifications n
        JOIN notification_escalation_rules ner
          ON n.category COLLATE utf8mb4_unicode_ci = ner.notification_category COLLATE utf8mb4_unicode_ci
          AND n.priority COLLATE utf8mb4_unicode_ci = ner.notification_priority COLLATE utf8mb4_unicode_ci
        LEFT JOIN notification_delivery_log ndl
          ON n.id = ndl.notification_id AND ndl.escalated = 1
        WHERE n.acknowledged = 0
        AND ner.is_active = 1
        AND ndl.id IS NULL
        AND TIMESTAMPDIFF(MINUTE, n.timestamp, NOW()) >= ner.escalation_minutes
        ORDER BY n.priority DESC, n.timestamp ASC
        LIMIT 20
      `;

      const result = await executeQuery(query);

      if (!result.success) {
        console.error('❌ Failed to get notifications for escalation:', result.error);
        return { processed: 0, escalated: 0 };
      }

      const notifications = result.data;
      console.log(`📧 Found ${notifications.length} notifications for escalation`);

      let processed = 0;
      let escalated = 0;

      for (const notification of notifications) {
        try {
          processed++;

          // Handle target_roles (already parsed by MySQL2)
          let targetRoles = [];
          if (notification.target_roles) {
            if (typeof notification.target_roles === 'string') {
              try {
                targetRoles = JSON.parse(notification.target_roles);
              } catch (error) {
                console.warn('Failed to parse target_roles:', error);
              }
            } else if (Array.isArray(notification.target_roles)) {
              targetRoles = notification.target_roles;
            }
          }

          const rule = {
            id: notification.rule_id,
            escalation_action: notification.escalation_action,
            target_roles: targetRoles,
            escalation_minutes: notification.escalation_minutes
          };

          // Get target users for escalation
          const targetUsers = await this.getEscalationTargets(rule, notification);

          if (targetUsers.length > 0) {
            // Send escalation emails
            const success = await this.sendEscalationEmails(notification, targetUsers, rule);

            if (success) {
              escalated++;
              // Log the escalation
              await this.logEscalation(notification.id, rule.id, targetUsers, 'time_based');
              console.log(`✅ Escalated notification ${notification.id} to ${targetUsers.length} users`);
            }
          }

        } catch (error) {
          console.error(`❌ Error processing escalation for notification ${notification.id}:`, error);
        }
      }

      console.log(`✅ Escalation processing complete: ${processed} processed, ${escalated} escalated`);
      return { processed, escalated };

    } catch (error) {
      console.error('❌ Error processing escalations:', error);
      return { processed: 0, escalated: 0 };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get escalation target users based on rule
   * @param {Object} rule - Escalation rule
   * @param {Object} notification - Notification object
   * @returns {Promise<Array>} Array of target users
   */
  async getEscalationTargets(rule, notification) {
    try {
      let query;
      let params = [];

      switch (rule.escalation_action) {
        case 'email':
          // Send to original notification recipients
          if (notification.user_id) {
            query = `
              SELECT u.id, u.email, u.role 
              FROM users u 
              WHERE u.id = ? AND u.email IS NOT NULL AND u.email != ''
            `;
            params = [notification.user_id];
          } else {
            // Get all users with email (settings system removed)
            // TODO: Implement new settings system for notification preferences
            const result = await executeQuery('SELECT id, email, username FROM users WHERE email IS NOT NULL AND email != ""');
            return result.success ? result.data : [];
          }
          break;

        case 'email_supervisor':
          // Send to users with supervisor/manager roles
          query = `
            SELECT u.id, u.email, u.role 
            FROM users u 
            WHERE u.role IN ('admin', 'head_manager', 'production_manager', 'operations_manager')
            AND u.email IS NOT NULL AND u.email != ''
          `;
          break;

        case 'email_all_roles':
          // Send to specific roles defined in target_roles
          if (rule.target_roles && rule.target_roles.length > 0) {
            const placeholders = rule.target_roles.map(() => '?').join(',');
            query = `
              SELECT u.id, u.email, u.role 
              FROM users u 
              WHERE u.role IN (${placeholders})
              AND u.email IS NOT NULL AND u.email != ''
            `;
            params = rule.target_roles;
          } else {
            return [];
          }
          break;

        default:
          console.warn(`Unknown escalation action: ${rule.escalation_action}`);
          return [];
      }

      const result = await executeQuery(query, params);

      if (result.success) {
        return result.data;
      } else {
        console.error('❌ Failed to get escalation targets:', result.error);
        return [];
      }

    } catch (error) {
      console.error('❌ Error getting escalation targets:', error);
      return [];
    }
  }

  /**
   * Send escalation emails to target users
   * @param {Object} notification - Notification object
   * @param {Array} targetUsers - Array of target users
   * @param {Object} rule - Escalation rule
   * @returns {Promise<boolean>} Success status
   */
  async sendEscalationEmails(notification, targetUsers, rule) {
    try {
      const emails = targetUsers.map(user => user.email);
      
      const escalationData = {
        title: `🚨 ESCALATED: ${notification.title}`,
        message: `This notification has been escalated due to lack of acknowledgment.\n\nOriginal Message: ${notification.message}`,
        priority: 'critical',
        category: notification.category,
        machine_id: notification.machine_id,
        source: notification.source,
        escalation_rule: rule.escalation_action,
        escalation_time: rule.escalation_minutes,
        original_timestamp: notification.timestamp
      };

      // Send escalation email
      const result = await emailNotificationService.sendNotificationEmail(escalationData, emails);

      return result && result.success;

    } catch (error) {
      console.error('❌ Error sending escalation emails:', error);
      return false;
    }
  }

  /**
   * Log escalation in delivery log
   * @param {number} notificationId - Notification ID
   * @param {number} ruleId - Escalation rule ID
   * @param {Array} targetUsers - Array of target user objects
   * @param {string} escalationType - Type of escalation
   */
  async logEscalation(notificationId, ruleId, targetUsers, escalationType) {
    try {
      // Log an entry for each target user who received the escalation
      for (const user of targetUsers) {
        const query = `
          INSERT INTO notification_delivery_log
          (notification_id, user_id, delivery_method, email_status, escalated, escalated_at, delivery_time)
          VALUES (?, ?, 'email', 'sent', 1, NOW(), NOW())
        `;

        await executeQuery(query, [notificationId, user.id]);
      }

      console.log(`📝 Logged ${escalationType} escalation for notification ${notificationId} to ${targetUsers.length} users`);

    } catch (error) {
      console.error('❌ Error logging escalation:', error);
    }
  }

  /**
   * Start the escalation processor (runs every 3 minutes)
   */
  startEscalationProcessor() {
    console.log('🚫 Notification escalation processor is DISABLED for debugging notification system');
    console.log('🔧 To re-enable, remove the return statement in NotificationEscalationService.startEscalationProcessor()');
    return; // TEMPORARILY DISABLED FOR DEBUGGING

    console.log('🚀 Starting notification escalation processor...');

    // Process immediately on startup (after 10 seconds)
    setTimeout(() => this.processEscalations(), 10000);

    // Then process every 3 minutes
    setInterval(() => {
      this.processEscalations();
    }, 3 * 60 * 1000); // 3 minutes
  }

  /**
   * Get escalation statistics
   * @returns {Promise<Object>} Escalation statistics
   */
  async getEscalationStats() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_escalations,
          COUNT(CASE WHEN escalated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h,
          COUNT(CASE WHEN escalated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as last_7d,
          AVG(TIMESTAMPDIFF(MINUTE, delivery_time, escalated_at)) as avg_escalation_time_minutes
        FROM notification_delivery_log 
        WHERE escalated = 1
      `;

      const result = await executeQuery(query);

      if (result.success && result.data.length > 0) {
        return {
          success: true,
          stats: result.data[0],
          isProcessing: this.isProcessing
        };
      } else {
        return { success: false, error: result.error };
      }

    } catch (error) {
      console.error('❌ Error getting escalation stats:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create and export singleton instance
const notificationEscalationService = new NotificationEscalationService();
export default notificationEscalationService;
