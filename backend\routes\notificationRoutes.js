import express from "express";
import { check, validationResult } from "express-validator";
import db from "../db.js";
import auth from "../middleware/auth.js";
import NotificationService from "../utils/notificationService.js";
import rateLimiter from "../middleware/rateLimiter.js";
import jwt from "jsonwebtoken";
import externalNotificationService from '../services/ExternalNotificationService.js';
import superAgentService from '../services/SuperAgentService.js';

import emailRetryService from '../services/EmailRetryService.js';
import notificationEscalationService from '../services/NotificationEscalationService.js';

const router = express.Router();

// Import SSE notification service
import sseNotificationService from "../services/SSENotificationService.js";

// SSE Stream Endpoint - Replaces WebSocket connection
router.get('/stream', async (req, res) => {
  try {
    // Extract JWT token from query parameter or Authorization header
    const token = req.query.token ||
                  req.headers.authorization?.replace('Bearer ', '') ||
                  req.headers.authorization?.replace('bearer ', '');

    if (!token) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'No token provided for SSE connection'
      });
    }

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Verify this is an SSE token (if purpose field exists)
      if (decoded.purpose && decoded.purpose !== 'sse') {
        throw new Error('Invalid token purpose');
      }
    } catch (jwtError) {
      console.error('SSE JWT verification failed:', jwtError.message);
      return res.status(401).json({
        error: 'Invalid token',
        message: 'JWT verification failed'
      });
    }

    const userId = decoded.id;
    console.log(`🔐 SSE authentication successful for user ${userId} (cookie-based)`);

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': req.headers.origin || '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'Cache-Control, Authorization',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    });

    // Prevent timeout
    res.setTimeout(0);

    // Connection metadata
    const metadata = {
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date().toISOString()
    };

    // Add client to SSE service
    const connection = sseNotificationService.addClient(userId, res, metadata);

    // Send initial notifications (last 50 notifications - both read and unread)
    console.log(`🚀 SSE: Starting initial notifications for user ${userId}`);
    try {
      // Get user role for proper notification filtering
      console.log(`🔍 SSE: Getting user role for user ${userId}`);      // Use the same database query method as the main API
      const [userResults] = await db.execute('SELECT role FROM users WHERE id = ?', [userId]);
      const userRole = userResults.length > 0 ? userResults[0].role : null;
      console.log(`👤 SSE: User ${userId} has role: ${userRole}`);

      console.log(`📊 SSE: Calling getUserNotifications for user ${userId}`);
      const initialNotifications = await getUserNotifications(userId, 50, 0, {}, userRole);
      console.log(`📊 SSE: getUserNotifications returned ${initialNotifications.length} notifications`);

      if (initialNotifications.length > 0) {
        console.log(`📤 SSE: Sending ${initialNotifications.length} initial notifications to user ${userId}`);
        await sseNotificationService.sendToConnection(connection, {
          type: 'initial_notifications',
          notifications: initialNotifications.map(n => sseNotificationService.formatNotification(n)),
          count: initialNotifications.length,
          timestamp: new Date().toISOString()
        });

        console.log(`📬 SSE: Successfully sent ${initialNotifications.length} initial notifications to user ${userId}`);
      } else {
        console.log(`📭 SSE: No notifications found for user ${userId} - sending empty response`);
        // Send empty initial notifications to confirm connection
        await sseNotificationService.sendToConnection(connection, {
          type: 'initial_notifications',
          notifications: [],
          count: 0,
          timestamp: new Date().toISOString()
        });
        console.log(`📭 SSE: Sent empty initial notifications to user ${userId}`);
      }
    } catch (dbError) {
      console.error(`❌ SSE: Failed to load initial notifications for user ${userId}:`, dbError);
      await sseNotificationService.sendToConnection(connection, {
        type: 'error',
        message: 'Failed to load initial notifications',
        timestamp: new Date().toISOString()
      });
    }

    // Handle client disconnect events
    req.on('close', () => {
      console.log(`🔌 SSE client disconnected (close event) - User: ${userId}`);
      sseNotificationService.removeClient(userId, res);
    });

    req.on('aborted', () => {
      console.log(`🔌 SSE client disconnected (aborted) - User: ${userId}`);
      sseNotificationService.removeClient(userId, res);
    });

    res.on('error', (error) => {
      console.error(`❌ SSE response error for user ${userId}:`, error);
      sseNotificationService.removeClient(userId, res);
    });

  } catch (error) {
    console.error('SSE connection setup failed:', error);
    if (!res.headersSent) {
      res.status(500).json({
        error: 'SSE connection failed',
        message: error.message
      });
    }
  }
});

// Helper function to get user notifications with filtering and pagination
const getUserNotifications = async (userId, limit = 100, offset = 0, filters = {}, userRole = null) => {
  // Ensure parameters are properly typed
  const safeLimit = parseInt(limit) || 100;
  const safeOffset = parseInt(offset) || 0;
  const safeUserId = parseInt(userId);
  
  console.log(`🔍 SSE getUserNotifications: Building query for user ${safeUserId} (${userRole})`);
  console.log(`📊 SSE getUserNotifications: Params - limit: ${safeLimit}, offset: ${safeOffset}`);

  let query = `
    SELECT
      id, title, message, category, priority, severity,
      machine_id, user_id, source, timestamp as created_at,
      CASE WHEN \`read\` = 1 THEN timestamp ELSE NULL END as read_at,
      acknowledged_at,
      \`read\`, acknowledged
    FROM notifications
  `;

  const params = [];
  // Admin users see all notifications, regular users see only their own + global
  if (userRole === 'admin') {
    query += ` WHERE 1=1`; // Admin sees all notifications
    console.log(`👑 SSE getUserNotifications: Admin user - will see all notifications`);
  } else {
    query += ` WHERE (user_id = ? OR user_id IS NULL)`;
    params.push(safeUserId);
    console.log(`👤 SSE getUserNotifications: Regular user - will see user-specific + global notifications`);
  }

  // Add filters
  if (filters.priority) {
    query += ` AND priority = ?`;
    params.push(filters.priority);
  }

  if (filters.category) {
    query += ` AND category = ?`;
    params.push(filters.category);
  }

  if (filters.unreadOnly) {
    query += ` AND \`read\` = 0`;
  }  // Use string interpolation for LIMIT and OFFSET to avoid MySQL2 parameter issues
  query += ` ORDER BY timestamp DESC LIMIT ${safeLimit} OFFSET ${safeOffset}`;

  console.log(`🗃️ SSE getUserNotifications: Executing query...`);
  console.log(`📝 SSE getUserNotifications: Query:`, query.replace(/\s+/g, ' ').trim());
  console.log(`📝 SSE getUserNotifications: Params:`, params);
  
  // Use the same database query method as the main API
  const [results] = await db.execute(query, params);
  console.log(`✅ SSE getUserNotifications: Found ${results.length} notifications`);

  return results;
};

// Send notification to specific user or all users using SSE
const sendNotification = async (notification, specificUserId = null) => {
  if (specificUserId) {
    // Send to specific user
    await sseNotificationService.sendNotificationToUser(specificUserId, notification);
  } else {
    // Broadcast to all connected clients
    await sseNotificationService.broadcastNotification(notification);
  }
};

// Get all notifications for the authenticated user - FIXED VERSION
router.get("/", auth, rateLimiter.notificationReadLimiter(), async (req, res) => {
  const startTime = Date.now();
  console.log(`🔍 [${startTime}] Starting notification request processing...`);

  // Set timeout for the entire request
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      console.error(`⏰ [${Date.now()}] Request timeout after ${Date.now() - startTime}ms`);
      res.status(408).json({
        error: "Request timeout",
        message: "The request took too long to process",
        duration: Date.now() - startTime
      });
    }
  }, 30000); // 30 second timeout

  try {
    console.log(`🔐 [${Date.now()}] Authentication successful for user ${req.user.id}`);

    const userId = req.user.id;
    const user = req.user; // User data is already available from auth middleware

    console.log(`👤 [${Date.now()}] User data available: ${userId} (${user.role})`);

    // Validate user data
    if (!user || !user.role) {
      clearTimeout(timeout);
      console.error(`❌ [${Date.now()}] Invalid user data for user ${userId}`);
      return res.status(400).json({
        error: "Invalid user data",
        message: "User role information is missing"
      });
    }

    // Parse query parameters
    const limit = Math.min(req.query.limit ? Number.parseInt(req.query.limit) : 100, 500); // Max 500
    const offset = req.query.offset ? Number.parseInt(req.query.offset) : 0;
    const priority = req.query.priority;
    const category = req.query.category;
    const unreadOnly = req.query.unreadOnly === 'true';

    const filters = { priority, category, unreadOnly };

    console.log(`⚙️ [${Date.now()}] Fetching notifications with limit=${limit}, offset=${offset}, filters=`, filters);

    const notifications = await getUserNotifications(userId, limit, offset, filters, user.role);

    clearTimeout(timeout);
    console.log(`✅ [${Date.now()}] Successfully fetched ${notifications.length} notifications in ${Date.now() - startTime}ms`);

    res.json(notifications);

  } catch (err) {
    clearTimeout(timeout);
    console.error(`❌ [${Date.now()}] Error fetching notifications for user ${req.user.id}:`, err);
    res.status(500).json({ 
      error: 'Server error',
      message: err.message
    });
  }
});

// Delete a notification for the authenticated user
router.delete('/:id', auth, async (req, res) => {
  try {
    const notificationId = req.params.id;    const userId = req.user.id;
    // Only delete if the notification belongs to the user or is global
    const [result] = await db.execute(
      `DELETE FROM notifications WHERE id = ? AND (user_id = ? OR user_id IS NULL)`,
      [notificationId, userId]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Notification not found or not authorized' });
    }
    // Broadcast SSE event to all clients
    await sseNotificationService.broadcastEvent({
      type: 'notification_deleted',
      id: Number(notificationId),
      userId,
      timestamp: new Date().toISOString()
    });
    res.json({ success: true, deleted: true, id: Number(notificationId) });
  } catch (err) {
    console.error('Error deleting notification:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Mark all notifications as read for the authenticated user
router.patch('/read-all', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    // Update all unread notifications for this user or global
    const [result] = await db.execute(
      `UPDATE notifications SET \`read\` = 1 WHERE (user_id = ? OR user_id IS NULL) AND \`read\` = 0`,
      [userId]
    );
    // Broadcast SSE event
    await sseNotificationService.broadcastEvent({
      type: 'notifications_read_all',
      userId,
      count: result.affectedRows,
      timestamp: new Date().toISOString()
    });
    res.json({ success: true, updated: result.affectedRows });
  } catch (err) {
    console.error('Error marking all as read:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Mark a notification as read
router.put("/:id/read", auth, async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;

    const [result] = await db.execute(
      "UPDATE notifications SET `read` = 1 WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND `read` = 0",
      [notificationId, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Notification not found or not authorized to read" });
    }

    // Broadcast SSE event for this action
    await sseNotificationService.broadcastEvent({
      type: 'notification_read',
      id: Number(notificationId),
      userId,
      timestamp: new Date().toISOString()
    });

    res.json({ success: true, updated: true, id: Number(notificationId) });
  } catch (err) {
    console.error("Error marking notification as read:", err);
    res.status(500).json({ error: "Server error", message: err.message });
  }
});

// Create a new notification with enhanced validation and security
router.post(
  "/",
  [
    auth,
    rateLimiter.notificationCreationLimiter(),
    check("title", "Title is required and must be between 1-255 characters")
      .isLength({ min: 1, max: 255 })
      .escape(),
    check("message", "Message is required and must be between 1-2000 characters")
      .isLength({ min: 1, max: 2000 })
      .escape(),
    check("category", "Invalid category")
      .isIn(["alert", "maintenance", "update", "info", "machine_alert", "production", "quality"]),
    check("priority", "Invalid priority")
      .optional()
      .isIn(["low", "medium", "high", "critical"]),
    check("severity", "Invalid severity")
      .optional()
      .isIn(["info", "warning", "error", "critical"]),
    check("machine_id", "Machine ID must be a number")
      .optional()
      .isInt({ min: 1 }),
    check("userId", "User ID must be a number")
      .optional()
      .isInt({ min: 1 })
  ],
  async (req, res) => {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check authorization - only admins or system can create notifications
    if (req.user.role !== "admin" && req.body.source !== "system") {
      return res.status(403).json({ error: "Not authorized to create notifications" });
    }

    /* REMOVED HARD STOP BLOCK THAT WAS DISABLING ALL API NOTIFICATION CREATION */

    try {
      const {
        title,
        message,
        category,
        priority = 'medium',
        severity = 'info',
        source = 'user',
        machine_id,
        userId
      } = req.body;

      console.log("Creating notification:", {
        title,
        message,
        category,
        priority,
        severity,
        source,
        machine_id,
        userId,
        createdBy: req.user.id
      });

      // Create notification using enhanced service
      const notification = await NotificationService.createNotification({
        title,
        message,
        category,
        priority,
        severity,
        source,
        machine_id,
        userId
      });

      // Send notification via SSE
      if (userId) {
        await sendNotification(notification, userId);
      } else {
        await sseNotificationService.broadcastNotification(notification);
      }

      // For critical machine alerts, broadcast to all users
      if (priority === 'critical' && category === 'machine_alert') {
        console.log(`🚨 Broadcasting critical machine alert to all users`);
        await sseNotificationService.broadcastNotification(notification);
      }

      res.status(201).json({
        success: true,
        notification: notification,
        message: 'Notification created and delivered via SSE'
      });
    } catch (err) {
      console.error("Error creating notification:", err);
      res.status(500).json({
        error: "Server error",
        message: err.message
      });
    }
  }
);

// Acknowledge a notification
router.put("/:id/acknowledge", auth, rateLimiter.notificationReadLimiter(), async (req, res) => {
  try {
    const notificationId = req.params.id;
    const success = await NotificationService.acknowledgeNotification(notificationId, req.user.id);

    if (success) {
      res.json({ success: true, message: "Notification acknowledged" });
    } else {
      res.status(404).json({ error: "Notification not found" });
    }
  } catch (err) {
    console.error("Error acknowledging notification:", err);
    res.status(500).json({ error: "Server error" });
  }
});

// Get notification statistics
router.get("/stats", auth, async (req, res) => {
  try {
    const [stats] = await db.execute(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN \`read\` = 0 THEN 1 ELSE 0 END) as unread,
        SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical,
        SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high,
        SUM(CASE WHEN category = 'machine_alert' THEN 1 ELSE 0 END) as machine_alerts,
        SUM(CASE WHEN acknowledged_at IS NOT NULL THEN 1 ELSE 0 END) as acknowledged
      FROM notifications
      WHERE user_id = ? OR user_id IS NULL
    `, [req.user.id]);

    const sseStats = sseNotificationService.getPublicStats();

    res.json({
      notifications: stats[0],
      sse: sseStats,
      system: {
        notifications_enabled: true
      }
    });
  } catch (err) {
    console.error("Error fetching notification stats:", err);
    res.status(500).json({ error: "Server error" });
  }
});

// Get notification system status (admin only)
router.get("/system/status", auth, async (req, res) => {
  try {
    // Check if user has admin permissions
    if (req.user.role !== "admin") {
      return res.status(403).json({ error: "Admin access required" });
    }

    // Get recent notification creation stats
    const [recentStats] = await db.execute(`
      SELECT
        COUNT(*) as total_last_hour,
        SUM(CASE WHEN timestamp >= DATE_SUB(NOW(), INTERVAL 10 MINUTE) THEN 1 ELSE 0 END) as last_10_minutes,
        SUM(CASE WHEN category = 'machine_alert' THEN 1 ELSE 0 END) as machine_alerts_last_hour
      FROM notifications
      WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    `);

    res.json({
      success: true,
      system: {
        notifications_enabled: notificationsEnabled,
        status: notificationsEnabled ? 'enabled' : 'disabled'
      },
      recent_activity: recentStats[0],
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error("Error fetching notification system status:", err);
    res.status(500).json({ error: "Server error" });
  }
});

// Get SSE connection status (admin only)
router.get("/sse/status", auth, (req, res) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin access required" });
  }

  const stats = sseNotificationService.getDetailedStats();
  res.json(stats);
});

// SSE Health Check
router.get('/sse/health', (req, res) => {
  try {
    const stats = sseNotificationService.getPublicStats();
    const isHealthy = stats.totalConnections >= 0;

    res.status(isHealthy ? 200 : 503).json({
      status: isHealthy ? 'healthy' : 'unhealthy',
      service: 'SSE Notification Service',
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Add read and acknowledge endpoints for SSE compatibility
router.patch('/:id/read', auth, async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;

    // Update read status in database - allow both user-specific and global notifications
    const result = await new Promise((resolve, reject) => {
      db.execute(
        "UPDATE notifications SET `read` = 1 WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND `read` = 0",
        [notificationId, userId],
        (err, result) => {
          if (err) reject(err);
          else resolve(result);
        }
      );
    });

    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: 'Notification not found or already read'
      });
    }    // Broadcast SSE event for this action
    await sseNotificationService.broadcastEvent({
      type: 'notification_read',
      id: Number(notificationId),
      userId,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Notification marked as read',
      notification_id: notificationId,
      read_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({
      error: 'Failed to mark notification as read',
      message: error.message
    });
  }
});

router.patch('/:id/acknowledge', auth, async (req, res) => {
  try {
    const notificationId = req.params.id;
    const userId = req.user.id;

    // Update acknowledgment in database - allow both user-specific and global notifications
    const result = await new Promise((resolve, reject) => {
      db.execute(
        "UPDATE notifications SET acknowledged = 1, acknowledged_at = NOW(), acknowledged_by = ? WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND acknowledged = 0",
        [userId, notificationId, userId],
        (err, result) => {
          if (err) reject(err);
          else resolve(result);
        }
      );
    });

    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: 'Notification not found or already acknowledged'
      });
    }

    // Send acknowledgment confirmation via SSE
    await sseNotificationService.sendNotificationToUser(userId, {
      id: notificationId,
      type: 'notification_acknowledged',
      acknowledged_at: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Notification acknowledged',
      notification_id: notificationId,
      acknowledged_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error acknowledging notification:', error);
    res.status(500).json({
      error: 'Failed to acknowledge notification',
      message: error.message
    });
  }
});

// Debug endpoint to check SSE connection status
router.get('/debug-sse', async (req, res) => {
  try {
    const stats = sseNotificationService.getDetailedStats();
    const publicStats = sseNotificationService.getPublicStats();

    res.json({
      success: true,
      message: 'SSE Debug Information',
      detailed_stats: stats,
      public_stats: publicStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error in SSE debug endpoint:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// SSE Diagnostic endpoint - comprehensive troubleshooting
router.get('/sse-diagnostic', async (req, res) => {
  try {
    console.log('🔍 SSE Diagnostic - Comprehensive troubleshooting...');

    const diagnostic = {
      timestamp: new Date().toISOString(),
      authentication: {
        status: 'unknown',
        user_id: null,
        user_role: null,
        token_present: false,
        token_valid: false,
        error: null
      },
      sse_service: {
        status: 'unknown',
        active_connections: 0,
        active_users: 0,
        uptime: 0,
        error: null
      },
      recommendations: []
    };

    // Check authentication status
    console.log('🔍 Checking authentication...');
    const token = req.cookies.token;

    if (!token) {
      diagnostic.authentication.status = 'not_authenticated';
      diagnostic.authentication.error = 'No authentication token found in cookies';
      diagnostic.recommendations.push({
        priority: 'high',
        action: 'login_required',
        message: 'User must log in first. Go to /login and use credentials: <EMAIL> / admin123'
      });
    } else {
      diagnostic.authentication.token_present = true;
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        diagnostic.authentication.status = 'authenticated';
        diagnostic.authentication.user_id = decoded.id;
        diagnostic.authentication.user_role = decoded.role;
        diagnostic.authentication.token_valid = true;
        diagnostic.recommendations.push({
          priority: 'info',
          action: 'authentication_ok',
          message: 'User is properly authenticated'
        });
      } catch (jwtError) {
        diagnostic.authentication.status = 'invalid_token';
        diagnostic.authentication.token_valid = false;
        diagnostic.authentication.error = jwtError.message;
        diagnostic.recommendations.push({
          priority: 'high',
          action: 'relogin_required',
          message: 'Authentication token is invalid. Please log out and log in again.'
        });
      }
    }

    // Check SSE service status
    console.log('🔍 Checking SSE service...');
    try {
      const sseStats = sseNotificationService.getDetailedStats();
      diagnostic.sse_service.status = 'running';
      diagnostic.sse_service.active_connections = sseStats.totalConnections;
      diagnostic.sse_service.active_users = sseStats.activeUsers;
      diagnostic.sse_service.uptime = sseStats.uptime;

      if (sseStats.totalConnections === 0) {
        diagnostic.recommendations.push({
          priority: 'medium',
          action: 'no_connections',
          message: 'SSE service is running but has no active connections. This is normal if no users are connected.'
        });
      } else {
        diagnostic.recommendations.push({
          priority: 'info',
          action: 'connections_active',
          message: `SSE service has ${sseStats.totalConnections} active connections from ${sseStats.activeUsers} users`
        });
      }
    } catch (sseError) {
      diagnostic.sse_service.status = 'error';
      diagnostic.sse_service.error = sseError.message;
      diagnostic.recommendations.push({
        priority: 'critical',
        action: 'sse_service_error',
        message: 'SSE service is not working properly. Check server logs.'
      });
    }

    // Generate overall status
    let overallStatus = 'healthy';
    if (diagnostic.authentication.status !== 'authenticated') {
      overallStatus = 'authentication_required';
    } else if (diagnostic.sse_service.status !== 'running') {
      overallStatus = 'service_error';
    }

    res.json({
      success: true,
      overall_status: overallStatus,
      diagnostic,
      next_steps: diagnostic.recommendations.filter(r => r.priority === 'high' || r.priority === 'critical'),
      test_credentials: {
        email: '<EMAIL>',
        password: 'admin123',
        login_url: '/login'
      }
    });

  } catch (error) {
    console.error('❌ Error in SSE diagnostic endpoint:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Test login endpoint - debug authentication issues
router.post('/test-login', async (req, res) => {
  try {
    console.log('🔍 Test login endpoint - debugging authentication...');
    const { email, password } = req.body;

    console.log('🔍 Login attempt:', { email, password: password ? '***' : 'missing' });

    // Check if user exists
    const { success, data: users, error } = await executeQuery(
      'SELECT id, username, email, password, role FROM users WHERE email = ?',
      [email || '<EMAIL>']
    );

    if (!success) {
      console.error('❌ Database error:', error);
      return res.json({
        success: false,
        step: 'database_query',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }

    if (users.length === 0) {
      console.log('❌ User not found');
      return res.json({
        success: false,
        step: 'user_lookup',
        error: 'User not found',
        email: email,
        timestamp: new Date().toISOString()
      });
    }

    const user = users[0];
    console.log('✅ User found:', { id: user.id, email: user.email, role: user.role });

    // Test password comparison
    if (password) {
      const bcrypt = (await import('bcryptjs')).default;
      const isMatch = await bcrypt.compare(password, user.password);
      console.log('🔑 Password match:', isMatch);

      if (!isMatch) {
        return res.json({
          success: false,
          step: 'password_verification',
          error: 'Password does not match',
          user_id: user.id,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Test JWT token generation
    const jwt = (await import('jsonwebtoken')).default;
    const token = jwt.sign(
      { id: user.id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '8h' }
    );

    console.log('✅ JWT token generated successfully');

    res.json({
      success: true,
      message: 'Login test successful',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token_preview: token.substring(0, 20) + '...',
      environment: {
        jwt_secret_exists: !!process.env.JWT_SECRET,
        db_host: process.env.DB_HOST
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Test login error:', error);
    res.status(500).json({
      success: false,
      step: 'general_error',
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }
});

// Authentication status check endpoint
router.get('/auth-status', async (req, res) => {
  try {
    console.log('🔍 Checking authentication status...');
    console.log('🔍 Available cookies:', Object.keys(req.cookies || {}));
    console.log('🔍 Available headers:', Object.keys(req.headers).filter(h => h.includes('auth') || h.includes('token')));

    // Check for authentication token in cookies
    const token = req.cookies.token;

    if (!token) {
      console.log('❌ No authentication token found in cookies');
      return res.json({
        authenticated: false,
        message: 'No authentication token found in cookies',
        available_cookies: Object.keys(req.cookies || {}),
        timestamp: new Date().toISOString()
      });
    }

    try {
      // Verify the HTTP-only cookie token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('✅ Token verified successfully for user:', decoded.id);

      res.json({
        authenticated: true,
        user_id: decoded.id,
        user_role: decoded.role,
        token_preview: token.substring(0, 20) + '...',
        timestamp: new Date().toISOString()
      });

    } catch (jwtError) {
      console.error('❌ JWT verification failed:', jwtError.message);
      res.json({
        authenticated: false,
        error: 'Invalid token',
        message: 'JWT verification failed: ' + jwtError.message,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('❌ Error in auth status endpoint:', error);
    res.status(500).json({
      authenticated: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Simple SSE connection test endpoint
router.get('/test-sse-connection', async (req, res) => {
  try {
    console.log('🔌 Testing SSE connection setup...');

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': req.headers.origin || '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'Cache-Control, Authorization',
    });

    // Send test message
    res.write('data: {"type":"test","message":"SSE connection test successful","timestamp":"' + new Date().toISOString() + '"}\n\n');

    // Send another message after 2 seconds
    setTimeout(() => {
      res.write('data: {"type":"test","message":"Second test message","timestamp":"' + new Date().toISOString() + '"}\n\n');
    }, 2000);

    // Close connection after 5 seconds
    setTimeout(() => {
      res.write('data: {"type":"test","message":"Connection closing","timestamp":"' + new Date().toISOString() + '"}\n\n');
      res.end();
    }, 5000);

    // Handle client disconnect
    req.on('close', () => {
      console.log('🔌 Test SSE client disconnected');
    });

  } catch (error) {
    console.error('❌ Error in SSE connection test:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
});

// Test SSE token generation endpoint
router.get('/test-sse-token', async (req, res) => {
  try {
    console.log('🔑 Testing SSE token generation...');
    console.log('🔑 Available cookies:', Object.keys(req.cookies || {}));
    console.log('🔑 Available headers:', Object.keys(req.headers).filter(h => h.includes('auth') || h.includes('token')));

    // Check for authentication token in cookies
    const token = req.cookies.token;

    if (!token) {
      console.log('❌ No authentication token found in cookies');
      return res.status(401).json({
        success: false,
        error: 'Not authenticated',
        message: 'No authentication token found in cookies',
        available_cookies: Object.keys(req.cookies || {}),
        timestamp: new Date().toISOString()
      });
    }

    try {
      // Verify the HTTP-only cookie token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('✅ Token verified successfully for user:', decoded.id);

      // Create a short-lived token specifically for SSE (5 minutes)
      const sseToken = jwt.sign(
        { id: decoded.id, role: decoded.role, purpose: 'sse' },
        process.env.JWT_SECRET,
        { expiresIn: '5m' }
      );

      console.log('✅ SSE token generated successfully');

      res.json({
        success: true,
        message: 'SSE token generated successfully',
        user_id: decoded.id,
        user_role: decoded.role,
        token_preview: sseToken.substring(0, 50) + '...',
        timestamp: new Date().toISOString()
      });

    } catch (jwtError) {
      console.error('❌ JWT verification failed:', jwtError.message);
      res.status(401).json({
        success: false,
        error: 'Invalid token',
        message: 'JWT verification failed: ' + jwtError.message,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('❌ Error in SSE token test endpoint:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Test endpoint to trigger SSE notification with database persistence
router.post('/test-sse', async (req, res) => {
  try {
    const title = `Test SSE Notification ${new Date().toLocaleTimeString()}`;
    const message = `This is a test notification to verify real-time SSE functionality at ${new Date().toLocaleString()}`;
    
    console.log('🧪 Creating persistent test notification:', title);
    
    let savedNotification = null;
    let dbError = null;
    
    // Try direct database insert first to bypass NotificationService
    try {
      console.log('🔍 Attempting direct database insert...');
      
      const insertQuery = `INSERT INTO notifications 
        (title, message, category, priority, severity, source, machine_id, user_id, timestamp, \`read\`) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), false)`;
      
      const insertParams = [title, message, 'info', 'medium', 'info', 'test_endpoint', null, null];
      
      console.log('🔍 Direct insert query params:', insertParams);
      
      const result = await db.execute(insertQuery, insertParams);
      const notificationId = result[0].insertId;
      
      console.log('✅ Direct insert successful, ID:', notificationId);
      
      // Get the created notification
      const selectQuery = "SELECT * FROM notifications WHERE id = ?";
      const selectResult = await db.execute(selectQuery, [notificationId]);
      
      if (selectResult[0].length > 0) {
        savedNotification = selectResult[0][0];
        console.log('✅ Test notification saved to database via direct insert:', savedNotification.id);
      } else {
        throw new Error('Notification not found after direct insert');
      }
      
    } catch (directError) {
      dbError = directError;
      console.log('⚠️ Direct database insert failed:', directError.message);
      
      // Fallback: try NotificationService with shorter timeout
      try {
        console.log('🔄 Trying NotificationService as fallback...');
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('NotificationService timed out')), 3000)
        );
        
        const dbPromise = NotificationService.createNotification({
          title,
          message,
          category: 'info',
          priority: 'medium',
          severity: 'info',
          source: 'test_endpoint',
          machine_id: null,
          userId: null
        });
        
        savedNotification = await Promise.race([dbPromise, timeoutPromise]);
        console.log('✅ Test notification saved via NotificationService:', savedNotification.id);
        
      } catch (serviceError) {
        console.log('⚠️ NotificationService also failed:', serviceError.message);
        
        // Create mock notification for SSE broadcast
        savedNotification = {
          id: Date.now(),
          title,
          message,
          category: 'info',
          priority: 'medium',
          severity: 'info',
          source: 'test_endpoint',
          machine_id: null,
          user_id: null,
          timestamp: new Date().toISOString(),
          created_at: new Date().toISOString(),
          read_at: null,
          acknowledged_at: null,
          read: false,
          acknowledged: false
        };
        console.log('🔄 Using mock notification for SSE broadcast');
        dbError = serviceError;
      }
    }
    
    // Broadcast via SSE regardless of database result
    console.log('📡 Broadcasting test notification via SSE:', savedNotification.title);
    const broadcastResult = await sseNotificationService.broadcastNotification(savedNotification);
    console.log('📊 SSE broadcast result:', broadcastResult);
    
    // Return response with detailed status
    res.json({
      success: true,
      notification: savedNotification,
      broadcast_result: broadcastResult,
      database_saved: !dbError,
      database_error: dbError?.message || null,
      message: dbError 
        ? 'Test notification broadcast via SSE (database save failed - will disappear on reload)'
        : 'Test notification saved to database and broadcast via SSE'
    });

  } catch (error) {
    console.error('❌ Error in test notification endpoint:', error);
    res.status(500).json({
      error: 'Failed to create test notification',
      message: error.message
    });
  }
});

// External notification endpoints using SuperAgent

/**
 * Send Slack notification
 * POST /api/notifications/external/slack
 */
router.post('/external/slack', auth, [
  check('webhook_url').isURL().withMessage('Valid webhook URL is required'),
  check('message').notEmpty().withMessage('Message is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { webhook_url, message, type, channel, username } = req.body;
    
    const slackMessage = {
      text: message,
      type: type || 'info',
      channel,
      username: username || 'LocQL System',
      details: req.body.details,
      attachments: req.body.attachments || [],
      blocks: req.body.blocks || []
    };

    const result = await externalNotificationService.sendSlackNotification(webhook_url, slackMessage);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Slack notification sent successfully',
        result
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send Slack notification',
        details: result.error
      });
    }
  } catch (error) {
    console.error('Slack notification endpoint error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Send Teams notification
 * POST /api/notifications/external/teams
 */
router.post('/external/teams', auth, [
  check('webhook_url').isURL().withMessage('Valid webhook URL is required'),
  check('message').notEmpty().withMessage('Message is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { webhook_url, message, type, title, subtitle } = req.body;
    
    const teamsMessage = {
      text: message,
      type: type || 'info',
      title: title || 'LocQL Notification',
      subtitle: subtitle || 'Manufacturing System Alert',
      facts: req.body.facts || [],
      actions: req.body.actions || []
    };

    const result = await externalNotificationService.sendTeamsNotification(webhook_url, teamsMessage);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Teams notification sent successfully',
        result
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send Teams notification',
        details: result.error
      });
    }
  } catch (error) {
    console.error('Teams notification endpoint error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Send Discord notification
 * POST /api/notifications/external/discord
 */
router.post('/external/discord', auth, [
  check('webhook_url').isURL().withMessage('Valid webhook URL is required'),
  check('message').notEmpty().withMessage('Message is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { webhook_url, message, type, title, username } = req.body;
    
    const discordMessage = {
      text: message,
      type: type || 'info',
      title: title || 'System Notification',
      username: username || 'LocQL System',
      embeds: req.body.embeds || [],
      tts: req.body.tts || false
    };

    const result = await externalNotificationService.sendDiscordNotification(webhook_url, discordMessage);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Discord notification sent successfully',
        result
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send Discord notification',
        details: result.error
      });
    }
  } catch (error) {
    console.error('Discord notification endpoint error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Send multi-platform notification
 * POST /api/notifications/external/multi
 */
router.post('/external/multi', auth, [
  check('message').notEmpty().withMessage('Message is required'),
  check('platforms').isArray().withMessage('Platforms array is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { message, platforms, type, title } = req.body;
    
    const universalMessage = {
      text: message,
      type: type || 'info',
      title: title || 'LocQL System Alert',
      details: req.body.details,
      facts: req.body.facts || [],
      timestamp: new Date().toISOString()
    };

    const result = await externalNotificationService.sendMultiPlatformNotification(universalMessage, platforms);
    
    res.json({
      success: result.overall.success,
      message: `Sent to ${result.overall.successfulSends}/${result.overall.totalPlatforms} platforms`,
      summary: result.overall,
      detailed_results: result.results
    });
  } catch (error) {
    console.error('Multi-platform notification endpoint error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Register webhook endpoint
 * POST /api/notifications/webhooks/register
 */
router.post('/webhooks/register', auth, [
  check('name').notEmpty().withMessage('Webhook name is required'),
  check('url').isURL().withMessage('Valid webhook URL is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, url, headers, auth: webhookAuth, timeout, format } = req.body;
    
    const config = {
      url,
      headers: headers || {},
      auth: webhookAuth || null,
      timeout: timeout || 30000,
      format: format || 'json',
      active: true
    };

    externalNotificationService.registerWebhook(name, config);
    
    res.json({
      success: true,
      message: `Webhook '${name}' registered successfully`,
      webhook: {
        name,
        url: url.substring(0, 50) + '...',
        format: config.format,
        timeout: config.timeout
      }
    });
  } catch (error) {
    console.error('Webhook registration error:', error);
    res.status(500).json({
      error: 'Failed to register webhook',
      message: error.message
    });
  }
});

/**
 * Test webhook connectivity
 * POST /api/notifications/webhooks/test/:webhookName
 */
router.post('/webhooks/test/:webhookName', auth, async (req, res) => {
  try {
    const { webhookName } = req.params;
    const result = await externalNotificationService.testWebhook(webhookName);
    
    const statusCode = result.success ? 200 : 500;
    res.status(statusCode).json(result);
  } catch (error) {
    console.error('Webhook test error:', error);
    res.status(500).json({
      error: 'Failed to test webhook',
      message: error.message
    });
  }
});

/**
 * Get registered webhooks
 * GET /api/notifications/webhooks
 */
router.get('/webhooks', auth, async (req, res) => {
  try {
    const webhooks = externalNotificationService.getRegisteredWebhooks();
    res.json({
      success: true,
      webhooks,
      total: webhooks.length
    });
  } catch (error) {
    console.error('Get webhooks error:', error);
    res.status(500).json({
      error: 'Failed to retrieve webhooks',
      message: error.message
    });
  }
});

/**
 * Update webhook status (enable/disable)
 * PUT /api/notifications/webhooks/:webhookName/status
 */
router.put('/webhooks/:webhookName/status', auth, [
  check('active').isBoolean().withMessage('Active status must be boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { webhookName } = req.params;
    const { active } = req.body;
    
    const success = externalNotificationService.setWebhookStatus(webhookName, active);
    
    if (success) {
      res.json({
        success: true,
        message: `Webhook '${webhookName}' ${active ? 'enabled' : 'disabled'}`,
        webhook: webhookName,
        active
      });
    } else {
      res.status(404).json({
        success: false,
        error: `Webhook '${webhookName}' not found`
      });
    }
  } catch (error) {
    console.error('Webhook status update error:', error);
    res.status(500).json({
      error: 'Failed to update webhook status',
      message: error.message
    });
  }
});

// ============================================================================
// EMAIL NOTIFICATION ROUTES - Phase 4 SMTP Integration
// ============================================================================

/**
 * Test email configuration
 * POST /api/notifications/email/test
 */
router.post('/email/test',
  auth,
  rateLimiter.notificationCreationLimiter(),
  [
    check('email')
      .isEmail()
      .withMessage('Valid email address is required')
      .normalizeEmail()
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { email } = req.body;
      console.log(`🧪 Testing email configuration for: ${email}`);

      // Test email configuration
      const result = await NotificationService.testEmailConfiguration(email);

      if (result.success) {
        res.json({
          success: true,
          message: 'Test email sent successfully',
          data: {
            messageId: result.messageId,
            deliveryTime: result.deliveryTime,
            recipients: result.recipients
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Test email failed',
          error: result.error
        });
      }

    } catch (error) {
      console.error('❌ Email test error:', error);
      res.status(500).json({
        error: 'Email test failed',
        message: error.message
      });
    }
  }
);

/**
 * Get email service health status
 * GET /api/notifications/email/health
 */
router.get('/email/health', auth, async (req, res) => {
  try {
    console.log('🏥 Checking email service health');

    const healthStatus = await NotificationService.getEmailServiceHealth();

    if (healthStatus.status === 'healthy') {
      res.json({
        success: true,
        status: 'healthy',
        message: 'Email service is operational',
        data: healthStatus
      });
    } else {
      res.status(503).json({
        success: false,
        status: 'unhealthy',
        message: 'Email service is not operational',
        data: healthStatus
      });
    }

  } catch (error) {
    console.error('❌ Email health check error:', error);
    res.status(500).json({
      error: 'Health check failed',
      message: error.message
    });
  }
});

/**
 * Get email delivery statistics
 * GET /api/notifications/email/stats
 */
router.get('/email/stats', auth, async (req, res) => {
  try {
    console.log('📊 Retrieving email delivery statistics');

    const stats = NotificationService.getEmailDeliveryStats();

    res.json({
      success: true,
      message: 'Email delivery statistics retrieved',
      data: stats
    });

  } catch (error) {
    console.error('❌ Email stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve email statistics',
      message: error.message
    });
  }
});

/**
 * Send shift report via email
 * POST /api/notifications/email/shift-report
 */
router.post('/email/shift-report',
  auth,
  rateLimiter.notificationCreationLimiter(),
  [
    check('reportData')
      .notEmpty()
      .withMessage('Report data is required'),
    check('recipients')
      .optional()
      .isArray()
      .withMessage('Recipients must be an array of email addresses')
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { reportData, recipients } = req.body;
      console.log(`📊 Sending shift report email`);

      // Send shift report email
      const result = await NotificationService.sendShiftReportEmail(reportData, recipients);

      if (result.sent !== false) {
        res.json({
          success: true,
          message: 'Shift report email sent successfully',
          data: {
            messageId: result.messageId,
            recipients: result.recipients,
            deliveryTime: result.deliveryTime
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Shift report email not sent',
          reason: result.reason
        });
      }

    } catch (error) {
      console.error('❌ Shift report email error:', error);
      res.status(500).json({
        error: 'Failed to send shift report email',
        message: error.message
      });
    }
  }
);

/**
 * Create critical machine alert with email delivery
 * POST /api/notifications/critical/machine-alert
 */
router.post('/critical/machine-alert',
  auth,
  rateLimiter.notificationCreationLimiter(),
  [
    check('title')
      .notEmpty()
      .withMessage('Title is required')
      .isLength({ max: 255 })
      .withMessage('Title must be less than 255 characters'),
    check('message')
      .notEmpty()
      .withMessage('Message is required')
      .isLength({ max: 1000 })
      .withMessage('Message must be less than 1000 characters'),
    check('machineId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Machine ID must be a positive integer')
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { title, message, machineId } = req.body;
      console.log(`🚨 Creating critical machine alert with email delivery`);

      // Create critical machine alert with email
      const notification = await NotificationService.createCriticalMachineAlert(title, message, machineId);

      // Send notification via SSE
      if (notification.userId) {
        await sseNotificationService.sendNotificationToUser(notification.userId, notification);
      } else {
        await sseNotificationService.broadcastNotification(notification);
      }

      res.status(201).json({
        success: true,
        message: 'Critical machine alert created and sent',
        data: notification
      });

    } catch (error) {
      console.error('❌ Critical machine alert error:', error);
      res.status(500).json({
        error: 'Failed to create critical machine alert',
        message: error.message
      });
    }
  }
);





/**
 * Get email retry queue statistics
 * GET /api/notifications/email/retry-stats
 */
router.get('/email/retry-stats', auth, async (req, res) => {
  try {
    console.log('📊 Getting email retry queue statistics');

    const stats = await emailRetryService.getRetryQueueStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Error getting retry queue stats:', error);
    res.status(500).json({
      error: 'Failed to get retry queue statistics',
      message: error.message
    });
  }
});

/**
 * Get notification escalation statistics
 * GET /api/notifications/escalation/stats
 */
router.get('/escalation/stats', auth, async (req, res) => {
  try {
    console.log('📊 Getting notification escalation statistics');

    const stats = await notificationEscalationService.getEscalationStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Error getting escalation stats:', error);
    res.status(500).json({
      error: 'Failed to get escalation statistics',
      message: error.message
    });
  }
});

/**
 * Create notification with dual-channel delivery
 * POST /api/notifications/dual-channel
 */
router.post('/dual-channel',
  auth,
  rateLimiter.notificationCreationLimiter(),
  [
    check("title", "Title is required and must be between 1-255 characters")
      .isLength({ min: 1, max: 255 })
      .escape(),
    check("message", "Message is required and must be between 1-2000 characters")
      .isLength({ min: 1, max: 2000 })
      .escape(),
    check("category", "Invalid category")
      .isIn(["alert", "maintenance", "update", "info", "machine_alert", "production", "quality"]),
    check("priority", "Invalid priority")
      .optional()
      .isIn(["low", "medium", "high", "critical"]),
    check("severity", "Invalid severity")
      .optional()
      .isIn(["info", "warning", "error", "critical"])
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        title,
        message,
        category,
        priority = 'medium',
        severity = 'info',
        source = 'user',
        machine_id,
        userId
      } = req.body;

      console.log("Creating dual-channel notification:", {
        title,
        category,
        priority,
        severity,
        createdBy: req.user.id
      });

      // Create notification with dual-channel delivery
      const notification = await NotificationService.createNotificationWithDualChannel({
        title,
        message,
        category,
        priority,
        severity,
        source,
        machine_id,
        userId
      });

      // Send notification via SSE
      if (userId) {
        await sseNotificationService.sendNotificationToUser(userId, notification);
      } else {
        await sseNotificationService.broadcastNotification(notification);
      }

      res.status(201).json({
        success: true,
        message: 'Dual-channel notification created and sent',
        data: notification
      });

    } catch (error) {
      console.error('❌ Dual-channel notification error:', error);
      res.status(500).json({
        error: 'Failed to create dual-channel notification',
        message: error.message
      });
    }
  }
);

// Export only the router (no WebSocket components)
/**
 * Test email settings integration
 * POST /api/notifications/email/test-settings
 */
router.post('/email/test-settings',
  auth,
  rateLimiter.notificationCreationLimiter(),
  [
    check('email')
      .isEmail()
      .withMessage('Valid email address is required')
      .normalizeEmail()
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { email } = req.body;
      const userId = req.user?.id || 1;

      console.log(`🧪 Testing email settings integration for user ${userId}: ${email}`);

      // Import email settings integration
      const { default: emailSettingsIntegration } = await import('../services/EmailSettingsIntegration.js');
      const { default: emailNotificationService } = await import('../services/EmailNotificationService.js');

      // Get current email settings
      const settings = await emailSettingsIntegration.getUserEmailSettings(userId);
      const settingsSummary = await emailSettingsIntegration.getSettingsSummary(userId);

      // Create test notification
      const testNotification = {
        id: 'test-' + Date.now(),
        title: 'Test Email Settings Integration',
        message: 'This is a test email to verify that all email settings are working correctly and producing immediate effects.',
        category: 'info',
        priority: 'medium',
        source: 'settings_test',
        timestamp: new Date().toISOString()
      };

      // Check if email should be sent based on settings
      const shouldSendResult = await emailSettingsIntegration.shouldSendEmail(testNotification, userId);

      if (!shouldSendResult.shouldSend) {
        return res.json({
          success: true,
          message: 'Email settings test completed - email not sent due to settings',
          data: {
            settingsSummary,
            shouldSend: false,
            reason: shouldSendResult.reason,
            testNotification
          }
        });
      }

      // Send test email with settings applied
      const result = await emailNotificationService.sendNotificationEmail(
        testNotification,
        [email],
        userId
      );

      if (result.success) {
        res.json({
          success: true,
          message: 'Email settings test completed successfully',
          data: {
            settingsSummary,
            shouldSend: true,
            emailResult: result,
            testNotification,
            appliedSettings: {
              template: settings.template,
              format: settings.format,
              language: settings.language,
              signatureEnabled: settings.signature?.enabled,
              deliveryMethod: settings.delivery?.method
            }
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Email settings test failed',
          error: result.error || result.reason,
          data: {
            settingsSummary,
            shouldSend: true,
            testNotification
          }
        });
      }

    } catch (error) {
      console.error('❌ Email settings test error:', error);
      res.status(500).json({
        error: 'Email settings test failed',
        message: error.message
      });
    }
  }
);

/**
 * Get email settings summary
 * GET /api/notifications/email/settings-summary
 */
router.get('/email/settings-summary', auth, async (req, res) => {
  try {
    const userId = req.user?.id || 1;

    // Import email settings integration
    const { default: emailSettingsIntegration } = await import('../services/EmailSettingsIntegration.js');

    const settingsSummary = await emailSettingsIntegration.getSettingsSummary(userId);
    const fullSettings = await emailSettingsIntegration.getUserEmailSettings(userId);

    res.json({
      success: true,
      data: {
        summary: settingsSummary,
        fullSettings,
        functionalSettings: [
          'enabled',
          'frequency',
          'template',
          'format',
          'language',
          'signature.enabled',
          'signature.text',
          'filtering.minPriority',
          'filtering.maxPerDay',
          'filtering.preventDuplicates',
          'filtering.smartGrouping',
          'delivery.method',
          'delivery.retryFailed',
          'delivery.readReceipts',
          'delivery.tracking',
          'batchSettings.hourlyBatch.enabled',
          'batchSettings.dailyDigest.enabled'
        ]
      }
    });

  } catch (error) {
    console.error('❌ Email settings summary error:', error);
    res.status(500).json({
      error: 'Failed to get email settings summary',
      message: error.message
    });
  }
});

export default router;

