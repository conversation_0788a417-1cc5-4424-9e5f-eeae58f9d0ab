import React, { memo } from 'react';
import { Responsive<PERSON><PERSON>r, AreaChart, Area, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Spin, Empty, Typography } from 'antd';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const { Text } = Typography;

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE, 
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOMIPEM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
}

const ArretAreaChart = memo(({ data = [], loading = false, title = "Durée Moyenne par Heure" }) => {
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <Text type="secondary">Chargement des données de tendance...</Text>
      </div>
    );  }

  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Empty 
          description="Aucune donnée de tendance disponible"
          style={{ color: '#8c8c8c' }}
        />
      </div>
    );
  }
  // Process and sanitize data
  const processedData = safeData
    .map((item) => ({
      hour: Number.parseInt(item.hour || item.heure || 0),
      avgDuration: Number.parseFloat(item.avgDuration || item.duree_moyenne || 0),
      count: Number.parseInt(item.count || item.nombre || 0),
      label: `${item.hour || item.heure || 0}h`
    }))
    .filter(item => !isNaN(item.hour) && !isNaN(item.avgDuration))
    .sort((a, b) => a.hour - b.hour);

  if (processedData.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Empty 
          description="Données de tendance invalides"
          style={{ color: '#8c8c8c' }}
        />
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#262626' }}>
            {`Heure: ${label}h`}
          </p>
          {payload.map((entry, index) => (
            <p key={index} style={{ 
              margin: '4px 0 0 0', 
              color: entry.color,
              fontSize: '13px'
            }}>
              {`${entry.name}: ${entry.value.toFixed(1)} min`}
            </p>
          ))}
          {payload[0]?.payload?.count && (
            <p style={{ 
              margin: '4px 0 0 0', 
              color: '#8c8c8c',
              fontSize: '12px'
            }}>
              {`Nombre d'arrêts: ${payload[0].payload.count}`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart 
          data={processedData} 
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <defs>
            <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={CHART_COLORS.success} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={CHART_COLORS.success} stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke="#f0f0f0"
            vertical={false}
          />
          
          <XAxis
            dataKey="hour"
            type="number"
            scale="linear"
            domain={['dataMin', 'dataMax']}
            tickFormatter={(value) => `${value}h`}
            tick={{ fill: '#666', fontSize: 12 }}
            axisLine={{ stroke: '#d9d9d9' }}
            tickLine={{ stroke: '#d9d9d9' }}
            label={{
              value: 'Heure de la journée',
              position: 'insideBottom',
              offset: -10,
              style: { fill: '#666', fontSize: 12 }
            }}
          />
          
          <YAxis
            tick={{ fill: '#666', fontSize: 12 }}
            axisLine={{ stroke: '#d9d9d9' }}
            tickLine={{ stroke: '#d9d9d9' }}
            label={{
              value: 'Durée moyenne (min)',
              angle: -90,
              position: 'insideLeft',
              style: { fill: '#666', fontSize: 12 }
            }}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          <Legend
            wrapperStyle={{
              paddingTop: '20px',
              fontSize: '12px'
            }}
          />

          <Area
            type="monotone"
            dataKey="avgDuration"
            stroke={CHART_COLORS.success}
            strokeWidth={3}
            fill="url(#areaGradient)"
            name="Durée moyenne"
            dot={{
              fill: CHART_COLORS.success,
              strokeWidth: 2,
              r: 4
            }}
            activeDot={{
              r: 6,
              fill: '#fff',
              stroke: CHART_COLORS.success,
              strokeWidth: 3
            }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>  );
});

export default ArretAreaChart;
