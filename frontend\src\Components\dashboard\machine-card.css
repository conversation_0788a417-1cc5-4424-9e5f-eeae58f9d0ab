/* Machine Card Styles */
.machine-card {
    transition: all 0.3s ease;
  }

  .machine-card:hover:not(.machine-offline) {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  }

  .machine-card .ant-progress-circle-trail {
    stroke: rgba(0, 0, 0, 0.04);
  }

  .machine-card .ant-card-body {
    padding: 0;
  }

  .machine-card .ant-statistic-title {
    margin-bottom: 4px;
  }

  .machine-card .ant-statistic-content {
    font-size: 16px;
  }

  /* Responsive adjustments */
  @media (max-width: 576px) {
    .machine-card .ant-statistic-content {
      font-size: 14px;
    }
  }

  .machine-card-container {
    position: relative;
    transition: all 0.3s;
  }

  .machine-card-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .machine-blur-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(2px);
    background-color: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
    transition: all 0.3s ease;
  }

  .machine-blur-overlay:hover {
    backdrop-filter: blur(1px);
    background-color: rgba(0, 0, 0, 0.03);
  }

  .soon-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(2px);
    background-color: rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
    border: 2px dashed var(--somipem-primary-blue, #1E3A8A);
    transition: all 0.3s ease;
  }

  .soon-overlay:hover {
    background-color: rgba(0, 0, 0, 0.03);
    border-color: var(--somipem-secondary-blue, #3B82F6);
  }

  .soon-text {
    font-size: 28px;
    font-weight: bold;
    color: var(--somipem-primary-blue, #1E3A8A);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
  }

  /* Offline machine styles */
  .machine-offline {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    pointer-events: none;
  }

  .machine-offline .ant-card-body {
    pointer-events: none;
  }

  .machine-offline .ant-progress-circle-path {
    stroke: #d9d9d9 !important;
  }

  .machine-offline .ant-statistic-content-value {
    color: rgba(0, 0, 0, 0.45) !important;
  }

  /* Disable hover effects for offline machines */
  .machine-offline:hover {
    transform: none !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  }
