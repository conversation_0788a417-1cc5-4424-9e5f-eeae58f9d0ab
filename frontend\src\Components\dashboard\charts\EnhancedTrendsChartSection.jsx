import React, { useMemo } from "react";
import { Row, Col, Space, Typography, Tag, Empty } from "antd";
import { LineChartOutlined } from "@ant-design/icons";
import {
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
} from "recharts";
import dayjs from "dayjs";
import UnifiedChartExpansion from "../../charts/UnifiedChartExpansion/UnifiedChartExpansion";
import {
  EnhancedQuantityBarChart,
  EnhancedTRSLineChart,
  EnhancedCycleTimeLineChart,
} from "../../charts/ChartExpansion/EnhancedChartComponents";
import { getDynamicColors } from "../../../utils/chartColors";
import { useSettings } from "../../../hooks/useSettings";
import { useUnifiedChartConfig } from "../../../hooks/useUnifiedChartConfig";

const { Text } = Typography;

/**
 * Enhanced container component for the trends chart section with expansion capabilities
 * @param {Object} props - Component props
 * @param {Array} props.data - The data to display in the charts
 * @param {Array} props.colors - Array of colors for styling
 * @param {string} props.dateRangeType - The type of date range (day, week, month)
 * @param {string} props.dateFilter - The date filter value
 * @param {function} props.formatDateRange - Function to format the date range for display
 * @returns {JSX.Element} The rendered enhanced chart section
 */
const EnhancedTrendsChartSection = ({
  data,
  colors: propColors,
  dateRangeType,
  dateFilter,
  formatDateRange
}) => {
  // Get settings for dynamic colors and unified chart configuration
  const { settings } = useSettings();

  // Get unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    chartType: 'bar', // Default type for trends
    allowedTypes: ['bar', 'line']
  });

  // Use dynamic colors from settings, fallback to prop colors
  const colors = useMemo(() => {
    const dynamicColors = getDynamicColors(settings);

    // If null is returned (default mode), use prop colors or original colors
    if (dynamicColors === null) {
      return propColors || [
        '#1890ff', // Default blue
        '#52c41a', // Default green
        '#faad14', // Default orange
        '#f5222d', // Default red
        '#722ed1'  // Default purple
      ];
    }

    return dynamicColors;
  }, [settings, propColors]);

  // Chart expansion handlers
  const handleChartExpand = (chartName) => {
    console.log(`Chart expanded: ${chartName}`);
    // You can add analytics tracking here
  };

  const handleChartCollapse = (chartName) => {
    console.log(`Chart collapsed: ${chartName}`);
    // You can add analytics tracking here
  };

  return (
    <>
      {/* Enhanced Quantity Charts */}
      <Col span={24}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <UnifiedChartExpansion
              title={`Quantité Bonne - ${dateRangeType === "day" ? "Journalière" : dateRangeType === "week" ? "Hebdomadaire" : "Mensuelle"}`}
              data={data}
              chartType="bar"
              expandMode="modal"
              onExpand={() => handleChartExpand("quantity-good")}
              onCollapse={() => handleChartCollapse("quantity-good")}
              exportEnabled={true}
              cardProps={{
                extra: (
                  <Tag color={dateFilter ? "blue" : "green"}>
                    {formatDateRange(dateFilter, dateRangeType)}
                  </Tag>
                )
              }}
            >
              <EnhancedQuantityBarChart
                data={data}
                title="Quantité Bonne"
                dataKey="good"
                color={colors[2]}
                tooltipLabel="Quantité bonne"
              />
            </UnifiedChartExpansion>
          </Col>

          <Col xs={24} md={12}>
            <UnifiedChartExpansion
              title={`Quantité Rejetée - ${dateRangeType === "day" ? "Journalière" : dateRangeType === "week" ? "Hebdomadaire" : "Mensuelle"}`}
              data={data}
              chartType="bar"
              expandMode="modal"
              onExpand={() => handleChartExpand("quantity-reject")}
              onCollapse={() => handleChartCollapse("quantity-reject")}
              exportEnabled={true}
              cardProps={{
                extra: (
                  <Tag color={dateFilter ? "blue" : "green"}>
                    {formatDateRange(dateFilter, dateRangeType)}
                  </Tag>
                )
              }}
            >
              <EnhancedQuantityBarChart
                data={data}
                title="Quantité Rejetée"
                dataKey="reject"
                color={colors[4]}
                label="Quantité"
                tooltipLabel="Quantité rejetée"
                isKg={true}
              />
            </UnifiedChartExpansion>
          </Col>
        </Row>
      </Col>

      {/* Enhanced TRS and Cycle Time Charts */}
      <Col xs={24} md={24}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <UnifiedChartExpansion
              title="Tendances TRS (Taux de Rendement Synthétique)"
              data={data}
              chartType="line"
              expandMode="modal"
              onExpand={() => handleChartExpand("trs-trends")}
              onCollapse={() => handleChartCollapse("trs-trends")}
              exportEnabled={true}
              cardProps={{
                extra: <Tag color="cyan">Évolution TRS</Tag>
              }}
            >
              <EnhancedTRSLineChart
                data={data}
                color={colors[0]}
              />
            </UnifiedChartExpansion>
          </Col>

          <Col xs={24} md={12}>
            <UnifiedChartExpansion
              title="Tendances Cycle De Temps"
              data={data}
              chartType="line"
              expandMode="modal"
              onExpand={() => handleChartExpand("cycle-time-trends")}
              onCollapse={() => handleChartCollapse("cycle-time-trends")}
              exportEnabled={true}
              cardProps={{
                extra: <Tag color="orange">Évolution Cycle</Tag>
              }}
            >
              <EnhancedCycleTimeLineChart
                data={data}
                color={colors[1]}
              />
            </UnifiedChartExpansion>
          </Col>
        </Row>
      </Col>


    </>
  );
};

/**
 * Enhanced Combined Chart Component
 */
const EnhancedCombinedChart = ({
  data,
  colors,
  height, // Will be provided by parent using settings
  enhanced = false,
  zoom = 1,
}) => {
  // Get unified chart configuration for consistent settings
  const chartConfig = useUnifiedChartConfig({
    chartType: 'bar',
    allowedTypes: ['bar', 'line']
  });
  if (!data || data.length === 0) {
    return (
      <div style={{ height: height || chartConfig.height, display: "flex", alignItems: "center", justifyContent: "center" }}>
        <Empty description="Aucune donnée disponible" />
      </div>
    );
  }

  // Use settings-based configuration instead of hardcoded values
  const finalHeight = height || chartConfig.height;
  const fontSize = enhanced ? 14 : 12;

  return (
    <ResponsiveContainer width="100%" height={finalHeight}>
      <ComposedChart data={data} margin={chartConfig.margins}>
        <CartesianGrid {...chartConfig.gridConfig} />
        <XAxis
          dataKey="date"
          {...chartConfig.axisConfig}
          tick={{ fontSize }}
          tickFormatter={(date) => {
            try {
              if (date && dayjs(date).isValid()) {
                return enhanced ? dayjs(date).format("DD/MM/YYYY") : dayjs(date).format("DD/MM");
              }
              return "N/A";
            } catch (e) {
              return "N/A";
            }
          }}
          interval={enhanced ? 0 : "preserveStartEnd"}
          angle={enhanced ? -45 : 0}
          textAnchor={enhanced ? "end" : "middle"}
          height={enhanced ? 80 : 60}
        />
        <YAxis
          yAxisId="left"
          {...chartConfig.axisConfig}
          tick={{ fontSize }}
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Quantité",
            angle: -90,
            position: "insideLeft",
            style: { fontSize },
          }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          {...chartConfig.axisConfig}
          tick={{ fontSize }}
          tickFormatter={(value) => `${value}%`}
          domain={[0, 100]}
          label={{
            value: "TRS (%)",
            angle: 90,
            position: "insideRight",
            style: { fontSize },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: enhanced ? 14 : 12,
          }}
          formatter={(value, name) => {
            const isValidNumber = typeof value === "number" && !isNaN(value);
            const formattedValue = isValidNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value;

            const labels = {
              good: "Quantité bonne",
              reject: "Quantité rejetée (kg)",
              oee: "TRS (%)",
            };

            return [formattedValue, labels[name] || name];
          }}
          labelFormatter={(label) => {
            try {
              if (label && dayjs(label).isValid()) {
                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
              }
              return "Date: N/A";
            } catch (e) {
              return "Date: N/A";
            }
          }}
        />
        {chartConfig.charts?.showLegend && (
          <Legend
            {...chartConfig.legendConfig}
            wrapperStyle={{ paddingTop: 20 }}
            formatter={(value) => {
              const labels = {
                good: "Quantité bonne",
                reject: "Quantité rejetée (kg)",
                oee: "TRS (%)",
              };
              return <span>{labels[value] || value}</span>;
            }}
          />
        )}
        <Bar
          yAxisId="left"
          dataKey="good"
          name="good"
          fill={colors[2]}
          maxBarSize={enhanced ? 60 : 40}
          stackId="production"
        />
        <Bar
          yAxisId="left"
          dataKey="reject"
          name="reject"
          fill={colors[4]}
          maxBarSize={enhanced ? 60 : 40}
          stackId="production"
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="oee"
          name="oee"
          stroke={colors[0]}
          strokeWidth={enhanced ? 3 : 2}
          dot={{ r: enhanced ? 6 : 4, fill: colors[0] }}
          activeDot={{ r: enhanced ? 8 : 6, fill: "#fff", stroke: colors[0], strokeWidth: 2 }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default EnhancedTrendsChartSection;
