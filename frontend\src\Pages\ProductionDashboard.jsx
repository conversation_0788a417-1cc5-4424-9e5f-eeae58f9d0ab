import React, { useState, useEffect, memo, useMemo, useCallback } from "react"
import ShiftReportButton from "../Components/ShiftReportButton"
import SOMIPEM_COLORS from "../styles/brand-colors"
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Spin,
  Typography,
  Progress,
  Grid,
  Tabs,
  Button,
  Space,
  Badge,
  Tooltip,
  Skeleton,
  Alert,
} from "antd"

// Simple date formatting function
const formatApiDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split('T')[0];
};
import { normalizePercentage } from "../utils/dataUtils"
import {
  DashboardOutlined,
  LineChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  TableOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  SearchOutlined,
} from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"

// Import custom hooks and components
import { useProduction, ProductionProvider } from "../context/ProductionContext"
import useDailyTableGraphQL from "../hooks/useDailyTableGraphQL"
import FilterPanel from "../Components/FilterPanel"
import MinimalFilterTest from "../Components/MinimalFilterTest"
import SearchResultsDisplay from "../Components/search/SearchResultsDisplay"
import GlobalSearchModal from "../Components/search/GlobalSearchModal"
import { useSettings } from '../hooks/useSettings';
import { useUnifiedDashboardCharts, getStandardChartExpansionProps } from '../utils/unifiedChartUtils';
// Removed live monitoring imports to improve performance and fix filter issues

// Import extracted components
import getStatisticsConfig from "../Components/dashboard/StatisticsConfig";
import getMachinePerformanceColumns from "../Components/dashboard/MachinePerformanceColumns";
import getProductionTableColumns from "../Components/dashboard/ProductionTableColumns";
import EnhancedTrendsChartSection from "../Components/dashboard/charts/EnhancedTrendsChartSection";
import UnifiedChartExpansion from "../Components/charts/UnifiedChartExpansion/UnifiedChartExpansion";
import {
  EnhancedPieChart,
  EnhancedMachineRejectsChart,
  EnhancedMachineTRSChart,
  EnhancedShiftTRSLineChart,
  EnhancedPerformanceLineChart
} from "../Components/charts/ChartExpansion/EnhancedChartComponents";
// Import new dynamic chart components
import EnhancedShiftChart from "../Components/charts/ChartExpansion/EnhancedShiftChart";
import EnhancedMachineChart from "../Components/charts/ChartExpansion/EnhancedMachineChart";

// Import performance optimization components
import OptimizedTable from "../Components/tables/OptimizedTable";
import OptimizedChart from "../Components/charts/OptimizedChart";
// import TestDowntimeChart from "../Components/TestDowntimeChart";

// Simple date formatting utility
const formatDateRange = (dateFilter, dateRangeType) => {
  if (!dateFilter?.start) return "Toutes les dates";
  
  const start = dayjs(dateFilter.start);
  const end = dateFilter.end ? dayjs(dateFilter.end) : start;
  
  if (dateRangeType === 'day') {
    return start.format('DD/MM/YYYY');
  } else if (dateRangeType === 'week') {
    return `${start.format('DD/MM')} - ${end.format('DD/MM/YYYY')}`;
  } else if (dateRangeType === 'month') {
    return start.format('MM/YYYY');
  }
  
  return `${start.format('DD/MM/YYYY')} - ${end.format('DD/MM/YYYY')}`;
};

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text, Paragraph } = Typography
const { useBreakpoint } = Grid

// Import dynamic colors utility for the unified chart system
// (This is automatically handled by useUnifiedDashboardCharts)

// Main component that uses the ProductionContext
const OptimizedProduction = () => {
  // Initialize unified chart configuration
  const chartConfig = useUnifiedDashboardCharts();
  
  // For backward compatibility, maintain the old interface
  const { getChartColor, getChartColors, charts } = chartConfig;

  // Get filter state from the ProductionContext with coordination
  const {
    dateFilter,
    dateRangeType,
    dateRangeDescription,
    selectedMachineModel,
    selectedMachine,
    machineModels,
    filteredMachineNames,
    handleMachineModelChange,
    handleMachineChange,
    handleDateChange,
    handleDateRangeTypeChange,
    resetFilters,
    handleRefresh,
    coordinationStatus,
    isProcessingFilters,
    triggerCoordination
  } = useProduction();

  // Build date query parameters function
  const buildDateQueryParams = useCallback(() => {
    const params = {};
    
    if (selectedMachineModel) {
      params.model = selectedMachineModel; // Changed from machineModel to model
    }
    
    if (selectedMachine) {
      params.machine = selectedMachine;
    }
    
    if (dateFilter?.start && dateFilter?.end) {
      params.startDate = dateFilter.start.format('YYYY-MM-DD');
      params.endDate = dateFilter.end.format('YYYY-MM-DD');
    }
    
    params.dateRangeType = dateRangeType;
    
    return params;
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType]);

  // Use GraphQL for all data
  const {
    getDashboardData,
    getAllDailyProduction,
    getProductionSidecards,
    getMachinePerformance,
    getShiftPerformance,
    getMachineModels,
    getMachineNames,
    getProductionChart,
    loading: graphqlLoading,
    error: graphqlError
  } = useDailyTableGraphQL();

  // GraphQL data state with data source tracking
  const [productionData, setProductionData] = useState({
    allDailyProduction: [],
    productionChart: { data: [], dataSource: 'unknown' },
    sidecards: { goodqty: 0, rejetqty: 0, dataSource: 'unknown' },
    machinePerformance: { data: [], dataSource: 'unknown' },
    shiftPerformance: { data: [], dataSource: 'unknown' },
    availabilityTrend: []
  });

  // Data source status state
  const [dataSourceStatus, setDataSourceStatus] = useState({
    elasticsearch: false,
    mysql: true,
    primary: 'mysql'
  });

  // Removed live monitoring imports to improve performance and fix filter issues

  // Fetch all GraphQL data based on filters
  const fetchAllData = useCallback(async () => {
    try {
      // Build GraphQL filter object with proper date formatting
      const filters = {
        dateRangeType,
        model: selectedMachineModel || undefined,
        machine: selectedMachine || undefined
      };

      // FIXED: Proper date formatting for GraphQL
      if (dateFilter) {
        // Convert dayjs object to YYYY-MM-DD format
        if (typeof dateFilter.format === 'function') {
          // It's a dayjs object
          filters.date = dateFilter.format('YYYY-MM-DD');
        } else if (dateFilter instanceof Date) {
          // It's a Date object
          filters.date = dateFilter.toISOString().split('T')[0];
        } else if (typeof dateFilter === 'string') {
          // It's already a string, use as is
          filters.date = dateFilter;
        }
      }

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key] === undefined) {
          delete filters[key];
        }
      });

      const [
        allProductionResult,
        dashboardDataResult
      ] = await Promise.all([
        getAllDailyProduction(filters), // FIXED: Now passes filters for consistent data
        getDashboardData(filters) // This provides filtered data for statistics cards with dataSource tracking
      ]);

      // Update data source status based on dashboard results only
      if (dashboardDataResult) {
        const primarySource = dashboardDataResult.productionChart?.dataSource || 
                             dashboardDataResult.sidecards?.dataSource || 
                             dashboardDataResult.machinePerformance?.dataSource || 'mysql';
        
        const newStatus = {
          elasticsearch: primarySource === 'elasticsearch',
          mysql: primarySource === 'mysql' || primarySource === 'unknown',
          primary: primarySource
        };
        
      // Set data source status
      setDataSourceStatus(newStatus);
      }

        setProductionData({
        allDailyProduction: allProductionResult?.getAllDailyProduction || [],
        productionChart: {
          data: (dashboardDataResult?.productionChart?.data || []).map(item => {
            const transformed = {
              date: item.Date_Insert_Day,
              good: parseInt(item.Total_Good_Qty_Day) || 0,
              reject: parseInt(item.Total_Rejects_Qty_Day) || 0,
              // Backend now returns consistent percentage format (0-100) for both data sources
              oee: parseFloat(item.OEE_Day) || 0,
              speed: parseFloat(item.Speed_Day) || 0,
              availability: parseFloat(item.Availability_Rate_Day) || 0,
              performance: parseFloat(item.Performance_Rate_Day) || 0,
              quality: parseFloat(item.Quality_Rate_Day) || 0,
              // Keep original field names for backward compatibility
              OEE_Day: parseFloat(item.OEE_Day) || 0,
              Availability_Rate_Day: parseFloat(item.Availability_Rate_Day) || 0,
              Performance_Rate_Day: parseFloat(item.Performance_Rate_Day) || 0,
              Quality_Rate_Day: parseFloat(item.Quality_Rate_Day) || 0
          };
          return transformed;
        }),
          dataSource: dashboardDataResult?.productionChart?.dataSource || 'unknown'
        },
        sidecards: {
          goodqty: parseInt(dashboardDataResult?.sidecards?.goodqty) || 0,
          rejetqty: parseInt(dashboardDataResult?.sidecards?.rejetqty) || 0,
          dataSource: dashboardDataResult?.sidecards?.dataSource || 'unknown'
        },
        machinePerformance: {
          data: (dashboardDataResult?.machinePerformance?.data || []).map(item => {
            const transformed = {
              ...item,
              // Backend now returns consistent percentage format (0-100) for both data sources
              availability: parseFloat(item.availability) || 0,
              performance: parseFloat(item.performance) || 0,
              oee: parseFloat(item.oee) || 0,
              quality: parseFloat(item.quality) || 0,
              // disponibilite is already in 0-100 range from backend for both sources
              disponibilite: parseFloat(item.disponibilite) || 0,
              // downtime is in hours, keep as is
              downtime: parseFloat(item.downtime) || 0
            };
            return transformed;
          }),
          dataSource: dashboardDataResult?.machinePerformance?.dataSource || 'unknown'
        },
        shiftPerformance: {
          data: (dashboardDataResult?.shiftPerformance?.data || []).map(item => {
            const transformed = {
              ...item,
              // Rename Shift to match frontend expectations
              name: item.Shift || item.name,
              // Backend now returns consistent percentage format (0-100) for both data sources
              availability: parseFloat(item.availability) || 0,
              performance: parseFloat(item.performance) || 0,
              oee: parseFloat(item.oee) || 0,
              quality: parseFloat(item.quality) || 0,
              // disponibilite is already in 0-100 range from backend for both sources
              disponibilite: parseFloat(item.disponibilite) || 0,
              // downtime is in hours, keep as is
              downtime: parseFloat(item.downtime) || 0
            };
            return transformed;
          }),
          dataSource: dashboardDataResult?.shiftPerformance?.dataSource || 'unknown'
        },
        availabilityTrend: dashboardDataResult?.availabilityTrend || []
      });
    } catch (error) {
      console.error('Error fetching GraphQL data:', error);
    }
  }, [dateRangeType, selectedMachineModel, selectedMachine, dateFilter, getDashboardData, getAllDailyProduction]); // FIXED: Added missing GraphQL function dependencies

  // Fetch GraphQL data when filters change
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Local UI state
  const [activeTab, setActiveTab] = useState("1")
  const [dataSize, setDataSize] = useState(0)

  // Elasticsearch search state
  const [searchResults, setSearchResults] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchMode, setSearchMode] = useState(false)
  const [globalSearchVisible, setGlobalSearchVisible] = useState(false)

  const screens = useBreakpoint()

  // We're now using the ProductionContext for these states
  const [dateFilterActive, setDateFilterActive] = useState(false)

  // Optimized filter handlers with useCallback
  const onDateChange = useCallback((date) => {
    handleDateChange(date);
    if (date) {
      setDateFilterActive(true);
    } else {
      setDateFilterActive(false);
    }
  }, [handleDateChange]); // FIXED: Added handleDateChange dependency

  // Calculate data size for performance monitoring with GraphQL data
  useEffect(() => {
    const totalRecords = productionData.allDailyProduction.length + 
                        (productionData.productionChart.data?.length || 0) + 
                        (productionData.machinePerformance.data?.length || 0);
    setDataSize(totalRecords);
  }, [productionData]);

  // Export functionality
  const handleExportData = useCallback(async (exportConfig) => {
    try {
      // Implement export logic here
      // You can add actual export implementation here
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, []);

  // Elasticsearch search handlers
  const handleSearchResults = useCallback((results, query) => {
    setSearchResults(results);
    setSearchQuery(query);
    setSearchMode(!!results);

    if (results) {
      // Switch to table view when search results are available
      setActiveTab("3");
    }
  }, []);

  const handleGlobalSearchResult = useCallback((result) => {
    // Handle global search result selection
    setGlobalSearchVisible(false);

    // You can add navigation logic here based on result type
    if (result.type === 'production-data') {
      setActiveTab("3"); // Switch to table view
    }
  }, []);

  const clearSearchMode = useCallback(() => {
    setSearchResults(null);
    setSearchQuery('');
    setSearchMode(false);
  }, []);



  // Memoize expensive calculations with GraphQL data
  const calculatedMetrics = useMemo(() => {
    let avgTRS = 0;
    let avgAvailability = 0;
    let avgPerformance = 0;
    let avgQuality = 0;

    // Use GraphQL production chart data with new structure
    const dataToUse = productionData.productionChart.data || [];
    const sidecardsToUse = productionData.sidecards;

    if (dataToUse.length > 0) {
      const sums = dataToUse.reduce((acc, item) => {
        // Get values from GraphQL data structure (already normalized by backend)
        let oeeValue = parseFloat(item.oee || item.OEE_Day || 0);
        let availValue = parseFloat(item.availability || item.Availability_Rate_Day || 0);
        let perfValue = parseFloat(item.performance || item.Performance_Rate_Day || 0);
        let qualValue = parseFloat(item.quality || item.Quality_Rate_Day || 0);

        // Values are already normalized by backend, but ensure they're in 0-100 range for display
        oeeValue = normalizePercentage(oeeValue);
        availValue = normalizePercentage(availValue);
        perfValue = normalizePercentage(perfValue);
        qualValue = normalizePercentage(qualValue);

        return {
          oee: acc.oee + oeeValue,
          availability: acc.availability + availValue,
          performance: acc.performance + perfValue,
          quality: acc.quality + qualValue,
        };
      }, { oee: 0, availability: 0, performance: 0, quality: 0 });

      avgTRS = sums.oee / dataToUse.length;
      avgAvailability = sums.availability / dataToUse.length;
      avgPerformance = sums.performance / dataToUse.length;
      avgQuality = sums.quality / dataToUse.length;
    }

    // Use GraphQL sidecards data (already normalized by backend)
    const totalGood = parseInt(sidecardsToUse.goodqty) || 0;
    const totalRejects = parseInt(sidecardsToUse.rejetqty) || 0;
    
    const rejectRate = totalGood + totalRejects > 0 ? (totalRejects / (totalGood + totalRejects)) * 100 : 0;
    const qualityRate = totalGood + totalRejects > 0 ? (totalGood / (totalGood + totalRejects)) * 100 : 0;

    return {
      avgTRS,
      avgAvailability,
      avgPerformance,
      avgQuality,
      rejectRate,
      qualityRate,
      totalGood,
      totalRejects,
    };
  }, [productionData]);

  const { avgTRS, avgAvailability, avgPerformance, avgQuality, rejectRate, qualityRate, totalGood, totalRejects } = calculatedMetrics;


  // Optimized shift calculation with useCallback
  const getCurrentShift = useCallback(() => {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 6 && hour < 14) {
      return "Matin";
    } else if (hour >= 14 && hour < 22) {
      return "Après-midi";
    } else {
      return "Nuit";
    }
  }, []);

  // Memoize statistics configuration using GraphQL data
  const stats = useMemo(() => {
    return getStatisticsConfig(
      totalGood,
      totalRejects,
      avgTRS,
      avgAvailability,
      avgPerformance,
      rejectRate,
      qualityRate
    );
  }, [totalGood, totalRejects, avgTRS, avgAvailability, avgPerformance, rejectRate, qualityRate])



  // Memoize machine performance columns
  const machinePerformanceColumns = useMemo(() =>
    getMachinePerformanceColumns(getChartColors([
      SOMIPEM_COLORS.PRIMARY_BLUE,
      SOMIPEM_COLORS.SECONDARY_BLUE,
      SOMIPEM_COLORS.CHART_TERTIARY,
      SOMIPEM_COLORS.SUCCESS_GREEN,
      SOMIPEM_COLORS.WARNING_ORANGE
    ]), normalizePercentage),
    [getChartColors]
  )

  // Memoize production table columns
  const productionTableColumns = useMemo(() =>
    getProductionTableColumns(getChartColors([
      SOMIPEM_COLORS.PRIMARY_BLUE,
      SOMIPEM_COLORS.SECONDARY_BLUE,
      SOMIPEM_COLORS.CHART_TERTIARY,
      SOMIPEM_COLORS.SUCCESS_GREEN,
      SOMIPEM_COLORS.WARNING_ORANGE
    ]), productionData.allDailyProduction),
    [getChartColors, productionData.allDailyProduction]
  )

  // Helper function to safely parse numeric values from database strings
  const safeParseFloat = useCallback((value, defaultValue = 0) => {
    if (value === null || value === undefined || value === '') {
      return defaultValue;
    }
    
    if (typeof value === 'number' && !isNaN(value)) {
      return value;
    }
    
    // Convert to string and clean it
    const stringValue = String(value).trim().replace(',', '.');
    const parsed = parseFloat(stringValue);
    
    return !isNaN(parsed) ? parsed : defaultValue;
  }, []);

  const safeParseInt = useCallback((value, defaultValue = 0) => {
    if (value === null || value === undefined || value === '') {
      return defaultValue;
    }
    
    if (typeof value === 'number' && !isNaN(value)) {
      return Math.round(value);
    }
    
    // Convert to string and clean it
    const stringValue = String(value).trim();
    const parsed = parseInt(stringValue, 10);
    
    return !isNaN(parsed) ? parsed : defaultValue;
  }, []);

  // Memoize processed table data for performance
  const processedTableData = useMemo(() => {
    return productionData.allDailyProduction.map(item => ({
      ...item,
      // Ensure all required fields have default values based on machine_daily_table_mould structure
      date: (() => {
        try {
          const rawDate = item.Date_Insert_Day || item.date;
          if (rawDate) {
            // Handle DD/MM/YYYY or DD/MM/YYYY HH:mm:ss format from database
            if (rawDate.includes('/')) {
              // Try DD/MM/YYYY HH:mm:ss format first
              let parsedDate = dayjs(rawDate, 'DD/MM/YYYY HH:mm:ss');
              if (!parsedDate.isValid()) {
                // Try DD/MM/YYYY format
                parsedDate = dayjs(rawDate, 'DD/MM/YYYY');
              }
              if (parsedDate.isValid()) {
                return parsedDate.format('YYYY-MM-DD');
              }
            }
            // Handle other formats (ISO, etc.)
            const parsedDate = dayjs(rawDate);
            if (parsedDate.isValid()) {
              return parsedDate.format('YYYY-MM-DD');
            }
          }
          console.warn(`Invalid date found in table data: ${rawDate}, using today's date`);
          return dayjs().format('YYYY-MM-DD');
        } catch (e) {
          console.error("Error parsing date for table:", e);
          return dayjs().format('YYYY-MM-DD');
        }
      })(),
      Machine_Name: item.Machine_Name || 'N/A',
      Shift: item.Shift || 'N/A',
      // Map to the correct database fields using safe parsing
      good: safeParseInt(item.Good_QTY_Day),
      reject: safeParseInt(item.Rejects_QTY_Day),
      oee: (() => {
        const oeeValue = safeParseFloat(item.OEE_Day);
        // If value is between 0 and 1, convert to percentage
        return oeeValue > 0 && oeeValue <= 1 ? oeeValue * 100 : oeeValue;
      })(),
      speed: safeParseFloat(item.Speed_Day, null),
      mould_number: item.Part_Number || 'N/A',
      poid_unitaire: item.Poid_Unitaire || 'N/A',
      cycle_theorique: item.Cycle_Theorique || 'N/A',
      poid_purge: item.Poid_Purge || 'N/A',
      availability: (() => {
        const availValue = safeParseFloat(item.Availability_Rate_Day);
        // If value is between 0 and 1, convert to percentage
        return availValue > 0 && availValue <= 1 ? availValue * 100 : availValue;
      })(),
      performance: (() => {
        const perfValue = safeParseFloat(item.Performance_Rate_Day);
        // If value is between 0 and 1, convert to percentage
        return perfValue > 0 && perfValue <= 1 ? perfValue * 100 : perfValue;
      })(),
      quality: (() => {
        const qualValue = safeParseFloat(item.Quality_Rate_Day);
        // If value is between 0 and 1, convert to percentage
        return qualValue > 0 && qualValue <= 1 ? qualValue * 100 : qualValue;
      })(),
      run_hours: safeParseFloat(item.Run_Hours_Day),
      down_hours: safeParseFloat(item.Down_Hours_Day)
    }));
  }, [productionData.allDailyProduction, safeParseFloat, safeParseInt])

  // Memoize tabs items for performance (after all dependencies are defined)
  const tabsItems = useMemo(() => [
    {
      key: "1",
      label: (
        <span>
          <LineChartOutlined />
          Tendances
        </span>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {/* Show detailed charts when ready */}
          {graphqlLoading ? (
            <Col span={24}>
              <Card>
                <Skeleton active paragraph={{ rows: 8 }} />
              </Card>
            </Col>
          ) : (
            /* Using the enhanced TrendsChartSection component with expansion capabilities */
            <EnhancedTrendsChartSection
              data={productionData.productionChart.data}
              colors={getChartColors([
                SOMIPEM_COLORS.PRIMARY_BLUE,
                SOMIPEM_COLORS.SECONDARY_BLUE,
                SOMIPEM_COLORS.CHART_TERTIARY,
                SOMIPEM_COLORS.SUCCESS_GREEN,
                SOMIPEM_COLORS.WARNING_ORANGE
              ])}
              dateRangeType={dateRangeType}
              dateFilter={dateFilter}
              formatDateRange={formatDateRange}
            />
          )}
        </Row>
      )
    },
    {
      key: "2",
      label: (
        <span>
          <BarChartOutlined />
          Performance
        </span>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {graphqlLoading ? (
            <Col span={24}>
              <Card>
                <Skeleton active paragraph={{ rows: 8 }} />
              </Card>
            </Col>
          ) : (
            <>
              {/* Performance des machines */}
              <Col span={24}>
                <Card
                  title={
                    <Space>
                      <BarChartOutlined style={{ fontSize: 20, color: getChartColor(SOMIPEM_COLORS.SECONDARY_BLUE, 1) }} />
                      <Text strong>Performance des Machines</Text>
                    </Space>
                  }
                  variant="borderless"
                  extra={<Badge count={productionData.machinePerformance.data?.length || 0} style={{ backgroundColor: getChartColor(SOMIPEM_COLORS.SECONDARY_BLUE, 1) }} />}
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} md={12}>
                      <UnifiedChartExpansion
                        title="Production par Machine"
                        data={productionData.machinePerformance.data}
                        chartType="bar"
                        expandMode="modal"
                      >
                        <EnhancedMachineChart
                          data={productionData.machinePerformance.data}
                          title="Production par Machine"
                          dataKey="production"
                          tooltipLabel="Production"
                          isKg={true}
                          color={getChartColor(SOMIPEM_COLORS.CHART_TERTIARY, 2)}
                        />
                      </UnifiedChartExpansion>
                    </Col>
                    <Col xs={24} md={12}>
                      <UnifiedChartExpansion
                        title="Rejets par Machine"
                        data={productionData.machinePerformance.data}
                        chartType="bar"
                        expandMode="modal"
                        exportEnabled={true}
                      >
                        <EnhancedMachineRejectsChart
                          data={productionData.machinePerformance.data}
                          color={getChartColor(SOMIPEM_COLORS.WARNING_ORANGE, 4)}
                        />
                      </UnifiedChartExpansion>
                    </Col>
                  </Row>
                </Card>
              </Col>

              {/* TRS par Machine */}
              <Col xs={24} md={12}>
              <UnifiedChartExpansion
                {...getStandardChartExpansionProps(
                  "TRS par Machine",
                  productionData.machinePerformance.data,
                  "bar"
                )}
              >
                <EnhancedMachineTRSChart
                  data={productionData.machinePerformance.data}
                  color={getChartColor('#60A5FA', 5)}
                />
              </UnifiedChartExpansion>
              </Col>

              {/* Répartition Production */}
              <Col xs={24} md={12}>
                <UnifiedChartExpansion
                  title="Répartition Production - Qualité"
                  data={[
                    { name: "Bonnes Pièces", value: Number(totalGood) || 0 },
                    { name: "Rejets", value: Number(totalRejects) || 0 },
                  ].filter((item) => item.value > 0)}
                  chartType="pie"
                  expandMode="modal"
                  exportEnabled={true}
                  cardProps={{
                    extra: <Tag color="red">Qualité</Tag>,
                    loading: graphqlLoading
                  }}
                >
                  <EnhancedPieChart
                    data={[
                      { name: "Bonnes Pièces", value: Number(totalGood) || 0 },
                      { name: "Rejets", value: Number(totalRejects) || 0 },
                    ].filter((item) => item.value > 0)}
                    colors={getChartColors([SOMIPEM_COLORS.CHART_TERTIARY, SOMIPEM_COLORS.WARNING_ORANGE])}
                    height={charts?.layout?.defaultHeight || 300}
                  />
                </UnifiedChartExpansion>
              </Col>

          {/* Comparaison des équipes */}
          <Col xs={24} md={24}>
            <Card
              title={
                <Space>
                  <BarChartOutlined style={{ fontSize: 20, color: getChartColor(SOMIPEM_COLORS.CHART_QUATERNARY, 3) }} />
                  <Text strong>Comparaison des Équipes</Text>
                </Space>
              }
              variant="borderless"
              extra={<Tag color="orange">Par équipe</Tag>}
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <UnifiedChartExpansion
                    title="Production par Équipe"
                    data={productionData.shiftPerformance.data}
                    chartType="bar"
                    expandMode="modal"
                    exportEnabled={true}
                  >
                    <EnhancedShiftChart
                      data={productionData.shiftPerformance.data}
                      title="Production par Équipe"
                      dataKey="production"
                      color={getChartColor(SOMIPEM_COLORS.CHART_TERTIARY, 2)}
                      label="Production"
                      tooltipLabel="Production"
                      isKg={false}
                    />
                  </UnifiedChartExpansion>
                </Col>
                <Col xs={24} md={12}>
                  <UnifiedChartExpansion
                    title="Temps d'arrêt par Équipe"
                    data={productionData.shiftPerformance.data}
                    chartType="bar"
                    expandMode="modal"
                    exportEnabled={true}
                  >
                    <EnhancedShiftChart
                      data={productionData.shiftPerformance.data}
                      title="Temps d'arrêt par Équipe"
                      dataKey="downtime"
                      color={getChartColor(SOMIPEM_COLORS.WARNING_ORANGE, 4)}
                      label="Temps d'arrêt (heures)"
                      tooltipLabel="Temps d'arrêt (heures)"
                      isKg={false}
                    />
                  </UnifiedChartExpansion>
                </Col>
                <Col xs={24} md={12}>
                  <UnifiedChartExpansion
                    title="TRS par Équipe"
                    data={productionData.shiftPerformance.data}
                    chartType="line"
                    expandMode="modal"
                    exportEnabled={true}
                  >
                    <EnhancedShiftTRSLineChart
                      data={productionData.shiftPerformance.data}
                      color={getChartColor(SOMIPEM_COLORS.PRIMARY_BLUE, 0)}
                    />
                  </UnifiedChartExpansion>
                </Col>
                <Col xs={24} md={12}>
                  <UnifiedChartExpansion
                    title="Performance par Équipe"
                    data={productionData.shiftPerformance.data}
                    chartType="line"
                    expandMode="modal"
                    exportEnabled={true}
                  >
                    <EnhancedPerformanceLineChart
                      data={productionData.shiftPerformance.data}
                      color={getChartColor('#60A5FA', 5)}
                    />
                  </UnifiedChartExpansion>
                </Col>
              </Row>
            </Card>
          </Col>
            </>
          )}
        </Row>
      )
    },
    {
      key: "3",
      label: (
        <span>
          <TableOutlined />
          Détails
        </span>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {/* Tableau de performance des machines */}
          <Col span={24}>
            <Card
              title={
                <Space>
                  <ToolOutlined style={{ fontSize: 20, color: getChartColor(SOMIPEM_COLORS.SECONDARY_BLUE, 1) }} />
                  <Text strong>Données Journalières par Machine</Text>
                </Space>
              }
              variant="borderless"
              extra={
                <Space>
                  <Badge count={productionData.allDailyProduction.length} style={{ backgroundColor: getChartColor(SOMIPEM_COLORS.SECONDARY_BLUE, 1) }} />
                  <Button type="link" icon={<DownloadOutlined />} disabled>
                    Exporter
                  </Button>
                </Space>
              }
            >
              <Table
                dataSource={productionData.allDailyProduction}
                columns={machinePerformanceColumns}
                pagination={{
                  pageSize: 5,
                  showSizeChanger: true,
                  pageSizeOptions: ["5", "10", "20"],
                  showTotal: (total) => `Total ${total} enregistrements`,
                }}
                scroll={{ x: 1800 }}
                rowKey={(record, index) => `${record.Machine_Name}-${record.Date_Insert_Day}-${index}`}
              />
            </Card>
          </Col>

          {/* Tableau détaillé des données de production */}
          <Col span={24}>
            <OptimizedTable
              title="Données Détaillées de Production"
              dataSource={processedTableData}
              columns={productionTableColumns}
              totalRecords={processedTableData.length}
              pageSize={50}
              currentPage={1}
              onExport={handleExportData}
              maxRecordsWarning={500}
              loading={graphqlLoading}
              scroll={{ x: 2200 }}
              rowKey={(record, index) => `${record.Date_Insert_Day}-${record.Machine_Name || 'unknown'}-${record.Part_Number || 'unknown'}-${index}`}
              expandable={{
                expandedRowRender: record => (
                  <Card size="small" title="Informations du moule">
                    <Row gutter={[16, 16]}>
                      <Col span={6}>
                        <Statistic
                          title="Numéro de Pièce"
                          value={record.Part_Number || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="Poids Unitaire"
                          value={record.Poid_Unitaire || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                          suffix="g"
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="Cycle Théorique"
                          value={record.Cycle_Theorique || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                          suffix="s"
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="Poids Purge"
                          value={record.Poid_Purge || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                          suffix="g"
                        />
                      </Col>
                    </Row>
                  </Card>
                ),
                expandRowByClick: true,
                rowExpandable: record => record.Part_Number && record.Part_Number !== 'N/A',
              }}
            />
          </Col>
        </Row>
      )
    }
  ], [
    productionData.allDailyProduction, productionData.machinePerformance.data, productionData.sidecards,
    dateRangeType, dateFilter, formatDateRange,
    processedTableData, productionTableColumns, handleExportData, graphqlLoading,
    machinePerformanceColumns
  ])

  // Simplified loading state using only GraphQL
  const isLoading = graphqlLoading;
  const isEssentialLoading = graphqlLoading;
  const isDetailedLoading = graphqlLoading;

  // Enhanced data availability check - FIXED: Use only filtered data sources with new structure
  // OLD LOGIC (BROKEN): const hasData = productionData.allDailyProduction.length > 0 || productionData.productionChart.length > 0;
  // ISSUE: allDailyProduction is NOT filtered, productionChart IS filtered
  // FIX: Use only filtered data sources for hasData check
  const hasData = (productionData.productionChart.data?.length || 0) > 0 || productionData.sidecards.goodqty > 0;
  const hasGraphQLData = (productionData.productionChart.data?.length || 0) > 0 || productionData.sidecards.goodqty > 0;

  // Using the extracted chart renderer component


  // Rendu du composant
  return (
    <div style={{ padding: screens.md ? 24 : 16 }}>
      <Spin spinning={isLoading} tip="Chargement des données..." size="large">
        <Row gutter={[24, 24]}>
          {/* En-tête */}
          <Col span={24}>
            <Card variant="borderless" styles={{ body: { padding: screens.md ? 24 : 16 } }}>
              <Row gutter={[24, 24]} align="middle">
                <Col xs={24} md={12}>
                  <Title level={3} style={{ marginBottom: 8 }}>
                    <DashboardOutlined style={{ marginRight: 12, color: getChartColor(SOMIPEM_COLORS.PRIMARY_BLUE, 0) }} />
                    Tableau de Bord de Production
                  </Title>
                </Col>
                <Col xs={24} md={12} style={{ textAlign: screens.md ? "right" : "left" }}>
                  <Space direction="vertical" style={{ width: "100%" }}>
                    {/* Enhanced MinimalFilterTest Component with Coordination Status */}
                    <div>
                      <MinimalFilterTest />
                      {coordinationStatus && (
                        <Tag 
                          color={coordinationStatus.color}
                          style={{ marginTop: '8px' }}
                        >
                          🔄 {coordinationStatus.message}
                        </Tag>
                      )}
                    </div>
                    
                    <FilterPanel
                      selectedMachineModel={selectedMachineModel}
                      selectedMachine={selectedMachine}
                      machineModels={machineModels}
                      filteredMachineNames={filteredMachineNames}
                      dateRangeType={dateRangeType}
                      dateFilter={dateFilter}
                      dateFilterActive={dateFilterActive}
                      handleMachineModelChange={(value) => {
                        console.log('📊 [DASHBOARD DEBUG] handleMachineModelChange called with:', value);
                        handleMachineModelChange(value);
                      }}
                      handleMachineChange={(value) => {
                        console.log('📊 [DASHBOARD DEBUG] handleMachineChange called with:', value);
                        handleMachineChange(value);
                      }}
                      handleDateRangeTypeChange={handleDateRangeTypeChange}
                      handleDateChange={onDateChange}
                      resetFilters={resetFilters}
                      handleRefresh={handleRefresh}
                      loading={graphqlLoading || isProcessingFilters}
                      dataSize={dataSize}
                      pageType="production"
                      onSearchResults={handleSearchResults}
                      enableElasticsearch={true}
                    />

                    {/* Data Size Indicator */}
                    {dataSize > 500 && (
                      <Tag color="blue" icon={<ThunderboltOutlined />}>
                        {dataSize} enregistrements
                      </Tag>
                    )}

                    {/* Display active filters */}
                    {(selectedMachineModel || dateFilterActive) && (
                      <Space wrap style={{ marginTop: 8 }}>
                        {selectedMachineModel && (
                          <Tag color="blue" closable onClose={() => handleMachineModelChange("")}>
                            Modèle: {selectedMachineModel}
                          </Tag>
                        )}
                        {selectedMachine && (
                          <Tag color="green" closable onClose={() => handleMachineChange("")}>
                            Machine: {selectedMachine}
                          </Tag>
                        )}
                        {dateFilterActive && (
                          <Tag color="purple" closable onClose={() => handleDateChange(null)}>
                            Période: {dateRangeDescription}
                          </Tag>
                        )}
                        {/* Manual coordination trigger */}
                        {(selectedMachineModel || selectedMachine || dateFilterActive) && (
                          <Button 
                            size="small" 
                            type="link" 
                            onClick={triggerCoordination}
                            loading={isProcessingFilters}
                          >
                            🔄 Sync
                          </Button>
                        )}
                      </Space>
                    )}

                    {/* Enhanced coordination status display */}
                    {!(selectedMachineModel || dateFilterActive) && hasGraphQLData && (
                      <Space wrap style={{ marginTop: 8 }}>
                        <Tag color="green" icon={<ThunderboltOutlined />}>
                          Powered by GraphQL
                        </Tag>
                        {coordinationStatus && (
                          <Tag color={coordinationStatus.dataSourceHealth === 'elasticsearch' ? 'blue' : 'orange'}>
                            Source: {coordinationStatus.dataSourceHealth === 'elasticsearch' ? 'Elasticsearch' : 'MySQL'}
                          </Tag>
                        )}
                        {isProcessingFilters && (
                          <Tag color="processing">
                            Processing...
                          </Tag>
                        )}
                      </Space>
                    )}
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* First row of stats cards - Show essential data immediately */}
          {stats.slice(0, 4).map((stat) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card
                hoverable
                loading={isEssentialLoading}
                style={{
                  backgroundColor: "#FFFFFF", // White background
                  border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
                  borderTop: `3px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue top border
                  height: "100%",
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
                }}
              >
                {isEssentialLoading ? (
                  <Skeleton active paragraph={{ rows: 1 }} />
                ) : (
                  <>
                    <Statistic
                      title={
                        <Tooltip title={stat.description}>
                          <Space>
                            {React.cloneElement(stat.icon, {
                              style: {
                                color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for icons
                                fontSize: 20
                              },
                            })}
                            <span style={{
                              color: SOMIPEM_COLORS.DARK_GRAY, // Dark Gray for titles
                              fontWeight: 600
                            }}>{stat.title}</span>
                            <InfoCircleOutlined style={{
                              color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for info icons
                              fontSize: 14
                            }} />
                          </Space>
                        </Tooltip>
                      }
                      value={stat.rawValue || stat.value} // Use rawValue for proper Ant Design formatting
                      precision={stat.title.includes("TRS") || stat.title.includes("Taux") ||
                                stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                                stat.title.includes("Qualité") ? 1 : 0}
                      suffix={stat.suffix}
                      valueStyle={{
                        fontSize: 24,
                        color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for key numbers
                        fontWeight: 700,
                      }}
                      formatter={(value) => {
                        // Use French number formatting for display
                        if (stat.suffix === '%') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1
                          });
                        } else if (stat.suffix === 'Pcs' || stat.suffix === 'Kg') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          });
                        }
                        return value.toLocaleString('fr-FR');
                      }}
                    />
                    {(stat.title.includes("TRS") || stat.title.includes("Taux") ||
                      stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                      stat.title.includes("Qualité")) && (
                      <Progress
                        percent={stat.rawValue || stat.value} // Use rawValue for progress bar
                        strokeColor={SOMIPEM_COLORS.SECONDARY_BLUE} // Secondary Blue for progress bars
                        trailColor="#F3F4F6" // Light gray trail
                        showInfo={false}
                        status="normal" // Remove success/warning/error status colors
                        style={{ marginTop: 12 }}
                        strokeWidth={6}
                      />
                    )}
                  </>
                )}
              </Card>
            </Col>
          ))}

          {/* Second row of stats cards - Show essential data immediately */}
          {stats.slice(4).map((stat) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card
                hoverable
                loading={isEssentialLoading}
                style={{
                  backgroundColor: "#FFFFFF", // White background
                  border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
                  borderTop: `3px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue top border
                  height: "100%",
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
                }}
              >
                {isEssentialLoading ? (
                  <Skeleton active paragraph={{ rows: 1 }} />
                ) : (
                  <>
                    <Statistic
                      title={
                        <Tooltip title={stat.description}>
                          <Space>
                            {React.cloneElement(stat.icon, {
                              style: {
                                color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for icons
                                fontSize: 20
                              },
                            })}
                            <span style={{
                              color: SOMIPEM_COLORS.DARK_GRAY, // Dark Gray for titles
                              fontWeight: 600
                            }}>{stat.title}</span>
                            <InfoCircleOutlined style={{
                              color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for info icons
                              fontSize: 14
                            }} />
                          </Space>
                        </Tooltip>
                      }
                      value={stat.rawValue || stat.value} // Use rawValue for proper Ant Design formatting
                      precision={stat.title.includes("TRS") || stat.title.includes("Taux") ||
                                stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                                stat.title.includes("Qualité") ? 1 : 0}
                      suffix={stat.suffix}
                      valueStyle={{
                        fontSize: 24,
                        color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for key numbers
                        fontWeight: 700,
                      }}
                      formatter={(value) => {
                        // Use French number formatting for display
                        if (stat.suffix === '%') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1
                          });
                        } else if (stat.suffix === 'Pcs' || stat.suffix === 'Kg') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          });
                        }
                        return value.toLocaleString('fr-FR');
                      }}
                    />
                    {(stat.title.includes("TRS") || stat.title.includes("Taux") ||
                      stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                      stat.title.includes("Qualité")) && (
                      <Progress
                        percent={stat.rawValue || stat.value} // Use rawValue for progress bar
                        strokeColor={SOMIPEM_COLORS.SECONDARY_BLUE} // Secondary Blue for progress bars
                        trailColor="#F3F4F6" // Light gray trail
                        showInfo={false}
                        status="normal" // Remove success/warning/error status colors
                        style={{ marginTop: 12 }}
                        strokeWidth={6}
                      />
                    )}
                  </>
                )}
              </Card>
            </Col>
          ))}

          {/* Show dashboard by default, only show empty state when loading or no data available */}
          {isLoading ? (
            <Col span={24}>
              <Card>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "40px 0",
                  }}
                >
                  <Spin size="large" style={{ marginBottom: 24 }} />
                  <Title level={3}>Chargement des données...</Title>
                  <Paragraph style={{ fontSize: 16, color: "#666", textAlign: "center", maxWidth: 600 }}>
                    {selectedMachineModel
                      ? `Chargement des données pour ${selectedMachineModel}...`
                      : "Chargement des données de production pour tous les modèles de machines..."
                    }
                  </Paragraph>
                </div>
              </Card>
            </Col>
          ) : !hasData ? (
            <Col span={24}>
              <Card>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "40px 0",
                  }}
                >
                  <DashboardOutlined style={{ fontSize: 64, color: "#1890ff", marginBottom: 24 }} />
                  <Title level={3}>Aucune donnée disponible</Title>
                  <Paragraph style={{ fontSize: 16, color: "#666", textAlign: "center", maxWidth: 600 }}>
                    {selectedMachineModel || dateFilter
                      ? `Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.`
                      : "Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."
                    }
                  </Paragraph>
                  {(selectedMachineModel || dateFilter) && (
                    <Paragraph style={{ fontSize: 14, color: "#999", textAlign: "center", marginTop: 16 }}>
                      Filtres actifs:
                      {selectedMachineModel && ` Modèle: ${selectedMachineModel}`}
                      {selectedMachine && ` Machine: ${selectedMachine}`}
                      {dateFilter && ` Période: ${formatApiDate(dateFilter)}`}
                    </Paragraph>
                  )}
                </div>
              </Card>
            </Col>
          ) : (
            <>
              {/* Onglets pour les graphiques et tableaux - Affichés par défaut */}
              <Col span={24}>
                <Card variant="borderless">
                  <Tabs
                    defaultActiveKey="1"
                    onChange={setActiveTab}
                    items={tabsItems}
                    tabBarExtraContent={
                      <Space>
                        <Button
                          type="link"
                          icon={<SearchOutlined />}
                          onClick={() => setGlobalSearchVisible(true)}
                        >
                          Recherche globale
                        </Button>

                        <Button type="link" icon={<DownloadOutlined />} disabled>
                          Exporter
                        </Button>
                        {selectedMachine && (
                          <ShiftReportButton
                            machineId={selectedMachine}
                            machineName={selectedMachine}
                            shift={getCurrentShift()}
                          />
                        )}
                      </Space>
                    }
                  />
                </Card>
              </Col>
            </>
          )}
        </Row>
      </Spin>

      {/* Search Results Display */}
      {searchMode && searchResults && (
        <div style={{ marginTop: 24 }}>
          <SearchResultsDisplay
            results={searchResults}
            searchQuery={searchQuery}
            pageType="production"
            loading={graphqlLoading}
            onResultSelect={(result) => {
              // Handle result selection
            }}
            onPageChange={(page) => {
              // Handle pagination
            }}
          />
        </div>
      )}



      {/* Global Search Modal */}
      <GlobalSearchModal
        visible={globalSearchVisible}
        onClose={() => setGlobalSearchVisible(false)}
        onResultSelect={handleGlobalSearchResult}
      />


    </div>
  )
}

// Memoize the main component for performance
const MemoizedOptimizedProduction = memo(OptimizedProduction);

// Rename the wrapper component to match the export name
const ProductionDashboard = memo(() => {
  return (
    <ProductionProvider>
      <MemoizedOptimizedProduction />
    </ProductionProvider>
  );
});

export default ProductionDashboard;