/**
 * Simple route checker without <PERSON><PERSON>peteer
 */

import fetch from 'node-fetch';

const FRONTEND_URL = 'http://localhost:5173';

async function checkRoutes() {
  console.log('🔍 Checking PDF Routes...\n');

  // Test routes
  const routes = [
    '/reports/pdf-test-simple',
    '/reports/pdf-test',
    '/reports/pdf-preview?data=eyJtYWNoaW5lIjp7Im5hbWUiOiJUZXN0In0sInNoaWZ0IjoiTWF0aW4iLCJkYXRlIjoiMjAyNS0wMS0xNiJ9'
  ];

  for (const route of routes) {
    console.log(`📍 Testing: ${route}`);
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.ok) {
        const html = await response.text();
        
        // Check for key indicators
        const hasReactRoot = html.includes('<div id="root">');
        const hasViteScript = html.includes('/@vite/client');
        const hasTitle = html.includes('<title>');
        const isBasicHTML = html.length < 1000; // Basic HTML shell
        
        console.log(`  ✅ Status: ${response.status}`);
        console.log(`  📄 Content Length: ${html.length} chars`);
        console.log(`  ⚛️ React Root: ${hasReactRoot ? '✅' : '❌'}`);
        console.log(`  🔧 Vite Script: ${hasViteScript ? '✅' : '❌'}`);
        console.log(`  📝 Has Title: ${hasTitle ? '✅' : '❌'}`);
        console.log(`  🏗️ Basic HTML Shell: ${isBasicHTML ? '✅' : '❌'}`);
        
        if (isBasicHTML) {
          console.log('  ℹ️ This appears to be the basic HTML shell - React app should load client-side');
        }
        
      } else {
        console.log(`  ❌ Status: ${response.status}`);
      }
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
    console.log('');
  }

  console.log('💡 If all routes return basic HTML shell, the issue is likely:');
  console.log('   1. React components have import/syntax errors');
  console.log('   2. Routes are not properly registered');
  console.log('   3. Dependencies are missing or incompatible');
  console.log('\n🔍 Check browser console at http://localhost:5173/reports/pdf-test-simple for errors');
}

checkRoutes().catch(console.error);
