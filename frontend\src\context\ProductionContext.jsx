import React, { createContext, useContext } from "react";
import useMachineData from "../hooks/useMachineData";
import useDateFilter from "../hooks/useDateFilter";
import useProductionData from "../hooks/useProductionData";

// Create context
const ProductionContext = createContext();

/**
 * Production context provider
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {JSX.Element} - Context provider
 */
export const ProductionProvider = ({ children }) => {
  // Use custom hooks
  const machineData = useMachineData();
  const dateFilter = useDateFilter();
  
  // Combine machine data and date filter for production data
  const productionData = useProductionData({
    selectedMachineModel: machineData.selectedMachineModel,
    selectedMachine: machineData.selectedMachine,
    dateFilter: dateFilter.dateFilter,
    dateRangeType: dateFilter.dateRangeType,
    buildDateQueryParams: dateFilter.buildDateQueryParams
  });

  // Calculate statistics
  const statistics = productionData.calculateStatistics();

  // Combine all data and functions
  const value = {
    ...machineData,
    ...dateFilter,
    ...productionData,
    ...statistics,
    
    // Function to reset all filters
    resetFilters: () => {
      dateFilter.resetDateFilter();
      dateFilter.setDateRangeType("day");
      machineData.setSelectedMachineModel("");
      machineData.setSelectedMachine("");
    },
    
    // Function to refresh data
    handleRefresh: () => {
      productionData.fetchData();
    }
  };

  return (
    <ProductionContext.Provider value={value}>
      {children}
    </ProductionContext.Provider>
  );
};

/**
 * Hook to use production context
 * @returns {Object} Production context
 */
export const useProduction = () => {
  const context = useContext(ProductionContext);
  if (context === undefined) {
    throw new Error("useProduction must be used within a ProductionProvider");
  }
  return context;
};

export default ProductionContext;
