import React, { createContext, useContext } from "react";
import useMachineData from "../hooks/useMachineData";
import useDateFilter from "../hooks/useDateFilter";
import useProductionData from "../hooks/useProductionData";
import useFilterCoordinator from "../hooks/useFilterCoordinator.jsx";

// Create context
const ProductionContext = createContext();

/**
 * Production context provider with enhanced filter coordination
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {JSX.Element} - Context provider
 */
export const ProductionProvider = ({ children }) => {
  // Use custom hooks
  const machineData = useMachineData();
  const dateFilter = useDateFilter();
  
  // Combine machine data and date filter for production data
  const productionData = useProductionData({
    selectedMachineModel: machineData.selectedMachineModel,
    selectedMachine: machineData.selectedMachine,
    dateFilter: dateFilter.dateFilter,
    dateRangeType: dateFilter.dateRangeType,
    buildDateQueryParams: dateFilter.buildDateQueryParams
  });

  // Enhanced filter coordination
  const filterCoordination = useFilterCoordinator({
    selectedMachineModel: machineData.selectedMachineModel,
    selectedMachine: machineData.selectedMachine,
    dateFilter: dateFilter.dateFilter,
    dateRangeType: dateFilter.dateRangeType,
    loading: machineData.loading || productionData.loading
  });

  // Calculate statistics
  const statistics = productionData.calculateStatistics();

  // Enhanced reset filters function with coordination
  const resetFilters = async () => {
    console.log('🔄 [PRODUCTION CONTEXT] Resetting all filters');
    
    // Reset all filter states
    dateFilter.resetDateFilter();
    dateFilter.setDateRangeType("day");
    machineData.setSelectedMachineModel("");
    machineData.setSelectedMachine("");
    
    // Trigger coordination after reset
    setTimeout(() => {
      filterCoordination.triggerCoordination();
    }, 100);
  };

  // Enhanced refresh function with coordination
  const handleRefresh = async () => {
    console.log('🔄 [PRODUCTION CONTEXT] Refreshing data with coordination');
    
    // Refresh data
    productionData.fetchData();
    
    // Trigger coordination
    setTimeout(() => {
      filterCoordination.triggerCoordination();
    }, 200);
  };

  // Combine all data and functions
  const value = {
    ...machineData,
    ...dateFilter,
    ...productionData,
    ...statistics,
    
    // Filter coordination status
    coordinationStatus: filterCoordination.coordinationStatus,
    isProcessingFilters: filterCoordination.isProcessingFilters,
    
    // Enhanced functions with coordination
    resetFilters,
    handleRefresh,
    
    // Manual coordination trigger
    triggerCoordination: filterCoordination.triggerCoordination
  };

  return (
    <ProductionContext.Provider value={value}>
      {children}
    </ProductionContext.Provider>
  );
};

/**
 * Hook to use production context
 * @returns {Object} Production context
 */
export const useProduction = () => {
  const context = useContext(ProductionContext);
  if (context === undefined) {
    throw new Error("useProduction must be used within a ProductionProvider");
  }
  return context;
};

export default ProductionContext;
