/**
 * Enhanced Arret Queued Context
 * 
 * Enhanced version of ArretQueuedContext that integrates with the new
 * unified GraphQL interface supporting both Elasticsearch and MySQL.
 */

import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

// Import the enhanced GraphQL interface
import { createEnhancedGraphQLInterface } from './modules/enhancedGraphQLInterface';
import { useDataSourceStatus, DataSourceStatusProvider } from '../../hooks/useDataSourceStatus';

// Import existing modules
import { CHART_COLORS, INITIAL_SKELETON_STATE } from './modules/constants.jsx';
import { useSkeletonManager } from './modules/skeletonManager.jsx';
import { useComputedValues } from './modules/computedValues.jsx';
import { useEventHandlers } from './modules/eventHandlers.jsx';
import { useGraphQLInterface } from './modules/graphQLInterface.jsx';

// Extend dayjs with required plugins
dayjs.extend(isoWeek);
dayjs.extend(isSameOrBefore);

const EnhancedArretQueuedContext = createContext();

export const useEnhancedArretQueuedContext = () => {
  const context = useContext(EnhancedArretQueuedContext);
  if (!context) {
    console.error('⚠️ useEnhancedArretQueuedContext: Context not found!');
    return null;
  }
  return context;
};

/**
 * Enhanced Queued Data Manager Hook
 */
const useEnhancedQueuedDataManager = (enhancedGraphQLInterface, dataSourceStatus) => {
  const [loadingStates, setLoadingStates] = useState({
    essential: false,
    performance: false,
    charts: false,
    table: false
  });

  const [dataStates, setDataStates] = useState({
    essential: null,
    performance: null,
    charts: null,
    table: null,
    error: null
  });

  const abortControllerRef = useRef(null);

  /**
   * Cancel any ongoing requests
   */
  const cancelRequests = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  /**
   * Fetch data in queue with priority
   */
  const fetchDataInQueue = useCallback(async (filters = {}, forceRefresh = false) => {
    // Cancel any ongoing requests
    cancelRequests();
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      console.log('🚀 Enhanced data fetch started with filters:', filters);
      console.log('📊 Data source status:', dataSourceStatus.primarySource);

      // Reset states if force refresh
      if (forceRefresh) {
        setDataStates({
          essential: null,
          performance: null,
          charts: null,
          table: null,
          error: null
        });
      }

      // Queue 1: Essential Data (highest priority)
      setLoadingStates(prev => ({ ...prev, essential: true }));
      try {
        const essentialData = await enhancedGraphQLInterface.getEssentialData(filters);
        if (!abortControllerRef.current?.signal.aborted) {
          setDataStates(prev => ({ ...prev, essential: essentialData }));
          console.log('✅ Essential data loaded from:', essentialData.dataSource);
        }
      } catch (error) {
        if (!abortControllerRef.current?.signal.aborted) {
          console.error('❌ Essential data failed:', error);
          setDataStates(prev => ({ ...prev, error: error.message }));
        }
      } finally {
        setLoadingStates(prev => ({ ...prev, essential: false }));
      }

      // Small delay before next queue
      await new Promise(resolve => setTimeout(resolve, 100));

      // Queue 2: Performance Metrics
      if (!abortControllerRef.current?.signal.aborted) {
        setLoadingStates(prev => ({ ...prev, performance: true }));
        try {
          const performanceData = await enhancedGraphQLInterface.getPerformanceMetrics(filters);
          if (!abortControllerRef.current?.signal.aborted) {
            setDataStates(prev => ({ ...prev, performance: performanceData }));
            console.log('✅ Performance data loaded from:', performanceData.dataSource);
          }
        } catch (error) {
          if (!abortControllerRef.current?.signal.aborted) {
            console.error('❌ Performance data failed:', error);
          }
        } finally {
          setLoadingStates(prev => ({ ...prev, performance: false }));
        }
      }

      // Small delay before next queue
      await new Promise(resolve => setTimeout(resolve, 100));

      // Queue 3: Chart Data
      if (!abortControllerRef.current?.signal.aborted) {
        setLoadingStates(prev => ({ ...prev, charts: true }));
        try {
          const chartData = await enhancedGraphQLInterface.getChartData(filters, 'day');
          if (!abortControllerRef.current?.signal.aborted) {
            setDataStates(prev => ({ ...prev, charts: chartData }));
            console.log('✅ Chart data loaded from:', chartData.dataSource);
          }
        } catch (error) {
          if (!abortControllerRef.current?.signal.aborted) {
            console.error('❌ Chart data failed:', error);
          }
        } finally {
          setLoadingStates(prev => ({ ...prev, charts: false }));
        }
      }

      // Small delay before next queue
      await new Promise(resolve => setTimeout(resolve, 100));

      // Queue 4: Table Data (lowest priority)
      if (!abortControllerRef.current?.signal.aborted) {
        setLoadingStates(prev => ({ ...prev, table: true }));
        try {
          const tableData = await enhancedGraphQLInterface.getTableData(filters, { page: 1, size: 100 });
          if (!abortControllerRef.current?.signal.aborted) {
            setDataStates(prev => ({ ...prev, table: tableData }));
            console.log('✅ Table data loaded from:', tableData.dataSource);
          }
        } catch (error) {
          if (!abortControllerRef.current?.signal.aborted) {
            console.error('❌ Table data failed:', error);
          }
        } finally {
          setLoadingStates(prev => ({ ...prev, table: false }));
        }
      }

      console.log('🎉 Enhanced data fetch completed');
    } catch (error) {
      if (!abortControllerRef.current?.signal.aborted) {
        console.error('❌ Enhanced data fetch error:', error);
        setDataStates(prev => ({ ...prev, error: error.message }));
      }
    }
  }, [enhancedGraphQLInterface, cancelRequests, dataSourceStatus.primarySource]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelRequests();
    };
  }, [cancelRequests]);

  return {
    loadingStates,
    dataStates,
    fetchDataInQueue,
    cancelRequests
  };
};

/**
 * Enhanced Arret Queued Provider Component
 */
export const EnhancedArretQueuedProvider = ({ children }) => {
  // Combined state object with progressive loading states
  const [state, setState] = useState({
    // Machine selection state
    machineModels: [],
    machineNames: [],
    selectedMachineModel: "",
    selectedMachine: "",
    filteredMachineNames: [],
    
    // Date filtering state
    dateRangeType: "month",
    selectedDate: null,
    dateFilterActive: false,
    dateRangeDescription: "",
    
    // Progressive loading states for data fetching
    dataQueue: {
      essential: { loading: false, loaded: false, error: null },
      performance: { loading: false, loaded: false, error: null },
      charts: { loading: false, loaded: false, error: null },
      table: { loading: false, loaded: false, error: null }
    },
    
    // Enhanced data states
    enhancedData: {
      stats: null,
      topStops: null,
      evolution: null,
      machineComparison: null,
      performanceMetrics: null,
      tableData: null,
      dataSource: 'unknown'
    },
    
    // Legacy data states for backwards compatibility
    allMachineStops: [],
    topStopsData: [],
    machineComparison: [],
    stopReasons: [],
    chartData: [],
    
    // UI states
    skeletons: INITIAL_SKELETON_STATE,
    isDataLoading: false,
    hasDataLoaded: false,
    isRefreshing: false,
    lastRefresh: null,
    
    // Error states
    error: null,
    networkError: false
  });

  // Initialize GraphQL interfaces (both enhanced and legacy)
  const { fetchGraphQL } = useGraphQLInterface();
  const enhancedGraphQLInterface = createEnhancedGraphQLInterface(fetchGraphQL);
  
  // Data source status monitoring
  const dataSourceStatus = useDataSourceStatus(fetchGraphQL, {
    checkInterval: 30000, // 30 seconds
    enableAutoRetry: true
  });

  // Enhanced queued data manager
  const {
    loadingStates: enhancedLoadingStates,
    dataStates: enhancedDataStates,
    fetchDataInQueue: enhancedFetchDataInQueue,
    cancelRequests: enhancedCancelRequests
  } = useEnhancedQueuedDataManager(enhancedGraphQLInterface, dataSourceStatus.dataSourceStatus);

  // Legacy hooks for backwards compatibility
  const skeletonManager = useSkeletonManager(state.skeletons, setState);
  const computedValues = useComputedValues(state);
  const eventHandlers = useEventHandlers(setState, skeletonManager);

  /**
   * Convert enhanced data to legacy format for backwards compatibility
   */
  const convertToLegacyFormat = useCallback((enhancedData) => {
    const legacyData = {};

    // Convert essential data
    if (enhancedData.essential) {
      legacyData.topStopsData = enhancedData.essential.topStops?.reasons || [];
      // Convert stats to legacy format if needed
    }

    // Convert chart data
    if (enhancedData.charts) {
      legacyData.machineComparison = enhancedData.charts.machineComparison?.machines || [];
      legacyData.chartData = enhancedData.charts.evolution?.evolution || [];
    }

    // Convert table data
    if (enhancedData.table) {
      legacyData.allMachineStops = enhancedData.table.stops || [];
    }

    return legacyData;
  }, []);

  /**
   * Enhanced refresh data function
   */
  const refreshData = useCallback(async (forceRefresh = false) => {
    const filters = {
      machineId: state.selectedMachine || undefined,
      model: state.selectedMachineModel || undefined,
      startDate: computedValues.dateRange?.startDate || undefined,
      endDate: computedValues.dateRange?.endDate || undefined,
      dateRangeType: state.dateRangeType
    };

    setState(prev => ({ ...prev, isRefreshing: true, error: null }));

    try {
      await enhancedFetchDataInQueue(filters, forceRefresh);
      setState(prev => ({ 
        ...prev, 
        isRefreshing: false, 
        lastRefresh: new Date(),
        hasDataLoaded: true 
      }));
    } catch (error) {
      console.error('Enhanced data refresh failed:', error);
      setState(prev => ({ 
        ...prev, 
        isRefreshing: false, 
        error: error.message,
        networkError: true
      }));
    }
  }, [state.selectedMachine, state.selectedMachineModel, state.dateRangeType, computedValues.dateRange, enhancedFetchDataInQueue]);

  /**
   * Update legacy data when enhanced data changes
   */
  useEffect(() => {
    const legacyData = convertToLegacyFormat(enhancedDataStates);
    const enhancedData = {
      stats: enhancedDataStates.essential?.stats || null,
      topStops: enhancedDataStates.essential?.topStops || null,
      evolution: enhancedDataStates.charts?.evolution || null,
      machineComparison: enhancedDataStates.charts?.machineComparison || null,
      performanceMetrics: enhancedDataStates.performance || null,
      tableData: enhancedDataStates.table || null,
      dataSource: enhancedDataStates.essential?.dataSource || 
                 enhancedDataStates.charts?.dataSource || 
                 enhancedDataStates.performance?.dataSource || 'unknown'
    };

    setState(prev => ({
      ...prev,
      ...legacyData,
      enhancedData,
      dataQueue: {
        essential: { 
          loading: enhancedLoadingStates.essential, 
          loaded: !!enhancedDataStates.essential, 
          error: enhancedDataStates.error 
        },
        performance: { 
          loading: enhancedLoadingStates.performance, 
          loaded: !!enhancedDataStates.performance, 
          error: null 
        },
        charts: { 
          loading: enhancedLoadingStates.charts, 
          loaded: !!enhancedDataStates.charts, 
          error: null 
        },
        table: { 
          loading: enhancedLoadingStates.table, 
          loaded: !!enhancedDataStates.table, 
          error: null 
        }
      },
      isDataLoading: Object.values(enhancedLoadingStates).some(loading => loading)
    }));
  }, [enhancedDataStates, enhancedLoadingStates, convertToLegacyFormat]);

  /**
   * Initial data load
   */
  useEffect(() => {
    if (dataSourceStatus.dataSourceStatus.primarySource !== 'unknown') {
      refreshData(false);
    }
  }, [dataSourceStatus.dataSourceStatus.primarySource, refreshData]);

  /**
   * Manual data indexing trigger
   */
  const triggerDataIndexing = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isRefreshing: true }));
      const result = await enhancedGraphQLInterface.indexDashboardData();
      console.log('Data indexing result:', result);
      
      if (result.success) {
        // Refresh data after successful indexing
        await refreshData(true);
      }
      
      return result;
    } catch (error) {
      console.error('Data indexing failed:', error);
      setState(prev => ({ ...prev, isRefreshing: false, error: error.message }));
      throw error;
    }
  }, [enhancedGraphQLInterface, refreshData]);

  // Context value combining enhanced and legacy features
  const contextValue = {
    // Enhanced features
    enhancedData: state.enhancedData,
    dataSourceStatus: dataSourceStatus.dataSourceStatus,
    isElasticsearchPrimary: dataSourceStatus.isElasticsearchPrimary,
    triggerDataIndexing,
    
    // State and data
    state,
    setState,
    
    // Legacy data for backwards compatibility
    allMachineStops: state.allMachineStops,
    topStopsData: state.topStopsData,
    machineComparison: state.machineComparison,
    stopReasons: state.stopReasons,
    chartData: state.chartData,
    
    // Computed values
    ...computedValues,
    
    // Event handlers
    ...eventHandlers,
    
    // Data management
    refreshData,
    cancelRequests: enhancedCancelRequests,
    
    // Loading states
    isDataLoading: state.isDataLoading,
    hasDataLoaded: state.hasDataLoaded,
    isRefreshing: state.isRefreshing,
    dataQueue: state.dataQueue,
    
    // Skeleton management
    skeletons: state.skeletons,
    ...skeletonManager,
    
    // Error states
    error: state.error,
    networkError: state.networkError,
    
    // Metadata
    lastRefresh: state.lastRefresh,
    
    // Chart colors
    CHART_COLORS
  };

  return (
    <EnhancedArretQueuedContext.Provider value={contextValue}>
      {children}
    </EnhancedArretQueuedContext.Provider>
  );
};

/**
 * Wrapper component that provides data source status
 */
export const EnhancedArretQueuedProviderWithDataSource = ({ children }) => {
  const { fetchGraphQL } = useGraphQLInterface();
  
  return (
    <DataSourceStatusProvider fetchGraphQL={fetchGraphQL}>
      <EnhancedArretQueuedProvider>
        {children}
      </EnhancedArretQueuedProvider>
    </DataSourceStatusProvider>
  );
};

export default EnhancedArretQueuedContext;
