import React, { createContext, useContext, useMemo, useEffect } from 'react';
import { theme as antdTheme } from 'antd';
import { useSettings } from '../hooks/useSettings';
import SOMIPEM_COLORS from '../styles/brand-colors';

/**
 * Enhanced Theme Context that integrates with Settings
 * Provides theme configuration based on user settings
 */
const EnhancedThemeContext = createContext();

/**
 * Enhanced Theme Provider that uses settings for theme configuration
 */
export function EnhancedThemeProvider({ children }) {
  const { theme, settings, loading } = useSettings();

  // Compute theme configuration based on settings
  const themeConfig = useMemo(() => {
    if (!settings || loading) {
      // Default theme while loading
      return {
        darkMode: false,
        compactMode: false,
        animationsEnabled: true,
        chartAnimations: true
      };
    }

    return {
      darkMode: theme.darkMode,
      compactMode: theme.compactMode,
      animationsEnabled: theme.animationsEnabled,
      chartAnimations: theme.chartAnimations
    };
  }, [theme, settings, loading]);

  // Ant Design theme configuration with comprehensive dark mode support
  const antdThemeConfig = useMemo(() => {
    // Determine algorithms to apply
    const algorithms = [];

    // Base algorithm (light or dark)
    algorithms.push(themeConfig.darkMode ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm);

    // Add compact algorithm if enabled
    if (themeConfig.compactMode) {
      algorithms.push(antdTheme.compactAlgorithm);
    }

    const baseConfig = {
      algorithm: algorithms.length === 1 ? algorithms[0] : algorithms,
      token: {
        colorPrimary: SOMIPEM_COLORS.primary,
        colorSuccess: SOMIPEM_COLORS.success,
        colorWarning: SOMIPEM_COLORS.warning,
        colorError: SOMIPEM_COLORS.error,
        colorInfo: SOMIPEM_COLORS.info,
        borderRadius: 6,
        wireframe: false,
        // Dark mode specific tokens
        ...(themeConfig.darkMode && {
          colorBgContainer: '#1f1f1f',
          colorBgElevated: '#262626',
          colorBgLayout: '#141414',
          colorBorder: '#303030',
          colorBorderSecondary: '#404040',
          colorText: '#ffffff',
          colorTextSecondary: '#cccccc',
          colorTextTertiary: '#999999',
          colorFill: '#262626',
          colorFillSecondary: '#1f1f1f',
          colorFillTertiary: '#141414',
          colorFillQuaternary: '#0f0f0f',
        })
      },
      components: {
        Layout: {
          bodyBg: themeConfig.darkMode ? '#141414' : '#f5f5f5',
          headerBg: themeConfig.darkMode ? '#001529' : '#ffffff',
          siderBg: themeConfig.darkMode ? '#001529' : '#ffffff',
          triggerBg: themeConfig.darkMode ? '#002140' : '#ffffff',
          triggerColor: themeConfig.darkMode ? '#ffffff' : '#000000',
        },
        Menu: {
          itemBg: 'transparent',
          subMenuItemBg: 'transparent',
          itemSelectedBg: SOMIPEM_COLORS.primary + '20',
          itemHoverBg: SOMIPEM_COLORS.primary + '10',
          itemColor: themeConfig.darkMode ? '#ffffff' : '#000000',
          itemSelectedColor: SOMIPEM_COLORS.primary,
          itemHoverColor: SOMIPEM_COLORS.primary,
        },
        Card: {
          paddingLG: themeConfig.compactMode ? 16 : 24,
          padding: themeConfig.compactMode ? 12 : 16,
          headerBg: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
          colorBgContainer: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
          colorBorder: themeConfig.darkMode ? '#303030' : '#d9d9d9',
        },
        Table: {
          padding: themeConfig.compactMode ? 8 : 12,
          paddingXS: themeConfig.compactMode ? 4 : 8,
          headerBg: themeConfig.darkMode ? '#262626' : '#fafafa',
          headerColor: themeConfig.darkMode ? '#ffffff' : '#000000',
          rowHoverBg: themeConfig.darkMode ? '#262626' : '#f5f5f5',
          colorBgContainer: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
        },
        Button: {
          paddingInline: themeConfig.compactMode ? 12 : 16,
          colorBgContainer: themeConfig.darkMode ? '#262626' : '#ffffff',
          colorBorder: themeConfig.darkMode ? '#404040' : '#d9d9d9',
        },
        Input: {
          colorBgContainer: themeConfig.darkMode ? '#262626' : '#ffffff',
          colorBorder: themeConfig.darkMode ? '#404040' : '#d9d9d9',
          colorText: themeConfig.darkMode ? '#ffffff' : '#000000',
        },
        Select: {
          colorBgContainer: themeConfig.darkMode ? '#262626' : '#ffffff',
          colorBorder: themeConfig.darkMode ? '#404040' : '#d9d9d9',
          colorText: themeConfig.darkMode ? '#ffffff' : '#000000',
        },
        Tabs: {
          colorText: themeConfig.darkMode ? '#ffffff' : '#000000',
          colorTextSecondary: themeConfig.darkMode ? '#cccccc' : '#666666',
        },
        Modal: {
          contentBg: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
          headerBg: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
          footerBg: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
        },
        Dropdown: {
          colorBgElevated: themeConfig.darkMode ? '#262626' : '#ffffff',
          colorBorder: themeConfig.darkMode ? '#404040' : '#d9d9d9',
        },
        Tooltip: {
          colorBgSpotlight: themeConfig.darkMode ? '#262626' : '#ffffff',
        }
      }
    };

    return baseConfig;
  }, [themeConfig]);

  // CSS classes for theme
  const themeClasses = useMemo(() => {
    const classes = [];
    
    if (themeConfig.darkMode) classes.push('dark-theme');
    if (themeConfig.compactMode) classes.push('compact-theme');
    if (!themeConfig.animationsEnabled) classes.push('no-animations');
    
    return classes.join(' ');
  }, [themeConfig]);

  // Apply theme classes to document body
  useEffect(() => {
    const body = document.body;
    
    // Remove existing theme classes
    body.classList.remove('dark-theme', 'compact-theme', 'no-animations');
    
    // Add current theme classes
    if (themeConfig.darkMode) body.classList.add('dark-theme');
    if (themeConfig.compactMode) body.classList.add('compact-theme');
    if (!themeConfig.animationsEnabled) body.classList.add('no-animations');
    
    return () => {
      // Cleanup on unmount
      body.classList.remove('dark-theme', 'compact-theme', 'no-animations');
    };
  }, [themeConfig]);

  // Chart theme configuration
  const chartTheme = useMemo(() => {
    return {
      backgroundColor: themeConfig.darkMode ? '#141414' : '#ffffff',
      textColor: themeConfig.darkMode ? '#ffffff' : '#000000',
      gridColor: themeConfig.darkMode ? '#303030' : '#f0f0f0',
      colors: SOMIPEM_COLORS.chartColors,
      animations: themeConfig.animationsEnabled && themeConfig.chartAnimations
    };
  }, [themeConfig]);

  // Performance settings for components
  const performanceConfig = useMemo(() => {
    if (!settings) return {};
    
    return {
      lazyLoading: settings.performance?.optimization?.lazyLoading ?? true,
      virtualization: settings.performance?.optimization?.virtualization ?? true,
      caching: settings.performance?.caching?.enabled ?? true,
      cacheDuration: settings.performance?.caching?.duration ?? 300
    };
  }, [settings]);

  // Table configuration
  const tableConfig = useMemo(() => {
    if (!settings) return {};
    
    return {
      pageSize: settings.tables?.defaultPageSize ?? 20,
      pageSizeOptions: settings.tables?.pageSizeOptions ?? [10, 20, 50, 100],
      showQuickJumper: settings.tables?.showQuickJumper ?? true,
      virtualized: settings.tables?.virtualizationThreshold > 0
    };
  }, [settings]);

  // Chart configuration
  const chartConfig = useMemo(() => {
    if (!settings) return {};

    return {
      showLegend: settings.charts?.showLegend ?? true,
      colorScheme: settings.charts?.colorScheme ?? 'brand',
      performanceMode: settings.charts?.performanceMode ?? false,
      animations: themeConfig.animationsEnabled && themeConfig.chartAnimations
    };
  }, [settings, themeConfig]);

  // Refresh configuration
  const refreshConfig = useMemo(() => {
    if (!settings) return {};
    
    return {
      dashboardInterval: settings.refresh?.dashboardInterval ?? 300,
      realtimeInterval: settings.refresh?.realtimeInterval ?? 60,
      autoRefreshEnabled: settings.refresh?.autoRefreshEnabled ?? true,
      backgroundRefresh: settings.refresh?.backgroundRefresh ?? true
    };
  }, [settings]);

  const contextValue = {
    // Theme configuration
    ...themeConfig,
    themeClasses,
    antdThemeConfig,
    chartTheme,
    
    // Component configurations
    performanceConfig,
    tableConfig,
    chartConfig,
    refreshConfig,
    
    // Utility functions
    isDark: themeConfig.darkMode,
    isCompact: themeConfig.compactMode,
    hasAnimations: themeConfig.animationsEnabled,
    
    // Settings loading state
    loading
  };

  return (
    <EnhancedThemeContext.Provider value={contextValue}>
      {children}
    </EnhancedThemeContext.Provider>
  );
}

/**
 * Hook to use enhanced theme context
 */
export function useEnhancedTheme() {
  const context = useContext(EnhancedThemeContext);
  
  if (!context) {
    throw new Error('useEnhancedTheme must be used within an EnhancedThemeProvider');
  }
  
  return context;
}

/**
 * HOC to provide enhanced theme to components
 */
export function withEnhancedTheme(Component) {
  return function EnhancedThemeComponent(props) {
    return (
      <EnhancedThemeProvider>
        <Component {...props} />
      </EnhancedThemeProvider>
    );
  };
}

export default EnhancedThemeContext;
