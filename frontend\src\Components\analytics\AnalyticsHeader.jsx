import React from 'react';
import { Typography, <PERSON>, Badge, <PERSON><PERSON><PERSON>, Button } from 'antd';
import { 
  RobotOutlined,
  ThunderboltOutlined,
  ExperimentOutlined,
  StarOutlined,
  SettingOutlined,
  ReloadOutlined,
  FullscreenOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const AnalyticsHeader = () => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
      borderRadius: '16px',
      padding: '32px',
      marginBottom: '24px',
      color: 'white',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Animated background elements */}
      <div style={{
        position: 'absolute',
        top: '-50%',
        right: '-10%',
        width: '200px',
        height: '200px',
        background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'pulse 4s ease-in-out infinite'
      }} />
      
      <div style={{
        position: 'absolute',
        bottom: '-30%',
        left: '-5%',
        width: '150px',
        height: '150px',
        background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%)',
        borderRadius: '50%',
        animation: 'pulse 6s ease-in-out infinite reverse'
      }} />
      
      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Main Header Row */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'flex-start',
          marginBottom: '16px'
        }}>
          <div>
            <Space align="center" size="large">
              <div style={{
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '12px',
                padding: '12px',
                backdropFilter: 'blur(10px)'
              }}>
                <RobotOutlined style={{ fontSize: '32px', color: 'white' }} />
              </div>
              
              <div>
                <Title 
                  level={1} 
                  style={{ 
                    color: 'white', 
                    margin: 0,
                    fontSize: '36px',
                    fontWeight: '700',
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                  }}
                >
                  AI Analytics Dashboard
                </Title>
                <Text style={{ 
                  color: 'rgba(255,255,255,0.9)', 
                  fontSize: '16px',
                  fontWeight: '500'
                }}>
                  Advanced Intelligence & Performance Optimization Platform
                </Text>
              </div>
            </Space>
          </div>
          
          <Space size="middle">
            <Tooltip title="Refresh All Data" placement="bottom">
              <Button 
                type="text" 
                icon={<ReloadOutlined />}
                style={{ 
                  color: 'white',
                  border: '1px solid rgba(255,255,255,0.3)',
                  borderRadius: '8px',
                  backdropFilter: 'blur(10px)'
                }}
              />
            </Tooltip>
            
            <Tooltip title="Analytics Settings" placement="bottom">
              <Button 
                type="text" 
                icon={<SettingOutlined />}
                style={{ 
                  color: 'white',
                  border: '1px solid rgba(255,255,255,0.3)',
                  borderRadius: '8px',
                  backdropFilter: 'blur(10px)'
                }}
              />
            </Tooltip>
            
            <Tooltip title="Fullscreen Mode" placement="bottom">
              <Button 
                type="text" 
                icon={<FullscreenOutlined />}
                style={{ 
                  color: 'white',
                  border: '1px solid rgba(255,255,255,0.3)',
                  borderRadius: '8px',
                  backdropFilter: 'blur(10px)'
                }}
              />
            </Tooltip>
          </Space>
        </div>
        
        {/* Feature Badges Row */}
        <Space wrap size="middle">
          <Badge 
            count={
              <Space size={4}>
                <ThunderboltOutlined style={{ fontSize: '10px' }} />
                <span>Real-time AI</span>
              </Space>
            }
            style={{ 
              background: 'linear-gradient(135deg, #52c41a, #389e0d)',
              borderRadius: '12px',
              padding: '4px 8px',
              border: 'none',
              color: 'white',
              fontSize: '11px',
              fontWeight: '600'
            }}
          />
          
          <Badge 
            count={
              <Space size={4}>
                <ExperimentOutlined style={{ fontSize: '10px' }} />
                <span>96 Analytics</span>
              </Space>
            }
            style={{ 
              background: 'linear-gradient(135deg, #fa8c16, #d46b08)',
              borderRadius: '12px',
              padding: '4px 8px',
              border: 'none',
              color: 'white',
              fontSize: '11px',
              fontWeight: '600'
            }}
          />
          
          <Badge 
            count={
              <Space size={4}>
                <StarOutlined style={{ fontSize: '10px' }} />
                <span>13 Intelligence Categories</span>
              </Space>
            }
            style={{ 
              background: 'linear-gradient(135deg, #eb2f96, #c41d7f)',
              borderRadius: '12px',
              padding: '4px 8px',
              border: 'none',
              color: 'white',
              fontSize: '11px',
              fontWeight: '600'
            }}
          />
          
          <Badge 
            count={
              <Space size={4}>
                <RobotOutlined style={{ fontSize: '10px' }} />
                <span>Predictive ML</span>
              </Space>
            }
            style={{ 
              background: 'linear-gradient(135deg, #722ed1, #531dab)',
              borderRadius: '12px',
              padding: '4px 8px',
              border: 'none',
              color: 'white',
              fontSize: '11px',
              fontWeight: '600'
            }}
          />
        </Space>
        
        {/* Status Indicator */}
        <div style={{
          position: 'absolute',
          top: '16px',
          right: '16px',
          background: 'rgba(255,255,255,0.2)',
          borderRadius: '20px',
          padding: '8px 12px',
          backdropFilter: 'blur(10px)'
        }}>
          <Space size={8}>
            <div style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: '#52c41a',
              animation: 'pulse 2s ease-in-out infinite'
            }} />
            <Text style={{ color: 'white', fontSize: '12px', fontWeight: '500' }}>
              AI Systems Online
            </Text>
          </Space>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.1);
          }
        }
      `}</style>
    </div>
  );
};

export default AnalyticsHeader;
