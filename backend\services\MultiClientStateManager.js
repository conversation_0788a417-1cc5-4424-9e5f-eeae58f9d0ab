/**
 * Multi-Client State Management Service
 * Provides real-time data synchronization across multiple user sessions and devices
 * 
 * Features:
 * - Real-time WebSocket state synchronization
 * - Cross-device data consistency
 * - Multi-user collaborative features
 * - Optimistic updates with conflict resolution
 * - Redis-backed state persistence
 */

import { WebSocket } from 'ws';
import redisPubSubService from './RedisPubSubService.js';
import redisService from './RedisService.js';
import { v4 as uuidv4 } from 'uuid';

class MultiClientStateManager {
  constructor() {
    this.clients = new Map(); // clientId -> client info
    this.userSessions = new Map(); // userId -> Set of clientIds
    this.roomSubscriptions = new Map(); // roomId -> Set of clientIds
    this.initialized = false;
    
    // State management configuration
    this.STATE_CONFIG = {
      // State types and their sync strategies
      SYNC_STRATEGIES: {
        IMMEDIATE: 'immediate',     // Sync immediately to all clients
        DEBOUNCED: 'debounced',     // Debounce rapid changes
        OPTIMISTIC: 'optimistic',   // Optimistic updates with rollback
        COLLABORATIVE: 'collaborative' // Multi-user collaborative editing
      },
      
      // State categories
      STATE_TYPES: {
        PRODUCTION_DATA: 'production_data',
        MACHINE_STATUS: 'machine_status',
        DASHBOARD_FILTERS: 'dashboard_filters',
        USER_PREFERENCES: 'user_preferences',
        REAL_TIME_METRICS: 'real_time_metrics',
        STOP_ANALYSIS: 'stop_analysis',
        REPORTS: 'reports'
      },
      
      // Conflict resolution strategies
      CONFLICT_RESOLUTION: {
        LAST_WRITE_WINS: 'last_write_wins',
        MERGE: 'merge',
        USER_PROMPT: 'user_prompt',
        OPERATIONAL_TRANSFORM: 'operational_transform'
      },
      
      // Cache TTL for different state types
      STATE_TTL: {
        production_data: 300,      // 5 minutes
        machine_status: 60,        // 1 minute
        dashboard_filters: 1800,   // 30 minutes
        user_preferences: 3600,    // 1 hour
        real_time_metrics: 30,     // 30 seconds
        stop_analysis: 600,        // 10 minutes
        reports: 900               // 15 minutes
      }
    };

    // Performance metrics
    this.metrics = {
      totalClients: 0,
      activeRooms: 0,
      stateUpdates: 0,
      conflictsResolved: 0,
      avgSyncLatency: 0,
      lastActivity: null
    };
  }

  /**
   * Initialize the multi-client state manager
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    try {
      // Initialize Redis services
      await redisService.initialize();
      await redisPubSubService.initialize();

      // Subscribe to state change events
      await this.setupStateSubscriptions();

      this.initialized = true;
      console.log('✅ Multi-Client State Manager initialized');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize Multi-Client State Manager:', error);
      return false;
    }
  }

  /**
   * Register a new WebSocket client
   */
  async registerClient(ws, userId, sessionInfo = {}) {
    const clientId = uuidv4();
    const timestamp = Date.now();

    const clientInfo = {
      id: clientId,
      userId,
      ws,
      sessionInfo: {
        userAgent: sessionInfo.userAgent || 'Unknown',
        ipAddress: sessionInfo.ipAddress || 'Unknown',
        deviceType: this.detectDeviceType(sessionInfo.userAgent),
        ...sessionInfo
      },
      connectedAt: timestamp,
      lastActivity: timestamp,
      subscribedRooms: new Set(),
      pendingUpdates: new Map(),
      isActive: true
    };

    // Store client information
    this.clients.set(clientId, clientInfo);

    // Track user sessions
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set());
    }
    this.userSessions.get(userId).add(clientId);

    // Set up WebSocket event handlers
    this.setupClientHandlers(clientInfo);

    // Send initial state to client
    await this.sendInitialState(clientInfo);

    // Notify other clients about new connection
    await this.broadcastUserPresence(userId, 'connected', clientInfo.sessionInfo);

    this.metrics.totalClients++;
    this.metrics.lastActivity = new Date().toISOString();

    console.log(`👤 Client registered: ${clientId} for user ${userId}`);
    return clientId;
  }

  /**
   * Set up WebSocket event handlers for a client
   */
  setupClientHandlers(clientInfo) {
    const { ws, id: clientId, userId } = clientInfo;

    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message);
        await this.handleClientMessage(clientId, data);
      } catch (error) {
        console.error(`❌ Error processing message from client ${clientId}:`, error);
        this.sendError(clientInfo, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      this.unregisterClient(clientId);
    });

    ws.on('error', (error) => {
      console.error(`❌ WebSocket error for client ${clientId}:`, error);
      this.unregisterClient(clientId);
    });

    // Set up ping/pong for connection health
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      } else {
        clearInterval(pingInterval);
      }
    }, 30000);

    clientInfo.pingInterval = pingInterval;
  }

  /**
   * Handle incoming messages from clients
   */
  async handleClientMessage(clientId, message) {
    const clientInfo = this.clients.get(clientId);
    if (!clientInfo) {
      console.warn(`⚠️ Message from unknown client: ${clientId}`);
      return;
    }

    clientInfo.lastActivity = Date.now();

    switch (message.type) {
      case 'STATE_UPDATE':
        await this.handleStateUpdate(clientInfo, message);
        break;
      
      case 'SUBSCRIBE_ROOM':
        await this.subscribeClientToRoom(clientInfo, message.roomId);
        break;
      
      case 'UNSUBSCRIBE_ROOM':
        await this.unsubscribeClientFromRoom(clientInfo, message.roomId);
        break;
      
      case 'REQUEST_STATE':
        await this.sendStateToClient(clientInfo, message.stateType, message.stateKey);
        break;
      
      case 'PING':
        this.sendToClient(clientInfo, { type: 'PONG', timestamp: Date.now() });
        break;
      
      default:
        console.warn(`⚠️ Unknown message type: ${message.type}`);
    }
  }

  /**
   * Handle state updates from clients
   */
  async handleStateUpdate(clientInfo, message) {
    const { stateType, stateKey, data, strategy, timestamp } = message;
    
    try {
      // Validate state update
      if (!this.validateStateUpdate(stateType, stateKey, data)) {
        this.sendError(clientInfo, 'Invalid state update');
        return;
      }

      // Check for conflicts
      const conflict = await this.detectConflict(stateType, stateKey, data, timestamp);
      
      if (conflict) {
        await this.resolveConflict(clientInfo, conflict);
        return;
      }

      // Apply state update
      const updateResult = await this.applyStateUpdate(
        clientInfo.userId,
        stateType,
        stateKey,
        data,
        strategy || this.STATE_CONFIG.SYNC_STRATEGIES.IMMEDIATE
      );

      if (updateResult.success) {
        // Broadcast update to relevant clients
        await this.broadcastStateUpdate(stateType, stateKey, data, clientInfo.id);
        
        // Send confirmation to originating client
        this.sendToClient(clientInfo, {
          type: 'STATE_UPDATE_CONFIRMED',
          stateType,
          stateKey,
          timestamp: Date.now()
        });

        this.metrics.stateUpdates++;
      } else {
        this.sendError(clientInfo, `Failed to update state: ${updateResult.error}`);
      }

    } catch (error) {
      console.error('❌ Error handling state update:', error);
      this.sendError(clientInfo, 'Internal server error');
    }
  }

  /**
   * Apply state update with Redis persistence
   */
  async applyStateUpdate(userId, stateType, stateKey, data, strategy) {
    try {
      const fullStateKey = `state:${stateType}:${stateKey}`;
      const ttl = this.STATE_CONFIG.STATE_TTL[stateType] || 300;

      // Create state update record
      const stateUpdate = {
        userId,
        stateType,
        stateKey,
        data,
        strategy,
        timestamp: Date.now(),
        version: await this.getNextVersion(fullStateKey)
      };

      // Store in Redis with appropriate TTL
      await redisService.setWithTTL(fullStateKey, JSON.stringify(stateUpdate), ttl);

      // Store in version history for conflict resolution
      const historyKey = `${fullStateKey}:history`;
      await redisService.listPush(historyKey, JSON.stringify(stateUpdate));
      await redisService.expire(historyKey, ttl * 2); // Keep history longer

      // Publish state change event
      await redisPubSubService.publishStateUpdate(stateType, stateKey, stateUpdate);

      return { success: true, version: stateUpdate.version };

    } catch (error) {
      console.error('❌ Error applying state update:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Broadcast state update to relevant clients
   */
  async broadcastStateUpdate(stateType, stateKey, data, originClientId) {
    const message = {
      type: 'STATE_UPDATED',
      stateType,
      stateKey,
      data,
      timestamp: Date.now(),
      origin: originClientId
    };

    // Determine which clients should receive this update
    const targetClients = this.getTargetClients(stateType, stateKey);

    let broadcastCount = 0;
    for (const clientId of targetClients) {
      if (clientId !== originClientId) { // Don't send back to originator
        const clientInfo = this.clients.get(clientId);
        if (clientInfo && clientInfo.isActive) {
          this.sendToClient(clientInfo, message);
          broadcastCount++;
        }
      }
    }

    console.log(`📡 Broadcasted state update to ${broadcastCount} clients`);
  }

  /**
   * Send message to a specific client
   */
  sendToClient(clientInfo, message) {
    if (clientInfo.ws.readyState === WebSocket.OPEN) {
      try {
        clientInfo.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error(`❌ Error sending message to client ${clientInfo.id}:`, error);
        return false;
      }
    }
    return false;
  }

  /**
   * Send error message to client
   */
  sendError(clientInfo, errorMessage) {
    this.sendToClient(clientInfo, {
      type: 'ERROR',
      message: errorMessage,
      timestamp: Date.now()
    });
  }

  /**
   * Unregister a client
   */
  unregisterClient(clientId) {
    const clientInfo = this.clients.get(clientId);
    if (!clientInfo) return;

    const { userId, pingInterval } = clientInfo;

    // Clear ping interval
    if (pingInterval) {
      clearInterval(pingInterval);
    }

    // Remove from user sessions
    const userClients = this.userSessions.get(userId);
    if (userClients) {
      userClients.delete(clientId);
      if (userClients.size === 0) {
        this.userSessions.delete(userId);
      }
    }

    // Remove from room subscriptions
    for (const roomId of clientInfo.subscribedRooms) {
      this.unsubscribeClientFromRoom(clientInfo, roomId);
    }

    // Remove client
    this.clients.delete(clientId);
    this.metrics.totalClients--;

    console.log(`👤 Client unregistered: ${clientId} for user ${userId}`);

    // Notify other clients about disconnection
    this.broadcastUserPresence(userId, 'disconnected', clientInfo.sessionInfo);
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      activeClients: this.clients.size,
      activeUsers: this.userSessions.size,
      activeRooms: this.roomSubscriptions.size
    };
  }

  /**
   * Setup Redis state subscriptions
   */
  async setupStateSubscriptions() {
    // Subscribe to state update events from other server instances
    await redisPubSubService.subscribe('state:*', (channel, message) => {
      this.handleRedisStateUpdate(channel, message);
    });
  }

  /**
   * Handle state updates from Redis pub/sub
   */
  async handleRedisStateUpdate(channel, message) {
    try {
      const stateUpdate = JSON.parse(message);
      const { stateType, stateKey, data } = stateUpdate;

      // Broadcast to relevant clients
      await this.broadcastStateUpdate(stateType, stateKey, data, null);

    } catch (error) {
      console.error('❌ Error handling Redis state update:', error);
    }
  }

  /**
   * Send initial state to a newly connected client
   */
  async sendInitialState(clientInfo) {
    try {
      // Get user's dashboard state
      const dashboardState = await this.getState('dashboard_filters', clientInfo.userId);

      // Get user preferences
      const userPreferences = await this.getState('user_preferences', clientInfo.userId);

      // Send initial state package
      this.sendToClient(clientInfo, {
        type: 'INITIAL_STATE',
        states: {
          dashboard_filters: dashboardState,
          user_preferences: userPreferences
        },
        timestamp: Date.now()
      });

    } catch (error) {
      console.error('❌ Error sending initial state:', error);
    }
  }

  /**
   * Get state from Redis
   */
  async getState(stateType, stateKey) {
    try {
      const fullStateKey = `state:${stateType}:${stateKey}`;
      const stateData = await redisService.get(fullStateKey);

      if (stateData) {
        const parsedState = JSON.parse(stateData);
        return parsedState.data;
      }

      return null;
    } catch (error) {
      console.error('❌ Error getting state:', error);
      return null;
    }
  }

  /**
   * Subscribe client to a room for targeted updates
   */
  async subscribeClientToRoom(clientInfo, roomId) {
    clientInfo.subscribedRooms.add(roomId);

    if (!this.roomSubscriptions.has(roomId)) {
      this.roomSubscriptions.set(roomId, new Set());
    }
    this.roomSubscriptions.get(roomId).add(clientInfo.id);

    console.log(`📡 Client ${clientInfo.id} subscribed to room: ${roomId}`);
  }

  /**
   * Unsubscribe client from a room
   */
  async unsubscribeClientFromRoom(clientInfo, roomId) {
    clientInfo.subscribedRooms.delete(roomId);

    const roomClients = this.roomSubscriptions.get(roomId);
    if (roomClients) {
      roomClients.delete(clientInfo.id);
      if (roomClients.size === 0) {
        this.roomSubscriptions.delete(roomId);
      }
    }

    console.log(`📡 Client ${clientInfo.id} unsubscribed from room: ${roomId}`);
  }

  /**
   * Get target clients for a state update
   */
  getTargetClients(stateType, stateKey) {
    const targetClients = new Set();

    // For user-specific states, only target that user's clients
    if (stateType === 'user_preferences' || stateType === 'dashboard_filters') {
      const userClients = this.userSessions.get(stateKey);
      if (userClients) {
        userClients.forEach(clientId => targetClients.add(clientId));
      }
    } else {
      // For global states, target all clients
      this.clients.forEach((_, clientId) => targetClients.add(clientId));
    }

    return targetClients;
  }

  /**
   * Validate state update
   */
  validateStateUpdate(stateType, stateKey, data) {
    // Basic validation - can be extended with schema validation
    if (!stateType || !stateKey || data === undefined) {
      return false;
    }

    // Check if state type is supported
    if (!Object.values(this.STATE_CONFIG.STATE_TYPES).includes(stateType)) {
      return false;
    }

    return true;
  }

  /**
   * Detect conflicts in state updates
   */
  async detectConflict(stateType, stateKey, data, timestamp) {
    try {
      const fullStateKey = `state:${stateType}:${stateKey}`;
      const currentState = await redisService.get(fullStateKey);

      if (currentState) {
        const parsedState = JSON.parse(currentState);

        // Check if there's a newer version
        if (parsedState.timestamp > timestamp) {
          return {
            type: 'version_conflict',
            currentVersion: parsedState.version,
            currentTimestamp: parsedState.timestamp,
            incomingTimestamp: timestamp
          };
        }
      }

      return null;
    } catch (error) {
      console.error('❌ Error detecting conflict:', error);
      return null;
    }
  }

  /**
   * Resolve state conflicts
   */
  async resolveConflict(clientInfo, conflict) {
    // For now, use last-write-wins strategy
    // Can be extended with more sophisticated conflict resolution
    this.sendToClient(clientInfo, {
      type: 'CONFLICT_DETECTED',
      conflict,
      resolution: 'last_write_wins',
      timestamp: Date.now()
    });

    this.metrics.conflictsResolved++;
  }

  /**
   * Get next version number for state
   */
  async getNextVersion(stateKey) {
    try {
      const versionKey = `${stateKey}:version`;
      const version = await redisService.increment(versionKey);
      await redisService.expire(versionKey, 3600); // Expire version counter after 1 hour
      return version;
    } catch (error) {
      console.error('❌ Error getting next version:', error);
      return 1;
    }
  }

  /**
   * Broadcast user presence updates
   */
  async broadcastUserPresence(userId, status, sessionInfo) {
    const message = {
      type: 'USER_PRESENCE',
      userId,
      status,
      sessionInfo,
      timestamp: Date.now()
    };

    // Broadcast to all clients except the user's own clients
    this.clients.forEach((clientInfo, clientId) => {
      if (clientInfo.userId !== userId) {
        this.sendToClient(clientInfo, message);
      }
    });
  }

  /**
   * Send specific state to client
   */
  async sendStateToClient(clientInfo, stateType, stateKey) {
    const state = await this.getState(stateType, stateKey);

    this.sendToClient(clientInfo, {
      type: 'STATE_RESPONSE',
      stateType,
      stateKey,
      data: state,
      timestamp: Date.now()
    });
  }

  /**
   * Detect device type from user agent
   */
  detectDeviceType(userAgent) {
    if (!userAgent) return 'unknown';

    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }
}

// Create singleton instance
const multiClientStateManager = new MultiClientStateManager();

export default multiClientStateManager;
