/**
 * Enhanced error handler for GraphQL requests
 * 
 * This improves error handling for AbortError situations to prevent excessive error messages
 * when navigating away from a page with active requests
 */

// Track abort errors to prevent excessive logging
let abortErrorsCount = 0;
const MAX_ABORT_LOGS = 3; // Only log first few abort errors to prevent console spam

export const handleGraphQLError = (err, requestId, startTime, isUnmount = false) => {
  // Always track stats
  const responseTime = Date.now() - startTime;
  
  // Special handling for AbortError
  if (err.name === 'AbortError' || (err.message && err.message.includes('aborted'))) {
    abortErrorsCount++;
    
    // Only log a limited number of abort errors to prevent console spam
    if (abortErrorsCount <= MAX_ABORT_LOGS) {
      console.log(`🛑 Request ${requestId} was cancelled ${isUnmount ? 'during unmount' : ''} after ${responseTime}ms`);
    } else if (abortErrorsCount === MAX_ABORT_LOGS + 1) {
      console.log(`🛑 Additional abort errors will be suppressed to prevent console spam`);
    }
    
    // Return a specific error that UI can detect and handle gracefully
    return {
      isAbortError: true,
      message: 'Request was cancelled',
      requestId,
      responseTime
    };
  }
  
  // Network errors
  if (err.message && (err.message.includes('Failed to fetch') || err.message.includes('Network'))) {
    console.error(`❌ Request ${requestId}: Network error after ${responseTime}ms`, err.message);
    return {
      isNetworkError: true,
      message: 'Network error - please check your connection and try again',
      requestId,
      responseTime
    };
  }
  
  // Timeout errors
  if (err.message && err.message.includes('timeout')) {
    console.error(`⏱️ Request ${requestId}: Timeout after ${responseTime}ms`);
    return {
      isTimeoutError: true,
      message: 'Request timed out - server may be experiencing high load',
      requestId,
      responseTime
    };
  }
  
  // GraphQL specific errors
  if (err.graphQLErrors) {
    console.error(`🔍 Request ${requestId}: GraphQL error after ${responseTime}ms`, err.graphQLErrors);
    return {
      isGraphQLError: true,
      message: err.graphQLErrors[0]?.message || 'GraphQL error',
      errors: err.graphQLErrors,
      requestId,
      responseTime
    };
  }
  
  // General errors
  console.error(`❌ Request ${requestId}: Failed after ${responseTime}ms:`, err);
  return {
    isGeneralError: true,
    message: err.message || 'Unknown error occurred',
    originalError: err,
    requestId,
    responseTime
  };
};

// Reset abort counter - call this when cleaning up or when new errors should be logged
export const resetAbortErrorCount = () => {
  abortErrorsCount = 0;
};

// Check if the error is an abort error (for UI to handle differently)
export const isAbortError = (error) => {
  if (!error) return false;
  return error.isAbortError || error.name === 'AbortError' || 
    (error.message && (error.message.includes('aborted') || error.message.includes('cancelled')));
};
