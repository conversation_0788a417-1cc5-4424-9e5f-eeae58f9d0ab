/* Import SOMIPEM brand utilities */
@import './styles/somipem-utilities.css';

/* SOMIPEM Brand CSS Variables */
:root {
  /* SOMIPEM Brand Colors */
  --somipem-primary-blue: #1E3A8A;
  --somipem-secondary-blue: #3B82F6;
  --somipem-dark-gray: #1F2937;
  --somipem-light-gray: #6B7280;
  --somipem-white: #FFFFFF;
  --somipem-success: #10B981;
  --somipem-warning: #F59E0B;
  --somipem-error: #EF4444;
  --somipem-hover-blue: rgba(59, 130, 246, 0.1);
  --somipem-accent-border: rgba(30, 58, 138, 0.2);
  --somipem-selected-bg: rgba(30, 58, 138, 0.1);
}

/* Dark mode color overrides */
[data-theme="dark"] {
  --somipem-primary-blue: #3B82F6;
  --somipem-secondary-blue: #60A5FA;
  --somipem-dark-gray: rgba(255, 255, 255, 0.9);
  --somipem-light-gray: rgba(255, 255, 255, 0.6);
  --somipem-accent-border: rgba(75, 85, 99, 0.3);
}

/* Styles globaux pour le mode sombre */
.dark {
  color-scheme: dark;
}
  
  /* Styles pour les transitions de thème */
  body,
  .App,
  .ant-layout,
  .ant-layout-header,
  .ant-layout-content,
  .ant-layout-footer,
  .ant-menu,
  .ant-card,
  .ant-statistic,
  .ant-table,
  .ant-typography,
  .ant-btn {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
  
  /* Styles spécifiques pour les graphiques en mode sombre */
  .dark .recharts-surface {
    filter: brightness(0.95);
  }
  
  .dark .recharts-text {
    fill: rgba(255, 255, 255, 0.75);
  }
  
  .dark .recharts-cartesian-grid-horizontal line,
  .dark .recharts-cartesian-grid-vertical line {
    stroke: rgba(255, 255, 255, 0.15);
  }
  
  .dark .recharts-legend-item-text {
    color: rgba(255, 255, 255, 0.75) !important;
  }
  
  /* Enhanced chart styles for dark mode with SOMIPEM branding */
  .dark .recharts-tooltip-wrapper .recharts-default-tooltip {
    background-color: #1F2937 !important;
    border-color: var(--somipem-accent-border) !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.5);
  }
  
  .dark .recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-label {
    color: var(--somipem-dark-gray) !important;
  }
  
  .dark .recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-item {
    color: var(--somipem-light-gray) !important;
  }
  
  .dark .recharts-default-legend .recharts-legend-item {
    color: var(--somipem-light-gray) !important;
  }
  
  /* Chart.js specific dark mode styles with SOMIPEM branding */
  .dark canvas {
    filter: brightness(0.95);
  }
  
  .dark .chartjs-tooltip {
    background-color: #1F2937 !important;
    border-color: var(--somipem-accent-border) !important;
    color: var(--somipem-dark-gray) !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.5);
  }
  
  /* Styles pour les tableaux en mode sombre avec SOMIPEM branding */
  .dark .ant-table {
    background-color: #1F2937;
  }
  
  .dark .ant-table-thead > tr > th {
    background-color: #111827;
    color: var(--somipem-dark-gray);
    border-bottom: 1px solid var(--somipem-accent-border);
  }
  
  .dark .ant-table-tbody > tr > td {
    border-bottom: 1px solid var(--somipem-accent-border);
  }
  
  .dark .ant-table-tbody > tr:hover > td {
    background-color: var(--somipem-hover-blue);
  }
  
  /* Styles pour les cartes en mode sombre avec SOMIPEM branding */
  .dark .ant-card {
    background-color: #1F2937;
    border-color: var(--somipem-accent-border);
  }
  
  .dark .ant-card-head {
    border-bottom-color: var(--somipem-accent-border);
  }
  
  /* Styles pour les boutons en mode sombre avec SOMIPEM branding */
  .dark .ant-btn-text {
    color: var(--somipem-light-gray);
  }
  
  .dark .ant-btn-text:hover {
    background-color: var(--somipem-hover-blue);
    color: var(--somipem-dark-gray);
  }
  
  .dark .ant-btn-primary {
    background-color: var(--somipem-primary-blue);
    border-color: var(--somipem-primary-blue);
  }
  
  .dark .ant-btn-primary:hover {
    background-color: var(--somipem-secondary-blue);
    border-color: var(--somipem-secondary-blue);
  }
  
  /* Styles pour les dividers en mode sombre */
  .dark .ant-divider {
    border-top-color: #303030;
  }
  
  /* Styles pour les tooltips en mode sombre */
  .dark .ant-tooltip-inner {
    background-color: #1f1f1f;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  
  /* Styles pour les menus déroulants en mode sombre avec SOMIPEM branding */
  .dark .ant-dropdown-menu {
    background-color: #1F2937;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }
  
  .dark .ant-dropdown-menu-item:hover {
    background-color: var(--somipem-hover-blue);
  }
  
  /* Styles pour les inputs en mode sombre avec SOMIPEM branding */
  .dark .ant-input {
    background-color: #111827;
    border-color: var(--somipem-accent-border);
    color: var(--somipem-dark-gray);
  }
  
  .dark .ant-input:hover {
    border-color: var(--somipem-secondary-blue);
  }
  
  .dark .ant-input:focus,
  .dark .ant-input-focused {
    border-color: var(--somipem-primary-blue);
    box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.2);
  }
  
  /* Styles pour les selects en mode sombre avec SOMIPEM branding */
  .dark .ant-select-selector {
    background-color: #111827 !important;
    border-color: var(--somipem-accent-border) !important;
    color: var(--somipem-dark-gray) !important;
  }
  
  .dark .ant-select-dropdown {
    background-color: #1F2937;
  }
  
  .dark .ant-select-item {
    color: var(--somipem-light-gray);
  }
  
  .dark .ant-select-item-option-selected {
    background-color: var(--somipem-selected-bg);
  }
  
  .dark .ant-select-item-option-active {
    background-color: var(--somipem-hover-blue);
  }
  
  /* Styles pour les datepickers en mode sombre avec SOMIPEM branding */
  .dark .ant-picker {
    background-color: #111827;
    border-color: var(--somipem-accent-border);
  }
  
  .dark .ant-picker-input > input {
    color: var(--somipem-dark-gray);
  }
  
  .dark .ant-picker-panel-container {
    background-color: #1F2937;
  }
  
  .dark .ant-picker-header {
    border-bottom-color: var(--somipem-accent-border);
  }
  
  .dark .ant-picker-header button {
    color: var(--somipem-light-gray);
  }
  
  .dark .ant-picker-content th {
    color: var(--somipem-light-gray);
  }
  
  .dark .ant-picker-cell {
    color: var(--somipem-light-gray);
  }
  
  .dark .ant-picker-cell-in-view {
    color: var(--somipem-dark-gray);
  }
  
  .dark .ant-picker-cell:hover .ant-picker-cell-inner {
    background-color: var(--somipem-hover-blue);
  }
  
  .dark .ant-picker-cell-selected .ant-picker-cell-inner {
    background-color: var(--somipem-primary-blue);
    color: #fff;
  }
  
  /* Styles pour les tabs en mode sombre avec SOMIPEM branding */
  .dark .ant-tabs-tab {
    color: var(--somipem-light-gray);
  }
  
  .dark .ant-tabs-tab:hover {
    color: var(--somipem-dark-gray);
  }
  
  .dark .ant-tabs-tab-active {
    color: var(--somipem-primary-blue);
  }
  
  .dark .ant-tabs-ink-bar {
    background-color: var(--somipem-primary-blue);
  }
  
  .dark .ant-tabs-nav::before {
    border-bottom-color: var(--somipem-accent-border);
  }
  
  /* Styles pour les badges en mode sombre avec SOMIPEM branding */
  .dark .ant-badge-count {
    box-shadow: 0 0 0 1px #1F2937;
  }
  
  /* Styles pour les avatars en mode sombre avec SOMIPEM branding */
  .dark .ant-avatar {
    background-color: var(--somipem-primary-blue);
    color: #fff;
  }
  
  /* Styles pour les progress bars en mode sombre avec SOMIPEM branding */
  .dark .ant-progress-bg {
    background-color: var(--somipem-primary-blue);
  }
  
  .dark .ant-progress-success-bg {
    background-color: var(--somipem-success);
  }
  
  .dark .ant-progress-text {
    color: var(--somipem-dark-gray);
  }

