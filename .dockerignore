# Dependencies
node_modules
*/node_modules
frontend/node_modules
backend/node_modules

# Build outputs
frontend/dist
frontend/build
backend/dist
backend/build

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker (keep production Dockerfile)
Dockerfile*
!Dockerfile.production
docker-compose*.yml
.dockerignore

# Documentation
*.md
!README.md
docs/

# Test files
**/*.test.js
**/*.test.jsx
**/*.spec.js
**/*.spec.jsx
__tests__/
test/
tests/
coverage/

# Development scripts and debug files
*.ps1
*.sh
*.bat
*.mjs
debug-*.js
test-*.js
verify-*.js
check-*.js
*-test.js
*-debug.js
*-analysis.js
*-investigation.js
*-assessment.js
*-demonstration.js
*-summary.js
*-guide.js
*-complete.js
*-status.js
*-report.js
*-plan.js
*-roadmap.js
*-migration.js
*-optimization.js
*-verification.js
*-fixes.js

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Project specific
uploads/
backend/uploads/
backend/test-output/
utputFormat/
cookies.txt
databaseStructure.sql
*.sql
