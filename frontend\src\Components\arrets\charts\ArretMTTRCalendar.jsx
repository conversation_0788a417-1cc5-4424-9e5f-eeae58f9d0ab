import React, { memo } from 'react';
import { Card, Typography, Row, Col, Tooltip } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text } = Typography;

const ArretMTTRCalendar = memo(({ data = [], loading = false, selectedDate = null }) => {
  if (loading) {
    return (
      <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>Chargement du calendrier MTTR...</div>
      </div>
    );
  }

  // Extract actual data array from response object
  const mttrData = Array.isArray(data) ? data : (data?.data || []);

  // Use selected date or current date if none selected
  const targetDate = selectedDate ? dayjs(selectedDate) : dayjs();
  const startOfMonth = targetDate.startOf('month');
  const endOfMonth = targetDate.endOf('month');
  const startDate = startOfMonth.startOf('week');
  const endDate = endOfMonth.endOf('week');

  const calendarDays = [];
  let currentDate = startDate;

  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
    const dayData = mttrData.find(item => 
      dayjs(item.date).isSame(currentDate, 'day')
    );
      calendarDays.push({
      date: currentDate.clone(),
      mttr: dayData ? parseFloat(dayData.mttr || 0) : 0,
      isCurrentMonth: currentDate.isSame(targetDate, 'month'),
      isToday: currentDate.isSame(dayjs(), 'day')
    });
    
    currentDate = currentDate.add(1, 'day');
  }

  // Get color based on MTTR value
  const getMTTRColor = (mttr) => {
    if (mttr === 0) return '#f5f5f5';
    if (mttr < 30) return '#52c41a'; // Green - Good
    if (mttr < 60) return '#faad14'; // Yellow - OK
    if (mttr < 120) return '#fa8c16'; // Orange - Warning
    return '#f5222d'; // Red - Critical
  };

  const weeks = [];
  for (let i = 0; i < calendarDays.length; i += 7) {
    weeks.push(calendarDays.slice(i, i + 7));
  }

  return (
    <div style={{ height: 300 }}>
      <div style={{ marginBottom: 16, textAlign: 'center' }}>        <Text strong style={{ fontSize: 16 }}>
          <CalendarOutlined style={{ marginRight: 8 }} />
          Calendrier MTTR - {targetDate.format('MMMM YYYY')}
        </Text>
      </div>
      
      {/* Week days header */}
      <Row style={{ marginBottom: 8 }}>
        {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map(day => (
          <Col span={3} key={day} style={{ textAlign: 'center' }}>
            <Text strong style={{ fontSize: 12, color: '#666' }}>{day}</Text>
          </Col>
        ))}
      </Row>

      {/* Calendar grid */}
      {weeks.map((week, weekIndex) => (
        <Row key={weekIndex} style={{ marginBottom: 4 }}>
          {week.map((day, dayIndex) => (
            <Col span={3} key={dayIndex} style={{ padding: '2px' }}>
              <Tooltip 
                title={`${day.date.format('DD/MM/YYYY')} - MTTR: ${day.mttr.toFixed(1)} min`}
                placement="top"
              >
                <div
                  style={{
                    height: 28,
                    backgroundColor: getMTTRColor(day.mttr),
                    border: day.isToday ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    borderRadius: 4,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    opacity: day.isCurrentMonth ? 1 : 0.4
                  }}
                >
                  <Text 
                    style={{ 
                      fontSize: 12, 
                      fontWeight: day.isToday ? 'bold' : 'normal',
                      color: day.mttr > 60 ? '#fff' : '#333'
                    }}
                  >
                    {day.date.format('DD')}
                  </Text>
                </div>
              </Tooltip>
            </Col>
          ))}
        </Row>
      ))}

      {/* Legend */}
      <Row style={{ marginTop: 16, justifyContent: 'center' }}>
        <Col>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ width: 16, height: 16, backgroundColor: '#52c41a', marginRight: 4, borderRadius: 2 }}></div>
              <Text style={{ fontSize: 12 }}>Bon (&lt;30min)</Text>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ width: 16, height: 16, backgroundColor: '#faad14', marginRight: 4, borderRadius: 2 }}></div>
              <Text style={{ fontSize: 12 }}>Moyen (30-60min)</Text>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ width: 16, height: 16, backgroundColor: '#fa8c16', marginRight: 4, borderRadius: 2 }}></div>
              <Text style={{ fontSize: 12 }}>Élevé (60-120min)</Text>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ width: 16, height: 16, backgroundColor: '#f5222d', marginRight: 4, borderRadius: 2 }}></div>
              <Text style={{ fontSize: 12 }}>Critique (&gt;120min)</Text>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
});

ArretMTTRCalendar.displayName = 'ArretMTTRCalendar';

export default ArretMTTRCalendar;
