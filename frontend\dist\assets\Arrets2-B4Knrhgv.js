import{T as ft,R as e,r as n,E as $,G as Ut,a as k,n as Me,m as Xt,A as Qt,b as V,c as D,C as v,d as J,P as Jt,i as pt,g as Ye,f as N,B as Re,F as ht,aa as Zt,e as st,l as er,D as lt,al as tr}from"./index-N0wOiMt6.js";import{d as h}from"./dayjs.min-BHt7dFLo.js";import"./fr-DC3HkAP8.js";import{R as it,w as rr,a as ar,c as nr,D as or}from"./DownloadOutlined-C8TU0wLq.js";import{G as sr,i as lr}from"./GlobalSearchModal-BvgW-kQU.js";import{R as q,L as yt,C as te,X as re,Y as ae,T as W,a as gt,b as ir,c as cr,d as xe,P as mr,e as dr,f as ur,B as ve,g as we}from"./PieChart-BZME-zsX.js";import{P as fr,D as pr,a as hr,M as yr}from"./performance-metrics-gauge-DkZB7Ct2.js";import{F as gr,S as Er}from"./SearchResultsDisplay-D6-PBd55.js";import{R as ct}from"./AlertOutlined-8CxkZMww.js";import{R as mt}from"./WarningOutlined-5IE-NKcf.js";import{R as Se}from"./ClockCircleOutlined-C6SZLNSK.js";import{R as br}from"./ToolOutlined-C50QNs7D.js";import{S as _e}from"./index-BP6n0Cjb.js";import{R as Dr}from"./InfoCircleOutlined-DImdGCrM.js";import{P as Ce}from"./progress-CyD0QBQj.js";import{R as Mr}from"./SearchOutlined-xbMlVLbw.js";import{R as Yr}from"./CalendarOutlined-BxlyaoqS.js";import{R as Sr}from"./LineChartOutlined-2kYZXigx.js";import{R as _r}from"./PieChartOutlined-BTCQURIB.js";import{R as Cr}from"./BarChartOutlined-DzqCoGDG.js";import{R as dt}from"./DashboardOutlined-CBEyWGTp.js";import{R as kr}from"./FilterOutlined-DjPdBmQR.js";import{A as Rr,a as xr}from"./ComposedChart-Bx4Th9mw.js";import"./index-C2CgWKoY.js";import"./FileTextOutlined-BAhSEapg.js";import"./ExperimentOutlined-J9m82uMz.js";import"./index-Dea_-S4D.js";import"./ThunderboltOutlined-Dz7LyqJg.js";import"./index-BCtPda2K.js";import"./ReloadOutlined-DZn6IdM2.js";import"./EyeOutlined-BNZGoZWA.js";const{Text:ut}=ft,Z={primary:"#1890ff",success:"#52c41a"},ee=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96","#fa8c16","#a0d911"],vr=({data:E=[],selectedMachine:p="",selectedDate:d=null,dateRangeType:M="day",targetValue:t=85,loading:o=!1})=>{const a=c=>{if(!c)return"";const l=h(c);if(!l.isValid())return c;switch(M){case"day":return l.format("HH:mm");case"week":return l.format("ddd DD");case"month":return l.format("DD/MM");default:return l.format("DD/MM/YYYY")}},m=n.useMemo(()=>{if(!d)return null;let c,l;switch(M){case"day":c=d.startOf("day").format("YYYY-MM-DD HH:mm"),l=d.endOf("day").format("YYYY-MM-DD HH:mm");break;case"week":c=d.startOf("isoWeek").format("YYYY-MM-DD"),l=d.endOf("isoWeek").format("YYYY-MM-DD");break;case"month":c=d.startOf("month").format("YYYY-MM-DD"),l=d.endOf("month").format("YYYY-MM-DD");break;default:return null}return{start:c,end:l}},[d,M]),u=({active:c,payload:l,label:C})=>!c||!l||!l.length?null:e.createElement("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}},e.createElement(ut,{strong:!0},a(C)),l.map((Y,ne)=>{let w=parseFloat(Y.value);return!isNaN(w)&&w>0&&w<1&&(w=w*100),e.createElement("div",{key:ne,style:{color:Y.color}},e.createElement(ut,null,Y.name,": ",isNaN(w)?"N/A":`${w.toFixed(1)}%`))}));if(!E||E.length===0)return e.createElement($,{description:"Aucune donnée disponible pour la tendance de disponibilité"});const b=e.useMemo(()=>E.map(c=>{const l={...c};return Object.keys(l).forEach(C=>{if((C==="disponibilite"||C.startsWith("disponibilite_"))&&l[C]!==void 0&&l[C]!==null){const Y=Number(l[C]);!isNaN(Y)&&Y>0&&Y<1&&(l[C]=Y*100)}}),l}),[E]);return e.createElement(q,{width:"100%",height:350},e.createElement(yt,{data:b,margin:{top:20,right:30,left:20,bottom:10}},e.createElement(te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(re,{dataKey:"date",tickFormatter:a,tick:{fill:"#666"},label:{value:"Période",position:"insideBottomRight",offset:-5,style:{fill:"#666"}}}),e.createElement(ae,{domain:[0,100],tickFormatter:c=>`${c}%`,label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.createElement(W,{content:e.createElement(u,null)}),e.createElement(gt,null),e.createElement(ir,{y:t,stroke:Z.success,strokeDasharray:"3 3",label:{value:`Objectif: ${t}%`,position:"right",fill:Z.success,fontSize:12}}),m&&e.createElement(cr,{x1:m.start,x2:m.end,fill:Z.primary,fillOpacity:.1}),p?e.createElement(xe,{type:"monotone",dataKey:"disponibilite",name:`Disponibilité ${p}`,stroke:Z.primary,strokeWidth:3,dot:{fill:Z.primary,strokeWidth:2,r:4},activeDot:{r:6,fill:"#fff",stroke:Z.primary,strokeWidth:2},isAnimationActive:!o}):E[0]&&Object.keys(E[0]).filter(c=>c!=="date"&&c.startsWith("disponibilite_")).map((c,l)=>{const C=c.replace("disponibilite_","");return e.createElement(xe,{key:c,type:"monotone",dataKey:c,name:`Disponibilité ${C}`,stroke:ee[l%ee.length],strokeWidth:2,dot:{fill:ee[l%ee.length],strokeWidth:2,r:3},activeDot:{r:5,fill:"#fff",stroke:ee[l%ee.length],strokeWidth:2},isAnimationActive:!o})})))};e.memo(vr);h.extend(rr);h.extend(lr);h.extend(ar);h.extend(nr);h.locale("fr");const{useBreakpoint:Et}=Ut,{Title:wr,Text:R,Paragraph:ke}=ft,{TabPane:de}=pt,{Option:ya}=tr,{RangePicker:ga}=or,I=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96"],y={primary:"#1890ff",secondary:"#13c2c2",success:"#52c41a",warning:"#faad14",danger:"#f5222d",purple:"#722ed1",pink:"#eb2f96",orange:"#fa8c16",cyan:"#13c2c2",lime:"#a0d911"},Nr=n.memo(({data:E})=>e.createElement(q,{width:"100%",height:300},e.createElement(yt,{data:E,margin:{top:16,right:24,left:24,bottom:16}},e.createElement(te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.createElement(re,{dataKey:"Stop_Date",tick:{fill:"#666"},tickFormatter:p=>h(p).format("DD/MM"),label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),e.createElement(ae,{label:{value:"Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.createElement(W,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:p=>[`${p} arrêts`,"Total"]}),e.createElement(xe,{type:"monotone",dataKey:"Total_Stops",stroke:y.primary,strokeWidth:2,dot:{fill:y.primary,strokeWidth:2},activeDot:{r:6,fill:"#fff",stroke:y.primary,strokeWidth:2}})))),Tr=n.memo(({data:E})=>e.createElement(q,{width:"100%",height:300},e.createElement(mr,{margin:{top:16,right:24,left:24,bottom:16}},e.createElement(dr,{data:E,dataKey:"count",nameKey:"stopName",cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:5,labelLine:!0,label:({name:p,percent:d})=>`${(d*100).toFixed(0)}%`},E.map((p,d)=>e.createElement(ur,{key:`cell-${d}`,fill:I[d%I.length],stroke:"#fff",strokeWidth:2}))),e.createElement(W,{formatter:(p,d,M)=>[`${p} arrêts`,M.payload.stopName],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.createElement(gt,{layout:"horizontal",verticalAlign:"bottom",align:"center",wrapperStyle:{paddingTop:20,fontSize:12,color:"#666"},formatter:(p,d,M)=>e.createElement("span",{style:{color:I[M%I.length],fontWeight:"bold"}},p)})))),Ar=n.memo(({data:E})=>{const p=Et(),d=t=>t?t.toLowerCase().includes("non déclaré")?"error":t.toLowerCase().includes("maintenance")?"warning":t.toLowerCase().includes("changement")?"processing":t.toLowerCase().includes("réglage")?"cyan":t.toLowerCase().includes("problème")?"orange":"default":"default",M=E.map(t=>{let o=t.Date_Insert,a=t.Debut_Stop,m=t.Fin_Stop_Time;const u=b=>{if(!b)return null;const c=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const C of c)if(h(b,C).isValid())return b;return h(b).isValid()?b:null};return o=u(o),a=u(a),m=u(m),{...t,Date_Insert:o,Machine_Name:t.Machine_Name||"N/A",Part_No:t.Part_NO||"N/A",Code_Stop:t.Code_Stop||"N/A",Debut_Stop:a,Fin_Stop_Time:m,Regleur_Prenom:t.Regleur_Prenom||"Non assigné",duration_minutes:t.duration_minutes||null}});return e.createElement(ht,{columns:[{title:"Date",dataIndex:"Date_Insert",key:"Date_Insert",render:t=>{if(!t)return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const o=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let a=null;for(const m of o){const u=h(t,m);if(u.isValid()){a=u;break}}return a||(a=h(t)),a&&a.isValid()?a.format("DD/MM/YYYY"):e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}},sorter:(t,o)=>{try{if(!t.Date_Insert||!o.Date_Insert)return 0;const a=b=>{if(!b)return null;const c=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const C of c){const Y=h(b,C);if(Y.isValid())return Y}const l=h(b);return l.isValid()?l:null},m=a(t.Date_Insert),u=a(o.Date_Insert);return!m||!u||!m.isValid()||!u.isValid()?0:m.unix()-u.unix()}catch{return 0}}},{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:t=>e.createElement(N,{color:"blue"},t||"N/A"),filters:[...new Set(M.map(t=>t.Machine_Name).filter(Boolean))].map(t=>({text:t,value:t})),onFilter:(t,o)=>o.Machine_Name===t},{title:"OF",dataIndex:"Part_No",key:"Part_No",render:t=>t&&t!=="N/A"?t:e.createElement(R,{type:"secondary"},"Non spécifié"),responsive:["md"]},{title:"Code Arrêt",dataIndex:"Code_Stop",key:"Code_Stop",render:t=>e.createElement(Re,{status:d(t),text:t||"N/A"}),filters:[...new Set(M.map(t=>t.Code_Stop).filter(Boolean))].map(t=>({text:t,value:t})),onFilter:(t,o)=>o.Code_Stop===t},{title:"Début",dataIndex:"Debut_Stop",key:"Debut_Stop",render:t=>{if(!t)return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const o=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let a=null;for(const m of o){const u=h(t,m);if(u.isValid()){a=u;break}}return a||(a=h(t)),a&&a.isValid()?a.format("HH:mm"):e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}}},{title:"Fin",dataIndex:"Fin_Stop_Time",key:"Fin_Stop_Time",render:t=>{if(!t)return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible");try{const o=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let a=null;for(const m of o){const u=h(t,m);if(u.isValid()){a=u;break}}return a||(a=h(t)),a&&a.isValid()?a.format("HH:mm"):e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}catch{return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non disponible")}}},{title:"Durée",key:"duration",render:(t,o)=>{if(o.duration_minutes!==null&&o.duration_minutes!==void 0)return`${o.duration_minutes} min`;if(!o.Debut_Stop||!o.Fin_Stop_Time)return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable");try{const a=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let m=null,u=null;for(const c of a){const l=h(o.Debut_Stop,c);if(l.isValid()){m=l;break}}m||(m=h(o.Debut_Stop));for(const c of a){const l=h(o.Fin_Stop_Time,c);if(l.isValid()){u=l;break}}if(u||(u=h(o.Fin_Stop_Time)),!m.isValid()||!u.isValid())return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable");const b=u.diff(m,"minute");return b<0?e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable"):`${b} min`}catch{return e.createElement(R,{type:"secondary",style:{fontStyle:"italic"}},"Non calculable")}},sorter:(t,o)=>{if(t.duration_minutes!==null&&t.duration_minutes!==void 0&&o.duration_minutes!==null&&o.duration_minutes!==void 0)return t.duration_minutes-o.duration_minutes;try{if(!t.Debut_Stop||!t.Fin_Stop_Time||!o.Debut_Stop||!o.Fin_Stop_Time)return 0;const a=Y=>{if(!Y)return null;const ne=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const Ee of ne){const oe=h(Y,Ee);if(oe.isValid())return oe}const w=h(Y);return w.isValid()?w:null},m=a(t.Debut_Stop),u=a(t.Fin_Stop_Time),b=a(o.Debut_Stop),c=a(o.Fin_Stop_Time);if(!m||!u||!b||!c||!m.isValid()||!u.isValid()||!b.isValid()||!c.isValid())return 0;const l=u.diff(m,"minute"),C=c.diff(b,"minute");return l-C}catch{return 0}}},{title:"Responsable",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:t=>t||"Non assigné",filters:[...new Set(M.map(t=>t.Regleur_Prenom||"Non assigné").filter(Boolean))].map(t=>({text:t,value:t==="Non assigné"?null:t})),onFilter:(t,o)=>t?o.Regleur_Prenom===t:!o.Regleur_Prenom}],dataSource:M,pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:t=>`Total ${t} arrêts`},scroll:{x:p.md?void 0:1e3},bordered:!0,size:"middle",rowKey:(t,o)=>`${t.Date_Insert}-${o}`,rowClassName:t=>t.Code_Stop&&t.Code_Stop.toLowerCase().includes("non déclaré")?"highlight-row-error":""})}),Ir=n.memo(({data:E})=>{const p=E.map(d=>({machine:d.Machine_Name||d.machine||"N/A",stops:d.stops||0,totalDuration:d.totalDuration||0}));return e.createElement("div",{style:{width:"100%",height:300}},e.createElement(V,{gutter:[0,16]},e.createElement(D,{span:24},e.createElement(q,{width:"100%",height:140},e.createElement(ve,{data:p,margin:{top:5,right:24,left:24,bottom:5}},e.createElement(te,{strokeDasharray:"3 3"}),e.createElement(re,{dataKey:"machine"}),e.createElement(ae,{label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{fontSize:12}}}),e.createElement(W,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:d=>[`${d} arrêts`,"Nombre d'arrêts"]}),e.createElement(we,{dataKey:"stops",fill:y.primary,name:"Nombre d'arrêts",radius:[4,4,0,0]})))),e.createElement(D,{span:24},e.createElement(q,{width:"100%",height:140},e.createElement(ve,{data:p,margin:{top:5,right:24,left:24,bottom:5}},e.createElement(te,{strokeDasharray:"3 3"}),e.createElement(re,{dataKey:"machine"}),e.createElement(ae,{label:{value:"Durée (min)",angle:-90,position:"insideLeft",style:{fontSize:12}}}),e.createElement(W,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:d=>[`${d} minutes`,"Durée totale"]}),e.createElement(we,{dataKey:"totalDuration",fill:y.secondary,name:"Durée totale (min)",radius:[4,4,0,0]}))))))}),Pr=n.memo(({data:E})=>e.createElement(q,{width:"100%",height:300},e.createElement(Rr,{data:E,margin:{top:16,right:24,left:24,bottom:16}},e.createElement(te,{strokeDasharray:"3 3"}),e.createElement(re,{dataKey:"hour",label:{value:"Heure de la journée",position:"bottom",offset:0}}),e.createElement(ae,{label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft"}}),e.createElement(W,{formatter:p=>[`${p.toFixed(1)} min`,"Durée moyenne"],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.createElement(xr,{type:"monotone",dataKey:"avgDuration",stroke:y.success,fill:`${y.success}33`})))),Hr=n.memo(({data:E})=>e.createElement(q,{width:"100%",height:300},e.createElement(ve,{data:E.sort((p,d)=>d.count-p.count),layout:"vertical",margin:{top:16,right:24,left:24,bottom:16}},e.createElement(te,{strokeDasharray:"3 3"}),e.createElement(re,{type:"number"}),e.createElement(ae,{type:"category",dataKey:"reason",width:120,tick:{fontSize:12}}),e.createElement(W,{formatter:p=>[`${p} occurrences`,"Fréquence"],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.createElement(we,{dataKey:"count",name:"Fréquence",fill:y.purple,barSize:20,radius:[0,4,4,0]})))),Ea=()=>{const[E,p]=n.useState([]),[d,M]=n.useState([]),[t,o]=n.useState(""),[a,m]=n.useState(""),[u,b]=n.useState(null),[c,l]=n.useState(""),[C,Y]=n.useState(!1),[ne,w]=n.useState(!1),[Ee,oe]=n.useState([]),[S,bt]=n.useState("day"),[f,Ne]=n.useState(null),[K,se]=n.useState(""),[z,ue]=n.useState(!1),[$r,Dt]=n.useState([]),[Mt,Te]=n.useState([]),[Ae,Ie]=n.useState([]),[Yt,Pe]=n.useState([]),[He,$e]=n.useState([]),[Fe,Oe]=n.useState([]),[fe,Le]=n.useState([]),[le,St]=n.useState([]),[Ve,ze]=n.useState([]),[P,pe]=n.useState(!0),[Be,G]=n.useState(null),[qe,We]=n.useState([]),[Ke,Ge]=n.useState([]),[he,je]=n.useState([]),[_t,Ct]=n.useState(0),[F,kt]=n.useState(0),[Ue,Xe]=n.useState([]),[Rt,xt]=n.useState(0),[vt,wt]=n.useState(0),[Nt,Tt]=n.useState(0),[At,Qe]=n.useState(!1),[Fr,It]=n.useState("1"),j=n.useRef(!0),ie=n.useRef(!1),T=n.useRef(!1),Je=Et(),_=(()=>{if(typeof window<"u"){const r=window.location.origin;return r.includes("ngrok-free.app")||r.includes("ngrok.io")?r:"http://localhost:5000"}return"http://localhost:5000"})(),ye=n.useCallback((r,i)=>{if(!r)return{short:"",full:""};const g=h(r);if(i==="day")return{short:g.format("DD/MM/YYYY"),full:`le ${g.format("DD MMMM YYYY")}`};if(i==="week"){const s=g.startOf("isoWeek"),A=g.endOf("isoWeek");return{short:`S${g.isoWeek()} ${g.format("YYYY")}`,full:`du ${s.format("DD MMMM")} au ${A.format("DD MMMM YYYY")}`}}else if(i==="month")return{short:g.format("MMMM YYYY"),full:`${g.format("MMMM YYYY")}`};return{short:"",full:""}},[]);n.useCallback(()=>{const r=new URLSearchParams;return t&&!a?r.append("model",t):a&&r.append("machine",a),f&&r.append("dateRangeType",S),r.toString()?`?${r.toString()}`:""},[t,a,f,S]),n.useCallback(async()=>{try{console.log("Fetching machine data...");const[r,i]=await Promise.all([k.get("/api/stops/machine-models").retry(2).withCredentials(),k.get("/api/stops/machine-names").retry(2).withCredentials()]);if(r.body&&r.body.length>0){const g=r.body.map(s=>s.model||s);console.log("Machine models fetched:",g),p(g)}else console.log("No machine models returned from API, using defaults"),p(["IPS","CCM24"]);i.body&&i.body.length>0?(console.log("Machine names fetched:",i.body),M(i.body),i.body.find(s=>s.Machine_Name==="IPS01")&&r.body.length>0&&r.body.find(A=>(A.model||A)==="IPS")&&o("IPS")):(console.log("No machine names returned from API, using defaults"),M([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}catch(r){console.error("Error loading machine data:",r),G("Erreur lors du chargement des données machines. Veuillez réessayer."),p(["IPS","CCM24"]),M([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}},[]),n.useEffect(()=>{if(t){const r=d.filter(i=>i.Machine_Name&&i.Machine_Name.startsWith(t));oe(r),a&&!r.some(i=>i.Machine_Name===a)&&m(""),T.current=!0}else oe([]),m(""),T.current=!0},[t,d,a]);const ce=n.useCallback(async()=>{var i,g;const r=setTimeout(()=>{j.current&&(pe(!1),ie.current=!1,console.warn("Data fetch timeout - forcing loading state to false"),Me.warning("Le chargement des données a pris trop de temps. Veuillez réessayer."))},15e3);if(ie.current){console.log("Fetch already in progress, skipping...");return}ie.current=!0,pe(!0),G(null);try{const s={};t&&!a?s.model=t:a&&(s.machine=a),f&&(S==="day"?(s.startDate=f.format("YYYY-MM-DD"),s.endDate=f.clone().add(1,"day").format("YYYY-MM-DD")):S==="week"?(s.startDate=f.clone().startOf("isoWeek").format("YYYY-MM-DD"),s.endDate=f.clone().endOf("isoWeek").format("YYYY-MM-DD")):S==="month"&&(s.startDate=f.clone().startOf("month").format("YYYY-MM-DD"),s.endDate=f.clone().endOf("month").format("YYYY-MM-DD")),s.dateRangeType=S),console.log("Fetching data with params:",s);const[A,U,be,O,L,X,me,Q,Wt,Kt,rt,at,De,nt]=await Promise.all([k.get(`${_}/api/unique-dates`).query(s).retry(2).withCredentials(),k.get(`${_}/api/sidecards-arret`).query(s).retry(2).withCredentials(),k.get(`${_}/api/sidecards-arretnonDeclare`).query(s).retry(2).withCredentials(),k.get(`${_}/api/top-5-stops`).query(s).retry(2).withCredentials(),k.get(`${_}/api/arrets-by-range`).query(s).retry(2).withCredentials(),k.get(`${_}/api/arrets-table-range/${f?f.format("YYYY-MM-DD"):""}`).query(s).retry(2).withCredentials(),k.get(`${_}/api/stop-duration-trend/${f?f.format("YYYY-MM-DD"):""}`).query(s).retry(2).withCredentials(),k.get(`${_}/api/machine-stop-comparison/${f?f.format("YYYY-MM-DD"):""}`).query(s).retry(2).withCredentials(),k.get(`${_}/api/operator-stop-stats/${f?f.format("YYYY-MM-DD"):""}`).query(s).retry(2).withCredentials(),k.get(`${_}/api/stop-reasons/${f?f.format("YYYY-MM-DD"):""}`).query(s).retry(2).withCredentials(),k.get(`${_}/api/disponibilite-trend`).query(s).retry(2).withCredentials(),k.get(`${_}/api/downtime-pareto`).query(s).retry(2).withCredentials(),k.get(`${_}/api/disponibilite-by-machine`).query(s).retry(2).withCredentials(),k.get(`${_}/api/mttr-calendar`).query(s).retry(2).withCredentials()]);if(!j.current)return;Le(X.body);const ge=me.body?me.body.map(x=>({...x,hour:Number.parseInt(x.hour),avgDuration:Number.parseFloat(x.avgDuration)||0})).sort((x,B)=>x.hour-B.hour):[];We(ge);const Gt=Array.isArray(Q.body)?Q.body.reduce((x,B)=>{const ot=parseFloat(B.totalDuration);return x+(isNaN(ot)?0:ot)},0):0,jt=ge.length?ge.reduce((x,B)=>x+B.avgDuration,0)/ge.length:0;if(Ge(Q.body||[]),je(Wt.body||[]),Ct(Gt),kt(jt),Xe(Kt.body||[]),Dt(A.body),St([{title:"Arrêts Totaux",value:((i=U.body[0])==null?void 0:i.Arret_Totale)||0,icon:e.createElement(ct,null),color:y.primary},{title:"Arrêts Non Déclarés",value:((g=be.body[0])==null?void 0:g.Arret_Totale_nondeclare)||0,icon:e.createElement(mt,null),color:y.danger}]),ze(O.body),Oe(L.body.map(x=>({Stop_Date:h(x.Stop_Date).format("YYYY-MM-DD"),Total_Stops:x.Total_Stops})).sort((x,B)=>h(x.Stop_Date).unix()-h(B.Stop_Date).unix())),a?Pt(X.body):Qe(!1),f){const{full:x}=ye(f,S);se(x),ue(!0)}else se(""),ue(!1);T.current=!1,Me.success("Données mises à jour avec succès"),Te(Array.isArray(rt.data)?rt.data:[]),Ie(Array.isArray(at.data)?at.data:[]),Pe(Array.isArray(De.data)?De.data:[]),$e(Array.isArray(nt.data)?nt.data:[]),console.log("Params sent to disponibiliteTrend:",s),console.log("disponibiliteTrendData fetched successfully",Mt)+console.log("disponibiliteByMachineData fetched successfully",De.data),console.log("mttrCalendarData fetched successfully",He),console.log("downtimeParetoData fetched successfully",Ae)}catch(s){if(console.error("Erreur lors du chargement des données:",s),!j.current)return;s.message==="Network Error"?G("Erreur de connexion au serveur. Veuillez vérifier que le serveur API est en cours d'exécution."):G(`Erreur lors du chargement des données: ${s.message}`),Le([]),Oe([]),We([]),Ge([]),je([]),ze([]),Xe([]),Te([]),Ie([]),Pe([]),$e([]),Me.error("Erreur lors du chargement des données")}finally{clearTimeout(r),j.current&&pe(!1),ie.current=!1}},[t,a,f,S,_,ye]),Pt=n.useCallback(r=>{const i=r.reduce((O,L)=>{try{if(!L.Debut_Stop||!L.Fin_Stop_Time||L.Debut_Stop.includes("Invalid")||L.Fin_Stop_Time.includes("Invalid"))return O;const X=h(L.Debut_Stop,"DD/MM/YYYY HH:mm"),me=h(L.Fin_Stop_Time,"DD/MM/YYYY HH:mm");if(!X.isValid()||!me.isValid())return O;const Q=me.diff(X,"minute");return Q>0?O+Q:O}catch{return O}},0),g=r.length;let s=24*60;f&&(S==="day"?s=24*60:S==="week"?s=7*24*60:S==="month"&&(s=f.daysInMonth()*24*60));const A=g>0?i/g:0,U=g>0?(s-i)/g:0,be=U+A>0?U/(U+A)*100:0;xt(A),wt(U),Tt(be),Qe(!0)},[f,S]),Ze=n.useCallback(async()=>{try{console.log("Fetching machine models...");const r=await k.get(`${_}/api/stops/machine-models`).retry(2).withCredentials();if(r.body&&r.body.length>0){const i=r.body.map(g=>g.model||g);console.log("Machine models fetched:",i),p(i)}else console.log("No machine models returned from API, using defaults"),p(["IPS","CCM24"])}catch(r){console.error("Error loading machine models:",r),G("Erreur lors du chargement des modèles de machines. Veuillez réessayer."),p(["IPS","CCM24"])}},[_]),et=n.useCallback(async()=>{try{console.log("Fetching machine names...");const r=await k.get(`${_}/api/stops/machine-names`).retry(2).withCredentials();r.body&&r.body.length>0?(console.log("Machine names fetched:",r.body),M(r.body),r.body.find(g=>g.Machine_Name==="IPS01")&&!t&&o("IPS")):(console.log("No machine names returned from API, using defaults"),M([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}catch(r){console.error("Error loading machine names:",r),G("Erreur lors du chargement des noms de machines. Veuillez réessayer."),M([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}},[_,t]);n.useEffect(()=>{Ze()},[Ze]),n.useEffect(()=>{et()},[et]),n.useEffect(()=>(j.current=!0,ce(),()=>{j.current=!1,pe(!1),ie.current=!1}),[ce]),n.useEffect(()=>{T.current&&ce()},[t,a,f,S,ce]);const Ht=r=>{if(bt(r),T.current=!0,f){const{full:i}=ye(f,r);se(i)}},$t=r=>{if(!r){tt();return}Ne(r);const{full:i}=ye(r,S);se(i),ue(!0),T.current=!0},tt=()=>{Ne(null),se(""),ue(!1),T.current=!0},Ft=r=>{r!==t&&(o(r),m(""),T.current=!0)},Ot=r=>{r!==a&&(m(r),T.current=!0)},Lt=()=>{o(""),m(""),T.current=!0},Vt=()=>{ce()},zt=n.useCallback((r,i)=>{b(r),l(i),Y(!!r)},[]),Bt=n.useCallback(r=>{console.log("Global search result selected:",r),w(!1),r.type==="machine-stop"&&console.log("Machine stop selected:",r.data)},[]);n.useCallback(()=>{b(null),l(""),Y(!1)},[]);const H=le.length>=2&&le[0].value>0?(le[1].value/le[0].value*100).toFixed(1):0,qt=[...le,{title:"Durée Totale",value:Math.round(_t),suffix:"min",icon:e.createElement(Se,null),color:y.secondary},{title:"Durée Moyenne",value:F.toFixed(1),suffix:"min",icon:e.createElement(Se,null),color:y.success},{title:"Interventions",value:he.reduce((r,i)=>r+i.interventions,0),icon:e.createElement(br,null),color:y.purple}];return n.useEffect(()=>{console.log("Loading state changed:",P)},[P]),e.createElement("div",{style:{padding:Je.md?24:16}},e.createElement(Xt,{spinning:P,tip:"Chargement des données...",size:"large"},Be&&e.createElement(Qt,{message:"Erreur",description:Be,type:"error",showIcon:!0,closable:!0,style:{marginBottom:16}}),e.createElement(V,{gutter:[24,24]},e.createElement(D,{span:24},e.createElement(v,{bordered:!1,bodyStyle:{padding:Je.md?24:16}},e.createElement(V,{gutter:[24,24],align:"middle"},e.createElement(D,{xs:24,md:12},e.createElement(wr,{level:3,style:{marginBottom:8}},e.createElement(ct,{style:{marginRight:12,color:y.danger}}),"Analyse des Arrêts de Production"),e.createElement(ke,{type:"secondary"},"Visualisation et analyse des arrêts machines pour optimiser la production")),e.createElement(D,{xs:24,md:12},e.createElement(gr,{selectedMachineModel:t,selectedMachine:a,machineModels:E.map(r=>typeof r=="object"?r.model:r),filteredMachineNames:Ee,dateRangeType:S,dateFilter:f,dateFilterActive:z,handleMachineModelChange:Ft,handleMachineChange:Ot,handleDateRangeTypeChange:Ht,handleDateChange:$t,resetFilters:()=>{Lt(),tt()},handleRefresh:Vt,loading:P,dataSize:fe.length,pageType:"arrets",onSearchResults:zt,enableElasticsearch:!0}))))),qt.map((r,i)=>e.createElement(D,{key:i,xs:24,sm:12,md:8,lg:6},e.createElement(v,{bordered:!1,hoverable:!0,style:{borderTop:`2px solid ${r.color||I[i%I.length]}`,height:"100%"}},e.createElement(_e,{title:e.createElement(J,null,r.icon&&e.cloneElement(r.icon,{style:{color:r.color||I[i%I.length]}}),e.createElement("span",null,r.title),r.title==="Arrêts Totaux"&&z&&e.createElement(Jt,{content:`Nombre total d'arrêts ${K}`,title:"Période sélectionnée"},e.createElement(Dr,{style:{color:y.primary,cursor:"pointer"}}))),value:r.value,suffix:r.suffix,valueStyle:{fontSize:24,color:r.color||I[i%I.length]}}),r.title==="Arrêts Non Déclarés"&&e.createElement("div",{style:{marginTop:8}},e.createElement(R,{type:"secondary"},H,"% du total"),e.createElement(Ce,{percent:Number.parseFloat(H),showInfo:!1,strokeColor:y.danger,size:"small"}))))),At&&e.createElement(e.Fragment,null),e.createElement(D,{span:24},e.createElement(v,{bordered:!1},e.createElement(pt,{defaultActiveKey:"1",onChange:It,tabBarExtraContent:e.createElement(J,null,e.createElement(Ye,{type:"link",icon:e.createElement(Mr,null),onClick:()=>w(!0)},"Recherche globale"),z&&e.createElement(N,{color:"blue"},e.createElement(Yr,{style:{marginRight:4}}),K),e.createElement(Ye,{type:"link",icon:e.createElement(it,null),disabled:!0},"Exporter"))},e.createElement(de,{tab:e.createElement("span",null,e.createElement(Sr,null),"Tendances"),key:"1"},e.createElement(V,{gutter:[24,24]},e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Évolution des Arrêts",bordered:!1},Fe.length>0?e.createElement(Nr,{data:Fe}):e.createElement($,{description:"Aucune donnée disponible"}))),e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Tendance de la Durée des Arrêts",bordered:!1},qe.length>0?e.createElement(Pr,{data:qe}):e.createElement($,{description:"Aucune donnée disponible"}))))),e.createElement(de,{tab:e.createElement("span",null,e.createElement(_r,null),"Répartition"),key:"2"},e.createElement(V,{gutter:[24,24]},e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Top 5 des Causes d'Arrêt",bordered:!1,extra:e.createElement(N,{color:"purple"},"Global")},Ve.length>0?e.createElement(Tr,{data:Ve}):e.createElement($,{description:"Aucune donnée disponible"}))),e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Causes d'Arrêt",bordered:!1,extra:e.createElement(N,{color:"cyan"},z?K:"Toutes les données")},Ue.length>0?e.createElement(Hr,{data:Ue}):e.createElement($,{description:"Aucune donnée disponible"}))))),e.createElement(de,{tab:e.createElement("span",null,e.createElement(Cr,null),"Comparaisons"),key:"3"},e.createElement(V,{gutter:[24,24]},e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Comparaison par Machine",bordered:!1,extra:e.createElement(N,{color:"orange"},z?K:"Toutes les données")},Ke.length>0?e.createElement(Ir,{data:Ke}):e.createElement($,{description:"Aucune donnée disponible"}))),e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Interventions par Opérateur",bordered:!1,extra:e.createElement(Re,{count:he.length,style:{backgroundColor:y.purple}})},he.length>0?e.createElement(ht,{dataSource:he,columns:[{title:"Opérateur",dataIndex:"operator",render:r=>e.createElement(J,null,e.createElement(Zt,{style:{color:y.purple}}),r||"Non assigné")},{title:"Interventions",dataIndex:"interventions",render:r=>e.createElement(N,{color:"purple"},r),sorter:(r,i)=>r.interventions-i.interventions,defaultSortOrder:"descend"}],pagination:!1,size:"middle",rowKey:"operator"}):e.createElement($,{description:"Aucune donnée disponible"}))))),e.createElement(de,{tab:e.createElement("span",null,e.createElement(dt,null),"Tableau de Bord"),key:"4"},e.createElement(v,{title:e.createElement(J,null,e.createElement(kr,null),e.createElement("span",null,"Détails des Arrêts ",z?`(${K})`:"(toutes les données)")),bordered:!1,extra:e.createElement(J,null,e.createElement(st,{title:"Nombre total d'arrêts"},e.createElement(Re,{count:fe.length,style:{backgroundColor:y.primary},overflowCount:999})),e.createElement(st,{title:"Exporter les données"},e.createElement(Ye,{type:"link",icon:e.createElement(it,null),disabled:!0},"Exporter")))},fe.length>0?e.createElement(e.Fragment,null,e.createElement("div",{style:{marginBottom:16}},e.createElement(R,{type:"secondary"},"Ce tableau présente tous les arrêts enregistrés pour la période sélectionnée. Vous pouvez filtrer par machine, code d'arrêt ou responsable, et trier par durée ou date.")),e.createElement(Ar,{data:fe}),e.createElement("style",{jsx:!0},`
                          .highlight-row-error {
                            background-color: rgba(245, 34, 45, 0.05);
                          }
                          .ant-table-row:hover {
                            cursor: pointer;
                            background-color: rgba(24, 144, 255, 0.05) !important;
                          }
                        `)):e.createElement($,{description:`Aucun arrêt enregistré pour cette ${S==="day"?"journée":S==="week"?"semaine":"période"}`,image:$.PRESENTED_IMAGE_SIMPLE}))),e.createElement(de,{tab:e.createElement("span",null,e.createElement(dt,null),"Performance"),key:"5"},e.createElement(V,{gutter:[24,24]},e.createElement(D,{span:24},e.createElement(v,{title:"Indicateurs de Performance",bordered:!1,extra:a?e.createElement(N,{color:"blue"},a):e.createElement(N,{color:"blue"},"Toutes les machines")},e.createElement(fr,{data:{disponibilite:Nt,mttr:Rt,mtbf:vt},selectedMachine:a,loading:P}))),e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Analyse Pareto des Arrêts",bordered:!1,extra:e.createElement(N,{color:"orange"},"Impact cumulé")},e.createElement(pr,{data:Ae,selectedMachine:a,selectedDate:f,dateRangeType:S,loading:P}))),e.createElement(D,{xs:24,lg:12},e.createElement(v,{title:"Disponibilité par Machine",bordered:!1,extra:t?e.createElement(N,{color:"purple"},t):null},e.createElement(hr,{data:Yt,selectedMachine:a,selectedMachineModel:t,loading:P}))),e.createElement(D,{xs:24,lg:24},e.createElement(v,{title:"Calendrier MTTR",bordered:!1,extra:e.createElement(N,{color:"cyan"},"Vue calendrier")},e.createElement(yr,{data:He,selectedMachine:a,selectedDate:f,dateRangeType:S,loading:P})))))))),e.createElement(D,{span:24},e.createElement(v,{title:e.createElement(J,null,e.createElement(er,null),e.createElement("span",null,"Résumé des Arrêts ",z?`(${K})`:"")),bordered:!1},e.createElement(V,{gutter:[24,24]},e.createElement(D,{xs:24,md:12},e.createElement(_e,{title:"Taux d'Arrêts Non Déclarés",value:H,suffix:"%",valueStyle:{color:y.danger},prefix:e.createElement(mt,null)}),e.createElement(Ce,{percent:Number.parseFloat(H),status:H>30?"exception":"normal",strokeColor:H>30?y.danger:H>15?y.warning:y.success}),e.createElement(lt,null),e.createElement(ke,null,e.createElement(R,{strong:!0},"Analyse: "),H>30?"Le taux d'arrêts non déclarés est très élevé. Une investigation est nécessaire pour améliorer le processus de déclaration.":H>15?"Le taux d'arrêts non déclarés est modéré. Des améliorations peuvent être apportées au processus de déclaration.":"Le taux d'arrêts non déclarés est faible, ce qui indique un bon suivi des procédures.")),e.createElement(D,{xs:24,md:12},e.createElement(_e,{title:"Durée Moyenne des Arrêts",value:parseFloat(F).toFixed(2),suffix:"min",valueStyle:{color:y.secondary},prefix:e.createElement(Se,null)}),e.createElement(Ce,{percent:Math.min(100,parseFloat(F)/60*100).toFixed(2),status:F>45?"exception":"normal",strokeColor:F>45?y.danger:F>20?y.warning:y.success}),e.createElement(lt,null),e.createElement(ke,null,e.createElement(R,{strong:!0},"Analyse: "),F>45?"La durée moyenne des arrêts est très élevée. Des actions correctives sont nécessaires pour réduire le temps d'intervention.":F>20?"La durée moyenne des arrêts est modérée. Des optimisations peuvent être envisagées pour réduire le temps d'intervention.":"La durée moyenne des arrêts est faible, ce qui indique une bonne réactivité des équipes d'intervention."))))))),C&&u&&e.createElement("div",{style:{marginTop:24}},e.createElement(Er,{results:u,searchQuery:c,pageType:"arrets",loading:P,onResultSelect:r=>{console.log("Arrets result selected:",r)},onPageChange:r=>{console.log("Page changed:",r)}})),e.createElement(sr,{visible:ne,onClose:()=>w(!1),onResultSelect:Bt}))};export{Ea as default};
