# Docker Compose for LOCQL Local Development - ngrok Integration
# Local build architecture: LOCQL Application (local build) + ngrok tunnel for external access
#
# 🎯 ARCHITECTURE:
# Internet → ngrok tunnel → LOCQL Application (local build, port 5000)
#
# ✅ BENEFITS:
# - Local development with live builds
# - External access through reserved ngrok domain
# - Fast iteration and testing
# - No dependency on Docker Hub images
# - Automatic HTTPS with ngrok
# - Reserved domain for consistent external access

services:

  # ============================================================================
  # 🗄️ DATA SERVICES
  # ============================================================================

  # Redis Cache Service
  redis:
    image: redis:7-alpine
    container_name: locql-redis-local
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - locql-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Elasticsearch Search & Analytics Service
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: locql-elasticsearch-local
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - bootstrap.memory_lock=true
      - cluster.name=locql-cluster
      - node.name=locql-node-1
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - locql-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # ============================================================================
  # 🚀 APPLICATION SERVICES
  # ============================================================================

  # LOCQL Unified Container (Local Build) - ngrok configured
  locql:
    build:
      context: .
      dockerfile: Dockerfile.unified
    container_name: locql-app-local
    ports:
      - "5000:5000"    # Direct access to LOCQL application
    environment:
      # Application Environment
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=5000
      - DOCKER_ENV=true
      - DUAL_ENV_SUPPORT=true

      # Database Configuration (Local MySQL)
      - DB_HOST=${DB_HOST:-host.docker.internal}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASS=${DB_PASS:-root}
      - DB_NAME=${DB_NAME:-Testingarea51}

      # Redis Configuration
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_DB=${REDIS_DB:-0}

      # Elasticsearch Configuration
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST:-elasticsearch}
      - ELASTICSEARCH_PORT=${ELASTICSEARCH_PORT:-9200}
      - ELASTICSEARCH_PROTOCOL=${ELASTICSEARCH_PROTOCOL:-http}
      - ELASTICSEARCH_INDEX_PREFIX=${ELASTICSEARCH_INDEX_PREFIX:-locql}

      # Authentication
      - JWT_SECRET=${JWT_SECRET:-dadfba2957b57678f0bdf5bb2f27b1938b9a0b472cd95a49a7f01397af5db005b9072f3d5b0a42268571b735187e2a7aedc532ae79797206fe5789718711d719}
      - JWT_EXPIRE=${JWT_EXPIRE:-8h}
      - JWT_COOKIE_EXPIRE=${JWT_COOKIE_EXPIRE:-1}

      # ngrok Configuration
      - NGROK_ENABLED=${NGROK_ENABLED:-true}
      - VITE_NGROK_ENABLED=${VITE_NGROK_ENABLED:-true}

      # Frontend Configuration (ngrok reserved domain)
      - FRONTEND_URL=${FRONTEND_URL:-https://eternal-friendly-chigger.ngrok-free.app}
      - VITE_API_URL=${VITE_API_URL:-https://eternal-friendly-chigger.ngrok-free.app}
      - VITE_WS_URL=${VITE_WS_URL:-wss://eternal-friendly-chigger.ngrok-free.app}

      # CORS Configuration (ngrok reserved domain)
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:5000,https://eternal-friendly-chigger.ngrok-free.app}
      - CORS_CREDENTIALS=${CORS_CREDENTIALS:-true}

      # WebSocket Configuration (ngrok reserved domain)
      - WS_EXTERNAL_URL=${WS_EXTERNAL_URL:-wss://eternal-friendly-chigger.ngrok-free.app}
      - HTTP_EXTERNAL_URL=${HTTP_EXTERNAL_URL:-https://eternal-friendly-chigger.ngrok-free.app}

      # Session Configuration (ngrok reserved domain)
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret-change-this-in-production}
      - SESSION_DOMAIN=${SESSION_DOMAIN:-eternal-friendly-chigger.ngrok-free.app}

      # Security Settings (Configured for ngrok HTTPS)
      - SECURE_COOKIES=${SECURE_COOKIES:-true}
      - SAME_SITE_COOKIES=${SAME_SITE_COOKIES:-lax}
      - TRUST_PROXY=${TRUST_PROXY:-true}

      # Logging and Debug (Development settings)
      - DEBUG=${DEBUG:-true}
      - LOG_LEVEL=${LOG_LEVEL:-debug}
      - AUTH_DEBUG=${AUTH_DEBUG:-true}
      - DISABLE_AUTH_CACHE=${DISABLE_AUTH_CACHE:-true}

      # Cache Configuration (Development settings)
      - DISABLE_CACHE=${DISABLE_CACHE:-true}

      # Email Configuration (Production SMTP)
      - SMTP_HOST=${SMTP_HOST:-sicaf.com.tn}
      - SMTP_PORT=${SMTP_PORT:-25}
      - SMTP_SECURE=${SMTP_SECURE:-false}
      - SMTP_USE_STARTTLS=${SMTP_USE_STARTTLS:-true}
      - SMTP_USER=${SMTP_USER:-<EMAIL>}
      - SMTP_PASS=${SMTP_PASS:-5vx7*Ok48}
      - SMTP_FROM_NAME=${SMTP_FROM_NAME:-LOCQL Performance System}
      - SMTP_FROM_EMAIL=${SMTP_FROM_EMAIL:-<EMAIL>}
      - FROM_NAME=${FROM_NAME:-LOCQL Performance System}
      - FROM_EMAIL=${FROM_EMAIL:-<EMAIL>}

    restart: unless-stopped

    # Service dependencies
    depends_on:
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy

    # Docker Desktop networking compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # Development resource limits (more generous)
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

    # Volume mounts for data persistence
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/backend/uploads
      - ./reports:/app/backend/reports

    # Network configuration
    networks:
      - locql-network

    # Security options
    security_opt:
      - no-new-privileges:true

  # ============================================================================
  # 🌐 TUNNEL SERVICE - External Internet Access (Reserved Domain)
  # ============================================================================

  # ngrok with reserved domain and authentication
  ngrok:
    image: ngrok/ngrok:latest
    container_name: locql-ngrok-tunnel-local
    restart: unless-stopped
    command: ["http", "--url=eternal-friendly-chigger.ngrok-free.app", "locql:5000"]
    ports:
      - "4040:4040"  # ngrok web interface
    environment:
      - NGROK_AUTHTOKEN=${NGROK_AUTHTOKEN}
    depends_on:
      - locql
    networks:
      - locql-network
    profiles:
      - ngrok

# ============================================================================
# 🌐 NETWORKS
# ============================================================================

networks:
  locql-network:
    driver: bridge
    name: locql-network-local

# ============================================================================
# 📊 VOLUMES
# ============================================================================

volumes:
  locql-logs:
    driver: local
  locql-uploads:
    driver: local
  locql-reports:
    driver: local
  redis-data:
    driver: local
  elasticsearch-data:
    driver: local

# ============================================================================
# 🚀 USAGE INSTRUCTIONS
# ============================================================================
#
# 1. Start LOCQL application only (local build):
#    docker-compose -f docker-compose.local-ngrok.yml up -d locql
#
# 2. Start with ngrok tunnel (reserved domain):
#    docker-compose -f docker-compose.local-ngrok.yml --profile ngrok up -d
#
# 3. Build and start:
#    docker-compose -f docker-compose.local-ngrok.yml --profile ngrok up --build -d
#
# 4. View logs:
#    docker-compose -f docker-compose.local-ngrok.yml logs -f
#
# 5. Stop services:
#    docker-compose -f docker-compose.local-ngrok.yml down
#
# 6. Access URLs:
#    - Local: http://localhost:5000
#    - External: https://eternal-friendly-chigger.ngrok-free.app
#    - ngrok Web Interface: http://localhost:4040
#
# 7. Development Features:
#    - Local builds for fast iteration
#    - Volume mounts for live code editing
#    - Debug logging enabled
#    - Generous resource limits
#    - Reserved ngrok domain for consistent external access
#
# ============================================================================
