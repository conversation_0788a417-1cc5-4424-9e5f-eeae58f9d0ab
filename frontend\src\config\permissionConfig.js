/**
 * Permission configuration for routes and UI elements
 *
 * This file centralizes all permission requirements for the application.
 * It maps routes and UI actions to the permissions required to access them.
 */

/**
 * Route permissions configuration
 * Maps route paths to required permissions, roles, or departments
 */
export const routePermissions = {
  // Dashboard routes
  '/home': {
    permissions: ['system:view_dashboard'],
    label: 'Tableau de Bord'
  },
  '/old': {
    permissions: ['system:view_dashboard'],
    label: 'Ancien Tableau de Bord'
  },

  // Production routes
  '/production': {
    permissions: ['production:view_production'],
    label: 'Production'
  },
  '/production-old': {
    permissions: ['production:view_production'],
    label: 'Ancienne Production'
  },

  // Stops routes
  '/arrets': {
    permissions: ['view_stops'],
    label: 'Arrêts'
  },
  '/arrets-dashboard': {
    permissions: ['view_stops'],
    label: 'Tableau de Bord des Arrêts'
  },

  // Analytics routes
  '/analytics': {
    permissions: ['view_analytics'],
    label: 'Analyses'
  },

  // Reports routes
  '/reports': {
    permissions: ['system:view_reports'],
    label: 'Rapports'
  },

  // Maintenance routes
  '/maintenance': {
    permissions: ['view_maintenance'],
    label: 'Maintenance'
  },

  // Notification routes
  '/notifications': {
    permissions: ['view_notifications'],
    label: 'Notifications'
  },



  // Admin routes
  '/admin': {
    roles: ['admin'],
    label: 'Administration'
  },
  '/admin/users': {
    permissions: ['manage_users'],
    roles: ['admin'],
    label: 'Gestion des Utilisateurs'
  },

  // User profile (accessible to all authenticated users)
  '/profile': {
    label: 'Mon Profil'
  }
};

/**
 * Action permissions configuration
 * Maps UI actions to required permissions
 */
export const actionPermissions = {
  // User management actions
  'create_user': {
    permissions: ['manage_users'],
    roles: ['admin']
  },
  'edit_user': {
    permissions: ['manage_users'],
    roles: ['admin']
  },
  'delete_user': {
    permissions: ['manage_users'],
    roles: ['admin']
  },

  // Production actions
  'edit_production': {
    permissions: ['production:manage_production']
  },
  'delete_production': {
    permissions: ['production:manage_production']
  },

  // Stop actions
  'add_stop': {
    permissions: ['add_stop']
  },
  'edit_stop': {
    permissions: ['edit_stop']
  },
  'delete_stop': {
    permissions: ['delete_stop']
  },

  // Report actions
  'create_report': {
    permissions: ['system:create_reports']
  },
  'edit_report': {
    permissions: ['system:edit_reports']
  },
  'delete_report': {
    permissions: ['system:delete_reports']
  },


};

/**
 * Navigation menu configuration
 * Maps menu items to required permissions and routes
 */
export const menuPermissions = {
  dashboard: {
    key: "dashboard",
    path: "/home",
    permissions: ['system:view_dashboard'],
    label: "Accueil"
  },
  production: {
    key: "production",
    path: "/production",
    permissions: ['production:view_production'],
    label: "Production"
  },
  stops: {
    key: "stops",
    path: "/arrets",
    permissions: ['view_stops'],
    label: "Arrêts"
  },
  analytics: {
    key: "analytics",
    path: "/analytics",
    permissions: ['view_analytics'],
    label: "Analyses"
  },
  reports: {
    key: "reports",
    path: "/reports",
    permissions: ['system:view_reports'],
    label: "Rapports"
  },
  maintenance: {
    key: "maintenance",
    path: "/maintenance",
    permissions: ['view_maintenance'],
    label: "Maintenance"
  },
  notifications: {
    key: "notifications",
    path: "/notifications",
    permissions: ['view_notifications'],
    label: "Notifications"
  },
  admin: {
    key: "admin",
    path: "/admin",
    roles: ['admin'],
    label: "Administration"
  },
  profile: {
    key: "profile",
    path: "/profile",
    label: "Mon Profil"
  }
};

export default {
  routePermissions,
  actionPermissions,
  menuPermissions
};
