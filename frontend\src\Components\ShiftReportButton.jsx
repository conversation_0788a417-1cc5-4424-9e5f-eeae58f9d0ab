import React, { useState } from "react";
import { Button, Modal, Select, message, Spin, Space, DatePicker } from "antd";
import { FilePdfOutlined, DownloadOutlined, CalendarOutlined } from "@ant-design/icons";
import request from "superagent";
import dayjs from "dayjs";

/**
 * Component for generating and downloading shift reports
 * @param {Object} props - Component props
 * @param {string} props.machineId - ID of the machine to generate report for
 * @param {string} props.machineName - Name of the machine
 * @param {string} props.shift - Current shift (optional)
 */
const ShiftReportButton = ({ machineId, machineName, shift }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedShift, setSelectedShift] = useState(shift || "Current");
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState(null);

  // Show modal to confirm report generation
  const showModal = () => {
    setIsModalVisible(true);
  };

  // Handle modal cancel
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // Generate shift report
  const generateReport = async () => {
    try {
      setLoading(true);
      console.log("Starting report generation with params:", {
        machineId,
        date: selectedDate.format("YYYY-MM-DD"),
        shift: selectedShift
      });

      // Set a safety timeout to prevent UI from being stuck in loading state
      const safetyTimeout = setTimeout(() => {
        if (loading) {
          console.log("Safety timeout triggered after 90 seconds");
          setLoading(false);
          message.error("La génération du rapport a pris trop de temps. Veuillez réessayer.");
        }
      }, 90000); // 90 seconds safety timeout

      // Add timeout to prevent infinite loading
      console.log("Sending API request to /api/shift-reports/generate");
      const response = await request.post('/api/shift-reports/generate').withCredentials()
        .send({
          machineId: machineId,
          date: selectedDate.format("YYYY-MM-DD"),
          shift: selectedShift,
        })
        .timeout(120000) // Increased timeout to 120 seconds
        .retry(2);

      console.log("Received response:", response.status, response.statusText);

      // Clear the safety timeout since request completed
      clearTimeout(safetyTimeout);

      if (response.body && response.body.success) {
        setReportData(response.body);
        message.success("Rapport généré avec succès");

        // Automatically open the PDF in a new tab
        if (response.body.filePath) {
          console.log("Auto-opening PDF in new tab:", response.data.filePath);
          window.open(response.data.filePath, '_blank');
        }
      } else {
        message.error("Erreur lors de la génération du rapport");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      console.log("Error details:", {
        code: error.code,
        message: error.message,
        response: error.response ? {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        } : 'No response',
        request: error.request ? 'Request exists' : 'No request'
      });

      // Provide more specific error messages
      if (error.code === 'ECONNABORTED') {
        message.error("La génération du rapport a pris trop de temps. Veuillez réessayer. Le serveur n'a pas répondu dans le délai imparti.");
      } else if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        message.error(
          `Erreur ${error.response.status}: ${error.response.data?.error || error.response.statusText || "Erreur inconnue"}`
        );
      } else if (error.request) {
        // The request was made but no response was received
        message.error("Aucune réponse du serveur. Vérifiez votre connexion réseau.");
      } else {
        // Something happened in setting up the request that triggered an Error
        message.error(`Erreur: ${error.message}`);
      }
    } finally {
      console.log("Request completed (success or error), resetting loading state");
      setLoading(false);
    }
  };

  // Download the generated report
  const downloadReport = () => {
    if (!reportData || !reportData.filePath) {
      message.error("Aucun rapport disponible pour téléchargement");
      return;
    }

    // Use downloadPath if available, otherwise fall back to filePath
    const downloadUrl = reportData.downloadPath || reportData.filePath;
    console.log("Downloading report from:", downloadUrl);

    // Use window.open to open the file in a new tab
    window.open(downloadUrl, '_blank');
  };

  return (
    <>
      <Button
        type="primary"
        icon={<FilePdfOutlined />}
        onClick={showModal}
        style={{ marginLeft: 8 }}
      >
        Rapport de Shift
      </Button>

      <Modal
        title="Générer un Rapport de Quart"
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <p>
          Générer un rapport de performance pour la machine{" "}
          <strong>{machineName}</strong> basé sur les données du quart sélectionné.
          Le rapport combine les données de la dernière ligne de la table machine_daily_table_mould
          et les données agrégées de la table machine_sessions pour la période de 8 heures du quart.
        </p>

        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <label style={{ marginRight: 8, width: 60 }}>Date:</label>
            <DatePicker
              value={selectedDate}
              onChange={(date) => setSelectedDate(date)}
              style={{ width: 200 }}
              format="DD/MM/YYYY"
              placeholder="Sélectionner une date"
              allowClear={false}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <label style={{ marginRight: 8, width: 60 }}>Quart:</label>
            <Select
              value={selectedShift}
              onChange={(value) => setSelectedShift(value)}
              style={{ width: 200 }}
            >
              <Select.Option value="Matin">Shift 1 (06:00 - 14:00)</Select.Option>
              <Select.Option value="Après-midi">
                Shift 2 (14:00 - 22:00)
              </Select.Option>
              <Select.Option value="Nuit">Shift 3 (22:00 - 06:00)</Select.Option>
              <Select.Option value="Current">Shift Actuel</Select.Option>
            </Select>
          </div>
        </div>

        {loading ? (
          <div style={{ textAlign: "center", padding: "20px 0" }}>
            <Spin size="large" />
            <p style={{ marginTop: 16 }}>Génération du rapport en cours...</p>
          </div>
        ) : reportData ? (
          <div style={{ textAlign: "center", padding: "20px 0" }}>
            <p style={{ color: "green", fontSize: 16 }}>
              Rapport généré avec succès! Le PDF a été ouvert dans un nouvel onglet.
            </p>
            <p style={{ fontSize: 14, marginBottom: 16 }}>
              Si le PDF ne s'est pas ouvert automatiquement, ou pour le télécharger, cliquez ci-dessous:
            </p>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={downloadReport}
            >
              Ouvrir/Télécharger le PDF
            </Button>
          </div>
        ) : (
          <div style={{ textAlign: "right" }}>
            <Space>
              <Button onClick={handleCancel}>Annuler</Button>
              <Button type="primary" onClick={generateReport}>
                Générer le Rapport
              </Button>
            </Space>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ShiftReportButton;