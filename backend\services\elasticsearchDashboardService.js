/**
 * Elasticsearch Dashboard Service
 * 
 * Provides optimized Elasticsearch queries for dashboard components
 * with proper aggregations and filtering capabilities.
 */

import { createRequire } from 'module';
import { checkElasticsearchHealth } from '../config/elasticsearch.js';
import elasticsearchService from './elasticsearchService.js';
const require = createRequire(import.meta.url);

class ElasticsearchDashboardService {
  constructor() {
    this.indexName = 'dashboard-machine-stops';
  }

  /**
   * Build date range filter for queries
   */
  buildDateRangeFilter(filters = {}) {
    const dateFilter = [];
    
    if (filters.startDate || filters.endDate) {
      const range = {};
      if (filters.startDate) {
        range.gte = new Date(filters.startDate).toISOString();
      }
      if (filters.endDate) {
        range.lte = new Date(filters.endDate).toISOString();
      }
      dateFilter.push({ range: { startTime: range } });
    }
    // Remove default 30-day filter - show all data unless explicitly filtered
    
    return dateFilter;
  }

  /**
   * Build machine filter
   */
  buildMachineFilter(filters = {}) {
    const machineFilter = [];
    
    if (filters.machineId) {
      machineFilter.push({ term: { machineId: filters.machineId } });
    }
    
    if (filters.machine) {
      // Support both machineId and machineName for compatibility
      machineFilter.push({ 
        bool: {
          should: [
            { term: { machineId: filters.machine } },
            { term: { machineName: filters.machine } }
          ]
        }
      });
    }
    
    if (filters.machineIds && Array.isArray(filters.machineIds) && filters.machineIds.length > 0) {
      machineFilter.push({ terms: { machineId: filters.machineIds } });
    }
    
    return machineFilter;
  }

  /**
   * Build category filter
   */
  buildCategoryFilter(filters = {}) {
    const categoryFilter = [];
    
    if (filters.category) {
      categoryFilter.push({ term: { category: filters.category } });
    }
    
    if (filters.categories && Array.isArray(filters.categories) && filters.categories.length > 0) {
      categoryFilter.push({ terms: { category: filters.categories } });
    }
    
    return categoryFilter;
  }

  /**
   * Build complete filter for queries
   */
  buildFilters(filters = {}) {
    const must = [];
    
    // Date range
    must.push(...this.buildDateRangeFilter(filters));
    
    // Machine filter
    must.push(...this.buildMachineFilter(filters));
    
    // Category filter
    must.push(...this.buildCategoryFilter(filters));
    
    // Severity filter
    if (filters.severity) {
      must.push({ term: { severity: filters.severity } });
    }
    
    // Shift filter
    if (filters.shift) {
      must.push({ term: { shift: filters.shift } });
    }
    
    return { bool: { must } };
  }

  /**
   * Get essential dashboard statistics
   */
  async getEssentialStats(filters = {}) {
    try {
      const query = {
        query: this.buildFilters(filters),
        aggs: {
          total_stops: {
            value_count: { field: 'id' }
          },
          total_duration: {
            sum: { field: 'duration' }
          },
          avg_duration: {
            avg: { field: 'duration' }
          },
          unique_machines: {
            cardinality: { field: 'machineId' }
          },
          stops_by_severity: {
            terms: { 
              field: 'severity',
              size: 10
            }
          },
          stops_by_category: {
            terms: { 
              field: 'category',
              size: 10
            }
          }
        },
        size: 0
      };

      const result = await elasticsearchService.search(this.indexName, query);
      const aggs = result.aggregations;

      return {
        totalStops: aggs.total_stops.value,
        totalDuration: Math.round(aggs.total_duration.value || 0),
        averageDuration: Math.round(aggs.avg_duration.value || 0),
        uniqueMachines: aggs.unique_machines.value,
        stopsBySeverity: aggs.stops_by_severity.buckets.map(bucket => ({
          severity: bucket.key,
          count: bucket.doc_count
        })),
        stopsByCategory: aggs.stops_by_category.buckets.map(bucket => ({
          category: bucket.key,
          count: bucket.doc_count
        }))
      };
    } catch (error) {
      console.error('Error getting essential stats:', error);
      throw error;
    }
  }

  /**
   * Get top stop reasons for dashboard
   */
  async getTopStopReasons(filters = {}, limit = 5) {
    try {
      const query = {
        query: this.buildFilters(filters),
        aggs: {
          top_reasons: {
            terms: { 
              field: 'stopReason.keyword',
              size: limit,
              order: { total_duration: 'desc' }
            },
            aggs: {
              total_duration: {
                sum: { field: 'duration' }
              },
              avg_duration: {
                avg: { field: 'duration' }
              },
              stop_count: {
                value_count: { field: 'id' }
              }
            }
          }
        },
        size: 0
      };

      const result = await elasticsearchService.search(this.indexName, query);
      const reasons = result.aggregations.top_reasons.buckets;

      return reasons.map((bucket, index) => ({
        id: index + 1,
        reason: bucket.key,
        count: bucket.stop_count.value,
        totalDuration: Math.round(bucket.total_duration.value || 0),
        averageDuration: Math.round(bucket.avg_duration.value || 0),
        percentage: 0 // Will be calculated after getting total
      }));
    } catch (error) {
      console.error('Error getting top stop reasons:', error);
      throw error;
    }
  }

  /**
   * Get machine comparison data
   */
  async getMachineComparison(filters = {}) {
    try {
      const query = {
        query: this.buildFilters(filters),
        aggs: {
          machines: {
            terms: { 
              field: 'machineId',
              size: 20
            },
            aggs: {
              total_stops: {
                value_count: { field: 'id' }
              },
              total_duration: {
                sum: { field: 'duration' }
              },
              avg_duration: {
                avg: { field: 'duration' }
              },
              categories: {
                terms: { 
                  field: 'category',
                  size: 10
                }
              },
              severity_breakdown: {
                terms: { 
                  field: 'severity',
                  size: 10
                }
              }
            }
          }
        },
        size: 0
      };

      const result = await elasticsearchService.search(this.indexName, query);
      const machines = result.aggregations.machines.buckets;

      return machines.map(bucket => ({
        machineId: bucket.key,
        machineName: bucket.key, // Could be enhanced with machine name mapping
        totalStops: bucket.total_stops.value,
        totalDuration: Math.round(bucket.total_duration.value || 0),
        averageDuration: Math.round(bucket.avg_duration.value || 0),
        categories: bucket.categories.buckets.map(cat => ({
          category: cat.key,
          count: cat.doc_count
        })),
        severityBreakdown: bucket.severity_breakdown.buckets.map(sev => ({
          severity: sev.key,
          count: sev.doc_count
        }))
      }));
    } catch (error) {
      console.error('Error getting machine comparison:', error);
      throw error;
    }
  }

  /**
   * Get time-series evolution data for charts
   */
  async getEvolutionData(filters = {}, interval = 'day') {
    try {
      // Determine the date histogram interval
      const dateHistogramInterval = interval === 'hour' ? '1h' : 
                                   interval === 'week' ? '1w' : '1d';

      const query = {
        query: this.buildFilters(filters),
        aggs: {
          evolution: {
            date_histogram: {
              field: 'startTime',
              calendar_interval: dateHistogramInterval,
              order: { _key: 'asc' }
            },
            aggs: {
              stops_count: {
                value_count: { field: 'id' }
              },
              total_duration: {
                sum: { field: 'duration' }
              },
              categories: {
                terms: { 
                  field: 'category',
                  size: 10
                }
              }
            }
          }
        },
        size: 0
      };

      const result = await elasticsearchService.search(this.indexName, query);
      const buckets = result.aggregations.evolution.buckets;

      return buckets.map(bucket => ({
        date: bucket.key_as_string || new Date(bucket.key).toISOString(),
        timestamp: bucket.key,
        stopsCount: bucket.stops_count.value,
        totalDuration: Math.round(bucket.total_duration.value || 0),
        categories: bucket.categories.buckets.map(cat => ({
          category: cat.key,
          count: cat.doc_count
        }))
      }));
    } catch (error) {
      console.error('Error getting evolution data:', error);
      throw error;
    }
  }

  /**
   * Get detailed stop data for table view
   */
  async getDetailedStops(filters = {}, pagination = { page: 1, size: 100 }) {
    try {
      const from = (pagination.page - 1) * pagination.size;
      
      const query = {
        query: this.buildFilters(filters),
        sort: [
          { startTime: { order: 'desc' } }
        ],
        from: from,
        size: pagination.size,
        _source: [
          'id', 'machineId', 'machineName', 'stopReason', 'stopCode',
          'startTime', 'endTime', 'duration', 'shift', 'operator',
          'description', 'category', 'severity'
        ]
      };

      const result = await elasticsearchService.search(this.indexName, query);
      
      return {
        stops: result.hits.hits.map(hit => hit._source),
        total: result.hits.total.value || result.hits.total,
        page: pagination.page,
        size: pagination.size,
        totalPages: Math.ceil((result.hits.total.value || result.hits.total) / pagination.size)
      };
    } catch (error) {
      console.error('Error getting detailed stops:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics for dashboard
   */
  async getPerformanceMetrics(filters = {}) {
    try {
      const query = {
        query: this.buildFilters(filters),
        aggs: {
          mttr: {
            avg: { field: 'duration' }
          },
          mtbf_calculation: {
            date_histogram: {
              field: 'startTime',
              calendar_interval: '1d',
              min_doc_count: 1
            },
            aggs: {
              daily_stops: {
                value_count: { field: 'id' }
              }
            }
          },
          uptime_calculation: {
            sum: { field: 'duration' }
          }
        },
        size: 0
      };

      const result = await elasticsearchService.search(this.indexName, query);
      const aggs = result.aggregations;

      // Calculate MTBF (Mean Time Between Failures)
      const dailyStops = aggs.mtbf_calculation.buckets;
      const avgStopsPerDay = dailyStops.length > 0 ? 
        dailyStops.reduce((sum, bucket) => sum + bucket.daily_stops.value, 0) / dailyStops.length : 0;
      const mtbf = avgStopsPerDay > 0 ? (24 * 60) / avgStopsPerDay : 0; // minutes between stops

      // Calculate availability (assuming 24/7 operation)
      const totalDowntime = aggs.uptime_calculation.value || 0;
      const periodDays = dailyStops.length || 1;
      const totalPossibleTime = periodDays * 24 * 60; // minutes
      const availability = totalPossibleTime > 0 ? 
        ((totalPossibleTime - totalDowntime) / totalPossibleTime) * 100 : 100;

      return {
        mttr: Math.round(aggs.mttr.value || 0), // Mean Time To Repair
        mtbf: Math.round(mtbf), // Mean Time Between Failures
        availability: Math.round(availability * 100) / 100, // Availability percentage
        totalDowntime: Math.round(totalDowntime),
        averageStopsPerDay: Math.round(avgStopsPerDay * 100) / 100
      };
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      throw error;
    }
  }

  /**
   * Search stops with text query
   */
  async searchStops(searchText, filters = {}, pagination = { page: 1, size: 50 }) {
    try {
      const must = [];
      
      // Add text search
      if (searchText && searchText.trim()) {
        must.push({
          multi_match: {
            query: searchText,
            fields: ['stopReason^2', 'description', 'stopCode', 'operator'],
            type: 'best_fields',
            fuzziness: 'AUTO'
          }
        });
      }
      
      // Add filters
      const filterClauses = this.buildFilters(filters);
      if (filterClauses.bool.must.length > 0) {
        must.push(...filterClauses.bool.must);
      }

      const from = (pagination.page - 1) * pagination.size;
      
      const query = {
        query: {
          bool: { must }
        },
        sort: [
          { _score: { order: 'desc' } },
          { startTime: { order: 'desc' } }
        ],
        from: from,
        size: pagination.size,
        highlight: {
          fields: {
            stopReason: {},
            description: {},
            operator: {}
          }
        }
      };

      const result = await elasticsearchService.search(this.indexName, query);
      
      return {
        stops: result.hits.hits.map(hit => ({
          ...hit._source,
          highlights: hit.highlight
        })),
        total: result.hits.total.value || result.hits.total,
        page: pagination.page,
        size: pagination.size,
        totalPages: Math.ceil((result.hits.total.value || result.hits.total) / pagination.size)
      };
    } catch (error) {
      console.error('Error searching stops:', error);
      throw error;
    }
  }

  /**
   * Check if Elasticsearch is available and index exists
   */
  async isAvailable() {
    try {
      const health = await checkElasticsearchHealth();
      if (!health) {
        return false;
      }
      
      const indexExists = await elasticsearchService.indexExists(this.indexName);
      return indexExists;
    } catch (error) {
      console.error('Elasticsearch availability check failed:', error);
      return false;
    }
  }

  /**
   * Get index statistics
   */
  async getIndexStats() {
    try {
      const stats = await elasticsearchService.client.indices.stats({
        index: this.indexName
      });
      
      const indexStats = stats.indices?.[this.indexName];
      if (!indexStats) {
        return {
          documentCount: 0,
          indexSize: 0,
          status: 'not_found'
        };
      }
      
      return {
        documentCount: indexStats.total?.docs?.count || 0,
        indexSize: indexStats.total?.store?.size_in_bytes || 0,
        status: 'healthy'
      };
    } catch (error) {
      console.error('Error getting index stats:', error);
      return {
        documentCount: 0,
        indexSize: 0,
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * Get index statistics
   */
  async getIndexStats() {
    try {
      const stats = await elasticsearchService.getIndexStats(this.indexName);
      const count = await elasticsearchService.getDocumentCount(this.indexName);
      
      return {
        documentCount: count,
        indexSize: stats._all?.total?.store?.size_in_bytes || 0,
        status: 'healthy'
      };
    } catch (error) {
      console.error('Error getting index stats:', error);
      return {
        documentCount: 0,
        indexSize: 0,
        status: 'error',
        error: error.message
      };
    }
  }
}

export default new ElasticsearchDashboardService();
