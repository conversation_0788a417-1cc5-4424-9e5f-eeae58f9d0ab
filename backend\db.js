import mysql from "mysql2/promise";
import dotenv from "dotenv";

dotenv.config({ path: './config.env' });

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  
  // Optimized pool settings for dashboard workload
  connectionLimit: 25,         // Increased from default 10
  queueLimit: 50,             // Queue up to 50 requests
  
  // Performance optimizations
  multipleStatements: false,   // Security best practice
  dateStrings: true,          // Better date handling
  supportBigNumbers: true,
  bigNumberStrings: true,
  
  // Connection lifecycle management
  idleTimeout: 300000,        // Close idle connections after 5 minutes
  maxIdle: 15,                // Keep 15 idle connections ready
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  
  // Additional MySQL optimizations
  charset: 'utf8mb4',
  ssl: false,                 // Adjust based on your setup
  
  // MySQL2 specific options
  namedPlaceholders: true,    // Enable named placeholders
  typeCast: true             // Enable type casting
});

// Pool event monitoring and health checks
pool.on('connection', (connection) => {
  console.log(`📊 DB Connection established: ${connection.threadId}`);
  
  // Skip session optimization to avoid compatibility issues
  // Session settings will be handled at the application level if needed
});

pool.on('error', (err) => {
  console.error('💥 Database pool error:', err);
  if (err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('🔄 Attempting database reconnection...');
  }
});



// Export enhanced query function with retry logic
export const queryWithRetry = async (sql, params, retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      const [results] = await pool.execute(sql, params);
      return results;
    } catch (error) {
      console.error(`Query attempt ${i + 1} failed:`, error.message);
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};

// Export callback-compatible query function for legacy code
export const query = (sql, params, callback) => {
  if (typeof params === 'function') {
    callback = params;
    params = [];
  }
  
  pool.execute(sql, params)
    .then(([results, fields]) => {
      callback(null, results, fields);
    })
    .catch(error => {
      callback(error);
    });
};

// Export promise-based execute function
export const execute = async (sql, params = []) => {
  return await pool.execute(sql, params);
};

// Export pool health check function
export const getPoolStatus = () => {
  try {
    return {
      totalConnections: pool.pool?._allConnections?.length || 0,
      freeConnections: pool.pool?._freeConnections?.length || 0,
      usedConnections: pool.pool?._acquiringConnections?.length || 0,
      queuedRequests: pool.pool?._connectionQueue?.length || 0
    };
  } catch (error) {
    console.warn('Unable to get pool status:', error.message);
    return { error: 'Pool status unavailable' };
  }
};

// Add callback-compatible query method to the pool
pool.query = (sql, params, callback) => {
  if (typeof params === 'function') {
    callback = params;
    params = [];
  }
  
  pool.execute(sql, params)
    .then(([results, fields]) => {
      callback(null, results, fields);
    })
    .catch(error => {
      callback(error);
    });
};

export default pool;