import React from "react" ;
import { useCallback } from 'react'
import { 
  SKELETON_SECTIONS, 
  DEFAULT_SKELETON_PHASES, 
  INITIAL_SKELETON_STATE 
} from './constants.jsx'

/**
 * Skeleton Management Module
 * Handles all skeleton loading states and transitions for the Arret dashboard
 */
export const useSkeletonManager = (skeletonStates, setSkeletonStates) => {
  
  // Start skeleton loading for specific sections
  const startSkeletonLoading = useCallback((sections = []) => {
    if (sections.length === 0) {
      // Default: show all essential skeletons
      sections = [
        SKELETON_SECTIONS.STATS, 
        SKELETON_SECTIONS.CHARTS, 
        SKELETON_SECTIONS.TABLE, 
        SKELETON_SECTIONS.INITIAL_LOAD
      ];
    }
    
    const newState = {};
    sections.forEach(section => {
      newState[section] = true;
    });
    
    setSkeletonStates(prev => ({ ...prev, ...newState }));
  }, [setSkeletonStates]);

  // Stop skeleton loading for specific sections
  const stopSkeletonLoading = useCallback((sections = []) => {
    if (sections.length === 0) {
      // Clear all skeletons
      setSkeletonStates(INITIAL_SKELETON_STATE);
      return;
    }
    
    const newState = {};
    sections.forEach(section => {
      newState[section] = false;
    });
    
    setSkeletonStates(prev => ({ ...prev, ...newState }));
  }, [setSkeletonStates]);

  // Progressive skeleton clearing with configurable phases
  const progressiveSkeletonClear = useCallback((phases = null) => {
    const phasesToUse = phases || DEFAULT_SKELETON_PHASES;
    
    phasesToUse.forEach(({ sections, delay }) => {
      setTimeout(() => {
        stopSkeletonLoading(sections);
      }, delay);
    });
    
  }, [stopSkeletonLoading]);

  // Smart skeleton management based on filter complexity
  const smartSkeletonForFilters = useCallback((hasModel, hasMachine, hasDate) => {
    const skeletonsToShow = [SKELETON_SECTIONS.STATS];
    
    if (hasMachine) {
      skeletonsToShow.push(
        SKELETON_SECTIONS.PERFORMANCE, 
        SKELETON_SECTIONS.MACHINE_COMPARISON_CHART
      );
    }
    
    if (hasDate) {
      skeletonsToShow.push(
        SKELETON_SECTIONS.DURATION_TREND_CHART, 
        SKELETON_SECTIONS.MTTR_HEATMAP
      );
    }
    
    if (hasModel || hasMachine) {
      skeletonsToShow.push(
        SKELETON_SECTIONS.TOP_STOPS_CHART, 
        SKELETON_SECTIONS.PARETO_CHART
      );
    }
    
    if (hasModel && hasMachine && hasDate) {
      skeletonsToShow.push(
        SKELETON_SECTIONS.TABLE, 
        SKELETON_SECTIONS.AVAILABILITY_CHART, 
        SKELETON_SECTIONS.FILTER_CHANGE
      );
    }
    
    startSkeletonLoading(skeletonsToShow);
    return skeletonsToShow;
  }, [startSkeletonLoading]);

  // Skeleton state checkers for components
  const isSkeletonActive = useCallback((section) => {
    return skeletonStates[section] || false;
  }, [skeletonStates]);

  const areSkeletonsActive = useCallback((sections) => {
    return sections.some(section => skeletonStates[section]);
  }, [skeletonStates]);

  const getActiveSkeletons = useCallback(() => {
    return Object.keys(skeletonStates).filter(key => skeletonStates[key]);
  }, [skeletonStates]);

  // Fast skeleton clear for cached data scenarios
  const fastSkeletonClear = useCallback(() => {
    const fastPhases = [
      { sections: [SKELETON_SECTIONS.STATS], delay: 100 },
      { sections: [SKELETON_SECTIONS.PERFORMANCE], delay: 200 },
      { sections: [SKELETON_SECTIONS.TOP_STOPS_CHART, SKELETON_SECTIONS.DURATION_TREND_CHART], delay: 300 },
      { sections: [SKELETON_SECTIONS.TABLE, SKELETON_SECTIONS.CHARTS], delay: 400 },
      { sections: [SKELETON_SECTIONS.INITIAL_LOAD, SKELETON_SECTIONS.DATA_REFRESH], delay: 500 }
    ];
    
    progressiveSkeletonClear(fastPhases);
  }, [progressiveSkeletonClear]);

  return {
    startSkeletonLoading,
    stopSkeletonLoading,
    progressiveSkeletonClear,
    smartSkeletonForFilters,
    isSkeletonActive,
    areSkeletonsActive,
    getActiveSkeletons,
    fastSkeletonClear
  }
}
