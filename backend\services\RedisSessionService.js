/**
 * Redis Session Management Service
 * Enhances JWT-based authentication with Redis session caching
 * 
 * Features:
 * - High-performance session storage and retrieval
 * - Multi-device session management
 * - Session invalidation and cleanup
 * - Real-time session monitoring
 * - Graceful fallback to database sessions
 */

import Redis from 'ioredis';
import redisConfig from '../config/redisConfig.js';
import { executeQuery } from '../utils/dbUtils.js';

class RedisSessionService {
  constructor() {
    this.client = null;
    this.initialized = false;
    this.metrics = {
      sessionsCreated: 0,
      sessionsRetrieved: 0,
      sessionsInvalidated: 0,
      cacheHits: 0,
      cacheMisses: 0,
      avgResponseTime: 0,
      lastActivity: null
    };

    // Session configuration
    this.SESSION_CONFIG = {
      // TTL settings (in seconds)
      DEFAULT_TTL: 8 * 60 * 60,        // 8 hours (matches JWT expiration)
      EXTENDED_TTL: 7 * 24 * 60 * 60,  // 7 days (for remember me)
      CLEANUP_INTERVAL: 60 * 60,       // 1 hour cleanup interval
      
      // Key prefixes
      SESSION_PREFIX: 'session:',
      USER_SESSIONS_PREFIX: 'user_sessions:',
      ACTIVE_SESSIONS_PREFIX: 'active_sessions',
      
      // Limits
      MAX_SESSIONS_PER_USER: 5,        // Maximum concurrent sessions per user
      SESSION_REFRESH_THRESHOLD: 0.5   // Refresh when 50% of TTL remaining
    };
  }

  /**
   * Initialize Redis session service with fallback support
   */
  async initialize() {
    if (this.initialized) {
      return true;
    }

    try {
      // Use the centralized Redis config with fallback
      await redisConfig.initialize();
      this.client = redisConfig.getClient();

      // Set up error handlers
      this.client.on('error', (error) => {
        console.log('❌ Redis Session Service error:', error.message);
      });

      this.client.on('connect', () => {
        console.log('✅ Redis Session Service connected');
      });

      this.initialized = true;
      
      if (redisConfig.isInFallbackMode()) {
        console.log('✅ Redis Session Service initialized with in-memory fallback');
      } else {
        console.log('✅ Redis Session Service initialized with Redis connection');
      }

      // Start cleanup process
      this.startSessionCleanup();

      return true;

    } catch (error) {
      console.error('❌ Redis Session Service initialization failed:', error.message);
      console.log('⚠️ Session service continuing with fallback mode');
      this.initialized = true; // Allow operation in fallback mode
      return true;
    }
  }

  /**
   * Create a new session in Redis
   */
  async createSession(sessionData) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Session Service not initialized - falling back to database');
      return this.createDatabaseSession(sessionData);
    }

    try {
      const startTime = Date.now();
      const { userId, token, userAgent, ipAddress, expiresAt, rememberMe } = sessionData;

      // Determine TTL based on remember me option
      const ttl = rememberMe ? this.SESSION_CONFIG.EXTENDED_TTL : this.SESSION_CONFIG.DEFAULT_TTL;

      // Create session object
      const session = {
        userId,
        token,
        userAgent: userAgent || 'Unknown',
        ipAddress: ipAddress || 'Unknown',
        createdAt: new Date().toISOString(),
        expiresAt: expiresAt || new Date(Date.now() + ttl * 1000).toISOString(),
        lastActivity: new Date().toISOString(),
        isActive: true,
        rememberMe: !!rememberMe
      };

      // Store session with token as key
      const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${token}`;
      await this.client.setex(sessionKey, ttl, JSON.stringify(session));

      // Add to user's session list
      const userSessionsKey = `${this.SESSION_CONFIG.USER_SESSIONS_PREFIX}${userId}`;
      await this.client.sadd(userSessionsKey, token);
      await this.client.expire(userSessionsKey, ttl);

      // Add to active sessions set
      await this.client.sadd(this.SESSION_CONFIG.ACTIVE_SESSIONS_PREFIX, token);

      // Enforce session limit per user
      await this.enforceSessionLimit(userId);

      // Also store in database for persistence
      await this.createDatabaseSession(sessionData);

      const responseTime = Date.now() - startTime;
      this.updateMetrics('create', responseTime);

      console.log(`✅ Session created in Redis for user ${userId} (${responseTime}ms)`);
      return { success: true, sessionKey, ttl };

    } catch (error) {
      console.error('❌ Failed to create Redis session:', error);
      // Fallback to database session
      return this.createDatabaseSession(sessionData);
    }
  }

  /**
   * Retrieve session from Redis
   */
  async getSession(token) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Session Service not initialized - falling back to database');
      return this.getDatabaseSession(token);
    }

    try {
      const startTime = Date.now();
      const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${token}`;

      const sessionData = await this.client.get(sessionKey);

      if (!sessionData) {
        this.metrics.cacheMisses++;
        console.log(`📦 Session cache MISS for token: ${token.substring(0, 10)}...`);
        
        // Try to get from database and cache it
        const dbSession = await this.getDatabaseSession(token);
        if (dbSession.success && dbSession.data) {
          // Cache the database session
          await this.cacheSessionFromDatabase(dbSession.data);
        }
        return dbSession;
      }

      const session = JSON.parse(sessionData);
      const responseTime = Date.now() - startTime;

      // Check if session needs refresh
      const shouldRefresh = await this.shouldRefreshSession(sessionKey, session);
      if (shouldRefresh) {
        await this.refreshSession(token, session);
      }

      // Update last activity
      session.lastActivity = new Date().toISOString();
      await this.client.setex(sessionKey, await this.client.ttl(sessionKey), JSON.stringify(session));

      this.updateMetrics('retrieve', responseTime, true);

      console.log(`📦 Session cache HIT for user ${session.userId} (${responseTime}ms)`);
      return { success: true, data: session };

    } catch (error) {
      console.error('❌ Failed to retrieve Redis session:', error);
      return this.getDatabaseSession(token);
    }
  }

  /**
   * Invalidate session in Redis
   */
  async invalidateSession(token) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Session Service not initialized - falling back to database');
      return this.invalidateDatabaseSession(token);
    }

    try {
      const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${token}`;
      
      // Get session data before deletion
      const sessionData = await this.client.get(sessionKey);
      
      if (sessionData) {
        const session = JSON.parse(sessionData);
        
        // Remove from user's session list
        const userSessionsKey = `${this.SESSION_CONFIG.USER_SESSIONS_PREFIX}${session.userId}`;
        await this.client.srem(userSessionsKey, token);
        
        // Remove from active sessions
        await this.client.srem(this.SESSION_CONFIG.ACTIVE_SESSIONS_PREFIX, token);
      }

      // Delete session
      const deleted = await this.client.del(sessionKey);

      // Also invalidate in database
      await this.invalidateDatabaseSession(token);

      this.metrics.sessionsInvalidated++;

      console.log(`✅ Session invalidated: ${token.substring(0, 10)}... (deleted: ${deleted})`);
      return { success: true, deleted: deleted > 0 };

    } catch (error) {
      console.error('❌ Failed to invalidate Redis session:', error);
      return this.invalidateDatabaseSession(token);
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  async invalidateUserSessions(userId) {
    if (!this.initialized) {
      console.warn('⚠️ Redis Session Service not initialized - falling back to database');
      return this.invalidateUserDatabaseSessions(userId);
    }

    try {
      const userSessionsKey = `${this.SESSION_CONFIG.USER_SESSIONS_PREFIX}${userId}`;
      
      // Get all user sessions
      const userTokens = await this.client.smembers(userSessionsKey);
      
      if (userTokens.length === 0) {
        console.log(`ℹ️ No active sessions found for user ${userId}`);
        return { success: true, invalidated: 0 };
      }

      // Invalidate each session
      let invalidated = 0;
      for (const token of userTokens) {
        const result = await this.invalidateSession(token);
        if (result.success) {
          invalidated++;
        }
      }

      // Clear user sessions set
      await this.client.del(userSessionsKey);

      console.log(`✅ Invalidated ${invalidated} sessions for user ${userId}`);
      return { success: true, invalidated };

    } catch (error) {
      console.error(`❌ Failed to invalidate user sessions for ${userId}:`, error);
      return this.invalidateUserDatabaseSessions(userId);
    }
  }

  /**
   * Get active sessions for a user
   */
  async getUserSessions(userId) {
    if (!this.initialized) {
      return this.getUserDatabaseSessions(userId);
    }

    try {
      const userSessionsKey = `${this.SESSION_CONFIG.USER_SESSIONS_PREFIX}${userId}`;
      const userTokens = await this.client.smembers(userSessionsKey);

      const sessions = [];
      for (const token of userTokens) {
        const sessionResult = await this.getSession(token);
        if (sessionResult.success && sessionResult.data) {
          sessions.push({
            token: token.substring(0, 10) + '...',
            userAgent: sessionResult.data.userAgent,
            ipAddress: sessionResult.data.ipAddress,
            createdAt: sessionResult.data.createdAt,
            lastActivity: sessionResult.data.lastActivity,
            isActive: sessionResult.data.isActive
          });
        }
      }

      return { success: true, sessions };

    } catch (error) {
      console.error(`❌ Failed to get user sessions for ${userId}:`, error);
      return this.getUserDatabaseSessions(userId);
    }
  }

  /**
   * Check if session should be refreshed
   */
  async shouldRefreshSession(sessionKey, session) {
    try {
      const ttl = await this.client.ttl(sessionKey);
      const originalTtl = session.rememberMe ? 
        this.SESSION_CONFIG.EXTENDED_TTL : 
        this.SESSION_CONFIG.DEFAULT_TTL;

      // Refresh if less than 50% of original TTL remains
      return ttl < (originalTtl * this.SESSION_CONFIG.SESSION_REFRESH_THRESHOLD);

    } catch (error) {
      console.error('❌ Failed to check session refresh:', error);
      return false;
    }
  }

  /**
   * Refresh session TTL
   */
  async refreshSession(token, session) {
    try {
      const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${token}`;
      const ttl = session.rememberMe ? 
        this.SESSION_CONFIG.EXTENDED_TTL : 
        this.SESSION_CONFIG.DEFAULT_TTL;

      await this.client.expire(sessionKey, ttl);
      console.log(`🔄 Session refreshed: ${token.substring(0, 10)}... (TTL: ${ttl}s)`);

    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
    }
  }

  /**
   * Enforce session limit per user
   */
  async enforceSessionLimit(userId) {
    try {
      const userSessionsKey = `${this.SESSION_CONFIG.USER_SESSIONS_PREFIX}${userId}`;
      const userTokens = await this.client.smembers(userSessionsKey);

      if (userTokens.length > this.SESSION_CONFIG.MAX_SESSIONS_PER_USER) {
        // Sort sessions by creation time and remove oldest
        const sessionsWithTime = [];
        
        for (const token of userTokens) {
          const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${token}`;
          const sessionData = await this.client.get(sessionKey);
          
          if (sessionData) {
            const session = JSON.parse(sessionData);
            sessionsWithTime.push({
              token,
              createdAt: new Date(session.createdAt).getTime()
            });
          }
        }

        // Sort by creation time (oldest first)
        sessionsWithTime.sort((a, b) => a.createdAt - b.createdAt);

        // Remove excess sessions
        const excessCount = sessionsWithTime.length - this.SESSION_CONFIG.MAX_SESSIONS_PER_USER;
        for (let i = 0; i < excessCount; i++) {
          await this.invalidateSession(sessionsWithTime[i].token);
        }

        console.log(`🧹 Removed ${excessCount} excess sessions for user ${userId}`);
      }

    } catch (error) {
      console.error(`❌ Failed to enforce session limit for user ${userId}:`, error);
    }
  }

  /**
   * Start periodic session cleanup
   */
  startSessionCleanup() {
    setInterval(async () => {
      try {
        console.log('🧹 Starting session cleanup...');
        
        // Get all active sessions
        const activeTokens = await this.client.smembers(this.SESSION_CONFIG.ACTIVE_SESSIONS_PREFIX);
        let cleanedCount = 0;

        for (const token of activeTokens) {
          const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${token}`;
          const exists = await this.client.exists(sessionKey);
          
          if (!exists) {
            // Remove from active sessions if session doesn't exist
            await this.client.srem(this.SESSION_CONFIG.ACTIVE_SESSIONS_PREFIX, token);
            cleanedCount++;
          }
        }

        console.log(`🧹 Session cleanup completed: ${cleanedCount} stale references removed`);

      } catch (error) {
        console.error('❌ Session cleanup failed:', error);
      }
    }, this.SESSION_CONFIG.CLEANUP_INTERVAL * 1000);
  }

  // Database fallback methods (simplified implementations)
  async createDatabaseSession(sessionData) {
    try {
      const { userId, token, expiresAt } = sessionData;
      const result = await executeQuery(
        'INSERT INTO sessions (user_id, token, expires_at) VALUES (?, ?, ?)',
        [userId, token, expiresAt]
      );
      return { success: result.success };
    } catch (error) {
      console.error('❌ Database session creation failed:', error);
      return { success: false, error };
    }
  }

  async getDatabaseSession(token) {
    try {
      const result = await executeQuery(
        'SELECT * FROM sessions WHERE token = ? AND expires_at > NOW()',
        [token]
      );
      return { 
        success: result.success, 
        data: result.success && result.data.length > 0 ? result.data[0] : null 
      };
    } catch (error) {
      console.error('❌ Database session retrieval failed:', error);
      return { success: false, error };
    }
  }

  async invalidateDatabaseSession(token) {
    try {
      const result = await executeQuery(
        'DELETE FROM sessions WHERE token = ?',
        [token]
      );
      return { success: result.success };
    } catch (error) {
      console.error('❌ Database session invalidation failed:', error);
      return { success: false, error };
    }
  }

  async invalidateUserDatabaseSessions(userId) {
    try {
      const result = await executeQuery(
        'DELETE FROM sessions WHERE user_id = ?',
        [userId]
      );
      return { success: result.success, invalidated: result.affectedRows || 0 };
    } catch (error) {
      console.error('❌ Database user session invalidation failed:', error);
      return { success: false, error };
    }
  }

  async getUserDatabaseSessions(userId) {
    try {
      const result = await executeQuery(
        'SELECT token, created_at, expires_at FROM sessions WHERE user_id = ? AND expires_at > NOW()',
        [userId]
      );
      return { 
        success: result.success, 
        sessions: result.success ? result.data : [] 
      };
    } catch (error) {
      console.error('❌ Database user sessions retrieval failed:', error);
      return { success: false, error };
    }
  }

  async cacheSessionFromDatabase(dbSession) {
    try {
      const sessionKey = `${this.SESSION_CONFIG.SESSION_PREFIX}${dbSession.token}`;
      const ttl = Math.max(0, Math.floor((new Date(dbSession.expires_at) - Date.now()) / 1000));
      
      if (ttl > 0) {
        await this.client.setex(sessionKey, ttl, JSON.stringify(dbSession));
        console.log(`📦 Cached database session: ${dbSession.token.substring(0, 10)}...`);
      }
    } catch (error) {
      console.error('❌ Failed to cache database session:', error);
    }
  }

  /**
   * Update performance metrics
   */
  updateMetrics(type, responseTime = 0, isHit = false) {
    this.metrics.lastActivity = new Date().toISOString();

    if (type === 'create') {
      this.metrics.sessionsCreated++;
    } else if (type === 'retrieve') {
      this.metrics.sessionsRetrieved++;
      if (isHit) {
        this.metrics.cacheHits++;
      } else {
        this.metrics.cacheMisses++;
      }
    }

    if (responseTime > 0) {
      this.metrics.avgResponseTime = 
        (this.metrics.avgResponseTime + responseTime) / 2;
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    const totalRetrievals = this.metrics.cacheHits + this.metrics.cacheMisses;
    const hitRate = totalRetrievals > 0 ? 
      ((this.metrics.cacheHits / totalRetrievals) * 100).toFixed(2) : 0;

    return {
      ...this.metrics,
      hitRate: `${hitRate}%`,
      initialized: this.initialized,
      connected: this.client?.status === 'ready'
    };
  }

  /**
   * Health check
   */
  async healthCheck() {
    if (!this.initialized) {
      return { healthy: false, reason: 'Not initialized' };
    }

    try {
      await this.client.ping();
      return {
        healthy: true,
        status: this.client.status,
        metrics: this.getMetrics()
      };
    } catch (error) {
      return {
        healthy: false,
        reason: error.message,
        status: this.client?.status || 'disconnected'
      };
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🔄 Shutting down Redis Session Service...');

    try {
      if (this.client) {
        await this.client.quit();
      }

      this.initialized = false;
      console.log('✅ Redis Session Service shut down gracefully');

    } catch (error) {
      console.error('❌ Error during Redis Session Service shutdown:', error);
    }
  }
}

// Create singleton instance
const redisSessionService = new RedisSessionService();

export default redisSessionService;
