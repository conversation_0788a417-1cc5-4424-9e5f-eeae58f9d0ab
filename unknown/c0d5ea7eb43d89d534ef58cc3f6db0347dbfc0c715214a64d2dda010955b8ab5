# LOCQL Unified Container - Local Testing Environment
# This file contains environment variables for local Docker testing

# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=development
PORT=5000
DEBUG=true
LOG_LEVEL=debug

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# Using host.docker.internal to connect to local MySQL from Docker container
DB_HOST=host.docker.internal
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=locql_db

# ==============================================
# AUTHENTICATION & SECURITY
# ==============================================
JWT_SECRET=locql_development_secret_key_for_testing

# Security settings for development
SECURE_COOKIES=false
TRUST_PROXY=false

# ==============================================
# CORS CONFIGURATION
# ==============================================
CORS_ORIGINS=http://localhost:5000,http://127.0.0.1:5000,http://localhost:5001

