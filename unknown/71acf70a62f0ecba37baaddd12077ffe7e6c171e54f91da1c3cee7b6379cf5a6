import React from "react" ;
import { useState, useEffect } from "react"

// Hook pour détecter si l'écran est de taille mobile
export const useMobile = () => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Fonction pour vérifier la taille de l'écran
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Vérifier au chargement
    checkMobile()

    // Ajouter un écouteur d'événement pour les changements de taille
    window.addEventListener("resize", checkMobile)

    // Nettoyer l'écouteur d'événement
    return () => {
      window.removeEventListener("resize", checkMobile)
    }
  }, [])

  return isMobile
}

export default useMobile

