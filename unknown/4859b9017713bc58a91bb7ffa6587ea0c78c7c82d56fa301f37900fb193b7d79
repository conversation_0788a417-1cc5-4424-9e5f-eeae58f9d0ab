import { useState, useCallback, useRef, useEffect } from 'react';
import request from 'superagent';
import React from "react" ;
export const useAggregatedData = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({});
  const [error, setError] = useState(null);
  const [metadata, setMetadata] = useState({});
  const batchCounter = useRef(0);
  const retryCount = useRef(0);
  const maxRetries = 3;
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      setData({});
      setError(null);
      setMetadata({});
    };
  }, []);

  const fetchAggregatedData = useCallback(async (requests, options = {}) => {
    if (!requests || requests.length === 0) return;
      const batchId = `batch_${++batchCounter.current}_${Date.now()}`;
    const startTime = Date.now();
    
    setLoading(true);
    setError(null);
    
    console.log(`🚀 Fetching aggregated data (${requests.length} requests) - ${batchId}`);
    
    try {
      const response = await request.post('/api/dashboard-data')
        .send({ 
          requests,
          batchId,
          ...options 
        })
        .timeout(options.timeout || 30000)
        .retry(retryCount.current < maxRetries ? 2 : 0);
      
      if (response.body.success) {
        setData(response.body.data);
        setMetadata(response.body.metadata);        
        const totalTime = Date.now() - startTime;
        
        console.log(`✅ Aggregated data loaded in ${totalTime}ms`);
        console.log(`📊 Server processing time: ${response.body.metadata.totalTimeMs}ms`);
        console.log(`📊 Average query time: ${response.body.metadata.avgQueryTime}ms`);
        
        // Log individual query performance
        Object.entries(response.body.data).forEach(([key, result]) => {
          if (result.queryTime) {
            console.log(`  └─ ${key}: ${result.queryTime}ms`);
          }
          if (result.error) {
            console.warn(`  ⚠️ ${key}: ${result.error}`);
          }
        });
        
        // Reset retry count on success
        retryCount.current = 0;
        
      } else {
        throw new Error('Aggregation request failed');
      }
    } catch (error) {
      const totalTime = Date.now() - startTime;
        console.error('💥 Aggregated data fetch failed:', error);
      
      // Retry logic for network errors
      if (retryCount.current < maxRetries && 
          (error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT')) {
        retryCount.current++;
        console.log(`🔄 Retrying aggregated query (attempt ${retryCount.current}/${maxRetries})`);
        
        setTimeout(() => {
          fetchAggregatedData(requests, options);
        }, 1000 * retryCount.current);
        
        return;
      }
      
      setError(error.response?.data?.error || error.message);
      retryCount.current = 0;
    } finally {
      setLoading(false);
    }
  }, []);
  
  const retryLastRequest = useCallback(() => {
    if (metadata.lastRequests) {
      fetchAggregatedData(metadata.lastRequests);
    }
  }, [metadata.lastRequests, fetchAggregatedData]);
  
  const clearData = useCallback(() => {
    setData({});
    setError(null);
    setMetadata({});
    retryCount.current = 0;
  }, []);
  
  return { 
    data, 
    loading, 
    error,
    metadata,
    fetchAggregatedData,
    retryLastRequest,
    clearData,
    hasError: !!error,
    canRetry: retryCount.current < maxRetries
  };
};
