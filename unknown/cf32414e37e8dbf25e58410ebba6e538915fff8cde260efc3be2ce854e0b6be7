import React from "react" ;
import SOMIPEM_COLORS from '../styles/brand-colors';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
  LineController, // Import LineController
  BarController,  // Import BarController
} from 'chart.js';
import { throttle } from 'lodash';

// Throttle chart updates to prevent excessive re-renders
export const throttledUpdateChart = throttle((chart, data) => {
  if (chart && chart.data) {
    chart.data = data;
    chart.update('none'); // Use 'none' mode for fastest updates
  }
}, 500);

// Enregistrer les composants Chart.js nécessaires
export function registerChartComponents() {
  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    Filler,
    ArcElement,
    LineController, // Register LineController
    BarController  // Register BarController
  );
  
  // Set global defaults for better performance
  ChartJS.defaults.font.family = "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif";
  ChartJS.defaults.responsive = true;
  ChartJS.defaults.maintainAspectRatio = false;
  ChartJS.defaults.animation = false; // Disable animations globally
  ChartJS.defaults.devicePixelRatio = 1; // Optimize for performance over quality
}

// Configuration des couleurs pour le mode sombre
export function updateChartColors(darkMode) {
  const gridColor = darkMode ? SOMIPEM_COLORS.DARK.BORDER : SOMIPEM_COLORS.ACCENT_BORDER;
  const textColor = darkMode ? SOMIPEM_COLORS.DARK.TEXT_SECONDARY : SOMIPEM_COLORS.LIGHT_GRAY;
  const backgroundColor = darkMode ? SOMIPEM_COLORS.DARK.BACKGROUND : SOMIPEM_COLORS.WHITE;
  const borderColor = darkMode ? SOMIPEM_COLORS.DARK.BORDER : SOMIPEM_COLORS.ACCENT_BORDER;
  const axisLabelColor = darkMode ? SOMIPEM_COLORS.DARK.TEXT : SOMIPEM_COLORS.DARK_GRAY;
  
  // SOMIPEM brand colors for charts (Blue theme only)
  const chartColors = darkMode ? [
    SOMIPEM_COLORS.DARK.PRIMARY_BLUE,
    SOMIPEM_COLORS.DARK.SECONDARY_BLUE,
    "#60A5FA", // Light blue
    "#3730A3", // Darker blue
    "#1E40AF", // Medium dark blue
    "#2563EB", // Medium blue
    "#6366F1"  // Indigo blue
  ] : [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.CHART_QUATERNARY,
    "#60A5FA", // Light blue
    "#1D4ED8", // Dark blue
    "#3730A3"  // Darker blue
  ];
  
  // Mettre à jour les options globales de Chart.js
  ChartJS.defaults.color = textColor;
  ChartJS.defaults.borderColor = gridColor;
  ChartJS.defaults.scale.grid.color = gridColor;
  ChartJS.defaults.scale.ticks.color = textColor;
  
  // Update tooltip styles
  ChartJS.defaults.plugins.tooltip.backgroundColor = darkMode ? 'rgba(33, 33, 33, 0.9)' : 'rgba(255, 255, 255, 0.9)';
  ChartJS.defaults.plugins.tooltip.titleColor = darkMode ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.95)';
  ChartJS.defaults.plugins.tooltip.bodyColor = textColor;
  ChartJS.defaults.plugins.tooltip.borderColor = borderColor;
  ChartJS.defaults.plugins.tooltip.boxPadding = 6;
  
  // Update legend styles
  ChartJS.defaults.plugins.legend.labels.color = textColor;
  ChartJS.defaults.plugins.legend.labels.boxWidth = 12;
  ChartJS.defaults.plugins.legend.labels.padding = 15;
  
  // Update point styles
  ChartJS.defaults.elements.point.backgroundColor = chartColors[0];
  ChartJS.defaults.elements.point.borderColor = darkMode ? '#141414' : 'white';
  
  // Update line styles
  ChartJS.defaults.elements.line.borderWidth = 2;
  ChartJS.defaults.elements.line.tension = 0.2; // Slight curve for better visual
  
  // Update bar styles
  ChartJS.defaults.elements.bar.backgroundColor = chartColors[0];
  ChartJS.defaults.elements.bar.borderWidth = 0;
  
  // Add CSS variables for chart containers
  if (typeof document !== 'undefined') {
    document.documentElement.style.setProperty('--chart-bg', backgroundColor);
    document.documentElement.style.setProperty('--chart-text', textColor);
    document.documentElement.style.setProperty('--chart-title', axisLabelColor);
    document.documentElement.style.setProperty('--chart-grid', gridColor);
    document.documentElement.style.setProperty('--chart-axis-label', axisLabelColor);
    document.documentElement.style.setProperty('--chart-tooltip-bg', darkMode ? 'rgba(33, 33, 33, 0.9)' : 'rgba(255, 255, 255, 0.9)');
  }
}

// Detect if running on mobile device
const isMobile = window.innerWidth < 768;

// Options de base pour les graphiques avec SOMIPEM branding
export const getBaseChartOptions = (darkMode) => {
  // Define theme-aware colors using SOMIPEM brand palette
  const gridColor = darkMode ? SOMIPEM_COLORS.DARK.BORDER : SOMIPEM_COLORS.ACCENT_BORDER;
  const textColor = darkMode ? SOMIPEM_COLORS.DARK.TEXT_SECONDARY : SOMIPEM_COLORS.LIGHT_GRAY;
  const axisLabelColor = darkMode ? SOMIPEM_COLORS.DARK.TEXT : SOMIPEM_COLORS.DARK_GRAY;
  const backgroundColor = darkMode ? SOMIPEM_COLORS.DARK.BACKGROUND : SOMIPEM_COLORS.WHITE;
  const borderColor = darkMode ? SOMIPEM_COLORS.DARK.BORDER : SOMIPEM_COLORS.ACCENT_BORDER;
  
  // SOMIPEM brand color palette for charts (Blue theme only)
  const chartColors = [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE, 
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.CHART_QUATERNARY,
    "#60A5FA", // Light blue
    "#1D4ED8", // Dark blue
    "#3730A3"  // Darker blue
  ];
  
  // Enhanced shadow for dark mode
  const boxShadow = darkMode ? 
    '0 4px 12px rgba(0, 0, 0, 0.5)' : 
    '0 4px 12px rgba(0, 0, 0, 0.1)';
  
  return {
    responsive: true,
    maintainAspectRatio: false,
    animation: false, // Disable animations for better performance
    devicePixelRatio: 1, // Optimize for performance over quality
    plugins: {
      legend: {
        position: 'top',
        align: 'center',
        labels: {
          color: textColor,
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 10, // Slightly larger legend items for better visibility
          font: {
            weight: 500, // Slightly bolder font for better readability in dark mode
          }
        },
        display: !isMobile, // Hide legend on mobile
      },
      tooltip: {
        enabled: !isMobile || window.innerWidth > 480, // Disable tooltips on small mobile
        backgroundColor: backgroundColor,
        titleColor: darkMode ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.95)',
        bodyColor: textColor,
        borderColor: borderColor,
        borderWidth: 1,
        padding: 10,
        cornerRadius: 6,
        boxPadding: 5,
        displayColors: true, // Show color boxes in tooltip
        boxShadow: boxShadow,
        callbacks: {
          // Limit tooltip text length for better performance
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            label += Math.round(context.parsed.y * 100) / 100;
            return label;
          },
          // Add title formatting
          title: function(tooltipItems) {
            return tooltipItems[0].label;
          }
        }
      },
      title: {
        display: false,
        color: axisLabelColor,
        font: {
          weight: 600,
          size: 16
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false, // Hide x grid lines for cleaner look
          color: gridColor,
          z: -1, // Place grid lines behind data points
        },
        border: {
          color: darkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
        },
        ticks: {
          color: textColor,
          maxRotation: 0, // Don't rotate x-axis labels
          autoSkipPadding: 10, // Ensure labels don't overlap
          maxTicksLimit: isMobile ? 5 : 10, // Limit number of ticks on mobile
          padding: 8, // Add padding between ticks and axis
          font: {
            size: isMobile ? 10 : 12,
          }
        },
        title: {
          display: false,
          color: axisLabelColor,
          font: {
            weight: 500
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: gridColor, // Theme-aware grid lines
          z: -1, // Place grid lines behind data points
          lineWidth: 1,
          drawBorder: true,
        },
        border: {
          color: darkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
        },
        ticks: {
          color: textColor,
          precision: 0, // Use integers only
          maxTicksLimit: isMobile ? 5 : 8, // Limit number of ticks on mobile
          padding: 8, // Add padding between ticks and axis
          font: {
            size: isMobile ? 10 : 12,
          }
        },
        title: {
          display: false,
          color: axisLabelColor,
          font: {
            weight: 500
          }
        }
      },
    },
    elements: {
      point: {
        radius: isMobile ? 0 : 3, // No points on mobile
        hoverRadius: isMobile ? 3 : 6,
        backgroundColor: function(context) {
          // Use the appropriate color from the chartColors array based on dataset index
          const index = context.datasetIndex % chartColors.length;
          return chartColors[index];
        },
        borderColor: darkMode ? '#141414' : 'white',
        borderWidth: 2,
        hoverBorderWidth: 2,
        hoverBorderColor: darkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',
      },
      line: {
        borderWidth: isMobile ? 2 : 3,
        tension: 0.2, // Slight curve for better visual
        fill: false,
        borderColor: function(context) {
          // Use the appropriate color from the chartColors array based on dataset index
          const index = context.datasetIndex % chartColors.length;
          return chartColors[index];
        },
        borderCapStyle: 'round',
      },
      bar: {
        backgroundColor: function(context) {
          // Use SOMIPEM brand colors based on dataset index
          const index = context.datasetIndex % chartColors.length;
          return chartColors[index];
        },
        borderWidth: 0,
        borderRadius: 4,
        hoverBackgroundColor: function(context) {
          // Use secondary blue for hover state (SOMIPEM brand guideline)
          const index = context.datasetIndex % chartColors.length;
          return darkMode ? 
            SOMIPEM_COLORS.DARK.SECONDARY_BLUE : 
            SOMIPEM_COLORS.SECONDARY_BLUE;
        },
      },
    },
  };
};