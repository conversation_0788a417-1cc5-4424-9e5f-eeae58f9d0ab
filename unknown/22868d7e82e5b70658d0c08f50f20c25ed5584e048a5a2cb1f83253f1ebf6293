/**
 * Utility functions for handling API responses and requests
 * @module apiUtils
 */
import React from "react" ;
import request from 'superagent';
import { API_BASE_URL } from './apiConfig';

/**
 * SuperAgent client factory with default configuration
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @returns {SuperAgent} Configured SuperAgent request
 */
export const createApiClient = (method, url) => {
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;

  return request[method.toLowerCase()](fullUrl)
    .timeout(30000) // 30 seconds
    .retry(2)
    .set('Content-Type', 'application/json')
    .set('Accept', 'application/json')
    .withCredentials(); // ✅ FIXED: Correct SuperAgent syntax for HTTP-only cookies
  // Note: No Authorization header needed - backend uses HTTP-only cookies
};

/**
 * Enhanced SuperAgent wrapper with error handling
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {Object} options - Request options
 * @returns {Promise} SuperAgent request promise
 */
export const apiRequest = async (method, url, options = {}) => {
  const { data, query, headers = {} } = options;
  
  let req = createApiClient(method, url);
  
  // Add custom headers
  Object.entries(headers).forEach(([key, value]) => {
    req = req.set(key, value);
  });
  
  // Add query parameters
  if (query) {
    req = req.query(query);
  }
  
  // Add request body
  if (data) {
    req = req.send(data);
  }
  
  try {
    const response = await req;
    return {
      data: response.body,
      status: response.status,
      headers: response.headers,
    };
  } catch (error) {
    // Handle SuperAgent errors
    if (error.response) {
      // Handle 401 Unauthorized errors
      if (error.response.status === 401) {
        // Check if it's not a login request
        if (!url.includes('/login')) {
          // Clear local storage
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          
          // Redirect to login page if not already there
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }
      }
      
      const apiError = new Error(error.message);
      apiError.response = {
        data: error.response.body,
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
      };
      throw apiError;
    }
    
    // Handle network errors
    throw error;
  }
};

/**
 * Extracts data from API response, handling both old and new response formats
 * @param {Object} response - The API response object
 * @returns {Array|Object} The extracted data
 */
export const extractResponseData = (response) => {
  if (!response) return null;

  // SuperAgent puts response data in response.body
  const responseData = response.body || response.data;

  // Handle new response format (with success and data properties)
  if (responseData && typeof responseData === 'object' && 'success' in responseData) {
    return responseData.data;
  }

  // Handle old response format (data directly in response body/data)
  return responseData;
};

/**
 * Checks if an API response is successful
 * @param {Object} response - The API response object
 * @returns {boolean} Whether the response is successful
 */
export const isResponseSuccessful = (response) => {
  if (!response) return false;

  // SuperAgent puts response data in response.body
  const responseData = response.body || response.data;

  // Handle new response format
  if (responseData && typeof responseData === 'object' && 'success' in responseData) {
    return Boolean(responseData.success);
  }

  // Handle old response format (assume success if status is 2xx)
  return response.status >= 200 && response.status < 300;
};

/**
 * Gets error message from API response
 * @param {Object} error - The error object
 * @returns {string} The error message
 */
export const getErrorMessage = (error) => {
  // Network errors
  if (error.message === 'Network Error') {
    return 'Unable to connect to the server. Please check your internet connection.';
  }
  
  // Timeout errors
  if (error.code === 'ECONNABORTED') {
    return 'The request timed out. Please try again.';
  }
  
  // Handle new response format
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  // Handle old response format
  if (error.response?.data?.error) {
    return error.response.data.error;
  }
  
  // Handle validation errors (array of errors)
  if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
    return error.response.data.errors.map(err => err.msg || err.message).join(', ');
  }
  
  // HTTP status based messages
  if (error.response) {
    switch (error.response.status) {
      case 400: return 'Bad request. Please check your input.';
      case 401: return 'Unauthorized. Please log in again.';
      case 403: return 'Forbidden. You do not have permission to access this resource.';
      case 404: return 'Resource not found.';
      case 500: return 'Internal server error. Please try again later.';
      default: return `Error ${error.response.status}: ${error.response.statusText}`;
    }
  }
  
  // Default error message
  return error.message || 'An unexpected error occurred. Please try again.';
};

/**
 * Handles API errors consistently
 * @param {Object} error - The error object
 * @param {Function} [notifyFn] - Optional notification function
 * @returns {Object} Error details object
 */
export const handleApiError = (error, notifyFn = null) => {
  const errorMessage = getErrorMessage(error);
  
  // If notification function is provided, use it
  if (notifyFn && typeof notifyFn === 'function') {
    notifyFn({
      message: 'Error',
      description: errorMessage,
      type: 'error',
    });
  }
  
  // Log error in development
  if (process.env.NODE_ENV !== 'production') {
    console.error('API Error:', error);
  }
  
  return {
    error: true,
    message: errorMessage,
    status: error.response?.status || 0,
    data: error.response?.data || null,
  };
};

/**
 * Creates a cached API request function to prevent duplicate requests
 * @param {Function} requestFn - The API request function
 * @param {number} [cacheTime=60000] - Cache time in milliseconds (default: 1 minute)
 * @returns {Function} Cached request function
 */
export const createCachedRequest = (requestFn, cacheTime = 60000) => {
  const cache = new Map();
  
  return async (...args) => {
    const cacheKey = JSON.stringify(args);
    const cachedItem = cache.get(cacheKey);
    
    // Return cached response if valid
    if (cachedItem && Date.now() - cachedItem.timestamp < cacheTime) {
      return cachedItem.response;
    }
    
    // Make the actual request
    try {
      const response = await requestFn(...args);
      
      // Cache the response
      cache.set(cacheKey, {
        response,
        timestamp: Date.now(),
      });
      
      return response;
    } catch (error) {
      // Don't cache errors
      throw error;
    }
  };
};

/**
 * Debounces an API request function
 * @param {Function} requestFn - The API request function
 * @param {number} [delay=300] - Debounce delay in milliseconds
 * @returns {Function} Debounced request function
 */
export const debounceRequest = (requestFn, delay = 300) => {
  let timeoutId;
  
  return (...args) => {
    return new Promise((resolve, reject) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      timeoutId = setTimeout(async () => {
        try {
          const result = await requestFn(...args);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);
    });
  };
};
