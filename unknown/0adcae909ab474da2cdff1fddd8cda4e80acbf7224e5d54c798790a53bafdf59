import dayjs from 'dayjs'
import React from "react" ;
/**
 * Data Processing Module
 * Handles data transformations and processing for the Arret dashboard
 */

/**
 * Comprehensive date parsing function to handle multiple date formats from database
 * @param {string} dateStr - Date string from database
 * @returns {dayjs.Dayjs|null} Parsed dayjs object or null if invalid
 */
export const parseDate = (dateStr) => {
  if (!dateStr) return null;

  try {
    const str = String(dateStr).trim();
    
    // Handle DD/MM/YYYY HH:MM:SS format (most common from GraphQL)
    if (str.includes('/')) {
      const parts = str.split(' ');
      const datePart = parts[0]; // "DD/MM/YYYY"
      const timePart = parts[1] || '00:00:00'; // "HH:MM:SS" or default
      
      const [day, month, year] = datePart.split('/');
      
      if (day && month && year && 
          day.length <= 2 && month.length <= 2 && year.length === 4) {
        
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        
        // Create ISO format: YYYY-MM-DDTHH:MM:SS
        const isoString = `${year}-${paddedMonth}-${paddedDay}T${timePart}`;
        const parsed = dayjs(isoString);
        
        if (parsed.isValid()) {
          return parsed;
        }
      }
    }
    
    // Handle YYYY HH:MM:SS-MM-DD format (alternative database format)
    if (str.includes('-') && str.includes(' ')) {
      const spaceIndex = str.indexOf(' ');
      const year = str.substring(0, spaceIndex);
      const remaining = str.substring(spaceIndex + 1);
      
      if (remaining.includes('-')) {
        const dashIndex = remaining.lastIndexOf('-');
        const time = remaining.substring(0, dashIndex);
        const monthDay = remaining.substring(dashIndex + 1);
        
        if (monthDay.includes('-')) {
          const [month, day] = monthDay.split('-');
          if (year && month && day && time) {
            const isoString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${time}`;
            const parsed = dayjs(isoString);
            
            if (parsed.isValid()) {
              return parsed;
            }
          }
        }
      }
    }
    
    // Standard ISO format fallback
    const parsed = dayjs(str);
    if (parsed.isValid()) {
      return parsed;
    }
    
    return null;
  } catch (error) {
    console.warn('Date parsing error:', error, 'for date:', dateStr);
    return null;
  }
}

/**
 * Transform GraphQL sidecards data to expected stats format
 * @param {Object} sidecards - GraphQL sidecards data (object with Arret_Totale, Arret_Totale_nondeclare)
 * @returns {Array} Transformed stats data
 */
export const transformSidecardsToStats = (sidecards = {}) => {
  if (!sidecards || typeof sidecards !== 'object') {
    return [];
  }

  try {
    // Handle the actual GraphQL sidecards structure - CONVERT STRINGS TO NUMBERS
    const stats = [
      {
        id: 1,
        title: "Total Arrêts",
        value: parseInt(sidecards.Arret_Totale) || 0,
        subtitle: '',
        icon: 'AlertOutlined',
        color: '#f5222d'
      },
      {
        id: 2,
        title: "Arrêts Non Déclarés",
        value: parseInt(sidecards.Arret_Totale_nondeclare) || 0,
        subtitle: '',
        icon: 'WarningOutlined',
        color: '#faad14'
      }
    ];

    return stats;

  } catch (error) {
    console.error('❌ Error transforming sidecards:', error);
    return [];
  }
}

/**
 * Transform and enhance stops data with daily table context
 * @param {Array} stopsData - Raw stops data
 * @param {Array} dailyData - Daily table data for context
 * @returns {Array} Enhanced stops data
 */
export const enhanceStopsData = (stopsData = [], dailyData = []) => {
  if (!stopsData || stopsData.length === 0) {
    return [];
  }

  try {
    const enhanced = stopsData.map(stop => {
      const enhanced = { ...stop };

      // Add daily table data context if available
      if (dailyData && dailyData.length > 0) {
        // Find matching daily record for production context
        // Use GraphQL field names: Machine_Name and Date_Insert
        const machineField = stop.Machine_Name || stop.nom_machine;
        const dateField = stop.Date_Insert || stop.date_arret;
        
        const matchingDaily = dailyData.find(daily => 
          daily.nom_machine === machineField &&
          dayjs(daily.date_arret).format('YYYY-MM-DD') === dayjs(dateField).format('YYYY-MM-DD')
        );

        if (matchingDaily) {
          enhanced.dailyContext = matchingDaily;
          enhanced.productionTarget = matchingDaily.objectif_production;
          enhanced.actualProduction = matchingDaily.production_reelle;
          enhanced.efficiency = matchingDaily.rendement;
        }
      }

      return enhanced;
    });

    return enhanced;

  } catch (error) {
    console.error('❌ Error enhancing stops data:', error);
    return stopsData;
  }
}

/**
 * Group stops data by date for chart visualization using comprehensive date parsing
 * @param {Array} stopsData - Stops data array
 * @returns {Object} Grouped data by date
 */
export const groupStopsByDate = (stopsData = []) => {
  if (!stopsData || stopsData.length === 0) {
    return {};
  }

  try {
    const grouped = stopsData.reduce((acc, stop) => {
      // Use GraphQL field names - Date_Insert instead of date_arret
      const dateField = stop.Date_Insert || stop.date_arret; // Try both for compatibility
      
      let dateKey = 'unknown';
      if (dateField) {
        const parsedDate = parseDate(dateField);
        if (parsedDate && parsedDate.isValid()) {
          dateKey = parsedDate.format('YYYY-MM-DD');
        }
      }

      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(stop);
      return acc;
    }, {});

    return grouped;

  } catch (error) {
    console.error('❌ Error grouping stops by date:', error);
    return {};
  }
}

/**
 * Transform grouped stops data to chart format
 * @param {Object} groupedStops - Stops grouped by date
 * @returns {Array} Chart data format
 */
export const transformToChartData = (groupedStops = {}) => {
  try {
    const chartData = Object.entries(groupedStops).map(([date, stops]) => {
      // date is in format "YYYY-MM-DD", convert to displayDate
      const displayDate = dayjs(date).format('DD/MM/YYYY');
      
      return {
        date: displayDate,
        dateKey: date,
        stops: stops.length,
        totalDuration: stops.reduce((sum, stop) => {
          // Calculate duration in minutes
          if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            return sum + (hours * 60) + minutes;
          }
          return sum;
        }, 0),
        details: stops
      };
    }).sort((a, b) => dayjs(a.dateKey).diff(dayjs(b.dateKey)));

    return chartData;

  } catch (error) {
    console.error('❌ Error transforming to chart data:', error);
    return [];
  }
}

/**
 * Process comprehensive GraphQL data and extract all components
 * @param {Object} comprehensiveData - Data from getFinalComprehensiveStopData
 * @returns {Object} Processed data for all dashboard components
 */
export const processComprehensiveData = (comprehensiveData) => {
  if (!comprehensiveData) {
    return {
      sidecards: [],
      stopsData: [],
      chartData: [],
      groupedData: {},
      isEmpty: true
    };
  }

  try {
    // Map GraphQL response to expected format - FIXED: sidecards is an object, not array
    const { sidecards = {}, allStops = [] } = comprehensiveData;



    // Transform sidecards data - FIXED: pass object instead of array
    const transformedStats = transformSidecardsToStats(sidecards);

    // Group stops by date (using allStops instead of stopsData)
    const groupedStops = groupStopsByDate(allStops);

    // Transform to chart data format
    const chartData = transformToChartData(groupedStops);

    const result = {
      sidecards: transformedStats,
      stopsData: allStops, // Use allStops as stopsData
      chartData,
      groupedData: groupedStops,
      isEmpty: allStops.length === 0
    };


    return result;

  } catch (error) {
    console.error('❌ Error processing comprehensive data:', error);
    return {
      sidecards: [],
      stopsData: [],
      chartData: [],
      groupedData: {},
      isEmpty: true
    };
  }
}

/**
 * Extract top stops data from processed stops
 * @param {Array} stopsData - Processed stops data
 * @param {number} limit - Number of top stops to return
 * @returns {Array} Top stops data
 */
export const extractTopStops = (stopsData = [], limit = 5) => {
  if (!stopsData || stopsData.length === 0) {
    return [];
  }

  try {
    // Group by stop reason and calculate frequency
    const reasonCounts = stopsData.reduce((acc, stop) => {
      const reason = stop.type_arret || stop.raison_arret || 'Unknown';
      if (!acc[reason]) {
        acc[reason] = {
          reason,
          count: 0,
          totalDuration: 0,
          stops: []
        };
      }
      acc[reason].count += 1;
      acc[reason].stops.push(stop);
      
      // Add duration if available
      if (stop.duree_arret) {
        const parts = stop.duree_arret.split(':');
        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        acc[reason].totalDuration += (hours * 60) + minutes;
      }
      
      return acc;
    }, {});

    // Sort by count and take top N
    const topStops = Object.values(reasonCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
      .map((item, index) => ({
        id: index + 1,
        reason: item.reason,
        count: item.count,
        totalDuration: item.totalDuration,
        percentage: Math.round((item.count / stopsData.length) * 100),
        stops: item.stops
      }));

    return topStops;

  } catch (error) {
    console.error('❌ Error extracting top stops:', error);
    return [];
  }
}
