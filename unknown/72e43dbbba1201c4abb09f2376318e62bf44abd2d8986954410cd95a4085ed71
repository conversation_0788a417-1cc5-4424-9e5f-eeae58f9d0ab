import React from "react" ;
import { useCallback, useRef, useEffect } from 'react'
import { message } from 'antd'
import { processComprehensiveData, extractTopStops, enhanceStopsData } from './dataProcessing.jsx'
import { 
  calculatePerformanceMetrics, 
  calculateAvailabilityTrendData, 
  calculateMTTRCalendarData, 
  calculateDowntimeParetoData 
} from './performanceCalculations.jsx'
import { DEBOUNCE_DELAYS, CIRCUIT_BREAKER } from './constants.jsx'

/**
 * Data Manager Module
 * Integrates with the optimized useStopTableGraphQL hook
 * Handles all data fetching, processing, and state management
 */
export const useDataManager = (
  graphQL,
  state,
  setState,
  skeletonManager
) => {
  // Refs for optimization and debouncing
  const pendingFetch = useRef(false)
  const lastFilterChange = useRef(Date.now())
  const circuitBreakerCount = useRef(0)
  const debounceTimeout = useRef(null)

  /**
   * Main data fetching function using the optimized GraphQL hook
   */
  const fetchData = useCallback(async (forceRefresh = false) => {
    const now = Date.now();
    
    // Determine debounce delay based on filter complexity
    const hasAllFilters = state.selectedMachineModel && state.selectedMachine && state.selectedDate;
    const debounceDelay = hasAllFilters ? DEBOUNCE_DELAYS.COMPLEX : 
                         (state.selectedMachine || state.selectedDate ? DEBOUNCE_DELAYS.MEDIUM : DEBOUNCE_DELAYS.SIMPLE);
    
    // Prevent rapid successive calls
    if (pendingFetch.current && !forceRefresh) {
      return;
    }

    // Smart throttling based on filter complexity
    if (!forceRefresh && (now - lastFilterChange.current) < debounceDelay) {
      return;
    }


    // Circuit breaker pattern
    if (circuitBreakerCount.current >= CIRCUIT_BREAKER.THRESHOLD) {
      message.error('Too many failed requests. Please refresh the page.');
      return;
    }

    // Mark fetch as pending
    pendingFetch.current = true;
    lastFilterChange.current = now;

    try {
      // Start appropriate skeleton loading
      skeletonManager.smartSkeletonForFilters(
        !!state.selectedMachineModel,
        !!state.selectedMachine,
        !!state.selectedDate
      );

      // Update loading states - detect complex filter scenario
      const hasComplexFilters = state.selectedMachineModel && state.selectedMachine && state.selectedDate;
      setState(prev => ({
        ...prev,
        loading: true,
        essentialLoading: true,
        complexFilterLoading: hasComplexFilters,
        error: null
      }));

      // Build filters for the GraphQL hook - FIXED parameter names
      const filters = {
        model: state.selectedMachineModel || null,
        machine: state.selectedMachine || null,
        startDate: state.selectedDate ? state.selectedDate.startOf(state.dateRangeType).format('YYYY-MM-DD') : null,
        endDate: state.selectedDate ? state.selectedDate.endOf(state.dateRangeType).format('YYYY-MM-DD') : null,
        dateRangeType: state.dateRangeType || 'day'
      };


      // Use the optimized GraphQL hook for comprehensive data
      const comprehensiveResult = await graphQL.getComprehensiveStopData(filters);
      
      if (!comprehensiveResult) {
        throw new Error('Failed to fetch comprehensive data');
      }

      

      // Process the comprehensive data (pass the result directly)
      const processedData = processComprehensiveData(comprehensiveResult);

      if (processedData.isEmpty) {
        
        // Set empty data arrays with proper structure
        setState(prev => ({
          ...prev,
          arretStats: [],
          topStopsData: [],
          arretsByRange: [],
          filteredArretsByRange: [],
          stopsData: [],
          rawChartData: [],
          durationTrend: [],
          stopReasons: [],
          mttr: 0,
          mtbf: 0,
          doper: state.selectedMachine ? 0 : 100, // Show 100% availability when no machine selected
          showPerformanceMetrics: !!state.selectedMachine,
          loading: false,
          essentialLoading: false
        }));

        skeletonManager.progressiveSkeletonClear();
        return;
      }

      // Enhance stops data (no daily table context needed for now)
      const enhancedStopsData = enhanceStopsData(
        processedData.stopsData, 
        [] // Empty array for daily data context
      );

      // Extract top stops data
      const topStopsData = extractTopStops(enhancedStopsData, 5);

      // Calculate performance metrics
      const performanceMetrics = calculatePerformanceMetrics(
        enhancedStopsData,
        state.dateRangeType,
        state.selectedDate
      );

      // Calculate advanced analytics data (only when machine is selected)
      let disponibiliteTrendData = [];
      let mttrCalendarData = [];
      let downtimeParetoData = [];
      
      if (state.selectedMachine && enhancedStopsData.length > 0) {
        console.log('🔧 Calculating advanced analytics for machine:', state.selectedMachine);
        
        // Calculate availability trend data
        disponibiliteTrendData = calculateAvailabilityTrendData(
          enhancedStopsData,
          state.dateRangeType
        );
        
        // Calculate MTTR calendar data
        mttrCalendarData = calculateMTTRCalendarData(enhancedStopsData);
        
        // Calculate downtime Pareto data
        downtimeParetoData = calculateDowntimeParetoData(enhancedStopsData);
        
        console.log('🔧 Advanced analytics calculated:', {
          disponibiliteTrendData: disponibiliteTrendData.length,
          mttrCalendarData: mttrCalendarData.length,
          downtimeParetoData: downtimeParetoData.length
        });
      }

      // Update all state with processed data
      setState(prev => ({
        ...prev,
        // Stats and overview data - ensure arretStats is properly formatted
        arretStats: processedData.sidecards && processedData.sidecards.length > 0 
          ? processedData.sidecards  // Keep original format from GraphQL
          : [], // Empty array fallback
        topStopsData,
        
        // Chart and visualization data
        arretsByRange: processedData.chartData,
        filteredArretsByRange: processedData.chartData,
        chartData: processedData.chartData, // Add the missing chartData field for ArretChartsSection
        stopsData: enhancedStopsData,
        rawChartData: processedData.chartData,
        durationTrend: processedData.chartData,
        
        // Performance metrics
        mttr: performanceMetrics.mttr,
        mtbf: performanceMetrics.mtbf,
        doper: performanceMetrics.doper,
        showPerformanceMetrics: !!state.selectedMachine,
        
        // Advanced analytics data
        disponibiliteTrendData,
        mttrCalendarData,
        downtimeParetoData,
        
        // Loading states
        loading: false,
        essentialLoading: false,
        complexFilterLoading: false,
        error: null
      }));

      

      // Progressive skeleton clearing for better UX
      skeletonManager.progressiveSkeletonClear();

      // Reset circuit breaker on success
      circuitBreakerCount.current = 0;


    } catch (error) {
      console.error('❌ Error in fetchData:', error);
      
      // Increment circuit breaker
      circuitBreakerCount.current += 1;

      // Try to use cached data as fallback
      const fallbackData = await graphQL.getCacheStats(); // Use available cache method
    
      
      // Set empty data to prevent infinite loading
      setState(prev => ({
        ...prev,
        arretStats: [],
        topStopsData: [],
        stopsData: [],
        loading: false,
        essentialLoading: false,
        complexFilterLoading: false,
        error: error.message || 'Failed to fetch data'
      }));

      // Clear skeletons and show error
      skeletonManager.stopSkeletonLoading();
      message.error(`Failed to load data: ${error.message}`);

    } finally {
      pendingFetch.current = false;
    }
  }, [
    state.selectedMachineModel,
    state.selectedMachine, 
    state.selectedDate,
    state.dateRangeType,
    graphQL,
    setState,
    skeletonManager
  ]);

  /**
   * Debounced version of fetchData for filter changes
   */
  const debouncedFetchData = useCallback((forceRefresh = false) => {
    // Clear any existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    // Determine delay based on filter complexity
    const hasAllFilters = state.selectedMachineModel && state.selectedMachine && state.selectedDate;
    const delay = hasAllFilters ? DEBOUNCE_DELAYS.COMPLEX :
                 (state.selectedMachine || state.selectedDate ? DEBOUNCE_DELAYS.MEDIUM : DEBOUNCE_DELAYS.SIMPLE);

    // Set new timeout
    debounceTimeout.current = setTimeout(() => {
      if (Date.now() - lastFilterChange.current >= delay * 0.8) {
        lastFilterChange.current = Date.now();
        fetchData(forceRefresh);
      }
    }, delay);

    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, [fetchData, state.selectedMachineModel, state.selectedMachine, state.selectedDate]);

  /**
   * Fetch machine models using the optimized hook
   */
  const fetchMachineModels = useCallback(async () => {
    try {
      // Use the main optimized method directly
      const result = await graphQL.getMachineModels();
      if (result && Array.isArray(result)) {
        setState(prev => ({ ...prev, machineModels: result }));
      } else {
        // Fallback data if result is not as expected
        const fallbackModels = ['IPS', 'AKROS', 'ML', 'FCS'];
        setState(prev => ({ ...prev, machineModels: fallbackModels }));
      }
    } catch (error) {
      console.error('❌ Error fetching machine models:', error);
      // Fallback data in case of error
      const fallbackModels = ['IPS', 'AKROS', 'ML', 'FCS'];
      setState(prev => ({ ...prev, machineModels: fallbackModels }));
    }
  }, [graphQL, setState]);

  /**
   * Fetch machine names using the optimized hook
   */
  const fetchMachineNames = useCallback(async (modelFilter = null) => {
    try {
      // Convert modelFilter to proper filters object format
      const filters = modelFilter ? { model: modelFilter } : {};
      const result = await graphQL.getMachineNames(filters);
      if (result && Array.isArray(result)) {
        
        if (modelFilter) {
          // If a model filter was provided, update filteredMachineNames
          setState(prev => ({ 
            ...prev, 
            filteredMachineNames: result,
            // Also update the full list if it's empty
            machineNames: prev.machineNames.length === 0 ? result : prev.machineNames 
          }));
        } else {
          // If no model filter, update the full list
          setState(prev => ({ 
            ...prev, 
            machineNames: result,
            // Also update filtered names if they match the current model or if none is selected
            filteredMachineNames: !prev.selectedMachineModel ? result : prev.filteredMachineNames
          }));
        }
      } else {
        // Set empty array as fallback
        if (modelFilter) {
          setState(prev => ({ ...prev, filteredMachineNames: [] }));
        } else {
          setState(prev => ({ ...prev, machineNames: [] }));
        }
      }
    } catch (error) {
      console.error('❌ Error fetching machine names:', error);
      // Set empty array in case of error
      if (modelFilter) {
        setState(prev => ({ ...prev, filteredMachineNames: [] }));
      } else {
        setState(prev => ({ ...prev, machineNames: [] }));
      }
    }
  }, [graphQL, setState]);

  return {
    fetchData,
    debouncedFetchData,
    fetchMachineModels,
    fetchMachineNames
  };
}
