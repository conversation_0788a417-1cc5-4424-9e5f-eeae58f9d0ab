/**
 * Request Cache Utility
 * Provides client-side caching for API requests to reduce redundant calls
 */
import React from "react" ;
import request from 'superagent';

// Enhanced request cache with intelligent cache management
class RequestCache {
  constructor() {
    this.cache = new Map();
    this.accessTimes = new Map();
    this.hitCount = 0;
    this.missCount = 0;
    this.maxSize = 100; // Maximum cache entries
    this.defaultTTL = 300000; // 5 minutes default
    
    // Start cache maintenance
    this.startMaintenance();
  }

  /**
   * Generate cache key from URL and parameters
   */
  generateKey(url, params = {}, method = 'GET') {
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}=${JSON.stringify(params[key])}`)
      .join('&');
    return `${method}:${url}${paramString ? '?' + paramString : ''}`;
  }

  /**
   * Get cached response if valid
   */
  get(url, params = {}, method = 'GET') {
    const key = this.generateKey(url, params, method);
    const cached = this.cache.get(key);
    
    if (!cached) {
      this.missCount++;
      return null;
    }
    
    // Check if cache is still valid
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      this.missCount++;
      return null;
    }
    
    this.hitCount++;
    console.log(`🎯 Cache HIT: ${key}`);
    return cached.data;
  }

  /**
   * Store response in cache
   */
  set(url, params = {}, method = 'GET', data, ttl = null) {
    const key = this.generateKey(url, params, method);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      expiresAt: Date.now() + (ttl || this.defaultTTL)
    });
    this.accessTimes.set(key, Date.now());
    
    console.log(`💾 Cache SET: ${key}`);
  }

  /**
   * Clear cache entries matching pattern
   */
  clearPattern(pattern) {
    const keys = Array.from(this.cache.keys());
    const clearedKeys = keys.filter(key => key.includes(pattern));
    
    clearedKeys.forEach(key => this.cache.delete(key));
    clearedKeys.forEach(key => this.accessTimes.delete(key));
    console.log(`🗑️ Cache CLEAR: ${clearedKeys.length} entries for pattern "${pattern}"`);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
    this.accessTimes.clear();
    console.log('🗑️ Cache CLEAR: All entries');
  }

  // Intelligent cache eviction based on LRU + TTL
  evictExpiredAndLeastUsed() {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    
    // Remove expired entries first
    entries.forEach(([key, entry]) => {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        this.accessTimes.delete(key);
      }
    });
    
    // If still over capacity, remove least recently used
    if (this.cache.size > this.maxSize) {
      const sortedByAccess = Array.from(this.accessTimes.entries())
        .sort((a, b) => a[1] - b[1]);
      
      const toRemove = sortedByAccess.slice(0, this.cache.size - this.maxSize);
      toRemove.forEach(([key]) => {
        this.cache.delete(key);
        this.accessTimes.delete(key);
      });
    }
  }

  // Predictive prefetching for common patterns
  prefetchRelatedData(key) {
    // Identify common access patterns
    const relatedKeys = this.findRelatedKeys(key);
    
    relatedKeys.forEach(relatedKey => {
      if (!this.cache.has(relatedKey)) {
        // Trigger background fetch for related data
        this.backgroundFetch(relatedKey);
      }
    });
  }

  findRelatedKeys(key) {
    // Simple pattern matching for related dashboard data
    const patterns = {
      'production': ['machines', 'stops', 'topMachines'],
      'machines': ['production', 'performance'],
      'stops': ['production', 'recentStops']
    };
    
    return Object.keys(patterns).reduce((related, pattern) => {
      if (key.includes(pattern)) {
        return [...related, ...patterns[pattern]];
      }
      return related;
    }, []);
  }

  async backgroundFetch(key) {
    // This would be implemented with actual API calls
    console.log(`🔄 Background prefetch for: ${key}`);
  }

  // Cache warming for critical data
  warmCache(criticalEndpoints = []) {
    criticalEndpoints.forEach(endpoint => {
      if (!this.cache.has(endpoint.key)) {
        this.backgroundFetch(endpoint.key);
      }
    });
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0;
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  estimateMemoryUsage() {
    let totalSize = 0;
    this.cache.forEach((entry) => {
      totalSize += JSON.stringify(entry.data).length;
    });
    return Math.round(totalSize / 1024); // KB
  }

  startMaintenance() {
    // Run cache maintenance every 5 minutes
    setInterval(() => {
      this.evictExpiredAndLeastUsed();
      console.log(`🧹 Cache maintenance: ${this.cache.size}/${this.maxSize} entries`);
    }, 300000);
  }
}

// Create global cache instance
const requestCache = new RequestCache();

/**
 * Enhanced SuperAgent wrapper with caching
 */
export const cachedRequest = async (url, options = {}) => {
  const {
    method = 'GET',
    params = {},
    data,
    headers = {},
    cache = true,
    cacheTTL = 30000,
    priority = 'medium',
    retries = 1,
    timeout = 10000,
    ...superagentOptions
  } = options;

  // Try cache first for GET requests
  if (method === 'GET' && cache) {
    const cached = requestCache.get(url, params, method);
    if (cached) {
      return Promise.resolve({ data: cached, fromCache: true });
    }
  }

  // Performance tracking
  const startTime = Date.now();
  console.log(`🚀 API Request: ${method} ${url}${params ? ' with params' : ''}`);

  try {
    // Make the actual request with retry logic
    let lastError = null;
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        let req = request[method.toLowerCase()](url)
          .timeout(timeout)
          .retry(retries);

        // Add query parameters
        if (params && Object.keys(params).length > 0) {
          req = req.query(params);
        }

        // Add headers
        Object.entries(headers).forEach(([key, value]) => {
          req = req.set(key, value);
        });

        // Add request body for non-GET requests
        if (data && method !== 'GET') {
          req = req.send(data);
        }

        // Apply any additional SuperAgent options
        Object.entries(superagentOptions).forEach(([key, value]) => {
          if (typeof req[key] === 'function') {
            req = req[key](value);
          }
        });

        const response = await req;

        const duration = Date.now() - startTime;
        console.log(`✅ API Success: ${url} (${duration}ms)`);

        // Performance warning for slow requests
        if (duration > 3000) {
          console.warn(`🐌 SLOW REQUEST: ${url} took ${duration}ms`);
        }

        // Cache successful GET responses
        if (method === 'GET' && cache && response.status === 200) {
          requestCache.set(url, params, method, response.body, cacheTTL);
        }

        return { 
          data: response.body,
          status: response.status,
          headers: response.headers,
          fromCache: false 
        };
      } catch (error) {
        lastError = error;
        if (attempt < retries) {
          console.warn(`🔄 Retry ${attempt + 1}/${retries} for ${url}`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
    throw lastError;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ API Error: ${url} (${duration}ms)`, error.message);
    throw error;
  }
};

/**
 * Batch API requests with priority and concurrency control
 */
export const batchRequests = async (requests, options = {}) => {
  const {
    concurrency = 3,
    delayBetweenBatches = 500,
    priorityOrder = ['high', 'medium', 'low']
  } = options;

  // Group requests by priority
  const prioritizedRequests = priorityOrder.reduce((acc, priority) => {
    acc[priority] = requests.filter(req => req.priority === priority);
    return acc;
  }, {});

  const results = [];

  // Process each priority level
  for (const priority of priorityOrder) {
    const priorityRequests = prioritizedRequests[priority] || [];
    if (priorityRequests.length === 0) continue;

    console.log(`📊 Processing ${priorityRequests.length} ${priority} priority requests`);

    // Process in batches
    for (let i = 0; i < priorityRequests.length; i += concurrency) {
      const batch = priorityRequests.slice(i, i + concurrency);
      
      const batchPromises = batch.map(request => 
        cachedRequest(request.url, request.options)
          .catch(error => ({ error, request }))
      );

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);

      // Delay between batches (except for last batch)
      if (i + concurrency < priorityRequests.length) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }
  }

  return results;
};

/**
 * Smart data fetching hook for dashboards
 */
export const useSmartDataFetching = (requests, dependencies = []) => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [metrics, setMetrics] = useState({
    totalRequests: 0,
    successfulRequests: 0,
    cachedRequests: 0,
    totalLoadTime: 0
  });

  const fetchData = useCallback(async () => {
    const startTime = Date.now();
    setLoading(true);
    setError(null);

    try {
      const results = await batchRequests(requests);
      const newData = {};
      let successCount = 0;
      let cacheCount = 0;

      results.forEach((result, index) => {
        const requestKey = requests[index].key || `request_${index}`;
        
        if (result.status === 'fulfilled' && result.value.data) {
          newData[requestKey] = result.value.data;
          successCount++;
          if (result.value.fromCache) cacheCount++;
        } else {
          console.warn(`Request ${requestKey} failed:`, result.reason);
        }
      });

      setData(newData);
      setMetrics({
        totalRequests: requests.length,
        successfulRequests: successCount,
        cachedRequests: cacheCount,
        totalLoadTime: Date.now() - startTime
      });

    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, dependencies);

  return { data, loading, error, metrics, refetch: fetchData };
};

export { requestCache };
export default cachedRequest;
