version: '3.8'

services:
  # Backend Service (Working version without canvas)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.working
    container_name: locql-backend
    ports:
      - "5000:5000"
    env_file:
      - docker.env
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development hot reload, excluding node_modules
      - ./backend:/app
      - /app/node_modules  # Anonymous volume to preserve container's node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locql-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:5000
    volumes:
      # Mount source code for development hot reload, excluding node_modules
      - ./frontend:/app
      - /app/node_modules  # Anonymous volume to preserve container's node_modules
    networks:
      - locql-network
    restart: unless-stopped
    depends_on:
      - backend
    # Add extra hosts for compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network

# No named volumes needed - using anonymous volumes for node_modules
