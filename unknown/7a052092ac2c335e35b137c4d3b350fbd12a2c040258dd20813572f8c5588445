# Docker Compose for LOCQL Unified Container - Local Development
# Pulls image from Docker Hub for local development deployment

services:
  locql:
    image: mayahinasr/locql-unified:latest
    container_name: locql-local
    ports:
      - "5000:5000"
    environment:
      # Application Environment
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=5000

      # Database Configuration (using host.docker.internal for local MySQL)
      - DB_HOST=${DB_HOST:-host.docker.internal}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_NAME:-locql_db}

      # Authentication
      - JWT_SECRET=${JWT_SECRET:-locql_development_secret_key_change_in_production}
      
      # CORS Configuration
      - CORS_ORIGINS=http://localhost:5000,http://127.0.0.1:5000
      
      # API Configuration (will auto-detect local environment)
      - VITE_API_URL=http://localhost:5000
      
      # WebSocket Configuration
      - WS_EXTERNAL_URL=ws://localhost:5000
      
      
      # Development Features
      - DEBUG=true
      - LOG_LEVEL=debug
      
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Resource limits for local development
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.1'
    
    # Volume mounts for development (optional - for logs)
    volumes:
      - ./logs:/app/logs
    
    # Network configuration
    networks:
      - locql-network

networks:
  locql-network:
    driver: bridge

# Optional: Add a MySQL service for complete local development
# Uncomment the following if you want to run MySQL in Docker as well
#
# services:
#   mysql:
#     image: mysql:8.0
#     container_name: locql-mysql
#     environment:
#       - MYSQL_ROOT_PASSWORD=rootpassword
#       - MYSQL_DATABASE=your_db_name
#       - MYSQL_USER=your_db_user
#       - MYSQL_PASSWORD=your_db_password
#     ports:
#       - "3306:3306"
#     volumes:
#       - mysql_data:/var/lib/mysql
#       - ./database.sql:/docker-entrypoint-initdb.d/database.sql
#     networks:
#       - locql-network
#     restart: unless-stopped
#
# volumes:
#   mysql_data:
