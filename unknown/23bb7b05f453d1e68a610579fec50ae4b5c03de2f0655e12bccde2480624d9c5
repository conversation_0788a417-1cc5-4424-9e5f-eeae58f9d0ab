version: '3.8'

services:
  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: locql-backend
    ports:
      - "5000:5000"
    env_file:
      - docker.env
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development hot reload
      - ./backend:/app
      - /app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locql-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:5000
      # ngrok WebSocket configuration
      - VITE_WS_URL=wss://eternal-friendly-chigger.ngrok-free.app
      - VITE_TUNNEL_URL=https://eternal-friendly-chigger.ngrok-free.app
    volumes:
      # Mount source code for development hot reload
      - ./frontend:/app
      - /app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    depends_on:
      - backend
    # Add extra hosts for ngrok compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network

# Optional: Add a volume for persistent data if needed
volumes:
  node_modules_backend:
  node_modules_frontend:
