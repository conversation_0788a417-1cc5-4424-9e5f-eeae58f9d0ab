# Simple Dockerfile for testing dependency issues
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Debug: List package files
RUN echo "=== Package files in container ===" && \
    ls -la package* && \
    echo "=== Content of package.json ===" && \
    head -20 package.json

# Install dependencies with verbose logging
RUN echo "=== Installing Node.js dependencies ===" && \
    if [ -f package-lock.json ]; then \
        echo "Using npm ci with package-lock.json" && \
        npm ci --verbose; \
    else \
        echo "No package-lock.json found, using npm install" && \
        npm install --verbose; \
    fi && \
    echo "=== Verifying apicache installation ===" && \
    npm list apicache && \
    echo "=== Testing apicache require ===" && \
    node -e "try { require('apicache'); console.log('✅ apicache found and working'); } catch(e) { console.error('❌ apicache missing:', e.message); process.exit(1); }" && \
    echo "=== Listing installed packages ===" && \
    ls -la node_modules/ | head -20 && \
    npm cache clean --force

# Copy source code
COPY . .

# Expose port
EXPOSE 5000

# Start the application
CMD ["node", "server.js"]
