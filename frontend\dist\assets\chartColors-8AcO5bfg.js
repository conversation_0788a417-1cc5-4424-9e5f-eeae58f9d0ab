import{h as u,r as c,c2 as C,S as t}from"./index-y9W4UQPd.js";const m=(r={})=>{const{settings:n,charts:o,theme:a}=u(),i=c.useRef(null),e=c.useMemo(()=>(i.current?i.current.updateSettings(n):i.current=new C(n),i.current),[n]);return c.useMemo(()=>{const{chartType:l="bar",allowedTypes:d=["bar","line","pie"],customOptions:p={}}=r;return{manager:e,chartType:e.getChartType(l,d),height:e.getChartHeight(),margins:e.getChartMargins(),colors:e.getColorScheme(),axisConfig:e.getAxisConfig(),gridConfig:e.getGridConfig(),tooltipConfig:e.getTooltipConfig(),legendConfig:e.getLegendConfig(),animationConfig:e.getAnimationConfig(),hoverEffectsConfig:e.getHoverEffectsConfig(),clickToExpandConfig:e.getClickToExpandConfig(),responsiveContainerProps:e.getResponsiveContainerProps(),getBarElementConfig:(g,f)=>e.getBarElementConfig(g,f),getLineElementConfig:(g,f)=>e.getLineElementConfig(g,f),displayConfig:e.getDisplayConfig(),interactionConfig:e.getInteractionConfig(),performanceConfig:e.getPerformanceConfig(),applySettingsToData:(g,f)=>e.applySettingsToData(g,f),areAnimationsActive:()=>e.areAnimationsActive(),getTextColor:()=>e.getTextColor(),getPrimaryColor:()=>e.getPrimaryColor(),getChartConfig:(g,f)=>e.getChartConfig(g,{...p,...f}),settings:n,charts:o,theme:a}},[e,r,n,o,a])},T=(r={})=>{const{fallbackType:n="bar",allowedTypes:o=["bar","line"],propChartType:a=null}=r,i=m({...r,chartType:n,allowedTypes:o}),e=c.useMemo(()=>a&&o.includes(a)?a:n,[a,n,o]);return{...i,chartType:e,isDynamic:!0,allowedTypes:o,canSwitchTo:s=>o.includes(s),getConfigForType:s=>o.includes(s)?i.getChartConfig(s):(console.warn(`Chart type "${s}" not allowed. Allowed types:`,o),i.getChartConfig(n))}},E=r=>r?new C(r).getColorScheme():[t.PRIMARY_BLUE,t.SECONDARY_BLUE,t.CHART_TERTIARY,t.SUCCESS_GREEN,t.WARNING_ORANGE],R=()=>({default:{name:"Default (Original Colors)",colors:["#1890ff","#52c41a","#faad14","#f5222d","#722ed1"],description:"Each chart keeps its original individual colors"},brand:{name:"Brand Colors",colors:[t.PRIMARY_BLUE,t.SECONDARY_BLUE,t.CHART_TERTIARY,t.SUCCESS_GREEN,t.WARNING_ORANGE],description:"Unified brand color palette"},blue:{name:"Blue Theme",colors:["#1890ff","#40a9ff","#69c0ff","#91d5ff","#bae7ff"],description:"Blue color variations"},green:{name:"Green Theme",colors:["#52c41a","#73d13d","#95de64","#b7eb8f","#d9f7be"],description:"Green color variations"},red:{name:"Red Theme",colors:["#ff4d4f","#ff7875","#ffa39e","#ffccc7","#ffe1e1"],description:"Red color variations"}});export{T as a,R as b,E as g,m as u};
