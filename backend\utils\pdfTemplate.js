import dayjs from 'dayjs';

/**
 * Enhanced PDF template utilities with French number formatting
 */
export class PDFReportTemplate {
  constructor(doc) {
    this.doc = doc;
    this.margins = { top: 50, bottom: 50, left: 50, right: 50 };
    this.colors = {
      primary: '#1E3A8A',    // SOMIPEM Primary Blue
      secondary: '#3B82F6',  // SOMIPEM Secondary Blue
      text: '#1F2937',       // Dark Gray
      light: '#6B7280'       // Light Gray
    };
    this.fonts = {
      title: 20,
      header: 16,
      body: 12,
      small: 10,
      footer: 8
    };
  }

  /**
   * Format numbers in French style (dots for thousands, comma for decimal)
   */
  formatFrenchNumber(number, decimals = 2) {
    if (typeof number !== 'number' || isNaN(number)) return 'N/A';
    
    return number.toLocaleString('fr-FR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  /**
   * Add company header with logo placeholder
   */
  addHeader(title) {
    // Company logo area (placeholder)
    this.doc.rect(this.margins.left, this.margins.top, 100, 40)
      .stroke(this.colors.light);
    
    this.doc.fontSize(8)
      .fillColor(this.colors.light)
      .text('LOGO SOMIPEM', this.margins.left + 10, this.margins.top + 15);

    // Title
    this.doc.fontSize(this.fonts.title)
      .fillColor(this.colors.primary)
      .text(title, this.margins.left + 120, this.margins.top + 10, {
        width: 350,
        align: 'center'
      });

    // Horizontal line
    this.doc.moveTo(this.margins.left, this.margins.top + 60)
      .lineTo(this.doc.page.width - this.margins.right, this.margins.top + 60)
      .strokeColor(this.colors.primary)
      .lineWidth(2)
      .stroke();

    this.doc.y = this.margins.top + 80;
    return this;
  }

  /**
   * Add section header with consistent styling
   */
  addSectionHeader(title) {
    this.doc.fontSize(this.fonts.header)
      .fillColor(this.colors.primary)
      .text(title, { underline: true })
      .moveDown(0.5);
    return this;
  }

  /**
   * Add information table with proper alignment
   */
  addInfoTable(data, columns = 2) {
    const itemHeight = 20;
    const columnWidth = (this.doc.page.width - this.margins.left - this.margins.right) / columns;
    
    let yPos = this.doc.y;
    const initialY = yPos;
    
    data.forEach((item, i) => {
      const column = i % columns;
      
      if (column === 0 && i > 0) {
        yPos += itemHeight;
      }
      
      const xPos = this.margins.left + (column * columnWidth);
      
      this.doc.fontSize(this.fonts.body)
        .fillColor(this.colors.text)
        .text(`${item.label}: `, xPos, yPos, { continued: true })
        .fillColor(this.colors.text)
        .text(item.value);
    });
    
    this.doc.y = initialY + Math.ceil(data.length / columns) * itemHeight + 10;
    return this;
  }

  /**
   * Add metrics table with performance indicators
   */
  addMetricsTable(metrics) {
    const tableWidth = this.doc.page.width - this.margins.left - this.margins.right;
    const rowHeight = 25;
    
    // Table header
    this.doc.rect(this.margins.left, this.doc.y, tableWidth, rowHeight)
      .fillAndStroke(this.colors.secondary, this.colors.primary);
    
    this.doc.fontSize(this.fonts.body)
      .fillColor('white')
      .text('Métrique', this.margins.left + 10, this.doc.y + 8, { width: tableWidth * 0.6 })
      .text('Valeur', this.margins.left + tableWidth * 0.6 + 10, this.doc.y + 8, { width: tableWidth * 0.4 });
    
    this.doc.y += rowHeight;
    
    // Table rows
    metrics.forEach((metric, i) => {
      const isEven = i % 2 === 0;
      const fillColor = isEven ? '#F9FAFB' : 'white';
      
      this.doc.rect(this.margins.left, this.doc.y, tableWidth, rowHeight)
        .fillAndStroke(fillColor, this.colors.light);
      
      this.doc.fontSize(this.fonts.body)
        .fillColor(this.colors.text)
        .text(metric.label, this.margins.left + 10, this.doc.y + 8, { width: tableWidth * 0.6 })
        .text(metric.value, this.margins.left + tableWidth * 0.6 + 10, this.doc.y + 8, { width: tableWidth * 0.4 });
      
      this.doc.y += rowHeight;
    });
    
    this.doc.moveDown(0.5);
    return this;
  }

  /**
   * Add performance indicator with color coding
   */
  addPerformanceIndicator(label, value, thresholds = { excellent: 85, good: 75, acceptable: 65 }) {
    let color = this.colors.primary;
    let status = '';
    
    if (value >= thresholds.excellent) {
      color = '#059669'; // Green
      status = 'Excellent';
    } else if (value >= thresholds.good) {
      color = '#3B82F6'; // Blue
      status = 'Très bon';
    } else if (value >= thresholds.acceptable) {
      color = '#F59E0B'; // Orange
      status = 'Acceptable';
    } else {
      color = '#DC2626'; // Red
      status = 'Faible';
    }
    
    // Indicator box
    this.doc.rect(this.margins.left, this.doc.y, 20, 20)
      .fillAndStroke(color, color);
    
    this.doc.fontSize(this.fonts.body)
      .fillColor(this.colors.text)
      .text(`${label}: ${this.formatFrenchNumber(value, 1)}% - ${status}`, 
            this.margins.left + 30, this.doc.y + 5);
    
    this.doc.y += 30;
    return this;
  }

  /**
   * Add footer with page numbers and generation info
   */
  addFooter(generatedBy, generatedAt) {
    const pageCount = this.doc.bufferedPageRange().count;
    
    for (let i = 0; i < pageCount; i++) {
      this.doc.switchToPage(i);
      
      // Footer line
      this.doc.moveTo(this.margins.left, this.doc.page.height - this.margins.bottom + 10)
        .lineTo(this.doc.page.width - this.margins.right, this.doc.page.height - this.margins.bottom + 10)
        .strokeColor(this.colors.light)
        .lineWidth(1)
        .stroke();
      
      // Footer text
      this.doc.fontSize(this.fonts.footer)
        .fillColor(this.colors.light)
        .text(
          `Généré par ${generatedBy} le ${dayjs(generatedAt).format("DD/MM/YYYY HH:mm")}`,
          this.margins.left,
          this.doc.page.height - this.margins.bottom + 20,
          { width: 300 }
        )
        .text(
          `Page ${i + 1} sur ${pageCount}`,
          this.doc.page.width - this.margins.right - 100,
          this.doc.page.height - this.margins.bottom + 20,
          { width: 100, align: 'right' }
        );
    }
    
    return this;
  }

  /**
   * Check if new page is needed
   */
  checkPageBreak(requiredSpace = 100) {
    if (this.doc.y + requiredSpace > this.doc.page.height - this.margins.bottom) {
      this.doc.addPage();
      this.doc.y = this.margins.top;
    }
    return this;
  }
}

export default PDFReportTemplate;
