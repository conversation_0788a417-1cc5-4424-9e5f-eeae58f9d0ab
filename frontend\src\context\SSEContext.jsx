/**
 * SSE Context Provider for Somipem
 * Manages a single SSE connection shared across all components
 * Prevents multiple connections per user and connection limit issues
 * Only initializes SSE connection when user is authenticated
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import useSSENotifications from '../hooks/useSSENotifications';

// Create the SSE context
const SSEContext = createContext();

// SSE Provider component
export const SSEProvider = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const [shouldInitializeSSE, setShouldInitializeSSE] = useState(false);

  // Only initialize SSE when user is authenticated and not loading
  useEffect(() => {
    console.log('🔐 SSEContext - Auth state changed:', {
      loading,
      isAuthenticated,
      shouldInitializeSSE,
      timestamp: new Date().toISOString()
    });

    if (!loading && isAuthenticated) {
      console.log('✅ User authenticated, initializing SSE connection...');
      setShouldInitializeSSE(true);
    } else if (!loading && !isAuthenticated) {
      console.log('❌ User not authenticated, SSE connection disabled');
      setShouldInitializeSSE(false);
    } else if (loading) {
      console.log('⏳ Still loading authentication status...');
    }
  }, [isAuthenticated, loading]);

  // Additional effect to handle authentication state changes more reliably
  useEffect(() => {
    // Add a small delay to ensure auth state is fully settled
    const timer = setTimeout(() => {
      if (!loading) {
        console.log('🔄 SSE Context - Final auth check:', { isAuthenticated, shouldInitializeSSE });
        if (isAuthenticated && !shouldInitializeSSE) {
          console.log('🔧 Force enabling SSE for authenticated user');
          setShouldInitializeSSE(true);
        }
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [isAuthenticated, loading, shouldInitializeSSE]);

  // Always initialize SSE hook, but pass disabled flag
  const sseData = useSSENotifications({
    enableBrowserNotifications: true,
    enableAntNotifications: false, // Prevent duplicate notifications
    maxNotificationsInMemory: 50,
    disabled: !shouldInitializeSSE // Disable SSE when not authenticated
  });

  // Enhanced logging for debugging
  console.log('🔍 SSE Context - Current state:', {
    shouldInitializeSSE,
    sseConnectionStatus: sseData.connectionStatus,
    sseIsConnected: sseData.isConnected,
    sseIsConnecting: sseData.isConnecting,
    notificationsCount: sseData.notifications?.length || 0,
    unreadCount: sseData.unreadCount
  });

  // Always use the actual SSE data, but the hook handles the disabled state internally
  const contextValue = sseData;

  return (
    <SSEContext.Provider value={contextValue}>
      {children}
    </SSEContext.Provider>
  );
};

// Custom hook to use SSE context
export const useSSE = () => {
  const context = useContext(SSEContext);
  
  if (!context) {
    throw new Error('useSSE must be used within an SSEProvider');
  }
  
  return context;
};

export default SSEContext;
