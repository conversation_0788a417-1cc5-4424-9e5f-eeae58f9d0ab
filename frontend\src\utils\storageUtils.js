/**
 * Utilities for working with localStorage and sessionStorage
 * @module storageUtils
 */

/**
 * Storage keys used in the application
 * @type {Object}
 */
export const STORAGE_KEYS = {
  // 🔒 SECURITY: AUTH_TOKEN and USER removed - using HTTP-only cookies
  // AUTH_TOKEN: 'token', // DEPRECATED: Use HTTP-only cookies
  // USER: 'user', // DEPRECATED: Use HTTP-only cookies
  THEME: 'themePreference',
  LANGUAGE: 'language',

  LAST_ROUTE: 'lastRoute',
  DASHBOARD_FILTERS: 'dashboardFilters',
  REMEMBER_ME: 'rememberMe',
};

/**
 * Get item from localStorage with JSON parsing
 * @param {string} key - Storage key
 * @param {*} [defaultValue=null] - Default value if key doesn't exist
 * @returns {*} Parsed value or default value
 */
export const getLocalItem = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key);
    
    if (item === null) {
      return defaultValue;
    }
    
    return JSON.parse(item);
  } catch (error) {
    console.error(`Error getting item from localStorage (${key}):`, error);
    return defaultValue;
  }
};

/**
 * Set item in localStorage with JSON stringification
 * @param {string} key - Storage key
 * @param {*} value - Value to store
 * @returns {boolean} Whether operation was successful
 */
export const setLocalItem = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error setting item in localStorage (${key}):`, error);
    return false;
  }
};

/**
 * Remove item from localStorage
 * @param {string} key - Storage key
 * @returns {boolean} Whether operation was successful
 */
export const removeLocalItem = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing item from localStorage (${key}):`, error);
    return false;
  }
};

/**
 * Get item from sessionStorage with JSON parsing
 * @param {string} key - Storage key
 * @param {*} [defaultValue=null] - Default value if key doesn't exist
 * @returns {*} Parsed value or default value
 */
export const getSessionItem = (key, defaultValue = null) => {
  try {
    const item = sessionStorage.getItem(key);
    
    if (item === null) {
      return defaultValue;
    }
    
    return JSON.parse(item);
  } catch (error) {
    console.error(`Error getting item from sessionStorage (${key}):`, error);
    return defaultValue;
  }
};

/**
 * Set item in sessionStorage with JSON stringification
 * @param {string} key - Storage key
 * @param {*} value - Value to store
 * @returns {boolean} Whether operation was successful
 */
export const setSessionItem = (key, value) => {
  try {
    sessionStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error setting item in sessionStorage (${key}):`, error);
    return false;
  }
};

/**
 * Remove item from sessionStorage
 * @param {string} key - Storage key
 * @returns {boolean} Whether operation was successful
 */
export const removeSessionItem = (key) => {
  try {
    sessionStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing item from sessionStorage (${key}):`, error);
    return false;
  }
};

/**
 * Clear all items from localStorage
 * @returns {boolean} Whether operation was successful
 */
export const clearLocalStorage = () => {
  try {
    localStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
};

/**
 * Clear all items from sessionStorage
 * @returns {boolean} Whether operation was successful
 */
export const clearSessionStorage = () => {
  try {
    sessionStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
    return false;
  }
};

/**
 * 🔒 DEPRECATED: Get user data from localStorage
 * @deprecated Use HTTP-only cookies for security. User data is managed by AuthContext.
 * @returns {Object|null} User data or null if not found
 */
export const getUser = () => {
  console.warn('🔒 DEPRECATED: getUser() - Use AuthContext instead of localStorage for user data');
  return null; // Always return null to prevent localStorage usage
};

/**
 * 🔒 DEPRECATED: Set user data in localStorage
 * @deprecated Use HTTP-only cookies for security. User data is managed by AuthContext.
 * @param {Object} user - User data
 * @returns {boolean} Whether operation was successful
 */
export const setUser = (user) => {
  console.warn('🔒 DEPRECATED: setUser() - Use AuthContext instead of localStorage for user data');
  return false; // Prevent localStorage usage
};

/**
 * 🔒 DEPRECATED: Get authentication token from localStorage
 * @deprecated Use HTTP-only cookies for security. Tokens are managed by backend.
 * @returns {string|null} Token or null if not found
 */
export const getToken = () => {
  console.warn('🔒 DEPRECATED: getToken() - Use HTTP-only cookies instead of localStorage for tokens');
  return null; // Always return null to prevent localStorage usage
};

/**
 * 🔒 DEPRECATED: Set authentication token in localStorage
 * @deprecated Use HTTP-only cookies for security. Tokens are managed by backend.
 * @param {string} token - Authentication token
 * @returns {boolean} Whether operation was successful
 */
export const setToken = (token) => {
  console.warn('🔒 DEPRECATED: setToken() - Use HTTP-only cookies instead of localStorage for tokens');
  return false; // Prevent localStorage usage
};

/**
 * 🔒 DEPRECATED: Clear authentication data (token and user)
 * @deprecated Authentication data is managed by HTTP-only cookies and AuthContext.
 * @returns {boolean} Whether operation was successful
 */
export const clearAuth = () => {
  console.warn('🔒 DEPRECATED: clearAuth() - Authentication is managed by HTTP-only cookies');
  return true; // Return true but don't actually clear anything
};



/**
 * Get dashboard filters from localStorage
 * @param {string} dashboardId - Dashboard identifier
 * @returns {Object} Dashboard filters
 */
export const getDashboardFilters = (dashboardId) => {
  const filters = getLocalItem(STORAGE_KEYS.DASHBOARD_FILTERS, {});
  return filters[dashboardId] || {};
};

/**
 * Save dashboard filters to localStorage
 * @param {string} dashboardId - Dashboard identifier
 * @param {Object} filters - Filters to save
 * @returns {boolean} Whether operation was successful
 */
export const saveDashboardFilters = (dashboardId, filters) => {
  const allFilters = getLocalItem(STORAGE_KEYS.DASHBOARD_FILTERS, {});
  allFilters[dashboardId] = filters;
  return setLocalItem(STORAGE_KEYS.DASHBOARD_FILTERS, allFilters);
};

/**
 * Check if storage is available
 * @param {string} type - Storage type ('localStorage' or 'sessionStorage')
 * @returns {boolean} Whether storage is available
 */
export const isStorageAvailable = (type) => {
  try {
    const storage = window[type];
    const testKey = '__storage_test__';
    storage.setItem(testKey, testKey);
    storage.removeItem(testKey);
    return true;
  } catch (error) {
    return false;
  }
};

export default {
  STORAGE_KEYS,
  getLocalItem,
  setLocalItem,
  removeLocalItem,
  getSessionItem,
  setSessionItem,
  removeSessionItem,
  clearLocalStorage,
  clearSessionStorage,
  // 🔒 DEPRECATED: Auth functions kept for backward compatibility but disabled
  getUser, // DEPRECATED
  setUser, // DEPRECATED
  getToken, // DEPRECATED
  setToken, // DEPRECATED
  clearAuth, // DEPRECATED

  getDashboardFilters,
  saveDashboardFilters,
  isStorageAvailable,
};