import React, { useState } from 'react';
import { Card, Row, Col, DatePicker, Select, Button, Space, Badge, Tooltip } from 'antd';
import { 
  CalendarOutlined,
  DatabaseOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FilterOutlined,
  ClearOutlined,
  SaveOutlined,
  HistoryOutlined
} from '@ant-design/icons';

const { RangePicker } = DatePicker;
const { Option } = Select;

const AnalyticsFilters = ({ filters, onFilterChange, loading }) => {
  const [presetMode, setPresetMode] = useState(false);

  // Mock data for dropdowns (will be replaced with real data later)
  const mockMachines = [
    { id: 'M001', name: 'Machine 001 - Line A' },
    { id: 'M002', name: 'Machine 002 - Line A' },
    { id: 'M003', name: 'Machine 003 - Line B' },
    { id: 'M004', name: 'Machine 004 - Line B' },
    { id: 'M005', name: 'Machine 005 - Line C' },
  ];

  const mockParts = [
    { id: 'P001', name: 'Part ABC-123', weight: '50g' },
    { id: 'P002', name: 'Part DEF-456', weight: '75g' },
    { id: 'P003', name: 'Part GHI-789', weight: '100g' },
    { id: 'P004', name: 'Part JKL-012', weight: '125g' },
  ];

  const mockOperators = [
    { id: 'OP001', name: 'John Smith', shift: 'Day' },
    { id: 'OP002', name: 'Maria Garcia', shift: 'Night' },
    { id: 'OP003', name: 'Ahmed Hassan', shift: 'Day' },
    { id: 'OP004', name: 'Lisa Chen', shift: 'Evening' },
  ];

  const shifts = ['Day', 'Evening', 'Night'];

  const handleDateRangeChange = (dates) => {
    onFilterChange({ dateRange: dates });
  };

  const handleMachineChange = (value) => {
    onFilterChange({ machine: value });
  };

  const handlePartChange = (value) => {
    onFilterChange({ partNumber: value });
  };

  const handleOperatorChange = (value) => {
    onFilterChange({ operator: value });
  };

  const handleShiftChange = (value) => {
    onFilterChange({ shift: value });
  };

  const handleClearFilters = () => {
    onFilterChange({
      dateRange: null,
      machine: null,
      partNumber: null,
      operator: null,
      shift: null
    });
  };

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value => value !== null && value !== undefined).length;
  };

  return (
    <Card
      style={{
        marginBottom: '24px',
        borderRadius: '16px',
        border: 'none',
        boxShadow: '0 8px 24px rgba(0,0,0,0.06)',
        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)'
      }}
      bodyStyle={{ padding: '24px' }}
    >
      {/* Filter Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px'
      }}>
        <Space align="center" size="middle">
          <div style={{
            background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
            borderRadius: '10px',
            padding: '8px',
            color: 'white'
          }}>
            <FilterOutlined style={{ fontSize: '16px' }} />
          </div>
          <div>
            <h3 style={{ margin: 0, color: '#1890ff', fontWeight: '600' }}>
              Smart Filters
            </h3>
            <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
              Apply intelligent filters to focus your analytics
            </span>
          </div>
          {getActiveFilterCount() > 0 && (
            <Badge 
              count={getActiveFilterCount()} 
              style={{ 
                backgroundColor: '#52c41a',
                borderRadius: '10px'
              }}
            />
          )}
        </Space>

        <Space>
          <Tooltip title="Load Saved Filter Preset">
            <Button 
              icon={<HistoryOutlined />} 
              style={{ borderRadius: '8px' }}
            >
              Presets
            </Button>
          </Tooltip>
          
          <Tooltip title="Save Current Filters">
            <Button 
              icon={<SaveOutlined />} 
              type="dashed"
              style={{ borderRadius: '8px' }}
            >
              Save
            </Button>
          </Tooltip>

          <Tooltip title="Clear All Filters">
            <Button 
              icon={<ClearOutlined />} 
              onClick={handleClearFilters}
              disabled={getActiveFilterCount() === 0}
              style={{ borderRadius: '8px' }}
            >
              Clear
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* Filter Controls */}
      <Row gutter={[16, 16]}>
        {/* Date Range */}
        <Col xs={24} sm={12} lg={6}>
          <div style={{ marginBottom: '8px' }}>
            <Space size={4}>
              <CalendarOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>Date Range</span>
            </Space>
          </div>
          <RangePicker
            value={filters.dateRange}
            onChange={handleDateRangeChange}
            style={{ 
              width: '100%',
              borderRadius: '8px',
              border: '2px solid #e8f4fd'
            }}
            placeholder={['Start Date', 'End Date']}
          />
        </Col>

        {/* Machine Selection */}
        <Col xs={24} sm={12} lg={6}>
          <div style={{ marginBottom: '8px' }}>
            <Space size={4}>
              <DatabaseOutlined style={{ color: '#52c41a' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>Machine</span>
            </Space>
          </div>
          <Select
            value={filters.machine}
            onChange={handleMachineChange}
            placeholder="Select Machine"
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            style={{ 
              width: '100%',
              borderRadius: '8px'
            }}
          >
            {mockMachines.map(machine => (
              <Option key={machine.id} value={machine.id}>
                <Space>
                  <Badge status="success" />
                  {machine.name}
                </Space>
              </Option>
            ))}
          </Select>
        </Col>

        {/* Part Number */}
        <Col xs={24} sm={12} lg={6}>
          <div style={{ marginBottom: '8px' }}>
            <Space size={4}>
              <DatabaseOutlined style={{ color: '#fa8c16' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>Part Number</span>
            </Space>
          </div>
          <Select
            value={filters.partNumber}
            onChange={handlePartChange}
            placeholder="Select Part"
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            style={{ 
              width: '100%',
              borderRadius: '8px'
            }}
          >
            {mockParts.map(part => (
              <Option key={part.id} value={part.id}>
                <div>
                  <div>{part.name}</div>
                  <div style={{ fontSize: '11px', color: '#8c8c8c' }}>
                    Weight: {part.weight}
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Col>

        {/* Operator */}
        <Col xs={24} sm={12} lg={3}>
          <div style={{ marginBottom: '8px' }}>
            <Space size={4}>
              <UserOutlined style={{ color: '#722ed1' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>Operator</span>
            </Space>
          </div>
          <Select
            value={filters.operator}
            onChange={handleOperatorChange}
            placeholder="Select Operator"
            allowClear
            showSearch
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            style={{ 
              width: '100%',
              borderRadius: '8px'
            }}
          >
            {mockOperators.map(operator => (
              <Option key={operator.id} value={operator.id}>
                <div>
                  <div>{operator.name}</div>
                  <div style={{ fontSize: '11px', color: '#8c8c8c' }}>
                    {operator.shift} Shift
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Col>

        {/* Shift */}
        <Col xs={24} sm={12} lg={3}>
          <div style={{ marginBottom: '8px' }}>
            <Space size={4}>
              <ClockCircleOutlined style={{ color: '#eb2f96' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>Shift</span>
            </Space>
          </div>
          <Select
            value={filters.shift}
            onChange={handleShiftChange}
            placeholder="Select Shift"
            allowClear
            style={{ 
              width: '100%',
              borderRadius: '8px'
            }}
          >
            {shifts.map(shift => (
              <Option key={shift} value={shift}>
                <Space>
                  <Badge 
                    status={shift === 'Day' ? 'success' : shift === 'Evening' ? 'warning' : 'error'} 
                  />
                  {shift} Shift
                </Space>
              </Option>
            ))}
          </Select>
        </Col>
      </Row>

      {/* Quick Filter Presets */}
      <div style={{ marginTop: '20px', padding: '16px', background: 'rgba(255,255,255,0.6)', borderRadius: '12px' }}>
        <div style={{ marginBottom: '12px' }}>
          <span style={{ fontWeight: '500', color: '#595959', fontSize: '13px' }}>
            Quick Presets:
          </span>
        </div>
        <Space wrap size="small">
          <Button size="small" style={{ borderRadius: '6px' }}>Today</Button>
          <Button size="small" style={{ borderRadius: '6px' }}>Yesterday</Button>
          <Button size="small" style={{ borderRadius: '6px' }}>Last 7 Days</Button>
          <Button size="small" style={{ borderRadius: '6px' }}>Last 30 Days</Button>
          <Button size="small" style={{ borderRadius: '6px' }}>This Month</Button>
          <Button size="small" style={{ borderRadius: '6px' }}>Current Shift</Button>
          <Button size="small" style={{ borderRadius: '6px' }}>Peak Hours</Button>
        </Space>
      </div>
    </Card>
  );
};

export default AnalyticsFilters;
