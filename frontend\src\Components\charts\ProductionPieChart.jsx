import React, { memo } from "react";
import {
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "recharts";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// SOMIPEM Brand Color Palette for Charts (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Pie chart component for production data
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {string} props.dataKey - Data key for values (default: "value")
 * @param {string} props.nameKey - Data key for names (default: "name")
 * @param {Array} props.colors - Array of colors (default: COLORS)
 * @returns {JSX.Element} - Rendered chart component
 */
const ProductionPieChart = memo(({ data, dataKey = "value", nameKey = "name", colors = COLORS }) => (
  <ResponsiveContainer width="100%" height={300}>
    <PieChart margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <Pie
        data={data}
        dataKey={dataKey}
        nameKey={nameKey}
        cx="50%"
        cy="50%"
        innerRadius={60}
        outerRadius={80}
        paddingAngle={5}
        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
        ))}
      </Pie>
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      />
      <Legend
        layout="vertical"
        verticalAlign="middle"
        align="right"
        wrapperStyle={{
          paddingLeft: 24,
          fontSize: 14,
          color: "#666",
        }}
      />
    </PieChart>
  </ResponsiveContainer>
));

ProductionPieChart.displayName = 'ProductionPieChart';

export default ProductionPieChart;
