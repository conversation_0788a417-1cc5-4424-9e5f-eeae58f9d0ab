import ChartConfigurationManager from '../config/ChartConfigurationManager';
import SOMIPEM_COLORS from '../styles/brand-colors';

/**
 * Chart Colors Utility
 * Provides dynamic color schemes based on user settings
 * Ensures consistent color application across all charts
 */

/**
 * Get dynamic color scheme based on current settings
 * @param {Object} settings - Current user settings
 * @returns {Array|null} Array of color values or null for default mode
 */
export const getDynamicColors = (settings) => {
  if (!settings) {
    // Fallback to brand colors if no settings available
    return [
      SOMIPEM_COLORS.PRIMARY_BLUE,
      SOMIPEM_COLORS.SECONDARY_BLUE,
      SOMIPEM_COLORS.CHART_TERTIARY,
      SOMIPEM_COLORS.SUCCESS_GREEN,
      SOMIPEM_COLORS.WARNING_ORANGE
    ];
  }

  const configManager = new ChartConfigurationManager(settings);
  return configManager.getColorScheme();
};

/**
 * Get color scheme options for settings UI
 * @returns {Object} Available color schemes with preview colors
 */
export const getColorSchemeOptions = () => {
  return {
    default: {
      name: 'Default (Original Colors)',
      colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'], // Mixed colors to represent variety
      description: 'Each chart keeps its original individual colors'
    },
    brand: {
      name: 'Brand Colors',
      colors: [
        SOMIPEM_COLORS.PRIMARY_BLUE,
        SOMIPEM_COLORS.SECONDARY_BLUE,
        SOMIPEM_COLORS.CHART_TERTIARY,
        SOMIPEM_COLORS.SUCCESS_GREEN,
        SOMIPEM_COLORS.WARNING_ORANGE
      ],
      description: 'Unified brand color palette'
    },
    blue: {
      name: 'Blue Theme',
      colors: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff'],
      description: 'Blue color variations'
    },
    green: {
      name: 'Green Theme',
      colors: ['#52c41a', '#73d13d', '#95de64', '#b7eb8f', '#d9f7be'],
      description: 'Green color variations'
    },
    red: {
      name: 'Red Theme',
      colors: ['#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7', '#ffe1e1'],
      description: 'Red color variations'
    }
  };
};

/**
 * Hook for using dynamic colors in React components
 * @param {Object} settings - Current user settings
 * @returns {Array} Dynamic color array
 */
export const useDynamicColors = (settings) => {
  return getDynamicColors(settings);
};

export default {
  getDynamicColors,
  getColorSchemeOptions,
  useDynamicColors
};
