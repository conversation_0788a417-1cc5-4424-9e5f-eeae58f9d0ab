import{r as d,a as bt,at as K,R as e,S,d as J,T as dt,f as k,e as he,ad as Vt,l as Ut,g as Oe,E as te,h as He,c as v,b as be,F as Yt,au as Ot,A as Gt,G as Ht,C as me,O as it,B as Nt,m as Tt,i as Wt}from"./index-LbZyOyVE.js";import{t as Kt,n as oe,R as Jt,S as Xt}from"./dataUtils-DeBxUyim.js";import{d as T}from"./dayjs.min-dvN_FXBc.js";import"./fr-B8jKMo6i.js";import{i as Zt,G as ea}from"./GlobalSearchModal-CBU8qa9s.js";import{w as ta,c as aa,R as st}from"./DownloadOutlined-DDEO8hwP.js";import{u as ra}from"./useDailyTableGraphQL-h9FMOZI7.js";import{F as na,S as oa}from"./SearchResultsDisplay-Cop4Ro78.js";import{U as ue,u as la,g as ia}from"./UnifiedChartExpansion-CbHcdyhz.js";import{c as je,d as Ge,a as ct,f as Se}from"./numberFormatter-CKFvf91F.js";import{R as sa,a as ca}from"./RiseOutlined-CBVHozvI.js";import{R as St}from"./DashboardOutlined-1-sdY0Ts.js";import{R as da}from"./ClockCircleOutlined-DqsC6tNJ.js";import{R as _t}from"./ThunderboltOutlined-EFtGH518.js";import{R as ma}from"./CloseCircleOutlined-j8iUMKYt.js";import{R as ua}from"./CheckCircleOutlined-Bxw2HLIH.js";import{R as Mt}from"./ToolOutlined-CByaXpyN.js";import{P as _e}from"./progress-DqUiDqeJ.js";import{R as kt}from"./LineChartOutlined-Dj8Kxu7c.js";import{R as fa}from"./CalendarOutlined-C9bHRkbV.js";import{u as Re,g as pa,a as It}from"./chartColors-BxU00oQu.js";import{R as fe,B as qe,C as le,X as ie,Y as se,T as ae,a as re,g as Be,P as ga,e as ha,f as ya,L as We,d as Ke,h as Ea,i as w}from"./PieChart-M_RudKFe.js";import{R as Rt}from"./InfoCircleOutlined-ItjliUYy.js";import{R as Da}from"./WarningOutlined-C7HhcXOO.js";import{R as pt}from"./BarChartOutlined-DwmNYdCA.js";import{S as Ue}from"./index-DTXi_feZ.js";import{R as ba}from"./TableOutlined-B1GWFj6L.js";import{R as Sa}from"./SearchOutlined-DzAh4Hfi.js";import"./FilePdfOutlined-CZagYiQ5.js";import"./index-DaGThAV-.js";import"./FileTextOutlined-DeApLYgQ.js";import"./ExperimentOutlined-PY_59KgI.js";import"./index-CgKoxNk1.js";import"./FilterOutlined-bWWAWSgt.js";import"./index-Bgi8X2DN.js";import"./ReloadOutlined-CoxAQyfN.js";import"./EyeOutlined-Bf51LvXG.js";import"./FullscreenOutlined-lwl2ZF1Q.js";import"./CloseOutlined-BKKOu6Ny.js";import"./ZoomOutOutlined-CdPjzMfa.js";const _a=()=>{const[n,l]=d.useState([]),[a,t]=d.useState([]),[r,c]=d.useState([]),[h,u]=d.useState("IPS"),[i,C]=d.useState(""),[R,x]=d.useState(!1),f=d.useCallback(async()=>{try{console.log("Fetching machine models..."),x(!0);const y=await bt.get("/api/machine-models").retry(2);if(y.body){const b=K(y),g=Array.isArray(b)?b.map(E=>E.model||E):[];console.log("Machine models fetched:",g),l(g.length>0?g:["IPS","CCM24"])}else console.log("No machine models returned from API, using defaults"),l(["IPS","CCM24"])}catch(y){console.error("Error loading machine models:",y),l(["IPS","CCM24"])}finally{x(!1)}},[]),p=d.useCallback(async()=>{try{console.log("Fetching machine names..."),x(!0);const y=await bt.get("/api/machine-names").retry(2);if(y.body){const b=K(y);console.log("Machine names fetched:",b),Array.isArray(b)&&b.length>0?(t(b),b.find(E=>E.Machine_Name==="IPS01")&&h==="IPS"&&console.log("Confirmed IPS as default machine model")):(console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}else console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(y){console.error("Error loading machine names:",y),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}finally{x(!1)}},[h]),s=y=>{console.log("🔧 [FILTER DEBUG] handleMachineModelChange called with:",y),console.log("🔧 [FILTER DEBUG] Previous selectedMachineModel:",h),u(y),console.log("🔧 [FILTER DEBUG] New selectedMachineModel will be:",y)},_=y=>{console.log("🚗 [FILTER DEBUG] handleMachineChange called with:",y),console.log("🚗 [FILTER DEBUG] Previous selectedMachine:",i),C(y),console.log("🚗 [FILTER DEBUG] New selectedMachine will be:",y)};return d.useEffect(()=>{if(h){const y=a.filter(b=>b.Machine_Name&&b.Machine_Name.startsWith(h));c(y),i&&!y.some(b=>b.Machine_Name===i)&&C("")}else c([]),C("")},[h,a,i]),d.useEffect(()=>{f()},[f]),d.useEffect(()=>{p()},[p]),{machineModels:n,machineNames:a,filteredMachineNames:r,selectedMachineModel:h,selectedMachine:i,loading:R,handleMachineModelChange:s,handleMachineChange:_,fetchMachineModels:f,fetchMachineNames:p,setSelectedMachineModel:u,setSelectedMachine:C}},Ra=n=>n?new Date(n).toISOString().split("T")[0]:null;T.extend(Zt);T.extend(ta);T.extend(aa);T.locale("fr");const xa=()=>{const[n,l]=d.useState(null),[a,t]=d.useState("day"),[r,c]=d.useState(""),[h,u]=d.useState(!1),i=d.useCallback((p,s)=>{if(!p)return{short:"",full:""};try{const _=T(p);if(!_.isValid())return console.error("Invalid date in formatDateRange:",p),{short:"Date invalide",full:"Date invalide"};if(s==="day")return{short:_.format("DD/MM/YYYY"),full:`le ${_.format("DD MMMM YYYY")}`};if(s==="week"){const y=_.startOf("isoWeek"),b=_.endOf("isoWeek"),g=_.isoWeek();return{short:`S${g} ${_.format("YYYY")}`,full:`Semaine ${g} (du ${y.format("DD MMMM")} au ${b.format("DD MMMM YYYY")})`}}else if(s==="month"){const y=_.format("MMMM"),b=y.charAt(0).toUpperCase()+y.slice(1);return{short:`${b} ${_.format("YYYY")}`,full:`${b} ${_.format("YYYY")}`}}return{short:"",full:""}}catch(_){return console.error("Error in formatDateRange:",_),{short:"Erreur de date",full:"Erreur de date"}}},[]),C=p=>{if(console.log("📅 [FILTER DEBUG] handleDateChange called with:",p),console.log("📅 [FILTER DEBUG] Current dateFilter:",n),console.log("📅 [FILTER DEBUG] Current dateRangeType:",a),!p){console.log("📅 [FILTER DEBUG] Date is null/undefined, resetting filter"),x();return}try{let s=T(p);a==="week"?s=s.startOf("isoWeek"):a==="month"&&(s=s.startOf("month")),l(s);const{full:_}=i(s,a);c(_),u(!0),console.log(`📅 [FILTER DEBUG] Date filter set: ${s.format("YYYY-MM-DD")}, Range type: ${a}`)}catch(s){console.error("Error handling date change:",s);const _=T(p);l(_);const{full:y}=i(_,a);c(y),u(!0)}},R=p=>{if(console.log("📅 [FILTER DEBUG] handleDateRangeTypeChange called with:",p),t(p),n){let s=T(n),_=s;p==="week"?_=s.startOf("isoWeek"):p==="month"&&(_=s.startOf("month")),l(_);const{full:y}=i(_,p);c(y),console.log(`📅 [FILTER DEBUG] Date range type changed to: ${p}, Adjusted date: ${_.format("YYYY-MM-DD")}`)}},x=()=>{l(null),c(""),u(!1)},f=d.useCallback(()=>{const p=new URLSearchParams;if(n)try{const s=Ra(n);s?(p.append("date",s),p.append("dateRangeType",a),console.log(`API request params: date=${s}, dateRangeType=${a}`)):console.error("Failed to format date for API request:",n)}catch(s){console.error("Error building date query params:",s)}return p},[n,a]);return{dateFilter:n,dateRangeType:a,dateRangeDescription:r,dateFilterActive:h,handleDateChange:C,handleDateRangeTypeChange:R,resetDateFilter:x,buildDateQueryParams:f,formatDateRange:i}},Ca=({selectedMachineModel:n,selectedMachine:l,dateFilter:a,dateRangeType:t,buildDateQueryParams:r})=>{const c=(L,V)=>{const F=(()=>{if(typeof window<"u"){const z=window.location.origin;return z.includes("ngrok-free.app")||z.includes("ngrok.io")?z:"http://localhost:5000"}return"http://localhost:5000"})();return bt[L](`${F}${V}`).retry(2).withCredentials().timeout(3e4)},[h,u]=d.useState(!1),[i,C]=d.useState([]),[R,x]=d.useState([]),[f,p]=d.useState([]),[s,_]=d.useState([]),[y,b]=d.useState(0),[g,E]=d.useState(0),[m,M]=d.useState([]),[$,j]=d.useState([]),[H,pe]=d.useState([]),[Q,X]=d.useState([]),xe=d.useCallback(()=>{const L=new URLSearchParams;n&&!l?L.append("model",n):l&&L.append("machine",l),L.append("limit","100"),L.append("chartLimit","200"),L.append("page","1");const V=r();if(Object.entries(V).forEach(([z,W])=>{L.append(z,W)}),!V.date&&!V.dateRangeType){const z=T().subtract(7,"days").format("YYYY-MM-DD");L.append("date",z),L.append("dateRangeType","week"),L.append("defaultFilter","true")}const F=V.dateRangeType;return F==="month"?L.append("aggregateBy","day"):F==="year"&&L.append("aggregateBy","week"),L.toString()?`?${L.toString()}`:""},[n,l,r]),Ce=d.useCallback(()=>{const L=T(),V=[];for(let F=9;F>=0;F--){const z=L.subtract(F,"day").format("YYYY-MM-DD");if(!T(z).isValid()){console.error("Invalid date generated:",z);continue}V.push({date:z,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.floor(Math.random()*30)+70,speed:Math.floor(Math.random()*5)+5,Machine_Name:l||(n?`${n}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(Math.random()*3)]})}return V},[l,n]),G=d.useCallback(async()=>{var L,V;if(!n){console.log("No machine model selected, skipping data fetch");return}u(!0);try{const F=xe();console.log("API query string:",F);const z=await Promise.allSettled([c("get",`/api/testing-chart-production${F}`),c("get","/api/unique-dates-production").catch(()=>({body:[]})),c("get",`/api/sidecards-prod${F}`),c("get",`/api/sidecards-prod-rejet${F}`),c("get",`/api/machine-performance${F}`),c("get",`/api/hourly-trends${F}`),c("get",`/api/machine-oee-trends${F}`),c("get",`/api/speed-trends${F}`),c("get",`/api/shift-comparison${F}`),c("get",`/api/machine-daily-mould${F}`)]),[W,Z,Me,de,ee,U,Ie,ve,Je,ye]=z;if(W.status==="fulfilled"&&W.value.body){const Y=K(W.value),De=(Array.isArray(Y)?Y:[]).map(Kt);C(De)}else console.log("No chart data available"),C([]);if(Z.status==="fulfilled"){const Y=K(Z.value);_(Y||[])}if(Me.status==="fulfilled"){const Y=K(Me.value);b(((L=Y[0])==null?void 0:L.goodqty)||0)}else b(0);if(de.status==="fulfilled"){const Y=K(de.value);E(((V=Y[0])==null?void 0:V.rejetqty)||0)}else E(0);if(ee.status==="fulfilled"&&ee.value.body){const Y=K(ee.value);x(Y||[])}else console.log("No machine performance data available"),x([]);if(U.status==="fulfilled"){const Y=K(U.value);pe(Y||[])}const Xe=Ie.status==="fulfilled"&&Ie.value.body?K(Ie.value).reduce((Y,q)=>(Y[q.date]=parseFloat(q.oee)||0,Y),{}):{},mt=ve.status==="fulfilled"&&ve.value.body?K(ve.value).reduce((Y,q)=>{const De=parseFloat(q.speed);return!isNaN(De)&&De>0&&(Y[q.date]=De),Y},{}):{},Te=[...[...new Set([...Object.keys(Xe),...Object.keys(mt)])]].sort((Y,q)=>T(Y).diff(T(q)));let we=Te;if(Te.length>0){const Y=T(Te[Te.length-1]);we=Te.filter(q=>Y.diff(T(q),"day")<=60)}const Ee=we.map(Y=>({date:Y,oee:Xe[Y]||0,speed:mt[Y]||null})).sort((Y,q)=>T(Y.date).diff(T(q.date)));if(ye&&ye.status==="fulfilled"&&ye.value.body){const Y=K(ye.value);if(Y.length>0)try{const q=Y.map(I=>{const ne=parseFloat(I.Good_QTY_Day||I.good||0),Qe=parseFloat(I.Rejects_QTY_Day||I.reject||0),Ze=parseFloat(I.OEE_Day||I.oee||0),tt=parseFloat(I.Speed_Day||I.speed||0),at=parseFloat(I.Availability_Rate_Day||I.availability||0),Ve=parseFloat(I.Performance_Rate_Day||I.performance||0),ft=parseFloat(I.Quality_Rate_Day||I.quality||0);let Ae=null;try{const P=I.Date_Insert_Day||I.date;if(P)if(T(P).isValid())Ae=T(P).format("YYYY-MM-DD");else{const B=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const ge of B){const Ye=T(P,ge);if(Ye.isValid()){Ae=Ye.format("YYYY-MM-DD");break}}}Ae||(console.warn(`Invalid date found: ${I.Date_Insert_Day||I.date}, using today's date instead`),Ae=T().format("YYYY-MM-DD"))}catch(P){console.error("Error parsing date:",P),Ae=T().format("YYYY-MM-DD")}const rt=oe(Ze),nt=oe(at),o=oe(Ve),D=oe(ft);return{date:Ae,oee:rt,speed:isNaN(tt)?0:tt,good:isNaN(ne)?0:ne,reject:isNaN(Qe)?0:Qe,Machine_Name:I.Machine_Name||"N/A",Shift:I.Shift||"N/A",availability:nt,performance:o,quality:D}}).sort((I,ne)=>T(I.date).diff(T(ne.date)));if(q.some(I=>I.good>0||I.reject>0||I.oee>0||I.speed>0))p(q);else{console.warn("No valid data points found in processed mould data");const I=Ce();p(I)}}catch(q){console.error("Error processing mould data:",q),p(Ee)}else if(console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback"),Ee.length>0)p(Ee);else{const q=Ce();p(q)}}else if(console.log("Machine daily mould API request failed or returned invalid data"),ye&&ye.status==="rejected"&&console.error("API error:",ye.reason),Ee.length>0)p(Ee);else{const Y=Ce();p(Y),console.log("Using sample data for default dashboard state (IPS model)")}Ie.status==="fulfilled"&&M(K(Ie.value)||[]),ve.status==="fulfilled"&&j(K(ve.value)||[]),Je.status==="fulfilled"&&X(K(Je.value)||[])}catch(F){console.error("Error loading data:",F),b(0),E(0),C([]),x([])}finally{u(!1)}},[n,l,a,t,xe,Ce]),ce=d.useCallback(async()=>{var L,V;try{u(!0);const F=await Promise.allSettled([c("get","/api/sidecards-prod"),c("get","/api/sidecards-prod-rejet")]),[z,W]=F;if(z.status==="fulfilled"){const Z=K(z.value);b(((L=Z[0])==null?void 0:L.goodqty)||15e3)}else console.error("Failed to fetch good quantity:",z.reason),b(15e3);if(W.status==="fulfilled"){const Z=K(W.value);E(((V=Z[0])==null?void 0:V.rejetqty)||750)}else console.error("Failed to fetch rejected quantity:",W.reason),E(750)}catch(F){console.error("Error loading general data:",F),b(15e3),E(750)}finally{u(!1)}},[]),et=d.useCallback(()=>{let L=0;i.length>0&&(L=i.reduce((de,ee)=>{let U=parseFloat(ee.oee||0);return U=oe(U),de+U},0)/i.length);const V=y+g>0?g/(y+g)*100:0,F=y+g>0?y/(y+g)*100:0;let z=0;i.length>0&&(z=i.reduce((de,ee)=>{let U=parseFloat(ee.availability||0);return U=oe(U),de+U},0)/i.length);let W=0;i.length>0&&(W=i.reduce((de,ee)=>{let U=parseFloat(ee.performance||0);return U=oe(U),de+U},0)/i.length);let Z=0;return i.length>0&&(Z=i.reduce((de,ee)=>{let U=parseFloat(ee.quality||0);return U=oe(U),de+U},0)/i.length),{avgTRS:L,rejectRate:V,qualityRate:F,avgAvailability:z,avgPerformance:W,avgQuality:Z}},[i,y,g]);return d.useEffect(()=>{console.log("🔄 Data fetch effect triggered:",{selectedMachineModel:n,selectedMachine:l,dateFilter:a,dateRangeType:t}),n?(console.log("📊 Fetching production data for model:",n),G()):(console.log("📊 Fetching general data (no machine model selected)"),ce())},[n,l,a,t,G,ce]),{loading:h,chartData:i,machinePerformance:R,mergedData:f,uniqueDates:s,goodQty:y,rejetQty:g,oeeTrends:m,speedTrends:$,hourlyTrends:H,shiftComparison:Q,fetchData:G,fetchGeneralData:ce,calculateStatistics:et}},vt=d.createContext(),Ma=({children:n})=>{const l=_a(),a=xa(),t=Ca({selectedMachineModel:l.selectedMachineModel,selectedMachine:l.selectedMachine,dateFilter:a.dateFilter,dateRangeType:a.dateRangeType,buildDateQueryParams:a.buildDateQueryParams}),r=t.calculateStatistics(),c={...l,...a,...t,...r,resetFilters:()=>{a.resetDateFilter(),a.setDateRangeType("day"),l.setSelectedMachineModel(""),l.setSelectedMachine("")},handleRefresh:()=>{t.fetchData()}};return e.createElement(vt.Provider,{value:c},n)},Pa=()=>{const n=d.useContext(vt);if(n===void 0)throw new Error("useProduction must be used within a ProductionProvider");return n},Na=(n,l,a,t,r,c,h)=>[{title:"Production Totale",value:je(n,"Pcs"),rawValue:n,suffix:"Pcs",icon:e.createElement(sa,null),color:S.PRIMARY_BLUE,description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:je(l,"Kg"),rawValue:l,suffix:"Kg",icon:e.createElement(ca,null),color:S.PRIMARY_BLUE,description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:je(a,"%"),rawValue:a,suffix:"%",icon:e.createElement(St,null),color:S.PRIMARY_BLUE,description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:je(t,"%"),rawValue:t,suffix:"%",icon:e.createElement(da,null),color:S.PRIMARY_BLUE,description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:je(r,"%"),rawValue:r,suffix:"%",icon:e.createElement(_t,null),color:S.PRIMARY_BLUE,description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:je(c,"%"),rawValue:c,suffix:"%",icon:e.createElement(ma,null),color:S.PRIMARY_BLUE,description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:je(h,"%"),rawValue:h,suffix:"%",icon:e.createElement(ua,null),color:S.PRIMARY_BLUE,description:"Pourcentage de pièces bonnes sur la production totale"}],{Text:gt}=dt,Ta=(n,l)=>[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>e.createElement(J,null,e.createElement(Mt,{style:{color:n[0]}}),e.createElement(gt,{strong:!0},a||"N/A")),sorter:(a,t)=>(a.Machine_Name||"").localeCompare(t.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:a=>{if(!a)return e.createElement(gt,null,"N/A");const t=new Date(a);return e.createElement(gt,null,t.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}))},sorter:(a,t)=>{const r=new Date(a.Date_Insert_Day||0),c=new Date(t.Date_Insert_Day||0);return r-c}},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:a=>e.createElement(k,{color:"green"},Ge(parseFloat(a)||0)," h"),sorter:(a,t)=>(parseFloat(a.Run_Hours_Day)||0)-(parseFloat(t.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:a=>e.createElement(k,{color:"orange"},Ge(parseFloat(a)||0)," h"),sorter:(a,t)=>(parseFloat(a.Down_Hours_Day)||0)-(parseFloat(t.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:a=>e.createElement(k,{color:"green"},ct(parseInt(a)||0)," pcs"),sorter:(a,t)=>(parseInt(a.Good_QTY_Day)||0)-(parseInt(t.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:a=>e.createElement(k,{color:"red"},ct(parseInt(a)||0)," pcs"),sorter:(a,t)=>(parseInt(a.Rejects_QTY_Day)||0)-(parseInt(t.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:a=>e.createElement(k,{color:"blue"},Ge(parseFloat(a)||0)),sorter:(a,t)=>(parseFloat(a.Speed_Day)||0)-(parseFloat(t.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:a=>{const t=l(a);return e.createElement(he,{title:`${Se(t,1)}% de disponibilité`},e.createElement(_e,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${Se(r,1)}%`:"0,0%"}))},sorter:(a,t)=>l(a.Availability_Rate_Day)-l(t.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:a=>{const t=l(a);return e.createElement(he,{title:`${t.toFixed(1)}% de performance`},e.createElement(_e,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>l(a.Performance_Rate_Day)-l(t.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:a=>{const t=l(a);return e.createElement(he,{title:`${t.toFixed(1)}% de qualité`},e.createElement(_e,{percent:t,size:"small",status:t>90?"success":t>80?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>l(a.Quality_Rate_Day)-l(t.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:a=>{const t=l(a);return e.createElement(he,{title:`${t.toFixed(1)}% de TRS`},e.createElement(_e,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>l(a.OEE_Day)-l(t.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:a=>e.createElement(k,{color:"blue"},a||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(a,t)=>t.Shift===a},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:a=>e.createElement(k,{color:"purple"},a||"N/A")},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:a=>e.createElement(k,{color:"cyan"},a||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:a=>e.createElement(k,{color:"magenta"},a||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:a=>e.createElement(k,{color:"gold"},a||"N/A")},{title:"Actions",key:"actions",fixed:"right",width:80,render:()=>e.createElement(Vt,{menu:{items:[{key:"1",icon:e.createElement(kt,null),label:"Voir tendances"},{key:"2",icon:e.createElement(Ut,null),label:"Paramètres"},{key:"3",icon:e.createElement(st,null),label:"Exporter données"}]},trigger:["click"]},e.createElement(Oe,{type:"text",icon:e.createElement(Jt,null)}))}],{Text:ht}=dt,Aa=(n,l=[])=>{const a=t=>{if(t==null||t==="")return 0;if(typeof t=="number"&&!isNaN(t))return t<=1&&t>0?t*100:t;if(typeof t=="string"){const r=parseFloat(t.replace(",","."));if(!isNaN(r))return r<=1&&r>0?r*100:r}return 0};return[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:t=>e.createElement(J,null,e.createElement(Mt,{style:{color:n[0]}}),e.createElement(ht,{strong:!0},t||"N/A")),filters:Array.from(new Set(l.map(t=>t.Machine_Name||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,r)=>r.Machine_Name===t||t==="N/A"&&!r.Machine_Name,sorter:(t,r)=>(t.Machine_Name||"").localeCompare(r.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:t=>{if(!t)return e.createElement(ht,null,"N/A");const r=new Date(t);return e.createElement(J,null,e.createElement(fa,{style:{color:n[1]}}),e.createElement(ht,null,r.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})))},sorter:(t,r)=>{const c=new Date(t.Date_Insert_Day||0),h=new Date(r.Date_Insert_Day||0);return c-h},defaultSortOrder:"descend"},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:t=>e.createElement(k,{color:"green"},Ge(parseFloat(t)||0)," h"),sorter:(t,r)=>(parseFloat(t.Run_Hours_Day)||0)-(parseFloat(r.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:t=>e.createElement(k,{color:"orange"},Ge(parseFloat(t)||0)," h"),sorter:(t,r)=>(parseFloat(t.Down_Hours_Day)||0)-(parseFloat(r.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:t=>e.createElement(k,{color:"green"},ct(parseInt(t)||0)," pcs"),sorter:(t,r)=>(parseInt(t.Good_QTY_Day)||0)-(parseInt(r.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:t=>e.createElement(k,{color:"red"},ct(parseInt(t)||0)," pcs"),sorter:(t,r)=>(parseInt(t.Rejects_QTY_Day)||0)-(parseInt(r.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:t=>e.createElement(k,{color:"blue"},Ge(parseFloat(t)||0)),sorter:(t,r)=>(parseFloat(t.Speed_Day)||0)-(parseFloat(r.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de disponibilité`},e.createElement(_e,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:c=>typeof c=="number"&&!isNaN(c)?`${Se(c,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Availability_Rate_Day)-a(r.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de performance`},e.createElement(_e,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:c=>typeof c=="number"&&!isNaN(c)?`${Se(c,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Performance_Rate_Day)-a(r.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de qualité`},e.createElement(_e,{percent:r,size:"small",status:r>90?"success":r>80?"normal":"exception",format:c=>typeof c=="number"&&!isNaN(c)?`${Se(c,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Quality_Rate_Day)-a(r.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de TRS`},e.createElement(_e,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:c=>typeof c=="number"&&!isNaN(c)?`${Se(c,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.OEE_Day)-a(r.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:t=>e.createElement(k,{color:"blue"},t||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(t,r)=>r.Shift===t},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:t=>e.createElement(k,{color:"purple"},t||"N/A"),filters:Array.from(new Set(l.map(t=>t.Part_Number||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,r)=>r.Part_Number===t||t==="N/A"&&!r.Part_Number},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:t=>e.createElement(k,{color:"cyan"},t||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:t=>e.createElement(k,{color:"magenta"},t||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:t=>e.createElement(k,{color:"gold"},t||"N/A")}]},Pt=(n,l=!1,a=0)=>{if(!n)return"N/A";try{const t=T(n);return t.isValid()?l?a>100?t.format("MM/DD"):a>50?t.format("MM/DD/YY"):t.format("DD/MM/YYYY"):a>30?t.format("MM/DD"):t.format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",t),"N/A"}},ke=[S.PRIMARY_BLUE,S.SECONDARY_BLUE,S.CHART_TERTIARY,S.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],xt=d.memo(({data:n,title:l,dataKey:a,color:t,label:r="Quantité",tooltipLabel:c="Quantité",isKg:h=!1,height:u=300,enhanced:i=!1,expanded:C=!1,zoom:R=1,selectedDataPoints:x=[],chartConfig:f={},dimensions:p={},isModal:s=!1})=>{const{charts:_,theme:y}=He(),b=Re({charts:_,theme:y});if(!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée disponible"}));const g=b.responsiveContainerProps,E=b.gridConfig,m=b.axisConfig,M=b.tooltipConfig,$=b.legendConfig,j=b.displayConfig,H=b.height||u,pe=b.margins,Q=p.margin||pe,X=p.fontSize||(i?14:12),xe=p.labelAngle||(i?-45:0),Ce=p.labelHeight||(i?100:60);return e.createElement(fe,{...g,height:H},e.createElement(qe,{data:n,margin:Q},e.createElement(le,{...E}),e.createElement(ie,{dataKey:"date",...m,tick:{fill:"#666",fontSize:X},tickFormatter:G=>Pt(G,C||i,n.length),interval:f.labelInterval!==void 0?f.labelInterval:i?0:"preserveStartEnd",angle:xe,textAnchor:xe!==0?"end":"middle",height:Ce,minTickGap:C?5:10}),e.createElement(se,{...m,tick:{fontSize:X},tickFormatter:G=>G.toLocaleString(),label:{value:h?`${r} (kg)`:r,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:X}}}),e.createElement(ae,{...M,formatter:G=>{const ce=parseFloat(G);return[isNaN(ce)?"N/A":ce.toLocaleString(),h?`${c} (kg)`:c]},labelFormatter:G=>{try{return G&&T(G).isValid()?`Date: ${T(G).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),(i||_.showLegend)&&e.createElement(re,{...$}),e.createElement(Be,{dataKey:a,name:c,fill:t,maxBarSize:f.maxBarSize||(i?60:40),radius:i||C?[4,4,0,0]:[0,0,0,0],...b.getBarElementConfig(t)},j.showDataLabels&&e.createElement(Ea,{dataKey:a,position:"top",formatter:G=>{const ce=parseFloat(G);return isNaN(ce)?"":ce.toLocaleString()},style:{fill:"#666",fontSize:"10px"}}))))}),Ya=d.memo(({data:n,title:l,dataKey:a,color:t,label:r="Quantité",tooltipLabel:c="Quantité",isKg:h=!1,height:u=300,enhanced:i=!1,expanded:C=!1,zoom:R=1,selectedDataPoints:x=[],chartConfig:f={},dimensions:p={},isModal:s=!1})=>{var E;if(l&&l.includes("Temps d'arrêt")&&(n==null?void 0:n.length)>0&&console.log("EnhancedShiftBarChart - Downtime data received:",n.map(m=>({Shift:m.Shift,[a]:m[a]}))),!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée disponible"}));const _=p.margin||(i?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),y=p.fontSize||(i?14:12),b=p.labelAngle||(i?-45:0),g=p.labelHeight||(i?100:60);return e.createElement(fe,{width:"100%",height:u},e.createElement(qe,{data:n,margin:_},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Shift",tick:{fill:"#666",fontSize:y},tickFormatter:m=>m||"N/A",interval:f.labelInterval!==void 0?f.labelInterval:i?0:"preserveStartEnd",angle:b,textAnchor:b!==0?"end":"middle",height:g,minTickGap:C?5:10}),e.createElement(se,{tick:{fontSize:y},tickFormatter:m=>m.toLocaleString(),domain:l&&l.includes("Temps d'arrêt")?[0,"dataMax"]:["auto","auto"],label:{value:h?`${r} (kg)`:r,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:y}}}),e.createElement(ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:i?14:12},formatter:m=>{const M=parseFloat(m);return[isNaN(M)?"N/A":M.toLocaleString(),h?`${c} (kg)`:c]},labelFormatter:m=>`Équipe: ${m}`}),i&&e.createElement(re,null),e.createElement(Be,{dataKey:a,name:c,fill:t,maxBarSize:f.maxBarSize||(i?60:40),radius:i||C?[4,4,0,0]:[0,0,0,0],label:(E=enhancedChartConfig.displayConfig)!=null&&E.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),wt=d.memo(({data:n,color:l=ke[0],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:c=1,selectedDataPoints:h=[],chartConfig:u={},dimensions:i={},isModal:C=!1})=>{var b;const{charts:R,theme:x}=He(),f=Re({charts:R,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée TRS disponible"}));const p=i.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),s=i.fontSize||(t?14:12),_=i.labelAngle||(t?-45:0),y=i.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(We,{data:n,margin:p},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"date",tick:{fill:"#666",fontSize:s},tickFormatter:g=>Pt(g,r||t,n.length),interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:s},tickFormatter:g=>`${g}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:s}}}),e.createElement(ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>{const E=parseFloat(g);return[!isNaN(E)?`${E.toFixed(2)}%`:`${g}%`,"TRS"]},labelFormatter:g=>{try{return g&&T(g).isValid()?`Date: ${T(g).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(re,null),e.createElement(Ke,{type:"monotone",dataKey:"oee",name:"TRS",stroke:l,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:l},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:l,strokeWidth:2},label:(b=f.displayConfig)!=null&&b.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Ft=d.memo(({data:n,color:l=ke[0],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:c=1,selectedDataPoints:h=[],chartConfig:u={},dimensions:i={},isModal:C=!1})=>{var b;const{charts:R,theme:x}=He(),f=Re({charts:R,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée TRS disponible"}));const p=i.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),s=i.fontSize||(t?14:12),_=i.labelAngle||(t?-45:0),y=i.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(We,{data:n,margin:p},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Shift",tick:{fill:"#666",fontSize:s},tickFormatter:g=>g||"N/A",interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:s},tickFormatter:g=>`${g}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:s}}}),e.createElement(ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>{let E=parseFloat(g);const m=!isNaN(E);return m&&E<=1&&E>0&&(E=E*100),[m?`${E.toFixed(2)}%`:`${g}%`,"TRS"]},labelFormatter:g=>`Équipe: ${g}`}),t&&e.createElement(re,null),e.createElement(Ke,{type:"monotone",dataKey:"oee",name:"TRS",stroke:l,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:l},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:l,strokeWidth:2},label:(b=f.displayConfig)!=null&&b.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Lt=d.memo(({data:n,color:l=ke[5],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:c=1,selectedDataPoints:h=[],chartConfig:u={},dimensions:i={},isModal:C=!1})=>{var b;const{charts:R,theme:x}=He(),f=Re({charts:R,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée de performance disponible"}));const p=i.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),s=i.fontSize||(t?14:12),_=i.labelAngle||(t?-45:0),y=i.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(We,{data:n,margin:p},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Shift",tick:{fill:"#666",fontSize:s},tickFormatter:g=>g||"N/A",interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:s},tickFormatter:g=>`${g}%`,domain:[0,100],label:{value:"Performance (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:s}}}),e.createElement(ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>{let E=parseFloat(g);const m=!isNaN(E);return m&&E<=1&&E>0&&(E=E*100),[m?`${E.toFixed(2)}%`:`${g}%`,"Performance"]},labelFormatter:g=>`Équipe: ${g}`}),t&&e.createElement(re,null),e.createElement(Ke,{type:"monotone",dataKey:"performance",name:"Performance",stroke:l,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:l},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:l,strokeWidth:2},label:(b=f.displayConfig)!=null&&b.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),$t=d.memo(({data:n,color:l=ke[1],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:c=1,selectedDataPoints:h=[],chartConfig:u={},dimensions:i={},isModal:C=!1})=>{var b;const{charts:R,theme:x}=He(),f=Re({charts:R,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée de cycle disponible"}));const p=i.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),s=i.fontSize||(t?14:12),_=i.labelAngle||(t?-45:0),y=i.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(We,{data:n,margin:p},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"date",tick:{fill:"#666",fontSize:s},tickFormatter:g=>Pt(g,r||t,n.length),interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:_,textAnchor:_!==0?"end":"middle",height:y,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:s},tickFormatter:g=>`${g}s`,label:{value:"Cycle De Temps (s)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:s}}}),e.createElement(ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:g=>[typeof g=="number"&&!isNaN(g)?`${g.toFixed(2)}s`:`${g}s`,"Cycle De Temps"],labelFormatter:g=>{try{return g&&T(g).isValid()?`Date: ${T(g).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(re,null),e.createElement(Ke,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:l,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:l},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:l,strokeWidth:2},label:(b=f.displayConfig)!=null&&b.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),zt=d.memo(({data:n,dataKey:l="value",nameKey:a="name",colors:t=ke,height:r,enhanced:c=!1,zoom:h=1,selectedDataPoints:u=[]})=>{var R;const i=Re({chartType:"pie",allowedTypes:["pie"]});if(!n||n.length===0)return e.createElement("div",{style:{height:r||i.height,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée disponible"}));const C=r||i.height;return e.createElement(fe,{width:"100%",height:C},e.createElement(ga,{margin:i.margins},e.createElement(ha,{data:n,dataKey:l,nameKey:a,cx:"50%",cy:"50%",innerRadius:c?80:60,outerRadius:c?120:80,paddingAngle:c?8:5,label:c?({name:x,percent:f})=>`${x}: ${(f*100).toFixed(1)}%`:!1,labelLine:c},n.map((x,f)=>e.createElement(ya,{key:`cell-${f}`,fill:t[f%t.length]}))),e.createElement(ae,{...i.tooltipConfig,formatter:(x,f)=>[typeof x=="number"?x.toLocaleString():x,f]}),((R=i.charts)==null?void 0:R.showLegend)&&e.createElement(re,{...i.legendConfig,layout:c?"horizontal":"vertical",verticalAlign:c?"bottom":"middle",align:c?"center":"right",wrapperStyle:{paddingLeft:c?0:24,paddingTop:c?20:0,fontSize:c?14:12}})))}),ka=d.memo(({data:n,color:l=ke[2],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:c=[]})=>{var x;const h=Re({chartType:"bar",allowedTypes:["bar"]}),u=a||h.height;if(!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée de production disponible"}));const i=n.reduce((f,p)=>{const s=p.Machine_Name;return f[s]||(f[s]={Machine_Name:s,production:0}),f[s].production+=Number(p.production)||0,f},{}),C=Object.values(i),R=t?14:12;return e.createElement(fe,{width:"100%",height:u},e.createElement(qe,{data:C,margin:h.margins},e.createElement(le,{...h.gridConfig}),e.createElement(ie,{dataKey:"Machine_Name",...h.axisConfig,tick:{fontSize:R},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(se,{...h.axisConfig,tick:{fontSize:R},tickFormatter:f=>f.toLocaleString(),label:{value:"Production (pcs)",angle:-90,position:"insideLeft",style:{fontSize:R}}}),e.createElement(ae,{...h.tooltipConfig,formatter:f=>[typeof f=="number"&&!isNaN(f)?Number.isInteger(f)?f.toLocaleString():f.toFixed(2):f,"Production"]}),((x=h.charts)==null?void 0:x.showLegend)&&e.createElement(re,{...h.legendConfig}),e.createElement(Be,{dataKey:"production",name:"Production",fill:l,radius:t?[4,4,0,0]:[0,0,0,0]})))}),jt=d.memo(({data:n,color:l=ke[4],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:c=[]})=>{var x;const h=Re({chartType:"bar",allowedTypes:["bar"]}),u=a||h.height;if(!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée de rejets disponible"}));const i=n.reduce((f,p)=>{const s=p.Machine_Name;return f[s]||(f[s]={Machine_Name:s,rejects:0}),f[s].rejects+=Number(p.rejects)||0,f},{}),C=Object.values(i),R=t?14:12;return e.createElement(fe,{width:"100%",height:u},e.createElement(qe,{data:C,margin:h.margins},e.createElement(le,{...h.gridConfig}),e.createElement(ie,{dataKey:"Machine_Name",...h.axisConfig,tick:{fontSize:R},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(se,{...h.axisConfig,tick:{fontSize:R},tickFormatter:f=>f.toLocaleString(),label:{value:"Rejets (kg)",angle:-90,position:"insideLeft",style:{fontSize:R}}}),e.createElement(ae,{...h.tooltipConfig,formatter:f=>[typeof f=="number"&&!isNaN(f)?Number.isInteger(f)?f.toLocaleString():f.toFixed(2):f,"Rejets"]}),((x=h.charts)==null?void 0:x.showLegend)&&e.createElement(re,{...h.legendConfig}),e.createElement(Be,{dataKey:"rejects",name:"Rejets",fill:l,radius:t?[4,4,0,0]:[0,0,0,0]})))}),qt=d.memo(({data:n,color:l=ke[5],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:c=[]})=>{if(Re({chartType:"bar",allowedTypes:["bar"]}),!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(te,{description:"Aucune donnée TRS disponible"}));const h=n.reduce((R,x)=>{const f=x.Machine_Name;R[f]||(R[f]={Machine_Name:f,trs:0,count:0});let p=Number(x.oee)||0;return p>0&&p<=1&&(p=p*100),R[f].trs+=p,R[f].count+=1,R},{}),u=Object.values(h).map(R=>({Machine_Name:R.Machine_Name,trs:R.count>0?R.trs/R.count:0})),i=t?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},C=t?14:12;return e.createElement(fe,{width:"100%",height:a},e.createElement(qe,{data:u,margin:i},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:C},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(se,{tick:{fontSize:C},tickFormatter:R=>`${R.toFixed(1)}%`,label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:C}},domain:[0,100]}),e.createElement(ae,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:R=>{let x=parseFloat(R);return isNaN(x)?["N/A","TRS"]:[`${x.toFixed(1)}%`,"TRS"]}}),t&&e.createElement(re,null),e.createElement(Be,{dataKey:"trs",name:"TRS",fill:l,radius:t?[4,4,0,0]:[0,0,0,0]})))});xt.displayName="EnhancedQuantityBarChart";Ya.displayName="EnhancedShiftBarChart";wt.displayName="EnhancedTRSLineChart";Ft.displayName="EnhancedShiftTRSLineChart";Lt.displayName="EnhancedPerformanceLineChart";$t.displayName="EnhancedCycleTimeLineChart";zt.displayName="EnhancedPieChart";ka.displayName="EnhancedMachineProductionChart";jt.displayName="EnhancedMachineRejectsChart";qt.displayName="EnhancedMachineTRSChart";const{Text:xr}=dt,Ia=({data:n,colors:l,dateRangeType:a,dateFilter:t,formatDateRange:r})=>{const{settings:c}=He();Re({chartType:"bar",allowedTypes:["bar","line"]});const h=d.useMemo(()=>{const C=pa(c);return C===null?l||["#1890ff","#52c41a","#faad14","#f5222d","#722ed1"]:C},[c,l]),u=C=>{console.log(`Chart expanded: ${C}`)},i=C=>{console.log(`Chart collapsed: ${C}`)};return e.createElement(e.Fragment,null,e.createElement(v,{span:24},e.createElement(be,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:`Quantité Bonne - ${a==="day"?"Journalière":a==="week"?"Hebdomadaire":"Mensuelle"}`,data:n,chartType:"bar",expandMode:"modal",onExpand:()=>u("quantity-good"),onCollapse:()=>i("quantity-good"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:t?"blue":"green"},r(t,a))}},e.createElement(xt,{data:n,title:"Quantité Bonne",dataKey:"good",color:h[2],tooltipLabel:"Quantité bonne"}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:`Quantité Rejetée - ${a==="day"?"Journalière":a==="week"?"Hebdomadaire":"Mensuelle"}`,data:n,chartType:"bar",expandMode:"modal",onExpand:()=>u("quantity-reject"),onCollapse:()=>i("quantity-reject"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:t?"blue":"green"},r(t,a))}},e.createElement(xt,{data:n,title:"Quantité Rejetée",dataKey:"reject",color:h[4],label:"Quantité",tooltipLabel:"Quantité rejetée",isKg:!0}))))),e.createElement(v,{xs:24,md:24},e.createElement(be,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Tendances TRS (Taux de Rendement Synthétique)",data:n,chartType:"line",expandMode:"modal",onExpand:()=>u("trs-trends"),onCollapse:()=>i("trs-trends"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:"cyan"},"Évolution TRS")}},e.createElement(wt,{data:n,color:h[0]}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Tendances Cycle De Temps",data:n,chartType:"line",expandMode:"modal",onExpand:()=>u("cycle-time-trends"),onCollapse:()=>i("cycle-time-trends"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:"orange"},"Évolution Cycle")}},e.createElement($t,{data:n,color:h[1]}))))))},Ct=d.memo(({data:n,title:l,dataKey:a,color:t,label:r="Valeur",tooltipLabel:c,isKg:h=!1,chartType:u,allowedTypes:i=["bar","line"],enhanced:C=!1,expanded:R=!1,isModal:x=!1,height:f=300,...p})=>{const s=It({fallbackType:"bar",allowedTypes:i,propChartType:u}),_=s.chartType,y=d.useMemo(()=>{if(!Array.isArray(n)||n.length===0)return[];const m=n.reduce((M,$)=>{const j=$.Shift||$.shift||$.name||$.label||"N/A",H=parseFloat($[a])||0;return j==="N/A"||(M[j]||(M[j]={shift:j,[a]:0,count:0,originalData:[]}),["production","rejects","downtime"].includes(a),M[j][a]+=H,M[j].count+=1,M[j].originalData.push($)),M},{});return Object.values(m).map(M=>(["production","rejects","downtime"].includes(a)||(M[a]=M[a]/M.count),{...M,[a]:Number(M[a])||0})).filter(M=>M[a]!==void 0&&M[a]!==null)},[n,a]),b=({active:m,payload:M,label:$})=>{if(m&&M&&M.length){const H=M[0].value,pe=h?`${H.toLocaleString()} kg`:H.toLocaleString(),Q=s.tooltipConfig;return e.createElement("div",{style:Q.contentStyle},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:s.getTextColor()}},`Équipe: ${$}`),e.createElement("p",{style:{margin:0,color:t||s.getPrimaryColor()}},`${c||$}: ${pe}`))}return null},g={data:y,margin:s.margins,...p},E=()=>{const m=t||s.getPrimaryColor();switch(_){case"line":return e.createElement(We,{...g},e.createElement(le,{...s.gridConfig}),e.createElement(ie,{dataKey:"shift",...s.axisConfig,tick:{fontSize:11}}),e.createElement(se,{...s.axisConfig,tick:{fontSize:11},label:{value:h?"Quantité (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(ae,{content:e.createElement(b,null)}),s.displayConfig.showLegend&&e.createElement(re,{...s.legendConfig}),e.createElement(Ke,{type:"monotone",dataKey:a,name:c||l,...s.getLineElementConfig(m)}));case"bar":default:return e.createElement(qe,{...g},e.createElement(le,{...s.gridConfig}),e.createElement(ie,{dataKey:"shift",...s.axisConfig,tick:{fontSize:11}}),e.createElement(se,{...s.axisConfig,tick:{fontSize:11},label:{value:h?"Quantité (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(ae,{content:e.createElement(b,null)}),s.displayConfig.showLegend&&e.createElement(re,{...s.legendConfig}),e.createElement(Be,{dataKey:a,name:c||l,...s.getBarElementConfig(m),maxBarSize:C||R?60:40}))}};return!y||y.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:f,width:"100%"}},e.createElement(te,{description:"Aucune donnée disponible pour les équipes",image:te.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{width:"100%",height:f}},e.createElement(fe,{...s.responsiveContainerProps},E()))});Ct.displayName="EnhancedShiftChart";const Bt=d.memo(({data:n,title:l,dataKey:a="production",color:t,label:r="Production",tooltipLabel:c,isKg:h=!0,chartType:u,allowedTypes:i=["bar","line"],enhanced:C=!1,expanded:R=!1,isModal:x=!1,height:f=300,...p})=>{const s=It({fallbackType:"bar",allowedTypes:i,propChartType:u}),_=s.chartType,y=d.useMemo(()=>!Array.isArray(n)||n.length===0?[]:n.map(m=>{const M=m.Machine_Name||m.Machine||m.machine||m.name||m.label||"N/A",$=m[a]||0;return{...m,machine:M,[a]:Number($)||0,originalData:m}}).filter(m=>m[a]!==void 0&&m[a]!==null),[n,a]),b=({active:m,payload:M,label:$})=>{const j=s.theme||{};if(m&&M&&M.length){const pe=M[0].value,Q=h?`${pe.toLocaleString()} Pcs`:pe.toLocaleString();return e.createElement("div",{style:{backgroundColor:j.darkMode?"#1f1f1f":"#ffffff",border:`1px solid ${t||S.PRIMARY_BLUE}`,borderRadius:"8px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:j.darkMode?"#ffffff":"#000000"}},`Machine: ${$}`),e.createElement("p",{style:{margin:0,color:t||S.PRIMARY_BLUE}},`${c||$}: ${Q}`))}return null},g={data:y,margin:s.margins,...p},E=()=>{var M,$;const m=t||S.PRIMARY_BLUE;switch(_){case"line":return e.createElement(We,{...g},e.createElement(le,{...s.gridConfig}),e.createElement(ie,{dataKey:"machine",...s.axisConfig,tick:{fontSize:11},angle:-45,textAnchor:"end",height:80}),e.createElement(se,{...s.axisConfig,tick:{fontSize:11},label:{value:h?"Production (Pcs)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(ae,{content:e.createElement(b,null)}),((M=s.charts)==null?void 0:M.showLegend)&&e.createElement(re,{...s.legendConfig}),e.createElement(Ke,{type:"monotone",dataKey:a,name:c||l,...s.getLineElementConfig(m)}));case"bar":default:return e.createElement(qe,{...g},e.createElement(le,{...s.gridConfig}),e.createElement(ie,{dataKey:"machine",...s.axisConfig,tick:{fontSize:11},angle:-45,textAnchor:"end",height:80}),e.createElement(se,{...s.axisConfig,tick:{fontSize:11},label:{value:h?"Production (Pcs)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(ae,{content:e.createElement(b,null)}),(($=s.charts)==null?void 0:$.showLegend)&&e.createElement(re,{...s.legendConfig}),e.createElement(Be,{dataKey:a,name:c||l,...s.getBarElementConfig(m),maxBarSize:C||R?60:40}))}};return!y||y.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:f,width:"100%"}},e.createElement(te,{description:"Aucune donnée disponible pour les machines",image:te.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{width:"100%",height:f}},e.createElement(fe,{...s.responsiveContainerProps},E()))});Bt.displayName="EnhancedMachineChart";const Qt=({dataSource:n=[],columns:l=[],loading:a=!1,title:t,totalRecords:r=0,pageSize:c=100,currentPage:h=1,onPageChange:u,onPageSizeChange:i,exportEnabled:C=!1,onExport:R,maxRecordsWarning:x=1e3,performanceMode:f=!1,rowKey:p="id",scroll:s={x:1300},expandable:_,...y})=>{const[b,g]=d.useState(!1),E=d.useMemo(()=>{const Q=n.length,X=Q*.1,xe=Q>x;return{recordCount:Q,estimatedRenderTime:X,isLargeDataset:xe,performanceLevel:Q>2e3?"poor":Q>1e3?"warning":"good"}},[n.length,x]),m=d.useCallback(async()=>{if(R){g(!0);try{await R({data:n,totalRecords:r,currentPage:h,pageSize:c})}catch(Q){console.error("Export failed:",Q)}finally{g(!1)}}},[R,n,r,h,c]),M=d.useMemo(()=>({current:h,pageSize:c,total:r,showSizeChanger:!0,showQuickJumper:r>1e3,pageSizeOptions:["50","100","200","500"],showTotal:(Q,X)=>`${X[0]}-${X[1]} sur ${Q} enregistrements`,onChange:u,onShowSizeChange:i,size:"default"}),[h,c,r,u,i]),$=()=>E.isLargeDataset?e.createElement(Gt,{message:`Attention: ${E.recordCount} enregistrements`,description:`Le chargement peut prendre ${Math.ceil(E.estimatedRenderTime/1e3)}s. Considérez l'utilisation de filtres pour améliorer les performances.`,type:"warning",showIcon:!0,icon:e.createElement(Da,null),style:{marginBottom:16},action:e.createElement(J,null,e.createElement(Oe,{size:"small",type:"link"},"Optimiser les filtres"))}):null,j=d.useMemo(()=>t?e.createElement(J,null,e.createElement("span",null,t),e.createElement(k,{color:E.performanceLevel==="good"?"green":E.performanceLevel==="warning"?"orange":"red"},E.recordCount," enregistrements"),f&&e.createElement(he,{title:`Temps de rendu estimé: ${E.estimatedRenderTime.toFixed(1)}ms`},e.createElement(Rt,{style:{color:"#1890ff"}}))):null,[t,E,f]),H=d.useMemo(()=>e.createElement(J,null,C&&e.createElement(Oe,{icon:e.createElement(st,null),onClick:m,loading:b,disabled:n.length===0},"Exporter"),f&&e.createElement(k,{color:"blue"},"Mode Performance")),[C,m,b,n.length,f]),pe=d.useMemo(()=>({...y,dataSource:n,columns:l,loading:a,rowKey:p,scroll:E.isLargeDataset?{...s,y:400}:s,pagination:r>c?M:!1,size:E.isLargeDataset?"small":"middle",expandable:_,title:j?()=>j:void 0,extra:H,virtual:E.isLargeDataset,sticky:!0,showSorterTooltip:!1,rowSelection:y.rowSelection?{...y.rowSelection,preserveSelectedRowKeys:!0}:void 0}),[y,n,l,a,p,s,E.isLargeDataset,r,c,M,_,j,H]);return e.createElement("div",null,e.createElement($,null),e.createElement(Yt,{...pe}),r>1e3&&e.createElement("div",{style:{marginTop:16,textAlign:"center"}},e.createElement(Ot,{...M,simple:!1,showLessItems:!1})))};Qt.propTypes={dataSource:w.array,columns:w.array,loading:w.bool,title:w.string,totalRecords:w.number,pageSize:w.number,currentPage:w.number,onPageChange:w.func,onPageSizeChange:w.func,exportEnabled:w.bool,onExport:w.func,maxRecordsWarning:w.number,performanceMode:w.bool,rowKey:w.oneOfType([w.string,w.func]),scroll:w.object,expandable:w.object};w.array,w.string,w.node,w.number,w.bool,w.bool,w.bool,w.bool,w.bool,w.number,w.func,w.func,w.node;const va=n=>n?new Date(n).toISOString().split("T")[0]:null,At=(n,l)=>{if(!(n!=null&&n.start))return"Toutes les dates";const a=T(n.start),t=n.end?T(n.end):a;return l==="day"?a.format("DD/MM/YYYY"):l==="week"?`${a.format("DD/MM")} - ${t.format("DD/MM/YYYY")}`:l==="month"?a.format("MM/YYYY"):`${a.format("DD/MM/YYYY")} - ${t.format("DD/MM/YYYY")}`};T.locale("fr");const{Title:yt,Text:Et,Paragraph:Dt}=dt,{useBreakpoint:wa}=Ht,Fa=()=>{var rt,nt;const n=la(),{getChartColor:l,getChartColors:a,charts:t}=n,{dateFilter:r,dateRangeType:c,dateRangeDescription:h,selectedMachineModel:u,selectedMachine:i,machineModels:C,filteredMachineNames:R,handleMachineModelChange:x,handleMachineChange:f,handleDateChange:p,handleDateRangeTypeChange:s,resetFilters:_,handleRefresh:y}=Pa();d.useCallback(()=>{const o={};return u&&(o.model=u),i&&(o.machine=i),r!=null&&r.start&&(r!=null&&r.end)&&(o.startDate=r.start.format("YYYY-MM-DD"),o.endDate=r.end.format("YYYY-MM-DD")),o.dateRangeType=c,o},[u,i,r,c]);const{getDashboardData:b,getAllDailyProduction:g,loading:E}=ra(),[m,M]=d.useState({allDailyProduction:[],productionChart:{data:[],dataSource:"unknown"},sidecards:{goodqty:0,rejetqty:0,dataSource:"unknown"},machinePerformance:{data:[],dataSource:"unknown"},shiftPerformance:{data:[],dataSource:"unknown"},availabilityTrend:[]}),[$,j]=d.useState({elasticsearch:!1,mysql:!0,primary:"mysql"}),H=d.useCallback(async()=>{var o,D,P,B,ge,Ye,Pe,Ne,ot,lt,Fe,Le;try{const O={dateRangeType:c,model:u||void 0,machine:i||void 0};r&&(typeof r.format=="function"?O.date=r.format("YYYY-MM-DD"):r instanceof Date?O.date=r.toISOString().split("T")[0]:typeof r=="string"&&(O.date=r)),Object.keys(O).forEach(N=>{O[N]===void 0&&delete O[N]});const[$e,A]=await Promise.all([g(O),b(O)]);if(A){const N=((o=A.productionChart)==null?void 0:o.dataSource)||((D=A.sidecards)==null?void 0:D.dataSource)||((P=A.machinePerformance)==null?void 0:P.dataSource)||"mysql";j({elasticsearch:N==="elasticsearch",mysql:N==="mysql"||N==="unknown",primary:N})}M({allDailyProduction:($e==null?void 0:$e.getAllDailyProduction)||[],productionChart:{data:(((B=A==null?void 0:A.productionChart)==null?void 0:B.data)||[]).map(N=>({date:N.Date_Insert_Day,good:parseInt(N.Total_Good_Qty_Day)||0,reject:parseInt(N.Total_Rejects_Qty_Day)||0,oee:parseFloat(N.OEE_Day)||0,speed:parseFloat(N.Speed_Day)||0,availability:parseFloat(N.Availability_Rate_Day)||0,performance:parseFloat(N.Performance_Rate_Day)||0,quality:parseFloat(N.Quality_Rate_Day)||0,OEE_Day:parseFloat(N.OEE_Day)||0,Availability_Rate_Day:parseFloat(N.Availability_Rate_Day)||0,Performance_Rate_Day:parseFloat(N.Performance_Rate_Day)||0,Quality_Rate_Day:parseFloat(N.Quality_Rate_Day)||0})),dataSource:((ge=A==null?void 0:A.productionChart)==null?void 0:ge.dataSource)||"unknown"},sidecards:{goodqty:parseInt((Ye=A==null?void 0:A.sidecards)==null?void 0:Ye.goodqty)||0,rejetqty:parseInt((Pe=A==null?void 0:A.sidecards)==null?void 0:Pe.rejetqty)||0,dataSource:((Ne=A==null?void 0:A.sidecards)==null?void 0:Ne.dataSource)||"unknown"},machinePerformance:{data:(((ot=A==null?void 0:A.machinePerformance)==null?void 0:ot.data)||[]).map(N=>({...N,availability:parseFloat(N.availability)||0,performance:parseFloat(N.performance)||0,oee:parseFloat(N.oee)||0,quality:parseFloat(N.quality)||0,disponibilite:parseFloat(N.disponibilite)||0,downtime:parseFloat(N.downtime)||0})),dataSource:((lt=A==null?void 0:A.machinePerformance)==null?void 0:lt.dataSource)||"unknown"},shiftPerformance:{data:(((Fe=A==null?void 0:A.shiftPerformance)==null?void 0:Fe.data)||[]).map(N=>({...N,name:N.Shift||N.name,availability:parseFloat(N.availability)||0,performance:parseFloat(N.performance)||0,oee:parseFloat(N.oee)||0,quality:parseFloat(N.quality)||0,disponibilite:parseFloat(N.disponibilite)||0,downtime:parseFloat(N.downtime)||0})),dataSource:((Le=A==null?void 0:A.shiftPerformance)==null?void 0:Le.dataSource)||"unknown"},availabilityTrend:(A==null?void 0:A.availabilityTrend)||[]})}catch(O){console.error("Error fetching GraphQL data:",O)}},[c,u,i,r]);d.useEffect(()=>{H()},[H]);const[pe,Q]=d.useState("1"),[X,xe]=d.useState(0),[Ce,G]=d.useState(null),[ce,et]=d.useState(""),[L,V]=d.useState(!1),[F,z]=d.useState(!1),W=wa(),[Z,Me]=d.useState(!1),de=d.useCallback(o=>{p(o),Me(!!o)},[]);d.useEffect(()=>{var D,P;const o=m.allDailyProduction.length+(((D=m.productionChart.data)==null?void 0:D.length)||0)+(((P=m.machinePerformance.data)==null?void 0:P.length)||0);xe(o)},[m]);const ee=d.useCallback(async o=>{},[]),U=d.useCallback((o,D)=>{G(o),et(D),V(!!o),o&&Q("3")},[]),Ie=d.useCallback(o=>{z(!1),o.type==="production-data"&&Q("3")},[]);d.useCallback(()=>{G(null),et(""),V(!1)},[]);const ve=d.useMemo(()=>{let o=0,D=0,P=0,B=0;const ge=m.productionChart.data||[],Ye=m.sidecards;if(ge.length>0){const Fe=ge.reduce((Le,O)=>{let $e=parseFloat(O.oee||O.OEE_Day||0),A=parseFloat(O.availability||O.Availability_Rate_Day||0),N=parseFloat(O.performance||O.Performance_Rate_Day||0),ze=parseFloat(O.quality||O.Quality_Rate_Day||0);return $e=oe($e),A=oe(A),N=oe(N),ze=oe(ze),{oee:Le.oee+$e,availability:Le.availability+A,performance:Le.performance+N,quality:Le.quality+ze}},{oee:0,availability:0,performance:0,quality:0});o=Fe.oee/ge.length,D=Fe.availability/ge.length,P=Fe.performance/ge.length,B=Fe.quality/ge.length}const Pe=parseInt(Ye.goodqty)||0,Ne=parseInt(Ye.rejetqty)||0,ot=Pe+Ne>0?Ne/(Pe+Ne)*100:0,lt=Pe+Ne>0?Pe/(Pe+Ne)*100:0;return{avgTRS:o,avgAvailability:D,avgPerformance:P,avgQuality:B,rejectRate:ot,qualityRate:lt,totalGood:Pe,totalRejects:Ne}},[m]),{avgTRS:Je,avgAvailability:ye,avgPerformance:Xe,avgQuality:mt,rejectRate:ut,qualityRate:Te,totalGood:we,totalRejects:Ee}=ve,Y=d.useCallback(()=>{const D=new Date().getHours();return D>=6&&D<14?"Matin":D>=14&&D<22?"Après-midi":"Nuit"},[]),q=d.useMemo(()=>Na(we,Ee,Je,ye,Xe,ut,Te),[we,Ee,Je,ye,Xe,ut,Te]),De=d.useMemo(()=>Ta(a([S.PRIMARY_BLUE,S.SECONDARY_BLUE,S.CHART_TERTIARY,S.SUCCESS_GREEN,S.WARNING_ORANGE]),oe),[a]),I=d.useMemo(()=>Aa(a([S.PRIMARY_BLUE,S.SECONDARY_BLUE,S.CHART_TERTIARY,S.SUCCESS_GREEN,S.WARNING_ORANGE]),m.allDailyProduction),[a,m.allDailyProduction]),ne=d.useCallback((o,D=0)=>{if(o==null||o==="")return D;if(typeof o=="number"&&!isNaN(o))return o;const P=String(o).trim().replace(",","."),B=parseFloat(P);return isNaN(B)?D:B},[]),Qe=d.useCallback((o,D=0)=>{if(o==null||o==="")return D;if(typeof o=="number"&&!isNaN(o))return Math.round(o);const P=String(o).trim(),B=parseInt(P,10);return isNaN(B)?D:B},[]),Ze=d.useMemo(()=>m.allDailyProduction.map(o=>({...o,date:(()=>{try{const D=o.Date_Insert_Day||o.date;if(D){if(D.includes("/")){let B=T(D,"DD/MM/YYYY HH:mm:ss");if(B.isValid()||(B=T(D,"DD/MM/YYYY")),B.isValid())return B.format("YYYY-MM-DD")}const P=T(D);if(P.isValid())return P.format("YYYY-MM-DD")}return console.warn(`Invalid date found in table data: ${D}, using today's date`),T().format("YYYY-MM-DD")}catch(D){return console.error("Error parsing date for table:",D),T().format("YYYY-MM-DD")}})(),Machine_Name:o.Machine_Name||"N/A",Shift:o.Shift||"N/A",good:Qe(o.Good_QTY_Day),reject:Qe(o.Rejects_QTY_Day),oee:(()=>{const D=ne(o.OEE_Day);return D>0&&D<=1?D*100:D})(),speed:ne(o.Speed_Day,null),mould_number:o.Part_Number||"N/A",poid_unitaire:o.Poid_Unitaire||"N/A",cycle_theorique:o.Cycle_Theorique||"N/A",poid_purge:o.Poid_Purge||"N/A",availability:(()=>{const D=ne(o.Availability_Rate_Day);return D>0&&D<=1?D*100:D})(),performance:(()=>{const D=ne(o.Performance_Rate_Day);return D>0&&D<=1?D*100:D})(),quality:(()=>{const D=ne(o.Quality_Rate_Day);return D>0&&D<=1?D*100:D})(),run_hours:ne(o.Run_Hours_Day),down_hours:ne(o.Down_Hours_Day)})),[m.allDailyProduction,ne,Qe]),tt=d.useMemo(()=>{var o,D;return[{key:"1",label:e.createElement("span",null,e.createElement(kt,null),"Tendances"),children:e.createElement(be,{gutter:[24,24]},E?e.createElement(v,{span:24},e.createElement(me,null,e.createElement(it,{active:!0,paragraph:{rows:8}}))):e.createElement(Ia,{data:m.productionChart.data,colors:a([S.PRIMARY_BLUE,S.SECONDARY_BLUE,S.CHART_TERTIARY,S.SUCCESS_GREEN,S.WARNING_ORANGE]),dateRangeType:c,dateFilter:r,formatDateRange:At}))},{key:"2",label:e.createElement("span",null,e.createElement(pt,null),"Performance"),children:e.createElement(be,{gutter:[24,24]},E?e.createElement(v,{span:24},e.createElement(me,null,e.createElement(it,{active:!0,paragraph:{rows:8}}))):e.createElement(e.Fragment,null,e.createElement(v,{span:24},e.createElement(me,{title:e.createElement(J,null,e.createElement(pt,{style:{fontSize:20,color:l(S.SECONDARY_BLUE,1)}}),e.createElement(Et,{strong:!0},"Performance des Machines")),variant:"borderless",extra:e.createElement(Nt,{count:((o=m.machinePerformance.data)==null?void 0:o.length)||0,style:{backgroundColor:l(S.SECONDARY_BLUE,1)}})},e.createElement(be,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Production par Machine",data:m.machinePerformance.data,chartType:"bar",expandMode:"modal"},e.createElement(Bt,{data:m.machinePerformance.data,title:"Production par Machine",dataKey:"production",tooltipLabel:"Production",isKg:!0,color:l(S.CHART_TERTIARY,2)}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Rejets par Machine",data:m.machinePerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(jt,{data:m.machinePerformance.data,color:l(S.WARNING_ORANGE,4)})))))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{...ia("TRS par Machine",m.machinePerformance.data,"bar")},e.createElement(qt,{data:m.machinePerformance.data,color:l("#60A5FA",5)}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Répartition Production - Qualité",data:[{name:"Bonnes Pièces",value:Number(we)||0},{name:"Rejets",value:Number(Ee)||0}].filter(P=>P.value>0),chartType:"pie",expandMode:"modal",exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:"red"},"Qualité"),loading:E}},e.createElement(zt,{data:[{name:"Bonnes Pièces",value:Number(we)||0},{name:"Rejets",value:Number(Ee)||0}].filter(P=>P.value>0),colors:a([S.CHART_TERTIARY,S.WARNING_ORANGE]),height:((D=t==null?void 0:t.layout)==null?void 0:D.defaultHeight)||300}))),e.createElement(v,{xs:24,md:24},e.createElement(me,{title:e.createElement(J,null,e.createElement(pt,{style:{fontSize:20,color:l(S.CHART_QUATERNARY,3)}}),e.createElement(Et,{strong:!0},"Comparaison des Équipes")),variant:"borderless",extra:e.createElement(k,{color:"orange"},"Par équipe")},e.createElement(be,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Production par Équipe",data:m.shiftPerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(Ct,{data:m.shiftPerformance.data,title:"Production par Équipe",dataKey:"production",color:l(S.CHART_TERTIARY,2),label:"Production",tooltipLabel:"Production",isKg:!1}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Temps d'arrêt par Équipe",data:m.shiftPerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(Ct,{data:m.shiftPerformance.data,title:"Temps d'arrêt par Équipe",dataKey:"downtime",color:l(S.WARNING_ORANGE,4),label:"Temps d'arrêt (heures)",tooltipLabel:"Temps d'arrêt (heures)",isKg:!1}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"TRS par Équipe",data:m.shiftPerformance.data,chartType:"line",expandMode:"modal",exportEnabled:!0},e.createElement(Ft,{data:m.shiftPerformance.data,color:l(S.PRIMARY_BLUE,0)}))),e.createElement(v,{xs:24,md:12},e.createElement(ue,{title:"Performance par Équipe",data:m.shiftPerformance.data,chartType:"line",expandMode:"modal",exportEnabled:!0},e.createElement(Lt,{data:m.shiftPerformance.data,color:l("#60A5FA",5)}))))))))},{key:"3",label:e.createElement("span",null,e.createElement(ba,null),"Détails"),children:e.createElement(be,{gutter:[24,24]},e.createElement(v,{span:24},e.createElement(me,{title:e.createElement(J,null,e.createElement(Mt,{style:{fontSize:20,color:l(S.SECONDARY_BLUE,1)}}),e.createElement(Et,{strong:!0},"Données Journalières par Machine")),variant:"borderless",extra:e.createElement(J,null,e.createElement(Nt,{count:m.allDailyProduction.length,style:{backgroundColor:l(S.SECONDARY_BLUE,1)}}),e.createElement(Oe,{type:"link",icon:e.createElement(st,null),disabled:!0},"Exporter"))},e.createElement(Yt,{dataSource:m.allDailyProduction,columns:De,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:P=>`Total ${P} enregistrements`},scroll:{x:1800},rowKey:(P,B)=>`${P.Machine_Name}-${P.Date_Insert_Day}-${B}`}))),e.createElement(v,{span:24},e.createElement(Qt,{title:"Données Détaillées de Production",dataSource:Ze,columns:I,totalRecords:Ze.length,pageSize:50,currentPage:1,onExport:ee,maxRecordsWarning:500,loading:E,scroll:{x:2200},rowKey:(P,B)=>`${P.Date_Insert_Day}-${P.Machine_Name||"unknown"}-${P.Part_Number||"unknown"}-${B}`,expandable:{expandedRowRender:P=>e.createElement(me,{size:"small",title:"Informations du moule"},e.createElement(be,{gutter:[16,16]},e.createElement(v,{span:6},e.createElement(Ue,{title:"Numéro de Pièce",value:P.Part_Number||"N/A",valueStyle:{fontSize:16}})),e.createElement(v,{span:6},e.createElement(Ue,{title:"Poids Unitaire",value:P.Poid_Unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})),e.createElement(v,{span:6},e.createElement(Ue,{title:"Cycle Théorique",value:P.Cycle_Theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})),e.createElement(v,{span:6},e.createElement(Ue,{title:"Poids Purge",value:P.Poid_Purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})))),expandRowByClick:!0,rowExpandable:P=>P.Part_Number&&P.Part_Number!=="N/A"}})))}]},[m.allDailyProduction,m.machinePerformance.data,m.sidecards,c,r,At,Ze,I,ee,E,De]),at=E,Ve=E,ft=(((rt=m.productionChart.data)==null?void 0:rt.length)||0)>0||m.sidecards.goodqty>0,Ae=(((nt=m.productionChart.data)==null?void 0:nt.length)||0)>0||m.sidecards.goodqty>0;return e.createElement("div",{style:{padding:W.md?24:16}},e.createElement(Tt,{spinning:at,tip:"Chargement des données...",size:"large"},e.createElement(be,{gutter:[24,24]},e.createElement(v,{span:24},e.createElement(me,{variant:"borderless",styles:{body:{padding:W.md?24:16}}},e.createElement(be,{gutter:[24,24],align:"middle"},e.createElement(v,{xs:24,md:12},e.createElement(yt,{level:3,style:{marginBottom:8}},e.createElement(St,{style:{marginRight:12,color:l(S.PRIMARY_BLUE,0)}}),"Tableau de Bord de Production")),e.createElement(v,{xs:24,md:12,style:{textAlign:W.md?"right":"left"}},e.createElement(J,{direction:"vertical",style:{width:"100%"}},e.createElement(na,{selectedMachineModel:u,selectedMachine:i,machineModels:C,filteredMachineNames:R,dateRangeType:c,dateFilter:r,dateFilterActive:Z,handleMachineModelChange:x,handleMachineChange:f,handleDateRangeTypeChange:s,handleDateChange:de,resetFilters:_,handleRefresh:y,loading:E,dataSize:X,pageType:"production",onSearchResults:U,enableElasticsearch:!0}),X>500&&e.createElement(k,{color:"blue",icon:e.createElement(_t,null)},X," enregistrements"),(u||Z)&&e.createElement(J,{wrap:!0,style:{marginTop:8}},u&&e.createElement(k,{color:"blue",closable:!0,onClose:()=>x("")},"Modèle: ",u),i&&e.createElement(k,{color:"green",closable:!0,onClose:()=>f("")},"Machine: ",i),Z&&e.createElement(k,{color:"purple",closable:!0,onClose:()=>p(null)},"Période: ",h)),!(u||Z)&&Ae&&e.createElement(J,{wrap:!0,style:{marginTop:8}},e.createElement(k,{color:"green",icon:e.createElement(_t,null)},"Powered by GraphQL"),e.createElement(k,{color:$.primary==="elasticsearch"?"blue":"orange"},"Source: ",$.primary==="elasticsearch"?"Elasticsearch":"MySQL"))))))),q.slice(0,4).map(o=>e.createElement(v,{key:o.title,xs:24,sm:12,md:6},e.createElement(me,{hoverable:!0,loading:Ve,style:{backgroundColor:"#FFFFFF",border:`1px solid ${S.PRIMARY_BLUE}`,borderTop:`3px solid ${S.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},Ve?e.createElement(it,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ue,{title:e.createElement(he,{title:o.description},e.createElement(J,null,e.cloneElement(o.icon,{style:{color:S.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:S.DARK_GRAY,fontWeight:600}},o.title),e.createElement(Rt,{style:{color:S.LIGHT_GRAY,fontSize:14}}))),value:o.rawValue||o.value,precision:o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité")?1:0,suffix:o.suffix,valueStyle:{fontSize:24,color:S.PRIMARY_BLUE,fontWeight:700},formatter:D=>o.suffix==="%"?D.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):o.suffix==="Pcs"||o.suffix==="Kg"?D.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):D.toLocaleString("fr-FR")}),(o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité"))&&e.createElement(_e,{percent:o.rawValue||o.value,strokeColor:S.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),q.slice(4).map(o=>e.createElement(v,{key:o.title,xs:24,sm:12,md:6},e.createElement(me,{hoverable:!0,loading:Ve,style:{backgroundColor:"#FFFFFF",border:`1px solid ${S.PRIMARY_BLUE}`,borderTop:`3px solid ${S.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},Ve?e.createElement(it,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ue,{title:e.createElement(he,{title:o.description},e.createElement(J,null,e.cloneElement(o.icon,{style:{color:S.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:S.DARK_GRAY,fontWeight:600}},o.title),e.createElement(Rt,{style:{color:S.LIGHT_GRAY,fontSize:14}}))),value:o.rawValue||o.value,precision:o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité")?1:0,suffix:o.suffix,valueStyle:{fontSize:24,color:S.PRIMARY_BLUE,fontWeight:700},formatter:D=>o.suffix==="%"?D.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):o.suffix==="Pcs"||o.suffix==="Kg"?D.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):D.toLocaleString("fr-FR")}),(o.title.includes("TRS")||o.title.includes("Taux")||o.title.includes("Disponibilité")||o.title.includes("Performance")||o.title.includes("Qualité"))&&e.createElement(_e,{percent:o.rawValue||o.value,strokeColor:S.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),at?e.createElement(v,{span:24},e.createElement(me,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(Tt,{size:"large",style:{marginBottom:24}}),e.createElement(yt,{level:3},"Chargement des données..."),e.createElement(Dt,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},u?`Chargement des données pour ${u}...`:"Chargement des données de production pour tous les modèles de machines...")))):ft?e.createElement(e.Fragment,null,e.createElement(v,{span:24},e.createElement(me,{variant:"borderless"},e.createElement(Wt,{defaultActiveKey:"1",onChange:Q,items:tt,tabBarExtraContent:e.createElement(J,null,e.createElement(Oe,{type:"link",icon:e.createElement(Sa,null),onClick:()=>z(!0)},"Recherche globale"),e.createElement(Oe,{type:"link",icon:e.createElement(st,null),disabled:!0},"Exporter"),i&&e.createElement(Xt,{machineId:i,machineName:i,shift:Y()}))})))):e.createElement(v,{span:24},e.createElement(me,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(St,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),e.createElement(yt,{level:3},"Aucune donnée disponible"),e.createElement(Dt,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},u||r?"Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.":"Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."),(u||r)&&e.createElement(Dt,{style:{fontSize:14,color:"#999",textAlign:"center",marginTop:16}},"Filtres actifs:",u&&` Modèle: ${u}`,i&&` Machine: ${i}`,r&&` Période: ${va(r)}`)))))),L&&Ce&&e.createElement("div",{style:{marginTop:24}},e.createElement(oa,{results:Ce,searchQuery:ce,pageType:"production",loading:E,onResultSelect:o=>{},onPageChange:o=>{}})),e.createElement(ea,{visible:F,onClose:()=>z(!1),onResultSelect:Ie}))},La=d.memo(Fa),Cr=d.memo(()=>e.createElement(Ma,null,e.createElement(La,null)));export{Cr as default};
