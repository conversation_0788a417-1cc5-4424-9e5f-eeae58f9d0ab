#!/usr/bin/env pwsh

# Test GraphQL endpoints for debugging

Write-Host "🔍 Testing GraphQL endpoints..." -ForegroundColor Cyan

# Test 1: Basic GraphQL health check
Write-Host "`n1. Testing basic GraphQL endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/graphql" -Method POST -ContentType "application/json" -Body '{"query":"query { __schema { types { name } } }"}'
    Write-Host "✅ GraphQL endpoint is accessible" -ForegroundColor Green
    Write-Host "Available types: $($response.data.__schema.types.Count)" -ForegroundColor Green
} catch {
    Write-Host "❌ GraphQL endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Stop sidecards query (used by ArretsDashboard)
Write-Host "`n2. Testing Stop sidecards query..." -ForegroundColor Yellow
try {
    $stopQuery = @{
        query = "query { getStopSidecards { Arret_Totale Arret_Totale_nondeclare } }"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/graphql" -Method POST -ContentType "application/json" -Body $stopQuery
    Write-Host "✅ Stop sidecards query successful" -ForegroundColor Green
    Write-Host "Data: $($response.data | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Stop sidecards query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

# Test 3: Production dashboard query (used by ProductionDashboard)
Write-Host "`n3. Testing Production dashboard query..." -ForegroundColor Yellow
try {
    $prodQuery = @{
        query = "query { getDashboardData { productionChart { Date_Insert_Day Total_Good_Qty_Day } sidecards { goodqty rejetqty } } }"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/graphql" -Method POST -ContentType "application/json" -Body $prodQuery
    Write-Host "✅ Production dashboard query successful" -ForegroundColor Green
    Write-Host "Data: $($response.data | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Production dashboard query failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

# Test 4: Compare with working REST API endpoints (used by Arrets2)
Write-Host "`n4. Testing working REST API endpoints..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/sidecards-arret" -Method GET
    Write-Host "✅ REST API sidecards-arret successful" -ForegroundColor Green
    Write-Host "Data: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ REST API sidecards-arret failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🔍 GraphQL endpoint testing completed!" -ForegroundColor Cyan
