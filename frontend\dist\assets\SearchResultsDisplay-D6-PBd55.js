import{r as p,av as Pe,al as j,a1 as _e,Y as $e,aw as Ce,q as Ne,ax as Oe,a2 as Be,R as e,b as V,c as d,d as _,B as be,ay as Te,g as S,S as l,f as E,e as G,D as ne,A as Ie,aj as De,C as Ue,E as ge,au as ze,T as Ge}from"./index-N0wOiMt6.js";import{i as f}from"./PieChart-BZME-zsX.js";import{d as Q}from"./dayjs.min-BHt7dFLo.js";import"./fr-DC3HkAP8.js";import{s as H,i as je,a as Me,R as qe,b as Fe}from"./GlobalSearchModal-BvgW-kQU.js";import{R as le}from"./SearchOutlined-xbMlVLbw.js";import{R as He}from"./ExperimentOutlined-J9m82uMz.js";import{S as We}from"./index-Dea_-S4D.js";import{R as ye}from"./ThunderboltOutlined-Dz7LyqJg.js";import{R as ee}from"./FilterOutlined-DjPdBmQR.js";import{S as Ve}from"./index-BCtPda2K.js";import{R as te}from"./CalendarOutlined-BxlyaoqS.js";import{R as z}from"./ClockCircleOutlined-C6SZLNSK.js";import{b as Qe,D as re}from"./DownloadOutlined-C8TU0wLq.js";import{R as Je}from"./ReloadOutlined-DZn6IdM2.js";import{L as oe}from"./index-C2CgWKoY.js";import{R as Ke}from"./EyeOutlined-BNZGoZWA.js";import{R as Xe}from"./FileTextOutlined-BAhSEapg.js";import{R as Ze}from"./BarChartOutlined-DzqCoGDG.js";import{S as W}from"./index-BP6n0Cjb.js";const{Option:Re}=j;function he(s){return(s==null?void 0:s.type)&&(s.type.isSelectOption||s.type.isSelectOptGroup)}const et=(s,R)=>{var c,m;const{prefixCls:a,className:C,popupClassName:b,dropdownClassName:O,children:B,dataSource:$,dropdownStyle:T,dropdownRender:D,popupRender:U,onDropdownVisibleChange:i,onOpenChange:h,styles:x,classNames:g}=s,I=Pe(B),n=((c=x==null?void 0:x.popup)===null||c===void 0?void 0:c.root)||T,r=((m=g==null?void 0:g.popup)===null||m===void 0?void 0:m.root)||b||O,y=U||D,Y=h||i;let k;I.length===1&&p.isValidElement(I[0])&&!he(I[0])&&([k]=I);const L=k?()=>k:void 0;let w;I.length&&he(I[0])?w=B:w=$?$.map(v=>{if(p.isValidElement(v))return v;switch(typeof v){case"string":return p.createElement(Re,{key:v,value:v},v);case"object":{const{value:q}=v;return p.createElement(Re,{key:q,value:q},v.text)}default:return}}):[];const{getPrefixCls:N}=p.useContext(_e),A=N("select",a),[M]=$e("SelectLike",n==null?void 0:n.zIndex);return p.createElement(j,Object.assign({ref:R,suffixIcon:null},Ce(s,["dataSource","dropdownClassName","popupClassName"]),{prefixCls:A,classNames:{popup:{root:r},root:g==null?void 0:g.root},styles:{popup:{root:Object.assign(Object.assign({},n),{zIndex:M})},root:x==null?void 0:x.root},className:Ne(`${A}-auto-complete`,C),mode:j.SECRET_COMBOBOX_MODE_DO_NOT_USE,popupRender:y,onOpenChange:Y,getInputElement:L}),w)},Le=p.forwardRef(et),{Option:tt}=j,rt=Oe(Le,"dropdownAlign",s=>Ce(s,["visible"])),se=Le;se.Option=tt;se._InternalPanelDoNotUseOrYouWillBeFired=rt;var ot={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};function ae(){return ae=Object.assign?Object.assign.bind():function(s){for(var R=1;R<arguments.length;R++){var c=arguments[R];for(var m in c)Object.prototype.hasOwnProperty.call(c,m)&&(s[m]=c[m])}return s},ae.apply(this,arguments)}const nt=(s,R)=>p.createElement(Be,ae({},s,{ref:R,icon:ot})),lt=p.forwardRef(nt);Q.extend(je);Q.locale("fr");const{Option:Se}=j,{Search:at}=Te,st=({machineModels:s=[],filteredMachineNames:R=[],selectedMachineModel:c="",selectedMachine:m="",dateFilter:a=null,dateRangeType:C="day",dateFilterActive:b=!1,handleMachineModelChange:O,handleMachineChange:B,handleDateChange:$,handleDateRangeTypeChange:T,resetFilters:D,handleRefresh:U,loading:i=!1,dataSize:h=0,estimatedLoadTime:x=0,pageType:g="production",onSearchResults:I,enableElasticsearch:n=!0,error:r=null})=>{var fe,Ee;console.log("🔍 [FILTERPANEL] Enhanced FilterPanel props:",{machineModels:(s==null?void 0:s.length)||0,filteredMachineNames:(R==null?void 0:R.length)||0,selectedMachineModel:c,selectedMachine:m,dateFilter:a?typeof a=="object"?(fe=a.format)==null?void 0:fe.call(a,"YYYY-MM-DD"):a:null,dateRangeType:C,dateFilterActive:b,loading:i,dataSize:h,pageType:g,enableElasticsearch:n,hasHandlers:{machineModelChange:typeof O=="function",machineChange:typeof B=="function",dateChange:typeof $=="function",dateRangeTypeChange:typeof T=="function",resetFilters:typeof D=="function",handleRefresh:typeof U=="function"}});const[y,Y]=p.useState(""),[k,L]=p.useState([]),[w,N]=p.useState(!1),[A,M]=p.useState(null),[v,q]=p.useState(!1),[Z,ce]=p.useState(!1),[ie,me]=p.useState({elasticsearch:!1,mysql:!0,primary:"mysql"}),[J,ve]=p.useState({hasAllFilters:!1,isComplexScenario:!1,activeFiltersCount:0});p.useEffect(()=>{n&&xe()},[n]),p.useEffect(()=>{var P;const t=c&&m&&b,o=[c&&"model",m&&"machine",b&&"date"].filter(Boolean).length;ve({hasAllFilters:t,isComplexScenario:t,activeFiltersCount:o}),t&&console.log("🎯 [FILTERPANEL] Complex filter scenario detected:",{model:c,machine:m,date:a?typeof a=="object"?(P=a.format)==null?void 0:P.call(a,"YYYY-MM-DD"):a:null,dataSize:h})},[c,m,b,a,h]);const xe=async()=>{try{const t=await H.checkHealth(),o=t.elasticsearch.status==="healthy";q(o),me(u=>({...u,elasticsearch:o,primary:o?"elasticsearch":"mysql"})),console.log("🔍 [FILTERPANEL] Elasticsearch health check:",{status:t.elasticsearch.status,enabled:o,primary:o?"elasticsearch":"mysql"})}catch(t){console.warn("🔍 [FILTERPANEL] Elasticsearch not available:",t),q(!1),me(o=>({...o,elasticsearch:!1,primary:"mysql"}))}},Ye=p.useCallback(H.createDebouncedSearch(async t=>{if(!t||t.length<2){L([]);return}try{const o=g==="production"?"machineName":"stopDescription",P=(await H.getSuggestions(t,o,8)).map(de=>({value:de,label:de}));L(P)}catch(o){console.error("🔍 [FILTERPANEL] Error getting suggestions:",o),L([])}},300),[g]),ke=t=>{Y(t),v&&t?Ye(t):L([])},ue=async t=>{if(!(!t.trim()||!v)){N(!0),console.log("🔍 [FILTERPANEL] Performing Elasticsearch search:",{query:t,pageType:g});try{const o={query:t.trim(),page:1,size:50};a&&(typeof a=="object"&&a.format?(o.dateFrom=a.format("YYYY-MM-DD"),o.dateTo=a.format("YYYY-MM-DD")):a instanceof Date?(o.dateFrom=a.toISOString().split("T")[0],o.dateTo=a.toISOString().split("T")[0]):typeof a=="string"&&(o.dateFrom=a,o.dateTo=a)),m&&(o.machineId=m),c&&(o.machineModel=c);let u;g==="production"?u=await H.searchProductionData(o):g==="arrets"&&(u=await H.searchMachineStops(o)),M(u),ce(!0),console.log("🔍 [FILTERPANEL] Search completed:",{total:(u==null?void 0:u.total)||0,searchMethod:(u==null?void 0:u.searchMethod)||"unknown",dataSource:(u==null?void 0:u.dataSource)||"unknown"}),I&&I(u,t)}catch(o){console.error("🔍 [FILTERPANEL] Search error:",o),M(null),o.message&&console.warn("Search failed:",o.message)}finally{N(!1)}}},pe=()=>{Y(""),M(null),ce(!1),L([]),console.log("🔍 [FILTERPANEL] Search mode cleared, returning to filter mode"),I&&I(null,"")},F=t=>{console.log("🎯 [FILTERPANEL] Quick filter applied:",t);const o=Q();let u,P;switch(t){case"today":u=o,P="day";break;case"week":u=o.startOf("isoWeek"),P="week";break;case"month":u=o.startOf("month"),P="month";break;case"last7days":u=o.subtract(7,"days"),P="day";break;case"last30days":u=o.subtract(30,"days"),P="day";break;default:console.warn("🎯 [FILTERPANEL] Unknown quick filter type:",t);return}T&&T(P),$&&$(u)},we=()=>h>1e3?{type:"warning",message:`⚠️ Volume important: ${h.toLocaleString("fr-FR")} enregistrements${x?` (temps estimé: ${x}s)`:""}`,showIcon:!0}:h>500?{type:"info",message:`📊 ${h.toLocaleString("fr-FR")} enregistrements à afficher`,showIcon:!0}:J.isComplexScenario&&h>100?{type:"info",message:`🎯 Filtrage complexe appliqué: ${h.toLocaleString("fr-FR")} résultats`,showIcon:!0}:null,Ae=()=>{const t={value:a,onChange:$,allowClear:!0,style:{width:"100%"},placeholder:`Sélectionner ${C==="day"?"une date":C==="week"?"une semaine":"un mois"}`,disabled:i};return C==="day"?e.createElement(re,{...t,format:"DD/MM/YYYY"}):C==="week"?e.createElement(re,{...t,picker:"week",format:"[Semaine] w YYYY"}):C==="month"?e.createElement(re,{...t,picker:"month",format:"MMMM YYYY"}):null},K=we();return e.createElement("div",{style:{width:"100%"}},e.createElement(V,{gutter:[16,16]},v&&e.createElement(d,{span:24},e.createElement(_,{wrap:!0,style:{width:"100%"},size:"middle"},e.createElement(be,{dot:Z,color:"green",offset:[0,0]},e.createElement(se,{style:{width:320},options:k,onSearch:ke,onSelect:t=>{Y(t),ue(t)},value:y,placeholder:`Rechercher ${g==="production"?"dans les données de production":"dans les arrêts"}...`,disabled:i},e.createElement(at,{loading:w,onSearch:ue,disabled:i,enterButton:e.createElement(S,{type:"primary",icon:e.createElement(le,null),disabled:i,style:{backgroundColor:l.PRIMARY_BLUE,borderColor:l.PRIMARY_BLUE}},"Rechercher")}))),Z&&e.createElement(_,null,e.createElement(E,{color:"green",icon:e.createElement(He,null),style:{backgroundColor:"#f6ffed",borderColor:l.SUCCESS_GREEN,color:l.SUCCESS_GREEN}},"Mode recherche actif"),e.createElement(S,{size:"small",onClick:pe,style:{color:l.PRIMARY_BLUE,borderColor:l.PRIMARY_BLUE}},"Retour aux filtres"),A&&e.createElement(E,{color:"blue",style:{backgroundColor:"#e6f7ff",borderColor:l.PRIMARY_BLUE,color:l.PRIMARY_BLUE}},((Ee=A.total)==null?void 0:Ee.toLocaleString("fr-FR"))||0," résultat(s)")),e.createElement(G,{title:"Basculer entre recherche Elasticsearch et filtres SQL"},e.createElement(We,{checkedChildren:"ES",unCheckedChildren:"SQL",checked:Z,onChange:t=>{t||pe()},disabled:i})),e.createElement(E,{color:ie.primary==="elasticsearch"?"blue":"orange",icon:e.createElement(ye,null)},"Source: ",ie.primary==="elasticsearch"?"Elasticsearch":"MySQL")),e.createElement(ne,{style:{margin:"12px 0"}})),e.createElement(d,{span:24},e.createElement(V,{gutter:[16,16],align:"middle"},e.createElement(d,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:l.LIGHT_GRAY,fontWeight:500}},"Modèle de Machine"),e.createElement(G,{title:"Sélectionner un modèle de machine pour filtrer les données"},e.createElement(j,{placeholder:"Tous les modèles",style:{width:"100%"},value:c||void 0,onChange:t=>{console.log("🎯 [FILTERPANEL] Machine Model changed:",t),O?O(t):console.error("🎯 [FILTERPANEL] handleMachineModelChange not provided!")},allowClear:!0,showSearch:!0,disabled:i,loading:i&&!s.length,suffixIcon:e.createElement(ee,{style:{color:l.PRIMARY_BLUE}}),filterOption:(t,o)=>o.children.toLowerCase().includes(t.toLowerCase())},(s||[]).map(t=>{const o=typeof t=="object"?t.model||t.value:t;return e.createElement(Se,{key:o,value:o},o)}))))),e.createElement(d,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:l.LIGHT_GRAY,fontWeight:500}},"Machine Spécifique"),e.createElement(j,{placeholder:"Sélectionner une machine",style:{width:"100%"},value:m||void 0,onChange:t=>{console.log("🏭 [FILTERPANEL] Machine changed:",t),B?B(t):console.error("🏭 [FILTERPANEL] handleMachineChange not provided!")},disabled:!c||R.length===0||i,allowClear:!0,showSearch:!0,loading:i&&c,filterOption:(t,o)=>o.children.toLowerCase().includes(t.toLowerCase())},(R||[]).map(t=>{const o=typeof t=="object"?t.Machine_Name||t.name||t.value:t;return e.createElement(Se,{key:o||`machine-${Math.random()}`,value:o},o||"Machine inconnue")})))),e.createElement(d,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:l.LIGHT_GRAY,fontWeight:500}},"Type de Période"),e.createElement(Ve,{options:[{label:"Jour",value:"day",icon:e.createElement(te,null)},{label:"Semaine",value:"week",icon:e.createElement(te,null)},{label:"Mois",value:"month",icon:e.createElement(te,null)}],value:C,onChange:T,disabled:i,style:{width:"100%"}}))),e.createElement(d,{xs:24,sm:12,lg:6},e.createElement("div",null,e.createElement("div",{style:{marginBottom:4,fontSize:"12px",color:l.LIGHT_GRAY,fontWeight:500}},"Sélection de Date"),Ae())))),e.createElement(d,{span:24},e.createElement(V,{gutter:[16,16],align:"middle",justify:"space-between"},e.createElement(d,null,e.createElement(_,{wrap:!0},(c||m||b)&&e.createElement(_,{wrap:!0},c&&e.createElement(E,{color:"blue",closable:!0,onClose:()=>O&&O(""),style:{backgroundColor:"#e6f7ff",borderColor:l.PRIMARY_BLUE,color:l.PRIMARY_BLUE}},"Modèle: ",c),m&&e.createElement(E,{color:"green",closable:!0,onClose:()=>B&&B(""),style:{backgroundColor:"#f6ffed",borderColor:l.SUCCESS_GREEN,color:l.SUCCESS_GREEN}},"Machine: ",m),b&&a&&e.createElement(E,{color:"orange",closable:!0,onClose:()=>$&&$(null),icon:e.createElement(z,null),style:{backgroundColor:"#fff7e6",borderColor:l.WARNING_ORANGE,color:l.WARNING_ORANGE}},a&&typeof a=="object"&&a.format?a.format(C==="day"?"DD/MM/YYYY":C==="week"?"[Semaine] w YYYY":"MMMM YYYY"):"Date sélectionnée")),e.createElement(E,{icon:e.createElement(ee,null),color:"processing",style:{backgroundColor:"#f0f9ff",borderColor:l.SECONDARY_BLUE,color:l.SECONDARY_BLUE}},h.toLocaleString("fr-FR")," enregistrement(s)",c&&!m&&` (modèle: ${c})`,m&&` (machine: ${m})`),J.activeFiltersCount>0&&e.createElement(E,{color:"blue",style:{backgroundColor:"#e6f7ff",borderColor:l.PRIMARY_BLUE,color:l.PRIMARY_BLUE}},J.activeFiltersCount," filtre(s) actif(s)"),i&&c&&!m&&e.createElement(E,{color:"processing"},e.createElement(z,{spin:!0})," Filtrage par modèle..."),i&&m&&e.createElement(E,{color:"processing"},e.createElement(z,{spin:!0})," Filtrage par machine..."),i&&b&&e.createElement(E,{color:"orange"},e.createElement(z,{spin:!0})," Filtrage par date..."),J.isComplexScenario&&i&&e.createElement(E,{color:"gold"},e.createElement(z,{spin:!0})," Traitement complexe..."))),e.createElement(d,null,e.createElement(_,null,e.createElement(G,{title:"Effacer tous les filtres"},e.createElement(S,{icon:e.createElement(Qe,null),onClick:D,disabled:!c&&!m&&!b||i},"Effacer")),e.createElement(G,{title:"Actualiser les données"},e.createElement(S,{type:"primary",icon:e.createElement(Je,null),onClick:U,loading:i,style:{backgroundColor:l.PRIMARY_BLUE,borderColor:l.PRIMARY_BLUE}},i?"Chargement...":"Actualiser")))))),e.createElement(d,{span:24},e.createElement(_,{split:e.createElement(ne,{type:"vertical"}),wrap:!0},e.createElement(_,null,e.createElement(E,{icon:e.createElement(ye,null),color:"blue",style:{backgroundColor:"#e6f7ff",borderColor:l.PRIMARY_BLUE,color:l.PRIMARY_BLUE}},"Filtres rapides:"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("today"),disabled:i,style:{color:l.PRIMARY_BLUE}},"Aujourd'hui"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("week"),disabled:i,style:{color:l.PRIMARY_BLUE}},"Cette semaine"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("month"),disabled:i,style:{color:l.PRIMARY_BLUE}},"Ce mois"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("last7days"),disabled:i,style:{color:l.PRIMARY_BLUE}},"7 derniers jours"),e.createElement(S,{size:"small",type:"link",onClick:()=>F("last30days"),disabled:i,style:{color:l.PRIMARY_BLUE}},"30 derniers jours")),!c&&!i&&e.createElement(E,{icon:e.createElement(ee,null),color:"blue",style:{backgroundColor:"#f0f9ff",borderColor:l.SECONDARY_BLUE,color:l.SECONDARY_BLUE}},"Affichage de tous les modèles"),!b&&!i&&e.createElement(E,{icon:e.createElement(z,null),color:"green",style:{backgroundColor:"#f6ffed",borderColor:l.SUCCESS_GREEN,color:l.SUCCESS_GREEN}},"Filtre par défaut: 7 derniers jours"),e.createElement(E,{color:"success",style:{marginLeft:"auto",backgroundColor:"#f6ffed",borderColor:l.SUCCESS_GREEN,color:l.SUCCESS_GREEN}},"✓ Actualisation automatique des filtres"))),K&&e.createElement(d,{span:24},e.createElement(Ie,{message:K.message,type:K.type,showIcon:K.showIcon,closable:!0,style:{marginBottom:0}})),r&&e.createElement(d,{span:24},e.createElement("div",{style:{padding:"12px",backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${l.PRIMARY_BLUE}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},e.createElement("div",{style:{display:"flex",alignItems:"center"}},e.createElement(De,{style:{color:"#ff4d4f",fontSize:"16px",marginRight:"8px"}}),e.createElement("div",null,e.createElement("div",{style:{fontWeight:"bold",color:l.DARK_GRAY}},"Erreur de chargement des données"),e.createElement("div",{style:{fontSize:"12px",marginTop:"4px",color:l.LIGHT_GRAY}},r.includes&&r.includes("AbortError")?"La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page.":typeof r=="string"?r:"Une erreur est survenue lors du chargement des données. Veuillez réessayer."),e.createElement(_,{style:{marginTop:"8px"}},e.createElement(S,{size:"small",type:"primary",onClick:U,style:{backgroundColor:l.PRIMARY_BLUE,borderColor:l.PRIMARY_BLUE}},"Réessayer"),e.createElement(S,{size:"small",onClick:D},"Réinitialiser les filtres"))))))))};st.propTypes={machineModels:f.array.isRequired,filteredMachineNames:f.array.isRequired,selectedMachineModel:f.string,selectedMachine:f.string,dateFilter:f.object,dateRangeType:f.oneOf(["day","week","month"]).isRequired,dateFilterActive:f.bool.isRequired,handleMachineModelChange:f.func.isRequired,handleMachineChange:f.func.isRequired,handleDateChange:f.func.isRequired,handleDateRangeTypeChange:f.func.isRequired,resetFilters:f.func.isRequired,handleRefresh:f.func.isRequired,loading:f.bool,dataSize:f.number,estimatedLoadTime:f.number,error:f.string,pageType:f.oneOf(["production","arrets"]),onSearchResults:f.func,enableElasticsearch:f.bool};const{Text:X,Title:wt,Paragraph:ct}=Ge,At=({results:s,searchQuery:R,pageType:c,loading:m=!1,onResultSelect:a,onPageChange:C,currentPage:b=1,pageSize:O=20})=>{const[B,$]=p.useState([]);if(!s)return null;const T=n=>{switch(n){case"production-data":return e.createElement(Ze,{style:{color:"#52c41a"}});case"machine-stop":return e.createElement(qe,{style:{color:"#ff4d4f"}});case"machine-session":return e.createElement(lt,{style:{color:"#1890ff"}});case"report":return e.createElement(Xe,{style:{color:"#722ed1"}});default:return e.createElement(le,{style:{color:"#666"}})}},D=n=>{const y={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"}}[n]||{color:"default",text:"Inconnu"};return e.createElement(E,{color:y.color},y.text)},U=n=>{const{data:r,type:y}=n;switch(y){case"production-data":return`${r.machineName} - ${Q(r.date).format("DD/MM/YYYY")}`;case"machine-stop":return`Arrêt ${r.machineName} - ${r.stopCode}`;case"machine-session":return`Session ${r.machineName} - ${r.sessionId}`;case"report":return r.title||`Rapport ${r.type}`;default:return"Résultat de recherche"}},i=n=>{var Y,k,L,w,N,A;const{data:r,type:y}=n;switch(y){case"production-data":return`OEE: ${((k=(Y=r.performance)==null?void 0:Y.oee)==null?void 0:k.toFixed(1))||0}% | Production: ${((L=r.production)==null?void 0:L.good)||0} pièces | Opérateur: ${r.operator||"N/A"}`;case"machine-stop":return`${r.stopDescription} | Durée: ${r.duration||0} min | Catégorie: ${r.stopCategory||"N/A"}`;case"machine-session":return`TRS: ${((N=(w=r.performance)==null?void 0:w.trs)==null?void 0:N.toFixed(1))||0}% | Production: ${((A=r.production)==null?void 0:A.total)||0} | Opérateur: ${r.operator||"N/A"}`;case"report":return r.description||`Généré par ${r.generatedBy||"N/A"}`;default:return"Aucune description disponible"}},h=n=>{if(!n)return null;const r=Object.keys(n);return r.length===0?null:e.createElement("div",{style:{marginTop:8,padding:"8px",backgroundColor:"#f6ffed",borderRadius:"4px"}},e.createElement(X,{type:"secondary",style:{fontSize:"12px"}},e.createElement(Fe,null)," Correspondances trouvées:"),r.map(y=>e.createElement("div",{key:y,style:{marginTop:4}},e.createElement(X,{strong:!0,style:{fontSize:"12px",color:"#52c41a"}},y,":"),e.createElement("div",{style:{fontSize:"12px",marginLeft:8},dangerouslySetInnerHTML:{__html:n[y].join(" ... ")}}))))},x=n=>{var Y,k,L,w,N,A;const{data:r,type:y}=n;return y==="production-data"?e.createElement(V,{gutter:16,style:{marginTop:8}},e.createElement(d,{span:6},e.createElement(W,{title:"OEE",value:((Y=r.performance)==null?void 0:Y.oee)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(d,{span:6},e.createElement(W,{title:"Production",value:((k=r.production)==null?void 0:k.good)||0,suffix:"pcs",valueStyle:{fontSize:"14px"}})),e.createElement(d,{span:6},e.createElement(W,{title:"Qualité",value:((L=r.performance)==null?void 0:L.quality)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})),e.createElement(d,{span:6},e.createElement(W,{title:"TRS",value:((w=r.performance)==null?void 0:w.trs)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}}))):y==="machine-stop"?e.createElement(V,{gutter:16,style:{marginTop:8}},e.createElement(d,{span:8},e.createElement(W,{title:"Durée",value:r.duration||0,suffix:"min",valueStyle:{fontSize:"14px"}})),e.createElement(d,{span:8},e.createElement(E,{color:r.severity==="high"?"red":r.severity==="medium"?"orange":"green"},r.severity||"low")),e.createElement(d,{span:8},e.createElement(be,{status:(N=r.resolution)!=null&&N.resolved?"success":"error",text:(A=r.resolution)!=null&&A.resolved?"Résolu":"En cours"}))):null},g=n=>{a&&a(n)},I=n=>e.createElement(oe.Item,{key:n.id,style:{padding:"16px",borderRadius:"8px",margin:"8px 0",border:"1px solid #f0f0f0",backgroundColor:"#fafafa",transition:"all 0.2s"},onMouseEnter:r=>{r.currentTarget.style.backgroundColor="#f5f5f5",r.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:r=>{r.currentTarget.style.backgroundColor="#fafafa",r.currentTarget.style.borderColor="#f0f0f0"},actions:[e.createElement(G,{title:"Voir les détails"},e.createElement(S,{type:"text",icon:e.createElement(Ke,null),onClick:()=>g(n)})),e.createElement(G,{title:"Exporter"},e.createElement(S,{type:"text",icon:e.createElement(Me,null),onClick:()=>g(n)}))]},e.createElement(oe.Item.Meta,{avatar:T(n.type),title:e.createElement(_,null,e.createElement(X,{strong:!0,style:{cursor:"pointer"},onClick:()=>g(n)},U(n)),D(n.type),n.score&&e.createElement(G,{title:`Score de pertinence: ${n.score.toFixed(3)}`},e.createElement(E,{color:"purple",style:{fontSize:"10px"}},Math.round(n.score*100),"%"))),description:e.createElement("div",null,e.createElement(ct,{style:{marginBottom:8}},i(n)),n.data.timestamp&&e.createElement("div",{style:{marginBottom:8}},e.createElement(z,{style:{marginRight:4}}),e.createElement(X,{type:"secondary",style:{fontSize:"12px"}},Q(n.data.timestamp||n.data.date).format("DD/MM/YYYY HH:mm"))),x(n),h(n.highlight))}));return e.createElement(Ue,{title:e.createElement(_,null,e.createElement(le,null),e.createElement("span",null,'Résultats de recherche pour "',R,'"'),e.createElement(E,{color:"blue"},s.total," résultat(s)")),extra:e.createElement(_,null,e.createElement(S,{size:"small",type:"link"},"Exporter tous"))},s.total===0?e.createElement(ge,{description:`Aucun résultat trouvé pour "${R}"`,image:ge.PRESENTED_IMAGE_SIMPLE}):e.createElement(e.Fragment,null,e.createElement(Ie,{message:`${s.total} résultat(s) trouvé(s) dans les ${c==="production"?"données de production":"arrêts de machine"}`,type:"info",showIcon:!0,style:{marginBottom:16}}),e.createElement(oe,{dataSource:s[c==="production"?"production":"stops"]||s.results||[],renderItem:I,loading:m,split:!1}),s.totalPages>1&&e.createElement(e.Fragment,null,e.createElement(ne,null),e.createElement("div",{style:{textAlign:"center"}},e.createElement(ze,{current:b,total:s.total,pageSize:O,onChange:C,showSizeChanger:!0,showQuickJumper:!0,showTotal:(n,r)=>`${r[0]}-${r[1]} sur ${n} résultats`})))))};export{st as F,At as S};
