# Simple test for shift performance
$headers = @{ 'Content-Type' = 'application/json' }
$body = '{"query":"{ enhancedGetShiftPerformance(filters: { model: \"IPS\" }) { data { Shift production } dataSource } }"}'

Write-Host "Testing shift performance query..."

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/graphql" -Method POST -Headers $headers -Body $body
    
    if ($response.errors) {
        Write-Host "GraphQL Errors:"
        $response.errors | ConvertTo-Json
    } else {
        $data = $response.data.enhancedGetShiftPerformance
        Write-Host "Data source: $($data.dataSource)"
        Write-Host "Total shifts: $($data.data.Count)"
        
        Write-Host "Shift results:"
        foreach ($shift in $data.data) {
            Write-Host "  - $($shift.Shift): $($shift.production) pieces"
        }
        
        $uniqueShifts = $data.data | Select-Object -ExpandProperty Shift | Sort-Object -Unique
        Write-Host "Unique shifts: $($uniqueShifts -join ', ')"
        
        if ($uniqueShifts -notcontains "IPS01") {
            Write-Host "SUCCESS: Data shows shifts, not machines"
        } else {
            Write-Host "WARNING: Data might still show machine names"
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}
