/**
 * Enhanced Loading Component with Progressive States
 * Provides beautiful loading states for different scenarios
 */

import React from 'react';
import { Card, Skeleton, Spin, Progress } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const EnhancedLoading = ({ 
  type = 'skeleton',
  title = null,
  message = 'Chargement...',
  progress = null,
  showProgress = false,
  size = 'default',
  style = {},
  children = null
}) => {
  const renderSkeletonByType = (skeletonType) => {
    switch (skeletonType) {
      case 'stats':
        return (
          <div style={{ display: 'flex', gap: '16px', padding: '16px' }}>
            {[...Array(4)].map((_, i) => (
              <Card key={i} style={{ flex: 1, minWidth: '200px' }}>
                <Skeleton 
                  active 
                  avatar={{ size: 'small' }}
                  paragraph={{ rows: 1 }}
                  title={{ width: '60%' }}
                />
                <div style={{ 
                  marginTop: '12px',
                  height: '24px',
                  background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                  backgroundSize: '200% 100%',
                  animation: 'shimmer 1.5s infinite',
                  borderRadius: '4px'
                }} />
              </Card>
            ))}
          </div>
        );
        
      case 'chart':
        return (
          <Card title={title} style={{ minHeight: '400px' }}>
            <Skeleton active paragraph={{ rows: 2 }} />
            <div style={{
              marginTop: '16px',
              height: '250px',
              background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
              backgroundSize: '200% 100%',
              animation: 'shimmer 1.5s infinite',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              gap: '16px'
            }}>
              <div style={{
                width: '80%',
                height: '20px',
                background: 'rgba(255,255,255,0.7)',
                borderRadius: '4px'
              }} />
              <div style={{
                width: '60%',
                height: '20px',
                background: 'rgba(255,255,255,0.7)',
                borderRadius: '4px'
              }} />
              <div style={{
                width: '90%',
                height: '20px',
                background: 'rgba(255,255,255,0.7)',
                borderRadius: '4px'
              }} />
            </div>
          </Card>
        );
        
      case 'table':
        return (
          <Card title={title} style={{ minHeight: '500px' }}>
            <Skeleton active />
            <div style={{ marginTop: '16px' }}>
              {[...Array(6)].map((_, i) => (
                <div 
                  key={i}
                  style={{
                    height: '40px',
                    background: i % 2 === 0 ? '#fafafa' : '#ffffff',
                    border: '1px solid #f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    padding: '0 16px',
                    gap: '16px'
                  }}
                >
                  {[...Array(6)].map((_, j) => (
                    <div
                      key={j}
                      style={{
                        width: `${Math.random() * 40 + 60}px`,
                        height: '16px',
                        background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                        backgroundSize: '200% 100%',
                        animation: 'shimmer 1.5s infinite',
                        borderRadius: '4px',
                        animationDelay: `${(i * 6 + j) * 0.1}s`
                      }}
                    />
                  ))}
                </div>
              ))}
            </div>
          </Card>
        );
        
      case 'performance':
        return (
          <Card title={title} style={{ minHeight: '180px' }}>
            <div style={{ display: 'flex', gap: '16px' }}>
              {[...Array(3)].map((_, i) => (
                <Card key={i} style={{ flex: 1 }}>
                  <Skeleton 
                    active 
                    avatar={{ size: 'large', shape: 'circle' }}
                    paragraph={{ rows: 2 }}
                    title={false}
                  />
                  <div style={{
                    marginTop: '12px',
                    height: '6px',
                    background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                    backgroundSize: '200% 100%',
                    animation: 'shimmer 1.5s infinite',
                    borderRadius: '3px'
                  }} />
                </Card>
              ))}
            </div>
          </Card>
        );
        
      default:
        return <Skeleton active paragraph={{ rows: 4 }} />;
    }
  };

  const renderSpinner = () => (
    <div style={{ 
      textAlign: 'center', 
      padding: '40px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '16px',
      ...style 
    }}>
      <Spin 
        size={size}
        indicator={<LoadingOutlined style={{ fontSize: size === 'large' ? 32 : 24 }} spin />}
      />
      {message && (
        <div style={{ 
          color: '#666', 
          fontSize: '14px',
          marginTop: '8px'
        }}>
          {message}
        </div>
      )}
      {showProgress && progress !== null && (
        <Progress 
          percent={progress} 
          size="small" 
          style={{ width: '200px' }}
          strokeColor={{
            from: '#108ee9',
            to: '#87d068',
          }}
        />
      )}
    </div>
  );

  // Add shimmer animation styles
  const shimmerStyles = (
    <style jsx>{`
      @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
      }
    `}</style>
  );

  if (type === 'spinner') {
    return (
      <>
        {renderSpinner()}
        {shimmerStyles}
      </>
    );
  }

  if (type.startsWith('skeleton-')) {
    const skeletonType = type.replace('skeleton-', '');
    return (
      <>
        {renderSkeletonByType(skeletonType)}
        {shimmerStyles}
      </>
    );
  }

  return (
    <>
      <Skeleton active paragraph={{ rows: 4 }} />
      {shimmerStyles}
    </>
  );
};

export default EnhancedLoading;
