import React, { useEffect } from 'react';
import { Row, Col, Typography, Button, Space, Alert, Badge } from 'antd';
import { 
  ReloadOutlined, 
  SearchOutlined, 
  DownloadOutlined, 
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import SOMIPEM_COLORS from '../../styles/brand-colors';

const { Title } = Typography;

const ArretHeader = () => {
  const { 
    handleRefresh, 
    setIsSearchModalVisible, 
    exportToExcel,
    loading,
    error,
    essentialLoading,
    detailedLoading,
    complexFilterLoading,
    graphQL, // New: direct access to GraphQL hook
    dataManager // New: access to dataManager methods
  } = useArretQueuedContext() || {};

  // Log performance metrics when appropriate
  useEffect(() => {
    if (graphQL && graphQL.getCacheStats) {
      const performanceInterval = setInterval(() => {
        const stats = graphQL.getCacheStats();
        if (stats) {
          console.log('📊 ArretHeader: GraphQL performance metrics', stats);
        }
      }, 60000); // Log every minute
      
      return () => clearInterval(performanceInterval);
    }
  }, [graphQL]);

  return (
    <>
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2} style={{ 
            margin: 0, 
            color: SOMIPEM_COLORS.PRIMARY_BLUE // Primary Blue for main title
          }}>
            🚨 Tableau de Bord des Arrêts de Machines
            {!loading && !error && (
              <Badge 
                count={<CheckCircleOutlined style={{ 
                  color: SOMIPEM_COLORS.SECONDARY_BLUE // Secondary Blue for status icon
                }} />} 
                offset={[5, -3]}
                title="Connected to optimized GraphQL backend"
              />
            )}
          </Title>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<SearchOutlined />} 
              onClick={() => setIsSearchModalVisible && setIsSearchModalVisible(true)}
              disabled={loading}
              style={{
                borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                color: SOMIPEM_COLORS.PRIMARY_BLUE
              }}
            >
              Recherche Globale
            </Button>
            <Button 
              icon={<DownloadOutlined />}
              onClick={() => exportToExcel && exportToExcel()}
              type="primary"
              disabled={loading}
              style={{
                backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
              }}
            >
              Exporter Excel
            </Button>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => handleRefresh && handleRefresh()}
              type="default"
              loading={loading || complexFilterLoading}
              style={{
                borderColor: SOMIPEM_COLORS.LIGHT_GRAY,
                color: SOMIPEM_COLORS.DARK_GRAY
              }}
            >
              {loading || complexFilterLoading ? 'Chargement...' : 'Actualiser'}
            </Button>
            {graphQL && graphQL.getCacheStats && (
              <Button
                icon={<HistoryOutlined />}
                onClick={() => {
                  const stats = graphQL.getCacheStats();
                  console.log('📊 Cache Stats:', stats);
                  alert(`Cache Hits: ${stats.cacheHits}\nCache Misses: ${stats.cacheMisses}\nAvg Response: ${stats.avgResponseTime.toFixed(2)}ms`);
                }}
                type="text"
                size="small"
                title="Show cache statistics"
                style={{
                  color: SOMIPEM_COLORS.LIGHT_GRAY
                }}
              />
            )}
          </Space>
        </Col>
      </Row>
      
      {/* Error Display */}
      {error && (
        <Alert
          message="Erreur de chargement"
          description={
            <div>
              <p>{error}</p>
              <Button 
                type="primary" 
                size="small" 
                icon={<ReloadOutlined />}
                onClick={() => handleRefresh && handleRefresh()}
                loading={loading}
              >
                Réessayer
              </Button>
            </div>
          }
          type="error"
          icon={<ExclamationCircleOutlined />}
          showIcon
          closable
          style={{ marginBottom: '16px' }}
        />
      )}
    </>
  );
};

export default ArretHeader;
