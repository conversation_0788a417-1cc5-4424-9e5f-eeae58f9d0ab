import React, { memo } from 'react';
import { Card, Statistic, Row, Col, Progress, Alert } from 'antd';
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  ThunderboltOutlined,
  DatabaseOutlined,
  ClockCircleOutlined 
} from '@ant-design/icons';
import { formatFrenchNumber, formatFrenchPercentage } from '../utils/numberFormatter';

// Optimized statistic card with memoization
export const OptimizedStatCard = memo(({ 
  title, 
  value, 
  suffix, 
  prefix, 
  precision = 0,
  valueStyle = {},
  trend = null,
  loading = false,
  color = '#1890ff'
}) => {
  const getTrendIcon = () => {
    if (!trend) return null;
    return trend > 0 ? 
      <ArrowUpOutlined style={{ color: '#3f8600' }} /> : 
      <ArrowDownOutlined style={{ color: '#cf1322' }} />;
  };

  return (
    <Card size="small" loading={loading}>
      <Statistic
        title={title}
        value={value}
        precision={precision}
        valueStyle={{ color, ...valueStyle }}
        prefix={prefix}
        suffix={
          <span>
            {suffix} {getTrendIcon()}
          </span>
        }
        formatter={(value) => {
          if (suffix === '%') {
            return formatFrenchPercentage(value, precision || 1);
          }
          return formatFrenchNumber(value, precision);
        }}
      />
      {trend !== null && (
        <div style={{ fontSize: 12, color: trend > 0 ? '#3f8600' : '#cf1322' }}>
          {formatFrenchPercentage(Math.abs(trend), 1)}% from last period
        </div>
      )}
    </Card>
  );
});

// Performance-optimized chart wrapper
export const OptimizedChartWrapper = memo(({ 
  children, 
  loading = false, 
  dataLength = 0,
  maxDataPoints = 200,
  title = "Chart"
}) => {
  const isDataSampled = dataLength > maxDataPoints;
  
  return (
    <Card title={title} size="small" loading={loading}>
      {isDataSampled && (
        <Alert
          message={`Showing ${maxDataPoints} of ${dataLength} data points for better performance`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      {children}
    </Card>
  );
});

// Dashboard performance metrics display
export const DashboardMetrics = memo(({ metrics = {}, className = "" }) => {
  const performanceScore = metrics.performanceScore || 0;
  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  return (
    <div className={className}>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <Statistic
              title="Performance Score"
              value={performanceScore}
              suffix="/100"
              valueStyle={{ color: getScoreColor(performanceScore) }}
              prefix={<ThunderboltOutlined />}
            />
            <Progress 
              percent={performanceScore} 
              strokeColor={getScoreColor(performanceScore)}
              size="small"
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <Statistic
              title="Cache Hit Rate"
              value={metrics.cacheHitRate || 0}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#1890ff' }}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <Statistic
              title="Avg Response Time"
              value={metrics.avgApiTime || 0}
              suffix="ms"
              valueStyle={{ color: '#722ed1' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <Statistic
              title="Active Requests"
              value={metrics.activeRequests || 0}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>
      
      {metrics.recommendations && metrics.recommendations.length > 0 && (
        <Alert
          message="Performance Recommendations"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              {metrics.recommendations.slice(0, 3).map((rec, index) => (
                <li key={index}>{rec}</li>
              ))}
            </ul>
          }
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );
});

// Optimized data table with virtualization hints
export const OptimizedDataDisplay = memo(({ 
  data = [], 
  loading = false, 
  maxRows = 50,
  title = "Data",
  onViewMore = null
}) => {
  const displayData = data.slice(0, maxRows);
  const hasMore = data.length > maxRows;

  return (
    <Card 
      title={title}
      loading={loading}
      extra={
        hasMore && onViewMore && (
          <span style={{ color: '#1890ff', cursor: 'pointer' }} onClick={onViewMore}>
            View All ({data.length})
          </span>
        )
      }
    >
      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        {displayData.map((item, index) => (
          <div key={index} style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
            {JSON.stringify(item)}
          </div>
        ))}
      </div>
      
      {hasMore && (
        <Alert
          message={`Showing ${maxRows} of ${data.length} items`}
          type="info"
          showIcon
          style={{ marginTop: 12 }}
        />
      )}
    </Card>
  );
});

export default {
  OptimizedStatCard,
  OptimizedChartWrapper,
  DashboardMetrics,
  OptimizedDataDisplay
};
