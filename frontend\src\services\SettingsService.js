import superagent from 'superagent';

/**
 * Settings Service - Handles all settings-related API calls
 * Uses SuperAgent for HTTP requests with standardized configuration
 */
class SettingsService {
  constructor() {
    this.baseURL = process.env.NODE_ENV === 'production'
      ? window.location.origin
      : 'http://localhost:5000';

    // Optimized timeout and retry configuration for settings
    this.timeout = 10000; // Reduced to 10 seconds for faster feedback
    this.retries = 3; // Increased retries for better reliability
    this.settingsTimeout = 5000; // Even shorter timeout for settings operations
  }

  /**
   * Configure SuperAgent request with standard settings
   * @param {Object} request - SuperAgent request object
   * @returns {Object} Configured request
   */
  configureRequest(request) {
    return request
      .timeout(this.timeout)
      .retry(this.retries)
      .withCredentials() // Use cookies for authentication
      .set('Content-Type', 'application/json');
  }

  /**
   * Handle API response and extract data
   * @param {Object} response - SuperAgent response
   * @returns {*} Response data
   */
  handleResponse(response) {
    if (!response.body.success) {
      throw new Error(response.body.message || 'API request failed');
    }
    return response.body;
  }

  /**
   * Handle API errors
   * @param {Error} error - Error object
   * @throws {Error} Formatted error
   */
  handleError(error) {
    console.error('Settings API Error:', error);
    
    if (error.response) {
      const { status, body } = error.response;
      
      switch (status) {
        case 401:
          throw new Error('Authentication required. Please log in.');
        case 403:
          throw new Error('Access denied. Insufficient permissions.');
        case 404:
          throw new Error('Settings endpoint not found.');
        case 429:
          throw new Error('Too many requests. Please try again later.');
        case 500:
          throw new Error(body?.message || 'Server error occurred.');
        default:
          throw new Error(body?.message || `Request failed with status ${status}`);
      }
    } else if (error.timeout) {
      throw new Error('Request timeout. Please check your connection.');
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('Cannot connect to server. Please try again later.');
    } else {
      throw new Error(error.message || 'Network error occurred.');
    }
  }

  /**
   * Test API connectivity and performance
   * @returns {Promise<Object>} Health check results
   */
  async healthCheck() {
    try {
      const startTime = Date.now();
      console.log('🏥 Performing settings API health check...');

      const request = superagent
        .get(`${this.baseURL}/api/health`)
        .timeout({
          response: 3000, // 3 seconds for health check
          deadline: 5000, // 5 seconds total
        });

      const response = await this.configureRequest(request);
      const duration = Date.now() - startTime;

      console.log(`✅ Health check passed in ${duration}ms`);
      return {
        success: true,
        duration,
        status: response.status,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Health check failed after ${duration}ms:`, error.message);
      return {
        success: false,
        duration,
        error: error.message,
        status: error.status || 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get user settings
   * @returns {Promise<Object>} User settings object
   */
  async getSettings() {
    try {
      console.log('🔍 Fetching user settings...');
      
      const request = superagent.get(`${this.baseURL}/api/settings`);
      const response = await this.configureRequest(request);
      const result = this.handleResponse(response);
      
      console.log('✅ Settings fetched successfully');
      return result.settings;
      
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Update user settings with enhanced error handling and timeout management
   * @param {Object} settings - Settings object to update
   * @returns {Promise<Object>} Updated settings object
   */
  async updateSettings(settings) {
    const startTime = Date.now();
    let lastError = null;

    // Validate settings payload size
    const settingsSize = JSON.stringify(settings).length;
    if (settingsSize > 100000) { // 100KB limit
      throw new Error(`Settings payload too large: ${settingsSize} bytes. Maximum allowed: 100KB`);
    }

    console.log(`🔄 Updating user settings... (${settingsSize} bytes)`);

    // Retry logic with exponential backoff
    for (let attempt = 1; attempt <= this.retries; attempt++) {
      const attemptStartTime = Date.now(); // Move outside try block
      try {
        console.log(`📡 Settings update attempt ${attempt}/${this.retries}`);

        const request = superagent
          .put(`${this.baseURL}/api/settings`)
          .send({ settings })
          .timeout({
            response: this.settingsTimeout, // 5 seconds for response
            deadline: this.settingsTimeout + 2000, // 7 seconds total deadline
          });

        const response = await this.configureRequest(request);
        const result = this.handleResponse(response);

        const duration = Date.now() - attemptStartTime;
        console.log(`✅ Settings updated successfully in ${duration}ms (attempt ${attempt})`);
        return result.settings;

      } catch (error) {
        lastError = error;
        const duration = Date.now() - attemptStartTime;

        console.warn(`⚠️ Settings update attempt ${attempt} failed after ${duration}ms:`, error.message);

        // Don't retry on certain errors
        if (error.status === 400 || error.status === 401 || error.status === 403) {
          console.error('❌ Non-retryable error, aborting retries');
          break;
        }

        // Wait before retry (exponential backoff)
        if (attempt < this.retries) {
          const backoffDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          console.log(`⏳ Waiting ${backoffDelay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, backoffDelay));
        }
      }
    }

    // All retries failed
    const totalDuration = Date.now() - startTime;
    console.error(`❌ Settings update failed after ${this.retries} attempts in ${totalDuration}ms`);
    this.handleError(lastError);
  }

  /**
   * Reset settings to defaults with enhanced error handling
   * @returns {Promise<Object>} Default settings object
   */
  async resetSettings() {
    const startTime = Date.now();
    let lastError = null;

    console.log('🔄 Resetting settings to defaults...');

    // Retry logic for reset operation
    for (let attempt = 1; attempt <= this.retries; attempt++) {
      const attemptStartTime = Date.now(); // Move outside try block
      try {
        console.log(`📡 Settings reset attempt ${attempt}/${this.retries}`);

        const request = superagent
          .post(`${this.baseURL}/api/settings/reset`)
          .timeout({
            response: this.settingsTimeout, // 5 seconds for response
            deadline: this.settingsTimeout + 2000, // 7 seconds total deadline
          });

        const response = await this.configureRequest(request);
        const result = this.handleResponse(response);

        const duration = Date.now() - attemptStartTime;
        console.log(`✅ Settings reset successfully in ${duration}ms (attempt ${attempt})`);
        return result.settings;

      } catch (error) {
        lastError = error;
        const duration = Date.now() - attemptStartTime;

        console.warn(`⚠️ Settings reset attempt ${attempt} failed after ${duration}ms:`, error.message);

        // Don't retry on certain errors
        if (error.status === 400 || error.status === 401 || error.status === 403) {
          console.error('❌ Non-retryable error, aborting retries');
          break;
        }

        // Wait before retry
        if (attempt < this.retries) {
          const backoffDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          console.log(`⏳ Waiting ${backoffDelay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, backoffDelay));
        }
      }
    }

    // All retries failed
    const totalDuration = Date.now() - startTime;
    console.error(`❌ Settings reset failed after ${this.retries} attempts in ${totalDuration}ms`);
    this.handleError(lastError);
  }

  /**
   * Get settings validation schema
   * @returns {Promise<Object>} Settings schema object
   */
  async getSettingsSchema() {
    try {
      console.log('📋 Fetching settings schema...');
      
      const request = superagent.get(`${this.baseURL}/api/settings/schema`);
      const response = await this.configureRequest(request);
      const result = this.handleResponse(response);
      
      console.log('✅ Settings schema fetched successfully');
      return result.schema;
      
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Validate settings against schema
   * @param {Object} settings - Settings to validate
   * @param {Object} schema - Validation schema
   * @returns {Object} Validation result
   */
  validateSettings(settings, schema) {
    const errors = [];
    const warnings = [];

    function validateObject(obj, schemaObj, path = '') {
      for (const [key, schemaValue] of Object.entries(schemaObj)) {
        const currentPath = path ? `${path}.${key}` : key;
        const value = obj[key];

        if (value === undefined || value === null) {
          if (schemaValue.required) {
            errors.push(`${currentPath} is required`);
          }
          continue;
        }

        // Type validation
        if (schemaValue.type) {
          const expectedType = schemaValue.type;
          const actualType = Array.isArray(value) ? 'array' : typeof value;

          if (actualType !== expectedType) {
            errors.push(`${currentPath} should be ${expectedType}, got ${actualType}`);
            continue;
          }
        }

        // Range validation for numbers
        if (typeof value === 'number' && schemaValue.type === 'number') {
          if (schemaValue.min !== undefined && value < schemaValue.min) {
            errors.push(`${currentPath} should be at least ${schemaValue.min}`);
          }
          if (schemaValue.max !== undefined && value > schemaValue.max) {
            errors.push(`${currentPath} should be at most ${schemaValue.max}`);
          }
        }

        // Options validation
        if (schemaValue.options && !schemaValue.options.includes(value)) {
          errors.push(`${currentPath} should be one of: ${schemaValue.options.join(', ')}`);
        }

        // Nested object validation
        if (typeof value === 'object' && !Array.isArray(value) && typeof schemaValue === 'object' && !schemaValue.type) {
          validateObject(value, schemaValue, currentPath);
        }
      }
    }

    try {
      validateObject(settings, schema);
    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get default settings structure
   * @returns {Object} Default settings object
   */
  getDefaultSettings() {
    return {
      theme: {
        darkMode: false,
        compactMode: false,
        animationsEnabled: true,
        chartAnimations: true
      },
      tables: {
        defaultPageSize: 20,
        pageSizeOptions: [10, 20, 50, 100],
        virtualizationThreshold: 100,
        showQuickJumper: true
      },
      charts: {
        animationsEnabled: true,
        defaultType: 'bar',
        showLegend: true,
        colorScheme: 'somipem',
        performanceMode: false
      },
      refresh: {
        dashboardInterval: 300,
        realtimeInterval: 60,
        autoRefreshEnabled: true,
        backgroundRefresh: true
      },
      notifications: {
        categories: {
          machine_alert: true,
          production: true,
          quality: true,
          maintenance: true,
          alert: true,
          info: true,
          updates: true
        },
        priorities: {
          critical: true,
          high: true,
          medium: true,
          low: true
        },
        delivery: {
          sse: true,
          email: false,
          browser: true
        },
        behavior: {
          sound: true,
          autoClose: false,
          autoCloseDelay: 5000,
          maxVisible: 5
        }
      },
      email: {
        enabled: false,
        frequency: 'immediate',
        template: 'standard'
      },
      reports: {
        generation: {
          autoGenerate: false,
          format: 'pdf',
          quality: 'standard',
          includeCharts: true,
          includeTables: true
        }
      },
      performance: {
        caching: {
          enabled: true,
          duration: 300,
          strategy: 'smart'
        },
        optimization: {
          lazyLoading: true,
          virtualization: true,
          compression: false
        }
      }
    };
  }
}

// Export singleton instance
export default new SettingsService();
