/**
 * Daily table routes for machine production data
 */
import express from "express";
import pool from "../db.js";
import { corsOptions } from "../middleware/cors.js";
import cors from "cors"
import { indexProductionData } from "../middleware/elasticsearchMiddleware.js";
import { buildDateFilter } from "../utils/dateUtils.js";
import { executeQuery } from "../utils/dbUtils.js";
import { sendSuccess, sendError, asyncHandler } from "../utils/responseUtils.js";
import redisRestApiEnhancer from "../services/RedisRestApiEnhancer.js";

// Utility function to normalize availability metrics
const normalizeAvailabilityMetrics = (data) => {
  if (!data || !Array.isArray(data)) return data;
  
  return data.map(row => {
    // Function to normalize a metric value (convert percentage string to decimal)
    const normalizeMetric = (value) => {
      if (!value) return value;
      
      if (typeof value === 'string' && value.includes('%')) {
        return parseFloat(value.replace('%', '').replace(',', '.')) / 100;
      } else if (typeof value === 'string') {
        return parseFloat(value.replace(',', '.'));
      }
      return value;
    };

    // Create a new object with normalized values for availability metrics
    return {
      ...row,
      Availability_Rate_Day: normalizeMetric(row.Availability_Rate_Day),
      Performance_Rate_Day: normalizeMetric(row.Performance_Rate_Day),
      Quality_Rate_Day: normalizeMetric(row.Quality_Rate_Day),
      OEE_Day: normalizeMetric(row.OEE_Day),
      Speed_Day: row.Speed_Day ? normalizeMetric(row.Speed_Day) : row.Speed_Day,
      // Also handle common aliases
      availability: row.availability ? normalizeMetric(row.availability) : undefined,
      performance: row.performance ? normalizeMetric(row.performance) : undefined,
      quality: row.quality ? normalizeMetric(row.quality) : undefined,
      oee: row.oee ? normalizeMetric(row.oee) : undefined,
      disponibilite: row.disponibilite ? normalizeMetric(row.disponibilite) : undefined
    };
  });
};

const router = express.Router();

/**
 * @route   GET /api/DailyTableMould
 * @desc    Get all machine daily data
 * @access  Public
 */
router.get("/DailyTableMould", cors(corsOptions), indexProductionData, redisRestApiEnhancer.createDashboardEndpoint('/api/DailyTableMould', {
  ttl: 300, // 5 minutes TTL for daily table data
  keyPrefix: 'dashboard'
})(asyncHandler(async (_, res) => {
  const { success, data, error } = await executeQuery(
    "SELECT * FROM machine_daily_table_mould"
  );

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  // Normalize availability metrics to always be decimals
  const normalizedData = data.map(row => {
    // Function to normalize a metric value
    const normalizeMetric = (value) => {
      if (!value) return value;
      
      if (typeof value === 'string' && value.includes('%')) {
        return parseFloat(value.replace('%', '').replace(',', '.')) / 100;
      } else if (typeof value === 'string') {
        return parseFloat(value.replace(',', '.'));
      }
      return value;
    };

    // Create a new object with normalized values
    return {
      ...row,
      Availability_Rate_Day: normalizeMetric(row.Availability_Rate_Day),
      Performance_Rate_Day: normalizeMetric(row.Performance_Rate_Day),
      Quality_Rate_Day: normalizeMetric(row.Quality_Rate_Day),
      OEE_Day: normalizeMetric(row.OEE_Day)
    };
  });

  return sendSuccess(res, normalizedData);
})))

/**
 * @route   GET /api/chart-production
 * @desc    Get production chart data for the last 5 days
 * @access  Public
 */
router.get("/chart-production", cors(corsOptions), redisRestApiEnhancer.createDashboardEndpoint('/api/chart-production', {
  ttl: 300, // 5 minutes TTL for production chart
  keyPrefix: 'dashboard'
})(asyncHandler(async (_, res) => {
  const query = `
    SELECT
      DATE_FORMAT(
          STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'),
          '%Y-%m-%d'
      ) AS Date_Insert_Day,
      SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS Total_Good_Qty_Day,
      SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS Total_Rejects_Qty_Day
    FROM machine_daily_table_mould
    WHERE
        STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
        DATE_SUB(CURDATE(), INTERVAL 5 DAY)
    GROUP BY Date_Insert_Day
    ORDER BY Date_Insert_Day ASC
  `;

  const { success, data, error } = await executeQuery(query);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  return sendSuccess(res, data);
})))

/**
 * @route   GET /api/unique-dates-production
 * @desc    Get unique production dates for the last 30 days
 * @access  Public
 */
router.get("/unique-dates-production", cors(corsOptions), asyncHandler(async (_, res) => {
  const query = `
    SELECT DISTINCT
      DATE_FORMAT(
        STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'),
        '%Y-%m-%d'
      ) AS date
    FROM machine_daily_table_mould
    WHERE
      STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
      DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ORDER BY date DESC
  `;

  const { success, data, error } = await executeQuery(query);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  // Extract just the date strings from the result objects
  const dates = data.map(item => item.date);

  return sendSuccess(res, dates);
}))

/**
 * @route   GET /api/testing-chart-production
 * @desc    Get production chart data with filtering options
 * @access  Public
 */
router.get("/testing-chart-production", cors(corsOptions), asyncHandler(async (req, res) => {
  const dateParam = req.query.date;
  const dateRangeType = req.query.dateRangeType || "day"; // Default to day view
  const machineModel = req.query.model;
  const machineName = req.query.machine || "IPS01"; // Default to IPS01

  // Add pagination parameters
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 100; // Default limit for charts
  const offset = (page - 1) * limit;
  // Base query
  let query = `
    SELECT
      DATE_FORMAT(
          STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'),
          '%Y-%m-%d'
      ) AS Date_Insert_Day,
      SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS Total_Good_Qty_Day,
      SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS Total_Rejects_Qty_Day,
      AVG(CAST(OEE_Day AS DECIMAL(10,2))) AS OEE_Day,
      AVG(CAST(Speed_Day AS DECIMAL(10,2))) AS Speed_Day,
      AVG(CAST(Availability_Rate_Day AS DECIMAL(10,2))) AS Availability_Rate_Day,
      AVG(CAST(Performance_Rate_Day AS DECIMAL(10,2))) AS Performance_Rate_Day,
      AVG(CAST(Quality_Rate_Day AS DECIMAL(10,2))) AS Quality_Rate_Day
    FROM machine_daily_table_mould
    WHERE 1=1`;

  // Initialize params array
  let queryParams = [];  // Add date filter
  if (dateParam) {
    // Use our date filter utility
    const dateFilter = buildDateFilter(dateParam, dateRangeType);
    query += dateFilter.condition;
    queryParams = queryParams.concat(dateFilter.params);
  }

  // Add machine filter
  if (machineModel) {
    query += ` AND Machine_Name LIKE ?`;
    queryParams.push(`${machineModel}%`);
  } else if (machineName) {
    query += ` AND Machine_Name = ?`;
    queryParams.push(machineName);
  }  // Group by date and ensure proper date sorting
  query += ` GROUP BY Date_Insert_Day ORDER BY Date_Insert_Day DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;
  // Don't push limit and offset as parameters since we're using string interpolation
  
  // Execute query with error handling
  const { success, data, error } = await executeQuery(query, queryParams);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  // Apply normalization to ensure all values are decimals
  const normalizedData = normalizeAvailabilityMetrics(data);

  return sendSuccess(res, normalizedData);
}))

// Update sidecards-prod endpoint to accept machine filters and ensure numeric values
router.get("/sidecards-prod", cors(corsOptions), redisRestApiEnhancer.createDashboardEndpoint('/api/sidecards-prod', {
  ttl: 300, // 5 minutes TTL for sidecards
  keyPrefix: 'dashboard'
})(async (req, res) => {
  try {
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day" // Default to day view
    const machineModel = req.query.model
    const machineName = req.query.machine

    let query = `SELECT SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS goodqty FROM machine_daily_table_mould WHERE 1=1`
    const queryParams = []

    if (dateParam) {
      // Handle different date range types
      if (dateRangeType === "day") {
        // Single day view
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`
        queryParams.push(dateParam)
      } else if (dateRangeType === "week") {
        // Week view - from Monday to Sunday (ISO week)
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
                  DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY)
                  AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <=
                  DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY), INTERVAL 6 DAY)`
        queryParams.push(dateParam, dateParam, dateParam, dateParam)
      } else if (dateRangeType === "month") {
        // Month view
        query += ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                  AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`
        queryParams.push(dateParam, dateParam)
      }
    } else {
      // When no date is specified, show all data for the selected machine model
      // No additional date filter is applied
      console.log("No date filter applied for sidecards-prod, showing all data")
    }

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      query += ` AND Machine_Name = ?`
      queryParams.push(machineName)
    }    const [results] = await pool.execute(query, queryParams)

    // Ensure we always return a numeric value, even if no data is found
    const goodqty = results[0]?.goodqty || 0

    // If no data is found and no filters are applied, return a sample value
    if (goodqty === 0 && !machineModel && !machineName && !dateParam) {
      return res.json([{ goodqty: 15000 }])
    }

    res.json([{ goodqty: Number(goodqty) }])
  } catch (err) {
    console.error("Database error:", err)
    // Return sample data in case of error
    res.json([{ goodqty: 15000 }])
  }
}))

// Update sidecards-prod-rejet endpoint to accept machine filters and ensure numeric values
router.get("/sidecards-prod-rejet", cors(corsOptions), redisRestApiEnhancer.createDashboardEndpoint('/api/sidecards-prod-rejet', {
  ttl: 300, // 5 minutes TTL for sidecards
  keyPrefix: 'dashboard'
})(async (req, res) => {
  try {
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day" // Default to day view
    const machineModel = req.query.model
    const machineName = req.query.machine

    let query = `SELECT SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS rejetqty FROM machine_daily_table_mould WHERE 1=1`
    const queryParams = []

    if (dateParam) {
      // Handle different date range types
      if (dateRangeType === "day") {
        // Single day view
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`
        queryParams.push(dateParam)
      } else if (dateRangeType === "week") {
        // Week view - from Monday to Sunday (ISO week)
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
                  DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY)
                  AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <=
                  DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY), INTERVAL 6 DAY)`
        queryParams.push(dateParam, dateParam, dateParam, dateParam)
      } else if (dateRangeType === "month") {
        // Month view
        query += ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                  AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`
        queryParams.push(dateParam, dateParam)
      }
    } else {
      // When no date is specified, show all data for the selected machine model
      // No additional date filter is applied
      console.log("No date filter applied for sidecards-prod-rejet, showing all data")
    }

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      query += ` AND Machine_Name = ?`
      queryParams.push(machineName)
    }    const [results] = await pool.execute(query, queryParams)

    // Ensure we always return a numeric value, even if no data is found
    const rejetqty = results[0]?.rejetqty || 0

    // If no data is found and no filters are applied, return a sample value
    if (rejetqty === 0 && !machineModel && !machineName && !dateParam) {
      return res.json([{ rejetqty: 750 }])
    }

    res.json([{ rejetqty: Number(rejetqty) }])
  } catch (err) {
    console.error("Database error:", err)
    // Return sample data in case of error
    res.json([{ rejetqty: 750 }])
  }
}))

// Update machine-performance endpoint to accept machine filters
router.get("/machine-performance", cors(corsOptions), async (req, res) => {
  try {
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day" // Default to day view
    const machineModel = req.query.model
    const machineName = req.query.machine || "IPS01" // Default to IPS01

    // Add pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50; // Default limit for tables
    const offset = (page - 1) * limit;    let query = `
      SELECT
        Machine_Name,
        Shift,
        SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS production,
        SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS rejects,
        ROUND(AVG(CASE 
          WHEN Availability_Rate_Day LIKE '%\%%' THEN CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
          ELSE CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,2))
        END), 2) AS availability,
        ROUND(AVG(CASE 
          WHEN Performance_Rate_Day LIKE '%\%%' THEN CAST(REPLACE(REPLACE(Performance_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
          ELSE CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,2))
        END), 2) AS performance,
        ROUND(AVG(CASE 
          WHEN OEE_Day LIKE '%\%%' THEN CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
          ELSE CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,2))
        END), 2) AS oee,
        ROUND(AVG(CASE 
          WHEN Quality_Rate_Day LIKE '%\%%' THEN CAST(REPLACE(REPLACE(Quality_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
          ELSE CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,2))
        END), 2) AS quality,
        ROUND(AVG(CASE 
          WHEN Availability_Rate_Day LIKE '%\%%' THEN CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2))
          ELSE CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,2)) * 100
        END), 2) AS disponibilite
      FROM machine_daily_table_mould
      WHERE 1=1`

    const queryParams = []

    if (dateParam) {
      // Handle different date range types
      if (dateRangeType === "day") {
        // Single day view
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`
        queryParams.push(dateParam)
      } else if (dateRangeType === "week") {
        // Week view
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >= DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d')) DAY)
                  AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <= DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d')) DAY), INTERVAL 6 DAY)`
        queryParams.push(dateParam, dateParam, dateParam, dateParam)
      } else if (dateRangeType === "month") {
        // Month view
        query += ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                  AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`
        queryParams.push(dateParam, dateParam)
      }
    }

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)    } else if (machineName) {
      query += ` AND Machine_Name = ?`;
      queryParams.push(machineName);
    }
    
    query += ` GROUP BY Machine_Name, Shift ORDER BY production DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;
    // Don't push limit and offset as parameters since we're using string interpolation

    const [results] = await pool.execute(query, queryParams);
    
    // We'll use the utility function here for consistency, even though the SQL query
    // should already be handling the normalization
    const normalizedResults = normalizeAvailabilityMetrics(results);
    
    res.json(normalizedResults)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Update hourly-trends endpoint to accept machine filters
router.get("/hourly-trends", cors(corsOptions), async (req, res) => {
  try {
    const machineModel = req.query.model
    const machineName = req.query.machine || "IPS01" // Default to IPS01

    let query = `
      SELECT
        DATE_FORMAT(DATE(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y %H:%i:%s')), '%Y-%m-%d %H') AS hour,
        AVG(CAST(Speed_Day AS DECIMAL(10,2))) AS average_speed
      FROM machine_daily_table_mould
      WHERE 1=1`

    const queryParams = []

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      query += ` AND Machine_Name = ?`
      queryParams.push(machineName)
    }

    query += ` GROUP BY hour ORDER BY hour DESC LIMIT 24`

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Update operator-stats endpoint to accept machine filters
router.get("/operator-stats", cors(corsOptions), async (req, res) => {
  try {
    const machineModel = req.query.model
    const machineName = req.query.machine
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day"

    let query = `
      SELECT
        Part_Number AS operator,
        SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS production,
        SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS rejects,
        AVG(CAST(OEE_Day AS DECIMAL(10,2))) AS efficiency,
        AVG(CAST(OEE_Day AS DECIMAL(10,2))) AS oee
      FROM machine_daily_table_mould
      WHERE Part_Number IS NOT NULL AND Part_Number != ''`

    const queryParams = []

    if (dateParam) {
      // Handle different date range types
      if (dateRangeType === "day") {
        // Single day view
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`
        queryParams.push(dateParam)
      } else if (dateRangeType === "week") {
        // Week view - from Monday to Sunday (ISO week)
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
                  DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY)
                  AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <=
                  DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY), INTERVAL 6 DAY)`
        queryParams.push(dateParam, dateParam, dateParam, dateParam)
      } else if (dateRangeType === "month") {
        // Month view
        query += ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                  AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`
        queryParams.push(dateParam, dateParam)
      }
    } else {
      // When no date is specified, show all data for the selected machine model
      // No additional date filter is applied
      console.log("No date filter applied for operator-stats, showing all data")
    }

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      query += ` AND Machine_Name = ?`
      queryParams.push(machineName)
    }

    query += ` GROUP BY operator ORDER BY production DESC LIMIT 10`

    const [results] = await pool.execute(query, queryParams)

    // If no results, return sample data
    if (!results || results.length === 0) {
      console.log("No operator stats found, returning sample data")
      return res.json([
        { operator: "Operator 1", production: 1200, rejects: 50, efficiency: 85, oee: 82 },
        { operator: "Operator 2", production: 980, rejects: 30, efficiency: 78, oee: 75 },
        { operator: "Operator 3", production: 850, rejects: 25, efficiency: 82, oee: 80 }
      ])
    }

    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    // Return sample data in case of error
    res.json([
      { operator: "Operator 1", production: 1200, rejects: 50, efficiency: 85, oee: 82 },
      { operator: "Operator 2", production: 980, rejects: 30, efficiency: 78, oee: 75 },
      { operator: "Operator 3", production: 850, rejects: 25, efficiency: 82, oee: 80 }
    ])
  }
})

// Update machine-oee-trends endpoint to accept machine filters
router.get("/machine-oee-trends", cors(corsOptions), async (req, res) => {
  try {
    const machineModel = req.query.model
    const machineName = req.query.machine || "IPS01" // Default to IPS01

    let query = `
      SELECT
        Machine_Name,
        DATE_FORMAT(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'), '%Y-%m-%d') AS date,
        AVG(CAST(OEE_Day AS DECIMAL(10,2))) AS oee
      FROM machine_daily_table_mould
      WHERE 1=1`

    const queryParams = []

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      query += ` AND Machine_Name = ?`
      queryParams.push(machineName)
    }

    query += ` GROUP BY Machine_Name, date ORDER BY date ASC LIMIT 30`

    const [results] = await pool.execute(query, queryParams)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Update speed-trends endpoint to accept machine filters
router.get("/speed-trends", cors(corsOptions), async (req, res) => {
  try {
    const machineModel = req.query.model
    const machineName = req.query.machine || "IPS01" // Default to IPS01
    const dateParam = req.query.date
    const dateRangeType = req.query.dateRangeType || "day" // Default to day view

    let query = `
      SELECT
        DATE_FORMAT(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS date,
        AVG(CAST(REPLACE(REPLACE(Speed_Day, ',', '.'), '%', '') AS DECIMAL(10,2))) AS speed
      FROM machine_daily_table_mould
      WHERE 1=1`

    const queryParams = []

    if (dateParam) {
      // Handle different date range types
      if (dateRangeType === "day") {
        // Single day view
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`
        queryParams.push(dateParam)
      } else if (dateRangeType === "week") {
        // Week view - from Monday to Sunday (ISO week)
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
                  DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY)
                  AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <=
                  DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY), INTERVAL 6 DAY)`
        queryParams.push(dateParam, dateParam, dateParam, dateParam)
      } else if (dateRangeType === "month") {
        // Month view
        query += ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                  AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`
        queryParams.push(dateParam, dateParam)
      }
    } else {
      // When no date is specified, show all data for the selected machine model
      // No additional date filter is applied
      console.log("No date filter applied for speed-trends, showing all data")
    }

    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`
      queryParams.push(`${machineModel}%`)
    } else if (machineName) {
      query += ` AND Machine_Name = ?`
      queryParams.push(machineName)
    }

    query += ` GROUP BY date ORDER BY date ASC`

    const [results] = await pool.execute(query, queryParams)

    // Ensure we have valid data and fill in missing dates
    const processedResults = results.map(item => ({
      date: item.date,
      speed: parseFloat(item.speed) || 0
    }));

    // If we have results, ensure we have a continuous date range
    if (processedResults.length > 0) {
      // Sort by date
      processedResults.sort((a, b) => new Date(a.date) - new Date(b.date));

      // Get the date range
      const startDate = new Date(processedResults[0].date);
      const endDate = new Date(processedResults[processedResults.length - 1].date);

      // If the date range is too large (more than 60 days), limit it
      const maxDays = 60;
      const daysDiff = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));

      if (daysDiff > maxDays) {
        // Limit to the most recent data
        const newStartDate = new Date(endDate);
        newStartDate.setDate(newStartDate.getDate() - maxDays);

        // Filter to only include data within the new range
        const filteredResults = processedResults.filter(item =>
          new Date(item.date) >= newStartDate
        );

        res.json(filteredResults);
      } else {
        res.json(processedResults);
      }
    } else {
      res.json([]);
    }
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Update shift-comparison endpoint to accept machine filters
router.get("/shift-comparison", cors(corsOptions), async (req, res) => {  try {
    const machineModel = req.query.model
    const machineName = req.query.machine || "IPS01" // Default to IPS01
    // Build the query without complex expressions
    let query = `
      SELECT
        Shift,
        SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS production,
        SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS downtime,
        AVG(CAST(OEE_Day AS DECIMAL(10,2))) AS oee,
        AVG(CAST(Performance_Rate_Day AS DECIMAL(10,2))) AS performance,
        AVG(CAST(Availability_Rate_Day AS DECIMAL(10,2))) AS disponibilite      FROM machine_daily_table_mould
      WHERE 1=1`;

    const queryParams = [];
    
    if (machineModel) {
      query += ` AND Machine_Name LIKE ?`;
      queryParams.push(`${machineModel}%`);
    } else if (machineName) {
      query += ` AND Machine_Name = ?`;
      queryParams.push(machineName);
    }
    
    query += ` GROUP BY Shift`;

    const [results] = await pool.execute(query, queryParams)
    
    // Apply our normalization utility for consistency
    const normalizedResults = normalizeAvailabilityMetrics(results);
    
    res.json(normalizedResults)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

/**
 * @route   GET /api/machine-models
 * @desc    Get distinct machine models
 * @access  Public
 */
router.get("/machine-models", cors(corsOptions), asyncHandler(async (_, res) => {
  const query = `
    SELECT DISTINCT
      SUBSTRING(Machine_Name, 1,
        CASE
          WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
          THEN LENGTH(SUBSTRING_INDEX(Machine_Name, REGEXP_SUBSTR(Machine_Name, '[0-9].*$'), 1))
          ELSE LENGTH(Machine_Name)
        END
      ) AS model
    FROM machine_daily_table_mould
    WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
  `;

  const { success, data, error } = await executeQuery(query);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  return sendSuccess(res, data);
}))

/**
 * @route   GET /api/machine-names
 * @desc    Get distinct machine names or default values if none found
 * @access  Public
 */
router.get("/machine-names", cors(corsOptions), asyncHandler(async (_, res) => {
  const query = `
    SELECT DISTINCT Machine_Name
    FROM machine_daily_table_mould
    WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
    ORDER BY Machine_Name
  `;

  // Default machine names to return if no results
  const defaultMachines = [
    { Machine_Name: "IPS01" },
    { Machine_Name: "IPS02" },
    { Machine_Name: "IPS03" },
    { Machine_Name: "IPS04" },
  ];

  const { success, data } = await executeQuery(query);

  // If query failed or returned no results, return default values
  if (!success || !data || data.length === 0) {
    return sendSuccess(res, defaultMachines);
  }

  return sendSuccess(res, data);
}))

/**
 * @route   GET /api/disponibilite-trend
 * @desc    Get availability trend data by date and machine
 * @access  Public
 */
router.get("/disponibilite-trend", cors(corsOptions), asyncHandler(async (req, res) => {
  const { startDate, endDate, model: machineModel, machine: machineName } = req.query;
  let query = `
    SELECT
      DATE_FORMAT(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'), '%Y-%m-%d') AS date,
      Machine_Name,
      ROUND(AVG(CAST(Availability_Rate_Day AS DECIMAL(10,2))), 2) AS disponibilite
    FROM machine_daily_table_mould
    WHERE 1=1`;

  const queryParams = [];

  // Add date range filter
  if (startDate && endDate) {
    query += ` AND DATE(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) BETWEEN ? AND ?`;
    queryParams.push(startDate, endDate);
  }

  // Add machine filter
  if (machineModel) {
    query += ` AND Machine_Name LIKE ?`;
    queryParams.push(`${machineModel}%`);
  } else if (machineName) {
    query += ` AND Machine_Name = ?`;
    queryParams.push(machineName);
  }

  query += ` GROUP BY date, Machine_Name ORDER BY date ASC, Machine_Name`;

  // Execute query
  const { success, data: results, error } = await executeQuery(query, queryParams);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  // Transform data for frontend
  const dateMap = new Map();

  results.forEach((row) => {
    if (!dateMap.has(row.date)) {
      dateMap.set(row.date, {
        date: row.date,
        disponibilite: row.disponibilite,
      });
    }
    dateMap.get(row.date)[`disponibilite_${row.Machine_Name}`] = row.disponibilite;
  });
  const transformedData = Array.from(dateMap.values());

  // Apply normalization to ensure all values are decimals
  const normalizedData = normalizeAvailabilityMetrics(transformedData);

  return sendSuccess(res, normalizedData);
}))

/**
 * @route   GET /api/downtime-pareto
 * @desc    Get downtime pareto chart data
 * @access  Public
 */
router.get("/downtime-pareto", cors(corsOptions), asyncHandler(async (req, res) => {
  const { startDate, endDate, model: machineModel, machine: machineName } = req.query;

  let query = `
    SELECT
      Code_Stop AS reason,
      SUM(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i'),
        STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i')
      )) AS duration,
      COUNT(*) AS count
    FROM machine_stop_table_mould
    WHERE 1=1`;

  const queryParams = [];

  // Add date filter
  if (startDate && endDate) {
    query += ` AND DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y')) BETWEEN ? AND ?`;
    queryParams.push(startDate, endDate);
  } else {
    // Default to last 30 days if no date range provided
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const defaultDate = thirtyDaysAgo.toISOString().split('T')[0];
    query += ` AND STR_TO_DATE(Date_Insert, '%d/%m/%Y') >= ?`;
    queryParams.push(defaultDate);
  }

  // Add machine filter
  if (machineModel) {
    query += ` AND Machine_Name LIKE ?`;
    queryParams.push(`${machineModel}%`);
  } else if (machineName) {
    query += ` AND Machine_Name = ?`;
    queryParams.push(machineName);
  }

  query += ` GROUP BY reason ORDER BY duration DESC`;

  // Execute query
  const { success, data, error } = await executeQuery(query, queryParams);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  return sendSuccess(res, data);
}))

/**
 * @route   GET /api/disponibilite-by-machine
 * @desc    Get availability metrics grouped by machine
 * @access  Public
 */
router.get("/disponibilite-by-machine", cors(corsOptions), asyncHandler(async (req, res) => {
  const { startDate, endDate, model, machine } = req.query;

  let query = `
    SELECT
      mdt.Machine_Name AS machine,      SUBSTRING(mdt.Machine_Name, 1,
        CASE
          WHEN mdt.Machine_Name REGEXP '^[A-Za-z]+[0-9]'
          THEN LENGTH(SUBSTRING_INDEX(mdt.Machine_Name, REGEXP_SUBSTR(mdt.Machine_Name, '[0-9].*$'), 1))
          ELSE LENGTH(mdt.Machine_Name)
        END
      ) AS model,
      ROUND(AVG(CAST(Availability_Rate_Day AS DECIMAL(10,2))), 2) AS disponibilite,
      COUNT(mst.Machine_Name) AS stops,
      ROUND(
        AVG(
          CASE
            WHEN mst.Machine_Name IS NOT NULL
            THEN TIMESTAMPDIFF(
              MINUTE,
              STR_TO_DATE(mst.Debut_Stop, '%d/%m/%Y %H:%i'),
              STR_TO_DATE(mst.Fin_Stop_Time, '%d/%m/%Y %H:%i')
            )
            ELSE 0
          END
        ), 2
      ) AS mttr,
      ROUND(
        (SUM(CAST(mdt.Run_Hours_Day AS DECIMAL(10,2))) * 60)
        / NULLIF(COUNT(mst.Machine_Name), 0),
        2
      ) AS mtbf
    FROM machine_daily_table_mould mdt
    LEFT JOIN machine_stop_table_mould mst
      ON mdt.Machine_Name = mst.Machine_Name
      AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(mst.Date_Insert, '%d/%m/%Y')
    WHERE 1=1`;

  const params = [];

  // Add date filter
  if (startDate && endDate) {
    query += ` AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') BETWEEN ? AND ?`;
    params.push(startDate, endDate);
  } else {
    query += ` AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)`;
  }

  // Add machine filter
  if (model) {
    query += ` AND mdt.Machine_Name LIKE ?`;
    params.push(`${model}%`);
  } else if (machine) {
    query += ` AND mdt.Machine_Name = ?`;
    params.push(machine);
  }

  query += ` GROUP BY mdt.Machine_Name ORDER BY disponibilite DESC`;
  // Execute query
  const { success, data, error } = await executeQuery(query, params);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  // Apply normalization to ensure all values are decimals
  const normalizedData = normalizeAvailabilityMetrics(data);

  return sendSuccess(res, normalizedData);
}))

/**
 * @route   GET /api/mttr-calendar
 * @desc    Get MTTR (Mean Time To Repair) data by date
 * @access  Public
 */
router.get("/mttr-calendar", cors(corsOptions), asyncHandler(async (req, res) => {
  const { startDate, endDate, model: machineModel, machine: machineName } = req.query;

  let query = `
    SELECT
      DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y'), '%Y-%m-%d') AS date,
      ROUND(AVG(TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i'),
        STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i')
      )), 2) AS mttr
    FROM machine_stop_table_mould
    WHERE 1=1`;

  const queryParams = [];

  // Add date filter
  if (startDate && endDate) {
    query += ` AND STR_TO_DATE(Date_Insert, '%d/%m/%Y') BETWEEN ? AND ?`;
    queryParams.push(startDate, endDate);
  } else {
    // Default to current month if no date range provided
    const today = new Date();
    query += ` AND YEAR(STR_TO_DATE(Date_Insert, '%d/%m/%Y')) = ?
               AND MONTH(STR_TO_DATE(Date_Insert, '%d/%m/%Y')) = ?`;
    queryParams.push(today.getFullYear(), today.getMonth() + 1);
  }

  // Add machine filter
  if (machineModel) {
    query += ` AND Machine_Name LIKE ?`;
    queryParams.push(`${machineModel}%`);
  } else if (machineName) {
    query += ` AND Machine_Name = ?`;
    queryParams.push(machineName);
  }

  query += ` GROUP BY date ORDER BY date`;

  // Execute query
  const { success, data, error } = await executeQuery(query, queryParams);

  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  return sendSuccess(res, data);
}))

/**
 * @route   GET /api/performance-metrics
 * @desc    Get performance metrics by machine
 * @access  Public
 */
router.get("/performance-metrics", cors(corsOptions), asyncHandler(async (req, res) => {
  const { date, dateRangeType = "day", model, machine } = req.query; 
  
  let query = `  SELECT
    mdt.Machine_Name AS machine,
    ROUND(AVG(CAST(mdt.Availability_Rate_Day AS DECIMAL(10,2))), 2) AS disponibilite,
    ROUND(AVG(
      TIMESTAMPDIFF(MINUTE,
        STR_TO_DATE(mst.Debut_Stop, '%d/%m/%Y %H:%i'),
        STR_TO_DATE(mst.Fin_Stop_Time, '%d/%m/%Y %H:%i')
      )
    ), 2) AS mttr,
    ROUND(
      (SUM(CAST(mdt.Run_Hours_Day AS DECIMAL(10,2))) * 60) /
      NULLIF(COUNT(DISTINCT CONCAT(mst.Machine_Name, '_', mst.Date_Insert, '_', mst.Debut_Stop)), 0),
      2
    ) AS mtbf
  FROM machine_daily_table_mould mdt
  LEFT JOIN (
    SELECT DISTINCT
      Machine_Name,
      Date_Insert,
      Debut_Stop,
      Fin_Stop_Time
    FROM machine_stop_table_mould
    WHERE Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL
  ) mst
    ON mdt.Machine_Name = mst.Machine_Name
   AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(mst.Date_Insert, '%d/%m/%Y')
  WHERE 1=1`;

  const params = [];

  // Add date filter based on dateRangeType
  if (date) {
    // Use our date filter utility
    const dateFilter = buildDateFilter(date, dateRangeType, 'mdt.Date_Insert_Day');
    query += ` AND ${dateFilter.condition.replace('WHERE ', '')}`;
    params.push(...dateFilter.params);
  }

  // Add machine filter
  if (model) {
    query += ` AND mdt.Machine_Name LIKE ?`;
    params.push(`${model}%`);
  } else if (machine) {
    query += ` AND mdt.Machine_Name = ?`;
    params.push(machine);
  }
  
  query += ` GROUP BY mdt.Machine_Name`;
  console.log("🔍 Performance metrics query:", query);
  console.log("🔍 Performance metrics params:", params);
  // Execute query
  const { success, data, error } = await executeQuery(query, params);

  if (!success) {
    console.error('Performance metrics query failed:', error);
    return sendError(res, "Database query failed", 500, error);
  }

  // Apply normalization to ensure all values are decimals
  const normalizedData = normalizeAvailabilityMetrics(data);

  return sendSuccess(res, normalizedData);
}))


/**
 * @route   GET /api/machine-daily-mould
 * @desc    Get daily machine data with filtering options
 * @access  Public
 */
router.get("/machine-daily-mould", cors(corsOptions), asyncHandler(async (req, res) => {
  const { date: dateParam, dateRangeType = "day", model: machineModel, machine: machineName } = req.query;

  // Check if table exists
  try {
    const tableExistsResult = await executeQuery("SHOW TABLES LIKE 'machine_daily_table_mould'");

    if (!tableExistsResult.success || tableExistsResult.data.length === 0) {
      return sendError(res, "Table not found", 404, "machine_daily_table_mould table does not exist");
    }

    // Check if table has data
    const countResult = await executeQuery("SELECT COUNT(*) as count FROM machine_daily_table_mould");

    if (!countResult.success || countResult.data[0].count === 0) {
      return sendSuccess(res, []);
    }
  } catch (error) {
    return sendError(res, "Database check failed", 500, error);
  }

  // Build query
  let query = `
    SELECT
      Machine_Name,
      Date_Insert_Day,
      Shift,
      Good_QTY_Day,
      Rejects_QTY_Day,
      OEE_Day,
      Speed_Day,
      Availability_Rate_Day,
      Performance_Rate_Day,
      Quality_Rate_Day,
      Run_Hours_Day,
      Part_Number
    FROM machine_daily_table_mould
    WHERE 1=1`;

  const queryParams = [];

  // Add date filter
  if (dateParam) {
    // Use our date filter utility
    const dateFilter = buildDateFilter(dateParam, dateRangeType);
    query += dateFilter.condition;
    queryParams.push(...dateFilter.params);
  }

  // Add machine filter
  if (machineModel) {
    query += ` AND Machine_Name LIKE ?`;
    queryParams.push(`${machineModel}%`);
  } else if (machineName) {
    query += ` AND Machine_Name = ?`;
    queryParams.push(machineName);
  }

  query += ` ORDER BY STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') DESC, Machine_Name`;

  // Execute query
  const { success, data, error } = await executeQuery(query, queryParams);
  if (!success) {
    return sendError(res, "Database query failed", 500, error);
  }

  // Normalize the availability metrics to always be decimal values
  const normalizedData = normalizeAvailabilityMetrics(data);

  return sendSuccess(res, normalizedData);
}))

export default router;