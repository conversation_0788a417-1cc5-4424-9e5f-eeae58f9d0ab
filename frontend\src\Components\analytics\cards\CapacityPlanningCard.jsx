import React, { useState } from 'react';
import { Card, Row, Col, Statistic, Select, Space, Button, Tooltip, Progress, Empty } from 'antd';
import { 
  BarChartOutlined,
  LineChartOutlined,
  ThunderboltOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined,
  SettingOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Option } = Select;

const CapacityPlanningCard = ({ loading, filters }) => {
  const [viewMode, setViewMode] = useState('overview');
  const [selectedMachine, setSelectedMachine] = useState('all');

  // Mock capacity planning data
  const capacityData = {
    currentUtilization: 78.5,
    plannedCapacity: 92.0,
    forecastAccuracy: 94.2,
    bottleneckRisk: 23,
    optimizationPotential: 15.8,
    machines: [
      {
        id: 'M001',
        name: 'Machine 001',
        currentLoad: 85.2,
        maxCapacity: 100,
        forecastLoad: 91.5,
        efficiency: 87.3,
        status: 'optimal'
      },
      {
        id: 'M002', 
        name: 'Machine 002',
        currentLoad: 72.1,
        maxCapacity: 100,
        forecastLoad: 88.9,
        efficiency: 91.8,
        status: 'good'
      },
      {
        id: 'M003',
        name: 'Machine 003',
        currentLoad: 94.7,
        maxCapacity: 100,
        forecastLoad: 98.2,
        efficiency: 76.4,
        status: 'attention'
      }
    ],
    weeklyForecast: [
      { week: 'Week 1', demand: 85, capacity: 95, utilization: 89.5 },
      { week: 'Week 2', demand: 92, capacity: 95, utilization: 96.8 },
      { week: 'Week 3', demand: 88, capacity: 95, utilization: 92.6 },
      { week: 'Week 4', demand: 96, capacity: 95, utilization: 101.1 }
    ]
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'optimal': return '#52c41a';
      case 'good': return '#1890ff';
      case 'attention': return '#fa8c16';
      case 'critical': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  if (loading) {
    return (
      <div style={{ 
        height: '300px', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }}>
        <Empty description="Loading Capacity AI..." />
      </div>
    );
  }

  return (
    <div style={{ minHeight: '400px' }}>
      {/* Control Panel */}
      <div style={{
        background: 'linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)',
        borderRadius: '8px',
        padding: '12px',
        marginBottom: '16px'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <BarChartOutlined style={{ color: '#722ed1' }} />
              <span style={{ fontWeight: '500', color: '#595959' }}>
                AI Capacity Planner v3.2
              </span>
              <div style={{
                background: '#52c41a',
                color: 'white',
                padding: '2px 8px',
                borderRadius: '10px',
                fontSize: '10px',
                fontWeight: '600'
              }}>
                PREDICTIVE
              </div>
            </Space>
          </Col>
          <Col>
            <Space size="small">
              <Select 
                value={viewMode}
                onChange={setViewMode}
                size="small"
                style={{ width: 120, borderRadius: '6px' }}
              >
                <Option value="overview">Overview</Option>
                <Option value="machines">Machines</Option>
                <Option value="forecast">Forecast</Option>
              </Select>
              <Button 
                size="small" 
                icon={<ReloadOutlined />}
                style={{ borderRadius: '6px' }}
              >
                Refresh
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Overview Mode */}
      {viewMode === 'overview' && (
        <div>
          <Row gutter={[16, 16]} style={{ marginBottom: '20px' }}>
            <Col span={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Current Utilization"
                  value={capacityData.currentUtilization}
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#722ed1', fontSize: '18px', fontWeight: '700' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Planned Capacity"
                  value={capacityData.plannedCapacity}
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#1890ff', fontSize: '18px', fontWeight: '700' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Forecast Accuracy"
                  value={capacityData.forecastAccuracy}
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#52c41a', fontSize: '18px', fontWeight: '700' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small" style={{ textAlign: 'center', borderRadius: '8px' }}>
                <Statistic
                  title="Optimization Potential"
                  value={capacityData.optimizationPotential}
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#fa8c16', fontSize: '18px', fontWeight: '700' }}
                />
              </Card>
            </Col>
          </Row>

          {/* AI Insights */}
          <Card
            title="AI Capacity Insights"
            size="small"
            style={{
              borderRadius: '10px',
              background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)'
            }}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{
                background: 'white',
                borderRadius: '6px',
                padding: '12px',
                border: '2px solid #52c41a20'
              }}>
                <Space>
                  <RiseOutlined style={{ color: '#52c41a' }} />
                  <span style={{ fontWeight: '500', color: '#52c41a' }}>Opportunity:</span>
                  <span>Machine M002 can handle 16% more load during peak hours</span>
                </Space>
              </div>
              
              <div style={{
                background: 'white',
                borderRadius: '6px',
                padding: '12px',
                border: '2px solid #fa8c1620'
              }}>
                <Space>
                  <EyeOutlined style={{ color: '#fa8c16' }} />
                  <span style={{ fontWeight: '500', color: '#fa8c16' }}>Alert:</span>
                  <span>Week 4 shows 101% utilization - consider capacity adjustment</span>
                </Space>
              </div>
              
              <div style={{
                background: 'white',
                borderRadius: '6px',
                padding: '12px',
                border: '2px solid #722ed120'
              }}>
                <Space>
                  <ThunderboltOutlined style={{ color: '#722ed1' }} />
                  <span style={{ fontWeight: '500', color: '#722ed1' }}>Recommendation:</span>
                  <span>Redistribute 8% load from M003 to M002 for optimal balance</span>
                </Space>
              </div>
            </Space>
          </Card>
        </div>
      )}

      {/* Machines Mode */}
      {viewMode === 'machines' && (
        <div>
          <h4 style={{ marginBottom: '16px', color: '#722ed1' }}>
            Machine Capacity Analysis
          </h4>
          <Row gutter={[16, 16]}>
            {capacityData.machines.map((machine) => (
              <Col xs={24} md={8} key={machine.id}>
                <Card
                  title={machine.name}
                  size="small"
                  style={{
                    borderRadius: '12px',
                    border: `2px solid ${getStatusColor(machine.status)}20`,
                    background: `linear-gradient(135deg, ${getStatusColor(machine.status)}05 0%, ${getStatusColor(machine.status)}10 100%)`
                  }}
                  extra={
                    <div style={{
                      background: getStatusColor(machine.status),
                      color: 'white',
                      padding: '2px 8px',
                      borderRadius: '10px',
                      fontSize: '10px',
                      fontWeight: '600',
                      textTransform: 'uppercase'
                    }}>
                      {machine.status}
                    </div>
                  }
                >
                  <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <div>
                      <div style={{ 
                        fontSize: '11px', 
                        color: '#8c8c8c',
                        marginBottom: '4px'
                      }}>
                        Current Load
                      </div>
                      <Progress
                        percent={machine.currentLoad}
                        strokeColor={getStatusColor(machine.status)}
                        showInfo={true}
                        format={percent => `${percent}%`}
                      />
                    </div>
                    
                    <div>
                      <div style={{ 
                        fontSize: '11px', 
                        color: '#8c8c8c',
                        marginBottom: '4px'
                      }}>
                        Forecast Load
                      </div>
                      <Progress
                        percent={machine.forecastLoad}
                        strokeColor="#1890ff"
                        showInfo={true}
                        format={percent => `${percent}%`}
                      />
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <div>
                        <div style={{ fontSize: '11px', color: '#8c8c8c' }}>Efficiency</div>
                        <div style={{ 
                          fontSize: '14px', 
                          fontWeight: '600',
                          color: machine.efficiency > 85 ? '#52c41a' : '#fa8c16'
                        }}>
                          {machine.efficiency}%
                        </div>
                      </div>
                      <Button size="small" type="primary" style={{ borderRadius: '6px' }}>
                        Optimize
                      </Button>
                    </div>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      )}

      {/* Forecast Mode */}
      {viewMode === 'forecast' && (
        <div>
          <h4 style={{ marginBottom: '16px', color: '#722ed1' }}>
            Weekly Capacity Forecast
          </h4>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {capacityData.weeklyForecast.map((week, index) => (
              <Card
                key={index}
                size="small"
                style={{
                  borderRadius: '10px',
                  border: week.utilization > 100 ? '2px solid #f5222d20' : '2px solid #52c41a20',
                  background: week.utilization > 100 ? 
                    'linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)' : 
                    'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)'
                }}
              >
                <Row gutter={16} align="middle">
                  <Col flex="auto">
                    <Space size="large">
                      <div>
                        <div style={{ fontWeight: '600', fontSize: '14px' }}>
                          {week.week}
                        </div>
                        <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                          Demand: {week.demand}% | Capacity: {week.capacity}%
                        </div>
                      </div>
                    </Space>
                  </Col>
                  
                  <Col>
                    <div style={{ width: '200px' }}>
                      <div style={{ 
                        fontSize: '11px', 
                        color: '#8c8c8c',
                        marginBottom: '4px'
                      }}>
                        Utilization: {week.utilization}%
                      </div>
                      <Progress
                        percent={Math.min(week.utilization, 100)}
                        strokeColor={week.utilization > 100 ? '#f5222d' : 
                                    week.utilization > 95 ? '#fa8c16' : '#52c41a'}
                        showInfo={false}
                      />
                    </div>
                  </Col>
                  
                  <Col>
                    {week.utilization > 100 ? (
                      <FallOutlined style={{ color: '#f5222d', fontSize: '16px' }} />
                    ) : week.utilization > 95 ? (
                      <EyeOutlined style={{ color: '#fa8c16', fontSize: '16px' }} />
                    ) : (
                      <RiseOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
                    )}
                  </Col>
                </Row>
              </Card>
            ))}
          </Space>
        </div>
      )}
    </div>
  );
};

export default CapacityPlanningCard;
