/**
 * Redis Initialization Service for Manufacturing Intelligence Platform
 * Handles Redis connection setup, health monitoring, and graceful shutdown
 */

import redisService from './RedisService.js';
import redisEnhancedResolvers from './RedisEnhancedResolvers.js';

class RedisInitializer {
  constructor() {
    this.initialized = false;
    this.healthCheckInterval = null;
    this.performanceReportInterval = null;
  }

  /**
   * Initialize Redis infrastructure
   */
  async initialize() {
    if (this.initialized) {
      console.log('✅ Redis already initialized');
      return true;
    }

    try {
      console.log('🚀 Initializing Redis infrastructure...');

      // Initialize Redis service
      const redisInitialized = await redisService.initialize();
      if (!redisInitialized) {
        console.warn('⚠️ Redis service initialization failed - running without cache');
        return false;
      }

      // Initialize enhanced resolvers
      const resolversInitialized = await redisEnhancedResolvers.initialize();
      if (!resolversInitialized) {
        console.warn('⚠️ Redis enhanced resolvers initialization failed');
        return false;
      }

      // Start health monitoring
      this.startHealthMonitoring();

      // Start performance reporting
      this.startPerformanceReporting();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

      this.initialized = true;
      console.log('✅ Redis infrastructure initialized successfully');

      // Warm up cache with common queries
      await this.warmUpCache();

      return true;

    } catch (error) {
      console.error('❌ Redis initialization failed:', error);
      return false;
    }
  }

  /**
   * Start health monitoring
   */
  startHealthMonitoring() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const isHealthy = await redisService.healthCheck();
        if (!isHealthy) {
          console.warn('⚠️ Redis health check failed - attempting reconnection');
          await redisService.reconnect();
        }
      } catch (error) {
        console.error('❌ Health check error:', error);
      }
    }, 30000); // Check every 30 seconds

    console.log('📊 Redis health monitoring started');
  }

  /**
   * Start performance reporting
   */
  startPerformanceReporting() {
    this.performanceReportInterval = setInterval(() => {
      try {
        const redisMetrics = redisService.getMetrics();
        const resolverMetrics = redisEnhancedResolvers.getPerformanceMetrics();

        console.log('📈 Redis Performance Report:', {
          redis: {
            cacheHits: redisMetrics.cacheHits,
            cacheMisses: redisMetrics.cacheMisses,
            hitRate: `${((redisMetrics.cacheHits / (redisMetrics.cacheHits + redisMetrics.cacheMisses)) * 100).toFixed(2)}%`,
            avgResponseTime: `${redisMetrics.avgResponseTime}ms`,
            totalQueries: redisMetrics.totalQueries
          },
          resolvers: {
            totalQueries: resolverMetrics.totalQueries,
            hitRate: `${resolverMetrics.hitRate}%`,
            avgCacheTime: `${resolverMetrics.avgCacheResponseTime.toFixed(2)}ms`,
            avgDbTime: `${resolverMetrics.avgDbResponseTime.toFixed(2)}ms`,
            efficiency: `${resolverMetrics.cacheEfficiency}x faster`
          }
        });

        // Alert if performance degrades
        if (resolverMetrics.hitRate < 60) {
          console.warn('⚠️ Cache hit rate below 60% - consider cache optimization');
        }

        if (resolverMetrics.avgCacheResponseTime > 100) {
          console.warn('⚠️ Cache response time above 100ms - investigate Redis performance');
        }

      } catch (error) {
        console.error('❌ Performance reporting error:', error);
      }
    }, 300000); // Report every 5 minutes

    console.log('📊 Redis performance reporting started');
  }

  /**
   * Warm up cache with common queries
   */
  async warmUpCache() {
    console.log('🔥 Starting Redis cache warm-up...');

    const commonFilters = [
      {}, // Default filters
      { dateRangeType: 'day' },
      { dateRangeType: 'week' },
      { dateRangeType: 'month' },
      { model: 'IPS' },
      { machine: 'IPS01' }
    ];

    try {
      // Import resolvers dynamically to avoid circular dependencies
      const { dailyTableQueries } = await import('../routes/graphql/dailyTableResolvers.js');

      const warmUpResolvers = {
        getProductionChart: dailyTableQueries.getProductionChart,
        getProductionSidecards: dailyTableQueries.getProductionSidecards,
        getMachinePerformance: dailyTableQueries.getMachinePerformance
      };

      await redisEnhancedResolvers.warmUpCache(warmUpResolvers, commonFilters);
      console.log('🔥 Cache warm-up completed');

    } catch (error) {
      console.warn('⚠️ Cache warm-up failed:', error);
    }
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      console.log(`🛑 Received ${signal} - shutting down Redis gracefully...`);

      // Clear intervals
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }
      if (this.performanceReportInterval) {
        clearInterval(this.performanceReportInterval);
      }

      // Close Redis connections
      try {
        await redisService.disconnect();
        console.log('✅ Redis connections closed gracefully');
      } catch (error) {
        console.error('❌ Error closing Redis connections:', error);
      }

      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon

    console.log('🛡️ Graceful shutdown handlers registered');
  }

  /**
   * Get initialization status
   */
  isInitialized() {
    return this.initialized;
  }

  /**
   * Get comprehensive status
   */
  async getStatus() {
    if (!this.initialized) {
      return {
        initialized: false,
        redis: { connected: false },
        resolvers: { initialized: false }
      };
    }

    try {
      const redisHealthy = await redisService.healthCheck();
      const redisMetrics = redisService.getMetrics();
      const resolverMetrics = redisEnhancedResolvers.getPerformanceMetrics();

      return {
        initialized: true,
        redis: {
          connected: redisHealthy,
          metrics: redisMetrics
        },
        resolvers: {
          initialized: true,
          metrics: resolverMetrics
        },
        performance: {
          overallHitRate: resolverMetrics.hitRate,
          avgResponseTime: resolverMetrics.avgCacheResponseTime,
          efficiency: resolverMetrics.cacheEfficiency
        }
      };
    } catch (error) {
      console.error('❌ Error getting Redis status:', error);
      return {
        initialized: true,
        error: error.message
      };
    }
  }

  /**
   * Force cache invalidation for maintenance
   */
  async invalidateAllCache() {
    try {
      const patterns = [
        'manufacturing:*',
        'resolver:*',
        'dashboard:*'
      ];

      let totalInvalidated = 0;
      for (const pattern of patterns) {
        const count = await redisService.invalidateCache(pattern);
        totalInvalidated += count;
      }

      console.log(`🗑️ Invalidated ${totalInvalidated} cache entries`);
      return totalInvalidated;

    } catch (error) {
      console.error('❌ Cache invalidation failed:', error);
      throw error;
    }
  }

  /**
   * Restart Redis connections
   */
  async restart() {
    console.log('🔄 Restarting Redis infrastructure...');

    try {
      // Disconnect existing connections
      await redisService.disconnect();

      // Clear intervals
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }
      if (this.performanceReportInterval) {
        clearInterval(this.performanceReportInterval);
      }

      // Reset initialization state
      this.initialized = false;

      // Reinitialize
      return await this.initialize();

    } catch (error) {
      console.error('❌ Redis restart failed:', error);
      return false;
    }
  }
}

// Create singleton instance
const redisInitializer = new RedisInitializer();

export default redisInitializer;
