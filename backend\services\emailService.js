import nodemailer from 'nodemailer';
import { executeQuery } from '../utils/dbUtils.js';
import { logger } from '../utils/logger.js';

/**
 * Email Service for SOMIPEM Application
 * Handles all email notifications with settings integration
 */
class EmailService {
  constructor() {
    this.transporter = null;
    this.isConfigured = false;
    this.emailQueue = [];
    this.batchQueue = [];
    this.dailyEmailCount = 0;
    this.lastResetDate = new Date().toDateString();
    
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter based on environment
   */
  async initializeTransporter() {
    try {
      // For development, use Ethereal Email (test email service)
      if (process.env.NODE_ENV === 'development') {
        const testAccount = await nodemailer.createTestAccount();
        
        this.transporter = nodemailer.createTransporter({
          host: 'smtp.ethereal.email',
          port: 587,
          secure: false,
          auth: {
            user: testAccount.user,
            pass: testAccount.pass,
          },
        });
        
        logger.info('Email service initialized with Ethereal Email for development');
      } else {
        // Production configuration (to be configured with actual SMTP settings)
        this.transporter = nodemailer.createTransporter({
          host: process.env.SMTP_HOST || 'localhost',
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        });
        
        logger.info('Email service initialized with production SMTP settings');
      }
      
      // Verify transporter configuration
      await this.transporter.verify();
      this.isConfigured = true;
      logger.info('Email transporter verified successfully');
      
    } catch (error) {
      logger.error('Failed to initialize email transporter:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Get user email settings from database
   */
  async getUserEmailSettings(userId = 1) {
    try {
      const query = `
        SELECT settings 
        FROM user_settings 
        WHERE user_id = ? 
        LIMIT 1
      `;
      
      const result = await executeQuery(query, [userId]);
      
      if (result.length > 0) {
        const settings = JSON.parse(result[0].settings);
        return settings.email || {};
      }
      
      // Return default email settings if none found
      return this.getDefaultEmailSettings();
      
    } catch (error) {
      logger.error('Failed to get user email settings:', error);
      return this.getDefaultEmailSettings();
    }
  }

  /**
   * Get default email settings
   */
  getDefaultEmailSettings() {
    return {
      enabled: false,
      frequency: 'immediate',
      template: 'standard',
      format: 'html',
      language: 'fr',
      attachments: { enabled: false },
      signature: { 
        enabled: false, 
        text: 'SOMIPEM Production System' 
      },
      filtering: {
        minPriority: 'medium',
        maxPerDay: 50,
        preventDuplicates: true,
        smartGrouping: false
      },
      delivery: {
        method: 'immediate',
        retryFailed: true,
        readReceipts: false,
        tracking: false
      },
      batchSettings: {
        hourlyBatch: { enabled: false, maxNotifications: 20 },
        dailyDigest: { enabled: false, digestTime: '08:00' }
      }
    };
  }

  /**
   * Send email notification
   */
  async sendNotification(notification, userId = 1) {
    try {
      if (!this.isConfigured) {
        logger.warn('Email service not configured, skipping notification');
        return { success: false, reason: 'Email service not configured' };
      }

      const emailSettings = await this.getUserEmailSettings(userId);
      
      // Check if email notifications are enabled
      if (!emailSettings.enabled) {
        return { success: false, reason: 'Email notifications disabled' };
      }

      // Check priority filtering
      if (!this.shouldSendBasedOnPriority(notification.priority, emailSettings.filtering.minPriority)) {
        return { success: false, reason: 'Priority below threshold' };
      }

      // Check daily limit
      if (!this.checkDailyLimit(emailSettings.filtering.maxPerDay)) {
        return { success: false, reason: 'Daily email limit reached' };
      }

      // Check for duplicates
      if (emailSettings.filtering.preventDuplicates && this.isDuplicate(notification)) {
        return { success: false, reason: 'Duplicate notification prevented' };
      }

      // Handle delivery method
      switch (emailSettings.delivery.method) {
        case 'immediate':
          return await this.sendImmediateEmail(notification, emailSettings);
        case 'scheduled':
          return this.scheduleEmail(notification, emailSettings);
        case 'batch':
          return this.addToBatch(notification, emailSettings);
        default:
          return await this.sendImmediateEmail(notification, emailSettings);
      }

    } catch (error) {
      logger.error('Failed to send email notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send immediate email
   */
  async sendImmediateEmail(notification, emailSettings) {
    try {
      const emailContent = this.generateEmailContent(notification, emailSettings);
      const mailOptions = this.buildMailOptions(emailContent, emailSettings);

      const result = await this.transporter.sendMail(mailOptions);
      
      // Increment daily count
      this.incrementDailyCount();
      
      // Log email for development
      if (process.env.NODE_ENV === 'development') {
        logger.info('Email sent successfully:', {
          messageId: result.messageId,
          previewUrl: nodemailer.getTestMessageUrl(result)
        });
      }

      // Store email record
      await this.storeEmailRecord(notification, emailSettings, result);

      return { 
        success: true, 
        messageId: result.messageId,
        previewUrl: process.env.NODE_ENV === 'development' ? nodemailer.getTestMessageUrl(result) : null
      };

    } catch (error) {
      logger.error('Failed to send immediate email:', error);
      
      // Retry if enabled
      if (emailSettings.delivery.retryFailed) {
        return this.retryEmail(notification, emailSettings);
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate email content based on template and settings
   */
  generateEmailContent(notification, emailSettings) {
    const template = emailSettings.template || 'standard';
    const language = emailSettings.language || 'fr';
    
    let subject, body;
    
    // Generate subject based on language
    if (language === 'fr') {
      subject = `[SOMIPEM] ${this.getNotificationTypeLabel(notification.type, 'fr')}: ${notification.title}`;
    } else {
      subject = `[SOMIPEM] ${this.getNotificationTypeLabel(notification.type, 'en')}: ${notification.title}`;
    }

    // Generate body based on template
    switch (template) {
      case 'minimal':
        body = this.generateMinimalTemplate(notification, language);
        break;
      case 'detailed':
        body = this.generateDetailedTemplate(notification, language);
        break;
      default: // standard
        body = this.generateStandardTemplate(notification, language);
        break;
    }

    // Add signature if enabled
    if (emailSettings.signature.enabled) {
      body += this.generateSignature(emailSettings.signature.text, emailSettings.format);
    }

    return { subject, body };
  }

  /**
   * Generate standard email template
   */
  generateStandardTemplate(notification, language = 'fr') {
    const isHtml = true; // Will be determined by settings
    
    if (language === 'fr') {
      if (isHtml) {
        return `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #0a3d62; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0;">SOMIPEM</h1>
              <p style="margin: 5px 0 0 0;">Système de Production</p>
            </div>
            <div style="padding: 20px; background-color: #f8f9fa;">
              <h2 style="color: #0a3d62; margin-top: 0;">${notification.title}</h2>
              <p><strong>Type:</strong> ${this.getNotificationTypeLabel(notification.type, 'fr')}</p>
              <p><strong>Priorité:</strong> ${this.getPriorityLabel(notification.priority, 'fr')}</p>
              <p><strong>Date:</strong> ${new Date(notification.timestamp).toLocaleString('fr-FR')}</p>
              <div style="margin: 20px 0; padding: 15px; background-color: white; border-left: 4px solid #0a3d62;">
                <p style="margin: 0;">${notification.message}</p>
              </div>
              ${notification.data ? `<p><strong>Données:</strong> ${JSON.stringify(notification.data, null, 2)}</p>` : ''}
            </div>
            <div style="background-color: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
              <p style="margin: 0;">Cet email a été généré automatiquement par le système SOMIPEM.</p>
            </div>
          </div>
        `;
      } else {
        return `
SOMIPEM - Système de Production

${notification.title}

Type: ${this.getNotificationTypeLabel(notification.type, 'fr')}
Priorité: ${this.getPriorityLabel(notification.priority, 'fr')}
Date: ${new Date(notification.timestamp).toLocaleString('fr-FR')}

Message:
${notification.message}

${notification.data ? `Données: ${JSON.stringify(notification.data, null, 2)}` : ''}

---
Cet email a été généré automatiquement par le système SOMIPEM.
        `;
      }
    } else {
      // English version
      if (isHtml) {
        return `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #0a3d62; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0;">SOMIPEM</h1>
              <p style="margin: 5px 0 0 0;">Production System</p>
            </div>
            <div style="padding: 20px; background-color: #f8f9fa;">
              <h2 style="color: #0a3d62; margin-top: 0;">${notification.title}</h2>
              <p><strong>Type:</strong> ${this.getNotificationTypeLabel(notification.type, 'en')}</p>
              <p><strong>Priority:</strong> ${this.getPriorityLabel(notification.priority, 'en')}</p>
              <p><strong>Date:</strong> ${new Date(notification.timestamp).toLocaleString('en-US')}</p>
              <div style="margin: 20px 0; padding: 15px; background-color: white; border-left: 4px solid #0a3d62;">
                <p style="margin: 0;">${notification.message}</p>
              </div>
              ${notification.data ? `<p><strong>Data:</strong> ${JSON.stringify(notification.data, null, 2)}</p>` : ''}
            </div>
            <div style="background-color: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
              <p style="margin: 0;">This email was automatically generated by the SOMIPEM system.</p>
            </div>
          </div>
        `;
      } else {
        return `
SOMIPEM - Production System

${notification.title}

Type: ${this.getNotificationTypeLabel(notification.type, 'en')}
Priority: ${this.getPriorityLabel(notification.priority, 'en')}
Date: ${new Date(notification.timestamp).toLocaleString('en-US')}

Message:
${notification.message}

${notification.data ? `Data: ${JSON.stringify(notification.data, null, 2)}` : ''}

---
This email was automatically generated by the SOMIPEM system.
        `;
      }
    }
  }

  /**
   * Build mail options for nodemailer
   */
  buildMailOptions(emailContent, emailSettings) {
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: process.env.EMAIL_TO || '<EMAIL>', // In production, get from user settings
      subject: emailContent.subject,
      text: emailSettings.format === 'text' ? emailContent.body : undefined,
      html: emailSettings.format === 'html' ? emailContent.body : undefined
    };

    // Add read receipts if enabled
    if (emailSettings.delivery.readReceipts) {
      mailOptions.headers = {
        'Disposition-Notification-To': mailOptions.from
      };
    }

    return mailOptions;
  }

  /**
   * Helper methods for labels and formatting
   */
  getNotificationTypeLabel(type, language = 'fr') {
    const labels = {
      fr: {
        machine: 'Machine',
        production: 'Production',
        quality: 'Qualité',
        maintenance: 'Maintenance',
        alert: 'Alerte',
        info: 'Information',
        updates: 'Mise à jour'
      },
      en: {
        machine: 'Machine',
        production: 'Production',
        quality: 'Quality',
        maintenance: 'Maintenance',
        alert: 'Alert',
        info: 'Information',
        updates: 'Updates'
      }
    };
    
    return labels[language][type] || type;
  }

  getPriorityLabel(priority, language = 'fr') {
    const labels = {
      fr: {
        critical: 'Critique',
        high: 'Élevée',
        medium: 'Moyenne',
        low: 'Faible'
      },
      en: {
        critical: 'Critical',
        high: 'High',
        medium: 'Medium',
        low: 'Low'
      }
    };
    
    return labels[language][priority] || priority;
  }

  /**
   * Utility methods
   */
  shouldSendBasedOnPriority(notificationPriority, minPriority) {
    const priorities = ['low', 'medium', 'high', 'critical'];
    const notificationLevel = priorities.indexOf(notificationPriority);
    const minLevel = priorities.indexOf(minPriority);
    return notificationLevel >= minLevel;
  }

  checkDailyLimit(maxPerDay) {
    const today = new Date().toDateString();
    if (this.lastResetDate !== today) {
      this.dailyEmailCount = 0;
      this.lastResetDate = today;
    }
    return this.dailyEmailCount < maxPerDay;
  }

  incrementDailyCount() {
    this.dailyEmailCount++;
  }

  isDuplicate(notification) {
    // Simple duplicate check - in production, implement more sophisticated logic
    return false;
  }

  generateSignature(signatureText, format) {
    if (format === 'html') {
      return `
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
          <p style="margin: 0; font-size: 12px; color: #6c757d;">${signatureText}</p>
        </div>
      `;
    } else {
      return `\n\n---\n${signatureText}`;
    }
  }

  async storeEmailRecord(notification, emailSettings, result) {
    try {
      const query = `
        INSERT INTO email_logs (
          notification_id, message_id, recipient, subject, 
          status, sent_at, settings_snapshot
        ) VALUES (?, ?, ?, ?, ?, NOW(), ?)
      `;
      
      await executeQuery(query, [
        notification.id || null,
        result.messageId,
        process.env.EMAIL_TO || '<EMAIL>',
        notification.title,
        'sent',
        JSON.stringify(emailSettings)
      ]);
      
    } catch (error) {
      logger.error('Failed to store email record:', error);
    }
  }

  async retryEmail(notification, emailSettings) {
    // Implement retry logic
    logger.info('Email retry not implemented yet');
    return { success: false, reason: 'Retry not implemented' };
  }

  scheduleEmail(notification, emailSettings) {
    // Implement email scheduling
    logger.info('Email scheduling not implemented yet');
    return { success: false, reason: 'Scheduling not implemented' };
  }

  addToBatch(notification, emailSettings) {
    // Implement batch processing
    logger.info('Email batching not implemented yet');
    return { success: false, reason: 'Batching not implemented' };
  }

  generateMinimalTemplate(notification, language) {
    return notification.message;
  }

  generateDetailedTemplate(notification, language) {
    return this.generateStandardTemplate(notification, language) + `
      <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border: 1px solid #dee2e6;">
        <h4>Informations détaillées:</h4>
        <p><strong>ID Notification:</strong> ${notification.id}</p>
        <p><strong>Système:</strong> SOMIPEM v1.0</p>
        <p><strong>Serveur:</strong> ${process.env.NODE_ENV || 'development'}</p>
      </div>
    `;
  }
}

// Create singleton instance
const emailService = new EmailService();

export default emailService;
