# LOCQL Unified Container - Environment Variables Template
# Copy this file to .env and update the values for your environment

# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=development
PORT=5000
DEBUG=true
LOG_LEVEL=debug

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# For local development with Docker, use host.docker.internal
# For production, use your actual database host
DB_HOST=host.docker.internal
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=locql_db

# ==============================================
# AUTHENTICATION & SECURITY
# ==============================================
# IMPORTANT: Change this in production!
JWT_SECRET=locql_development_secret_key_change_in_production

# Security settings for production
SECURE_COOKIES=false
TRUST_PROXY=false

# ==============================================
# CORS CONFIGURATION
# ==============================================
# Comma-separated list of allowed origins
CORS_ORIGINS=http://localhost:5000,http://127.0.0.1:5000
