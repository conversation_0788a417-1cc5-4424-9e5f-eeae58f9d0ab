import React from 'react';
import { Card, Table, Tag, Space, Empty, Spin } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';

// Chart colors matching original
const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2", 
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
};

const ArretOperatorStats = () => {
  const context = useArretQueuedContext();
  
  if (!context) {
    return <div>Context not available</div>;
  }
  
  const { 
    operatorStats = [], 
    loading = false 
  } = context;

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!operatorStats || operatorStats.length === 0) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <Empty description="Aucune donnée d'opérateur disponible" />
      </div>
    );
  }

  const columns = [
    {
      title: "Opérateur",
      dataIndex: "operator",
      key: "operator",
      render: (text) => (
        <Space>
          <UserOutlined style={{ color: CHART_COLORS.purple }} />
          {text || "Non assigné"}
        </Space>
      ),
    },
    {
      title: "Interventions",
      dataIndex: "interventions",
      key: "interventions",
      render: (value) => <Tag color="purple">{value}</Tag>,
      sorter: (a, b) => a.interventions - b.interventions,
      defaultSortOrder: "descend",
    },
  ];

  return (
    <Table
      dataSource={operatorStats}
      columns={columns}
      pagination={false}
      size="middle"
      rowKey="operator"
      style={{ height: '300px', overflow: 'auto' }}
    />
  );
};

export default ArretOperatorStats;
