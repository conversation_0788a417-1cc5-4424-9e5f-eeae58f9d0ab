/**
 * 🔄 Elasticsearch Data Indexer
 * 
 * Phase 3: Automatic data indexing service
 * Populates Elasticsearch indices with existing MySQL data
 * Implements incremental indexing and data synchronization
 */

import { db } from '../server.js';
import elasticsearchProductionService from './ElasticsearchProductionService.js';
import elasticsearchStopsService from './ElasticsearchStopsService.js';

class ElasticsearchDataIndexer {
  constructor() {
    this.batchSize = 1000; // Process 1000 records at a time
    this.indexingInProgress = false;
  }

  /**
   * Initialize and perform full data indexing
   */
  async performFullIndexing() {
    if (this.indexingInProgress) {
      console.log('⚠️ Indexing already in progress, skipping...');
      return { success: false, message: 'Indexing already in progress' };
    }

    this.indexingInProgress = true;
    const startTime = Date.now();
    
    try {
      console.log('🚀 Starting full Elasticsearch data indexing...');
      
      // Index production data
      const productionResult = await this.indexProductionData();
      
      // Index stops data
      const stopsResult = await this.indexStopsData();
      
      const totalTime = Date.now() - startTime;
      
      const result = {
        success: true,
        duration_ms: totalTime,
        production: productionResult,
        stops: stopsResult,
        total_records: productionResult.indexed + stopsResult.indexed
      };
      
      console.log(`✅ Full indexing completed in ${totalTime}ms`);
      console.log(`📊 Total records indexed: ${result.total_records}`);
      
      return result;
      
    } catch (error) {
      console.error('❌ Full indexing failed:', error);
      return { success: false, message: error.message };
    } finally {
      this.indexingInProgress = false;
    }
  }

  /**
   * Index production data from machine_daily_table_mould
   */
  async indexProductionData() {
    try {
      console.log('📊 Indexing production data...');
      
      // Get total count for progress tracking
      const [countResult] = await db.execute('SELECT COUNT(*) as total FROM machine_daily_table_mould');
      const totalRecords = countResult[0].total;
      
      if (totalRecords === 0) {
        console.log('ℹ️ No production data to index');
        return { indexed: 0, total: 0 };
      }
      
      console.log(`📈 Found ${totalRecords} production records to index`);
      
      let offset = 0;
      let totalIndexed = 0;
      
      while (offset < totalRecords) {
        // Fetch batch of records
        const [rows] = await db.execute(`
          SELECT 
            id,
            machine_id,
            machine_name,
            date,
            shift,
            operator,
            good_production,
            rejects,
            target_production,
            production_rate,
            oee,
            availability,
            performance_rate,
            quality_rate,
            trs,
            run_hours,
            down_hours,
            speed,
            part_number,
            mould_number,
            unit_weight,
            theoretical_cycle,
            purge_weight,
            order_number,
            article,
            planned_quantity
          FROM machine_daily_table_mould 
          ORDER BY date DESC, id DESC
          LIMIT ? OFFSET ?
        `, [this.batchSize, offset]);
        
        if (rows.length === 0) break;
        
        // Index this batch
        const result = await elasticsearchProductionService.indexProductionData(rows);
        
        if (result.success) {
          totalIndexed += result.indexed;
          console.log(`✅ Indexed batch: ${result.indexed} records (${totalIndexed}/${totalRecords})`);
        } else {
          console.error(`❌ Failed to index batch at offset ${offset}:`, result.message);
        }
        
        offset += this.batchSize;
        
        // Small delay to prevent overwhelming Elasticsearch
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log(`✅ Production data indexing completed: ${totalIndexed}/${totalRecords} records`);
      return { indexed: totalIndexed, total: totalRecords };
      
    } catch (error) {
      console.error('❌ Error indexing production data:', error);
      throw error;
    }
  }

  /**
   * Index stops data from machine_stop_table_mould
   */
  async indexStopsData() {
    try {
      console.log('🛑 Indexing stops data...');
      
      // Get total count for progress tracking
      const [countResult] = await db.execute('SELECT COUNT(*) as total FROM machine_stop_table_mould');
      const totalRecords = countResult[0].total;
      
      if (totalRecords === 0) {
        console.log('ℹ️ No stops data to index');
        return { indexed: 0, total: 0 };
      }
      
      console.log(`🛑 Found ${totalRecords} stop records to index`);
      
      let offset = 0;
      let totalIndexed = 0;
      
      while (offset < totalRecords) {
        // Fetch batch of records
        const [rows] = await db.execute(`
          SELECT 
            id,
            machine_id,
            machine_name,
            stop_code,
            stop_description,
            stop_category,
            date_insert,
            start_time,
            end_time,
            operator,
            shift,
            part_number,
            order_number,
            resolved,
            resolved_by,
            resolved_at,
            resolution_notes,
            maintenance_required,
            maintenance_type,
            maintenance_priority
          FROM machine_stop_table_mould 
          ORDER BY date_insert DESC, id DESC
          LIMIT ? OFFSET ?
        `, [this.batchSize, offset]);
        
        if (rows.length === 0) break;
        
        // Index this batch
        const result = await elasticsearchStopsService.indexStopsData(rows);
        
        if (result.success) {
          totalIndexed += result.indexed;
          console.log(`✅ Indexed batch: ${result.indexed} records (${totalIndexed}/${totalRecords})`);
        } else {
          console.error(`❌ Failed to index batch at offset ${offset}:`, result.message);
        }
        
        offset += this.batchSize;
        
        // Small delay to prevent overwhelming Elasticsearch
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log(`✅ Stops data indexing completed: ${totalIndexed}/${totalRecords} records`);
      return { indexed: totalIndexed, total: totalRecords };
      
    } catch (error) {
      console.error('❌ Error indexing stops data:', error);
      throw error;
    }
  }

  /**
   * Incremental indexing - index only recent data
   * @param {number} hours - Number of hours back to index (default: 24)
   */
  async performIncrementalIndexing(hours = 24) {
    if (this.indexingInProgress) {
      console.log('⚠️ Indexing already in progress, skipping incremental indexing...');
      return { success: false, message: 'Indexing already in progress' };
    }

    this.indexingInProgress = true;
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Starting incremental indexing (last ${hours} hours)...`);
      
      const cutoffTime = new Date(Date.now() - (hours * 60 * 60 * 1000));
      const cutoffString = cutoffTime.toISOString().slice(0, 19).replace('T', ' ');
      
      // Index recent production data
      const productionResult = await this.indexRecentProductionData(cutoffString);
      
      // Index recent stops data
      const stopsResult = await this.indexRecentStopsData(cutoffString);
      
      const totalTime = Date.now() - startTime;
      
      const result = {
        success: true,
        duration_ms: totalTime,
        hours_back: hours,
        cutoff_time: cutoffString,
        production: productionResult,
        stops: stopsResult,
        total_records: productionResult.indexed + stopsResult.indexed
      };
      
      console.log(`✅ Incremental indexing completed in ${totalTime}ms`);
      console.log(`📊 Total recent records indexed: ${result.total_records}`);
      
      return result;
      
    } catch (error) {
      console.error('❌ Incremental indexing failed:', error);
      return { success: false, message: error.message };
    } finally {
      this.indexingInProgress = false;
    }
  }

  /**
   * Index recent production data
   */
  async indexRecentProductionData(cutoffTime) {
    try {
      const [rows] = await db.execute(`
        SELECT 
          id, machine_id, machine_name, date, shift, operator,
          good_production, rejects, target_production, production_rate,
          oee, availability, performance_rate, quality_rate, trs,
          run_hours, down_hours, speed, part_number, mould_number,
          unit_weight, theoretical_cycle, purge_weight, order_number,
          article, planned_quantity
        FROM machine_daily_table_mould 
        WHERE date >= ?
        ORDER BY date DESC, id DESC
      `, [cutoffTime]);
      
      if (rows.length === 0) {
        return { indexed: 0, total: 0 };
      }
      
      const result = await elasticsearchProductionService.indexProductionData(rows);
      console.log(`✅ Indexed ${result.indexed} recent production records`);
      
      return { indexed: result.indexed, total: rows.length };
      
    } catch (error) {
      console.error('❌ Error indexing recent production data:', error);
      throw error;
    }
  }

  /**
   * Index recent stops data
   */
  async indexRecentStopsData(cutoffTime) {
    try {
      const [rows] = await db.execute(`
        SELECT 
          id, machine_id, machine_name, stop_code, stop_description,
          stop_category, date_insert, start_time, end_time, operator,
          shift, part_number, order_number, resolved, resolved_by,
          resolved_at, resolution_notes, maintenance_required,
          maintenance_type, maintenance_priority
        FROM machine_stop_table_mould 
        WHERE date_insert >= ?
        ORDER BY date_insert DESC, id DESC
      `, [cutoffTime]);
      
      if (rows.length === 0) {
        return { indexed: 0, total: 0 };
      }
      
      const result = await elasticsearchStopsService.indexStopsData(rows);
      console.log(`✅ Indexed ${result.indexed} recent stop records`);
      
      return { indexed: result.indexed, total: rows.length };
      
    } catch (error) {
      console.error('❌ Error indexing recent stops data:', error);
      throw error;
    }
  }

  /**
   * Check if indexing is currently in progress
   */
  isIndexingInProgress() {
    return this.indexingInProgress;
  }

  /**
   * Get indexing status
   */
  getStatus() {
    return {
      indexing_in_progress: this.indexingInProgress,
      batch_size: this.batchSize,
      last_run: this.lastRunTime || null
    };
  }
}

export default new ElasticsearchDataIndexer();
