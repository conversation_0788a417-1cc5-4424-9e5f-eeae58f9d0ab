import{r as s,a2 as y,b0 as ce,u as ue,aI as o,R as e,T as me,d as g,ay as m,g as u,F as de,E as U,M as N,b,c as d,aa as x,aK as pe,al as k,ab as I,D as fe,n as c,f as Ee,B as ge,e as C,aJ as he,b1 as ve,b2 as we}from"./index-LbZyOyVE.js";import{R as be}from"./SearchOutlined-DzAh4Hfi.js";import{R as ye}from"./ReloadOutlined-CoxAQyfN.js";import{S as Re}from"./index-CgKoxNk1.js";import{R as xe}from"./CloseCircleOutlined-j8iUMKYt.js";import{R as Ie}from"./CheckCircleOutlined-Bxw2HLIH.js";import{R as F}from"./EyeOutlined-Bf51LvXG.js";import{A as Ce}from"./index-D5LEhvq6.js";var Ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M608 112c-167.9 0-304 136.1-304 304 0 70.3 23.9 135 63.9 186.5l-41.1 41.1-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-44.9 44.9-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-65.3 65.3a8.03 8.03 0 000 11.3l42.3 42.3c3.1 3.1 8.2 3.1 11.3 0l253.6-253.6A304.06 304.06 0 00608 720c167.9 0 304-136.1 304-304S775.9 112 608 112zm161.2 465.2C726.2 620.3 668.9 644 608 644c-60.9 0-118.2-23.7-161.2-66.8-43.1-43-66.8-100.3-66.8-161.2 0-60.9 23.7-118.2 66.8-161.2 43-43.1 100.3-66.8 161.2-66.8 60.9 0 118.2 23.7 161.2 66.8 43.1 43 66.8 100.3 66.8 161.2 0 60.9-23.7 118.2-66.8 161.2z"}}]},name:"key",theme:"outlined"},Se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"},Oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M678.3 642.4c24.2-13 51.9-20.4 81.4-20.4h.1c3 0 4.4-3.6 2.2-5.6a371.67 371.67 0 00-103.7-65.8c-.4-.2-.8-.3-1.2-.5C719.2 505 759.6 431.7 759.6 349c0-137-110.8-248-247.5-248S264.7 212 264.7 349c0 82.7 40.4 156 102.6 201.1-.4.2-.8.3-1.2.5-44.7 18.9-84.8 46-119.3 80.6a373.42 373.42 0 00-80.4 119.5A373.6 373.6 0 00137 888.8a8 8 0 008 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C357 628.2 432.2 597 512.2 597c56.7 0 111.1 15.7 158 45.1a8.1 8.1 0 008.1.3zM512.2 521c-45.8 0-88.9-17.9-121.4-50.4A171.2 171.2 0 01340.5 349c0-45.9 17.9-89.1 50.3-121.6S466.3 177 512.2 177s88.9 17.9 121.4 50.4A171.2 171.2 0 01683.9 349c0 45.9-17.9 89.1-50.3 121.6C601.1 503.1 558 521 512.2 521zM880 759h-84v-84c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v84h-84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h84v84c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-84h84c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"user-add",theme:"outlined"};function A(){return A=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(l[i]=n[i])}return l},A.apply(this,arguments)}const Pe=(l,r)=>s.createElement(y,A({},l,{ref:r,icon:ce})),L=s.forwardRef(Pe);function S(){return S=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(l[i]=n[i])}return l},S.apply(this,arguments)}const Me=(l,r)=>s.createElement(y,S({},l,{ref:r,icon:Ae})),$e=s.forwardRef(Me);function O(){return O=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(l[i]=n[i])}return l},O.apply(this,arguments)}const je=(l,r)=>s.createElement(y,O({},l,{ref:r,icon:Se})),Te=s.forwardRef(je);function P(){return P=Object.assign?Object.assign.bind():function(l){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(l[i]=n[i])}return l},P.apply(this,arguments)}const ze=(l,r)=>s.createElement(y,P({},l,{ref:r,icon:Oe})),Ue=s.forwardRef(ze),{Title:Ne,Text:V}=me,{Option:_}=k,Fe=({darkMode:l})=>{const{user:r,createUser:n,updateUser:i,deleteUser:B,getAllUsers:D,resetUserPassword:q}=ue(),[K,G]=s.useState([]),[Z,M]=s.useState(!1),[J,p]=s.useState(!1),[W,$]=s.useState("Ajouter un utilisateur"),[h,j]=s.useState(null),[R]=o.useForm(),[v,H]=s.useState(""),[Q,T]=s.useState(!1),[X,w]=s.useState(!1),[Y,ee]=s.useState(null),[z]=o.useForm(),f=async()=>{M(!0);try{const t=await D();t.success?G(t.data||[]):c.error("Erreur lors du chargement des utilisateurs")}catch(t){console.error("Erreur:",t),c.error("Erreur lors du chargement des utilisateurs")}finally{M(!1)}};s.useEffect(()=>{f()},[]);const te=K.filter(t=>{var a,E;return((a=t.username)==null?void 0:a.toLowerCase().includes(v.toLowerCase()))||((E=t.email)==null?void 0:E.toLowerCase().includes(v.toLowerCase()))||t.fullName&&t.fullName.toLowerCase().includes(v.toLowerCase())}),ae=()=>{$("Ajouter un utilisateur"),j(null),R.resetFields(),p(!0),T(!1)},re=t=>{$("Modifier l'utilisateur"),j(t),R.setFieldsValue({username:t.username,email:t.email,role:t.role,fullName:t.fullName||"",phone:t.phone||"",active:t.active}),p(!0)},le=async t=>{try{if(h){const a=await i(h.id,t);a.success?(c.success("Utilisateur mis à jour avec succès"),f(),p(!1)):c.error(a.message||"Erreur lors de la mise à jour de l'utilisateur")}else{const a=await n(t);a.success?(c.success("Utilisateur créé avec succès"),f(),p(!1)):c.error(a.message||"Erreur lors de la création de l'utilisateur")}}catch(a){console.error("Erreur:",a),c.error("Une erreur est survenue")}},se=async t=>{try{const a=await B(t);a.success?(c.success("Utilisateur supprimé avec succès"),f()):c.error(a.message||"Erreur lors de la suppression de l'utilisateur")}catch(a){console.error("Erreur:",a),c.error("Une erreur est survenue")}},ne=t=>{ee(t),z.resetFields(),w(!0)},ie=async t=>{try{const a=await q(Y.id,t.newPassword);a.success?(c.success("Mot de passe réinitialisé avec succès"),w(!1)):c.error(a.message||"Erreur lors de la réinitialisation du mot de passe")}catch(a){console.error("Erreur:",a),c.error("Une erreur est survenue")}},oe=[{title:"Utilisateur",key:"user",render:(t,a)=>e.createElement(g,null,e.createElement(Ce,{icon:e.createElement(x,null),style:{backgroundColor:a.role==="admin"?"#52c41a":"#1890ff",marginRight:8}}),e.createElement("div",null,e.createElement(V,{strong:!0},a.fullName||a.username),e.createElement("div",null,e.createElement(V,{type:"secondary",style:{fontSize:"12px"}},a.email)))),sorter:(t,a)=>(t.fullName||t.username).localeCompare(a.fullName||a.username)},{title:"Rôle",dataIndex:"role",key:"role",render:t=>e.createElement(Ee,{color:t==="admin"?"green":"blue"},t==="admin"?"Administrateur":"Utilisateur"),filters:[{text:"Administrateur",value:"admin"},{text:"Utilisateur",value:"user"}],onFilter:(t,a)=>a.role===t},{title:"Statut",dataIndex:"active",key:"active",render:t=>e.createElement(ge,{status:t?"success":"default",text:t?"Actif":"Inactif"}),filters:[{text:"Actif",value:!0},{text:"Inactif",value:!1}],onFilter:(t,a)=>a.active===t},{title:"Créé le",dataIndex:"createdAt",key:"createdAt",render:t=>t?new Date(t).toLocaleDateString():"N/A",sorter:(t,a)=>new Date(t.createdAt||0)-new Date(a.createdAt||0),responsive:["md"]},{title:"Actions",key:"actions",render:(t,a)=>e.createElement(g,{size:"small"},e.createElement(C,{title:"Modifier"},e.createElement(u,{icon:e.createElement(he,null),onClick:()=>re(a),type:"text",disabled:a.id===(r==null?void 0:r.id)})),e.createElement(C,{title:"Réinitialiser le mot de passe"},e.createElement(u,{icon:e.createElement($e,null),onClick:()=>ne(a),type:"text",disabled:a.id===(r==null?void 0:r.id)})),e.createElement(C,{title:"Supprimer"},e.createElement(ve,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>se(a.id),okText:"Oui",cancelText:"Non",disabled:a.id===(r==null?void 0:r.id)},e.createElement(u,{danger:!0,icon:e.createElement(we,null),type:"text",disabled:a.id===(r==null?void 0:r.id)}))))}];return e.createElement("div",null,e.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16,flexWrap:"wrap",gap:"8px"}},e.createElement(Ne,{level:4},"Gestion des utilisateurs"),e.createElement(g,{wrap:!0},e.createElement(m,{placeholder:"Rechercher un utilisateur",prefix:e.createElement(be,null),value:v,onChange:t=>H(t.target.value),style:{width:250},allowClear:!0}),e.createElement(u,{type:"primary",icon:e.createElement(Ue,null),onClick:ae},"Ajouter un utilisateur"),e.createElement(u,{icon:e.createElement(ye,null),onClick:f},"Actualiser"))),e.createElement(de,{columns:oe,dataSource:te,rowKey:"id",loading:Z,pagination:{pageSize:10,showSizeChanger:!0,showTotal:t=>`Total: ${t} utilisateurs`},locale:{emptyText:e.createElement(U,{image:U.PRESENTED_IMAGE_SIMPLE,description:"Aucun utilisateur trouvé"})}}),e.createElement(N,{title:W,open:J,onCancel:()=>p(!1),footer:null,width:700,destroyOnClose:!0},e.createElement(o,{form:R,layout:"vertical",onFinish:le,initialValues:{role:"user",active:!0}},e.createElement(b,{gutter:16},e.createElement(d,{span:12},e.createElement(o.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer le nom complet"}]},e.createElement(m,{prefix:e.createElement(x,null),placeholder:"Nom complet"}))),e.createElement(d,{span:12},e.createElement(o.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer le nom d'utilisateur"}]},e.createElement(m,{prefix:e.createElement(x,null),placeholder:"Nom d'utilisateur"})))),e.createElement(b,{gutter:16},e.createElement(d,{span:12},e.createElement(o.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer l'email"},{type:"email",message:"Veuillez entrer un email valide"}]},e.createElement(m,{prefix:e.createElement(pe,null),placeholder:"Email"}))),e.createElement(d,{span:12},e.createElement(o.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}]},e.createElement(m,{prefix:e.createElement(Te,null),placeholder:"Téléphone"})))),e.createElement(b,{gutter:16},e.createElement(d,{span:12},e.createElement(o.Item,{name:"role",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}]},e.createElement(k,{placeholder:"Sélectionner un rôle"},e.createElement(_,{value:"user"},"Utilisateur"),e.createElement(_,{value:"admin"},"Administrateur")))),e.createElement(d,{span:12},e.createElement(o.Item,{name:"active",label:"Statut",valuePropName:"checked"},e.createElement(Re,{checkedChildren:e.createElement(Ie,null),unCheckedChildren:e.createElement(xe,null)})))),!h&&e.createElement(b,{gutter:16},e.createElement(d,{span:24},e.createElement(o.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"}]},e.createElement(m.Password,{prefix:e.createElement(I,null),placeholder:"Mot de passe",iconRender:t=>t?e.createElement(F,null):e.createElement(L,null),visibilityToggle:{visible:Q,onVisibleChange:T}})))),e.createElement(fe,null),e.createElement(o.Item,{style:{marginBottom:0,textAlign:"right"}},e.createElement(g,null,e.createElement(u,{onClick:()=>p(!1)},"Annuler"),e.createElement(u,{type:"primary",htmlType:"submit"},h?"Mettre à jour":"Ajouter"))))),e.createElement(N,{title:"Réinitialiser le mot de passe",open:X,onCancel:()=>w(!1),footer:null,destroyOnClose:!0},e.createElement(o,{form:z,layout:"vertical",onFinish:ie},e.createElement(o.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer un nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}]},e.createElement(m.Password,{prefix:e.createElement(I,null),placeholder:"Nouveau mot de passe",iconRender:t=>t?e.createElement(F,null):e.createElement(L,null)})),e.createElement(o.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer le mot de passe"},({getFieldValue:t})=>({validator(a,E){return!E||t("newPassword")===E?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},e.createElement(m.Password,{prefix:e.createElement(I,null),placeholder:"Confirmer le mot de passe"})),e.createElement(o.Item,{style:{marginBottom:0,textAlign:"right"}},e.createElement(g,null,e.createElement(u,{onClick:()=>w(!1)},"Annuler"),e.createElement(u,{type:"primary",htmlType:"submit"},"Réinitialiser"))))))},Ge=Object.freeze(Object.defineProperty({__proto__:null,default:Fe},Symbol.toStringTag,{value:"Module"}));export{$e as R,Fe as U,Te as a,Ge as u};
