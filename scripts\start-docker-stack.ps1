#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Start Complete Docker Stack for Multi-Client State Management Testing
    
.DESCRIPTION
    Starts the complete development environment using Docker Compose including:
    - MySQL Database
    - Redis Cache & Pub/Sub
    - LOCQL Application with Multi-Client State Management
    - Optional ngrok tunnel for external access
    
.PARAMETER WithNgrok
    Include ngrok tunnel for external access
    
.PARAMETER Rebuild
    Force rebuild of containers before starting
    
.PARAMETER Logs
    Show logs after starting containers
    
.EXAMPLE
    .\start-docker-stack.ps1
    
.EXAMPLE
    .\start-docker-stack.ps1 -WithNgrok -Rebuild -Logs
#>

param(
    [Parameter(Mandatory=$false)]
    [switch]$WithNgrok,
    
    [Parameter(Mandatory=$false)]
    [switch]$Rebuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$Logs
)

# Script configuration
$ErrorActionPreference = "Stop"

# Colors for output
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-TestHeader {
    param([string]$Title)
    
    Write-ColorOutput "`n$('=' * 60)" -Color $Colors.Header
    Write-ColorOutput "🐳 $Title" -Color $Colors.Header
    Write-ColorOutput "$('=' * 60)" -Color $Colors.Header
}

function Test-DockerInstallation {
    Write-ColorOutput "`n🔍 Checking Docker installation..." -Color $Colors.Info
    
    try {
        $dockerVersion = docker --version
        Write-ColorOutput "✅ Docker is installed: $dockerVersion" -Color $Colors.Success
        
        $composeVersion = docker-compose --version
        Write-ColorOutput "✅ Docker Compose is installed: $composeVersion" -Color $Colors.Success
        
        return $true
    } catch {
        Write-ColorOutput "❌ Docker or Docker Compose is not installed" -Color $Colors.Error
        Write-ColorOutput "Please install Docker Desktop from https://www.docker.com/products/docker-desktop" -Color $Colors.Warning
        return $false
    }
}

function Test-DockerRunning {
    Write-ColorOutput "`n🔍 Checking if Docker is running..." -Color $Colors.Info
    
    try {
        docker info | Out-Null
        Write-ColorOutput "✅ Docker is running" -Color $Colors.Success
        return $true
    } catch {
        Write-ColorOutput "❌ Docker is not running" -Color $Colors.Error
        Write-ColorOutput "Please start Docker Desktop" -Color $Colors.Warning
        return $false
    }
}

function Stop-ExistingContainers {
    Write-ColorOutput "`n🛑 Stopping existing containers..." -Color $Colors.Info
    
    try {
        # Stop containers using the docker-compose file
        docker-compose -f docker-compose.local-ngrok.yml down --remove-orphans
        Write-ColorOutput "✅ Existing containers stopped" -Color $Colors.Success
    } catch {
        Write-ColorOutput "⚠️ No existing containers to stop or error occurred" -Color $Colors.Warning
    }
}

function Start-DockerStack {
    Write-ColorOutput "`n🚀 Starting Docker stack..." -Color $Colors.Info
    
    try {
        $composeArgs = @("-f", "docker-compose.local-ngrok.yml")
        
        if ($WithNgrok) {
            $composeArgs += @("--profile", "ngrok")
            Write-ColorOutput "🌐 Including ngrok tunnel for external access" -Color $Colors.Info
        }
        
        $composeArgs += "up"
        
        if ($Rebuild) {
            $composeArgs += "--build"
            Write-ColorOutput "🔨 Rebuilding containers..." -Color $Colors.Info
        }
        
        $composeArgs += @("-d", "--wait")
        
        Write-ColorOutput "Executing: docker-compose $($composeArgs -join ' ')" -Color $Colors.Info
        
        & docker-compose @composeArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Docker stack started successfully" -Color $Colors.Success
        } else {
            throw "Docker compose failed with exit code $LASTEXITCODE"
        }

    } catch {
        Write-ColorOutput "❌ Failed to start Docker stack: $($_.Exception.Message)" -Color $Colors.Error
        throw
    }
}

function Test-ServiceHealth {
    Write-ColorOutput "`n🏥 Checking service health..." -Color $Colors.Info
    
    $services = @(
        @{ Name = "MySQL"; Container = "locql-mysql-local"; Port = 3306; HealthCheck = "mysqladmin ping -h localhost -u root -proot" },
        @{ Name = "Redis"; Container = "locql-redis-local"; Port = 6379; HealthCheck = "redis-cli ping" },
        @{ Name = "LOCQL App"; Container = "locql-app-local"; Port = 5000; HealthCheck = "curl -f http://localhost:5000/api/health" }
    )
    
    $allHealthy = $true
    
    foreach ($service in $services) {
        Write-ColorOutput "🔍 Checking $($service.Name)..." -Color $Colors.Info
        
        try {
            # Check if container is running
            $containerStatus = docker ps --filter "name=$($service.Container)" --format "{{.Status}}"
            
            if ($containerStatus -match "Up") {
                Write-ColorOutput "  ✅ Container is running" -Color $Colors.Success
                
                # Wait a moment for service to be ready
                Start-Sleep -Seconds 2
                
                # Test service health
                try {
                    if ($service.Name -eq "LOCQL App") {
                        Invoke-RestMethod -Uri "http://localhost:$($service.Port)/api/health" -TimeoutSec 10 | Out-Null
                        Write-ColorOutput "  ✅ Service is healthy" -Color $Colors.Success
                    } else {
                        # For MySQL and Redis, check via docker exec
                        $healthResult = docker exec $service.Container sh -c $service.HealthCheck 2>&1
                        if ($LASTEXITCODE -eq 0) {
                            Write-ColorOutput "  ✅ Service is healthy" -Color $Colors.Success
                        } else {
                            Write-ColorOutput "  ⚠️ Service health check failed: $healthResult" -Color $Colors.Warning
                            $allHealthy = $false
                        }
                    }
                } catch {
                    Write-ColorOutput "  ⚠️ Service health check failed: $($_.Exception.Message)" -Color $Colors.Warning
                    $allHealthy = $false
                }
            } else {
                Write-ColorOutput "  ❌ Container is not running" -Color $Colors.Error
                $allHealthy = $false
            }
        } catch {
            Write-ColorOutput "  ❌ Error checking $($service.Name): $($_.Exception.Message)" -Color $Colors.Error
            $allHealthy = $false
        }
    }
    
    return $allHealthy
}

function Show-ServiceInfo {
    Write-ColorOutput "`n📊 Service Information:" -Color $Colors.Info
    
    # Show running containers
    Write-ColorOutput "`n🐳 Running Containers:" -Color $Colors.Info
    docker ps --filter "name=locql" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    # Show service URLs
    Write-ColorOutput "`n🌐 Service URLs:" -Color $Colors.Info
    Write-ColorOutput "  • LOCQL Application: http://localhost:5000" -Color $Colors.Success
    Write-ColorOutput "  • MySQL Database: localhost:3306" -Color $Colors.Success
    Write-ColorOutput "  • Redis Cache: localhost:6379" -Color $Colors.Success
    
    if ($WithNgrok) {
        Write-ColorOutput "  • ngrok Web Interface: http://localhost:4040" -Color $Colors.Success
        Write-ColorOutput "  • External Access: https://eternal-friendly-chigger.ngrok-free.app" -Color $Colors.Success
    }
    
    # Show useful commands
    Write-ColorOutput "`n🛠️ Useful Commands:" -Color $Colors.Info
    Write-ColorOutput "  • View logs: docker-compose -f docker-compose.local-ngrok.yml logs -f" -Color $Colors.Info
    Write-ColorOutput "  • Stop stack: docker-compose -f docker-compose.local-ngrok.yml down" -Color $Colors.Info
    Write-ColorOutput "  • Restart service: docker-compose -f docker-compose.local-ngrok.yml restart locql" -Color $Colors.Info
    Write-ColorOutput "  • Execute in container: docker exec -it locql-app-local sh" -Color $Colors.Info
}

function Show-TestingInstructions {
    Write-ColorOutput "`n🧪 Testing Instructions:" -Color $Colors.Info
    Write-ColorOutput "Now that the Docker stack is running, you can test the multi-client state management:" -Color $Colors.Info
    Write-ColorOutput "" -Color $Colors.Info
    Write-ColorOutput "1. Test server health:" -Color $Colors.Info
    Write-ColorOutput "   curl http://localhost:5000/api/health" -Color $Colors.Success
    Write-ColorOutput "" -Color $Colors.Info
    Write-ColorOutput "2. Test Redis connectivity:" -Color $Colors.Info
    Write-ColorOutput "   curl http://localhost:5000/api/health/redis" -Color $Colors.Success
    Write-ColorOutput "" -Color $Colors.Info
    Write-ColorOutput "3. Run the test suite:" -Color $Colors.Info
    Write-ColorOutput "   .\scripts\test-multi-client-state-management.ps1 -TestMode full -VerboseOutput" -Color $Colors.Success
    Write-ColorOutput "" -Color $Colors.Info
    Write-ColorOutput "4. Test WebSocket endpoint:" -Color $Colors.Info
    Write-ColorOutput "   # Use a WebSocket client to connect to ws://localhost:5000/api/state-sync-ws" -Color $Colors.Success
}

# Main execution
try {
    Write-TestHeader "DOCKER STACK STARTUP"
    
    Write-ColorOutput "Starting complete development environment..." -Color $Colors.Info
    Write-ColorOutput "Configuration: docker-compose.local-ngrok.yml" -Color $Colors.Info
    if ($WithNgrok) { Write-ColorOutput "ngrok tunnel: ENABLED" -Color $Colors.Info }
    if ($Rebuild) { Write-ColorOutput "Rebuild containers: ENABLED" -Color $Colors.Info }
    
    # Pre-flight checks
    if (-not (Test-DockerInstallation)) { exit 1 }
    if (-not (Test-DockerRunning)) { exit 1 }
    
    # Stop existing containers
    Stop-ExistingContainers
    
    # Start the stack
    Start-DockerStack
    
    # Wait for services to be ready
    Write-ColorOutput "`n⏳ Waiting for services to be ready..." -Color $Colors.Info
    Start-Sleep -Seconds 10
    
    # Test service health
    $allHealthy = Test-ServiceHealth
    
    if ($allHealthy) {
        Write-ColorOutput "`n🎉 All services are healthy and ready!" -Color $Colors.Success
    } else {
        Write-ColorOutput "`n⚠️ Some services may not be fully ready yet" -Color $Colors.Warning
        Write-ColorOutput "This is normal during initial startup - services may take a few more seconds" -Color $Colors.Info
    }
    
    # Show service information
    Show-ServiceInfo
    
    # Show testing instructions
    Show-TestingInstructions
    
    # Show logs if requested
    if ($Logs) {
        Write-ColorOutput "`n📋 Container Logs:" -Color $Colors.Info
        docker-compose -f docker-compose.local-ngrok.yml logs --tail=50
    }
    
    Write-ColorOutput "`n✅ Docker stack startup completed successfully!" -Color $Colors.Success
    
} catch {
    Write-ColorOutput "`n❌ Docker stack startup failed: $($_.Exception.Message)" -Color $Colors.Error
    Write-ColorOutput "`nTroubleshooting tips:" -Color $Colors.Warning
    Write-ColorOutput "1. Ensure Docker Desktop is running" -Color $Colors.Info
    Write-ColorOutput "2. Check if ports 3306, 5000, 6379 are available" -Color $Colors.Info
    Write-ColorOutput "3. Try rebuilding: .\start-docker-stack.ps1 -Rebuild" -Color $Colors.Info
    Write-ColorOutput "4. Check logs: docker-compose -f docker-compose.local-ngrok.yml logs" -Color $Colors.Info
    exit 1
}
