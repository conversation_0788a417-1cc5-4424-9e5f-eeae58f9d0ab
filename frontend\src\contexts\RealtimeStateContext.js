/**
 * Real-time State Management Context
 * Provides centralized state management across the application using WebSocket-based synchronization
 * 
 * Features:
 * - Centralized state management for all real-time data
 * - Cross-device synchronization
 * - Optimistic updates with conflict resolution
 * - Connection status monitoring
 * - Automatic reconnection handling
 */

import React, { createContext, useContext, useReducer, useEffect, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useRealtimeState } from '../hooks/useRealtimeState';

// State types managed by the context
const STATE_TYPES = {
  PRODUCTION_DATA: 'production_data',
  MACHINE_STATUS: 'machine_status',
  DASHBOARD_FILTERS: 'dashboard_filters',
  USER_PREFERENCES: 'user_preferences',
  REAL_TIME_METRICS: 'real_time_metrics',
  STOP_ANALYSIS: 'stop_analysis',
  REPORTS: 'reports'
};

// Action types for state management
const ACTION_TYPES = {
  SET_STATE: 'SET_STATE',
  UPDATE_STATE: 'UPDATE_STATE',
  SET_CONNECTION_STATUS: 'SET_CONNECTION_STATUS',
  ADD_CONFLICT: 'ADD_CONFLICT',
  RESOLVE_CONFLICT: 'RESOLVE_CONFLICT',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR'
};

// Initial state
const initialState = {
  // Data states
  productionData: null,
  machineStatus: null,
  dashboardFilters: {},
  userPreferences: {},
  realTimeMetrics: null,
  stopAnalysis: null,
  reports: null,
  
  // Connection status
  connectionStatus: 'disconnected',
  isConnected: false,
  
  // UI states
  loading: {},
  errors: {},
  conflicts: [],
  
  // Metadata
  lastUpdate: null,
  optimisticUpdates: new Set()
};

// State reducer
const stateReducer = (state, action) => {
  switch (action.type) {
    case ACTION_TYPES.SET_STATE:
      return {
        ...state,
        [action.stateKey]: action.data,
        lastUpdate: action.timestamp || Date.now()
      };

    case ACTION_TYPES.UPDATE_STATE:
      return {
        ...state,
        [action.stateKey]: {
          ...state[action.stateKey],
          ...action.data
        },
        lastUpdate: action.timestamp || Date.now()
      };

    case ACTION_TYPES.SET_CONNECTION_STATUS:
      return {
        ...state,
        connectionStatus: action.status,
        isConnected: action.status === 'connected'
      };

    case ACTION_TYPES.ADD_CONFLICT:
      return {
        ...state,
        conflicts: [...state.conflicts, action.conflict]
      };

    case ACTION_TYPES.RESOLVE_CONFLICT:
      return {
        ...state,
        conflicts: state.conflicts.filter(c => c.id !== action.conflictId)
      };

    case ACTION_TYPES.SET_LOADING:
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.key]: action.isLoading
        }
      };

    case ACTION_TYPES.SET_ERROR:
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.key]: action.error
        }
      };

    default:
      return state;
  }
};

// Create context
const RealtimeStateContext = createContext();

// Context provider component
export const RealtimeStateProvider = ({ children }) => {
  const { user } = useAuth();
  const [state, dispatch] = useReducer(stateReducer, initialState);
  
  // Real-time state hooks for different data types
  const productionState = useRealtimeState(
    STATE_TYPES.PRODUCTION_DATA, 
    'global', 
    null,
    { syncStrategy: 'immediate' }
  );
  
  const machineStatusState = useRealtimeState(
    STATE_TYPES.MACHINE_STATUS, 
    'global', 
    null,
    { syncStrategy: 'immediate' }
  );
  
  const dashboardFiltersState = useRealtimeState(
    STATE_TYPES.DASHBOARD_FILTERS, 
    user?.id || 'anonymous', 
    {},
    { syncStrategy: 'debounced', debounceMs: 500 }
  );
  
  const userPreferencesState = useRealtimeState(
    STATE_TYPES.USER_PREFERENCES, 
    user?.id || 'anonymous', 
    {},
    { syncStrategy: 'immediate' }
  );
  
  const realTimeMetricsState = useRealtimeState(
    STATE_TYPES.REAL_TIME_METRICS, 
    'global', 
    null,
    { syncStrategy: 'immediate' }
  );

  // Update context state when real-time states change
  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.SET_STATE,
      stateKey: 'productionData',
      data: productionState.state,
      timestamp: productionState.lastUpdate
    });
  }, [productionState.state, productionState.lastUpdate]);

  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.SET_STATE,
      stateKey: 'machineStatus',
      data: machineStatusState.state,
      timestamp: machineStatusState.lastUpdate
    });
  }, [machineStatusState.state, machineStatusState.lastUpdate]);

  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.SET_STATE,
      stateKey: 'dashboardFilters',
      data: dashboardFiltersState.state,
      timestamp: dashboardFiltersState.lastUpdate
    });
  }, [dashboardFiltersState.state, dashboardFiltersState.lastUpdate]);

  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.SET_STATE,
      stateKey: 'userPreferences',
      data: userPreferencesState.state,
      timestamp: userPreferencesState.lastUpdate
    });
  }, [userPreferencesState.state, userPreferencesState.lastUpdate]);

  useEffect(() => {
    dispatch({
      type: ACTION_TYPES.SET_STATE,
      stateKey: 'realTimeMetrics',
      data: realTimeMetricsState.state,
      timestamp: realTimeMetricsState.lastUpdate
    });
  }, [realTimeMetricsState.state, realTimeMetricsState.lastUpdate]);

  // Update connection status
  useEffect(() => {
    const connectionStates = [
      productionState.connectionState,
      machineStatusState.connectionState,
      dashboardFiltersState.connectionState,
      userPreferencesState.connectionState,
      realTimeMetricsState.connectionState
    ];

    // Determine overall connection status
    const connectedCount = connectionStates.filter(state => state === 'connected').length;
    const connectingCount = connectionStates.filter(state => state === 'connecting').length;
    
    let overallStatus = 'disconnected';
    if (connectedCount > 0) {
      overallStatus = 'connected';
    } else if (connectingCount > 0) {
      overallStatus = 'connecting';
    }

    dispatch({
      type: ACTION_TYPES.SET_CONNECTION_STATUS,
      status: overallStatus
    });
  }, [
    productionState.connectionState,
    machineStatusState.connectionState,
    dashboardFiltersState.connectionState,
    userPreferencesState.connectionState,
    realTimeMetricsState.connectionState
  ]);

  // Collect conflicts from all state hooks
  useEffect(() => {
    const allConflicts = [
      ...productionState.conflicts,
      ...machineStatusState.conflicts,
      ...dashboardFiltersState.conflicts,
      ...userPreferencesState.conflicts,
      ...realTimeMetricsState.conflicts
    ];

    // Update conflicts in context state
    allConflicts.forEach(conflict => {
      dispatch({
        type: ACTION_TYPES.ADD_CONFLICT,
        conflict
      });
    });
  }, [
    productionState.conflicts,
    machineStatusState.conflicts,
    dashboardFiltersState.conflicts,
    userPreferencesState.conflicts,
    realTimeMetricsState.conflicts
  ]);

  // Context value with state and actions
  const contextValue = {
    // State data
    ...state,
    
    // State update functions
    updateProductionData: (data, strategy) => productionState.setState(data, strategy),
    updateMachineStatus: (data, strategy) => machineStatusState.setState(data, strategy),
    updateDashboardFilters: (data, strategy) => dashboardFiltersState.setState(data, strategy),
    updateUserPreferences: (data, strategy) => userPreferencesState.setState(data, strategy),
    updateRealTimeMetrics: (data, strategy) => realTimeMetricsState.setState(data, strategy),
    
    // Connection control
    reconnect: () => {
      productionState.reconnect();
      machineStatusState.reconnect();
      dashboardFiltersState.reconnect();
      userPreferencesState.reconnect();
      realTimeMetricsState.reconnect();
    },
    
    // Conflict resolution
    resolveConflict: (conflictId, resolution) => {
      // Try to resolve conflict in each state hook
      productionState.resolveConflict(conflictId, resolution);
      machineStatusState.resolveConflict(conflictId, resolution);
      dashboardFiltersState.resolveConflict(conflictId, resolution);
      userPreferencesState.resolveConflict(conflictId, resolution);
      realTimeMetricsState.resolveConflict(conflictId, resolution);
      
      // Remove from context state
      dispatch({
        type: ACTION_TYPES.RESOLVE_CONFLICT,
        conflictId
      });
    },
    
    // Optimistic updates
    rollbackOptimisticUpdates: () => {
      productionState.rollbackOptimisticUpdate();
      machineStatusState.rollbackOptimisticUpdate();
      dashboardFiltersState.rollbackOptimisticUpdate();
      userPreferencesState.rollbackOptimisticUpdate();
      realTimeMetricsState.rollbackOptimisticUpdate();
    },
    
    // Loading and error management
    setLoading: (key, isLoading) => {
      dispatch({
        type: ACTION_TYPES.SET_LOADING,
        key,
        isLoading
      });
    },
    
    setError: (key, error) => {
      dispatch({
        type: ACTION_TYPES.SET_ERROR,
        key,
        error
      });
    },
    
    // Sync strategies
    SYNC_STRATEGIES: productionState.SYNC_STRATEGIES,
    
    // State types
    STATE_TYPES
  };

  return (
    <RealtimeStateContext.Provider value={contextValue}>
      {children}
    </RealtimeStateContext.Provider>
  );
};

// Custom hook to use the context
export const useRealtimeStateContext = () => {
  const context = useContext(RealtimeStateContext);
  
  if (!context) {
    throw new Error('useRealtimeStateContext must be used within a RealtimeStateProvider');
  }
  
  return context;
};

// Export state types for use in components
export { STATE_TYPES };
