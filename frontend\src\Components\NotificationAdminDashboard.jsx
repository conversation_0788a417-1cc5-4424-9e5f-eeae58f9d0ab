/**
 * Notification Admin Dashboard
 * Comprehensive admin interface for notification system management
 * Includes system status, testing, and integration verification
 */

import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Space,
  Typography,
  Alert,
  Button,
  Tooltip
} from 'antd';
import {
  SettingOutlined,
  BugOutlined,
  TestTubeOutlined,
  DashboardOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAuth } from '../hooks/useAuth';


const { Title } = Typography;
const { TabPane } = Tabs;

const NotificationAdminDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('status');

  // Check if user has admin permissions
  const isAdmin = user?.role === 'admin';

  if (!isAdmin) {
    return (
      <Alert
        message="Admin Access Required"
        description="You need administrator privileges to access the notification management dashboard."
        type="warning"
        showIcon
        style={{ margin: '20px 0' }}
      />
    );
  }

  return (
    <div style={{ padding: '20px 0' }}>
      <Card
        title={
          <Space>
            <DashboardOutlined />
            <Title level={3} style={{ margin: 0 }}>
              Notification System Admin Dashboard
            </Title>
          </Space>
        }
        style={{ marginBottom: 20 }}
      >
        <Alert
          message="Administrator Dashboard"
          description="This dashboard provides comprehensive tools for managing and testing the notification system. Use these tools to monitor system status, test functionality, and verify integrations."
          type="info"
          showIcon
          style={{ marginBottom: 20 }}
        />

        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          size="large"
        >
          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                System Status & Control
              </Space>
            }
            key="status"
          >
            <Alert
              message="Notification System"
              description="The notification system is always active and ready to receive and display notifications."
              type="success"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Alert
              message="System Control Information"
              description={
                <div>
                  <p><strong>System Status:</strong> Shows whether notifications are globally enabled or disabled</p>
                  <p><strong>Toggle Control:</strong> Allows you to enable/disable the notification system</p>
                  <p><strong>Activity Monitoring:</strong> Displays recent notification creation statistics</p>
                  <p><strong>Use Cases:</strong></p>
                  <ul>
                    <li>Disable notifications during debugging or maintenance</li>
                    <li>Monitor system activity and performance</li>
                    <li>Quickly enable/disable without server restart</li>
                  </ul>
                </div>
              }
              type="info"
              showIcon
              style={{ marginTop: 20 }}
            />
          </TabPane>

          {/* Testing components disabled */}
          {/*
          <TabPane
            tab={
              <Space>
                <BugOutlined />
                Testing Panel (Disabled)
              </Space>
            }
            key="testing"
          >
            <Alert
              message="Testing Panel Disabled"
              description="Notification testing components have been disabled to prevent notification creation."
              type="warning"
              showIcon
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <TestTubeOutlined />
                Integration Tests (Disabled)
              </Space>
            }
            key="integration"
          >
            <Alert
              message="Integration Testing Disabled"
              description="Notification integration testing has been disabled to prevent notification creation."
              type="warning"
              showIcon
            />
          </TabPane>
          */}
        </Tabs>
      </Card>

      {/* Quick Actions */}
      <Card
        title="Quick Actions"
        size="small"
        style={{ marginTop: 20 }}
      >
        <Space wrap>
          <Tooltip title="Refresh all system data">
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            >
              Refresh Dashboard
            </Button>
          </Tooltip>
          
          <Tooltip title="Navigate to notifications page">
            <Button 
              onClick={() => window.location.href = '/notifications'}
            >
              View Notifications Page
            </Button>
          </Tooltip>
          
          <Tooltip title="Open browser console for debugging">
            <Button 
              onClick={() => {
                console.log('Notification Admin Dashboard - Debug Info');
                console.log('User:', user);
                console.log('Current Tab:', activeTab);
                console.log('Timestamp:', new Date().toISOString());
              }}
            >
              Debug Console
            </Button>
          </Tooltip>
        </Space>
      </Card>

      {/* Documentation */}
      <Card
        title="Documentation & Resources"
        size="small"
        style={{ marginTop: 20 }}
      >
        <Alert
          message="Command Line Tools"
          description={
            <div>
              <p><strong>Notification System Information:</strong></p>
              <p>The notification system is always active and ready to process notifications. All machine alerts, maintenance notifications, and system updates will be automatically created and stored.</p>
              <p><strong>Available API Endpoints:</strong></p>
              <ul>
                <li><code>GET /api/notifications/system/status</code> - Check system status</li>
                <li><code>GET /api/notifications/stats</code> - Get notification statistics</li>
                <li><code>POST /api/notifications</code> - Create new notification</li>
                <li><code>GET /api/notifications</code> - Fetch user notifications</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default NotificationAdminDashboard;
