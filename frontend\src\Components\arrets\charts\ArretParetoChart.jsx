import React, { memo } from 'react';
import { <PERSON>sponsive<PERSON><PERSON>r, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line, ComposedChart } from 'recharts';
import { Empty, Spin } from 'antd';

const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
};

const ArretParetoChart = memo(({ data = [], loading = false }) => {
  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée Pareto disponible" />
      </div>
    );
  }

  // Process and sort data for Pareto analysis
  const sortedData = data
    .map(item => ({
      reason: item.reason || item.Code_Stop || item.stopName || 'N/A',
      value: parseFloat(item.value || item.duration || item.count || 0),
      percentage: parseFloat(item.percentage || 0),
    }))
    .sort((a, b) => b.value - a.value);

  // Calculate cumulative percentage
  const totalValue = sortedData.reduce((sum, item) => sum + item.value, 0);
  let cumulativeSum = 0;
  
  const paretoData = sortedData.map(item => {
    cumulativeSum += item.value;
    const cumulativePercentage = (cumulativeSum / totalValue) * 100;
    
    return {
      ...item,
      cumulativePercentage: cumulativePercentage,
    };
  });
  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart data={paretoData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="reason"
          tick={{ fill: "#666", fontSize: 12 }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis
          yAxisId="left"
          label={{
            value: "Durée (min)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
          tick={{ fill: "#666" }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          label={{
            value: "Cumul (%)",
            angle: 90,
            position: "insideRight",
            style: { fill: "#666" },
          }}
          tick={{ fill: "#666" }}
          domain={[0, 100]}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 4,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value, name) => {
            if (name === 'value') return [`${value.toFixed(1)} min`, 'Durée'];
            if (name === 'cumulativePercentage') return [`${value.toFixed(1)}%`, 'Cumul'];
            return [value, name];
          }}
        />
        <Legend />
        <Bar
          yAxisId="left"
          dataKey="value"
          fill={CHART_COLORS.primary}
          name="Durée"
          radius={[4, 4, 0, 0]}
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="cumulativePercentage"
          stroke={CHART_COLORS.danger}
          strokeWidth={3}
          dot={{ fill: CHART_COLORS.danger, strokeWidth: 2, r: 4 }}
          name="Cumul %"
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
});

ArretParetoChart.displayName = 'ArretParetoChart';

export default ArretParetoChart;
