/**
 * ArretContext Module Index
 * 
 * This file provides easy imports for the modular ArretContext system.
 * Components can import from this index or directly from the modules.
 */

// Main context export
export { default as ArretContext, ArretProvider, useArretContext } from './ArretContext.jsx'

// Individual modules for advanced usage
export { CHART_COLORS, SKELETON_SECTIONS, INITIAL_DATA_STATE } from './modules/constants.jsx'
export { useSkeletonManager } from './modules/skeletonManager.jsx'
export { useDataManager } from './modules/dataManager.jsx'
export { useEventHandlers } from './modules/eventHandlers.jsx'
export { useComputedValues } from './modules/computedValues.jsx'
export { 
  calculatePerformanceMetrics, 
  formatPerformanceMetric, 
  getPerformanceMetricStatus 
} from './modules/performanceCalculations.jsx'
export { 
  transformSidecardsToStats, 
  enhanceStopsData, 
  processComprehensiveData,
  extractTopStops 
} from './modules/dataProcessing.jsx'

// Re-export for backward compatibility with existing imports
export { ArretProvider as default } from './ArretContext.jsx'
