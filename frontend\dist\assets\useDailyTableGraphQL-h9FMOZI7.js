import{r as a}from"./index-LbZyOyVE.js";const j="/api/graphql",I=()=>{const[G,y]=a.useState(!1),[k,m]=a.useState(null),t=a.useCallback(async(e,r={})=>{y(!0),m(null);try{const n=await fetch(j,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:r})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const i=await n.json();if(i.errors)throw new Error(i.errors.map(l=>l.message).join(", "));return y(!1),i.data}catch(n){throw m(n.message),y(!1),n}},[]),M=a.useCallback(async(e={})=>t(`
      query GetAllDailyProduction($filters: FilterInput) {
        getAllDailyProduction(filters: $filters) {
          Machine_Name
          Date_Insert_Day
          Run_Hours_Day
          Down_Hours_Day
          Good_QTY_Day
          Rejects_QTY_Day
          Speed_Day
          Availability_Rate_Day
          Performance_Rate_Day
          Quality_Rate_Day
          OEE_Day
          Shift
        }
      }
    `,{filters:e}),[t]),d=a.useCallback(async(e={})=>t(`
      query GetProductionChart($filters: EnhancedFilterInput) {
        enhancedGetProductionChart(filters: $filters) {
          data {
            Date_Insert_Day
            Total_Good_Qty_Day
            Total_Rejects_Qty_Day
            OEE_Day
            Speed_Day
            Availability_Rate_Day
            Performance_Rate_Day
            Quality_Rate_Day
          }
          dataSource
        }
      }
    `,{filters:e}),[t]),h=a.useCallback(async(e={})=>t(`
      query GetProductionSidecards($filters: EnhancedFilterInput) {
        enhancedGetProductionSidecards(filters: $filters) {
          goodqty
          rejetqty
          dataSource
        }
      }
    `,{filters:e}),[t]),w=a.useCallback(async()=>t(`
      query {
        getUniqueDates
      }
    `),[t]),C=a.useCallback(async()=>t(`
      query {
        getMachineModels {
          model
        }
      }
    `),[t]),$=a.useCallback(async()=>t(`
      query {
        getMachineNames {
          Machine_Name
        }
      }
    `),[t]),f=a.useCallback(async(e={})=>t(`
      query GetMachinePerformance($filters: EnhancedFilterInput) {
        enhancedGetMachinePerformance(filters: $filters) {
          data {
            Machine_Name
            Shift
            production
            rejects
            downtime
            availability
            performance
            oee
            quality
            disponibilite
          }
          dataSource
        }
      }
    `,{filters:e}),[t]),q=a.useCallback(async(e={})=>t(`
      query GetShiftPerformance($filters: EnhancedFilterInput) {
        enhancedGetShiftPerformance(filters: $filters) {
          data {
            Shift
            production
            rejects
            downtime
            availability
            performance
            oee
            quality
            disponibilite
          }
          dataSource
        }
      }
    `,{filters:e}),[t]),_=a.useCallback(async(e={})=>t(`
      query GetAvailabilityTrend($filters: FilterInput) {
        getAvailabilityTrend(filters: $filters) {
          date
          machine
          disponibilite
        }
      }
    `,{filters:e}),[t]),E=a.useCallback(async(e={})=>t(`
      query GetPerformanceMetrics($filters: FilterInput) {
        getPerformanceMetrics(filters: $filters) {
          machine
          model
          disponibilite
          stops
          mttr
          mtbf
        }
      }
    `,{filters:e}),[t]),T=a.useCallback(async(e={})=>{var r,n,i,l,D,P,b,g,p;try{const[o,c,s,u,S]=await Promise.all([d(e),h(e),f(e),q(e),_(e)]);return{productionChart:{data:((r=o==null?void 0:o.enhancedGetProductionChart)==null?void 0:r.data)||[],dataSource:((n=o==null?void 0:o.enhancedGetProductionChart)==null?void 0:n.dataSource)||"unknown"},sidecards:{goodqty:((i=c==null?void 0:c.enhancedGetProductionSidecards)==null?void 0:i.goodqty)||0,rejetqty:((l=c==null?void 0:c.enhancedGetProductionSidecards)==null?void 0:l.rejetqty)||0,dataSource:((D=c==null?void 0:c.enhancedGetProductionSidecards)==null?void 0:D.dataSource)||"unknown"},machinePerformance:{data:((P=s==null?void 0:s.enhancedGetMachinePerformance)==null?void 0:P.data)||[],dataSource:((b=s==null?void 0:s.enhancedGetMachinePerformance)==null?void 0:b.dataSource)||"unknown"},shiftPerformance:{data:((g=u==null?void 0:u.enhancedGetShiftPerformance)==null?void 0:g.data)||[],dataSource:((p=u==null?void 0:u.enhancedGetShiftPerformance)==null?void 0:p.dataSource)||"unknown"},availabilityTrend:(S==null?void 0:S.getAvailabilityTrend)||[]}}catch(o){throw console.error("Error fetching dashboard data:",o),o}},[d,h,f,q,_]);return{loading:G,error:k,getAllDailyProduction:M,getProductionChart:d,getProductionSidecards:h,getUniqueDates:w,getMachineModels:C,getMachineNames:$,getMachinePerformance:f,getShiftPerformance:q,getAvailabilityTrend:_,getPerformanceMetrics:E,getDashboardData:T,executeQuery:t}};export{I as u};
