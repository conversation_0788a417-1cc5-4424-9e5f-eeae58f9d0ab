# SOMIPEM Settings System - Complete Client Guide

## 📖 **How to Use This Guide**

This guide documents every setting in the SOMIPEM application, explaining what each setting does, where you can see its effects, and when you might want to change it. All settings produce **immediate visual effects** - no page refresh required.

**Navigation**: Access settings via the "Paramètres" menu item or go directly to `/settings`

---

## 🎨 **Theme & Display Settings**

### **Dark Mode**
- **Setting**: `theme.darkMode`
- **Purpose**: Switch between light and dark themes for better visibility in different lighting conditions
- **Options**: Light Mode (default) / Dark Mode
- **Immediate Effects**:
  - **Document Background**: Changes from light (#f0f2f5) to dark (#141414)
  - **All Components**: Cards, tables, menus, forms switch to dark colors
  - **Logo**: Automatically switches to dark mode logo variant
  - **Text Colors**: All text becomes white/high-contrast for readability
- **Impact Locations**:
  - ✅ **Entire Application**: Every page and component
  - ✅ **Navigation Menu**: Header and sidebar
  - ✅ **Data Tables**: All production and performance tables
  - ✅ **Charts**: Background colors and text
  - ✅ **Forms**: All input fields and controls
  - ✅ **Modals**: Dialog boxes and popups
- **When to Use**:
  - Working in low-light environments
  - Reducing eye strain during extended use
  - Personal preference for dark interfaces
  - Better contrast for certain visual conditions
- **Verification**: Toggle the setting and observe immediate color changes across the entire interface

### **Compact Mode**
- **Setting**: `theme.compactMode`
- **Purpose**: Reduce spacing and padding to display more content on screen
- **Options**: Normal Mode (default) / Compact Mode
- **Immediate Effects**:
  - **Card Padding**: Reduces from 24px to 16px
  - **Table Rows**: Smaller row height for more data visibility
  - **Button Sizes**: Smaller buttons with reduced padding
  - **Form Spacing**: Tighter spacing between form elements
- **Impact Locations**:
  - ✅ **Dashboard Cards**: Production metrics, performance cards
  - ✅ **Data Tables**: Machine data, production history
  - ✅ **Forms**: Settings forms, input dialogs
  - ✅ **Statistics**: KPI displays and metrics
- **When to Use**:
  - Working on smaller screens
  - Need to see more data at once
  - Prefer denser information layout
  - Maximizing screen real estate
- **Verification**: Toggle and observe immediate spacing changes in cards and tables

### **Animations**
- **Setting**: `theme.animationsEnabled`
- **Purpose**: Control smooth transitions and animations throughout the application
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Page Transitions**: Smooth navigation between pages
  - **Component Animations**: Fade-ins, slide effects
  - **Hover Effects**: Button and link hover animations
  - **Loading States**: Animated spinners and progress bars
- **Impact Locations**:
  - ✅ **Navigation**: Page transitions and menu animations
  - ✅ **Interactive Elements**: Buttons, links, form controls
  - ✅ **Data Loading**: Loading spinners and progress indicators
  - ✅ **Modal Dialogs**: Slide-in and fade effects
- **When to Use**:
  - **Disable** for better performance on slower devices
  - **Disable** for accessibility (motion sensitivity)
  - **Enable** for smoother, more polished user experience
- **Verification**: Toggle and observe immediate changes in transition smoothness

### **Chart Animations**
- **Setting**: `theme.chartAnimations`
- **Purpose**: Control animations specifically for charts and data visualizations
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Chart Rendering**: Smooth data entry animations
  - **Data Updates**: Animated transitions when data changes
  - **Interactive Charts**: Hover and selection animations
- **Impact Locations**:
  - ✅ **Production Dashboard**: All production trend charts
  - ✅ **Performance Charts**: Machine performance visualizations
  - ✅ **Arrets Analysis**: Downtime and availability charts
  - ✅ **Settings Preview**: Live chart demonstrations
- **When to Use**:
  - **Disable** for faster chart rendering with large datasets
  - **Disable** for accessibility (motion sensitivity)
  - **Enable** for engaging data visualization experience
- **Verification**: Toggle and observe chart animation changes in settings preview

---

## 📊 **Chart Settings**

### **Basic Chart Configuration**

#### **Default Chart Type**
- **Setting**: `charts.defaultType`
- **Purpose**: Set the default visualization type for new charts
- **Options**: Bar Chart (default) / Line Chart / Pie Chart / Area Chart
- **Immediate Effects**:
  - **Settings Preview**: Chart type changes instantly
  - **New Charts**: Future charts use selected type
- **Impact Locations**:
  - ✅ **Settings Preview**: Immediate demonstration
  - ✅ **Dashboard Charts**: When creating new visualizations
  - ✅ **Report Generation**: Default chart type for reports
- **When to Use**:
  - **Bar Charts**: Comparing quantities across categories
  - **Line Charts**: Showing trends over time
  - **Pie Charts**: Showing proportions of a whole
  - **Area Charts**: Emphasizing magnitude of change over time
- **Verification**: Change setting and see immediate chart type change in preview

#### **Show Legend**
- **Setting**: `charts.showLegend`
- **Purpose**: Control legend visibility across all charts
- **Options**: Show (default) / Hide
- **Immediate Effects**:
  - **All Charts**: Legends appear/disappear instantly
  - **Chart Space**: More space for data when hidden
- **Impact Locations**:
  - ✅ **Production Charts**: Machine performance legends
  - ✅ **Dashboard Visualizations**: All chart legends
  - ✅ **Settings Preview**: Immediate legend toggle
- **When to Use**:
  - **Hide** when chart data is self-explanatory
  - **Hide** to maximize chart display area
  - **Show** when multiple data series need identification
- **Verification**: Toggle and observe immediate legend visibility changes

#### **Color Scheme**
- **Setting**: `charts.colorScheme`
- **Purpose**: Set color palette for all charts
- **Options**: SOMIPEM (Brand Colors) / Blue Palette / Green Palette / Red Palette
- **Immediate Effects**:
  - **All Charts**: Colors change instantly
  - **Brand Consistency**: SOMIPEM colors maintain brand identity
- **Impact Locations**:
  - ✅ **All Chart Components**: Every chart in the application
  - ✅ **Dashboard Visualizations**: Production and performance charts
  - ✅ **Settings Preview**: Immediate color demonstration
- **When to Use**:
  - **SOMIPEM**: Maintain brand consistency (recommended)
  - **Blue/Green/Red**: Personal preference or accessibility needs
- **Verification**: Change scheme and see immediate color changes in all charts

#### **Performance Mode**
- **Setting**: `charts.performanceMode`
- **Purpose**: Optimize charts for large datasets
- **Options**: Disabled (default) / Enabled
- **Immediate Effects**:
  - **Chart Rendering**: Faster rendering with large data
  - **Reduced Complexity**: Simplified chart elements
- **Impact Locations**:
  - ✅ **Large Dataset Charts**: Production history, machine data
  - ✅ **Performance-Critical Views**: Real-time dashboards
- **When to Use**:
  - **Enable** when working with large datasets (>1000 points)
  - **Enable** on slower devices or networks
  - **Disable** for full visual quality with smaller datasets
- **Verification**: Toggle and observe rendering speed changes with large data

### **Advanced Chart Configuration**

#### **Chart Layout & Size**

##### **Default Chart Height**
- **Setting**: `charts.layout.defaultHeight`
- **Purpose**: Set standard height for all charts
- **Options**: 200-800 pixels (default: 300px)
- **Immediate Effects**:
  - **All Charts**: Height changes instantly
  - **Dashboard Layout**: More/less vertical space usage
- **Impact Locations**:
  - ✅ **Dashboard Charts**: All production and performance charts
  - ✅ **Settings Preview**: Immediate height demonstration
- **When to Use**:
  - **Smaller Heights (200-250px)**: Compact dashboards, more charts visible
  - **Larger Heights (400-800px)**: Detailed analysis, better data visibility
- **Verification**: Adjust slider and see immediate height changes in preview

##### **Compact Chart Mode**
- **Setting**: `charts.layout.compactMode`
- **Purpose**: Smaller charts for higher data density
- **Options**: Disabled (default) / Enabled
- **Immediate Effects**:
  - **Chart Margins**: Reduced spacing around charts
  - **Text Sizes**: Smaller labels and legends
- **Impact Locations**:
  - ✅ **Dashboard Views**: More charts fit on screen
  - ✅ **Multi-Chart Pages**: Better space utilization
- **When to Use**:
  - **Enable** for dashboard overview pages
  - **Enable** on smaller screens
  - **Disable** for detailed chart analysis
- **Verification**: Toggle and observe immediate chart size changes

##### **Chart Aspect Ratio**
- **Setting**: `charts.layout.aspectRatio`
- **Purpose**: Control chart proportions
- **Options**: Auto (default) / 16:9 (Widescreen) / 4:3 (Standard) / 1:1 (Square)
- **Immediate Effects**:
  - **Chart Proportions**: Width-to-height ratio changes
  - **Visual Consistency**: Uniform chart shapes
- **Impact Locations**:
  - ✅ **All Charts**: Consistent proportions across application
  - ✅ **Report Generation**: Standardized chart formats
- **When to Use**:
  - **Auto**: Let system determine best ratio
  - **16:9**: Widescreen displays, trend analysis
  - **4:3**: Traditional displays, balanced view
  - **1:1**: Square charts, equal emphasis on both axes
- **Verification**: Change ratio and see immediate proportion changes

##### **Chart Margins**
- **Setting**: `charts.layout.marginSize`
- **Purpose**: Control spacing around charts
- **Options**: Compact / Standard (default) / Spacious
- **Immediate Effects**:
  - **Chart Spacing**: Padding around chart elements
  - **Label Space**: Room for axis labels and titles
- **Impact Locations**:
  - ✅ **All Charts**: Spacing consistency
  - ✅ **Dense Dashboards**: Space optimization
- **When to Use**:
  - **Compact**: Maximize chart area, minimal spacing
  - **Standard**: Balanced spacing and readability
  - **Spacious**: Maximum readability, generous spacing
- **Verification**: Change setting and observe immediate spacing changes

#### **Data Display Options**

##### **Show Data Labels**
- **Setting**: `charts.dataDisplay.showDataLabels`
- **Purpose**: Display values directly on chart elements
- **Options**: Hidden (default) / Visible
- **Immediate Effects**:
  - **Chart Elements**: Values appear on bars, points, slices
  - **Data Clarity**: Exact values visible without hovering
- **Impact Locations**:
  - ✅ **Bar Charts**: Values on top of bars
  - ✅ **Line Charts**: Values at data points
  - ✅ **Pie Charts**: Percentages on slices
- **When to Use**:
  - **Show** for precise value communication
  - **Show** for presentation and reporting
  - **Hide** to reduce visual clutter
- **Verification**: Toggle and see immediate label appearance/disappearance

##### **Show Grid Lines**
- **Setting**: `charts.dataDisplay.gridLines`
- **Purpose**: Display chart grid lines for easier value reading
- **Options**: Visible (default) / Hidden
- **Immediate Effects**:
  - **Chart Background**: Grid lines appear/disappear
  - **Value Reading**: Easier to estimate values
- **Impact Locations**:
  - ✅ **All Charts**: Background grid visibility
  - ✅ **Data Analysis**: Value estimation assistance
- **When to Use**:
  - **Show** for precise data reading
  - **Hide** for cleaner, minimalist appearance
- **Verification**: Toggle and observe immediate grid line changes

##### **Show Data Points**
- **Setting**: `charts.dataDisplay.showDataPoints`
- **Purpose**: Display individual data points on line charts
- **Options**: Visible (default) / Hidden
- **Immediate Effects**:
  - **Line Charts**: Dots appear/disappear at data points
  - **Data Precision**: Exact data point locations visible
- **Impact Locations**:
  - ✅ **Line Charts**: All trend visualizations
  - ✅ **Time Series**: Production and performance trends
- **When to Use**:
  - **Show** for precise data point identification
  - **Hide** for smoother line appearance
- **Verification**: Toggle and see immediate point visibility changes

##### **Zero-Based Y-Axis**
- **Setting**: `charts.dataDisplay.zeroBased`
- **Purpose**: Force Y-axis to start from zero
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Y-Axis Scale**: Axis starts from zero or data minimum
  - **Data Perspective**: Changes visual emphasis of differences
- **Impact Locations**:
  - ✅ **All Charts**: Y-axis scaling
  - ✅ **Data Comparison**: Visual proportion accuracy
- **When to Use**:
  - **Enable** for accurate proportion representation
  - **Disable** to emphasize small differences in data
- **Verification**: Toggle and observe immediate axis scaling changes

#### **Chart Interaction**

##### **Enable Zoom**
- **Setting**: `charts.interaction.enableZoom`
- **Purpose**: Allow users to zoom into chart data
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Chart Interaction**: Zoom functionality available/unavailable
  - **Data Exploration**: Detailed view capability
- **Impact Locations**:
  - ✅ **All Interactive Charts**: Zoom capability
  - ✅ **Large Dataset Charts**: Detailed exploration
- **When to Use**:
  - **Enable** for detailed data analysis
  - **Disable** to prevent accidental zooming
- **Verification**: Toggle and test zoom functionality on charts

##### **Hover Effects**
- **Setting**: `charts.interaction.hoverEffects`
- **Purpose**: Highlight chart elements on mouse hover
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Chart Elements**: Highlighting on hover
  - **User Feedback**: Visual interaction confirmation
- **Impact Locations**:
  - ✅ **All Charts**: Hover highlighting
  - ✅ **Interactive Elements**: Bars, points, slices
- **When to Use**:
  - **Enable** for better user interaction
  - **Disable** for performance on slower devices
- **Verification**: Toggle and test hover effects on chart elements

##### **Tooltip Style**
- **Setting**: `charts.interaction.tooltipStyle`
- **Purpose**: Control detail level of chart tooltips
- **Options**: Minimal / Standard (default) / Detailed
- **Immediate Effects**:
  - **Tooltip Content**: Information level changes
  - **Tooltip Size**: More/less information displayed
- **Impact Locations**:
  - ✅ **All Chart Tooltips**: Information display
  - ✅ **Data Exploration**: Detail level
- **When to Use**:
  - **Minimal**: Basic value only
  - **Standard**: Value with context
  - **Detailed**: Full information with metadata
- **Verification**: Change setting and hover over chart elements to see tooltip changes

---

## 📋 **Table Settings**

### **Default Page Size**
- **Setting**: `tables.defaultPageSize`
- **Purpose**: Set number of rows displayed per page in data tables
- **Options**: 10 / 20 (default) / 50 / 100 rows
- **Immediate Effects**:
  - **All Tables**: Pagination changes instantly
  - **Data Visibility**: More/fewer rows visible at once
- **Impact Locations**:
  - ✅ **Production Data Tables**: Machine performance data
  - ✅ **Arrets Tables**: Downtime incident lists
  - ✅ **Settings Preview**: Demonstration table
- **When to Use**:
  - **10-20 rows**: Slower connections, focused viewing
  - **50-100 rows**: Fast connections, overview needs
- **Verification**: Change setting and observe immediate table pagination changes

### **Page Size Options**
- **Setting**: `tables.pageSizeOptions`
- **Purpose**: Available page size choices in pagination dropdown
- **Options**: Array of available sizes [10, 20, 50, 100]
- **Immediate Effects**:
  - **Pagination Controls**: Dropdown options change
  - **User Flexibility**: More/fewer size choices
- **Impact Locations**:
  - ✅ **All Table Pagination**: Size selection dropdown
- **When to Use**: Customize based on typical data viewing needs
- **Verification**: Check pagination dropdown for available options

### **Virtualization Threshold**
- **Setting**: `tables.virtualizationThreshold`
- **Purpose**: Enable virtual scrolling for large datasets
- **Options**: 50-1000 rows (default: 100)
- **Immediate Effects**:
  - **Large Tables**: Performance optimization kicks in
  - **Scrolling**: Virtual scrolling for datasets above threshold
- **Impact Locations**:
  - ✅ **Large Data Tables**: Production history, machine logs
  - ✅ **Performance**: Smooth scrolling with large datasets
- **When to Use**:
  - **Lower values (50-100)**: Slower devices, better performance
  - **Higher values (500-1000)**: Powerful devices, more features
- **Verification**: Test with large datasets to observe performance differences

### **Show Quick Jumper**
- **Setting**: `tables.showQuickJumper`
- **Purpose**: Display page jump input in table pagination
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Pagination Controls**: Jump-to-page input appears/disappears
  - **Navigation**: Quick page access available/unavailable
- **Impact Locations**:
  - ✅ **All Table Pagination**: Quick jump input
  - ✅ **Large Tables**: Fast navigation capability
- **When to Use**:
  - **Enable** for large datasets requiring quick navigation
  - **Disable** for simpler, cleaner pagination interface
- **Verification**: Toggle and observe pagination control changes

---

## 🔄 **Refresh Settings**

### **Dashboard Refresh Interval**
- **Setting**: `refresh.dashboardInterval`
- **Purpose**: How often dashboard data refreshes automatically
- **Options**: 30 seconds to 1 hour (default: 5 minutes)
- **Immediate Effects**:
  - **Auto-Refresh Timer**: New interval takes effect immediately
  - **Data Updates**: Dashboard refreshes at new frequency
- **Impact Locations**:
  - ✅ **Production Dashboard**: All production metrics
  - ✅ **Performance Dashboard**: Machine performance data
  - ✅ **Home Dashboard**: Overview statistics
- **When to Use**:
  - **Shorter intervals (30s-2min)**: Critical monitoring, real-time needs
  - **Longer intervals (10min-1hr)**: Stable operations, battery saving
- **Verification**: Change setting and observe refresh timing changes

### **Real-time Data Interval**
- **Setting**: `refresh.realtimeInterval`
- **Purpose**: How often real-time data components update
- **Options**: 10 seconds to 10 minutes (default: 1 minute)
- **Immediate Effects**:
  - **Real-time Components**: Update frequency changes
  - **Live Data**: More/less frequent updates
- **Impact Locations**:
  - ✅ **Live Production Data**: Current machine status
  - ✅ **Real-time Charts**: Live data visualizations
  - ✅ **Status Indicators**: Current system state
- **When to Use**:
  - **Shorter intervals (10-30s)**: Critical monitoring
  - **Longer intervals (5-10min)**: Stable operations
- **Verification**: Monitor real-time components for update frequency changes

### **Auto Refresh Enabled**
- **Setting**: `refresh.autoRefreshEnabled`
- **Purpose**: Enable/disable automatic data refreshing
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Auto-Refresh**: Starts/stops immediately
  - **Manual Control**: User must refresh manually when disabled
- **Impact Locations**:
  - ✅ **All Dashboards**: Automatic data updates
  - ✅ **Background Processes**: Data fetching
- **When to Use**:
  - **Enable** for live monitoring and current data
  - **Disable** for static analysis or battery saving
- **Verification**: Toggle and observe automatic refresh behavior

### **Background Refresh**
- **Setting**: `refresh.backgroundRefresh`
- **Purpose**: Continue refreshing data when tab is not active
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Background Updates**: Data updates when tab inactive
  - **Resource Usage**: CPU/network usage in background
- **Impact Locations**:
  - ✅ **Browser Tab Management**: Background data fetching
  - ✅ **Multi-tab Usage**: Data stays current across tabs
- **When to Use**:
  - **Enable** for continuous monitoring across multiple tabs
  - **Disable** to save resources when not actively viewing
- **Verification**: Switch tabs and return to observe data freshness

---

## 🔔 **Notification Settings**

### **Notification Categories**
- **Setting**: `notifications.categories.*`
- **Purpose**: Control which types of notifications you receive
- **Categories Available**:
  - **Machine Alert**: Equipment malfunction notifications
  - **Production**: Production target and milestone notifications
  - **Quality**: Quality control and inspection alerts
  - **Maintenance**: Scheduled and emergency maintenance notifications
  - **Alert**: General system alerts and warnings
  - **Info**: Informational updates and system messages
  - **Updates**: System updates and feature announcements
- **Immediate Effects**:
  - **Notification Filtering**: Only enabled categories show notifications
  - **Notification Count**: Fewer/more notifications based on selection
- **Impact Locations**:
  - ✅ **Notification Panel**: Real-time notification filtering
  - ✅ **Alert System**: System-wide notification filtering
  - ✅ **Email Notifications**: Category-based email filtering
- **When to Use**:
  - **Enable Critical Categories**: Machine Alert, Production for operations
  - **Disable Non-Essential**: Info, Updates for focused work
  - **Customize by Role**: Different categories for different job functions
- **Verification**: Toggle categories and observe notification filtering

### **Priority Levels**
- **Setting**: `notifications.priorities.*`
- **Purpose**: Control which priority levels of notifications to receive
- **Levels Available**:
  - **Critical**: Immediate attention required (equipment failure, safety)
  - **High**: Important but not immediate (production delays, quality issues)
  - **Medium**: Standard notifications (maintenance reminders, updates)
  - **Low**: Informational notifications (system status, tips)
- **Immediate Effects**:
  - **Priority Filtering**: Only enabled priorities show notifications
  - **Alert Urgency**: Focus on most important notifications
- **Impact Locations**:
  - ✅ **Notification System**: Priority-based filtering
  - ✅ **Alert Urgency**: Visual priority indicators
- **When to Use**:
  - **Critical + High Only**: High-pressure operations, focused monitoring
  - **All Levels**: Comprehensive monitoring, training environments
- **Verification**: Toggle priorities and observe notification filtering

### **Delivery Methods**
- **Setting**: `notifications.deliveryMethods.*`
- **Purpose**: Choose how you receive notifications
- **Methods Available**:
  - **SSE (Server-Sent Events)**: Real-time browser notifications
  - **Email**: Email delivery of notifications
  - **Browser**: Browser push notifications
- **Immediate Effects**:
  - **Notification Channels**: Enabled methods receive notifications
  - **Multi-Channel**: Same notification via multiple methods
- **Impact Locations**:
  - ✅ **Real-time Notifications**: SSE delivery
  - ✅ **Email System**: Email notification delivery
  - ✅ **Browser Notifications**: Push notification system
- **When to Use**:
  - **SSE**: Real-time monitoring, active application use
  - **Email**: Offline notifications, record keeping
  - **Browser**: Background notifications, multi-tab usage
- **Verification**: Toggle methods and test notification delivery

### **Notification Behavior**
- **Setting**: `notifications.behavior.*`
- **Purpose**: Control how notifications appear and behave
- **Options Available**:
  - **Sound**: Audio notification alerts (enabled/disabled)
  - **Auto Close**: Automatically dismiss notifications (enabled/disabled)
  - **Auto Close Delay**: Time before auto-dismissal (1-30 seconds)
  - **Max Visible**: Maximum notifications shown at once (1-10)
- **Immediate Effects**:
  - **Sound**: Audio alerts play/don't play
  - **Auto Close**: Notifications dismiss automatically/manually
  - **Display Limit**: Number of simultaneous notifications
- **Impact Locations**:
  - ✅ **Notification Display**: Visual notification behavior
  - ✅ **Audio System**: Sound alert system
  - ✅ **User Interface**: Notification management
- **When to Use**:
  - **Sound On**: Critical monitoring, audio alerts needed
  - **Auto Close**: High-volume notifications, automatic management
  - **Manual Close**: Important notifications requiring acknowledgment
- **Verification**: Trigger notifications and observe behavior changes

---

## 📧 **Email Settings**

### **Basic Email Configuration**

#### **Enable Email Notifications**
- **Setting**: `email.enabled`
- **Purpose**: Master switch for all email notifications
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Email System**: All email notifications start/stop
  - **Email Settings**: Additional options become available/unavailable
- **Impact Locations**:
  - ✅ **Email Delivery System**: Master email control
  - ✅ **Notification System**: Email channel availability
- **When to Use**:
  - **Enable**: Need offline notifications, email records
  - **Disable**: Reduce email volume, use only real-time notifications
- **Verification**: Toggle and observe email system activation

#### **Email Frequency**
- **Setting**: `email.frequency`
- **Purpose**: Control how often email notifications are sent
- **Options**: Immediate / Hourly Batch / Daily Digest
- **Immediate Effects**:
  - **Email Timing**: Delivery frequency changes immediately
  - **Email Grouping**: Single emails vs. batched notifications
- **Impact Locations**:
  - ✅ **Email Delivery**: Timing and grouping of emails
  - ✅ **Notification Processing**: Batching behavior
- **When to Use**:
  - **Immediate**: Critical monitoring, real-time needs
  - **Hourly Batch**: Balanced approach, reduced email volume
  - **Daily Digest**: Summary approach, minimal email interruption
- **Verification**: Change frequency and observe email delivery timing

#### **Email Template**
- **Setting**: `email.template`
- **Purpose**: Choose email formatting and detail level
- **Options**: Minimal / Standard (default) / Detailed
- **Immediate Effects**:
  - **Email Content**: Information level and formatting changes
  - **Email Length**: Shorter/longer emails based on template
- **Impact Locations**:
  - ✅ **Email Content**: Template formatting and information level
  - ✅ **Email Appearance**: Visual design and layout
- **When to Use**:
  - **Minimal**: Quick alerts, mobile-friendly
  - **Standard**: Balanced information and readability
  - **Detailed**: Comprehensive information, analysis needs
- **Verification**: Send test emails and observe template differences

### **Advanced Email Configuration**

#### **Email Format**
- **Setting**: `email.format`
- **Purpose**: Choose email content format
- **Options**: HTML (Rich formatting) / Plain Text / Both HTML and Text
- **Immediate Effects**:
  - **Email Appearance**: Rich formatting vs. plain text
  - **Compatibility**: Better compatibility with different email clients
- **Impact Locations**:
  - ✅ **Email Rendering**: Visual appearance and formatting
  - ✅ **Email Client Compatibility**: Support across different email systems
- **When to Use**:
  - **HTML**: Rich formatting, charts, and visual elements
  - **Plain Text**: Maximum compatibility, simple formatting
  - **Both**: Best compatibility with fallback options
- **Verification**: Send test emails and check formatting in different email clients

#### **Email Language**
- **Setting**: `email.language`
- **Purpose**: Set language for email notifications
- **Options**: Français (default) / English / Español
- **Immediate Effects**:
  - **Email Content**: All text changes to selected language
  - **Date/Time Formats**: Localized formatting
- **Impact Locations**:
  - ✅ **Email Text**: All notification content language
  - ✅ **Email Templates**: Template text localization
- **When to Use**: Match user's preferred language or organizational requirements
- **Verification**: Send test emails and verify language content

#### **Include Attachments**
- **Setting**: `email.attachments.enabled`
- **Purpose**: Attach relevant files to email notifications
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Email Size**: Larger emails with attachments
  - **Information Access**: Direct access to relevant files
- **Impact Locations**:
  - ✅ **Email Attachments**: File inclusion in notifications
  - ✅ **Email Size**: Impact on email delivery and storage
- **When to Use**:
  - **Enable**: Need immediate access to reports, charts, data files
  - **Disable**: Reduce email size, faster delivery
- **Verification**: Send test emails and check for attachment inclusion

### **Email Signature**

#### **Email Signature**
- **Setting**: `email.signature.enabled`
- **Purpose**: Include custom signature in all emails
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Email Footer**: Custom signature appears/disappears
  - **Professional Appearance**: Branded email communications
- **Impact Locations**:
  - ✅ **Email Footer**: Signature inclusion in all emails
  - ✅ **Email Branding**: Professional appearance
- **When to Use**:
  - **Enable**: Professional communications, branding requirements
  - **Disable**: Simpler emails, automated system notifications
- **Verification**: Send test emails and check signature inclusion

#### **Custom Signature Text**
- **Setting**: `email.signature.text`
- **Purpose**: Define custom signature content
- **Options**: Multi-line text input
- **Immediate Effects**:
  - **Signature Content**: Text appears in email signature
  - **Personalization**: Custom contact information and branding
- **Impact Locations**:
  - ✅ **Email Signature**: Custom text content
- **When to Use**: Include contact information, job title, company branding
- **Verification**: Update signature text and send test email

### **Email Filtering & Rules**

#### **Minimum Priority Level**
- **Setting**: `email.filtering.minPriority`
- **Purpose**: Only send emails for notifications at or above this priority
- **Options**: Low and above / Medium and above / High and above / Critical only
- **Immediate Effects**:
  - **Email Volume**: Fewer/more emails based on priority threshold
  - **Email Relevance**: Only important notifications via email
- **Impact Locations**:
  - ✅ **Email Filtering**: Priority-based email delivery
  - ✅ **Email Volume**: Reduction in low-priority emails
- **When to Use**:
  - **Critical Only**: Emergency notifications only
  - **High and Above**: Important notifications only
  - **Medium and Above**: Balanced approach
  - **Low and Above**: All notifications via email
- **Verification**: Generate notifications of different priorities and observe email delivery

#### **Maximum Emails per Day**
- **Setting**: `email.filtering.maxPerDay`
- **Purpose**: Limit daily email notifications to prevent inbox flooding
- **Options**: 1-200 emails per day (default: 50)
- **Immediate Effects**:
  - **Email Limiting**: Daily email count restriction
  - **Inbox Protection**: Prevents email overload
- **Impact Locations**:
  - ✅ **Email Delivery**: Daily volume control
  - ✅ **Email Management**: Inbox protection
- **When to Use**:
  - **Lower Limits (1-20)**: Minimal email, critical only
  - **Higher Limits (50-200)**: Comprehensive email notifications
- **Verification**: Monitor daily email count against set limit

#### **Duplicate Prevention**
- **Setting**: `email.filtering.preventDuplicates`
- **Purpose**: Prevent sending duplicate notifications via email
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Email Deduplication**: Duplicate emails blocked/allowed
  - **Email Efficiency**: Reduced redundant communications
- **Impact Locations**:
  - ✅ **Email Processing**: Duplicate detection and prevention
  - ✅ **Email Volume**: Reduction in redundant emails
- **When to Use**:
  - **Enable**: Reduce email clutter, prevent redundancy
  - **Disable**: Ensure all notifications are sent (even duplicates)
- **Verification**: Generate duplicate notifications and observe email delivery

#### **Smart Grouping**
- **Setting**: `email.filtering.smartGrouping`
- **Purpose**: Group similar notifications together in single emails
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Email Consolidation**: Multiple notifications in single email
  - **Email Organization**: Better structured email content
- **Impact Locations**:
  - ✅ **Email Content**: Grouped notification presentation
  - ✅ **Email Volume**: Fewer emails with more content
- **When to Use**:
  - **Enable**: Reduce email volume, better organization
  - **Disable**: Individual emails for each notification
- **Verification**: Generate multiple similar notifications and observe email grouping

### **Email Delivery Options**

#### **Delivery Method**
- **Setting**: `email.delivery.method`
- **Purpose**: Control how email notifications are delivered
- **Options**: Immediate (default) / Scheduled / Batch Processing
- **Immediate Effects**:
  - **Email Timing**: Delivery timing strategy changes
  - **Email Processing**: Different delivery mechanisms
- **Impact Locations**:
  - ✅ **Email Delivery System**: Timing and processing method
  - ✅ **Email Performance**: Delivery speed and reliability
- **When to Use**:
  - **Immediate**: Real-time email delivery
  - **Scheduled**: Specific time-based delivery
  - **Batch**: Efficient processing for high volumes
- **Verification**: Send notifications and observe delivery timing

#### **Retry Failed Emails**
- **Setting**: `email.delivery.retryFailed`
- **Purpose**: Automatically retry sending failed emails
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Email Reliability**: Failed emails retried/not retried
  - **Email Delivery**: Higher success rate with retries
- **Impact Locations**:
  - ✅ **Email Delivery System**: Retry mechanism
  - ✅ **Email Reliability**: Improved delivery success
- **When to Use**:
  - **Enable**: Ensure important notifications are delivered
  - **Disable**: Reduce system load, accept delivery failures
- **Verification**: Simulate email failures and observe retry behavior

#### **Read Receipts**
- **Setting**: `email.delivery.readReceipts`
- **Purpose**: Request read receipts for sent emails
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Email Headers**: Read receipt requests added/removed
  - **Email Tracking**: Ability to track email opens
- **Impact Locations**:
  - ✅ **Email Headers**: Read receipt request inclusion
  - ✅ **Email Tracking**: Open tracking capability
- **When to Use**:
  - **Enable**: Track important notification delivery and reading
  - **Disable**: Respect recipient privacy, reduce email complexity
- **Verification**: Send emails and check for read receipt requests

#### **Email Tracking**
- **Setting**: `email.delivery.tracking`
- **Purpose**: Track email opens and link clicks
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Email Analytics**: Tracking pixels and links added/removed
  - **Email Insights**: Open and click tracking data
- **Impact Locations**:
  - ✅ **Email Content**: Tracking elements inclusion
  - ✅ **Email Analytics**: Open and click data collection
- **When to Use**:
  - **Enable**: Analyze email effectiveness, engagement tracking
  - **Disable**: Respect privacy, simpler email content
- **Verification**: Send tracked emails and monitor analytics data

### **Enhanced Batch Settings**

#### **Hourly Batch Configuration**
- **Setting**: `email.batchSettings.hourlyBatch.*`
- **Purpose**: Configure hourly email batching
- **Options**:
  - **Enabled**: Hourly batching on/off
  - **Max Notifications**: 1-100 notifications per hour
- **Immediate Effects**:
  - **Email Batching**: Hourly grouping of notifications
  - **Email Volume**: Controlled hourly email delivery
- **Impact Locations**:
  - ✅ **Email Scheduling**: Hourly batch processing
  - ✅ **Email Volume Control**: Hourly delivery limits
- **When to Use**:
  - **Enable**: Reduce email frequency, batch similar notifications
  - **Disable**: Individual email delivery for each notification
- **Verification**: Generate multiple notifications within an hour and observe batching

#### **Daily Digest Configuration**
- **Setting**: `email.batchSettings.dailyDigest.*`
- **Purpose**: Configure daily email digest
- **Options**:
  - **Enabled**: Daily digest on/off
  - **Digest Time**: 00:00-23:00 (time to send daily digest)
- **Immediate Effects**:
  - **Daily Summary**: Single daily email with all notifications
  - **Email Timing**: Specific time for digest delivery
- **Impact Locations**:
  - ✅ **Email Scheduling**: Daily digest timing
  - ✅ **Email Content**: Comprehensive daily summary
- **When to Use**:
  - **Enable**: Single daily summary, minimal email interruption
  - **Disable**: More frequent email delivery
- **Verification**: Configure digest time and observe daily email delivery

---

## 📄 **Report Settings**

### **Report Generation Settings**

#### **Auto Generate Reports**
- **Setting**: `reports.generation.autoGenerate`
- **Purpose**: Automatically generate reports based on schedules
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Report Automation**: Automatic report creation starts/stops
  - **Report Scheduling**: Scheduled reports become active/inactive
- **Impact Locations**:
  - ✅ **Report System**: Automated report generation
  - ✅ **Report Scheduling**: Background report creation
- **When to Use**:
  - **Enable**: Regular reporting needs, automated workflows
  - **Disable**: Manual report generation only
- **Verification**: Enable and check for scheduled report generation

#### **Report Format**
- **Setting**: `reports.generation.format`
- **Purpose**: Default format for generated reports
- **Options**: PDF (default) / HTML / Excel
- **Immediate Effects**:
  - **Report Output**: File format for generated reports
  - **Report Compatibility**: Different format capabilities
- **Impact Locations**:
  - ✅ **Report Generation**: Output file format
  - ✅ **Report Distribution**: Format compatibility
- **When to Use**:
  - **PDF**: Professional reports, printing, archiving
  - **HTML**: Web viewing, interactive elements
  - **Excel**: Data analysis, spreadsheet manipulation
- **Verification**: Generate reports and verify output format

#### **Report Quality**
- **Setting**: `reports.generation.quality`
- **Purpose**: Quality level for generated reports
- **Options**: Low (Fast) / Standard (default) / High (Slow)
- **Immediate Effects**:
  - **Report Generation Speed**: Faster/slower report creation
  - **Report Quality**: Different visual quality and detail levels
- **Impact Locations**:
  - ✅ **Report Processing**: Generation speed and quality
  - ✅ **Report Appearance**: Visual quality and detail
- **When to Use**:
  - **Low**: Quick reports, draft versions
  - **Standard**: Balanced quality and speed
  - **High**: Final reports, presentations, archiving
- **Verification**: Generate reports with different quality settings and compare

#### **Include Charts**
- **Setting**: `reports.generation.includeCharts`
- **Purpose**: Include charts and visualizations in reports
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Report Content**: Charts included/excluded from reports
  - **Report Size**: Larger/smaller reports based on chart inclusion
- **Impact Locations**:
  - ✅ **Report Content**: Chart and visualization inclusion
  - ✅ **Report File Size**: Impact on report size and generation time
- **When to Use**:
  - **Enable**: Visual reports, data visualization needs
  - **Disable**: Text-only reports, smaller file sizes
- **Verification**: Generate reports and check for chart inclusion

#### **Include Tables**
- **Setting**: `reports.generation.includeTables`
- **Purpose**: Include data tables in reports
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Report Content**: Data tables included/excluded
  - **Report Detail**: More/less detailed data presentation
- **Impact Locations**:
  - ✅ **Report Content**: Data table inclusion
  - ✅ **Report Detail Level**: Comprehensive vs. summary data
- **When to Use**:
  - **Enable**: Detailed reports, data analysis needs
  - **Disable**: Summary reports, visual-only presentations
- **Verification**: Generate reports and check for table inclusion

### **Report Scheduling**

#### **Daily Reports**
- **Setting**: `reports.schedules.daily.*`
- **Purpose**: Configure daily report generation
- **Options**:
  - **Enabled**: Daily reports on/off
  - **Time**: 00:00-23:00 (time to generate daily reports)
- **Immediate Effects**:
  - **Daily Automation**: Daily report generation starts/stops
  - **Report Timing**: Specific time for daily report creation
- **Impact Locations**:
  - ✅ **Report Scheduling**: Daily report automation
  - ✅ **Report Delivery**: Consistent daily reporting
- **When to Use**:
  - **Enable**: Daily operational reports, regular monitoring
  - **Disable**: Manual or different frequency reporting
- **Verification**: Enable daily reports and monitor generation at scheduled time

#### **Weekly Reports**
- **Setting**: `reports.schedules.weekly.*`
- **Purpose**: Configure weekly report generation
- **Options**:
  - **Enabled**: Weekly reports on/off
  - **Day**: Monday-Sunday (day of week for report generation)
- **Immediate Effects**:
  - **Weekly Automation**: Weekly report generation starts/stops
  - **Report Day**: Specific day for weekly report creation
- **Impact Locations**:
  - ✅ **Report Scheduling**: Weekly report automation
  - ✅ **Report Delivery**: Consistent weekly reporting
- **When to Use**:
  - **Enable**: Weekly summary reports, management reporting
  - **Disable**: Manual or different frequency reporting
- **Verification**: Enable weekly reports and monitor generation on scheduled day

#### **Monthly Reports**
- **Setting**: `reports.schedules.monthly.*`
- **Purpose**: Configure monthly report generation
- **Options**:
  - **Enabled**: Monthly reports on/off
  - **Day**: 1-31 (day of month for report generation)
- **Immediate Effects**:
  - **Monthly Automation**: Monthly report generation starts/stops
  - **Report Date**: Specific day of month for report creation
- **Impact Locations**:
  - ✅ **Report Scheduling**: Monthly report automation
  - ✅ **Report Delivery**: Consistent monthly reporting
- **When to Use**:
  - **Enable**: Monthly summary reports, executive reporting
  - **Disable**: Manual or different frequency reporting
- **Verification**: Enable monthly reports and monitor generation on scheduled date

---

## ⚡ **Performance Settings**

### **Caching Configuration**

#### **Enable Caching**
- **Setting**: `performance.caching.enabled`
- **Purpose**: Cache data to improve application performance
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Data Loading**: Faster/slower data loading from cache/server
  - **Network Usage**: Reduced/increased network requests
- **Impact Locations**:
  - ✅ **Data Loading**: All data fetching operations
  - ✅ **Application Performance**: Overall speed and responsiveness
- **When to Use**:
  - **Enable**: Improve performance, reduce server load
  - **Disable**: Always get fresh data, troubleshooting
- **Verification**: Monitor data loading speeds and network requests

#### **Cache Duration**
- **Setting**: `performance.caching.duration`
- **Purpose**: How long to cache data before refreshing
- **Options**: 60 seconds to 1 hour (default: 5 minutes)
- **Immediate Effects**:
  - **Cache Freshness**: More/less frequent cache updates
  - **Data Currency**: Newer/older cached data
- **Impact Locations**:
  - ✅ **Cached Data**: Freshness of cached information
  - ✅ **Performance Balance**: Speed vs. data currency
- **When to Use**:
  - **Shorter Duration (1-5min)**: Frequently changing data
  - **Longer Duration (15-60min)**: Stable data, better performance
- **Verification**: Monitor cache refresh timing and data freshness

#### **Cache Strategy**
- **Setting**: `performance.caching.strategy`
- **Purpose**: Caching strategy selection
- **Options**: None / Basic / Smart (default) / Aggressive
- **Immediate Effects**:
  - **Caching Behavior**: Different caching algorithms and policies
  - **Performance Impact**: Varying levels of performance optimization
- **Impact Locations**:
  - ✅ **Caching System**: Cache management and optimization
  - ✅ **Application Performance**: Different performance characteristics
- **When to Use**:
  - **None**: No caching, always fresh data
  - **Basic**: Simple caching for basic performance
  - **Smart**: Intelligent caching based on usage patterns
  - **Aggressive**: Maximum caching for best performance
- **Verification**: Monitor caching behavior and performance metrics

### **Optimization Settings**

#### **Lazy Loading**
- **Setting**: `performance.optimization.lazyLoading`
- **Purpose**: Load content as needed rather than all at once
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Initial Load Time**: Faster/slower initial page loading
  - **Resource Usage**: Lower/higher initial resource consumption
- **Impact Locations**:
  - ✅ **Page Loading**: Initial page load performance
  - ✅ **Images and Charts**: Content loading behavior
  - ✅ **Large Data Sets**: Data loading optimization
- **When to Use**:
  - **Enable**: Improve initial load times, better user experience
  - **Disable**: Load all content immediately, simpler behavior
- **Verification**: Monitor initial page load times and content loading behavior

#### **Virtualization**
- **Setting**: `performance.optimization.virtualization`
- **Purpose**: Optimize large lists and tables for better performance
- **Options**: Enabled (default) / Disabled
- **Immediate Effects**:
  - **Large Data Performance**: Better/worse performance with large datasets
  - **Memory Usage**: Lower/higher memory consumption
- **Impact Locations**:
  - ✅ **Large Tables**: Data table performance with many rows
  - ✅ **Long Lists**: List component performance
  - ✅ **Memory Usage**: Application memory consumption
- **When to Use**:
  - **Enable**: Working with large datasets, performance optimization
  - **Disable**: Small datasets, simpler rendering
- **Verification**: Test performance with large datasets and monitor memory usage

#### **Compression**
- **Setting**: `performance.optimization.compression`
- **Purpose**: Compress data transfers to reduce bandwidth usage
- **Options**: Enabled / Disabled (default)
- **Immediate Effects**:
  - **Network Usage**: Reduced/normal bandwidth consumption
  - **Transfer Speed**: Faster/normal data transfer (depending on connection)
- **Impact Locations**:
  - ✅ **Data Transfer**: All API requests and responses
  - ✅ **Network Performance**: Bandwidth usage and transfer speeds
- **When to Use**:
  - **Enable**: Slow connections, bandwidth limitations
  - **Disable**: Fast connections, reduce CPU usage
- **Verification**: Monitor network usage and transfer speeds

---

## 🔍 **Live Preview Tab**

### **Purpose**
The Live Preview tab demonstrates all settings effects in real-time, allowing you to see exactly how each setting impacts the application before committing to changes.

### **Features**
- **Quick Theme Controls**: Instant dark mode, compact mode, and animation toggles
- **Current Settings Status**: Real-time display of all current setting values
- **Sample Data Visualization**: Live charts and tables that respond to setting changes
- **Settings Effects Demo**: Detailed explanations of what each setting does
- **Immediate Feedback**: All changes are visible instantly without page refresh

### **How to Use**
1. **Navigate to Live Preview Tab**: Click the "Live Preview" tab in settings
2. **Use Quick Controls**: Toggle theme settings using the quick control buttons
3. **Observe Changes**: Watch how charts, tables, and UI elements change immediately
4. **Test Settings**: Modify settings in other tabs and return to see effects
5. **Verify Impact**: Confirm settings work as expected before finalizing

### **Verification Process**
- **Visual Confirmation**: See immediate visual changes
- **Functional Testing**: Test interactive elements like charts and tables
- **Cross-Tab Validation**: Changes persist across different settings tabs
- **Real-time Updates**: No page refresh required for any changes

---

## 🎯 **Quick Reference**

### **Most Commonly Used Settings**
1. **Dark Mode** (`theme.darkMode`) - Essential for different lighting conditions
2. **Default Page Size** (`tables.defaultPageSize`) - Affects all data viewing
3. **Auto Refresh** (`refresh.autoRefreshEnabled`) - Critical for live monitoring
4. **Email Notifications** (`email.enabled`) - Important for offline alerts
5. **Chart Type** (`charts.defaultType`) - Affects data visualization

### **Performance Impact Settings**
- **Performance Mode** (`charts.performanceMode`) - For large datasets
- **Caching** (`performance.caching.enabled`) - Overall application speed
- **Virtualization** (`performance.optimization.virtualization`) - Large table performance
- **Lazy Loading** (`performance.optimization.lazyLoading`) - Initial load speed

### **Troubleshooting**
- **Settings Not Saving**: Check network connection and try again
- **No Visual Changes**: Verify setting is enabled and refresh if needed
- **Performance Issues**: Enable performance mode and caching
- **Too Many Notifications**: Adjust categories, priorities, and frequency settings

### **Best Practices**
- **Start with Defaults**: Default settings work well for most users
- **Customize Gradually**: Change one setting at a time to understand impact
- **Use Live Preview**: Always test settings in Live Preview tab first
- **Monitor Performance**: Adjust performance settings based on your device and connection
- **Regular Review**: Periodically review settings as your needs change

---

*This comprehensive guide covers all 67 settings in the SOMIPEM application. Each setting produces immediate, visible effects as documented. For additional support or questions about specific settings, consult your system administrator or refer to the application's help documentation.*
