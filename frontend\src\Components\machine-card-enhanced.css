.machine-card-container {
    position: relative;
    transition: all 0.3s;
  }
  
  .machine-card-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .machine-blur-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(4px);
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
    transition: all 0.3s ease;
  }
  
  .machine-blur-overlay:hover {
    backdrop-filter: blur(2px);
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .soon-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(6px);
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
    border: 2px dashed var(--somipem-primary-blue, #1E3A8A);
    transition: all 0.3s ease;
  }
  
  .soon-overlay:hover {
    background-color: rgba(0, 0, 0, 0.15);
    border-color: var(--somipem-secondary-blue, #3B82F6);
  }
  
  .soon-text {
    font-size: 28px;
    font-weight: bold;
    color: var(--somipem-primary-blue, #1E3A8A);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
  }
  