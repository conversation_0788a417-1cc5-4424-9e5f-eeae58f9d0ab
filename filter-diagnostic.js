/**
 * Filter System Diagnostic and Fix Script
 * 
 * This script tests the current filter system functionality and identifies specific issues.
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:5000';

console.log('🔍 PRODUCTION DASHBOARD FILTER SYSTEM DIAGNOSTIC');
console.log('='.repeat(60));

// Test 1: Check if basic API endpoints are working
async function testBasicAPIEndpoints() {
  console.log('\n📡 Testing Basic API Endpoints...');
  
  try {
    // Test machine models endpoint
    const modelsResponse = await fetch(`${BACKEND_URL}/api/machine-models`);
    const models = await modelsResponse.json();
    console.log('✅ Machine Models API:', models.length > 0 ? `${models.length} models found` : 'No models found');
    
    // Test machine names endpoint
    const namesResponse = await fetch(`${BACKEND_URL}/api/machine-names`);
    const names = await namesResponse.json();
    console.log('✅ Machine Names API:', names.length > 0 ? `${names.length} machines found` : 'No machines found');
    
    return { models, names };
  } catch (error) {
    console.error('❌ Basic API Error:', error.message);
    return { models: [], names: [] };
  }
}

// Test 2: Check GraphQL endpoints without filters
async function testGraphQLBaseline() {
  console.log('\n🔄 Testing GraphQL Baseline (No Filters)...');
  
  const query = `
    query {
      enhancedGetProductionChart {
        data {
          Date_Insert_Day
          Total_Good_Qty_Day
          Total_Rejects_Qty_Day
        }
        dataSource
      }
    }
  `;
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    const result = await response.json();
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return null;
    }
    
    const chartData = result.data?.enhancedGetProductionChart?.data || [];
    console.log('✅ GraphQL Baseline:', `${chartData.length} records found`);
    if (chartData.length > 0) {
      console.log('   📊 Sample data:', chartData[0]);
    }
    
    return chartData;
  } catch (error) {
    console.error('❌ GraphQL Baseline Error:', error.message);
    return null;
  }
}

// Test 3: Check GraphQL endpoints WITH filters
async function testGraphQLWithFilters() {
  console.log('\n🎯 Testing GraphQL With Filters...');
  
  const filters = {
    model: 'IPS',
    dateRangeType: 'day',
    date: '2024-12-15'
  };
  
  const query = `
    query GetFilteredData($filters: EnhancedFilterInput) {
      enhancedGetProductionChart(filters: $filters) {
        data {
          Date_Insert_Day
          Total_Good_Qty_Day
          Total_Rejects_Qty_Day
        }
        dataSource
      }
    }
  `;
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/graphql`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query,
        variables: { filters }
      })
    });
    
    const result = await response.json();
    if (result.errors) {
      console.error('❌ GraphQL Filter Errors:', result.errors);
      return null;
    }
    
    const chartData = result.data?.enhancedGetProductionChart?.data || [];
    console.log('✅ GraphQL Filtered:', `${chartData.length} records found with filters:`, filters);
    if (chartData.length > 0) {
      console.log('   📊 Sample filtered data:', chartData[0]);
    }
    
    return chartData;
  } catch (error) {
    console.error('❌ GraphQL Filter Error:', error.message);
    return null;
  }
}

// Test 4: Check date filter variations
async function testDateFilterVariations() {
  console.log('\n📅 Testing Date Filter Variations...');
  
  const dateFilters = [
    { date: '2024-12-15', dateRangeType: 'day' },
    { date: '2024-12-15', dateRangeType: 'week' },
    { date: '2024-12-01', dateRangeType: 'month' }
  ];
  
  for (const filter of dateFilters) {
    const query = `
      query GetDateFilteredData($filters: EnhancedFilterInput) {
        enhancedGetProductionChart(filters: $filters) {
          data {
            Date_Insert_Day
          }
          dataSource
        }
      }
    `;
    
    try {
      const response = await fetch(`${BACKEND_URL}/api/graphql`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          query,
          variables: { filters: filter }
        })
      });
      
      const result = await response.json();
      const chartData = result.data?.enhancedGetProductionChart?.data || [];
      console.log(`   📈 ${filter.dateRangeType} filter (${filter.date}):`, `${chartData.length} records`);
      
    } catch (error) {
      console.error(`   ❌ Date filter error for ${filter.dateRangeType}:`, error.message);
    }
  }
}

// Test 5: Check machine filter variations
async function testMachineFilterVariations() {
  console.log('\n🏭 Testing Machine Filter Variations...');
  
  const machineFilters = [
    { model: 'IPS' },
    { model: 'CCM24' },
    { machine: 'IPS01' },
    { model: 'IPS', machine: 'IPS01' }
  ];
  
  for (const filter of machineFilters) {
    const query = `
      query GetMachineFilteredData($filters: EnhancedFilterInput) {
        enhancedGetProductionChart(filters: $filters) {
          data {
            Date_Insert_Day
          }
          dataSource
        }
      }
    `;
    
    try {
      const response = await fetch(`${BACKEND_URL}/api/graphql`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          query,
          variables: { filters: filter }
        })
      });
      
      const result = await response.json();
      const chartData = result.data?.enhancedGetProductionChart?.data || [];
      console.log(`   🔧 Filter ${JSON.stringify(filter)}:`, `${chartData.length} records`);
      
    } catch (error) {
      console.error(`   ❌ Machine filter error:`, error.message);
    }
  }
}

// Main diagnostic function
async function runFilterDiagnostic() {
  try {
    await testBasicAPIEndpoints();
    await testGraphQLBaseline();
    await testGraphQLWithFilters();
    await testDateFilterVariations();
    await testMachineFilterVariations();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 DIAGNOSTIC COMPLETE');
    console.log('\n📋 FILTER SYSTEM ISSUES IDENTIFIED:');
    console.log('1. Check if filters are properly passed to GraphQL queries');
    console.log('2. Verify date format conversion in frontend');
    console.log('3. Ensure useEffect dependencies are correct for re-fetching');
    console.log('4. Check if filter state changes trigger data refresh');
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Run the diagnostic
runFilterDiagnostic();
