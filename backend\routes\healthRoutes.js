import express from 'express';
import superAgentService from '../services/SuperAgentService.js';
import healthMonitoringService from '../services/HealthMonitoringService.js';
import { checkElasticsearchHealth } from '../config/elasticsearch.js';
import redisConfig from '../config/redisConfig.js';
import redisService from '../services/RedisService.js';
import RedisSessionService from '../services/RedisSessionService.js';
import RedisPubSubService from '../services/RedisPubSubService.js';
import elasticsearchDashboardService from '../services/elasticsearchDashboardService.js';

const router = express.Router();

/**
 * Simple health check endpoint
 * GET /api/health
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    message: 'Server is running'
  });
});

/**
 * Comprehensive health check endpoint using SuperAgent
 * GET /api/health/status
 */
router.get('/status', async (req, res) => {
  try {
    const healthStatus = await healthMonitoringService.performHealthCheck();
    
    // Set appropriate HTTP status
    const httpStatus = healthStatus.overall === 'healthy' ? 200 : 
                      healthStatus.overall === 'degraded' ? 200 : 503;

    res.status(httpStatus).json(healthStatus);
  } catch (error) {
    console.error('Health status check failed:', error);
    res.status(500).json({
      overall: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Health statistics endpoint
 * GET /api/health/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = healthMonitoringService.getHealthStatistics();
    res.json(stats);
  } catch (error) {
    console.error('Health stats retrieval failed:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Health history endpoint
 * GET /api/health/history?limit=10
 */
router.get('/history', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const history = healthMonitoringService.getHealthHistory(limit);
    
    res.json({
      total_entries: history.length,
      limit,
      history
    });
  } catch (error) {
    console.error('Health history retrieval failed:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Test connectivity to specific external service
 * GET /api/health/test/:serviceName
 */
router.get('/test/:serviceName', async (req, res) => {
  try {
    const { serviceName } = req.params;
    const result = await healthMonitoringService.testServiceConnectivity(serviceName);
    
    const httpStatus = result.connected ? 200 : 503;
    res.status(httpStatus).json(result);
  } catch (error) {
    console.error(`Service connectivity test failed for ${req.params.serviceName}:`, error);
    res.status(500).json({
      error: error.message,
      service: req.params.serviceName,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Monitor external endpoints using SuperAgent
 * POST /api/health/monitor
 * Body: { endpoints: [{ name: "service", url: "http://..." }] }
 */
router.post('/monitor', async (req, res) => {
  try {
    const { endpoints } = req.body;
    
    if (!endpoints || !Array.isArray(endpoints)) {
      return res.status(400).json({
        error: 'Invalid request body. Expected array of endpoints.',
        expected_format: {
          endpoints: [
            { name: 'service-name', url: 'http://service-url' }
          ]
        }
      });
    }

    const monitoringResults = await superAgentService.monitorEndpoints(endpoints, {
      includeMetrics: true,
      timeout: req.query.timeout ? parseInt(req.query.timeout) : 5000
    });

    res.json(monitoringResults);
  } catch (error) {
    console.error('Endpoint monitoring failed:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Quick health ping endpoint
 * GET /api/health/ping
 */
router.get('/ping', async (req, res) => {
  try {
    const currentHealth = healthMonitoringService.getCurrentHealth();
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      current_health: currentHealth.overall || 'unknown'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * External service health check using SuperAgent
 * GET /api/health/external/:serviceUrl (base64 encoded)
 */
router.get('/external/:encodedUrl', async (req, res) => {
  try {
    const { encodedUrl } = req.params;
    const serviceUrl = Buffer.from(encodedUrl, 'base64').toString();
    
    // Validate URL format
    try {
      new URL(serviceUrl);
    } catch (urlError) {
      return res.status(400).json({
        error: 'Invalid URL format',
        provided_url: serviceUrl
      });
    }

    const healthResult = await superAgentService.healthCheck(serviceUrl, {
      timeout: req.query.timeout ? parseInt(req.query.timeout) : 10000
    });

    const httpStatus = healthResult.healthy ? 200 : 503;
    res.status(httpStatus).json(healthResult);
  } catch (error) {
    console.error('External service health check failed:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Batch external service health check
 * POST /api/health/batch
 * Body: { services: [{ name: "service", url: "http://..." }] }
 */
router.post('/batch', async (req, res) => {
  try {
    const { services } = req.body;
    
    if (!services || !Array.isArray(services)) {
      return res.status(400).json({
        error: 'Invalid request body. Expected array of services.',
        expected_format: {
          services: [
            { name: 'service-name', url: 'http://service-url' }
          ]
        }
      });
    }

    const batchResults = await superAgentService.batchHealthCheck(services);
    
    const httpStatus = batchResults.overall.healthy ? 200 : 503;
    res.status(httpStatus).json(batchResults);
  } catch (error) {
    console.error('Batch health check failed:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Start health monitoring service
 * POST /api/health/monitoring/start
 */
router.post('/monitoring/start', async (req, res) => {
  try {
    const interval = req.body.interval || 300000; // 5 minutes default
    
    healthMonitoringService.startMonitoring(interval);
    
    res.json({
      message: 'Health monitoring started',
      interval,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Failed to start health monitoring:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Stop health monitoring service
 * POST /api/health/monitoring/stop
 */
router.post('/monitoring/stop', async (req, res) => {
  try {
    healthMonitoringService.stopMonitoring();
    
    res.json({
      message: 'Health monitoring stopped',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Failed to stop health monitoring:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/elasticsearch
 * Elasticsearch health status with fallback verification
 */
router.get('/elasticsearch', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // Check Elasticsearch health
    const esHealth = await checkElasticsearchHealth();
    const responseTime = Date.now() - startTime;
    
    // Check if indexes exist
    const indexesStatus = {
      production: false,
      arrets: false
    };
    
    try {
      indexesStatus.production = await elasticsearchDashboardService.isAvailable();
    } catch (error) {
      console.warn('Production index check failed:', error.message);
    }
    
    try {
      // Check arrets index exists
      const { default: elasticsearchService } = await import('../services/elasticsearchService.js');
      indexesStatus.arrets = await elasticsearchService.indexExists('machine_stops');
    } catch (error) {
      console.warn('Arrets index check failed:', error.message);
    }

    res.json({
      success: true,
      status: esHealth ? 'healthy' : 'unhealthy',
      cluster_health: esHealth ? 'green' : 'red',
      responseTime,
      timestamp: new Date().toISOString(),
      indexes: indexesStatus,
      fallbackAvailable: true, // MySQL is always available as fallback
      details: {
        elasticsearchAvailable: esHealth,
        mysqlFallbackReady: true,
        indexingCapable: esHealth && (indexesStatus.production || indexesStatus.arrets)
      }
    });
    
  } catch (error) {
    console.error('Elasticsearch health check error:', error);
    res.status(500).json({
      success: false,
      status: 'error',
      cluster_health: 'red',
      error: error.message,
      fallbackAvailable: true,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/redis
 * Redis health status with caching metrics
 */
router.get('/redis', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // Check Redis connection
    const redisHealthy = redisConfig.isConnected;
    const responseTime = Date.now() - startTime;
    
    // Get Redis metrics
    const metrics = redisConfig.getMetrics();
    
    // Test Redis operations if connected
    let operationTests = {
      ping: false,
      set: false,
      get: false,
      delete: false
    };
    
    if (redisHealthy) {
      try {
        const client = redisConfig.getClient();
        
        // Test ping
        await client.ping();
        operationTests.ping = true;
        
        // Test set/get/delete operations
        const testKey = `health_test_${Date.now()}`;
        await client.set(testKey, 'test_value', 'EX', 10);
        operationTests.set = true;
        
        const value = await client.get(testKey);
        operationTests.get = value === 'test_value';
        
        await client.del(testKey);
        operationTests.delete = true;
        
      } catch (error) {
        console.warn('Redis operation test failed:', error.message);
      }
    }

    res.json({
      success: true,
      status: redisHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      timestamp: new Date().toISOString(),
      isConnected: redisHealthy,
      metrics: {
        totalQueries: metrics.totalQueries || 0,
        cacheHits: metrics.cacheHits || 0,
        cacheMisses: metrics.cacheMisses || 0,
        cacheHitRate: metrics.totalQueries > 0 ? (metrics.cacheHits / metrics.totalQueries) : 0,
        avgResponseTime: metrics.avgResponseTime || 0,
        lastHealthCheck: metrics.lastHealthCheck || null
      },
      operations: operationTests,
      fallbackBehavior: {
        withoutCache: true,
        gracefulDegradation: true,
        performanceImpact: 'moderate'
      }
    });
    
  } catch (error) {
    console.error('Redis health check error:', error);
    res.status(500).json({
      success: false,
      status: 'error',
      error: error.message,
      fallbackBehavior: {
        withoutCache: true,
        gracefulDegradation: true,
        performanceImpact: 'high'
      },
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/pubsub
 * Redis Pub/Sub health status
 */
router.get('/pubsub', async (req, res) => {
  try {
    const pubsubService = new RedisPubSubService();
    const health = await pubsubService.healthCheck();
    
    res.json({
      success: true,
      healthy: health.healthy,
      status: health.healthy ? 'active' : 'inactive',
      details: health,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Pub/Sub health check error:', error);
    res.json({
      success: false,
      healthy: false,
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
