/**
 * Enhanced GraphQL Interface for Dashboard
 * 
 * Provides enhanced GraphQL queries for dashboard components with support
 * for both Elasticsearch and MySQL data sources with automatic fallback.
 */

// Enhanced GraphQL queries for unified dashboard
export const ENHANCED_DASHBOARD_QUERIES = {
  // Get dashboard statistics
  GET_DASHBOARD_STATS: `
    query GetDashboardStats($filters: EnhancedFilterInput) {
      getDashboardStats(filters: $filters) {
        totalStops
        totalDuration
        averageDuration
        uniqueMachines
        stopsBySeverity {
          severity
          count
        }
        stopsByCategory {
          category
          count
        }
        dataSource
      }
    }
  `,

  // Get all machine stops with enhanced features
  GET_ALL_MACHINE_STOPS: `
    query GetAllMachineStops($filters: EnhancedFilterInput, $pagination: PaginationInput) {
      enhancedGetAllMachineStops(filters: $filters, pagination: $pagination) {
        stops {
          id
          machineId
          machineName
          stopReason
          stopCode
          startTime
          endTime
          duration
          shift
          operator
          description
          category
          severity
          highlights
        }
        total
        page
        size
        totalPages
        dataSource
      }
    }
  `,

  // Get top 5 stops with enhanced features
  GET_TOP_5_STOPS: `
    query GetTop5Stops($filters: EnhancedFilterInput) {
      enhancedGetTop5Stops(filters: $filters) {
        reasons {
          id
          reason
          count
          totalDuration
          averageDuration
          percentage
        }
        dataSource
      }
    }
  `,

  // Get machine comparison data
  GET_MACHINE_COMPARISON: `
    query GetMachineComparison($filters: EnhancedFilterInput) {
      enhancedGetMachineStopComparison(filters: $filters) {
        machines {
          machineId
          machineName
          totalStops
          totalDuration
          averageDuration
          categories {
            category
            count
          }
          severityBreakdown {
            severity
            count
          }
        }
        dataSource
      }
    }
  `,

  // Get stop evolution data for time-series charts
  GET_STOP_EVOLUTION: `
    query GetStopEvolution($filters: EnhancedFilterInput, $interval: String) {
      getStopEvolution(filters: $filters, interval: $interval) {
        evolution {
          date
          timestamp
          stopsCount
          totalDuration
          categories {
            category
            count
          }
        }
        dataSource
      }
    }
  `,

  // Get performance metrics
  GET_PERFORMANCE_METRICS: `
    query GetPerformanceMetrics($filters: EnhancedFilterInput) {
      getPerformanceMetrics(filters: $filters) {
        mttr
        mtbf
        availability
        totalDowntime
        averageStopsPerDay
        dataSource
      }
    }
  `,

  // Get data source status
  GET_DATA_SOURCE_STATUS: `
    query GetDataSourceStatus {
      getDataSourceStatus {
        primarySource
        elasticsearch {
          available
          stats {
            documentCount
            indexSize
            status
          }
          error
        }
        mysql {
          available
          error
        }
      }
    }
  `
};

// Enhanced GraphQL mutations
export const ENHANCED_DASHBOARD_MUTATIONS = {
  // Trigger manual data indexing
  INDEX_DASHBOARD_DATA: `
    mutation IndexDashboardData {
      indexDashboardData {
        success
        indexed
        errors
        message
      }
    }
  `
};

/**
 * Enhanced GraphQL Interface Class
 */
export class EnhancedGraphQLInterface {
  constructor(fetchGraphQL) {
    this.fetchGraphQL = fetchGraphQL;
    this.dataSourceStatus = null;
    this.lastStatusCheck = 0;
    this.statusCacheTime = 30000; // 30 seconds
  }

  /**
   * Check data source status with caching
   */
  async checkDataSourceStatus(force = false) {
    const now = Date.now();
    if (!force && this.dataSourceStatus && (now - this.lastStatusCheck) < this.statusCacheTime) {
      return this.dataSourceStatus;
    }

    try {
      const result = await this.fetchGraphQL(ENHANCED_DASHBOARD_QUERIES.GET_DATA_SOURCE_STATUS);
      this.dataSourceStatus = result.data?.getDataSourceStatus || null;
      this.lastStatusCheck = now;
      return this.dataSourceStatus;
    } catch (error) {
      console.error('Error checking data source status:', error);
      return null;
    }
  }

  /**
   * Get dashboard statistics with enhanced features
   */
  async getDashboardStats(filters = {}) {
    try {
      const result = await this.fetchGraphQL(
        ENHANCED_DASHBOARD_QUERIES.GET_DASHBOARD_STATS,
        { filters }
      );
      
      if (result.errors) {
        console.error('GraphQL errors in getDashboardStats:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.getDashboardStats || null;
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Get essential data (main stats + top stops)
   */
  async getEssentialData(filters = {}) {
    try {
      // Execute both queries in parallel for better performance
      const [statsResult, topStopsResult] = await Promise.all([
        this.getDashboardStats(filters),
        this.getTop5Stops(filters)
      ]);

      return {
        stats: statsResult,
        topStops: topStopsResult,
        dataSource: statsResult?.dataSource || topStopsResult?.dataSource || 'unknown'
      };
    } catch (error) {
      console.error('Error getting essential data:', error);
      throw error;
    }
  }

  /**
   * Get chart data (evolution + machine comparison)
   */
  async getChartData(filters = {}, interval = 'day') {
    try {
      const [evolutionResult, comparisonResult] = await Promise.all([
        this.getStopEvolution(filters, interval),
        this.getMachineComparison(filters)
      ]);

      return {
        evolution: evolutionResult,
        machineComparison: comparisonResult,
        dataSource: evolutionResult?.dataSource || comparisonResult?.dataSource || 'unknown'
      };
    } catch (error) {
      console.error('Error getting chart data:', error);
      throw error;
    }
  }

  /**
   * Get table data with pagination
   */
  async getTableData(filters = {}, pagination = { page: 1, size: 100 }) {
    try {
      const result = await this.fetchGraphQL(
        ENHANCED_DASHBOARD_QUERIES.GET_ALL_MACHINE_STOPS,
        { filters, pagination }
      );
      
      if (result.errors) {
        console.error('GraphQL errors in getTableData:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.enhancedGetAllMachineStops || null;
    } catch (error) {
      console.error('Error getting table data:', error);
      throw error;
    }
  }

  /**
   * Get top 5 stop reasons
   */
  async getTop5Stops(filters = {}) {
    try {
      const result = await this.fetchGraphQL(
        ENHANCED_DASHBOARD_QUERIES.GET_TOP_5_STOPS,
        { filters }
      );
      
      if (result.errors) {
        console.error('GraphQL errors in getTop5Stops:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.enhancedGetTop5Stops || null;
    } catch (error) {
      console.error('Error getting top 5 stops:', error);
      throw error;
    }
  }

  /**
   * Get machine comparison data
   */
  async getMachineComparison(filters = {}) {
    try {
      const result = await this.fetchGraphQL(
        ENHANCED_DASHBOARD_QUERIES.GET_MACHINE_COMPARISON,
        { filters }
      );
      
      if (result.errors) {
        console.error('GraphQL errors in getMachineComparison:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.enhancedGetMachineStopComparison || null;
    } catch (error) {
      console.error('Error getting machine comparison:', error);
      throw error;
    }
  }

  /**
   * Get stop evolution data for time-series charts
   */
  async getStopEvolution(filters = {}, interval = 'day') {
    try {
      const result = await this.fetchGraphQL(
        ENHANCED_DASHBOARD_QUERIES.GET_STOP_EVOLUTION,
        { filters, interval }
      );
      
      if (result.errors) {
        console.error('GraphQL errors in getStopEvolution:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.getStopEvolution || null;
    } catch (error) {
      console.error('Error getting stop evolution:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(filters = {}) {
    try {
      const result = await this.fetchGraphQL(
        ENHANCED_DASHBOARD_QUERIES.GET_PERFORMANCE_METRICS,
        { filters }
      );
      
      if (result.errors) {
        console.error('GraphQL errors in getPerformanceMetrics:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.getPerformanceMetrics || null;
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      throw error;
    }
  }

  /**
   * Trigger manual data indexing
   */
  async indexDashboardData() {
    try {
      const result = await this.fetchGraphQL(ENHANCED_DASHBOARD_MUTATIONS.INDEX_DASHBOARD_DATA);
      
      if (result.errors) {
        console.error('GraphQL errors in indexDashboardData:', result.errors);
        throw new Error(result.errors[0].message);
      }
      
      return result.data?.indexDashboardData || null;
    } catch (error) {
      console.error('Error indexing dashboard data:', error);
      throw error;
    }
  }

  /**
   * Enhanced retry mechanism with exponential backoff
   */
  async executeWithRetry(operation, maxRetries = 3, baseDelay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms:`, error.message);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}

/**
 * Create enhanced GraphQL interface instance
 */
export const createEnhancedGraphQLInterface = (fetchGraphQL) => {
  return new EnhancedGraphQLInterface(fetchGraphQL);
};

export default EnhancedGraphQLInterface;
