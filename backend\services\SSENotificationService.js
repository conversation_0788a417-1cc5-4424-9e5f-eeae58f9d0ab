/**
 * SSE Notification Service for Somipem
 * High-performance, production-ready Server-Sent Events implementation
 * Replaces WebSocket system with better reliability and performance
 */

import jwt from 'jsonwebtoken';
import { EventEmitter } from 'events';

class SSENotificationService extends EventEmitter {
  constructor() {
    super();
    this.clients = new Map(); // userId -> Set of {res, metadata}
    this.connectionStats = {
      totalConnections: 0,
      activeUsers: 0,
      messagesPerSecond: 0,
      averageLatency: 0,
      reconnections: 0
    };
    
    // Performance monitoring
    this.messageCount = 0;
    this.latencySum = 0;
    this.latencyCount = 0;
    
    // Configuration
    this.config = {
      heartbeatInterval: 30000, // 30 seconds
      maxConnectionsPerUser: 10, // Increased limit to accommodate multiple components/tabs
      messageRetryTime: 3000, // SSE retry time
      compressionThreshold: 1024, // Compress messages > 1KB
      maxMessageHistory: 100 // Keep last 100 messages per user
    };
    
    this.startHeartbeat();
    this.startStatsCollection();
    
    console.log('🚀 SSE Notification Service initialized');
  }

  /**
   * Add new SSE client connection
   * @param {number} userId - User ID from JWT
   * @param {Response} res - Express response object
   * @param {Object} metadata - Connection metadata
   */
  addClient(userId, res, metadata = {}) {
    try {
      // Initialize user connections if not exists
      if (!this.clients.has(userId)) {
        this.clients.set(userId, new Set());
      }

      const userConnections = this.clients.get(userId);
      
      // Enforce connection limit per user
      if (userConnections.size >= this.config.maxConnectionsPerUser) {
        console.warn(`⚠️ User ${userId} exceeded max connections (${this.config.maxConnectionsPerUser}) - UserAgent: ${metadata.userAgent || 'Unknown'}`);
        // Close oldest connection
        const oldestConnection = Array.from(userConnections)[0];
        this.removeClient(userId, oldestConnection.res);
      }

      // Create connection object
      const connection = {
        res,
        connectedAt: new Date(),
        lastHeartbeat: new Date(),
        messagesSent: 0,
        userAgent: metadata.userAgent || 'Unknown',
        ip: metadata.ip || 'Unknown'
      };

      userConnections.add(connection);
      this.connectionStats.totalConnections++;
      this.connectionStats.activeUsers = this.clients.size;

      // Send connection confirmation with retry configuration
      this.sendToConnection(connection, {
        type: 'connected',
        timestamp: new Date().toISOString(),
        message: 'SSE connection established',
        config: {
          heartbeatInterval: this.config.heartbeatInterval,
          retryTime: this.config.messageRetryTime
        }
      });

      console.log(`✅ SSE client connected - User: ${userId}, Total: ${this.getTotalConnections()}, UserAgent: ${metadata.userAgent || 'Unknown'}`);
      this.emit('clientConnected', { userId, connection });

      return connection;
    } catch (error) {
      console.error(`❌ Failed to add SSE client for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Remove SSE client connection
   * @param {number} userId - User ID
   * @param {Response} res - Express response object
   */
  removeClient(userId, res) {
    try {
      if (!this.clients.has(userId)) return;

      const userConnections = this.clients.get(userId);
      const connectionToRemove = Array.from(userConnections)
        .find(conn => conn.res === res);

      if (connectionToRemove) {
        userConnections.delete(connectionToRemove);
        
        // Clean up empty user sets
        if (userConnections.size === 0) {
          this.clients.delete(userId);
        }

        // Close the response stream safely
        try {
          if (!res.destroyed && !res.finished) {
            res.end();
          }
        } catch (closeError) {
          console.warn(`⚠️ Error closing SSE connection:`, closeError.message);
        }

        this.connectionStats.totalConnections--;
        this.connectionStats.activeUsers = this.clients.size;

        console.log(`🔌 SSE client disconnected - User: ${userId}, Total: ${this.getTotalConnections()}`);
        this.emit('clientDisconnected', { userId, connection: connectionToRemove });
      }
    } catch (error) {
      console.error(`❌ Failed to remove SSE client for user ${userId}:`, error);
    }
  }

  /**
   * Send notification to specific user
   * @param {number} userId - Target user ID
   * @param {Object} notification - Notification object from database
   * @param {Object} options - Send options
   */
  async sendNotificationToUser(userId, notification, options = {}) {
    const startTime = Date.now();
    
    try {
      if (!this.clients.has(userId)) {
        console.log(`📭 No SSE connections for user ${userId}`);
        return { sent: false, reason: 'no_connections' };
      }

      const userConnections = this.clients.get(userId);
      let successCount = 0;
      let failedConnections = [];

      const message = {
        type: 'notification',
        notification: this.formatNotification(notification),
        timestamp: new Date().toISOString(),
        priority: notification.priority,
        category: notification.category,
        ...options
      };

      // Send to all user connections
      for (const connection of userConnections) {
        try {
          await this.sendToConnection(connection, message);
          connection.messagesSent++;
          successCount++;
        } catch (error) {
          console.error(`❌ Failed to send to connection:`, error);
          failedConnections.push(connection);
        }
      }

      // Clean up failed connections
      failedConnections.forEach(conn => {
        userConnections.delete(conn);
      });

      // Update performance metrics
      const latency = Date.now() - startTime;
      this.updateLatencyMetrics(latency);
      this.messageCount++;

      console.log(`📨 Sent notification to ${successCount}/${userConnections.size + failedConnections.length} connections for user ${userId}`);
      
      return {
        sent: successCount > 0,
        successCount,
        failedCount: failedConnections.length,
        latency
      };

    } catch (error) {
      console.error(`❌ Failed to send notification to user ${userId}:`, error);
      return { sent: false, error: error.message };
    }
  }

  /**
   * Broadcast notification to all connected users
   * @param {Object} notification - Notification object
   * @param {Object} options - Broadcast options
   */
  async broadcastNotification(notification, options = {}) {
    const startTime = Date.now();
    let totalSent = 0;
    let totalFailed = 0;

    console.log(`📢 Broadcasting ${notification.priority} notification to ${this.clients.size} users`);

    const broadcastPromises = Array.from(this.clients.keys()).map(async (userId) => {
      const result = await this.sendNotificationToUser(userId, notification, {
        ...options,
        broadcast: true
      });
      
      if (result.sent) {
        totalSent += result.successCount || 1;
      } else {
        totalFailed++;
      }
    });

    await Promise.all(broadcastPromises);

    const totalTime = Date.now() - startTime;
    console.log(`📢 Broadcast complete: ${totalSent} sent, ${totalFailed} failed in ${totalTime}ms`);

    return {
      totalSent,
      totalFailed,
      broadcastTime: totalTime,
      usersReached: this.clients.size
    };
  }

  /**
   * Broadcast event to all connected users
   * @param {Object} event - Event object to broadcast
   * @param {Object} options - Broadcast options
   */
  async broadcastEvent(event, options = {}) {
    const startTime = Date.now();
    let totalSent = 0;
    let totalFailed = 0;

    console.log(`📢 Broadcasting ${event.type} event to ${this.clients.size} users`);

    const message = {
      ...event,
      timestamp: event.timestamp || new Date().toISOString(),
      ...options
    };

    const broadcastPromises = Array.from(this.clients.entries()).map(async ([userId, connections]) => {
      let userSuccess = 0;
      let userFailed = 0;

      for (const connection of connections) {
        try {
          await this.sendToConnection(connection, message);
          connection.messagesSent++;
          userSuccess++;
        } catch (error) {
          console.error(`❌ Failed to send event to user ${userId}:`, error);
          userFailed++;
        }
      }

      totalSent += userSuccess;
      totalFailed += userFailed;
    });

    await Promise.all(broadcastPromises);

    const totalTime = Date.now() - startTime;
    console.log(`📢 Event broadcast complete: ${totalSent} sent, ${totalFailed} failed in ${totalTime}ms`);

    return {
      totalSent,
      totalFailed,
      broadcastTime: totalTime,
      usersReached: this.clients.size
    };
  }

  /**
   * Send data to specific connection
   * @param {Object} connection - Connection object
   * @param {Object} data - Data to send
   */
  async sendToConnection(connection, data) {
    return new Promise((resolve, reject) => {
      try {
        if (connection.res.destroyed || connection.res.finished) {
          reject(new Error('Connection closed'));
          return;
        }

        // Format SSE message
        const sseMessage = this.formatSSEMessage(data);
        
        // Send with error handling
        connection.res.write(sseMessage, (error) => {
          if (error) {
            reject(error);
          } else {
            connection.lastHeartbeat = new Date();
            resolve();
          }
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Format data as SSE message
   * @param {Object} data - Data to format
   */
  formatSSEMessage(data) {
    const jsonData = JSON.stringify(data);
    
    // Add retry time for automatic reconnection
    let sseMessage = `retry: ${this.config.messageRetryTime}\n`;
    
    // Add event type if specified
    if (data.type) {
      sseMessage += `event: ${data.type}\n`;
    }
    
    // Add data
    sseMessage += `data: ${jsonData}\n\n`;
    
    return sseMessage;
  }

  /**
   * Format notification for SSE transmission
   * @param {Object} notification - Raw notification from database
   */
  formatNotification(notification) {
    // Handle both database format and already formatted notifications
    const created_at = notification.created_at || notification.timestamp;
    const read_at = notification.read_at || (notification.read === 1 ? created_at : null);
    const acknowledged_at = notification.acknowledged_at;

    return {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      category: notification.category,
      priority: notification.priority,
      severity: notification.severity,
      machine_id: notification.machine_id,
      source: notification.source,
      created_at: created_at,
      read_at: read_at,
      acknowledged_at: acknowledged_at,
      // Add computed fields for frontend
      isUnread: !read_at,
      isAcknowledged: !!acknowledged_at,
      timeAgo: this.getTimeAgo(created_at)
    };
  }

  /**
   * Start heartbeat to keep connections alive
   */
  startHeartbeat() {
    setInterval(() => {
      this.sendHeartbeatToAll();
    }, this.config.heartbeatInterval);
  }

  /**
   * Send heartbeat to all connections
   */
  async sendHeartbeatToAll() {
    const heartbeatMessage = {
      type: 'heartbeat',
      timestamp: new Date().toISOString(),
      stats: this.getPublicStats()
    };

    let activeConnections = 0;
    let deadConnections = [];

    for (const [userId, connections] of this.clients) {
      for (const connection of connections) {
        try {
          await this.sendToConnection(connection, heartbeatMessage);
          activeConnections++;
        } catch (error) {
          deadConnections.push({ userId, connection });
        }
      }
    }

    // Clean up dead connections
    deadConnections.forEach(({ userId, connection }) => {
      this.removeClient(userId, connection.res);
    });

    if (deadConnections.length > 0) {
      console.log(`🧹 Cleaned up ${deadConnections.length} dead SSE connections`);
    }
  }

  /**
   * Start statistics collection
   */
  startStatsCollection() {
    setInterval(() => {
      this.connectionStats.messagesPerSecond = this.messageCount;
      this.messageCount = 0;
      
      // Log stats every minute
    }, 1000);
  }

  /**
   * Update latency metrics
   */
  updateLatencyMetrics(latency) {
    this.latencySum += latency;
    this.latencyCount++;
    this.connectionStats.averageLatency = Math.round(this.latencySum / this.latencyCount);
  }

  /**
   * Get time ago string
   */
  getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  }

  /**
   * Get total connection count
   */
  getTotalConnections() {
    let total = 0;
    this.clients.forEach(connections => {
      total += connections.size;
    });
    return total;
  }

  /**
   * Get public statistics
   */
  getPublicStats() {
    return {
      totalConnections: this.getTotalConnections(),
      activeUsers: this.clients.size,
      averageLatency: this.connectionStats.averageLatency,
      uptime: process.uptime()
    };
  }

  /**
   * Get detailed statistics for admin
   */
  getDetailedStats() {
    const userStats = Array.from(this.clients.entries()).map(([userId, connections]) => ({
      userId,
      connectionCount: connections.size,
      connections: Array.from(connections).map(conn => ({
        connectedAt: conn.connectedAt,
        messagesSent: conn.messagesSent,
        userAgent: conn.userAgent,
        ip: conn.ip
      }))
    }));

    return {
      ...this.connectionStats,
      totalConnections: this.getTotalConnections(),
      activeUsers: this.clients.size,
      userStats,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🛑 Shutting down SSE Notification Service...');
    
    // Send shutdown message to all clients
    const shutdownMessage = {
      type: 'shutdown',
      message: 'Server is shutting down',
      timestamp: new Date().toISOString()
    };

    for (const [userId, connections] of this.clients) {
      for (const connection of connections) {
        try {
          await this.sendToConnection(connection, shutdownMessage);
          connection.res.end();
        } catch (error) {
          // Ignore errors during shutdown
        }
      }
    }

    this.clients.clear();
    console.log('✅ SSE Notification Service shutdown complete');
  }
}

// Singleton instance
const sseNotificationService = new SSENotificationService();

// Graceful shutdown handling
process.on('SIGTERM', () => sseNotificationService.shutdown());
process.on('SIGINT', () => sseNotificationService.shutdown());

export default sseNotificationService;
