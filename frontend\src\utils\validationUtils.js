/**
 * Utilities for form validation
 * @module validationUtils
 */

/**
 * Validation rules for common form fields
 * @type {Object}
 */
export const VALIDATION_RULES = {
  /**
   * Required field validation
   * @param {*} value - Field value
   * @param {string} [message='Ce champ est obligatoire'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  required: (value, message = 'Ce champ est obligatoire') => {
    if (value === undefined || value === null || value === '') {
      return message;
    }
    
    if (Array.isArray(value) && value.length === 0) {
      return message;
    }
    
    return true;
  },
  
  /**
   * Email validation
   * @param {string} value - Email value
   * @param {string} [message='Email invalide'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  email: (value, message = 'Email invalide') => {
    if (!value) return true;
    
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    return emailRegex.test(value) ? true : message;
  },
  
  /**
   * Minimum length validation
   * @param {string} value - Field value
   * @param {number} length - Minimum length
   * @param {string} [message] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  minLength: (value, length, message) => {
    if (!value) return true;
    
    return value.length >= length 
      ? true 
      : (message || `Minimum ${length} caractères requis`);
  },
  
  /**
   * Maximum length validation
   * @param {string} value - Field value
   * @param {number} length - Maximum length
   * @param {string} [message] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  maxLength: (value, length, message) => {
    if (!value) return true;
    
    return value.length <= length 
      ? true 
      : (message || `Maximum ${length} caractères autorisés`);
  },
  
  /**
   * Pattern validation
   * @param {string} value - Field value
   * @param {RegExp} pattern - Regular expression pattern
   * @param {string} [message='Format invalide'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  pattern: (value, pattern, message = 'Format invalide') => {
    if (!value) return true;
    
    return pattern.test(value) ? true : message;
  },
  
  /**
   * Number validation
   * @param {string|number} value - Field value
   * @param {string} [message='Veuillez entrer un nombre valide'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  number: (value, message = 'Veuillez entrer un nombre valide') => {
    if (!value && value !== 0) return true;
    
    return !isNaN(Number(value)) ? true : message;
  },
  
  /**
   * Integer validation
   * @param {string|number} value - Field value
   * @param {string} [message='Veuillez entrer un nombre entier'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  integer: (value, message = 'Veuillez entrer un nombre entier') => {
    if (!value && value !== 0) return true;
    
    return Number.isInteger(Number(value)) ? true : message;
  },
  
  /**
   * Minimum value validation
   * @param {string|number} value - Field value
   * @param {number} min - Minimum value
   * @param {string} [message] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  min: (value, min, message) => {
    if (!value && value !== 0) return true;
    
    const numValue = Number(value);
    return !isNaN(numValue) && numValue >= min 
      ? true 
      : (message || `La valeur minimale est ${min}`);
  },
  
  /**
   * Maximum value validation
   * @param {string|number} value - Field value
   * @param {number} max - Maximum value
   * @param {string} [message] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  max: (value, max, message) => {
    if (!value && value !== 0) return true;
    
    const numValue = Number(value);
    return !isNaN(numValue) && numValue <= max 
      ? true 
      : (message || `La valeur maximale est ${max}`);
  },
  
  /**
   * Phone number validation
   * @param {string} value - Phone number
   * @param {string} [message='Numéro de téléphone invalide'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  phone: (value, message = 'Numéro de téléphone invalide') => {
    if (!value) return true;
    
    // Basic international phone validation
    const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
    return phoneRegex.test(value.replace(/\s/g, '')) ? true : message;
  },
  
  /**
   * URL validation
   * @param {string} value - URL
   * @param {string} [message='URL invalide'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  url: (value, message = 'URL invalide') => {
    if (!value) return true;
    
    try {
      new URL(value);
      return true;
    } catch (error) {
      return message;
    }
  },
  
  /**
   * Date validation
   * @param {string|Date} value - Date value
   * @param {string} [message='Date invalide'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  date: (value, message = 'Date invalide') => {
    if (!value) return true;
    
    const date = new Date(value);
    return !isNaN(date.getTime()) ? true : message;
  },
  
  /**
   * Password strength validation
   * @param {string} value - Password
   * @param {string} [message='Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule et un chiffre'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  password: (value, message = 'Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule et un chiffre') => {
    if (!value) return true;
    
    // At least 8 chars, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return passwordRegex.test(value) ? true : message;
  },
  
  /**
   * Match field validation
   * @param {*} value - Field value
   * @param {string} fieldToMatch - Field name to match against
   * @param {Object} values - All form values
   * @param {string} [message='Les valeurs ne correspondent pas'] - Error message
   * @returns {boolean|string} true if valid, error message if invalid
   */
  match: (value, fieldToMatch, values, message = 'Les valeurs ne correspondent pas') => {
    if (!value) return true;
    
    return value === values[fieldToMatch] ? true : message;
  },
};

/**
 * Validate a single field with multiple rules
 * @param {*} value - Field value
 * @param {Object} rules - Validation rules
 * @param {Object} [values={}] - All form values (for match validation)
 * @returns {string|true} Error message or true if valid
 */
export const validateField = (value, rules, values = {}) => {
  if (!rules) return true;
  
  for (const rule in rules) {
    if (rule in VALIDATION_RULES) {
      const ruleValue = rules[rule];
      
      // Handle different rule formats
      if (rule === 'required' && typeof ruleValue === 'boolean') {
        if (ruleValue) {
          const result = VALIDATION_RULES.required(value);
          if (result !== true) return result;
        }
      } else if (rule === 'match') {
        const result = VALIDATION_RULES.match(
          value, 
          ruleValue.field, 
          values, 
          ruleValue.message
        );
        if (result !== true) return result;
      } else if (rule === 'minLength' || rule === 'maxLength' || rule === 'min' || rule === 'max') {
        const result = VALIDATION_RULES[rule](
          value, 
          ruleValue.value, 
          ruleValue.message
        );
        if (result !== true) return result;
      } else if (rule === 'pattern') {
        const result = VALIDATION_RULES.pattern(
          value, 
          ruleValue.pattern, 
          ruleValue.message
        );
        if (result !== true) return result;
      } else if (typeof ruleValue === 'object') {
        const result = VALIDATION_RULES[rule](value, ruleValue.message);
        if (result !== true) return result;
      } else {
        const result = VALIDATION_RULES[rule](value, ruleValue);
        if (result !== true) return result;
      }
    }
  }
  
  return true;
};

/**
 * Validate all form fields
 * @param {Object} values - Form values
 * @param {Object} validationSchema - Validation schema
 * @returns {Object} Validation errors
 */
export const validateForm = (values, validationSchema) => {
  const errors = {};
  
  for (const field in validationSchema) {
    const result = validateField(values[field], validationSchema[field], values);
    if (result !== true) {
      errors[field] = result;
    }
  }
  
  return errors;
};

/**
 * Check if form has errors
 * @param {Object} errors - Validation errors
 * @returns {boolean} Whether form has errors
 */
export const hasErrors = (errors) => {
  return Object.keys(errors).length > 0;
};

/**
 * Create a validation schema for a form
 * @param {Object} schema - Schema definition
 * @returns {Object} Validation schema
 * 
 * @example
 * const validationSchema = createValidationSchema({
 *   username: { required: true, minLength: { value: 3, message: 'Trop court' } },
 *   email: { required: true, email: true },
 *   password: { required: true, password: true },
 *   confirmPassword: { required: true, match: { field: 'password' } }
 * });
 */
export const createValidationSchema = (schema) => {
  return schema;
};

export default {
  VALIDATION_RULES,
  validateField,
  validateForm,
  hasErrors,
  createValidationSchema,
};