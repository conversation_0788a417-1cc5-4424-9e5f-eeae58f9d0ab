#!/usr/bin/env node

/**
 * Settings Database Test Script
 * Tests database connectivity and settings persistence directly
 */

import { executeQuery } from '../utils/dbUtils.js';

async function testSettingsDatabase() {
  console.log('🧪 Testing Settings Database...\n');

  try {
    // Step 1: Test database connection
    console.log('1️⃣ Testing database connection...');
    const connectionTest = await executeQuery('SELECT 1 as test');
    
    if (!connectionTest.success) {
      throw new Error('Database connection failed: ' + connectionTest.error);
    }
    console.log('✅ Database connection successful\n');

    // Step 2: Check if user_settings table exists
    console.log('2️⃣ Checking user_settings table...');
    const tableCheck = await executeQuery('SHOW TABLES LIKE "user_settings"');
    
    if (!tableCheck.success) {
      throw new Error('Failed to check user_settings table: ' + tableCheck.error);
    }
    
    if (tableCheck.data.length === 0) {
      console.log('❌ user_settings table does not exist');
      console.log('Creating user_settings table...');
      
      const createTable = await executeQuery(`
        CREATE TABLE user_settings (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          settings_json JSON NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY unique_user_settings (user_id),
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      if (!createTable.success) {
        throw new Error('Failed to create user_settings table: ' + createTable.error);
      }
      console.log('✅ user_settings table created');
    } else {
      console.log('✅ user_settings table exists');
    }

    // Step 3: Check table structure
    console.log('3️⃣ Checking table structure...');
    const structureCheck = await executeQuery('DESCRIBE user_settings');
    
    if (!structureCheck.success) {
      throw new Error('Failed to check table structure: ' + structureCheck.error);
    }
    
    console.log('✅ Table structure:');
    structureCheck.data.forEach(column => {
      console.log(`   - ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });

    // Step 4: Find a real user for testing
    console.log('\n4️⃣ Finding a real user for testing...');
    const userCheck = await executeQuery('SELECT id FROM users LIMIT 1');

    if (!userCheck.success || userCheck.data.length === 0) {
      console.log('❌ No users found in database');
      console.log('ℹ️  Skipping CRUD tests - need at least one user');
      return;
    }

    const testUserId = userCheck.data[0].id;
    console.log(`✅ Using user ID ${testUserId} for testing`);

    // Step 5: Test settings CRUD operations
    console.log('\n5️⃣ Testing settings CRUD operations...');
    const testSettings = {
      theme: { darkMode: true, compactMode: false },
      charts: { 
        defaultType: 'line', 
        showLegend: false,
        layout: { defaultHeight: 400 },
        dataDisplay: { showDataLabels: true }
      }
    };

    // Clean up any existing test data
    await executeQuery('DELETE FROM user_settings WHERE user_id = ?', [testUserId]);

    // Test INSERT
    console.log('   📝 Testing INSERT...');
    const insertResult = await executeQuery(
      'INSERT INTO user_settings (user_id, settings_json) VALUES (?, ?)',
      [testUserId, JSON.stringify(testSettings)]
    );
    
    if (!insertResult.success) {
      throw new Error('INSERT failed: ' + insertResult.error);
    }
    console.log('   ✅ INSERT successful');

    // Test SELECT
    console.log('   📖 Testing SELECT...');
    const selectResult = await executeQuery(
      'SELECT settings_json FROM user_settings WHERE user_id = ?',
      [testUserId]
    );
    
    if (!selectResult.success) {
      throw new Error('SELECT failed: ' + selectResult.error);
    }
    
    if (selectResult.data.length === 0) {
      throw new Error('No settings found after INSERT');
    }
    
    const retrievedSettings = JSON.parse(selectResult.data[0].settings_json);
    console.log('   ✅ SELECT successful');
    console.log('   📄 Retrieved settings:', JSON.stringify(retrievedSettings, null, 2));

    // Test UPDATE
    console.log('   🔄 Testing UPDATE...');
    const updatedSettings = {
      ...testSettings,
      charts: { 
        ...testSettings.charts, 
        defaultType: 'bar',
        layout: { defaultHeight: 350 }
      }
    };
    
    const updateResult = await executeQuery(
      'UPDATE user_settings SET settings_json = ?, updated_at = NOW() WHERE user_id = ?',
      [JSON.stringify(updatedSettings), testUserId]
    );
    
    if (!updateResult.success) {
      throw new Error('UPDATE failed: ' + updateResult.error);
    }
    
    if (updateResult.data.affectedRows === 0) {
      throw new Error('UPDATE affected 0 rows');
    }
    console.log('   ✅ UPDATE successful');

    // Verify UPDATE
    const verifyResult = await executeQuery(
      'SELECT settings_json FROM user_settings WHERE user_id = ?',
      [testUserId]
    );
    
    const verifiedSettings = JSON.parse(verifyResult.data[0].settings_json);
    if (verifiedSettings.charts.defaultType !== 'bar') {
      throw new Error('UPDATE verification failed - settings not updated');
    }
    console.log('   ✅ UPDATE verification successful');

    // Clean up test data
    await executeQuery('DELETE FROM user_settings WHERE user_id = ?', [testUserId]);
    console.log('   🧹 Test data cleaned up');

    // Step 6: Check for existing user settings
    console.log('\n6️⃣ Checking existing user settings...');
    const existingSettings = await executeQuery('SELECT user_id, JSON_LENGTH(settings_json) as settings_count FROM user_settings LIMIT 5');
    
    if (existingSettings.success && existingSettings.data.length > 0) {
      console.log('✅ Found existing user settings:');
      existingSettings.data.forEach(row => {
        console.log(`   - User ${row.user_id}: ${row.settings_count} settings properties`);
      });
    } else {
      console.log('ℹ️  No existing user settings found');
    }

    console.log('\n🎉 Settings Database Test PASSED!');
    console.log('\n📋 Test Results:');
    console.log('   ✅ Database Connection: Working');
    console.log('   ✅ Table Structure: Valid');
    console.log('   ✅ INSERT Operation: Working');
    console.log('   ✅ SELECT Operation: Working');
    console.log('   ✅ UPDATE Operation: Working');
    console.log('   ✅ JSON Storage: Working');
    console.log('\n🎯 Database is ready for settings persistence!');

  } catch (error) {
    console.error('❌ Settings Database Test FAILED!');
    console.error('Error:', error.message);
    
    if (error.code) {
      console.error('Error Code:', error.code);
    }
    
    process.exit(1);
  }
}

// Run the test
testSettingsDatabase();


