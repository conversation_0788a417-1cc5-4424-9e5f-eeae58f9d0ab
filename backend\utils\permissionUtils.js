/**
 * Utility functions for handling permissions
 */

/**
 * Parse JSON permissions safely
 * @param {string|object|array} permissions - Permissions to parse
 * @returns {Array} Parsed permissions array
 */
export const parsePermissions = (permissions) => {
  if (!permissions) {
    return [];
  }

  try {
    // Handle case where permissions might be already a JSON object or array
    let parsedPermissions = permissions;

    // If it's a string, try to parse it as JSON
    if (typeof permissions === 'string') {
      // Check if it's a JSON string
      if (permissions.trim().startsWith('[') || permissions.trim().startsWith('{')) {
        parsedPermissions = JSON.parse(permissions);
      } else {
        // It might be a comma-separated string
        parsedPermissions = permissions.split(',');
      }
    }

    // Ensure we have an array
    if (!Array.isArray(parsedPermissions)) {
      // If it's an object with values, extract values
      if (typeof parsedPermissions === 'object' && parsedPermissions !== null) {
        parsedPermissions = Object.values(parsedPermissions);
      } else {
        // Convert to array with single item
        parsedPermissions = [parsedPermissions];
      }
    }

    // Clean up permissions - remove quotes and trim whitespace
    return parsedPermissions
      .filter(p => p !== null && p !== undefined) // Remove null/undefined values
      .map(p => {
        if (typeof p === 'string') {
          return p.replace(/^"|"$/g, '').trim(); // Remove quotes and trim
        }
        return String(p); // Convert non-string values to strings
      });
  } catch (error) {
    console.error('Error parsing permissions:', error);
    return [];
  }
};

/**
 * Format permissions as a standardized JSON array
 * @param {Array|string|object} permissions - Permissions to format
 * @returns {string} JSON string of permissions
 */
export const formatPermissions = (permissions) => {
  const parsedPermissions = parsePermissions(permissions);
  return JSON.stringify(parsedPermissions);
};

/**
 * Check if user has required permissions
 * @param {Array} userPermissions - User's permissions
 * @param {Array|string} requiredPermissions - Required permissions
 * @param {boolean} requireAll - If true, user must have all required permissions
 * @returns {boolean} True if user has required permissions
 */
export const hasPermission = (userPermissions, requiredPermissions, requireAll = false) => {
  // Parse user permissions
  const parsedUserPermissions = parsePermissions(userPermissions);

  // If user has system:admin permission, they have access to everything
  if (parsedUserPermissions.includes('system:admin') || parsedUserPermissions.includes('admin')) {
    return true;
  }

  // Parse required permissions
  const parsedRequiredPermissions = parsePermissions(requiredPermissions);

  // Check permissions with namespace support
  if (requireAll) {
    // User must have ALL required permissions
    return parsedRequiredPermissions.every(permission =>
      hasSpecificPermission(parsedUserPermissions, permission)
    );
  } else {
    // User must have ANY of the required permissions
    return parsedRequiredPermissions.some(permission =>
      hasSpecificPermission(parsedUserPermissions, permission)
    );
  }
};

/**
 * Check if user has a specific permission, supporting namespaces and wildcards
 * @param {Array} userPermissions - User's permissions
 * @param {string} permission - Permission to check
 * @returns {boolean} True if user has the permission
 */
export const hasSpecificPermission = (userPermissions, permission) => {
  // Direct match
  if (userPermissions.includes(permission)) {
    return true;
  }

  // Check for namespace wildcards
  if (permission.includes(':')) {
    const [namespace, action] = permission.split(':');

    // Check for namespace wildcard (e.g., "finance:*")
    if (userPermissions.includes(`${namespace}:*`)) {
      return true;
    }

    // Check for view_all_departments special case
    if (userPermissions.includes('system:view_all_departments') &&
        action.startsWith('view_')) {
      return true;
    }
  }

  // Check for legacy permissions (without namespace)
  // This is for backward compatibility
  if (permission.includes(':')) {
    const [, action] = permission.split(':');
    if (userPermissions.includes(action)) {
      return true;
    }
  }

  return false;
};

/**
 * Combine permissions from multiple sources
 * @param {...Array|string|object} permissionSets - Sets of permissions to combine
 * @returns {Array} Combined unique permissions
 */
export const combinePermissions = (...permissionSets) => {
  // Parse and combine all permission sets
  const allPermissions = permissionSets
    .filter(set => set !== null && set !== undefined)
    .flatMap(set => parsePermissions(set));

  // Return unique permissions
  return [...new Set(allPermissions)];
};

/**
 * Get all permissions for a user, including role hierarchy permissions
 * @param {Object} user - User object with role and permissions
 * @returns {Array} All user permissions
 */
export const getUserPermissions = async (user) => {
  if (!user) return [];

  try {
    // Import dynamically to avoid circular dependency
    const { getAllRolePermissions } = await import('./roleHierarchy.js');

    // Get user's direct permissions
    const userPermissions = parsePermissions(user.permissions || []);

    // Get permissions from user's role including inheritance
    const rolePermissions = user.role ? getAllRolePermissions(user.role) : [];

    // Combine all permissions
    return combinePermissions(userPermissions, rolePermissions);
  } catch (error) {
    console.error('Error getting user permissions:', error);

    // Fallback to just user permissions if role hierarchy fails
    return parsePermissions(user.permissions || []);
  }
};

export default {
  parsePermissions,
  formatPermissions,
  hasPermission,
  hasSpecificPermission,
  combinePermissions,
  getUserPermissions
};
