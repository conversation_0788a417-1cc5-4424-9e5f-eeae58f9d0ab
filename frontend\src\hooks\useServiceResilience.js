/**
 * Service Resilience Monitoring Hook
 * 
 * Monitors Elasticsearch and Redis service availability and implements
 * fallback strategies for dashboard components
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { notification } from 'antd';

const API_BASE_URL = (() => {
  if (typeof window !== 'undefined') {
    const currentOrigin = window.location.origin;
    if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
      return `${currentOrigin}/api`;
    }
    if (import.meta.env.VITE_API_URL) {
      return `${import.meta.env.VITE_API_URL}/api`;
    }
    return `${currentOrigin}/api`;
  }
  return 'http://localhost:5000/api';
})();

export const useServiceResilience = (options = {}) => {
  const {
    enableMonitoring = true,
    notifyUser = true,
    checkInterval = 30000, // 30 seconds
    retryAttempts = 3
  } = options;

  // Service status state
  const [serviceStatus, setServiceStatus] = useState({
    elasticsearch: {
      available: false,
      status: 'unknown',
      lastCheck: null,
      responseTime: null,
      fallbackActive: false
    },
    redis: {
      available: false,
      status: 'unknown',
      lastCheck: null,
      responseTime: null,
      fallbackActive: false
    },
    overall: {
      resilience: 'unknown',
      score: 0,
      degraded: false
    }
  });

  // Monitoring state
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  
  // Refs for cleanup
  const monitoringInterval = useRef(null);
  const retryTimeout = useRef(null);

  /**
   * Check Elasticsearch service health
   */
  const checkElasticsearchHealth = useCallback(async () => {
    try {
      const startTime = Date.now();
      const response = await fetch(`${API_BASE_URL}/health/elasticsearch`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        timeout: 10000
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      return {
        available: data.success && data.status === 'healthy',
        status: data.status || 'unknown',
        responseTime,
        fallbackAvailable: data.fallbackAvailable || false,
        details: data.details || {}
      };
    } catch (error) {
      console.warn('Elasticsearch health check failed:', error.message);
      return {
        available: false,
        status: 'error',
        responseTime: null,
        fallbackAvailable: true,
        error: error.message
      };
    }
  }, []);

  /**
   * Check Redis service health
   */
  const checkRedisHealth = useCallback(async () => {
    try {
      const startTime = Date.now();
      const response = await fetch(`${API_BASE_URL}/health/redis`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        timeout: 10000
      });
      
      const responseTime = Date.now() - startTime;
      const data = await response.json();
      
      return {
        available: data.success && data.status === 'healthy',
        status: data.status || 'unknown',
        responseTime,
        metrics: data.metrics || {},
        fallbackAvailable: data.fallbackBehavior?.withoutCache || false
      };
    } catch (error) {
      console.warn('Redis health check failed:', error.message);
      return {
        available: false,
        status: 'error',
        responseTime: null,
        metrics: {},
        fallbackAvailable: true,
        error: error.message
      };
    }
  }, []);

  /**
   * Perform comprehensive service health check
   */
  const checkAllServices = useCallback(async () => {
    try {
      const [elasticsearchHealth, redisHealth] = await Promise.all([
        checkElasticsearchHealth(),
        checkRedisHealth()
      ]);

      const now = new Date().toISOString();

      // Calculate overall resilience
      const resilienceFactors = [
        elasticsearchHealth.available || elasticsearchHealth.fallbackAvailable,
        redisHealth.available || redisHealth.fallbackAvailable,
        true, // Database (MySQL) is always available
        true  // System can function without external services
      ];

      const resilienceScore = (resilienceFactors.filter(Boolean).length / resilienceFactors.length) * 100;
      
      const newStatus = {
        elasticsearch: {
          ...elasticsearchHealth,
          lastCheck: now,
          fallbackActive: !elasticsearchHealth.available && elasticsearchHealth.fallbackAvailable
        },
        redis: {
          ...redisHealth,
          lastCheck: now,
          fallbackActive: !redisHealth.available && redisHealth.fallbackAvailable
        },
        overall: {
          resilience: resilienceScore >= 90 ? 'excellent' : 
                     resilienceScore >= 70 ? 'good' : 
                     resilienceScore >= 50 ? 'fair' : 'poor',
          score: resilienceScore,
          degraded: resilienceScore < 100
        }
      };

      setServiceStatus(newStatus);
      return newStatus;

    } catch (error) {
      console.error('Service health check failed:', error);
      return serviceStatus;
    }
  }, [checkElasticsearchHealth, checkRedisHealth, serviceStatus]);

  /**
   * Handle service status changes and notify user
   */
  const handleStatusChange = useCallback((newStatus) => {
    const prevStatus = serviceStatus;
    
    // Check for significant changes
    const elasticsearchChanged = prevStatus.elasticsearch.available !== newStatus.elasticsearch.available;
    const redisChanged = prevStatus.redis.available !== newStatus.redis.available;
    const resilienceChanged = prevStatus.overall.resilience !== newStatus.overall.resilience;

    if (notifyUser && (elasticsearchChanged || redisChanged || resilienceChanged)) {
      // Notify about Elasticsearch changes
      if (elasticsearchChanged) {
        if (newStatus.elasticsearch.available) {
          notification.success({
            message: 'Elasticsearch Restored',
            description: 'Search functionality is now available',
            duration: 4
          });
        } else if (newStatus.elasticsearch.fallbackActive) {
          notification.warning({
            message: 'Elasticsearch Unavailable',
            description: 'Using database fallback - functionality maintained',
            duration: 6
          });
        } else {
          notification.error({
            message: 'Elasticsearch Failed',
            description: 'Search functionality may be limited',
            duration: 8
          });
        }
      }

      // Notify about Redis changes
      if (redisChanged) {
        if (newStatus.redis.available) {
          notification.success({
            message: 'Redis Cache Restored',
            description: 'Performance optimization is now active',
            duration: 4
          });
        } else if (newStatus.redis.fallbackActive) {
          notification.info({
            message: 'Cache Unavailable',
            description: 'System running without cache - slight performance impact',
            duration: 6
          });
        }
      }

      // Notify about overall resilience changes
      if (resilienceChanged && newStatus.overall.resilience === 'poor') {
        notification.error({
          message: 'System Resilience Alert',
          description: 'Multiple services are experiencing issues',
          duration: 10
        });
      }
    }
  }, [serviceStatus, notifyUser]);

  /**
   * Start monitoring services
   */
  const startMonitoring = useCallback(() => {
    if (isMonitoring || !enableMonitoring) return;

    setIsMonitoring(true);
    
    // Initial check
    checkAllServices().then(handleStatusChange);

    // Set up periodic monitoring
    monitoringInterval.current = setInterval(() => {
      checkAllServices().then(handleStatusChange);
    }, checkInterval);

    console.log('🔍 Service resilience monitoring started');
  }, [isMonitoring, enableMonitoring, checkAllServices, handleStatusChange, checkInterval]);

  /**
   * Stop monitoring services
   */
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);
    
    if (monitoringInterval.current) {
      clearInterval(monitoringInterval.current);
      monitoringInterval.current = null;
    }

    if (retryTimeout.current) {
      clearTimeout(retryTimeout.current);
      retryTimeout.current = null;
    }

    console.log('🔍 Service resilience monitoring stopped');
  }, [isMonitoring]);

  /**
   * Retry failed services
   */
  const retryServices = useCallback(async () => {
    if (retryCount >= retryAttempts) {
      notification.error({
        message: 'Service Recovery Failed',
        description: `Unable to restore services after ${retryAttempts} attempts`,
        duration: 10
      });
      return;
    }

    setRetryCount(prev => prev + 1);
    
    notification.info({
      message: 'Retrying Services',
      description: `Attempting to reconnect... (${retryCount + 1}/${retryAttempts})`,
      duration: 3
    });

    // Wait before retry
    retryTimeout.current = setTimeout(() => {
      checkAllServices().then(newStatus => {
        handleStatusChange(newStatus);
        
        // Reset retry count if any service is restored
        if (newStatus.elasticsearch.available || newStatus.redis.available) {
          setRetryCount(0);
        }
      });
    }, 5000); // 5 second delay

  }, [retryCount, retryAttempts, checkAllServices, handleStatusChange]);

  /**
   * Get fallback recommendations for components
   */
  const getFallbackRecommendations = useCallback(() => {
    const recommendations = {
      elasticsearch: {
        available: serviceStatus.elasticsearch.available,
        fallbackActive: serviceStatus.elasticsearch.fallbackActive,
        recommendations: serviceStatus.elasticsearch.fallbackActive ? [
          'Search functionality limited to basic filters',
          'Advanced search features temporarily disabled',
          'Data availability maintained through database fallback'
        ] : []
      },
      redis: {
        available: serviceStatus.redis.available,
        fallbackActive: serviceStatus.redis.fallbackActive,
        recommendations: serviceStatus.redis.fallbackActive ? [
          'Response times may be slightly slower',
          'Live data updates may take longer',
          'All functionality remains available'
        ] : []
      },
      general: {
        resilienceScore: serviceStatus.overall.score,
        degraded: serviceStatus.overall.degraded,
        recommendations: serviceStatus.overall.degraded ? [
          'System is operating in fallback mode',
          'Core functionality is maintained',
          'Performance may be impacted during service restoration'
        ] : [
          'All systems operational',
          'Optimal performance available'
        ]
      }
    };

    return recommendations;
  }, [serviceStatus]);

  /**
   * Force refresh service status
   */
  const refreshStatus = useCallback(async () => {
    const newStatus = await checkAllServices();
    handleStatusChange(newStatus);
    return newStatus;
  }, [checkAllServices, handleStatusChange]);

  // Start monitoring on mount
  useEffect(() => {
    if (enableMonitoring) {
      startMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [enableMonitoring, startMonitoring, stopMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
      }
      if (retryTimeout.current) {
        clearTimeout(retryTimeout.current);
      }
    };
  }, []);

  return {
    // Service status
    serviceStatus,
    isMonitoring,
    
    // Controls
    startMonitoring,
    stopMonitoring,
    retryServices,
    refreshStatus,
    
    // Utilities
    getFallbackRecommendations,
    
    // Quick access to status
    elasticsearchAvailable: serviceStatus.elasticsearch.available,
    redisAvailable: serviceStatus.redis.available,
    fallbackActive: serviceStatus.elasticsearch.fallbackActive || serviceStatus.redis.fallbackActive,
    resilienceScore: serviceStatus.overall.score,
    degraded: serviceStatus.overall.degraded
  };
};

export default useServiceResilience;
