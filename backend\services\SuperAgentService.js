import superagent from 'superagent';

/**
 * SuperAgent service for external HTTP calls, health monitoring, and API integrations
 * Provides centralized HTTP client functionality for the backend
 */
class SuperAgentService {
  constructor() {
    this.defaultTimeout = 30000; // 30 seconds
    this.defaultRetries = 3;
    this.baseHeaders = {
      'User-Agent': 'LocQL-Backend/1.0',
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    };
  }

  /**
   * Create a configured SuperAgent request with default settings
   * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
   * @param {string} url - Request URL
   * @param {Object} options - Additional options
   * @returns {Object} Configured SuperAgent request
   */
  createRequest(method, url, options = {}) {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      headers = {},
      auth = null
    } = options;

    let request = superagent[method.toLowerCase()](url)
      .timeout(timeout)
      .retry(retries)
      .set({ ...this.baseHeaders, ...headers });

    // Add authentication if provided
    if (auth) {
      if (auth.bearer) {
        request = request.set('Authorization', `Bearer ${auth.bearer}`);
      } else if (auth.basic) {
        request = request.auth(auth.basic.username, auth.basic.password);
      } else if (auth.apiKey) {
        request = request.set(auth.apiKey.header, auth.apiKey.value);
      }
    }

    return request;
  }

  /**
   * Perform a GET request
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async get(url, options = {}) {
    try {
      const response = await this.createRequest('GET', url, options);
      return {
        success: true,
        data: response.body,
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      return this.handleError(error, 'GET', url);
    }
  }

  /**
   * Perform a POST request
   * @param {string} url - Request URL
   * @param {Object} data - Request body data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async post(url, data = {}, options = {}) {
    try {
      const response = await this.createRequest('POST', url, options).send(data);
      return {
        success: true,
        data: response.body,
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      return this.handleError(error, 'POST', url);
    }
  }

  /**
   * Perform a PUT request
   * @param {string} url - Request URL
   * @param {Object} data - Request body data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async put(url, data = {}, options = {}) {
    try {
      const response = await this.createRequest('PUT', url, options).send(data);
      return {
        success: true,
        data: response.body,
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      return this.handleError(error, 'PUT', url);
    }
  }

  /**
   * Perform a DELETE request
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async delete(url, options = {}) {
    try {
      const response = await this.createRequest('DELETE', url, options);
      return {
        success: true,
        data: response.body,
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      return this.handleError(error, 'DELETE', url);
    }
  }

  /**
   * Health check for external services
   * @param {string} url - Service URL to check
   * @param {Object} options - Health check options
   * @returns {Promise<Object>} Health status
   */
  async healthCheck(url, options = {}) {
    const startTime = Date.now();
    
    try {
      const response = await this.createRequest('GET', url, {
        timeout: options.timeout || 10000, // Shorter timeout for health checks
        retries: options.retries || 1,
        headers: options.headers || {}
      });

      const responseTime = Date.now() - startTime;

      return {
        healthy: true,
        url,
        status: response.status,
        responseTime,
        timestamp: new Date().toISOString(),
        data: response.body
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        healthy: false,
        url,
        status: error.status || 0,
        responseTime,
        timestamp: new Date().toISOString(),
        error: error.message || 'Unknown error',
        details: {
          code: error.code,
          errno: error.errno,
          timeout: error.timeout
        }
      };
    }
  }

  /**
   * Batch health check for multiple services
   * @param {Array} services - Array of service configurations
   * @returns {Promise<Object>} Batch health results
   */
  async batchHealthCheck(services) {
    const startTime = Date.now();
    
    try {
      const healthPromises = services.map(service => 
        this.healthCheck(service.url, service.options || {})
          .then(result => ({ ...result, name: service.name }))
      );

      const results = await Promise.allSettled(healthPromises);
      const totalTime = Date.now() - startTime;

      const healthResults = results.map(result => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            healthy: false,
            name: 'unknown',
            error: result.reason.message,
            timestamp: new Date().toISOString()
          };
        }
      });

      const overallHealth = healthResults.every(result => result.healthy);

      return {
        overall: {
          healthy: overallHealth,
          totalTime,
          timestamp: new Date().toISOString(),
          checkedServices: services.length
        },
        services: healthResults
      };
    } catch (error) {
      return {
        overall: {
          healthy: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          checkedServices: services.length
        },
        services: []
      };
    }
  }

  /**
   * Monitor external API endpoints
   * @param {Array} endpoints - Endpoints to monitor
   * @param {Object} options - Monitoring options
   * @returns {Promise<Object>} Monitoring results
   */
  async monitorEndpoints(endpoints, options = {}) {
    const { includeMetrics = true, timeout = 5000 } = options;
    
    const monitoringResults = await Promise.allSettled(
      endpoints.map(async (endpoint) => {
        const startTime = Date.now();
        
        try {
          const result = await this.healthCheck(endpoint.url, {
            timeout,
            headers: endpoint.headers || {}
          });

          if (includeMetrics) {
            result.metrics = {
              availability: result.healthy ? 100 : 0,
              responseTime: result.responseTime,
              endpoint: endpoint.name || endpoint.url
            };
          }

          return { ...result, name: endpoint.name || endpoint.url };
        } catch (error) {
          return {
            healthy: false,
            name: endpoint.name || endpoint.url,
            error: error.message,
            responseTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
          };
        }
      })
    );

    return {
      timestamp: new Date().toISOString(),
      totalEndpoints: endpoints.length,
      results: monitoringResults.map(result => 
        result.status === 'fulfilled' ? result.value : result.reason
      )
    };
  }

  /**
   * Handle errors consistently
   * @param {Error} error - The error object
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @returns {Object} Formatted error response
   */
  handleError(error, method, url) {
    console.error(`SuperAgent ${method} request failed:`, {
      url,
      status: error.status,
      message: error.message,
      code: error.code
    });

    return {
      success: false,
      error: error.message,
      status: error.status || 500,
      code: error.code,
      url,
      method,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Test connectivity to a service
   * @param {string} url - Service URL
   * @param {Object} options - Test options
   * @returns {Promise<Object>} Connectivity test result
   */
  async testConnectivity(url, options = {}) {
    const testStart = Date.now();
    
    try {
      const result = await this.healthCheck(url, {
        timeout: options.timeout || 5000,
        retries: 0 // No retries for connectivity test
      });

      return {
        connected: result.healthy,
        url,
        responseTime: result.responseTime,
        testDuration: Date.now() - testStart,
        timestamp: new Date().toISOString(),
        details: result
      };
    } catch (error) {
      return {
        connected: false,
        url,
        error: error.message,
        testDuration: Date.now() - testStart,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
const superAgentService = new SuperAgentService();
export default superAgentService;
