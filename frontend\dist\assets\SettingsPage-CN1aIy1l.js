import{r as o,aR as Gn,p as nt,bs as Sn,bt as Cn,z as ae,w as At,v as $e,H as xn,bT as Un,aN as Qe,q as be,_ as wt,bU as Yn,bV as Kn,aB as wn,bq as Dt,aA as Rn,bx as Xn,aS as Ht,Q as $n,U as kn,bD as Qn,bE as Jn,ap as St,bW as Nn,bz as Zn,V as ce,bB as ea,bA as ta,bC as na,bM as aa,bX as ra,bY as la,bZ as ia,b_ as oa,a1 as sa,aZ as ca,bO as ua,am as ma,aF as da,bQ as Dn,bP as ga,bR as nn,Z as an,bd as fa,bS as pa,b$ as ha,y as He,K as Te,c0 as Ea,bp as Ct,aQ as Xe,aq as va,aM as ya,e as ba,R as e,J as Sa,j as Mn,h as Tn,c1 as Ca,m as In,T as Bn,C as se,d as ht,g as Et,b as ve,c as S,f as Lt,A as Ft,F as xa,S as pt,l as wa,i as On,al as Re,D as dt,aK as Ra,ay as $a,s as Mt}from"./index-N0wOiMt6.js";import{u as ka,b as Na}from"./chartColors-CNaGTE23.js";import{S as bt}from"./index-BP6n0Cjb.js";import{R as Tt,B as Da,C as rn,X as ln,Y as on,T as It,a as Bt,g as sn,P as Ma,e as Ta,f as Ia,L as Ba,d as cn}from"./PieChart-BZME-zsX.js";import{P as Oa}from"./progress-CyD0QBQj.js";import{R as un}from"./EyeOutlined-BNZGoZWA.js";import{R as mn}from"./ReloadOutlined-DZn6IdM2.js";import{R as Pa}from"./SaveOutlined-BzKiXS91.js";import{R as Aa}from"./BulbOutlined-B59dca5S.js";import{S as _}from"./index-Dea_-S4D.js";import{R as Ha}from"./TableOutlined-qwEzR1LV.js";import{R as La}from"./BarChartOutlined-DzqCoGDG.js";import{R as Fa}from"./BellOutlined-D7NJM_PG.js";import{R as _a}from"./FileTextOutlined-BAhSEapg.js";import{R as za}from"./ThunderboltOutlined-Dz7LyqJg.js";var ja={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},qa=function(n,a){return o.createElement(Gn,nt({},n,{ref:a,icon:ja}))},Va=o.forwardRef(qa);function _t(){return typeof BigInt=="function"}function Pn(t){return!t&&t!==0&&!Number.isNaN(t)||!String(t).trim()}function ct(t){var n=t.trim(),a=n.startsWith("-");a&&(n=n.slice(1)),n=n.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),n.startsWith(".")&&(n="0".concat(n));var l=n||"0",i=l.split("."),u=i[0]||"0",y=i[1]||"0";u==="0"&&y==="0"&&(a=!1);var g=a?"-":"";return{negative:a,negativeStr:g,trimStr:l,integerStr:u,decimalStr:y,fullStr:"".concat(g).concat(l)}}function jt(t){var n=String(t);return!Number.isNaN(Number(n))&&n.includes("e")}function st(t){var n=String(t);if(jt(t)){var a=Number(n.slice(n.indexOf("e-")+2)),l=n.match(/\.(\d+)/);return l!=null&&l[1]&&(a+=l[1].length),a}return n.includes(".")&&qt(n)?n.length-n.indexOf(".")-1:0}function Rt(t){var n=String(t);if(jt(t)){if(t>Number.MAX_SAFE_INTEGER)return String(_t()?BigInt(t).toString():Number.MAX_SAFE_INTEGER);if(t<Number.MIN_SAFE_INTEGER)return String(_t()?BigInt(t).toString():Number.MIN_SAFE_INTEGER);n=t.toFixed(st(n))}return ct(n).fullStr}function qt(t){return typeof t=="number"?!Number.isNaN(t):t?/^\s*-?\d+(\.\d+)?\s*$/.test(t)||/^\s*-?\d+\.\s*$/.test(t)||/^\s*-?\.\d+\s*$/.test(t):!1}var Wa=function(){function t(n){if(Cn(this,t),ae(this,"origin",""),ae(this,"negative",void 0),ae(this,"integer",void 0),ae(this,"decimal",void 0),ae(this,"decimalLen",void 0),ae(this,"empty",void 0),ae(this,"nan",void 0),Pn(n)){this.empty=!0;return}if(this.origin=String(n),n==="-"||Number.isNaN(n)){this.nan=!0;return}var a=n;if(jt(a)&&(a=Number(a)),a=typeof a=="string"?a:Rt(a),qt(a)){var l=ct(a);this.negative=l.negative;var i=l.trimStr.split(".");this.integer=BigInt(i[0]);var u=i[1]||"0";this.decimal=BigInt(u),this.decimalLen=u.length}else this.nan=!0}return Sn(t,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(a){var l="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(a,"0"));return BigInt(l)}},{key:"negate",value:function(){var a=new t(this.toString());return a.negative=!a.negative,a}},{key:"cal",value:function(a,l,i){var u=Math.max(this.getDecimalStr().length,a.getDecimalStr().length),y=this.alignDecimal(u),g=a.alignDecimal(u),x=l(y,g).toString(),E=i(u),d=ct(x),C=d.negativeStr,R=d.trimStr,m="".concat(C).concat(R.padStart(E+1,"0"));return new t("".concat(m.slice(0,-E),".").concat(m.slice(-E)))}},{key:"add",value:function(a){if(this.isInvalidate())return new t(a);var l=new t(a);return l.isInvalidate()?this:this.cal(l,function(i,u){return i+u},function(i){return i})}},{key:"multi",value:function(a){var l=new t(a);return this.isInvalidate()||l.isInvalidate()?new t(NaN):this.cal(l,function(i,u){return i*u},function(i){return i*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(a){return this.toString()===(a==null?void 0:a.toString())}},{key:"lessEquals",value:function(a){return this.add(a.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return a?this.isInvalidate()?"":ct("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),t}(),Ga=function(){function t(n){if(Cn(this,t),ae(this,"origin",""),ae(this,"number",void 0),ae(this,"empty",void 0),Pn(n)){this.empty=!0;return}this.origin=String(n),this.number=Number(n)}return Sn(t,[{key:"negate",value:function(){return new t(-this.toNumber())}},{key:"add",value:function(a){if(this.isInvalidate())return new t(a);var l=Number(a);if(Number.isNaN(l))return this;var i=this.number+l;if(i>Number.MAX_SAFE_INTEGER)return new t(Number.MAX_SAFE_INTEGER);if(i<Number.MIN_SAFE_INTEGER)return new t(Number.MIN_SAFE_INTEGER);var u=Math.max(st(this.number),st(l));return new t(i.toFixed(u))}},{key:"multi",value:function(a){var l=Number(a);if(this.isInvalidate()||Number.isNaN(l))return new t(NaN);var i=this.number*l;if(i>Number.MAX_SAFE_INTEGER)return new t(Number.MAX_SAFE_INTEGER);if(i<Number.MIN_SAFE_INTEGER)return new t(Number.MIN_SAFE_INTEGER);var u=Math.max(st(this.number),st(l));return new t(i.toFixed(u))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(a){return this.toNumber()===(a==null?void 0:a.toNumber())}},{key:"lessEquals",value:function(a){return this.add(a.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return a?this.isInvalidate()?"":Rt(this.number):this.origin}}]),t}();function qe(t){return _t()?new Wa(t):new Ga(t)}function xt(t,n,a){var l=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(t==="")return"";var i=ct(t),u=i.negativeStr,y=i.integerStr,g=i.decimalStr,x="".concat(n).concat(g),E="".concat(u).concat(y);if(a>=0){var d=Number(g[a]);if(d>=5&&!l){var C=qe(t).add("".concat(u,"0.").concat("0".repeat(a)).concat(10-d));return xt(C.toString(),n,a,l)}return a===0?E:"".concat(E).concat(n).concat(g.padEnd(a,"0").slice(0,a))}return x===".0"?E:"".concat(E).concat(x)}function Ua(t,n){return typeof Proxy<"u"&&t?new Proxy(t,{get:function(l,i){if(n[i])return n[i];var u=l[i];return typeof u=="function"?u.bind(l):u}}):t}function Ya(t,n){var a=o.useRef(null);function l(){try{var u=t.selectionStart,y=t.selectionEnd,g=t.value,x=g.substring(0,u),E=g.substring(y);a.current={start:u,end:y,value:g,beforeTxt:x,afterTxt:E}}catch{}}function i(){if(t&&a.current&&n)try{var u=t.value,y=a.current,g=y.beforeTxt,x=y.afterTxt,E=y.start,d=u.length;if(u.startsWith(g))d=g.length;else if(u.endsWith(x))d=u.length-a.current.afterTxt.length;else{var C=g[E-1],R=u.indexOf(C,E-1);R!==-1&&(d=R+1)}t.setSelectionRange(d,d)}catch(m){At(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(m.message))}}return[l,i]}var Ka=function(){var n=o.useState(!1),a=$e(n,2),l=a[0],i=a[1];return xn(function(){i(Un())},[]),l},Xa=200,Qa=600;function Ja(t){var n=t.prefixCls,a=t.upNode,l=t.downNode,i=t.upDisabled,u=t.downDisabled,y=t.onStep,g=o.useRef(),x=o.useRef([]),E=o.useRef();E.current=y;var d=function(){clearTimeout(g.current)},C=function(k,w){k.preventDefault(),d(),E.current(w);function A(){E.current(w),g.current=setTimeout(A,Xa)}g.current=setTimeout(A,Qa)};o.useEffect(function(){return function(){d(),x.current.forEach(function(h){return Qe.cancel(h)})}},[]);var R=Ka();if(R)return null;var m="".concat(n,"-handler"),v=be(m,"".concat(m,"-up"),ae({},"".concat(m,"-up-disabled"),i)),p=be(m,"".concat(m,"-down"),ae({},"".concat(m,"-down-disabled"),u)),c=function(){return x.current.push(Qe(d))},b={unselectable:"on",role:"button",onMouseUp:c,onMouseLeave:c};return o.createElement("div",{className:"".concat(m,"-wrap")},o.createElement("span",nt({},b,{onMouseDown:function(k){C(k,!0)},"aria-label":"Increase Value","aria-disabled":i,className:v}),a||o.createElement("span",{unselectable:"on",className:"".concat(n,"-handler-up-inner")})),o.createElement("span",nt({},b,{onMouseDown:function(k){C(k,!1)},"aria-label":"Decrease Value","aria-disabled":u,className:p}),l||o.createElement("span",{unselectable:"on",className:"".concat(n,"-handler-down-inner")})))}function dn(t){var n=typeof t=="number"?Rt(t):ct(t).fullStr,a=n.includes(".");return a?ct(n.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:t+"0"}const Za=function(){var t=o.useRef(0),n=function(){Qe.cancel(t.current)};return o.useEffect(function(){return n},[]),function(a){n(),t.current=Qe(function(){a()})}};var er=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],tr=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],gn=function(n,a){return n||a.isEmpty()?a.toString():a.toNumber()},fn=function(n){var a=qe(n);return a.isInvalidate()?null:a},nr=o.forwardRef(function(t,n){var a=t.prefixCls,l=t.className,i=t.style,u=t.min,y=t.max,g=t.step,x=g===void 0?1:g,E=t.defaultValue,d=t.value,C=t.disabled,R=t.readOnly,m=t.upHandler,v=t.downHandler,p=t.keyboard,c=t.changeOnWheel,b=c===void 0?!1:c,h=t.controls,k=h===void 0?!0:h;t.classNames;var w=t.stringMode,A=t.parser,z=t.formatter,f=t.precision,V=t.decimalSeparator,L=t.onChange,I=t.onInput,D=t.onPressEnter,$=t.onStep,F=t.changeOnBlur,X=F===void 0?!0:F,Y=t.domRef,ie=wt(t,er),W="".concat(a,"-input"),oe=o.useRef(null),K=o.useState(!1),he=$e(K,2),ue=he[0],de=he[1],Q=o.useRef(!1),ne=o.useRef(!1),q=o.useRef(!1),H=o.useState(function(){return qe(d??E)}),G=$e(H,2),B=G[0],me=G[1];function re(N){d===void 0&&me(N)}var ge=o.useCallback(function(N,M){if(!M)return f>=0?f:Math.max(st(N),st(x))},[f,x]),ye=o.useCallback(function(N){var M=String(N);if(A)return A(M);var U=M;return V&&(U=U.replace(V,".")),U.replace(/[^\w.-]+/g,"")},[A,V]),Se=o.useRef(""),Ie=o.useCallback(function(N,M){if(z)return z(N,{userTyping:M,input:String(Se.current)});var U=typeof N=="number"?Rt(N):N;if(!M){var j=ge(U,M);if(qt(U)&&(V||j>=0)){var ze=V||".";U=xt(U,ze,j)}}return U},[z,ge,V]),Pe=o.useState(function(){var N=E??d;return B.isInvalidate()&&["string","number"].includes(wn(N))?Number.isNaN(N)?"":N:Ie(B.toString(),!1)}),Be=$e(Pe,2),Ce=Be[0],ke=Be[1];Se.current=Ce;function fe(N,M){ke(Ie(N.isInvalidate()?N.toString(!1):N.toString(!M),M))}var Ne=o.useMemo(function(){return fn(y)},[y,f]),De=o.useMemo(function(){return fn(u)},[u,f]),Le=o.useMemo(function(){return!Ne||!B||B.isInvalidate()?!1:Ne.lessEquals(B)},[Ne,B]),xe=o.useMemo(function(){return!De||!B||B.isInvalidate()?!1:B.lessEquals(De)},[De,B]),J=Ya(oe.current,ue),Ee=$e(J,2),Fe=Ee[0],r=Ee[1],T=function(M){return Ne&&!M.lessEquals(Ne)?Ne:De&&!De.lessEquals(M)?De:null},Z=function(M){return!T(M)},Ae=function(M,U){var j=M,ze=Z(j)||j.isEmpty();if(!j.isEmpty()&&!U&&(j=T(j)||j,ze=!0),!R&&!C&&ze){var lt=j.toString(),We=ge(lt,U);return We>=0&&(j=qe(xt(lt,".",We)),Z(j)||(j=qe(xt(lt,".",We,!0)))),j.equals(B)||(re(j),L==null||L(j.isEmpty()?null:gn(w,j)),d===void 0&&fe(j,U)),j}return B},je=Za(),we=function N(M){if(Fe(),Se.current=M,ke(M),!ne.current){var U=ye(M),j=qe(U);j.isNaN()||Ae(j,!0)}I==null||I(M),je(function(){var ze=M;A||(ze=M.replace(/。/g,".")),ze!==M&&N(ze)})},Oe=function(){ne.current=!0},gt=function(){ne.current=!1,we(oe.current.value)},at=function(M){we(M.target.value)},_e=function(M){var U;if(!(M&&Le||!M&&xe)){Q.current=!1;var j=qe(q.current?dn(x):x);M||(j=j.negate());var ze=(B||qe(0)).add(j.toString()),lt=Ae(ze,!1);$==null||$(gn(w,lt),{offset:q.current?dn(x):x,type:M?"up":"down"}),(U=oe.current)===null||U===void 0||U.focus()}},Je=function(M){var U=qe(ye(Ce)),j;U.isNaN()?j=Ae(B,M):j=Ae(U,M),d!==void 0?fe(B,!1):j.isNaN()||fe(j,!1)},rt=function(){Q.current=!0},vt=function(M){var U=M.key,j=M.shiftKey;Q.current=!0,q.current=j,U==="Enter"&&(ne.current||(Q.current=!1),Je(!1),D==null||D(M)),p!==!1&&!ne.current&&["Up","ArrowUp","Down","ArrowDown"].includes(U)&&(_e(U==="Up"||U==="ArrowUp"),M.preventDefault())},Ve=function(){Q.current=!1,q.current=!1};o.useEffect(function(){if(b&&ue){var N=function(j){_e(j.deltaY<0),j.preventDefault()},M=oe.current;if(M)return M.addEventListener("wheel",N,{passive:!1}),function(){return M.removeEventListener("wheel",N)}}});var $t=function(){X&&Je(!1),de(!1),Q.current=!1};return Dt(function(){B.isInvalidate()||fe(B,!1)},[f,z]),Dt(function(){var N=qe(d);me(N);var M=qe(ye(Ce));(!N.equals(M)||!Q.current||z)&&fe(N,Q.current)},[d]),Dt(function(){z&&r()},[Ce]),o.createElement("div",{ref:Y,className:be(a,l,ae(ae(ae(ae(ae({},"".concat(a,"-focused"),ue),"".concat(a,"-disabled"),C),"".concat(a,"-readonly"),R),"".concat(a,"-not-a-number"),B.isNaN()),"".concat(a,"-out-of-range"),!B.isInvalidate()&&!Z(B))),style:i,onFocus:function(){de(!0)},onBlur:$t,onKeyDown:vt,onKeyUp:Ve,onCompositionStart:Oe,onCompositionEnd:gt,onBeforeInput:rt},k&&o.createElement(Ja,{prefixCls:a,upNode:m,downNode:v,upDisabled:Le,downDisabled:xe,onStep:_e}),o.createElement("div",{className:"".concat(W,"-wrap")},o.createElement("input",nt({autoComplete:"off",role:"spinbutton","aria-valuemin":u,"aria-valuemax":y,"aria-valuenow":B.isInvalidate()?null:B.toString(),step:x},ie,{ref:Rn(oe,n),className:W,value:Ce,onChange:at,disabled:C,readOnly:R}))))}),ar=o.forwardRef(function(t,n){var a=t.disabled,l=t.style,i=t.prefixCls,u=i===void 0?"rc-input-number":i,y=t.value,g=t.prefix,x=t.suffix,E=t.addonBefore,d=t.addonAfter,C=t.className,R=t.classNames,m=wt(t,tr),v=o.useRef(null),p=o.useRef(null),c=o.useRef(null),b=function(k){c.current&&Kn(c.current,k)};return o.useImperativeHandle(n,function(){return Ua(c.current,{focus:b,nativeElement:v.current.nativeElement||p.current})}),o.createElement(Yn,{className:C,triggerFocus:b,prefixCls:u,value:y,disabled:a,style:l,prefix:g,suffix:x,addonAfter:d,addonBefore:E,classNames:R,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:v},o.createElement(nr,nt({prefixCls:u,disabled:a,ref:c,domRef:p,className:R==null?void 0:R.input},m)))});const rr=t=>{var n;const a=(n=t.handleVisible)!==null&&n!==void 0?n:"auto",l=t.controlHeightSM-t.lineWidth*2;return Object.assign(Object.assign({},Xn(t)),{controlWidth:90,handleWidth:l,handleFontSize:t.fontSize/2,handleVisible:a,handleActiveBg:t.colorFillAlter,handleBg:t.colorBgContainer,filledHandleBg:new Ht(t.colorFillSecondary).onBackground(t.colorBgContainer).toHexString(),handleHoverColor:t.colorPrimary,handleBorderColor:t.colorBorder,handleOpacity:a===!0?1:0,handleVisibleWidth:a===!0?l:0})},pn=({componentCls:t,borderRadiusSM:n,borderRadiusLG:a},l)=>{const i=l==="lg"?a:n;return{[`&-${l}`]:{[`${t}-handler-wrap`]:{borderStartEndRadius:i,borderEndEndRadius:i},[`${t}-handler-up`]:{borderStartEndRadius:i},[`${t}-handler-down`]:{borderEndEndRadius:i}}}},lr=t=>{const{componentCls:n,lineWidth:a,lineType:l,borderRadius:i,inputFontSizeSM:u,inputFontSizeLG:y,controlHeightLG:g,controlHeightSM:x,colorError:E,paddingInlineSM:d,paddingBlockSM:C,paddingBlockLG:R,paddingInlineLG:m,colorIcon:v,motionDurationMid:p,handleHoverColor:c,handleOpacity:b,paddingInline:h,paddingBlock:k,handleBg:w,handleActiveBg:A,colorTextDisabled:z,borderRadiusSM:f,borderRadiusLG:V,controlWidth:L,handleBorderColor:I,filledHandleBg:D,lineHeightLG:$,calc:F}=t;return[{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},St(t)),Nn(t)),{display:"inline-block",width:L,margin:0,padding:0,borderRadius:i}),Zn(t,{[`${n}-handler-wrap`]:{background:w,[`${n}-handler-down`]:{borderBlockStart:`${ce(a)} ${l} ${I}`}}})),ea(t,{[`${n}-handler-wrap`]:{background:D,[`${n}-handler-down`]:{borderBlockStart:`${ce(a)} ${l} ${I}`}},"&:focus-within":{[`${n}-handler-wrap`]:{background:w}}})),ta(t,{[`${n}-handler-wrap`]:{background:w,[`${n}-handler-down`]:{borderBlockStart:`${ce(a)} ${l} ${I}`}}})),na(t)),{"&-rtl":{direction:"rtl",[`${n}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:y,lineHeight:$,borderRadius:V,[`input${n}-input`]:{height:F(g).sub(F(a).mul(2)).equal(),padding:`${ce(R)} ${ce(m)}`}},"&-sm":{padding:0,fontSize:u,borderRadius:f,[`input${n}-input`]:{height:F(x).sub(F(a).mul(2)).equal(),padding:`${ce(C)} ${ce(d)}`}},"&-out-of-range":{[`${n}-input-wrap`]:{input:{color:E}}},"&-group":Object.assign(Object.assign(Object.assign({},St(t)),ra(t)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${n}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${n}-group-addon`]:{borderRadius:V,fontSize:t.fontSizeLG}},"&-sm":{[`${n}-group-addon`]:{borderRadius:f}}},la(t)),ia(t)),{[`&:not(${n}-compact-first-item):not(${n}-compact-last-item)${n}-compact-item`]:{[`${n}, ${n}-group-addon`]:{borderRadius:0}},[`&:not(${n}-compact-last-item)${n}-compact-first-item`]:{[`${n}, ${n}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${n}-compact-first-item)${n}-compact-last-item`]:{[`${n}, ${n}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${n}-input`]:{cursor:"not-allowed"},[n]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},St(t)),{width:"100%",padding:`${ce(k)} ${ce(h)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:i,outline:0,transition:`all ${p} linear`,appearance:"textfield",fontSize:"inherit"}),aa(t.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${n}-handler-wrap, &-focused ${n}-handler-wrap`]:{width:t.handleWidth,opacity:1}})},{[n]:Object.assign(Object.assign(Object.assign({[`${n}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:t.handleVisibleWidth,opacity:b,height:"100%",borderStartStartRadius:0,borderStartEndRadius:i,borderEndEndRadius:i,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${p}`,overflow:"hidden",[`${n}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${n}-handler-up-inner,
              ${n}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:t.handleFontSize}}},[`${n}-handler`]:{height:"50%",overflow:"hidden",color:v,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${ce(a)} ${l} ${I}`,transition:`all ${p} linear`,"&:active":{background:A},"&:hover":{height:"60%",[`
              ${n}-handler-up-inner,
              ${n}-handler-down-inner
            `]:{color:c}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},oa()),{color:v,transition:`all ${p} linear`,userSelect:"none"})},[`${n}-handler-up`]:{borderStartEndRadius:i},[`${n}-handler-down`]:{borderEndEndRadius:i}},pn(t,"lg")),pn(t,"sm")),{"&-disabled, &-readonly":{[`${n}-handler-wrap`]:{display:"none"},[`${n}-input`]:{color:"inherit"}},[`
          ${n}-handler-up-disabled,
          ${n}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${n}-handler-up-disabled:hover &-handler-up-inner,
          ${n}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:z}})}]},ir=t=>{const{componentCls:n,paddingBlock:a,paddingInline:l,inputAffixPadding:i,controlWidth:u,borderRadiusLG:y,borderRadiusSM:g,paddingInlineLG:x,paddingInlineSM:E,paddingBlockLG:d,paddingBlockSM:C,motionDurationMid:R}=t;return{[`${n}-affix-wrapper`]:Object.assign(Object.assign({[`input${n}-input`]:{padding:`${ce(a)} 0`}},Nn(t)),{position:"relative",display:"inline-flex",alignItems:"center",width:u,padding:0,paddingInlineStart:l,"&-lg":{borderRadius:y,paddingInlineStart:x,[`input${n}-input`]:{padding:`${ce(d)} 0`}},"&-sm":{borderRadius:g,paddingInlineStart:E,[`input${n}-input`]:{padding:`${ce(C)} 0`}},[`&:not(${n}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${n}-disabled`]:{background:"transparent"},[`> div${n}`]:{width:"100%",border:"none",outline:"none",[`&${n}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${n}-handler-wrap`]:{zIndex:2},[n]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:i},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:l,marginInlineStart:i,transition:`margin ${R}`}},[`&:hover ${n}-handler-wrap, &-focused ${n}-handler-wrap`]:{width:t.handleWidth,opacity:1},[`&:not(${n}-affix-wrapper-without-controls):hover ${n}-suffix`]:{marginInlineEnd:t.calc(t.handleWidth).add(l).equal()}})}},or=$n("InputNumber",t=>{const n=kn(t,Qn(t));return[lr(n),ir(n),Jn(n)]},rr,{unitless:{handleOpacity:!0}});var sr=function(t,n){var a={};for(var l in t)Object.prototype.hasOwnProperty.call(t,l)&&n.indexOf(l)<0&&(a[l]=t[l]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,l=Object.getOwnPropertySymbols(t);i<l.length;i++)n.indexOf(l[i])<0&&Object.prototype.propertyIsEnumerable.call(t,l[i])&&(a[l[i]]=t[l[i]]);return a};const An=o.forwardRef((t,n)=>{const{getPrefixCls:a,direction:l}=o.useContext(sa),i=o.useRef(null);o.useImperativeHandle(n,()=>i.current);const{className:u,rootClassName:y,size:g,disabled:x,prefixCls:E,addonBefore:d,addonAfter:C,prefix:R,suffix:m,bordered:v,readOnly:p,status:c,controls:b,variant:h}=t,k=sr(t,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),w=a("input-number",E),A=ca(w),[z,f,V]=or(w,A),{compactSize:L,compactItemClassnames:I}=ua(w,l);let D=o.createElement(Va,{className:`${w}-handler-up-inner`}),$=o.createElement(fa,{className:`${w}-handler-down-inner`});const F=typeof b=="boolean"?b:void 0;typeof b=="object"&&(D=typeof b.upIcon>"u"?D:o.createElement("span",{className:`${w}-handler-up-inner`},b.upIcon),$=typeof b.downIcon>"u"?$:o.createElement("span",{className:`${w}-handler-down-inner`},b.downIcon));const{hasFeedback:X,status:Y,isFormItemInput:ie,feedbackIcon:W}=o.useContext(ma),oe=pa(Y,c),K=da(B=>{var me;return(me=g??L)!==null&&me!==void 0?me:B}),he=o.useContext(Dn),ue=x??he,[de,Q]=ga("inputNumber",h,v),ne=X&&o.createElement(o.Fragment,null,W),q=be({[`${w}-lg`]:K==="large",[`${w}-sm`]:K==="small",[`${w}-rtl`]:l==="rtl",[`${w}-in-form-item`]:ie},f),H=`${w}-group`,G=o.createElement(ar,Object.assign({ref:i,disabled:ue,className:be(V,A,u,y,I),upHandler:D,downHandler:$,prefixCls:w,readOnly:p,controls:F,prefix:R,suffix:ne||m,addonBefore:d&&o.createElement(an,{form:!0,space:!0},d),addonAfter:C&&o.createElement(an,{form:!0,space:!0},C),classNames:{input:q,variant:be({[`${w}-${de}`]:Q},nn(w,oe,X)),affixWrapper:be({[`${w}-affix-wrapper-sm`]:K==="small",[`${w}-affix-wrapper-lg`]:K==="large",[`${w}-affix-wrapper-rtl`]:l==="rtl",[`${w}-affix-wrapper-without-controls`]:b===!1||ue},f),wrapper:be({[`${H}-rtl`]:l==="rtl"},f),groupWrapper:be({[`${w}-group-wrapper-sm`]:K==="small",[`${w}-group-wrapper-lg`]:K==="large",[`${w}-group-wrapper-rtl`]:l==="rtl",[`${w}-group-wrapper-${de}`]:Q},nn(`${w}-group-wrapper`,oe,X),f)}},k));return z(G)}),tt=An,cr=t=>o.createElement(ha,{theme:{components:{InputNumber:{handleVisible:!0}}}},o.createElement(An,Object.assign({},t)));tt._InternalPanelDoNotUseOrYouWillBeFired=cr;function zt(t,n,a){return(t-n)/(a-n)}function Vt(t,n,a,l){var i=zt(n,a,l),u={};switch(t){case"rtl":u.right="".concat(i*100,"%"),u.transform="translateX(50%)";break;case"btt":u.bottom="".concat(i*100,"%"),u.transform="translateY(50%)";break;case"ttb":u.top="".concat(i*100,"%"),u.transform="translateY(-50%)";break;default:u.left="".concat(i*100,"%"),u.transform="translateX(-50%)";break}return u}function ot(t,n){return Array.isArray(t)?t[n]:t}var ut=o.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),ur=o.createContext({}),mr=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],hn=o.forwardRef(function(t,n){var a=t.prefixCls,l=t.value,i=t.valueIndex,u=t.onStartMove,y=t.onDelete,g=t.style,x=t.render,E=t.dragging,d=t.draggingDelete,C=t.onOffsetChange,R=t.onChangeComplete,m=t.onFocus,v=t.onMouseEnter,p=wt(t,mr),c=o.useContext(ut),b=c.min,h=c.max,k=c.direction,w=c.disabled,A=c.keyboard,z=c.range,f=c.tabIndex,V=c.ariaLabelForHandle,L=c.ariaLabelledByForHandle,I=c.ariaRequired,D=c.ariaValueTextFormatterForHandle,$=c.styles,F=c.classNames,X="".concat(a,"-handle"),Y=function(q){w||u(q,i)},ie=function(q){m==null||m(q,i)},W=function(q){v(q,i)},oe=function(q){if(!w&&A){var H=null;switch(q.which||q.keyCode){case Te.LEFT:H=k==="ltr"||k==="btt"?-1:1;break;case Te.RIGHT:H=k==="ltr"||k==="btt"?1:-1;break;case Te.UP:H=k!=="ttb"?1:-1;break;case Te.DOWN:H=k!=="ttb"?-1:1;break;case Te.HOME:H="min";break;case Te.END:H="max";break;case Te.PAGE_UP:H=2;break;case Te.PAGE_DOWN:H=-2;break;case Te.BACKSPACE:case Te.DELETE:y(i);break}H!==null&&(q.preventDefault(),C(H,i))}},K=function(q){switch(q.which||q.keyCode){case Te.LEFT:case Te.RIGHT:case Te.UP:case Te.DOWN:case Te.HOME:case Te.END:case Te.PAGE_UP:case Te.PAGE_DOWN:R==null||R();break}},he=Vt(k,l,b,h),ue={};if(i!==null){var de;ue={tabIndex:w?null:ot(f,i),role:"slider","aria-valuemin":b,"aria-valuemax":h,"aria-valuenow":l,"aria-disabled":w,"aria-label":ot(V,i),"aria-labelledby":ot(L,i),"aria-required":ot(I,i),"aria-valuetext":(de=ot(D,i))===null||de===void 0?void 0:de(l),"aria-orientation":k==="ltr"||k==="rtl"?"horizontal":"vertical",onMouseDown:Y,onTouchStart:Y,onFocus:ie,onMouseEnter:W,onKeyDown:oe,onKeyUp:K}}var Q=o.createElement("div",nt({ref:n,className:be(X,ae(ae(ae({},"".concat(X,"-").concat(i+1),i!==null&&z),"".concat(X,"-dragging"),E),"".concat(X,"-dragging-delete"),d),F.handle),style:He(He(He({},he),g),$.handle)},ue,p));return x&&(Q=x(Q,{index:i,prefixCls:a,value:l,dragging:E,draggingDelete:d})),Q}),dr=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],gr=o.forwardRef(function(t,n){var a=t.prefixCls,l=t.style,i=t.onStartMove,u=t.onOffsetChange,y=t.values,g=t.handleRender,x=t.activeHandleRender,E=t.draggingIndex,d=t.draggingDelete,C=t.onFocus,R=wt(t,dr),m=o.useRef({}),v=o.useState(!1),p=$e(v,2),c=p[0],b=p[1],h=o.useState(-1),k=$e(h,2),w=k[0],A=k[1],z=function(D){A(D),b(!0)},f=function(D,$){z($),C==null||C(D)},V=function(D,$){z($)};o.useImperativeHandle(n,function(){return{focus:function(D){var $;($=m.current[D])===null||$===void 0||$.focus()},hideHelp:function(){Ea.flushSync(function(){b(!1)})}}});var L=He({prefixCls:a,onStartMove:i,onOffsetChange:u,render:g,onFocus:f,onMouseEnter:V},R);return o.createElement(o.Fragment,null,y.map(function(I,D){var $=E===D;return o.createElement(hn,nt({ref:function(X){X?m.current[D]=X:delete m.current[D]},dragging:$,draggingDelete:$&&d,style:ot(l,D),key:D,value:I,valueIndex:D},L))}),x&&c&&o.createElement(hn,nt({key:"a11y"},L,{value:y[w],valueIndex:null,dragging:E!==-1,draggingDelete:d,render:x,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))}),fr=function(n){var a=n.prefixCls,l=n.style,i=n.children,u=n.value,y=n.onClick,g=o.useContext(ut),x=g.min,E=g.max,d=g.direction,C=g.includedStart,R=g.includedEnd,m=g.included,v="".concat(a,"-text"),p=Vt(d,u,x,E);return o.createElement("span",{className:be(v,ae({},"".concat(v,"-active"),m&&C<=u&&u<=R)),style:He(He({},p),l),onMouseDown:function(b){b.stopPropagation()},onClick:function(){y(u)}},i)},pr=function(n){var a=n.prefixCls,l=n.marks,i=n.onClick,u="".concat(a,"-mark");return l.length?o.createElement("div",{className:u},l.map(function(y){var g=y.value,x=y.style,E=y.label;return o.createElement(fr,{key:g,prefixCls:u,style:x,value:g,onClick:i},E)})):null},hr=function(n){var a=n.prefixCls,l=n.value,i=n.style,u=n.activeStyle,y=o.useContext(ut),g=y.min,x=y.max,E=y.direction,d=y.included,C=y.includedStart,R=y.includedEnd,m="".concat(a,"-dot"),v=d&&C<=l&&l<=R,p=He(He({},Vt(E,l,g,x)),typeof i=="function"?i(l):i);return v&&(p=He(He({},p),typeof u=="function"?u(l):u)),o.createElement("span",{className:be(m,ae({},"".concat(m,"-active"),v)),style:p})},Er=function(n){var a=n.prefixCls,l=n.marks,i=n.dots,u=n.style,y=n.activeStyle,g=o.useContext(ut),x=g.min,E=g.max,d=g.step,C=o.useMemo(function(){var R=new Set;if(l.forEach(function(v){R.add(v.value)}),i&&d!==null)for(var m=x;m<=E;)R.add(m),m+=d;return Array.from(R)},[x,E,d,i,l]);return o.createElement("div",{className:"".concat(a,"-step")},C.map(function(R){return o.createElement(hr,{prefixCls:a,key:R,value:R,style:u,activeStyle:y})}))},En=function(n){var a=n.prefixCls,l=n.style,i=n.start,u=n.end,y=n.index,g=n.onStartMove,x=n.replaceCls,E=o.useContext(ut),d=E.direction,C=E.min,R=E.max,m=E.disabled,v=E.range,p=E.classNames,c="".concat(a,"-track"),b=zt(i,C,R),h=zt(u,C,R),k=function(f){!m&&g&&g(f,-1)},w={};switch(d){case"rtl":w.right="".concat(b*100,"%"),w.width="".concat(h*100-b*100,"%");break;case"btt":w.bottom="".concat(b*100,"%"),w.height="".concat(h*100-b*100,"%");break;case"ttb":w.top="".concat(b*100,"%"),w.height="".concat(h*100-b*100,"%");break;default:w.left="".concat(b*100,"%"),w.width="".concat(h*100-b*100,"%")}var A=x||be(c,ae(ae({},"".concat(c,"-").concat(y+1),y!==null&&v),"".concat(a,"-track-draggable"),g),p.track);return o.createElement("div",{className:A,style:He(He({},w),l),onMouseDown:k,onTouchStart:k})},vr=function(n){var a=n.prefixCls,l=n.style,i=n.values,u=n.startPoint,y=n.onStartMove,g=o.useContext(ut),x=g.included,E=g.range,d=g.min,C=g.styles,R=g.classNames,m=o.useMemo(function(){if(!E){if(i.length===0)return[];var p=u??d,c=i[0];return[{start:Math.min(p,c),end:Math.max(p,c)}]}for(var b=[],h=0;h<i.length-1;h+=1)b.push({start:i[h],end:i[h+1]});return b},[i,E,u,d]);if(!x)return null;var v=m!=null&&m.length&&(R.tracks||C.tracks)?o.createElement(En,{index:null,prefixCls:a,start:m[0].start,end:m[m.length-1].end,replaceCls:be(R.tracks,"".concat(a,"-tracks")),style:C.tracks}):null;return o.createElement(o.Fragment,null,v,m.map(function(p,c){var b=p.start,h=p.end;return o.createElement(En,{index:c,prefixCls:a,style:He(He({},ot(l,c)),C.track),start:b,end:h,key:c,onStartMove:y})}))},yr=130;function vn(t){var n="targetTouches"in t?t.targetTouches[0]:t;return{pageX:n.pageX,pageY:n.pageY}}function br(t,n,a,l,i,u,y,g,x,E,d){var C=o.useState(null),R=$e(C,2),m=R[0],v=R[1],p=o.useState(-1),c=$e(p,2),b=c[0],h=c[1],k=o.useState(!1),w=$e(k,2),A=w[0],z=w[1],f=o.useState(a),V=$e(f,2),L=V[0],I=V[1],D=o.useState(a),$=$e(D,2),F=$[0],X=$[1],Y=o.useRef(null),ie=o.useRef(null),W=o.useRef(null),oe=o.useContext(ur),K=oe.onDragStart,he=oe.onDragChange;xn(function(){b===-1&&I(a)},[a,b]),o.useEffect(function(){return function(){document.removeEventListener("mousemove",Y.current),document.removeEventListener("mouseup",ie.current),W.current&&(W.current.removeEventListener("touchmove",Y.current),W.current.removeEventListener("touchend",ie.current))}},[]);var ue=function(H,G,B){G!==void 0&&v(G),I(H);var me=H;B&&(me=H.filter(function(re,ge){return ge!==b})),y(me),he&&he({rawValues:H,deleteIndex:B?b:-1,draggingIndex:b,draggingValue:G})},de=Ct(function(q,H,G){if(q===-1){var B=F[0],me=F[F.length-1],re=l-B,ge=i-me,ye=H*(i-l);ye=Math.max(ye,re),ye=Math.min(ye,ge);var Se=u(B+ye);ye=Se-B;var Ie=F.map(function(ke){return ke+ye});ue(Ie)}else{var Pe=(i-l)*H,Be=Xe(L);Be[q]=F[q];var Ce=x(Be,Pe,q,"dist");ue(Ce.values,Ce.value,G)}}),Q=function(H,G,B){H.stopPropagation();var me=B||a,re=me[G];h(G),v(re),X(me),I(me),z(!1);var ge=vn(H),ye=ge.pageX,Se=ge.pageY,Ie=!1;K&&K({rawValues:me,draggingIndex:G,draggingValue:re});var Pe=function(ke){ke.preventDefault();var fe=vn(ke),Ne=fe.pageX,De=fe.pageY,Le=Ne-ye,xe=De-Se,J=t.current.getBoundingClientRect(),Ee=J.width,Fe=J.height,r,T;switch(n){case"btt":r=-xe/Fe,T=Le;break;case"ttb":r=xe/Fe,T=Le;break;case"rtl":r=-Le/Ee,T=xe;break;default:r=Le/Ee,T=xe}Ie=E?Math.abs(T)>yr&&d<L.length:!1,z(Ie),de(G,r,Ie)},Be=function Ce(ke){ke.preventDefault(),document.removeEventListener("mouseup",Ce),document.removeEventListener("mousemove",Pe),W.current&&(W.current.removeEventListener("touchmove",Y.current),W.current.removeEventListener("touchend",ie.current)),Y.current=null,ie.current=null,W.current=null,g(Ie),h(-1),z(!1)};document.addEventListener("mouseup",Be),document.addEventListener("mousemove",Pe),H.currentTarget.addEventListener("touchend",Be),H.currentTarget.addEventListener("touchmove",Pe),Y.current=Pe,ie.current=Be,W.current=H.currentTarget},ne=o.useMemo(function(){var q=Xe(a).sort(function(re,ge){return re-ge}),H=Xe(L).sort(function(re,ge){return re-ge}),G={};H.forEach(function(re){G[re]=(G[re]||0)+1}),q.forEach(function(re){G[re]=(G[re]||0)-1});var B=E?1:0,me=Object.values(G).reduce(function(re,ge){return re+Math.abs(ge)},0);return me<=B?L:a},[a,L,E]);return[b,m,A,ne,Q]}function Sr(t,n,a,l,i,u){var y=o.useCallback(function(m){return Math.max(t,Math.min(n,m))},[t,n]),g=o.useCallback(function(m){if(a!==null){var v=t+Math.round((y(m)-t)/a)*a,p=function(k){return(String(k).split(".")[1]||"").length},c=Math.max(p(a),p(n),p(t)),b=Number(v.toFixed(c));return t<=b&&b<=n?b:null}return null},[a,t,n,y]),x=o.useCallback(function(m){var v=y(m),p=l.map(function(h){return h.value});a!==null&&p.push(g(m)),p.push(t,n);var c=p[0],b=n-t;return p.forEach(function(h){var k=Math.abs(v-h);k<=b&&(c=h,b=k)}),c},[t,n,l,a,y,g]),E=function m(v,p,c){var b=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit";if(typeof p=="number"){var h,k=v[c],w=k+p,A=[];l.forEach(function(I){A.push(I.value)}),A.push(t,n),A.push(g(k));var z=p>0?1:-1;b==="unit"?A.push(g(k+z*a)):A.push(g(w)),A=A.filter(function(I){return I!==null}).filter(function(I){return p<0?I<=k:I>=k}),b==="unit"&&(A=A.filter(function(I){return I!==k}));var f=b==="unit"?k:w;h=A[0];var V=Math.abs(h-f);if(A.forEach(function(I){var D=Math.abs(I-f);D<V&&(h=I,V=D)}),h===void 0)return p<0?t:n;if(b==="dist")return h;if(Math.abs(p)>1){var L=Xe(v);return L[c]=h,m(L,p-z,c,b)}return h}else{if(p==="min")return t;if(p==="max")return n}},d=function(v,p,c){var b=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",h=v[c],k=E(v,p,c,b);return{value:k,changed:k!==h}},C=function(v){return u===null&&v===0||typeof u=="number"&&v<u},R=function(v,p,c){var b=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",h=v.map(x),k=h[c],w=E(h,p,c,b);if(h[c]=w,i===!1){var A=u||0;c>0&&h[c-1]!==k&&(h[c]=Math.max(h[c],h[c-1]+A)),c<h.length-1&&h[c+1]!==k&&(h[c]=Math.min(h[c],h[c+1]-A))}else if(typeof u=="number"||u===null){for(var z=c+1;z<h.length;z+=1)for(var f=!0;C(h[z]-h[z-1])&&f;){var V=d(h,1,z);h[z]=V.value,f=V.changed}for(var L=c;L>0;L-=1)for(var I=!0;C(h[L]-h[L-1])&&I;){var D=d(h,-1,L-1);h[L-1]=D.value,I=D.changed}for(var $=h.length-1;$>0;$-=1)for(var F=!0;C(h[$]-h[$-1])&&F;){var X=d(h,-1,$-1);h[$-1]=X.value,F=X.changed}for(var Y=0;Y<h.length-1;Y+=1)for(var ie=!0;C(h[Y+1]-h[Y])&&ie;){var W=d(h,1,Y+1);h[Y+1]=W.value,ie=W.changed}}return{value:h[c],values:h}};return[x,R]}function Cr(t){return o.useMemo(function(){if(t===!0||!t)return[!!t,!1,!1,0];var n=t.editable,a=t.draggableTrack,l=t.minCount,i=t.maxCount;return[!0,n,!n&&a,l||0,i]},[t])}var xr=o.forwardRef(function(t,n){var a=t.prefixCls,l=a===void 0?"rc-slider":a,i=t.className,u=t.style,y=t.classNames,g=t.styles,x=t.id,E=t.disabled,d=E===void 0?!1:E,C=t.keyboard,R=C===void 0?!0:C,m=t.autoFocus,v=t.onFocus,p=t.onBlur,c=t.min,b=c===void 0?0:c,h=t.max,k=h===void 0?100:h,w=t.step,A=w===void 0?1:w,z=t.value,f=t.defaultValue,V=t.range,L=t.count,I=t.onChange,D=t.onBeforeChange,$=t.onAfterChange,F=t.onChangeComplete,X=t.allowCross,Y=X===void 0?!0:X,ie=t.pushable,W=ie===void 0?!1:ie,oe=t.reverse,K=t.vertical,he=t.included,ue=he===void 0?!0:he,de=t.startPoint,Q=t.trackStyle,ne=t.handleStyle,q=t.railStyle,H=t.dotStyle,G=t.activeDotStyle,B=t.marks,me=t.dots,re=t.handleRender,ge=t.activeHandleRender,ye=t.track,Se=t.tabIndex,Ie=Se===void 0?0:Se,Pe=t.ariaLabelForHandle,Be=t.ariaLabelledByForHandle,Ce=t.ariaRequired,ke=t.ariaValueTextFormatterForHandle,fe=o.useRef(null),Ne=o.useRef(null),De=o.useMemo(function(){return K?oe?"ttb":"btt":oe?"rtl":"ltr"},[oe,K]),Le=Cr(V),xe=$e(Le,5),J=xe[0],Ee=xe[1],Fe=xe[2],r=xe[3],T=xe[4],Z=o.useMemo(function(){return isFinite(b)?b:0},[b]),Ae=o.useMemo(function(){return isFinite(k)?k:100},[k]),je=o.useMemo(function(){return A!==null&&A<=0?1:A},[A]),we=o.useMemo(function(){return typeof W=="boolean"?W?je:!1:W>=0?W:!1},[W,je]),Oe=o.useMemo(function(){return Object.keys(B||{}).map(function(ee){var O=B[ee],le={value:Number(ee)};return O&&wn(O)==="object"&&!o.isValidElement(O)&&("label"in O||"style"in O)?(le.style=O.style,le.label=O.label):le.label=O,le}).filter(function(ee){var O=ee.label;return O||typeof O=="number"}).sort(function(ee,O){return ee.value-O.value})},[B]),gt=Sr(Z,Ae,je,Oe,Y,we),at=$e(gt,2),_e=at[0],Je=at[1],rt=va(f,{value:z}),vt=$e(rt,2),Ve=vt[0],$t=vt[1],N=o.useMemo(function(){var ee=Ve==null?[]:Array.isArray(Ve)?Ve:[Ve],O=$e(ee,1),le=O[0],pe=le===void 0?Z:le,Me=Ve===null?[]:[pe];if(J){if(Me=Xe(ee),L||Ve===void 0){var Ze=L>=0?L+1:2;for(Me=Me.slice(0,Ze);Me.length<Ze;){var Ge;Me.push((Ge=Me[Me.length-1])!==null&&Ge!==void 0?Ge:Z)}}Me.sort(function(Ue,Ye){return Ue-Ye})}return Me.forEach(function(Ue,Ye){Me[Ye]=_e(Ue)}),Me},[Ve,J,Z,L,_e]),M=function(O){return J?O:O[0]},U=Ct(function(ee){var O=Xe(ee).sort(function(le,pe){return le-pe});I&&!ya(O,N,!0)&&I(M(O)),$t(O)}),j=Ct(function(ee){ee&&fe.current.hideHelp();var O=M(N);$==null||$(O),At(!$,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),F==null||F(O)}),ze=function(O){if(!(d||!Ee||N.length<=r)){var le=Xe(N);le.splice(O,1),D==null||D(M(le)),U(le);var pe=Math.max(0,O-1);fe.current.hideHelp(),fe.current.focus(pe)}},lt=br(Ne,De,N,Z,Ae,_e,U,j,Je,Ee,r),We=$e(lt,5),Wt=We[0],Ln=We[1],Fn=We[2],kt=We[3],Gt=We[4],Ut=function(O,le){if(!d){var pe=Xe(N),Me=0,Ze=0,Ge=Ae-Z;N.forEach(function(et,yt){var tn=Math.abs(O-et);tn<=Ge&&(Ge=tn,Me=yt),et<O&&(Ze=yt)});var Ue=Me;Ee&&Ge!==0&&(!T||N.length<T)?(pe.splice(Ze+1,0,O),Ue=Ze+1):pe[Me]=O,J&&!N.length&&L===void 0&&pe.push(O);var Ye=M(pe);if(D==null||D(Ye),U(pe),le){var it,mt;(it=document.activeElement)===null||it===void 0||(mt=it.blur)===null||mt===void 0||mt.call(it),fe.current.focus(Ue),Gt(le,Ue,pe)}else $==null||$(Ye),At(!$,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),F==null||F(Ye)}},_n=function(O){O.preventDefault();var le=Ne.current.getBoundingClientRect(),pe=le.width,Me=le.height,Ze=le.left,Ge=le.top,Ue=le.bottom,Ye=le.right,it=O.clientX,mt=O.clientY,et;switch(De){case"btt":et=(Ue-mt)/Me;break;case"ttb":et=(mt-Ge)/Me;break;case"rtl":et=(Ye-it)/pe;break;default:et=(it-Ze)/pe}var yt=Z+et*(Ae-Z);Ut(_e(yt),O)},zn=o.useState(null),Yt=$e(zn,2),Nt=Yt[0],Kt=Yt[1],jn=function(O,le){if(!d){var pe=Je(N,O,le);D==null||D(M(N)),U(pe.values),Kt(pe.value)}};o.useEffect(function(){if(Nt!==null){var ee=N.indexOf(Nt);ee>=0&&fe.current.focus(ee)}Kt(null)},[Nt]);var qn=o.useMemo(function(){return Fe&&je===null?!1:Fe},[Fe,je]),Xt=Ct(function(ee,O){Gt(ee,O),D==null||D(M(N))}),Qt=Wt!==-1;o.useEffect(function(){if(!Qt){var ee=N.lastIndexOf(Ln);fe.current.focus(ee)}},[Qt]);var ft=o.useMemo(function(){return Xe(kt).sort(function(ee,O){return ee-O})},[kt]),Vn=o.useMemo(function(){return J?[ft[0],ft[ft.length-1]]:[Z,ft[0]]},[ft,J,Z]),Jt=$e(Vn,2),Zt=Jt[0],en=Jt[1];o.useImperativeHandle(n,function(){return{focus:function(){fe.current.focus(0)},blur:function(){var O,le=document,pe=le.activeElement;(O=Ne.current)!==null&&O!==void 0&&O.contains(pe)&&(pe==null||pe.blur())}}}),o.useEffect(function(){m&&fe.current.focus(0)},[]);var Wn=o.useMemo(function(){return{min:Z,max:Ae,direction:De,disabled:d,keyboard:R,step:je,included:ue,includedStart:Zt,includedEnd:en,range:J,tabIndex:Ie,ariaLabelForHandle:Pe,ariaLabelledByForHandle:Be,ariaRequired:Ce,ariaValueTextFormatterForHandle:ke,styles:g||{},classNames:y||{}}},[Z,Ae,De,d,R,je,ue,Zt,en,J,Ie,Pe,Be,Ce,ke,g,y]);return o.createElement(ut.Provider,{value:Wn},o.createElement("div",{ref:Ne,className:be(l,i,ae(ae(ae(ae({},"".concat(l,"-disabled"),d),"".concat(l,"-vertical"),K),"".concat(l,"-horizontal"),!K),"".concat(l,"-with-marks"),Oe.length)),style:u,onMouseDown:_n,id:x},o.createElement("div",{className:be("".concat(l,"-rail"),y==null?void 0:y.rail),style:He(He({},q),g==null?void 0:g.rail)}),ye!==!1&&o.createElement(vr,{prefixCls:l,style:Q,values:N,startPoint:de,onStartMove:qn?Xt:void 0}),o.createElement(Er,{prefixCls:l,marks:Oe,dots:me,style:H,activeStyle:G}),o.createElement(gr,{ref:fe,prefixCls:l,style:ne,values:kt,draggingIndex:Wt,draggingDelete:Fn,onStartMove:Xt,onOffsetChange:jn,onFocus:v,onBlur:p,handleRender:re,activeHandleRender:ge,onChangeComplete:j,onDelete:Ee?ze:void 0}),o.createElement(pr,{prefixCls:l,marks:Oe,onClick:Ut})))});const wr=o.createContext({}),yn=o.forwardRef((t,n)=>{const{open:a,draggingDelete:l,value:i}=t,u=o.useRef(null),y=a&&!l,g=o.useRef(null);function x(){Qe.cancel(g.current),g.current=null}function E(){g.current=Qe(()=>{var d;(d=u.current)===null||d===void 0||d.forceAlign(),g.current=null})}return o.useEffect(()=>(y?E():x(),x),[y,t.title,i]),o.createElement(ba,Object.assign({ref:Rn(u,n)},t,{open:y}))}),Rr=t=>{const{componentCls:n,antCls:a,controlSize:l,dotSize:i,marginFull:u,marginPart:y,colorFillContentHover:g,handleColorDisabled:x,calc:E,handleSize:d,handleSizeHover:C,handleActiveColor:R,handleActiveOutlineColor:m,handleLineWidth:v,handleLineWidthHover:p,motionDurationMid:c}=t;return{[n]:Object.assign(Object.assign({},St(t)),{position:"relative",height:l,margin:`${ce(y)} ${ce(u)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${ce(u)} ${ce(y)}`},[`${n}-rail`]:{position:"absolute",backgroundColor:t.railBg,borderRadius:t.borderRadiusXS,transition:`background-color ${c}`},[`${n}-track,${n}-tracks`]:{position:"absolute",transition:`background-color ${c}`},[`${n}-track`]:{backgroundColor:t.trackBg,borderRadius:t.borderRadiusXS},[`${n}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${n}-rail`]:{backgroundColor:t.railHoverBg},[`${n}-track`]:{backgroundColor:t.trackHoverBg},[`${n}-dot`]:{borderColor:g},[`${n}-handle::after`]:{boxShadow:`0 0 0 ${ce(v)} ${t.colorPrimaryBorderHover}`},[`${n}-dot-active`]:{borderColor:t.dotActiveBorderColor}},[`${n}-handle`]:{position:"absolute",width:d,height:d,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:E(v).mul(-1).equal(),insetBlockStart:E(v).mul(-1).equal(),width:E(d).add(E(v).mul(2)).equal(),height:E(d).add(E(v).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:d,height:d,backgroundColor:t.colorBgElevated,boxShadow:`0 0 0 ${ce(v)} ${t.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${c},
            inset-block-start ${c},
            width ${c},
            height ${c},
            box-shadow ${c},
            outline ${c}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:E(C).sub(d).div(2).add(p).mul(-1).equal(),insetBlockStart:E(C).sub(d).div(2).add(p).mul(-1).equal(),width:E(C).add(E(p).mul(2)).equal(),height:E(C).add(E(p).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${ce(p)} ${R}`,outline:`6px solid ${m}`,width:C,height:C,insetInlineStart:t.calc(d).sub(C).div(2).equal(),insetBlockStart:t.calc(d).sub(C).div(2).equal()}}},[`&-lock ${n}-handle`]:{"&::before, &::after":{transition:"none"}},[`${n}-mark`]:{position:"absolute",fontSize:t.fontSize},[`${n}-mark-text`]:{position:"absolute",display:"inline-block",color:t.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:t.colorText}},[`${n}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${n}-dot`]:{position:"absolute",width:i,height:i,backgroundColor:t.colorBgElevated,border:`${ce(v)} solid ${t.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${t.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:t.dotActiveBorderColor}},[`&${n}-disabled`]:{cursor:"not-allowed",[`${n}-rail`]:{backgroundColor:`${t.railBg} !important`},[`${n}-track`]:{backgroundColor:`${t.trackBgDisabled} !important`},[`
          ${n}-dot
        `]:{backgroundColor:t.colorBgElevated,borderColor:t.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${n}-handle::after`]:{backgroundColor:t.colorBgElevated,cursor:"not-allowed",width:d,height:d,boxShadow:`0 0 0 ${ce(v)} ${x}`,insetInlineStart:0,insetBlockStart:0},[`
          ${n}-mark-text,
          ${n}-dot
        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${a}-tooltip-inner`]:{minWidth:"unset"}})}},Hn=(t,n)=>{const{componentCls:a,railSize:l,handleSize:i,dotSize:u,marginFull:y,calc:g}=t,x=n?"paddingBlock":"paddingInline",E=n?"width":"height",d=n?"height":"width",C=n?"insetBlockStart":"insetInlineStart",R=n?"top":"insetInlineStart",m=g(l).mul(3).sub(i).div(2).equal(),v=g(i).sub(l).div(2).equal(),p=n?{borderWidth:`${ce(v)} 0`,transform:`translateY(${ce(g(v).mul(-1).equal())})`}:{borderWidth:`0 ${ce(v)}`,transform:`translateX(${ce(t.calc(v).mul(-1).equal())})`};return{[x]:l,[d]:g(l).mul(3).equal(),[`${a}-rail`]:{[E]:"100%",[d]:l},[`${a}-track,${a}-tracks`]:{[d]:l},[`${a}-track-draggable`]:Object.assign({},p),[`${a}-handle`]:{[C]:m},[`${a}-mark`]:{insetInlineStart:0,top:0,[R]:g(l).mul(3).add(n?0:y).equal(),[E]:"100%"},[`${a}-step`]:{insetInlineStart:0,top:0,[R]:l,[E]:"100%",[d]:l},[`${a}-dot`]:{position:"absolute",[C]:g(l).sub(u).div(2).equal()}}},$r=t=>{const{componentCls:n,marginPartWithMark:a}=t;return{[`${n}-horizontal`]:Object.assign(Object.assign({},Hn(t,!0)),{[`&${n}-with-marks`]:{marginBottom:a}})}},kr=t=>{const{componentCls:n}=t;return{[`${n}-vertical`]:Object.assign(Object.assign({},Hn(t,!1)),{height:"100%"})}},Nr=t=>{const a=t.controlHeightLG/4,l=t.controlHeightSM/2,i=t.lineWidth+1,u=t.lineWidth+1*1.5,y=t.colorPrimary,g=new Ht(y).setA(.2).toRgbString();return{controlSize:a,railSize:4,handleSize:a,handleSizeHover:l,dotSize:8,handleLineWidth:i,handleLineWidthHover:u,railBg:t.colorFillTertiary,railHoverBg:t.colorFillSecondary,trackBg:t.colorPrimaryBorder,trackHoverBg:t.colorPrimaryBorderHover,handleColor:t.colorPrimaryBorder,handleActiveColor:y,handleActiveOutlineColor:g,handleColorDisabled:new Ht(t.colorTextDisabled).onBackground(t.colorBgContainer).toHexString(),dotBorderColor:t.colorBorderSecondary,dotActiveBorderColor:t.colorPrimaryBorder,trackBgDisabled:t.colorBgContainerDisabled}},Dr=$n("Slider",t=>{const n=kn(t,{marginPart:t.calc(t.controlHeight).sub(t.controlSize).div(2).equal(),marginFull:t.calc(t.controlSize).div(2).equal(),marginPartWithMark:t.calc(t.controlHeightLG).sub(t.controlSize).equal()});return[Rr(n),$r(n),kr(n)]},Nr);function Ot(){const[t,n]=o.useState(!1),a=o.useRef(null),l=()=>{Qe.cancel(a.current)},i=u=>{l(),u?n(u):a.current=Qe(()=>{n(u)})};return o.useEffect(()=>l,[]),[t,i]}var Mr=function(t,n){var a={};for(var l in t)Object.prototype.hasOwnProperty.call(t,l)&&n.indexOf(l)<0&&(a[l]=t[l]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,l=Object.getOwnPropertySymbols(t);i<l.length;i++)n.indexOf(l[i])<0&&Object.prototype.propertyIsEnumerable.call(t,l[i])&&(a[l[i]]=t[l[i]]);return a};function Tr(t,n){return t||t===null?t:n||n===null?n:a=>typeof a=="number"?a.toString():""}const bn=e.forwardRef((t,n)=>{const{prefixCls:a,range:l,className:i,rootClassName:u,style:y,disabled:g,tooltipPrefixCls:x,tipFormatter:E,tooltipVisible:d,getTooltipPopupContainer:C,tooltipPlacement:R,tooltip:m={},onChangeComplete:v,classNames:p,styles:c}=t,b=Mr(t,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:h}=t,{getPrefixCls:k,direction:w,className:A,style:z,classNames:f,styles:V,getPopupContainer:L}=Sa("slider"),I=e.useContext(Dn),D=g??I,{handleRender:$,direction:F}=e.useContext(wr),Y=(F||w)==="rtl",[ie,W]=Ot(),[oe,K]=Ot(),he=Object.assign({},m),{open:ue,placement:de,getPopupContainer:Q,prefixCls:ne,formatter:q}=he,H=ue??d,G=(ie||oe)&&H!==!1,B=Tr(q,E),[me,re]=Ot(),ge=J=>{v==null||v(J),re(!1)},ye=(J,Ee)=>J||(Ee?Y?"left":"right":"top"),Se=k("slider",a),[Ie,Pe,Be]=Dr(Se),Ce=be(i,A,f.root,p==null?void 0:p.root,u,{[`${Se}-rtl`]:Y,[`${Se}-lock`]:me},Pe,Be);Y&&!b.vertical&&(b.reverse=!b.reverse),e.useEffect(()=>{const J=()=>{Qe(()=>{K(!1)},1)};return document.addEventListener("mouseup",J),()=>{document.removeEventListener("mouseup",J)}},[]);const ke=l&&!H,fe=$||((J,Ee)=>{const{index:Fe}=Ee,r=J.props;function T(we,Oe,gt){var at,_e,Je,rt;gt&&((_e=(at=b)[we])===null||_e===void 0||_e.call(at,Oe)),(rt=(Je=r)[we])===null||rt===void 0||rt.call(Je,Oe)}const Z=Object.assign(Object.assign({},r),{onMouseEnter:we=>{W(!0),T("onMouseEnter",we)},onMouseLeave:we=>{W(!1),T("onMouseLeave",we)},onMouseDown:we=>{K(!0),re(!0),T("onMouseDown",we)},onFocus:we=>{var Oe;K(!0),(Oe=b.onFocus)===null||Oe===void 0||Oe.call(b,we),T("onFocus",we,!0)},onBlur:we=>{var Oe;K(!1),(Oe=b.onBlur)===null||Oe===void 0||Oe.call(b,we),T("onBlur",we,!0)}}),Ae=e.cloneElement(J,Z),je=(!!H||G)&&B!==null;return ke?Ae:e.createElement(yn,Object.assign({},he,{prefixCls:k("tooltip",ne??x),title:B?B(Ee.value):"",value:Ee.value,open:je,placement:ye(de??R,h),key:Fe,classNames:{root:`${Se}-tooltip`},getPopupContainer:Q||C||L}),Ae)}),Ne=ke?(J,Ee)=>{const Fe=e.cloneElement(J,{style:Object.assign(Object.assign({},J.props.style),{visibility:"hidden"})});return e.createElement(yn,Object.assign({},he,{prefixCls:k("tooltip",ne??x),title:B?B(Ee.value):"",open:B!==null&&G,placement:ye(de??R,h),key:"tooltip",classNames:{root:`${Se}-tooltip`},getPopupContainer:Q||C||L,draggingDelete:Ee.draggingDelete}),Fe)}:void 0,De=Object.assign(Object.assign(Object.assign(Object.assign({},V.root),z),c==null?void 0:c.root),y),Le=Object.assign(Object.assign({},V.tracks),c==null?void 0:c.tracks),xe=be(f.tracks,p==null?void 0:p.tracks);return Ie(e.createElement(xr,Object.assign({},b,{classNames:Object.assign({handle:be(f.handle,p==null?void 0:p.handle),rail:be(f.rail,p==null?void 0:p.rail),track:be(f.track,p==null?void 0:p.track)},xe?{tracks:xe}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},V.handle),c==null?void 0:c.handle),rail:Object.assign(Object.assign({},V.rail),c==null?void 0:c.rail),track:Object.assign(Object.assign({},V.track),c==null?void 0:c.track)},Object.keys(Le).length?{tracks:Le}:{}),step:b.step,range:l,className:Ce,style:De,disabled:D,ref:n,prefixCls:Se,handleRender:fe,activeHandleRender:Ne,onChangeComplete:ge})))}),{Title:Pt,Text:te}=Bn;function Ir(){var I,D;const{darkMode:t,toggleDarkMode:n}=Mn(),{theme:a,tables:l,charts:i,refresh:u,notifications:y,email:g,loading:x,toggleCompactMode:E,toggleAnimations:d}=Tn(),{themeClasses:C,chartTheme:R,tableConfig:m,chartConfig:v,refreshConfig:p}=Ca(),c=ka({charts:i,theme:a});o.useEffect(()=>{console.log("SettingsPreview - Charts settings:",i),console.log("SettingsPreview - Settings theme:",a),console.log("SettingsPreview - MainLayout dark mode:",t),console.log("SettingsPreview - Chart config:",v)},[i,a,t,v]);const[b,h]=o.useState(0),[k,w]=o.useState(new Date),A=[{key:"1",name:"Machine A",status:"Running",efficiency:95,production:1250},{key:"2",name:"Machine B",status:"Maintenance",efficiency:0,production:0},{key:"3",name:"Machine C",status:"Running",efficiency:87,production:1100},{key:"4",name:"Machine D",status:"Running",efficiency:92,production:1180},{key:"5",name:"Machine E",status:"Stopped",efficiency:0,production:0}],z=[{name:"Jan",production:4e3,efficiency:85},{name:"Feb",production:3e3,efficiency:78},{name:"Mar",production:2e3,efficiency:92},{name:"Apr",production:2780,efficiency:88},{name:"May",production:1890,efficiency:95},{name:"Jun",production:2390,efficiency:82}],f=[{name:"Running",value:60,color:"#52c41a"},{name:"Maintenance",value:25,color:"#faad14"},{name:"Stopped",value:15,color:"#ff4d4f"}],V=[{title:"Machine",dataIndex:"name",key:"name"},{title:"Status",dataIndex:"status",key:"status",render:$=>{const F=$==="Running"?"green":$==="Maintenance"?"orange":"red";return e.createElement(Lt,{color:F},$)}},{title:"Efficiency (%)",dataIndex:"efficiency",key:"efficiency",render:$=>e.createElement(Oa,{percent:$,size:"small",status:$>90?"success":$>70?"normal":"exception"})},{title:"Production",dataIndex:"production",key:"production",render:$=>$.toLocaleString()}];o.useEffect(()=>{if(!p.autoRefreshEnabled)return;const $=setInterval(()=>{h(F=>F+1),w(new Date)},p.dashboardInterval*1e3);return()=>clearInterval($)},[p.autoRefreshEnabled,p.dashboardInterval]);const L=()=>{const $=c.responsiveContainerProps,F=c.getGridConfig,X=c.getAxisConfig,Y=c.getTooltipConfig,ie=c.getLegendConfig,W=c.getAnimationConfig,oe=c.getColorScheme,K=oe||[pt.PRIMARY_BLUE,pt.SECONDARY_BLUE,pt.CHART_TERTIARY,pt.SUCCESS_GREEN,pt.WARNING_ORANGE],he=c.applySettingsToData(z,"bar"),ue=c.applySettingsToData(f,"pie");switch("bar"){case"line":const Q=c.getLineChartConfig();return e.createElement(Tt,{...$},e.createElement(Ba,{data:he,margin:c.getChartMargins()},e.createElement(rn,{...F}),e.createElement(ln,{dataKey:"name",...X}),e.createElement(on,{...X}),e.createElement(It,{...Y}),v.showLegend&&e.createElement(Bt,{...ie}),e.createElement(cn,{type:"monotone",dataKey:"production",stroke:K[0],strokeWidth:2,dot:Q.lineProps.dot,activeDot:Q.lineProps.activeDot,...W}),e.createElement(cn,{type:"monotone",dataKey:"efficiency",stroke:K[1],strokeWidth:2,dot:Q.lineProps.dot,activeDot:Q.lineProps.activeDot,...W})));case"pie":const ne=c.getPieChartConfig();return e.createElement(Tt,{...$},e.createElement(Ma,{margin:c.getChartMargins()},e.createElement(Ta,{data:ue,...ne.pieProps,labelLine:!1,label:({name:H,percent:G})=>`${H} ${(G*100).toFixed(0)}%`,...W},ue.map((H,G)=>e.createElement(Ia,{key:`cell-${G}`,fill:H.fill||oe[G%oe.length]}))),e.createElement(It,{...Y}),v.showLegend&&e.createElement(Bt,{...ie})));default:const q=c.getBarChartConfig;return e.createElement(Tt,{...$},e.createElement(Da,{data:he,margin:c.getChartMargins},e.createElement(rn,{...F}),e.createElement(ln,{dataKey:"name",...X}),e.createElement(on,{...X}),e.createElement(It,{...Y}),v.showLegend&&e.createElement(Bt,{...ie}),e.createElement(sn,{dataKey:"production",fill:K[0],radius:q.barProps.radius,...W}),e.createElement(sn,{dataKey:"efficiency",fill:K[1],radius:q.barProps.radius,...W})))}};return x?e.createElement("div",{style:{textAlign:"center",padding:"50px"}},e.createElement(In,{size:"large",tip:"Loading settings preview..."})):e.createElement("div",{className:C,style:{padding:"24px"}},e.createElement(Pt,{level:3},"Settings Preview - Live Demo"),e.createElement(te,{type:"secondary"},'This preview updates immediately when you change settings. All changes demonstrate the "immediate effect guarantee".'),e.createElement(se,{style:{marginTop:"16px",marginBottom:"16px"}},e.createElement(Pt,{level:4},"Quick Theme Controls"),e.createElement(ht,null,e.createElement(Et,{type:t?"primary":"default",onClick:n,icon:t?"🌙":"☀️"},t?"Dark Mode":"Light Mode"),e.createElement(Et,{type:a.compactMode?"primary":"default",onClick:E},a.compactMode?"Compact":"Normal"," Mode"),e.createElement(Et,{type:a.animationsEnabled?"primary":"default",onClick:d},"Animations ",a.animationsEnabled?"ON":"OFF"))),e.createElement(se,{style:{marginBottom:"16px"}},e.createElement(Pt,{level:4},"Current Settings Status"),e.createElement(ve,{gutter:[16,8]},e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Theme:")," ",t?"Dark":"Light"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Layout:")," ",a.compactMode?"Compact":"Normal"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Animations:")," ",a.animationsEnabled?"Enabled":"Disabled"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Chart Animations:")," ",a.chartAnimations?"Enabled":"Disabled"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Page Size:")," ",l.defaultPageSize," rows"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Color Scheme:")," ",i.colorScheme?i.colorScheme.toUpperCase():"N/A"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Auto Refresh:")," ",u.autoRefreshEnabled?"ON":"OFF"),e.createElement(S,{span:6},e.createElement(te,{strong:!0},"Email:")," ",g.enabled?"Enabled":"Disabled"))),e.createElement(ve,{gutter:[16,16],style:{marginTop:"24px"}},e.createElement(S,{span:6},e.createElement(se,null,e.createElement(bt,{title:"Total Production",value:16060,suffix:"units",valueStyle:{color:"#3f8600"}}))),e.createElement(S,{span:6},e.createElement(se,null,e.createElement(bt,{title:"Efficiency",value:87.5,suffix:"%",precision:1,valueStyle:{color:"#cf1322"}}))),e.createElement(S,{span:6},e.createElement(se,null,e.createElement(bt,{title:"Active Machines",value:3,suffix:"/ 5",valueStyle:{color:"#1890ff"}}))),e.createElement(S,{span:6},e.createElement(se,null,e.createElement(bt,{title:"Refresh Count",value:b,suffix:p.autoRefreshEnabled?"(Auto)":"(Manual)"}),e.createElement(te,{type:"secondary",style:{fontSize:"12px"}},"Last: ",k.toLocaleTimeString())))),e.createElement(ve,{gutter:[16,16],style:{marginTop:"16px"}},e.createElement(S,{span:16},e.createElement(se,{title:`Sample ${v.defaultType} Chart`},e.createElement("div",{key:`chart-${v.defaultType}-${v.showLegend}-${JSON.stringify(i.layout)}-${JSON.stringify(i.dataDisplay)}`},L()))),e.createElement(S,{span:8},e.createElement(se,{title:"Settings Effects Demo"},e.createElement(ht,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(te,{strong:!0},"Chart Type:")," ",v.defaultType,e.createElement("br",null),e.createElement(te,{type:"secondary"},"Changes chart visualization immediately")),e.createElement("div",null,e.createElement(te,{strong:!0},"Show Legend:")," ",v.showLegend?"Yes":"No",e.createElement("br",null),e.createElement(te,{type:"secondary"},"Toggles chart legend visibility")),e.createElement("div",null,e.createElement(te,{strong:!0},"Animations:")," ",v.animations?"Enabled":"Disabled",e.createElement("br",null),e.createElement(te,{type:"secondary"},"Controls chart transition effects")),e.createElement("div",null,e.createElement(te,{strong:!0},"Page Size:")," ",m.pageSize," rows",e.createElement("br",null),e.createElement(te,{type:"secondary"},"Changes table pagination immediately")),e.createElement("div",null,e.createElement(te,{strong:!0},"Refresh Interval:")," ",p.dashboardInterval,"s",e.createElement("br",null),e.createElement(te,{type:"secondary"},"Updates auto-refresh timing")),e.createElement("div",null,e.createElement(te,{strong:!0},"Auto Refresh:")," ",p.autoRefreshEnabled?"On":"Off",e.createElement("br",null),e.createElement(te,{type:"secondary"},"Enables/disables automatic data updates")))),e.createElement(se,{title:"Notification Settings",style:{marginTop:"16px"}},e.createElement(ht,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(te,{strong:!0},"Categories Enabled:"),e.createElement("br",null),Object.entries(y.categories).filter(([$,F])=>F).map(([$])=>e.createElement(Lt,{key:$,color:"blue",style:{margin:"2px"}},$.replace("_"," ").toUpperCase()))),e.createElement("div",null,e.createElement(te,{strong:!0},"Sound:")," ",y.behavior.sound?"Enabled":"Disabled"),e.createElement("div",null,e.createElement(te,{strong:!0},"Max Visible:")," ",y.behavior.maxVisible))),e.createElement(se,{title:"Email Settings",style:{marginTop:"16px"}},e.createElement(ht,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(te,{strong:!0},"Email Notifications:")," ",g.enabled?"Enabled":"Disabled"),g.enabled&&e.createElement(e.Fragment,null,e.createElement("div",null,e.createElement(te,{strong:!0},"Frequency:")," ",g.frequency),e.createElement("div",null,e.createElement(te,{strong:!0},"Template:")," ",g.template),((I=g.quietHours)==null?void 0:I.enabled)&&e.createElement("div",null,e.createElement(te,{strong:!0},"Quiet Hours:")," ",g.quietHours.start," - ",g.quietHours.end)))),p.autoRefreshEnabled&&e.createElement(Ft,{message:"Auto Refresh Active",description:`Data refreshes every ${p.dashboardInterval} seconds`,type:"info",showIcon:!0,style:{marginTop:"16px"}}))),e.createElement(ve,{style:{marginTop:"16px"}},e.createElement(S,{span:24},e.createElement(se,{title:"Sample Data Table"},e.createElement(xa,{columns:V,dataSource:A,pagination:{pageSize:m.pageSize,showQuickJumper:m.showQuickJumper,showSizeChanger:!0,pageSizeOptions:((D=m.pageSizeOptions)==null?void 0:D.map(String))||["10","20","50","100"]},size:a.compactMode?"small":"middle"})))))}const{Title:Br,Text:s,Paragraph:Or}=Bn,{TabPane:Ke}=On,{Option:P}=Re;function Xr(){var L,I,D,$,F,X,Y,ie,W,oe,K,he,ue,de,Q,ne,q,H,G,B,me,re,ge,ye,Se,Ie,Pe,Be,Ce,ke,fe,Ne,De,Le,xe,J,Ee,Fe;const{darkMode:t,toggleDarkMode:n}=Mn(),{loading:a,error:l,updating:i,updateError:u,updateSetting:y,resetSettings:g,theme:x,tables:E,charts:d,refresh:C,notifications:R,email:m,reports:v,performance:p}=Tn(),[c,b]=o.useState("theme"),[h,k]=o.useState(!1),[w,A]=o.useState(!1),z=(r,T)=>{Mt.success({message:"Setting Updated",description:`${r} has been ${typeof T=="boolean"?T?"enabled":"disabled":`set to ${T}`}`,duration:2,placement:"topRight"})},f=(r,T,Z)=>{y(r,T),z(Z,T),A(!0)},V=async()=>{try{await g(),Mt.success({message:"Settings Reset",description:"All settings have been reset to their default values",duration:3}),A(!1)}catch(r){Mt.error({message:"Reset Failed",description:r.message,duration:5})}};return a?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px"}},e.createElement(In,{size:"large",tip:"Loading settings..."})):l?e.createElement(Ft,{message:"Settings Load Error",description:l,type:"error",showIcon:!0,style:{margin:"20px"}}):e.createElement("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"}},e.createElement("div",{style:{marginBottom:"24px"}},e.createElement(Br,{level:2},e.createElement(wa,null)," Settings"),e.createElement(Or,null,"Customize your experience with real-time preview. Changes are applied immediately."),e.createElement(ht,{style:{marginBottom:"16px"}},e.createElement(Et,{type:"primary",icon:e.createElement(un,null),onClick:()=>k(!h)},h?"Exit Preview":"Preview Mode"),e.createElement(Et,{icon:e.createElement(mn,null),onClick:V,loading:i,danger:!0},"Reset to Defaults"),w&&e.createElement(Lt,{color:"orange"},e.createElement(Pa,null)," Changes Applied")),u&&e.createElement(Ft,{message:"Update Error",description:u,type:"error",closable:!0,style:{marginBottom:"16px"}})),e.createElement(On,{activeKey:c,onChange:b,type:"card",size:"large"},e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(Aa,null)," Theme & Display"),key:"theme"},e.createElement(se,{title:"Visual Appearance",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Dark Mode"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Switch between light and dark themes"),e.createElement("br",null),e.createElement(_,{checked:t,onChange:r=>{n(),z("Dark Mode",r)},style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Compact Mode"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Reduce spacing for more content"),e.createElement("br",null),e.createElement(_,{checked:x.compactMode,onChange:r=>f("theme.compactMode",r,"Compact Mode"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Animations"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Enable smooth transitions and animations"),e.createElement("br",null),e.createElement(_,{checked:x.animationsEnabled,onChange:r=>f("theme.animationsEnabled",r,"Animations"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Chart Animations"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Animate chart transitions and updates"),e.createElement("br",null),e.createElement(_,{checked:x.chartAnimations,onChange:r=>f("theme.chartAnimations",r,"Chart Animations"),style:{marginTop:"8px"}})))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(Ha,null)," Tables"),key:"tables"},e.createElement(se,{title:"Data Display Settings",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Default Page Size"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Number of rows per page"),e.createElement("br",null),e.createElement(Re,{value:E.defaultPageSize,onChange:r=>f("tables.defaultPageSize",r,"Page Size"),style:{width:"100%",marginTop:"8px"}},E.pageSizeOptions.map(r=>e.createElement(P,{key:r,value:r},r," rows"))))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Virtualization Threshold"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Enable virtualization for large datasets"),e.createElement("br",null),e.createElement(tt,{value:E.virtualizationThreshold,onChange:r=>f("tables.virtualizationThreshold",r,"Virtualization Threshold"),min:50,max:1e3,step:50,style:{width:"100%",marginTop:"8px"},addonAfter:"rows"}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Quick Jumper"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Show page jump input"),e.createElement("br",null),e.createElement(_,{checked:E.showQuickJumper,onChange:r=>f("tables.showQuickJumper",r,"Quick Jumper"),style:{marginTop:"8px"}})))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(La,null)," Charts"),key:"charts"},e.createElement(se,{title:"Chart Configuration",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Color Scheme"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Chart color palette (affects all charts immediately)"),e.createElement("br",null),e.createElement(Re,{value:d.colorScheme,onChange:r=>f("charts.colorScheme",r,"Color Scheme"),style:{width:"100%",marginTop:"8px"}},Object.entries(Na()).map(([r,T])=>e.createElement(P,{key:r,value:r},e.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},e.createElement("div",{style:{display:"flex",gap:"2px"}},T.colors.slice(0,4).map((Z,Ae)=>e.createElement("div",{key:Ae,style:{width:"12px",height:"12px",backgroundColor:Z,borderRadius:"2px",border:"1px solid #d9d9d9"}}))),e.createElement("span",null,T.name))))))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Show Legend"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Display chart legends"),e.createElement("br",null),e.createElement(_,{checked:d.showLegend,onChange:r=>f("charts.showLegend",r,"Chart Legend"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Performance Mode"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Optimize for large datasets"),e.createElement("br",null),e.createElement(_,{checked:d.performanceMode,onChange:r=>f("charts.performanceMode",r,"Performance Mode"),style:{marginTop:"8px"}}))))),e.createElement(se,{title:"Chart Layout & Size",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Default Chart Height"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Default height for charts (pixels)"),e.createElement("br",null),e.createElement(tt,{value:((L=d.layout)==null?void 0:L.defaultHeight)||300,onChange:r=>f("charts.layout.defaultHeight",r,"Chart Height"),min:200,max:800,step:50,style:{width:"100%",marginTop:"8px"},addonAfter:"px"}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Compact Chart Mode"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Smaller charts for more data density"),e.createElement("br",null),e.createElement(_,{checked:((I=d.layout)==null?void 0:I.compactMode)||!1,onChange:r=>f("charts.layout.compactMode",r,"Compact Chart Mode"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Chart Aspect Ratio"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Preferred aspect ratio for charts"),e.createElement("br",null),e.createElement(Re,{value:((D=d.layout)==null?void 0:D.aspectRatio)||"auto",onChange:r=>f("charts.layout.aspectRatio",r,"Chart Aspect Ratio"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"auto"},"Auto"),e.createElement(P,{value:"16:9"},"16:9 (Widescreen)"),e.createElement(P,{value:"4:3"},"4:3 (Standard)"),e.createElement(P,{value:"1:1"},"1:1 (Square)")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Chart Margins"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Spacing around charts"),e.createElement("br",null),e.createElement(Re,{value:(($=d.layout)==null?void 0:$.marginSize)||"standard",onChange:r=>f("charts.layout.marginSize",r,"Chart Margins"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"compact"},"Compact"),e.createElement(P,{value:"standard"},"Standard"),e.createElement(P,{value:"spacious"},"Spacious")))))),e.createElement(se,{title:"Data Display Options",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Show Data Labels"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Display values on chart elements"),e.createElement("br",null),e.createElement(_,{checked:((F=d.dataDisplay)==null?void 0:F.showDataLabels)||!1,onChange:r=>f("charts.dataDisplay.showDataLabels",r,"Show Data Labels"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Show Grid Lines"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Display chart grid lines"),e.createElement("br",null),e.createElement(_,{checked:((X=d.dataDisplay)==null?void 0:X.showGridLines)!==!1,onChange:r=>f("charts.dataDisplay.showGridLines",r,"Show Grid Lines"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Show Data Points"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Show individual points on line charts"),e.createElement("br",null),e.createElement(_,{checked:((Y=d.dataDisplay)==null?void 0:Y.showDataPoints)!==!1,onChange:r=>f("charts.dataDisplay.showDataPoints",r,"Show Data Points"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Zero-Based Y-Axis"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Start Y-axis from zero"),e.createElement("br",null),e.createElement(_,{checked:((ie=d.dataDisplay)==null?void 0:ie.zeroBasedAxis)!==!1,onChange:r=>f("charts.dataDisplay.zeroBasedAxis",r,"Zero-Based Y-Axis"),style:{marginTop:"8px"}}))))),e.createElement(se,{title:"Chart Interaction",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Hover Effects"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Highlight elements on hover"),e.createElement("br",null),e.createElement(_,{checked:((W=d.interactions)==null?void 0:W.hoverEffects)!==!1,onChange:r=>f("charts.interactions.hoverEffects",r,"Hover Effects"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Click to Expand"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Click charts to open in modal"),e.createElement("br",null),e.createElement(_,{checked:((oe=d.interactions)==null?void 0:oe.clickToExpand)!==!1,onChange:r=>f("charts.interactions.clickToExpand",r,"Click to Expand"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Tooltip Style"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Chart tooltip detail level"),e.createElement("br",null),e.createElement(Re,{value:((K=d.interactions)==null?void 0:K.tooltipStyle)||"standard",onChange:r=>f("charts.interactions.tooltipStyle",r,"Tooltip Style"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"minimal"},"Minimal"),e.createElement(P,{value:"standard"},"Standard"),e.createElement(P,{value:"detailed"},"Detailed"))))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(mn,null)," Refresh"),key:"refresh"},e.createElement(se,{title:"Auto-Refresh Configuration",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Auto Refresh"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Automatically refresh data"),e.createElement("br",null),e.createElement(_,{checked:C.autoRefreshEnabled,onChange:r=>f("refresh.autoRefreshEnabled",r,"Auto Refresh"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Background Refresh"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Refresh data when tab is not active"),e.createElement("br",null),e.createElement(_,{checked:C.backgroundRefresh,onChange:r=>f("refresh.backgroundRefresh",r,"Background Refresh"),style:{marginTop:"8px"}}))),e.createElement(S,{span:24},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Dashboard Refresh Interval"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"How often to refresh dashboard data (seconds)"),e.createElement("br",null),e.createElement(bn,{value:C.dashboardInterval,onChange:r=>f("refresh.dashboardInterval",r,"Dashboard Interval"),min:30,max:3600,step:30,marks:{30:"30s",300:"5m",900:"15m",1800:"30m",3600:"1h"},style:{marginTop:"16px"}}))),e.createElement(S,{span:24},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Real-time Data Interval"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"How often to refresh real-time data (seconds)"),e.createElement("br",null),e.createElement(bn,{value:C.realtimeInterval,onChange:r=>f("refresh.realtimeInterval",r,"Real-time Interval"),min:10,max:600,step:10,marks:{10:"10s",60:"1m",300:"5m",600:"10m"},style:{marginTop:"16px"}})))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(Fa,null)," Notifications"),key:"notifications"},e.createElement(se,{title:"Notification Preferences",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:24},e.createElement(s,{strong:!0},"Notification Categories"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Choose which types of notifications to receive"),e.createElement("br",null),e.createElement("div",{style:{marginTop:"12px"}},Object.entries(R.categories).map(([r,T])=>e.createElement("div",{key:r,style:{marginBottom:"8px"}},e.createElement(_,{checked:T,onChange:Z=>f(`notifications.categories.${r}`,Z,`${r} notifications`),style:{marginRight:"8px"}}),e.createElement(s,null,r.replace("_"," ").toUpperCase()))))),e.createElement(S,{span:24},e.createElement(dt,null),e.createElement(s,{strong:!0},"Priority Levels"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Choose which priority levels to show"),e.createElement("br",null),e.createElement("div",{style:{marginTop:"12px"}},Object.entries(R.priorities).map(([r,T])=>e.createElement("div",{key:r,style:{marginBottom:"8px"}},e.createElement(_,{checked:T,onChange:Z=>f(`notifications.priorities.${r}`,Z,`${r} priority notifications`),style:{marginRight:"8px"}}),e.createElement(s,null,r.toUpperCase()))))),e.createElement(S,{span:24},e.createElement(dt,null),e.createElement(s,{strong:!0},"Notification Behavior"),e.createElement("br",null),e.createElement(ve,{gutter:[16,16],style:{marginTop:"12px"}},e.createElement(S,{span:8},e.createElement(s,null,"Sound Notifications"),e.createElement("br",null),e.createElement(_,{checked:R.behavior.sound,onChange:r=>f("notifications.behavior.sound",r,"Sound Notifications")})),e.createElement(S,{span:8},e.createElement(s,null,"Auto Close"),e.createElement("br",null),e.createElement(_,{checked:R.behavior.autoClose,onChange:r=>f("notifications.behavior.autoClose",r,"Auto Close")})),e.createElement(S,{span:8},e.createElement(s,null,"Max Visible"),e.createElement("br",null),e.createElement(tt,{value:R.behavior.maxVisible,onChange:r=>f("notifications.behavior.maxVisible",r,"Max Visible Notifications"),min:1,max:20,style:{width:"100%"}}))))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(Ra,null)," Email"),key:"email"},e.createElement(se,{title:"Email Notification Settings",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:24},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Enable Email Notifications"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Receive notifications via email"),e.createElement("br",null),e.createElement(_,{checked:m.enabled,onChange:r=>f("email.enabled",r,"Email Notifications"),style:{marginTop:"8px"}}))),m.enabled&&e.createElement(e.Fragment,null,e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Email Frequency"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"How often to send email notifications"),e.createElement("br",null),e.createElement(Re,{value:m.frequency,onChange:r=>f("email.frequency",r,"Email Frequency"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"immediate"},"Immediate"),e.createElement(P,{value:"hourly_batch"},"Hourly Batch"),e.createElement(P,{value:"daily_digest"},"Daily Digest")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Email Template"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Choose email template style"),e.createElement("br",null),e.createElement(Re,{value:m.template,onChange:r=>f("email.template",r,"Email Template"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"minimal"},"Minimal"),e.createElement(P,{value:"standard"},"Standard"),e.createElement(P,{value:"detailed"},"Detailed")))),e.createElement(S,{span:24},e.createElement(dt,null),e.createElement(s,{strong:!0},"Email Notification Categories"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Choose which categories to receive via email"),e.createElement("br",null),e.createElement("div",{style:{marginTop:"12px"}},m.notifications&&m.notifications.categories&&Object.entries(m.notifications.categories).map(([r,T])=>e.createElement("div",{key:r,style:{marginBottom:"8px"}},e.createElement(_,{checked:T,onChange:Z=>f(`email.notifications.categories.${r}`,Z,`Email ${r} notifications`),style:{marginRight:"8px"}}),e.createElement(s,null,r.replace("_"," ").toUpperCase()))))),e.createElement(S,{span:24},e.createElement(dt,null),e.createElement(s,{strong:!0},"Email Priority Levels"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Choose which priority levels to receive via email"),e.createElement("br",null),e.createElement("div",{style:{marginTop:"12px"}},m.notifications&&m.notifications.priorities&&Object.entries(m.notifications.priorities).map(([r,T])=>e.createElement("div",{key:r,style:{marginBottom:"8px"}},e.createElement(_,{checked:T,onChange:Z=>f(`email.notifications.priorities.${r}`,Z,`Email ${r} priority notifications`),style:{marginRight:"8px"}}),e.createElement(s,null,r.toUpperCase()))))),m.quietHours&&e.createElement(S,{span:24},e.createElement(dt,null),e.createElement(s,{strong:!0},"Quiet Hours"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Disable email notifications during specific hours"),e.createElement("br",null),e.createElement(ve,{gutter:[16,16],style:{marginTop:"12px"}},e.createElement(S,{span:8},e.createElement(s,null,"Enable Quiet Hours"),e.createElement("br",null),e.createElement(_,{checked:m.quietHours.enabled,onChange:r=>f("email.quietHours.enabled",r,"Quiet Hours")})),m.quietHours.enabled&&e.createElement(e.Fragment,null,e.createElement(S,{span:8},e.createElement(s,null,"Start Time"),e.createElement("br",null),e.createElement(Re,{value:m.quietHours.start,onChange:r=>f("email.quietHours.start",r,"Quiet Hours Start"),style:{width:"100%"}},Array.from({length:24},(r,T)=>e.createElement(P,{key:T,value:`${T.toString().padStart(2,"0")}:00`},`${T.toString().padStart(2,"0")}:00`)))),e.createElement(S,{span:8},e.createElement(s,null,"End Time"),e.createElement("br",null),e.createElement(Re,{value:m.quietHours.end,onChange:r=>f("email.quietHours.end",r,"Quiet Hours End"),style:{width:"100%"}},Array.from({length:24},(r,T)=>e.createElement(P,{key:T,value:`${T.toString().padStart(2,"0")}:00`},`${T.toString().padStart(2,"0")}:00`))))))),m.batchSettings&&e.createElement(S,{span:24},e.createElement(dt,null),e.createElement(s,{strong:!0},"Batch Settings"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Configure email batching and digest options"),e.createElement("br",null),e.createElement(ve,{gutter:[16,16],style:{marginTop:"12px"}},e.createElement(S,{span:12},e.createElement(s,null,"Hourly Batch"),e.createElement("br",null),e.createElement(_,{checked:(he=m.batchSettings.hourlyBatch)==null?void 0:he.enabled,onChange:r=>f("email.batchSettings.hourlyBatch.enabled",r,"Hourly Batch")}),((ue=m.batchSettings.hourlyBatch)==null?void 0:ue.enabled)&&e.createElement("div",{style:{marginTop:"8px"}},e.createElement(s,null,"Max Notifications per Hour"),e.createElement("br",null),e.createElement(tt,{value:m.batchSettings.hourlyBatch.maxNotifications,onChange:r=>f("email.batchSettings.hourlyBatch.maxNotifications",r,"Max Hourly Notifications"),min:1,max:100,style:{width:"100%"}}))),e.createElement(S,{span:12},e.createElement(s,null,"Daily Digest"),e.createElement("br",null),e.createElement(_,{checked:(de=m.batchSettings.dailyDigest)==null?void 0:de.enabled,onChange:r=>f("email.batchSettings.dailyDigest.enabled",r,"Daily Digest")}),((Q=m.batchSettings.dailyDigest)==null?void 0:Q.enabled)&&e.createElement("div",{style:{marginTop:"8px"}},e.createElement(s,null,"Digest Time"),e.createElement("br",null),e.createElement(Re,{value:m.batchSettings.dailyDigest.time,onChange:r=>f("email.batchSettings.dailyDigest.time",r,"Daily Digest Time"),style:{width:"100%"}},Array.from({length:24},(r,T)=>e.createElement(P,{key:T,value:`${T.toString().padStart(2,"0")}:00`},`${T.toString().padStart(2,"0")}:00`)))))))))),e.createElement(se,{title:"Advanced Email Settings",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Email Format"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Email content format preference"),e.createElement("br",null),e.createElement(Re,{value:m.format||"html",onChange:r=>f("email.format",r,"Email Format"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"html"},"HTML (Rich formatting)"),e.createElement(P,{value:"text"},"Plain Text"),e.createElement(P,{value:"both"},"Both HTML and Text")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Email Language"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Language for email notifications"),e.createElement("br",null),e.createElement(Re,{value:m.language||"fr",onChange:r=>f("email.language",r,"Email Language"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"fr"},"Français"),e.createElement(P,{value:"en"},"English"),e.createElement(P,{value:"es"},"Español")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Include Attachments"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Attach relevant files to emails"),e.createElement("br",null),e.createElement(_,{checked:((ne=m.attachments)==null?void 0:ne.enabled)||!1,onChange:r=>f("email.attachments.enabled",r,"Include Attachments"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Email Signature"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Include custom signature"),e.createElement("br",null),e.createElement(_,{checked:((q=m.signature)==null?void 0:q.enabled)||!1,onChange:r=>f("email.signature.enabled",r,"Email Signature"),style:{marginTop:"8px"}}))),((H=m.signature)==null?void 0:H.enabled)&&e.createElement(S,{span:24},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Custom Signature"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Your custom email signature"),e.createElement("br",null),e.createElement($a.TextArea,{value:((G=m.signature)==null?void 0:G.text)||"",onChange:r=>f("email.signature.text",r.target.value,"Email Signature Text"),placeholder:"Enter your custom email signature...",rows:3,style:{marginTop:"8px"}}))))),e.createElement(se,{title:"Email Filtering & Rules",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Minimum Priority Level"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Only send emails for this priority and above"),e.createElement("br",null),e.createElement(Re,{value:((B=m.filtering)==null?void 0:B.minPriority)||"low",onChange:r=>f("email.filtering.minPriority",r,"Minimum Priority Level"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"low"},"Low and above"),e.createElement(P,{value:"medium"},"Medium and above"),e.createElement(P,{value:"high"},"High and above"),e.createElement(P,{value:"critical"},"Critical only")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Maximum Emails per Day"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Limit daily email notifications"),e.createElement("br",null),e.createElement(tt,{value:((me=m.filtering)==null?void 0:me.maxPerDay)||50,onChange:r=>f("email.filtering.maxPerDay",r,"Max Emails per Day"),min:1,max:200,style:{width:"100%",marginTop:"8px"},addonAfter:"emails"}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Duplicate Prevention"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Prevent duplicate notifications"),e.createElement("br",null),e.createElement(_,{checked:((re=m.filtering)==null?void 0:re.preventDuplicates)!==!1,onChange:r=>f("email.filtering.preventDuplicates",r,"Duplicate Prevention"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Smart Grouping"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Group similar notifications together"),e.createElement("br",null),e.createElement(_,{checked:((ge=m.filtering)==null?void 0:ge.smartGrouping)||!1,onChange:r=>f("email.filtering.smartGrouping",r,"Smart Grouping"),style:{marginTop:"8px"}}))))),e.createElement(se,{title:"Email Delivery Options",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Delivery Method"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"How to deliver email notifications"),e.createElement("br",null),e.createElement(Re,{value:((ye=m.delivery)==null?void 0:ye.method)||"immediate",onChange:r=>f("email.delivery.method",r,"Delivery Method"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"immediate"},"Immediate"),e.createElement(P,{value:"scheduled"},"Scheduled"),e.createElement(P,{value:"batch"},"Batch Processing")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Retry Failed Emails"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Retry sending failed emails"),e.createElement("br",null),e.createElement(_,{checked:((Se=m.delivery)==null?void 0:Se.retryFailed)!==!1,onChange:r=>f("email.delivery.retryFailed",r,"Retry Failed Emails"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Read Receipts"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Request read receipts for emails"),e.createElement("br",null),e.createElement(_,{checked:((Ie=m.delivery)==null?void 0:Ie.readReceipts)||!1,onChange:r=>f("email.delivery.readReceipts",r,"Read Receipts"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Email Tracking"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Track email opens and clicks"),e.createElement("br",null),e.createElement(_,{checked:((Pe=m.delivery)==null?void 0:Pe.tracking)||!1,onChange:r=>f("email.delivery.tracking",r,"Email Tracking"),style:{marginTop:"8px"}})))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(_a,null)," Reports"),key:"reports"},e.createElement(se,{title:"Report Generation Settings",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Auto Generate Reports"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Automatically generate reports"),e.createElement("br",null),e.createElement(_,{checked:(Be=v.generation)==null?void 0:Be.autoGenerate,onChange:r=>f("reports.generation.autoGenerate",r,"Auto Generate Reports"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Report Format"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Default format for generated reports"),e.createElement("br",null),e.createElement(Re,{value:(Ce=v.generation)==null?void 0:Ce.format,onChange:r=>f("reports.generation.format",r,"Report Format"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"pdf"},"PDF"),e.createElement(P,{value:"html"},"HTML"),e.createElement(P,{value:"excel"},"Excel")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Report Quality"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Quality level for generated reports"),e.createElement("br",null),e.createElement(Re,{value:(ke=v.generation)==null?void 0:ke.quality,onChange:r=>f("reports.generation.quality",r,"Report Quality"),style:{width:"100%",marginTop:"8px"}},e.createElement(P,{value:"low"},"Low (Fast)"),e.createElement(P,{value:"standard"},"Standard"),e.createElement(P,{value:"high"},"High (Slow)")))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Include Charts"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Include charts in reports"),e.createElement("br",null),e.createElement(_,{checked:(fe=v.generation)==null?void 0:fe.includeCharts,onChange:r=>f("reports.generation.includeCharts",r,"Include Charts"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Include Tables"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Include data tables in reports"),e.createElement("br",null),e.createElement(_,{checked:(Ne=v.generation)==null?void 0:Ne.includeTables,onChange:r=>f("reports.generation.includeTables",r,"Include Tables"),style:{marginTop:"8px"}}))))),v.schedules&&e.createElement(se,{title:"Report Scheduling",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:8},e.createElement(s,{strong:!0},"Daily Reports"),e.createElement("br",null),e.createElement(_,{checked:(De=v.schedules.daily)==null?void 0:De.enabled,onChange:r=>f("reports.schedules.daily.enabled",r,"Daily Reports")}),((Le=v.schedules.daily)==null?void 0:Le.enabled)&&e.createElement("div",{style:{marginTop:"8px"}},e.createElement(s,null,"Time"),e.createElement("br",null),e.createElement(Re,{value:v.schedules.daily.time,onChange:r=>f("reports.schedules.daily.time",r,"Daily Report Time"),style:{width:"100%"}},Array.from({length:24},(r,T)=>e.createElement(P,{key:T,value:`${T.toString().padStart(2,"0")}:00`},`${T.toString().padStart(2,"0")}:00`))))),e.createElement(S,{span:8},e.createElement(s,{strong:!0},"Weekly Reports"),e.createElement("br",null),e.createElement(_,{checked:(xe=v.schedules.weekly)==null?void 0:xe.enabled,onChange:r=>f("reports.schedules.weekly.enabled",r,"Weekly Reports")}),((J=v.schedules.weekly)==null?void 0:J.enabled)&&e.createElement("div",{style:{marginTop:"8px"}},e.createElement(s,null,"Day"),e.createElement("br",null),e.createElement(Re,{value:v.schedules.weekly.day,onChange:r=>f("reports.schedules.weekly.day",r,"Weekly Report Day"),style:{width:"100%"}},e.createElement(P,{value:"monday"},"Monday"),e.createElement(P,{value:"tuesday"},"Tuesday"),e.createElement(P,{value:"wednesday"},"Wednesday"),e.createElement(P,{value:"thursday"},"Thursday"),e.createElement(P,{value:"friday"},"Friday"),e.createElement(P,{value:"saturday"},"Saturday"),e.createElement(P,{value:"sunday"},"Sunday")))),e.createElement(S,{span:8},e.createElement(s,{strong:!0},"Monthly Reports"),e.createElement("br",null),e.createElement(_,{checked:(Ee=v.schedules.monthly)==null?void 0:Ee.enabled,onChange:r=>f("reports.schedules.monthly.enabled",r,"Monthly Reports")}),((Fe=v.schedules.monthly)==null?void 0:Fe.enabled)&&e.createElement("div",{style:{marginTop:"8px"}},e.createElement(s,null,"Day of Month"),e.createElement("br",null),e.createElement(tt,{value:v.schedules.monthly.day,onChange:r=>f("reports.schedules.monthly.day",r,"Monthly Report Day"),min:1,max:31,style:{width:"100%"}})))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(za,null)," Performance"),key:"performance"},e.createElement(se,{title:"Performance Optimization",style:{marginBottom:"16px"}},e.createElement(ve,{gutter:[24,16]},e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Enable Caching"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Cache data to improve performance"),e.createElement("br",null),e.createElement(_,{checked:p.caching.enabled,onChange:r=>f("performance.caching.enabled",r,"Caching"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Cache Duration"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"How long to cache data (seconds)"),e.createElement("br",null),e.createElement(tt,{value:p.caching.duration,onChange:r=>f("performance.caching.duration",r,"Cache Duration"),min:60,max:3600,step:60,style:{width:"100%",marginTop:"8px"},addonAfter:"seconds"}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Lazy Loading"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Load content as needed"),e.createElement("br",null),e.createElement(_,{checked:p.optimization.lazyLoading,onChange:r=>f("performance.optimization.lazyLoading",r,"Lazy Loading"),style:{marginTop:"8px"}}))),e.createElement(S,{span:12},e.createElement("div",{style:{marginBottom:"16px"}},e.createElement(s,{strong:!0},"Virtualization"),e.createElement("br",null),e.createElement(s,{type:"secondary"},"Optimize large lists and tables"),e.createElement("br",null),e.createElement(_,{checked:p.optimization.virtualization,onChange:r=>f("performance.optimization.virtualization",r,"Virtualization"),style:{marginTop:"8px"}})))))),e.createElement(Ke,{tab:e.createElement("span",null,e.createElement(un,null)," Live Preview"),key:"preview"},e.createElement(Ir,null))))}export{Xr as default};
