import React from 'react';
import { Modal } from 'antd';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import GlobalSearchModal from '../search/GlobalSearchModal';

const ArretSearchModal = () => {
  const {
    isSearchModalVisible = false,
    setIsSearchModalVisible,
    searchResults = [],
    performGlobalSearch,
    searchLoading = false
  } = useArretQueuedContext() || {};
  const handleSearch = async (searchTerm) => {
    if (performGlobalSearch) {
      await performGlobalSearch(searchTerm);
    }
  };

  return (
    <GlobalSearchModal
      visible={isSearchModalVisible}
      onClose={() => setIsSearchModalVisible && setIsSearchModalVisible(false)}
      onSearch={handleSearch}
      results={searchResults}
      loading={searchLoading}
      searchContext="arrets"
    />
  );
};

export default ArretSearchModal;
