import React from "react" ;
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, LineElement, PointElement, TimeScale, Title, Tooltip, Legend } from 'chart.js';
import 'chartjs-adapter-date-fns';
import { enUS } from 'date-fns/locale';
import { throttle } from 'lodash';
import SOMIPEM_COLORS from '../styles/brand-colors';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  TimeScale,
  Title,
  Tooltip,
  Legend
);

// Throttle chart updates to prevent excessive re-renders
export const throttledUpdateChart = throttle((chart, data) => {
  if (chart && chart.data) {
    chart.data = data;
    chart.update();
  }
}, 500);

// Optimize data processing with memoization pattern
const memoizedProcessData = (dataFn) => {
  let lastData = null;
  let lastResult = null;
  
  return (data) => {
    if (data !== lastData) {
      lastData = data;
      lastResult = dataFn(data);
    }
    return lastResult;
  };
};

const chartConfig = {
  productionData: memoizedProcessData((machineData = []) => ({
    labels: machineData.map(m => m.Machine_Name),
    datasets: [
      {
        label: 'Planifié',
        data: machineData.map(m => m.Total_Quantite_Planifier),
        backgroundColor: SOMIPEM_COLORS.CHART_TERTIARY, // Light blue for planned
        barThickness: 20,
        borderRadius: 4
      },
      {
        label: 'Réalisé',
        data: machineData.map(m => m.Total_Quantite_Bon),
        backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE, // SOMIPEM primary blue for realized
        barThickness: 20,
        borderRadius: 4
      },
      {
        label: 'TRS',
        data: machineData.map(m => m.Avg_TRS),
        borderColor: SOMIPEM_COLORS.SECONDARY_BLUE, // SOMIPEM secondary blue for TRS line
        borderWidth: 2,
        type: 'line',
        fill: false,
        tension: 0.4,
        pointRadius: window.innerWidth < 768 ? 0 : 4
      }
    ]
  })),

  // Fixed the missing closing parenthesis here
  dailyData: memoizedProcessData((dailyStats = []) => ({
    labels: dailyStats.map(d => d.time_bucket),
    datasets: [{
      label: 'Production',
      data: dailyStats.map(d => d.production),
      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE, // SOMIPEM primary blue
      borderWidth: 2,
      fill: true,
      tension: 0.3,
      backgroundColor: (context) => {
        const ctx = context.chart.ctx;
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, SOMIPEM_COLORS.LIGHT_BLUE_BG); // SOMIPEM light blue background
        gradient.addColorStop(1, 'rgba(30, 58, 138, 0)'); // Transparent SOMIPEM primary blue
        return gradient;
      },
      pointRadius: 4
    }]
  })), // Added missing closing parenthesis

  barOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: { color: '#666' }
      },
      tooltip: {
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff'
      }
    },
    scales: {
      x: {
        stacked: false,
        grid: { display: false }
      },
      y: {
        beginAtZero: true,
        grid: { color: '#f0f0f0' },
        ticks: { color: '#666' }
      }
    },
    animation: false,
    devicePixelRatio: 1,
  },

  lineOptions: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: { color: '#666' }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(0,0,0,0.8)',
        titleColor: '#fff',
        bodyColor: '#fff'
      }
    },
    scales: {
      x: {
        type: 'time',
        time: {
          parser: 'HH:mm',
          unit: 'hour',
          displayFormats: { hour: 'HH:mm' },
          tooltipFormat: 'HH:mm'
        },
        grid: { display: false },
        adapters: { date: { locale: enUS } },
        ticks: { 
          color: '#666',
          maxTicksLimit: 8,
          source: 'auto',
        }
      },
      y: {
        beginAtZero: true,
        grid: { color: '#f0f0f0' },
        ticks: { 
          color: '#666',
          maxTicksLimit: 6
        }
      }
    },
    elements: {
      line: {
        tension: 0.3
      },
      point: {
        radius: window.innerWidth < 768 ? 0 : 3,
        hoverRadius: 6
      }
    },
    animation: false,
    devicePixelRatio: 1,
  }
};

export { ChartJS, chartConfig };