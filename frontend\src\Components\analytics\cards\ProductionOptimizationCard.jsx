import React, { useState } from 'react';
import { Card, Row, Col, Statistic, Button, Space, Badge, Tag, Progress, Empty } from 'antd';
import { 
  ThunderboltOutlined,
  RocketOutlined,
  BulbOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  AlertOutlined
} from '@ant-design/icons';

const ProductionOptimizationCard = ({ loading, filters }) => {
  const [activeOptimization, setActiveOptimization] = useState(null);

  // Mock AI optimization data
  const optimizationData = {
    currentPerformance: 87.5,
    optimizedPerformance: 94.2,
    potentialGain: 6.7,
    estimatedSavings: 12450,
    implementationTime: 24,
    activeOptimizations: [
      {
        id: 1,
        title: "Cycle Time Optimization",
        impact: "High",
        status: "active",
        progress: 78,
        description: "AI-recommended cycle time adjustments",
        estimatedGain: "+3.2%",
        timeToComplete: "6 hours",
        confidence: 92.5
      },
      {
        id: 2,
        title: "Temperature Profile Tuning",
        impact: "Medium",
        status: "pending",
        progress: 0,
        description: "Optimal temperature curve for quality improvement",
        estimatedGain: "+2.1%",
        timeToComplete: "12 hours",
        confidence: 87.3
      },
      {
        id: 3,
        title: "Material Feed Rate",
        impact: "Medium",
        status: "completed",
        progress: 100,
        description: "Optimized material injection parameters",
        estimatedGain: "+1.4%",
        timeToComplete: "Completed",
        confidence: 94.8
      }
    ],
    realTimeRecommendations: [
      {
        priority: "high",
        message: "Increase injection speed by 5% on Machine M003",
        impact: "+2.3% efficiency",
        confidence: 89.2
      },
      {
        priority: "medium",
        message: "Adjust cooling time by -10% for Part ABC-123",
        impact: "+1.8% cycle time",
        confidence: 84.7
      },
      {
        priority: "low",
        message: "Consider material preheating for next shift",
        impact: "+0.9% quality",
        confidence: 76.4
      }
    ]
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#1890ff';
      case 'completed': return '#52c41a';
      case 'pending': return '#fa8c16';
      default: return '#d9d9d9';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#f5222d';
      case 'medium': return '#fa8c16';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  if (loading) {
    return (
      <div style={{ 
        height: '400px', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }}>
        <Empty description="Loading Optimization Engine..." />
      </div>
    );
  }

  return (
    <div style={{ minHeight: '400px' }}>
      {/* Performance Overview */}
      <Row gutter={16} style={{ marginBottom: '20px' }}>
        <Col span={8}>
          <Card
            size="small"
            style={{
              textAlign: 'center',
              background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
              border: '2px solid #1890ff20',
              borderRadius: '12px'
            }}
          >
            <Statistic
              title="Current Performance"
              value={optimizationData.currentPerformance}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#1890ff', fontSize: '20px', fontWeight: '700' }}
            />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card
            size="small"
            style={{
              textAlign: 'center',
              background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
              border: '2px solid #52c41a20',
              borderRadius: '12px'
            }}
          >
            <Statistic
              title="AI Optimized Target"
              value={optimizationData.optimizedPerformance}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#52c41a', fontSize: '20px', fontWeight: '700' }}
            />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card
            size="small"
            style={{
              textAlign: 'center',
              background: 'linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)',
              border: '2px solid #fa8c1620',
              borderRadius: '12px'
            }}
          >
            <Statistic
              title="Potential Savings"
              value={optimizationData.estimatedSavings}
              prefix="$"
              precision={0}
              valueStyle={{ color: '#fa8c16', fontSize: '20px', fontWeight: '700' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Active Optimizations */}
      <div style={{ marginBottom: '20px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '12px'
        }}>
          <Space>
            <RocketOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontWeight: '600', color: '#595959' }}>
              Active Optimizations
            </span>
            <Badge 
              count={optimizationData.activeOptimizations.filter(opt => opt.status === 'active').length} 
              style={{ backgroundColor: '#1890ff' }} 
            />
          </Space>
        </div>

        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {optimizationData.activeOptimizations.map((optimization) => (
            <Card
              key={optimization.id}
              size="small"
              style={{
                borderRadius: '10px',
                border: `2px solid ${getStatusColor(optimization.status)}20`,
                background: `linear-gradient(135deg, ${getStatusColor(optimization.status)}05 0%, ${getStatusColor(optimization.status)}10 100%)`
              }}
            >
              <Row gutter={16} align="middle">
                <Col flex="auto">
                  <div>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginBottom: '4px'
                    }}>
                      <span style={{ fontWeight: '600', fontSize: '14px' }}>
                        {optimization.title}
                      </span>
                      <Space size="small">
                        <Tag color={getStatusColor(optimization.status)}>
                          {optimization.status}
                        </Tag>
                        <Tag color={optimization.impact === 'High' ? 'red' : optimization.impact === 'Medium' ? 'orange' : 'green'}>
                          {optimization.impact} Impact
                        </Tag>
                      </Space>
                    </div>
                    <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '8px' }}>
                      {optimization.description}
                    </div>
                    <Row gutter={16} align="middle">
                      <Col flex="auto">
                        <Progress
                          percent={optimization.progress}
                          size="small"
                          strokeColor={getStatusColor(optimization.status)}
                          showInfo={false}
                        />
                      </Col>
                      <Col>
                        <Space size="large">
                          <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '11px', color: '#8c8c8c' }}>Gain</div>
                            <div style={{ 
                              fontSize: '12px', 
                              fontWeight: '600',
                              color: '#52c41a'
                            }}>
                              {optimization.estimatedGain}
                            </div>
                          </div>
                          <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '11px', color: '#8c8c8c' }}>Time</div>
                            <div style={{ fontSize: '12px', fontWeight: '500' }}>
                              {optimization.timeToComplete}
                            </div>
                          </div>
                          <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '11px', color: '#8c8c8c' }}>Confidence</div>
                            <div style={{ 
                              fontSize: '12px', 
                              fontWeight: '600',
                              color: optimization.confidence > 90 ? '#52c41a' : '#fa8c16'
                            }}>
                              {optimization.confidence}%
                            </div>
                          </div>
                        </Space>
                      </Col>
                    </Row>
                  </div>
                </Col>
              </Row>
            </Card>
          ))}
        </Space>
      </div>

      {/* Real-time Recommendations */}
      <div>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '12px'
        }}>
          <Space>
            <BulbOutlined style={{ color: '#fa8c16' }} />
            <span style={{ fontWeight: '600', color: '#595959' }}>
              Real-time AI Recommendations
            </span>
            <Badge count="Live" style={{ backgroundColor: '#52c41a' }} />
          </Space>
          <Button 
            type="primary" 
            size="small" 
            icon={<CheckCircleOutlined />}
            style={{ borderRadius: '6px' }}
          >
            Apply All
          </Button>
        </div>

        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {optimizationData.realTimeRecommendations.map((rec, index) => (
            <Card
              key={index}
              size="small"
              style={{
                borderRadius: '8px',
                border: `2px solid ${getPriorityColor(rec.priority)}20`,
                background: `linear-gradient(135deg, ${getPriorityColor(rec.priority)}05 0%, ${getPriorityColor(rec.priority)}10 100%)`
              }}
            >
              <Row justify="space-between" align="middle">
                <Col flex="auto">
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginBottom: '4px'
                  }}>
                    {rec.priority === 'high' && <AlertOutlined style={{ color: '#f5222d' }} />}
                    {rec.priority === 'medium' && <ClockCircleOutlined style={{ color: '#fa8c16' }} />}
                    {rec.priority === 'low' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    <span style={{ fontSize: '13px', fontWeight: '500' }}>
                      {rec.message}
                    </span>
                  </div>
                  <div style={{ fontSize: '11px', color: '#8c8c8c' }}>
                    Expected impact: {rec.impact} • Confidence: {rec.confidence}%
                  </div>
                </Col>
                <Col>
                  <Space size="small">
                    <Tag 
                      color={getPriorityColor(rec.priority)}
                      style={{ textTransform: 'uppercase', fontSize: '10px' }}
                    >
                      {rec.priority}
                    </Tag>
                    <Button type="primary" size="small" style={{ borderRadius: '4px' }}>
                      Apply
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Card>
          ))}
        </Space>
      </div>
    </div>
  );
};

export default ProductionOptimizationCard;
