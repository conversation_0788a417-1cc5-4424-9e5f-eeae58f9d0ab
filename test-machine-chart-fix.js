/**
 * Test Machine Chart Fix - Check TRS Field Mapping
 */

import fetch from 'node-fetch';

const GRAPHQL_URL = 'http://localhost:5000/api/graphql';

// Test machine performance with TRS field
const testMachinePerformance = async () => {
  console.log('🔍 Testing Machine Performance with TRS field mapping...\n');

  const query = `
    query {
      getMachinePerformance(filters: { model: "IPS" }) {
        Machine_Name
        production
        rejects
        availability
        performance
        oee
        trs
        quality
        disponibilite
        downtime
      }
    }
  `;

  try {
    const response = await fetch(GRAPHQL_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.log('❌ Errors:', JSON.stringify(result.errors, null, 2));
    } else {
      console.log('✅ Success:', JSON.stringify(result.data, null, 2));
      
      // Check if TRS field is present
      if (result.data.getMachinePerformance.length > 0) {
        const machineData = result.data.getMachinePerformance[0];
        console.log('\n🔍 Field Analysis:');
        console.log('  - Machine Name:', machineData.Machine_Name);
        console.log('  - OEE value:', machineData.oee);
        console.log('  - TRS value:', machineData.trs);
        console.log('  - OEE === TRS:', machineData.oee === machineData.trs);
      }
    }
  } catch (error) {
    console.error('❌ Network error:', error);
  }
};

// Run test
testMachinePerformance();
