import express from 'express';
import auth from '../middleware/auth.js';
import { checkPermission } from '../middleware/permission.js';
import { corsOptions } from '../middleware/cors.js';
import cors from 'cors';
import db from '../db.js';

const router = express.Router();

// @route   GET api/permissions
// @desc    Get all permissions
// @access  Private (with permission)
router.get('/', cors(corsOptions), auth, checkPermission(['manage_roles', 'view_roles']), async (req, res) => {
  try {
    // First, try to get permissions from the database
    const [dbPermissions] = await db.execute('SELECT * FROM permissions ORDER BY namespace, name');

    // Import the permission namespaces from roleHierarchy.js
    const { PERMISSION_NAMESPACES } = await import('../utils/roleHierarchy.js');

    // Create a flattened list of permissions with proper namespacing
    const formattedPermissions = [];

    // Process each namespace and its permissions from roleHierarchy.js
    Object.entries(PERMISSION_NAMESPACES).forEach(([namespace, data]) => {
      Object.entries(data.permissions).forEach(([permName, description]) => {
        const fullPermName = `${namespace}:${permName}`;

        // Check if this permission already exists in the database results
        const existingPerm = dbPermissions.find(p =>
          p.name === fullPermName ||
          (p.namespace === namespace && p.name === permName)
        );

        if (existingPerm) {
          // Use the database version but ensure it has the correct format
          formattedPermissions.push({
            id: existingPerm.id || fullPermName,
            name: fullPermName, // Always use the namespaced format
            description: existingPerm.description || description,
            namespace: namespace
          });
        } else {
          // Add the permission from roleHierarchy.js
          formattedPermissions.push({
            id: fullPermName,
            name: fullPermName,
            description: description,
            namespace: namespace
          });
        }
      });
    });

    // Add any additional permissions from the database that aren't in roleHierarchy.js
    dbPermissions.forEach(dbPerm => {
      const hasNamespace = dbPerm.name && dbPerm.name.includes(':');
      let namespace, permName;

      if (hasNamespace) {
        [namespace, permName] = dbPerm.name.split(':');
      } else {
        namespace = dbPerm.namespace || 'other';
        permName = dbPerm.name;
      }

      const fullPermName = `${namespace}:${permName}`;

      // Check if this permission is already in our formatted list
      const alreadyExists = formattedPermissions.some(p => p.name === fullPermName);

      if (!alreadyExists) {
        formattedPermissions.push({
          id: dbPerm.id || fullPermName,
          name: fullPermName,
          description: dbPerm.description || `${permName} permission`,
          namespace: namespace
        });
      }
    });

    res.json({ success: true, data: formattedPermissions });
  } catch (err) {
    console.error('Error fetching permissions:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

export default router;