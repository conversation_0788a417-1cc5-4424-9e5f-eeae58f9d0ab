import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'antd';
import { usePermission } from '../hooks/usePermission';

/**
 * Permission-based button component that only renders if the user has the required permissions
 * 
 * @param {Object} props - Component props
 * @param {string|string[]} [props.permissions] - Required permission(s)
 * @param {string|string[]} [props.roles] - Required role(s)
 * @param {number|number[]} [props.departments] - Required department(s)
 * @param {string} [props.disabledTooltip] - Tooltip to show when button is disabled due to permissions
 * @param {boolean} [props.hideIfUnauthorized=false] - Whether to hide the button if unauthorized (instead of disabling)
 * @param {React.ReactNode} props.children - Button content
 * @returns {React.ReactNode} Button if authorized, disabled button or null if not
 */
const PermissionButton = ({ 
  permissions, 
  roles, 
  departments, 
  disabledTooltip = "Vous n'avez pas les permissions nécessaires pour cette action",
  hideIfUnauthorized = false,
  children,
  ...buttonProps 
}) => {
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  
  // Check if user meets all specified conditions
  const isAuthorized = (
    // If permissions specified, user must have them
    (!permissions || hasPermission(permissions)) &&
    // If roles specified, user must have one
    (!roles || hasRole(roles)) &&
    // If departments specified, user must have access
    (!departments || hasDepartmentAccess(departments))
  );
  
  // If not authorized and configured to hide, return null
  if (!isAuthorized && hideIfUnauthorized) {
    return null;
  }
  
  // If not authorized and not hidden, render disabled button with tooltip
  if (!isAuthorized) {
    return (
      <Tooltip title={disabledTooltip}>
        <span>
          <Button {...buttonProps} disabled>
            {children}
          </Button>
        </span>
      </Tooltip>
    );
  }
  
  // If authorized, render normal button
  return <Button {...buttonProps}>{children}</Button>;
};

export default PermissionButton;
