import superAgentService from '../../services/SuperAgentService.js';
import externalNotificationService from '../../services/ExternalNotificationService.js';
import healthMonitoringService from '../../services/HealthMonitoringService.js';

/**
 * External API resolvers using SuperAgent
 * Provides GraphQL access to external service integrations
 */
export const externalApiResolvers = {
  Query: {
    /**
     * Get health status of external services
     */
    externalHealthStatus: async () => {
      try {
        const healthStatus = await healthMonitoringService.getCurrentHealth();
        return {
          overall: healthStatus.overall || 'unknown',
          timestamp: healthStatus.timestamp || new Date().toISOString(),
          services: healthStatus.services ? Object.keys(healthStatus.services).map(serviceName => ({
            name: serviceName,
            status: healthStatus.services[serviceName].status || 'unknown',
            responseTime: healthStatus.services[serviceName].responseTime || 0,
            details: JSON.stringify(healthStatus.services[serviceName].details || {})
          })) : []
        };
      } catch (error) {
        console.error('GraphQL external health status error:', error);
        return {
          overall: 'error',
          timestamp: new Date().toISOString(),
          services: [],
          error: error.message
        };
      }
    },

    /**
     * Get registered webhooks
     */
    registeredWebhooks: async () => {
      try {
        const webhooks = externalNotificationService.getRegisteredWebhooks();
        return webhooks.map(webhook => ({
          name: webhook.name,
          url: webhook.url,
          active: webhook.active,
          format: webhook.format,
          timeout: webhook.timeout
        }));
      } catch (error) {
        console.error('GraphQL registered webhooks error:', error);
        throw new Error(`Failed to retrieve webhooks: ${error.message}`);
      }
    },

    /**
     * Test external service connectivity
     */
    testExternalService: async (_, { url, timeout }) => {
      try {
        const result = await superAgentService.testConnectivity(url, { timeout: timeout || 5000 });
        return {
          connected: result.connected,
          url: result.url,
          responseTime: result.responseTime,
          testDuration: result.testDuration,
          timestamp: result.timestamp,
          error: result.error || null
        };
      } catch (error) {
        console.error('GraphQL test external service error:', error);
        return {
          connected: false,
          url,
          responseTime: 0,
          testDuration: 0,
          timestamp: new Date().toISOString(),
          error: error.message
        };
      }
    },

    /**
     * Monitor multiple endpoints
     */
    monitorEndpoints: async (_, { endpoints, timeout }) => {
      try {
        const endpointList = endpoints.map(endpoint => ({
          name: endpoint.name,
          url: endpoint.url,
          headers: endpoint.headers ? JSON.parse(endpoint.headers) : {}
        }));

        const result = await superAgentService.monitorEndpoints(endpointList, {
          includeMetrics: true,
          timeout: timeout || 5000
        });

        return {
          timestamp: result.timestamp,
          totalEndpoints: result.totalEndpoints,
          results: result.results.map(res => ({
            name: res.name || res.url,
            healthy: res.healthy,
            url: res.url || '',
            responseTime: res.responseTime || 0,
            status: res.status || 0,
            timestamp: res.timestamp,
            error: res.error || null,
            metrics: res.metrics ? JSON.stringify(res.metrics) : null
          }))
        };
      } catch (error) {
        console.error('GraphQL monitor endpoints error:', error);
        throw new Error(`Failed to monitor endpoints: ${error.message}`);
      }
    }
  },

  Mutation: {
    /**
     * Send external notification
     */
    sendExternalNotification: async (_, { input }) => {
      try {
        const { platform, webhook_url, message, type, title } = input;
        
        let result;
        const notificationData = {
          text: message,
          type: type || 'info',
          title: title || 'GraphQL Notification'
        };

        switch (platform.toLowerCase()) {
          case 'slack':
            result = await externalNotificationService.sendSlackNotification(webhook_url, notificationData);
            break;
          case 'teams':
            result = await externalNotificationService.sendTeamsNotification(webhook_url, notificationData);
            break;
          case 'discord':
            result = await externalNotificationService.sendDiscordNotification(webhook_url, notificationData);
            break;
          default:
            throw new Error(`Unsupported platform: ${platform}`);
        }

        return {
          success: result.success,
          platform: result.platform,
          timestamp: result.timestamp,
          response: result.response || result.error || 'No response data',
          error: result.error || null
        };
      } catch (error) {
        console.error('GraphQL send external notification error:', error);
        return {
          success: false,
          platform: input.platform,
          timestamp: new Date().toISOString(),
          response: '',
          error: error.message
        };
      }
    },

    /**
     * Send multi-platform notification
     */
    sendMultiPlatformNotification: async (_, { input }) => {
      try {
        const { message, platforms, type, title } = input;
        
        const universalMessage = {
          text: message,
          type: type || 'info',
          title: title || 'GraphQL Multi-Platform Notification'
        };

        const platformConfigs = platforms.map(p => ({
          type: p.platform.toLowerCase(),
          webhook: p.webhook_url,
          name: p.name || p.platform
        }));

        const result = await externalNotificationService.sendMultiPlatformNotification(universalMessage, platformConfigs);

        return {
          success: result.overall.success,
          totalPlatforms: result.overall.totalPlatforms,
          successfulSends: result.overall.successfulSends,
          failedSends: result.overall.failedSends,
          totalTime: result.overall.totalTime,
          timestamp: result.overall.timestamp,
          results: result.results.map(res => ({
            platform: res.platform,
            success: res.success,
            timestamp: res.timestamp,
            response: res.response || res.error || 'No response data',
            error: res.error || null
          }))
        };
      } catch (error) {
        console.error('GraphQL multi-platform notification error:', error);
        throw new Error(`Failed to send multi-platform notification: ${error.message}`);
      }
    },

    /**
     * Register webhook
     */
    registerWebhook: async (_, { input }) => {
      try {
        const { name, url, headers, timeout, format } = input;
        
        const config = {
          url,
          headers: headers ? JSON.parse(headers) : {},
          timeout: timeout || 30000,
          format: format || 'json',
          active: true
        };

        externalNotificationService.registerWebhook(name, config);

        return {
          success: true,
          name,
          url: url.substring(0, 50) + '...',
          format: config.format,
          timeout: config.timeout,
          message: `Webhook '${name}' registered successfully`
        };
      } catch (error) {
        console.error('GraphQL register webhook error:', error);
        return {
          success: false,
          name: input.name,
          url: '',
          format: '',
          timeout: 0,
          message: `Failed to register webhook: ${error.message}`
        };
      }
    },

    /**
     * Test webhook connectivity
     */
    testWebhook: async (_, { webhookName }) => {
      try {
        const result = await externalNotificationService.testWebhook(webhookName);
        
        return {
          success: result.success,
          webhook: result.webhook,
          timestamp: result.timestamp,
          response: result.response || 'Test completed',
          error: result.error || null,
          config: result.webhook_config ? JSON.stringify(result.webhook_config) : null
        };
      } catch (error) {
        console.error('GraphQL test webhook error:', error);
        return {
          success: false,
          webhook: webhookName,
          timestamp: new Date().toISOString(),
          response: '',
          error: error.message,
          config: null
        };
      }
    },

    /**
     * Update webhook status
     */
    updateWebhookStatus: async (_, { webhookName, active }) => {
      try {
        const success = externalNotificationService.setWebhookStatus(webhookName, active);
        
        return {
          success,
          webhook: webhookName,
          active,
          message: success 
            ? `Webhook '${webhookName}' ${active ? 'enabled' : 'disabled'}` 
            : `Webhook '${webhookName}' not found`,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('GraphQL update webhook status error:', error);
        return {
          success: false,
          webhook: webhookName,
          active,
          message: `Failed to update webhook status: ${error.message}`,
          timestamp: new Date().toISOString()
        };
      }
    },

    /**
     * Perform health check
     */
    performHealthCheck: async () => {
      try {
        const healthStatus = await healthMonitoringService.performHealthCheck();
        
        return {
          overall: healthStatus.overall,
          timestamp: healthStatus.timestamp,
          services: healthStatus.services ? Object.keys(healthStatus.services).map(serviceName => ({
            name: serviceName,
            status: healthStatus.services[serviceName].status || 'unknown',
            responseTime: healthStatus.services[serviceName].responseTime || 0,
            details: JSON.stringify(healthStatus.services[serviceName].details || {})
          })) : [],
          message: `Health check completed: ${healthStatus.overall}`,
          error: healthStatus.error || null
        };
      } catch (error) {
        console.error('GraphQL perform health check error:', error);
        return {
          overall: 'error',
          timestamp: new Date().toISOString(),
          services: [],
          message: 'Health check failed',
          error: error.message
        };
      }
    }
  }
};
