import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Modal, Spin, Button, Space, Tooltip, Grid } from 'antd';
import { FullscreenOutlined, DownloadOutlined, CloseOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import { useLayout } from '../../../context/LayoutContext';
import './UnifiedChartModal.css';

const { useBreakpoint } = Grid;

/**
 * Enhanced Responsive Chart Modal Component
 * Provides consistent fullscreen chart display across all device sizes
 * Features responsive design, touch-friendly controls, and smooth animations
 */
const UnifiedChartModal = ({
  visible,
  onClose,
  title,
  data,
  chartType,
  chartConfig,
  children,
  exportEnabled = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const screens = useBreakpoint();

  // Use layout context for proper positioning and z-index management
  const {
    isMobile,
    isTablet,
    isDesktop,
    getModalDimensions,
    getModalZIndex,
    getModalMaskZIndex,
    getModalContainer
  } = useLayout();

  // Handle modal close with animation
  const handleClose = useCallback(() => {
    console.log('UnifiedChartModal: handleClose triggered');
    console.log('UnifiedChartModal: onClose callback type:', typeof onClose);
    console.log('UnifiedChartModal: visible prop:', visible);

    // Reset internal state first
    setIsLoading(false);
    setIsFullscreen(false);

    // Call the parent's onClose callback
    if (typeof onClose === 'function') {
      console.log('UnifiedChartModal: Calling onClose callback');
      onClose();
    } else {
      console.error('UnifiedChartModal: onClose is not a function!', onClose);
    }
  }, [onClose, visible]);

  // Enhanced keyboard events with fullscreen support
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (!visible) return;

      switch (event.key) {
        case 'Escape':
          if (isFullscreen) {
            setIsFullscreen(false);
          } else {
            handleClose();
          }
          break;
        case 'F11':
          event.preventDefault();
          setIsFullscreen(!isFullscreen);
          break;
        default:
          break;
      }
    };

    if (visible) {
      document.addEventListener('keydown', handleKeyPress);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [visible, handleClose, isFullscreen]);

  // Handle export functionality
  const handleExport = useCallback(() => {
    setIsLoading(true);
    try {
      // Export functionality would be implemented here
      console.log('Exporting chart:', title);
      // Could integrate with chart-specific export methods
    } finally {
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [title]);

  // Toggle fullscreen mode
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Get modal dimensions from layout context (accounts for sidebar positioning)
  const modalDimensions = useMemo(() => {
    return getModalDimensions(isFullscreen);
  }, [getModalDimensions, isFullscreen]);

  // Calculate responsive chart height
  const getModalChartHeight = useCallback(() => {
    const viewportHeight = window.innerHeight;
    const headerHeight = isMobile ? 60 : 80;
    const paddingHeight = isMobile ? 16 : 32;

    if (isFullscreen) {
      return viewportHeight - headerHeight - paddingHeight;
    }

    if (isMobile) {
      return viewportHeight - headerHeight - paddingHeight;
    }

    if (isTablet) {
      return Math.floor(viewportHeight * 0.75);
    }

    // Desktop
    return Math.floor(viewportHeight * 0.7);
  }, [isMobile, isTablet, isFullscreen]);

  // Enhanced chart rendering with responsive optimizations
  const renderModalChart = useCallback(() => {
    if (isLoading) {
      return (
        <div
          style={{
            height: getModalChartHeight(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: '16px'
          }}
        >
          <Spin size="large" tip={isMobile ? "Chargement..." : "Chargement du graphique..."} />
          {!isMobile && (
            <div style={{ color: '#666', fontSize: '14px' }}>
              Préparation de l'affichage optimisé...
            </div>
          )}
        </div>
      );
    }

    if (!data || data.length === 0) {
      return (
        <div
          style={{
            height: getModalChartHeight(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            color: '#999',
            fontSize: isMobile ? '14px' : '16px',
            gap: '8px'
          }}
        >
          <div>Aucune donnée disponible</div>
          {!isMobile && (
            <div style={{ fontSize: '12px', opacity: 0.7 }}>
              Vérifiez vos filtres ou la période sélectionnée
            </div>
          )}
        </div>
      );
    }

    const chartHeight = getModalChartHeight();

    return (
      <div
        style={{
          height: chartHeight,
          width: '100%',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <div
          id="modal-chart"
          style={{
            height: '100%',
            width: '100%',
            transition: 'all 0.3s ease'
          }}
        >
          {React.cloneElement(children, {
            // Pass original props first
            ...children.props,
            // Override with modal-specific props
            data: data,
            height: chartHeight,
            enhanced: true,
            expanded: true,
            isModal: true,
            isMobile: isMobile,
            isTablet: isTablet,
            isFullscreen: isFullscreen,
            chartConfig: {
              ...chartConfig,
              // Responsive chart configuration
              showAllLabels: !isMobile,
              labelInterval: isMobile ? 1 : 0,
              maxBarSize: isMobile ? 40 : 60,
              strokeWidth: isMobile ? 2 : 3,
              dotSize: isMobile ? 4 : 6,
              fontSize: isMobile ? 10 : 12,
              // Touch-friendly settings for mobile
              touchEnabled: isMobile,
              gestureHandling: isMobile ? 'cooperative' : 'auto'
            },
          })}
        </div>
      </div>
    );
  }, [isLoading, data, getModalChartHeight, isMobile, isTablet, isFullscreen, children, chartConfig]);

  // Enhanced responsive modal header
  const modalHeader = useMemo(() => (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        background: `linear-gradient(135deg, ${chartConfig?.getPrimaryColor() || '#1890ff'} 0%, ${chartConfig?.colors?.[1] || '#096dd9'} 100%)`,
        padding: isMobile ? '8px 12px' : '12px 20px',
        margin: isMobile ? '-8px -12px 0 -12px' : '-16px -24px 0 -24px',
        borderRadius: isFullscreen || isMobile ? '0' : '8px 8px 0 0',
        minHeight: isMobile ? '48px' : '60px',
        position: 'relative',
        zIndex: 10
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div style={{
        display: 'flex',
        alignItems: 'center',
        flex: 1,
        minWidth: 0 // Allow text truncation
      }}>
        <span style={{
          color: 'white',
          fontSize: isMobile ? '14px' : '18px',
          fontWeight: 'bold',
          textShadow: '0 1px 2px rgba(0,0,0,0.3)',
          marginRight: '8px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {title}
        </span>
        {!isMobile && (
          <FullscreenOutlined style={{
            color: "white",
            fontSize: '16px',
            textShadow: '0 1px 2px rgba(0,0,0,0.3)'
          }} />
        )}
      </div>

      <Space size={isMobile ? 4 : 8}>
        {!isMobile && !isFullscreen && (
          <Tooltip title="Mode plein écran (F11)">
            <Button
              icon={<FullscreenOutlined />}
              onClick={handleFullscreenToggle}
              size={isMobile ? "small" : "middle"}
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '1px solid rgba(255,255,255,0.3)',
                color: 'white',
                minWidth: isMobile ? '32px' : '40px'
              }}
            />
          </Tooltip>
        )}
        {isFullscreen && (
          <Tooltip title="Quitter le plein écran (F11)">
            <Button
              icon={<FullscreenExitOutlined />}
              onClick={handleFullscreenToggle}
              size={isMobile ? "small" : "middle"}
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '1px solid rgba(255,255,255,0.3)',
                color: 'white',
                minWidth: isMobile ? '32px' : '40px'
              }}
            />
          </Tooltip>
        )}
        {exportEnabled && (
          <Tooltip title={isMobile ? "Exporter" : "Exporter le graphique"}>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
              loading={isLoading}
              size={isMobile ? "small" : "middle"}
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '1px solid rgba(255,255,255,0.3)',
                color: 'white',
                minWidth: isMobile ? '32px' : '40px'
              }}
            >
              {!isMobile && "Exporter"}
            </Button>
          </Tooltip>
        )}
        <Tooltip title={isMobile ? "Fermer" : "Fermer (Échap)"}>
          <Button
            icon={<CloseOutlined />}
            onClick={handleClose}
            size={isMobile ? "small" : "middle"}
            type="text"
            style={{
              background: 'rgba(255,255,255,0.2)',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white',
              minWidth: isMobile ? '32px' : '40px',
              position: 'relative',
              zIndex: 10,
              cursor: 'pointer'
            }}
          />
        </Tooltip>
      </Space>
    </div>
  ), [title, chartConfig, isMobile, isFullscreen, exportEnabled, isLoading, handleFullscreenToggle, handleExport, handleClose]);

  // Add effect to log when visible prop changes
  useEffect(() => {
    console.log('UnifiedChartModal: visible prop changed to:', visible);
  }, [visible]);

  // Add effect to force re-render when visible changes
  useEffect(() => {
    if (!visible) {
      console.log('UnifiedChartModal: Modal should be closed, visible is false');
      // Reset internal state when modal is closed
      setIsLoading(false);
      setIsFullscreen(false);
    }
  }, [visible]);

  return (
    <Modal
      key={`unified-chart-modal-${visible ? 'open' : 'closed'}`}
      title={modalHeader}
      open={visible}
      onCancel={handleClose}
      onClose={handleClose}
      width={modalDimensions.width}
      style={{
        top: isFullscreen ? 0 : modalDimensions.top,
        left: isFullscreen ? 0 : modalDimensions.left,
        maxWidth: isFullscreen ? '100vw' : modalDimensions.width,
        padding: isFullscreen ? 0 : modalDimensions.padding,
        margin: 0,
        zIndex: isFullscreen ? 10011 : getModalZIndex()
      }}
      styles={{
        body: {
          height: `calc(${modalDimensions.height} - ${isMobile ? '48px' : '60px'})`,
          padding: isMobile ? "8px" : "16px",
          margin: 0,
          overflow: "hidden",
          background: "#fafafa"
        },
        mask: {
          backgroundColor: 'transparent',
          zIndex: getModalMaskZIndex()
        }
      }}
      footer={null}
      destroyOnClose={true}
      maskClosable={!isFullscreen}
      keyboard={true}
      centered={false}
      getContainer={isFullscreen ? () => document.body : getModalContainer}
      className={`unified-chart-modal ${isMobile ? 'mobile' : ''} ${isTablet ? 'tablet' : ''} ${isFullscreen ? 'fullscreen' : ''}`}
      wrapClassName="unified-chart-modal-wrap"
      transitionName="unified-chart-modal"
      maskTransitionName="unified-chart-modal-mask"
    >
      <div style={{
        height: "100%",
        width: "100%",
        background: "white",
        borderRadius: isFullscreen || isMobile ? "0" : "8px",
        padding: isMobile ? "8px" : "16px",
        boxShadow: isFullscreen ? "none" : "0 4px 16px rgba(0,0,0,0.15)",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        position: "relative",
        zIndex: 1
      }}>
        {renderModalChart()}
      </div>
    </Modal>
  );
};

UnifiedChartModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  data: PropTypes.array.isRequired,
  chartType: PropTypes.string,
  chartConfig: PropTypes.object,
  children: PropTypes.element.isRequired,
  exportEnabled: PropTypes.bool,
};

export default UnifiedChartModal;
