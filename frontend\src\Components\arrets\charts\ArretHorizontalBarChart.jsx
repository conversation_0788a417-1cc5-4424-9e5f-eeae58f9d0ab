import React, { memo } from 'react';
import { Spin, Empty } from 'antd';
import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Cell } from 'recharts';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE,
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOMIPEM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
};

const ArretHorizontalBar<PERSON>hart = memo(({ data = [], loading, colors }) => {
  
  // Enhanced debug logging
  console.log('🔍 ArretHorizontalBar<PERSON>hart received:', {
    dataType: typeof data,
    isArray: Array.isArray(data),
    dataLength: Array.isArray(data) ? data.length : 'N/A',
    rawData: data,
    firstItem: Array.isArray(data) && data[0] ? data[0] : 'N/A'
  });

  // Use colors prop if provided, otherwise fall back to default SOMIPEM colors
  const chartColors = colors || [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.SUCCESS_GREEN,
    SOMIPEM_COLORS.WARNING_ORANGE
  ];
  
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" />
      </div>
    );
  }
  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);
  // Check if data is empty
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '300px' }}>
        <Empty description="Aucune donnée de causes d'arrêt disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }  // Process data to ensure consistent field names and limit to top 10
  const processedData = safeData.map(item => {
    const reason = item.reason || item.stopName || item.Stop_Reason || item.name || "Non défini";
    const count = item.count || item.frequency || item.value || 0;
    
    console.log('🔄 Processing stop reason item:', {
      originalItem: item,
      extractedReason: reason,
      extractedCount: count
    });
    
    return {
      reason: reason,
      count: count,
      duration: item.duration || 0
    };
  }).sort((a, b) => b.count - a.count).slice(0, 10); // Show top 10 causes
  
  console.log('📊 Final processed data for chart:', processedData);

  // Color gradient for bars using dynamic color system
  const getBarColor = (index, total) => {
    // Use the provided colors array, cycling through if necessary
    return chartColors[index % chartColors.length];
  };

  return (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart
        data={processedData}
        layout="vertical"
        margin={{ top: 20, right: 30, left: 150, bottom: 20 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          type="number" 
          tick={{ fontSize: 11, fill: SOMIPEM_COLORS.LIGHT_GRAY }}
          axisLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          tickLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
        />
        <YAxis 
          type="category" 
          dataKey="reason" 
          width={140}
          tick={{ fontSize: 11, fill: SOMIPEM_COLORS.DARK_GRAY }}
          axisLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          tickLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          tickFormatter={(value) => {
            // Truncate long reason names
            return value.length > 25 ? `${value.substring(0, 22)}...` : value;
          }}
        />
        <Tooltip
          formatter={(value, name, props) => [
            `${value} occurrence${value > 1 ? 's' : ''}`,
            "Fréquence"
          ]}
          labelFormatter={(label) => `Cause: ${label}`}
          contentStyle={{
            backgroundColor: "#fff",
            border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`,
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: '12px',
            color: SOMIPEM_COLORS.DARK_GRAY
          }}
        />        <Bar 
          dataKey="count" 
          name="Fréquence" 
          barSize={24}
          radius={[0, 6, 6, 0]}
        >
          {processedData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={getBarColor(index, processedData.length)} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
});

export default ArretHorizontalBarChart;
