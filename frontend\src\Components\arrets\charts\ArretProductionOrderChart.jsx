import React, { memo, useMemo } from 'react';
import { Spin, Empty } from 'antd';
import { Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, Cell } from 'recharts';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE,
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOMIPEM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
};

const ArretProductionOrder<PERSON>hart = memo(({ data = [], loading, colors }) => {
  // Use colors prop if provided, otherwise fall back to default SOMIPEM colors
  const chartColors = colors || [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.SUCCESS_GREEN,
    SOMIPEM_COLORS.WARNING_ORANGE
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Process data to analyze stops by production order (Part_NO)
  const processedData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group stops by Part_NO and calculate metrics
    const orderStats = data.reduce((acc, stop) => {
      const partNo = stop.Part_NO || stop.partNo || stop.part_no || "Non défini";
      const duration = parseFloat(stop.duration_minutes) || parseFloat(stop.Duree_Arret) || 0;
      
      if (!acc[partNo]) {
        acc[partNo] = {
          partNo: partNo,
          stopCount: 0,
          totalDuration: 0,
          avgDuration: 0,
          machines: new Set()
        };
      }
      
      acc[partNo].stopCount += 1;
      acc[partNo].totalDuration += duration;
      acc[partNo].machines.add(stop.Machine_Name || stop.machine || "Inconnue");
      
      return acc;
    }, {});

    // Convert to array and calculate averages
    const result = Object.values(orderStats).map(order => ({
      partNo: order.partNo,
      stopCount: order.stopCount,
      totalDuration: Math.round(order.totalDuration),
      avgDuration: order.stopCount > 0 ? Math.round(order.totalDuration / order.stopCount) : 0,
      machineCount: order.machines.size,
      efficiency: Math.max(0, 100 - (order.stopCount * 2)) // Simple efficiency metric
    }));

    // Sort by stop count (most problematic orders first) and limit to top 15
    return result
      .sort((a, b) => b.stopCount - a.stopCount)
      .slice(0, 15);
  }, [data]);

  if (!processedData || processedData.length === 0) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '300px' }}>
        <Empty 
          description="Aucune donnée de production disponible" 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      </div>
    );
  }

  // Color gradient for bars using dynamic color system
  const getBarColor = (index, total) => {
    // Use the provided colors array, cycling through if necessary
    return chartColors[index % chartColors.length];
  };

  return (
    <ResponsiveContainer width="100%" height={420}>
      <BarChart
        data={processedData}
        margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="partNo"
          tick={{ fontSize: 10, fill: SOMIPEM_COLORS.LIGHT_GRAY, angle: -45, textAnchor: 'end' }}
          axisLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          tickLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          height={80}
          interval={0}
          tickFormatter={(value) => {
            // Truncate long part numbers
            return value.length > 12 ? `${value.substring(0, 10)}...` : value;
          }}
        />
        <YAxis 
          tick={{ fontSize: 11, fill: SOMIPEM_COLORS.LIGHT_GRAY }}
          axisLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          tickLine={{ stroke: SOMIPEM_COLORS.LIGHT_GRAY }}
          label={{ value: 'Nombre d\'arrêts', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: SOMIPEM_COLORS.DARK_GRAY } }}
        />
        <Tooltip
          formatter={(value, name, props) => {
            const data = props.payload;
            return [
              <div key="tooltip" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                <div><strong>Commande:</strong> {data.partNo}</div>
                <div><strong>Arrêts:</strong> {data.stopCount}</div>
                <div><strong>Durée totale:</strong> {data.totalDuration} min</div>
                <div><strong>Durée moyenne:</strong> {data.avgDuration} min</div>
                <div><strong>Machines:</strong> {data.machineCount}</div>
                <div><strong>Efficacité:</strong> {data.efficiency}%</div>
              </div>
            ];
          }}
          labelFormatter={() => ''}
          contentStyle={{
            backgroundColor: "#fff",
            border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`,
            borderRadius: 8,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            fontSize: '12px',
            padding: '12px'
          }}
        />
        <Bar 
          dataKey="stopCount" 
          name="Nombre d'arrêts"
          radius={[4, 4, 0, 0]}
          maxBarSize={50}
        >
          {processedData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={getBarColor(index, processedData.length)} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
});

ArretProductionOrderChart.displayName = 'ArretProductionOrderChart';

export default ArretProductionOrderChart;
