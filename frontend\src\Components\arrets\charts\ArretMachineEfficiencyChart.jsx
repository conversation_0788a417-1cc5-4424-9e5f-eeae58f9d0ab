import React, { useEffect, useRef, useState } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { Spin, Empty, Button, Space, Row, Col } from 'antd';
import { TrophyOutlined, StopOutlined } from '@ant-design/icons';
import SOMIPEM_COLORS from '../../../styles/brand-colors';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';
import { useSettings } from '../../../hooks/useSettings';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const ArretMachineEfficiencyChart = ({ data = [], loading = false, colors }) => {
  const chartRef = useRef();
  const [viewMode, setViewMode] = useState('efficiency'); // 'efficiency', 'stops', or 'both'
  
  // Get settings for unified chart configuration
  const { settings, charts, theme } = useSettings();
  
  // Unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    charts,
    theme
  });
  
  // Use colors prop if provided, otherwise fall back to chart config colors
  const chartColors = colors || chartConfig.colors || [
    SOMIPEM_COLORS.PRIMARY_BLUE,
    SOMIPEM_COLORS.SECONDARY_BLUE,
    SOMIPEM_COLORS.CHART_TERTIARY,
    SOMIPEM_COLORS.SUCCESS_GREEN,
    SOMIPEM_COLORS.WARNING_ORANGE
  ];
  // Process data to analyze machine efficiency
  const processMachineEfficiency = (stopsData) => {
    console.log('🎯 ArretMachineEfficiencyChart - Data received:', {
      dataLength: stopsData?.length || 0,
      dataType: typeof stopsData,
      isArray: Array.isArray(stopsData),
      loading,
      sampleData: stopsData?.slice(0, 2) || []
    });

    // Ensure data is an array - handle both direct arrays and response objects
    const processedData = Array.isArray(stopsData) ? stopsData : (stopsData?.data || []);
    
    if (!Array.isArray(processedData) || processedData.length === 0) {
      console.log('🔍 ArretMachineEfficiencyChart - No valid data to process');
      return { labels: [], datasets: [] };
    }

    // Group by machine
    const machineStats = {};
    
    processedData.forEach(stop => {
      // Handle multiple possible field names for machine name
      const machine = stop.Machine_Name || stop.machineName || stop.machine_name || 'Unknown';
      
      if (!machineStats[machine]) {
        machineStats[machine] = {
          stopCount: 0,
          totalDuration: 0,
          avgDuration: 0,
          efficiency: 100 // Start with 100% efficiency
        };
      }
      
      machineStats[machine].stopCount += 1;
      
      // Calculate duration from multiple possible sources
      let duration = 0;
      
      if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
        duration = parseFloat(stop.duration_minutes);
      } else if (stop.duration !== undefined && stop.duration !== null) {
        duration = parseFloat(stop.duration);
      } else {
        // Calculate from start/end times
        const startTime = stop.Debut_Stop || stop.debut_stop || stop.startTime || stop.start_time;
        const endTime = stop.Fin_Stop_Time || stop.fin_stop_time || stop.endTime || stop.end_time;
        
        if (startTime && endTime) {
          try {
            // Handle ISO format dates from GraphQL
            const start = new Date(startTime);
            const end = new Date(endTime);
            
            if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
              duration = (end - start) / (1000 * 60); // Duration in minutes
            }
          } catch (error) {
            console.warn('Error calculating duration:', error);
          }
        }
      }
      
      if (duration > 0) {
        machineStats[machine].totalDuration += duration;
      }
    });

    // Calculate efficiency metrics
    Object.keys(machineStats).forEach(machine => {
      const stats = machineStats[machine];
      stats.avgDuration = stats.stopCount > 0 ? stats.totalDuration / stats.stopCount : 0;
      
      // Calculate efficiency based on stops and duration
      // Lower stops and shorter duration = higher efficiency
      const maxStops = Math.max(...Object.values(machineStats).map(s => s.stopCount));
      const maxDuration = Math.max(...Object.values(machineStats).map(s => s.totalDuration));
      
      if (maxStops > 0 && maxDuration > 0) {
        const stopPenalty = (stats.stopCount / maxStops) * 50; // 50% penalty for stops
        const durationPenalty = (stats.totalDuration / maxDuration) * 50; // 50% penalty for duration
        stats.efficiency = Math.max(0, 100 - stopPenalty - durationPenalty);
      }
    });

    // Sort by efficiency (best first)
    const sortedMachines = Object.entries(machineStats)
      .sort(([,a], [,b]) => b.efficiency - a.efficiency);

    const labels = sortedMachines.map(([machine]) => machine);
    const efficiencyScores = sortedMachines.map(([, stats]) => Math.round(stats.efficiency));
    const stopCounts = sortedMachines.map(([, stats]) => stats.stopCount);

    return {
      labels,
      efficiencyScores,
      stopCounts,
      machineStats: Object.fromEntries(sortedMachines)
    };
  };

  const chartData = processMachineEfficiency(data);

  const getDatasets = () => {
    if (viewMode === 'efficiency') {
      return [
        {
          label: 'Score d\'Efficacité (%)',
          data: chartData.efficiencyScores,
          backgroundColor: chartData.efficiencyScores.map((score, index) => {
            // Use dynamic colors with opacity based on efficiency scores
            const baseColor = chartColors[index % chartColors.length];
            return `${baseColor}CC`; // Add transparency
          }),
          borderColor: chartData.efficiencyScores.map((score, index) => {
            return chartColors[index % chartColors.length];
          }),
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false
        }
      ];
    } else if (viewMode === 'stops') {
      return [
        {
          label: 'Nombre d\'Arrêts',
          data: chartData.stopCounts,
          backgroundColor: `${chartColors[1]}99`, // Using second color with transparency
          borderColor: chartColors[1],
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false
        }
      ];
    } else {
      // both mode
      return [
        {
          label: 'Score d\'Efficacité (%)',
          data: chartData.efficiencyScores,
          backgroundColor: chartData.efficiencyScores.map((score, index) => {
            const baseColor = chartColors[index % chartColors.length];
            return `${baseColor}CC`;
          }),
          borderColor: chartData.efficiencyScores.map((score, index) => {
            return chartColors[index % chartColors.length];
          }),
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false,
          yAxisID: 'y'
        },
        {
          label: 'Nombre d\'Arrêts',
          data: chartData.stopCounts,
          backgroundColor: `${chartColors[1]}99`,
          borderColor: chartColors[1],
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false,
          yAxisID: 'y1'
        }
      ];
    }
  };
  const getOptions = () => {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              weight: '500'
            }
          }
        },
        title: {
          display: true,
          text: viewMode === 'efficiency' ? 'Score d\'Efficacité des Machines' : 
                viewMode === 'stops' ? 'Nombre d\'Arrêts par Machine' : 
                'Efficacité et Arrêts des Machines',
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: 20
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context) {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              
              if (label.includes('Efficacité')) {
                return `${label}: ${value}%`;
              } else {
                return `${label}: ${value} arrêts`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 11
            },
            maxRotation: 45,
            minRotation: 0
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      },
      animation: {
        duration: chartConfig.animationConfig?.isAnimationActive ? 1000 : 0,
        easing: 'easeInOutQuart'
      }
    };

    if (viewMode === 'both') {
      // Dual axis for both mode
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Score d\'Efficacité (%)',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          max: 100,
          font: {
            size: 11
          },
          callback: function(value) {
            return value + '%';
          }
        }
      };
      baseOptions.scales.y1 = {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Nombre d\'Arrêts',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          beginAtZero: true,
          font: {
            size: 11
          }
        }
      };
    } else {
      // Single axis
      baseOptions.scales.y = {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: viewMode === 'efficiency' ? 'Score d\'Efficacité (%)' : 'Nombre d\'Arrêts',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          beginAtZero: true,
          max: viewMode === 'efficiency' ? 100 : undefined,
          font: {
            size: 11
          },
          callback: function(value) {
            return viewMode === 'efficiency' ? value + '%' : value;
          }
        }
      };
    }

    return baseOptions;
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Spin size="large" tip="Chargement de l'efficacité des machines..." />
      </div>
    );
  }

  if (!chartData.labels || chartData.labels.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)',
        borderRadius: '12px'
      }}>
        <Empty 
          description="Aucune donnée d'efficacité disponible"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }
  return (
    <div style={{ height: '100%', width: '100%' }}>
      {/* View Mode Toggle */}
      <Row style={{ marginBottom: '12px' }}>
        <Col span={24}>
          <Space size="small" style={{ width: '100%', justifyContent: 'center' }}>
            <Button
              type={viewMode === 'efficiency' ? 'primary' : 'default'}
              icon={<TrophyOutlined />}
              onClick={() => setViewMode('efficiency')}
              size="small"
            >
              Efficacité
            </Button>
            <Button
              type={viewMode === 'stops' ? 'primary' : 'default'}
              icon={<StopOutlined />}
              onClick={() => setViewMode('stops')}
              size="small"
            >
              Arrêts
            </Button>
            <Button
              type={viewMode === 'both' ? 'primary' : 'default'}
              onClick={() => setViewMode('both')}
              size="small"
            >
              Les deux
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Chart */}
      <div style={{ 
        height: '400px', // Fixed height for better visibility
        minHeight: '300px',
        width: '100%',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        borderRadius: '12px',
        padding: '16px',
        overflow: 'hidden'
      }}>
        <Bar 
          ref={chartRef} 
          data={{
            labels: chartData.labels,
            datasets: getDatasets()
          }} 
          options={getOptions()} 
        />
      </div>
    </div>
  );
};

export default ArretMachineEfficiencyChart;
