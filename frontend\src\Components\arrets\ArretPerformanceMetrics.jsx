import React from 'react';
import { Row, Col, Card, Statistic, Tooltip, Typography, Space, Alert } from 'antd';
import { 
  ClockCircleOutlined, 
  ToolOutlined, 
  TrophyOutlined, 
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import SOMIPEM_COLORS from '../../styles/brand-colors';

const { Text, Title } = Typography;

const ArretPerformanceMetrics = () => {
  const context = useArretQueuedContext();
  
  if (!context) {
    return <div>Context not available</div>;
  }  const { 
    mttr = 0, 
    mtbf = 0, 
    doper = 0, 
    showPerformanceMetrics = false,
    selectedMachine,
    loading = false
  } = context;
  // Only show performance metrics when we have a specific machine selected and have data
  if (!selectedMachine || (!showPerformanceMetrics && mttr === 0 && mtbf === 0 && doper === 0)) {
    return null;
  }

  // Helper function to get status color based on metric value
  const getMetricStatus = (value, type) => {
    switch (type) {
      case 'mttr':
        return value <= 30 ? 'success' : value <= 60 ? 'warning' : 'error';
      case 'mtbf':
        return value >= 120 ? 'success' : value >= 60 ? 'warning' : 'error';
      case 'doper':
        return value >= 85 ? 'success' : value >= 75 ? 'warning' : 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />; // Keep green for success
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />; // Secondary Blue for warning
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />; // Keep red for error
      default:
        return <InfoCircleOutlined style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }} />;
    }
  };

  const mttrStatus = getMetricStatus(mttr, 'mttr');
  const mtbfStatus = getMetricStatus(mtbf, 'mtbf');
  const doperStatus = getMetricStatus(doper, 'doper');

  return (
    <div style={{ marginBottom: '24px' }}>      <Alert        
        message={
          <Space>
            <InfoCircleOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />
            <Text strong style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Indicateurs de Performance - Machine {selectedMachine || 'Sélectionnée'}</Text>
          </Space>
        }
        description={
          <span style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>Ces métriques évaluent la performance et la disponibilité de la machine sélectionnée</span>
        }
        type="info"
        showIcon={false}
        style={{ 
          marginBottom: '16px', 
          borderRadius: '8px',
          backgroundColor: '#FFFFFF',
          border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`
        }}
      />
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card 
            hoverable
            style={{ 
              backgroundColor: "#FFFFFF", // White background
              borderRadius: '12px',
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border (consistent)
              borderTop: `3px solid ${mttrStatus === 'success' ? '#52c41a' : mttrStatus === 'warning' ? SOMIPEM_COLORS.SECONDARY_BLUE : '#ff4d4f'}`, // Status indicator on top
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Statistic
              title={
                <Space>
                  <ClockCircleOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} /> {/* Primary Blue icon */}
                  <span style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>MTTR (Temps Moyen de Réparation)</span> {/* Dark Gray title */}
                  <Tooltip title="Temps moyen nécessaire pour réparer une panne. Plus faible = mieux.">
                    <InfoCircleOutlined style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }} /> {/* Light Gray info icon */}
                  </Tooltip>
                </Space>
              }
              value={mttr}
              precision={1}
              suffix="min"
              loading={loading}
              valueStyle={{ 
                color: mttrStatus === 'success' ? '#52c41a' : mttrStatus === 'warning' ? SOMIPEM_COLORS.SECONDARY_BLUE : '#ff4d4f', // Keep status colors for values
                fontSize: '28px',
                fontWeight: 'bold'
              }}
              prefix={getStatusIcon(mttrStatus)}
            />
            <div style={{ marginTop: '8px' }}>
              <Text style={{ 
                fontSize: '12px',
                color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for secondary text
              }}>
                {mttrStatus === 'success' && "Excellent - Réparations rapides"}
                {mttrStatus === 'warning' && "Correct - Peut être amélioré"}
                {mttrStatus === 'error' && "Attention - Réparations lentes"}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card 
            hoverable
            style={{ 
              backgroundColor: "#FFFFFF", // White background
              borderRadius: '12px',
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border (consistent)
              borderTop: `3px solid ${mtbfStatus === 'success' ? '#52c41a' : mtbfStatus === 'warning' ? SOMIPEM_COLORS.SECONDARY_BLUE : '#ff4d4f'}`, // Status indicator on top
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Statistic
              title={
                <Space>
                  <ToolOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} /> {/* Primary Blue icon */}
                  <span style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>MTBF (Temps Moyen Entre Pannes)</span> {/* Dark Gray title */}
                  <Tooltip title="Temps moyen de fonctionnement entre deux pannes. Plus élevé = mieux.">
                    <InfoCircleOutlined style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }} /> {/* Light Gray info icon */}
                  </Tooltip>
                </Space>
              }
              value={mtbf}
              precision={1}
              suffix="min"
              loading={loading}
              valueStyle={{ 
                color: mtbfStatus === 'success' ? '#52c41a' : mtbfStatus === 'warning' ? SOMIPEM_COLORS.SECONDARY_BLUE : '#ff4d4f', // Keep status colors for values
                fontSize: '28px',
                fontWeight: 'bold'
              }}
              prefix={getStatusIcon(mtbfStatus)}
            />
            <div style={{ marginTop: '8px' }}>
              <Text style={{ 
                fontSize: '12px',
                color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for secondary text
              }}>
                {mtbfStatus === 'success' && "Excellent - Machine fiable"}
                {mtbfStatus === 'warning' && "Correct - Surveillance recommandée"}
                {mtbfStatus === 'error' && "Attention - Pannes fréquentes"}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card 
            hoverable
            style={{ 
              backgroundColor: "#FFFFFF", // White background
              borderRadius: '12px',
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border (consistent)
              borderTop: `3px solid ${doperStatus === 'success' ? '#52c41a' : doperStatus === 'warning' ? SOMIPEM_COLORS.SECONDARY_BLUE : '#ff4d4f'}`, // Status indicator on top
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Statistic
              title={
                <Space>
                  <TrophyOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} /> {/* Primary Blue icon */}
                  <span style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>DOPER (Disponibilité)</span> {/* Dark Gray title */}
                  <Tooltip title="Pourcentage de temps où la machine est opérationnelle. Plus élevé = mieux.">
                    <InfoCircleOutlined style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }} /> {/* Light Gray info icon */}
                  </Tooltip>
                </Space>
              }
              value={doper}
              precision={1}
              suffix="%"
              loading={loading}
              valueStyle={{ 
                color: doperStatus === 'success' ? '#52c41a' : doperStatus === 'warning' ? SOMIPEM_COLORS.SECONDARY_BLUE : '#ff4d4f', // Keep status colors for values
                fontSize: '28px',
                fontWeight: 'bold'
              }}
              prefix={getStatusIcon(doperStatus)}
            />
            <div style={{ marginTop: '8px' }}>
              <Text style={{ 
                fontSize: '12px',
                color: SOMIPEM_COLORS.LIGHT_GRAY // Light Gray for secondary text
              }}>
                {doperStatus === 'success' && "Excellent - Très disponible"}
                {doperStatus === 'warning' && "Correct - Peut être optimisé"}
                {doperStatus === 'error' && "Attention - Disponibilité faible"}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card 
            size="small" 
            style={{ 
              backgroundColor: '#FFFFFF', // White background
              borderRadius: '8px',
              border: `1px solid ${SOMIPEM_COLORS.LIGHT_GRAY}`, // Light Gray border
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
          >
            <Title level={5} style={{ 
              margin: 0, 
              color: SOMIPEM_COLORS.DARK_GRAY // Dark Gray for title
            }}>
              <InfoCircleOutlined style={{ 
                marginRight: '8px',
                color: SOMIPEM_COLORS.PRIMARY_BLUE // Primary Blue for icon
              }} />
              Guide d'interprétation
            </Title>
            <Row gutter={[16, 8]} style={{ marginTop: '12px' }}>
              <Col xs={24} md={8}>
                <Text style={{ 
                  fontSize: '12px',
                  color: SOMIPEM_COLORS.DARK_GRAY // Dark Gray for text
                }}>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                  <strong>MTTR optimal:</strong> &lt; 30 min
                </Text>
              </Col>
              <Col xs={24} md={8}>
                <Text style={{ 
                  fontSize: '12px',
                  color: SOMIPEM_COLORS.DARK_GRAY // Dark Gray for text
                }}>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                  <strong>MTBF optimal:</strong> &gt; 120 min
                </Text>
              </Col>
              <Col xs={24} md={8}>
                <Text style={{ 
                  fontSize: '12px',
                  color: SOMIPEM_COLORS.DARK_GRAY // Dark Gray for text
                }}>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                  <strong>DOPER optimal:</strong> &gt; 85%
                </Text>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ArretPerformanceMetrics;
