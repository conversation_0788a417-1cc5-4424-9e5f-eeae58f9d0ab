import dayjs from 'dayjs';
import cache from 'memory-cache';

/**
 * Enhanced Report Data Service with caching and optimization
 */
export class ReportDataService {
  constructor(db) {
    this.db = db;
    this.cacheTimeout = 10 * 60 * 1000; // Increased to 10 minutes for better performance
  }

  /**
   * Execute database queries using promise-based connection pool
   * NO MOCK DATA FALLBACKS - Real data only
   */
  async query(sql, params = [], timeoutMs = 30000) {
    try {
      console.log(`🔍 [DB Query] Executing: ${sql.substring(0, 100)}...`);
      console.log(`🔍 [DB Query] Params:`, params);

      // Use promise-based execute directly from the connection pool
      const queryPromise = this.db.execute(sql, params);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Database query timeout after ${timeoutMs}ms: ${sql.substring(0, 100)}...`)), timeoutMs)
      );

      const [results] = await Promise.race([queryPromise, timeoutPromise]);

      console.log(`✅ [DB Query] Success: ${results.length} rows returned`);
      return results;
    } catch (error) {
      console.error(`❌ [DB Query] Failed: ${error.message}`);
      console.error(`❌ [DB Query] Stack:`, error.stack);
      throw error; // Always throw - no mock data fallbacks
    }
  }

  /**
   * Get cached data or fetch from database
   */
  async getCachedData(key, fetchFunction) {
    const cached = cache.get(key);
    if (cached) {
      console.log(`Cache hit for key: ${key}`);
      return cached;
    }

    console.log(`Cache miss for key: ${key}, fetching from database`);
    const data = await fetchFunction();
    cache.put(key, data, this.cacheTimeout);
    return data;
  }

  /**
   * Safe numeric parser with French locale support
   */
  parseNumeric(value, defaultValue = 0) {
    if (value === null || value === undefined) return defaultValue;
    
    // Handle percentage values and comma as decimal separator
    const cleanValue = value.toString()
      .replace('%', '')
      .replace(',', '.')
      .trim();
    
    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * Calculate shift time windows with proper timezone handling
   */
  calculateShiftTimeWindow(date, shift) {
    // Use the provided date, not current date
    const targetDate = date ? dayjs(date) : dayjs();
    
    const shiftTimes = {
      "matin": {
        start: targetDate.format("YYYY-MM-DD 06:00:00"),
        end: targetDate.format("YYYY-MM-DD 14:00:00")
      },
      "apres-midi": {
        start: targetDate.format("YYYY-MM-DD 14:00:00"),
        end: targetDate.format("YYYY-MM-DD 22:00:00")
      },
      "après-midi": {
        start: targetDate.format("YYYY-MM-DD 14:00:00"),
        end: targetDate.format("YYYY-MM-DD 22:00:00")
      },
      "nuit": {
        start: targetDate.format("YYYY-MM-DD 22:00:00"),
        end: targetDate.add(1, 'day').format("YYYY-MM-DD 06:00:00")
      }
    };

    // Normalize shift name to lowercase for matching
    const normalizedShift = shift ? shift.toLowerCase().trim() : '';
    
    if (shiftTimes[normalizedShift]) {
      return shiftTimes[normalizedShift];
    }

    // Default to 8-hour window on the target date
    return {
      start: targetDate.format("YYYY-MM-DD 06:00:00"),
      end: targetDate.format("YYYY-MM-DD 14:00:00")
    };
  }

  /**
   * Get machine daily data with optimization and faster timeout
   */
  async getMachineData(machineId) {
    const cacheKey = `machine_daily_${machineId}`;
    
    return this.getCachedData(cacheKey, async () => {
      try {
        console.log(`🔍 Searching for machine data: ${machineId}`);
        
        // Optimized query with index hint and reduced timeout
        const results = await this.query(
          `SELECT Machine_Name, Date_Insert_Day, Run_Hours_Day, Down_Hours_Day,
                  Good_QTY_Day, Rejects_QTY_Day, Speed_Day, Availability_Rate_Day,
                  Performance_Rate_Day, Quality_Rate_Day, OEE_Day, Shift,
                  Part_Number, Poid_Unitaire, Cycle_Theorique, Poid_Purge
           FROM machine_daily_table_mould
           WHERE Machine_Name = ?
           ORDER BY Date_Insert_Day DESC
           LIMIT 1`,
          [machineId],
          12000 // Increased timeout for better reliability
        );

        if (results.length > 0) {
          console.log(`✅ Found machine data for: ${machineId}`);
          this.logDataSource(machineId, 'MACHINE_DAILY', 'machine_daily_table_mould', results.length);
          return results[0];
        }

        // If no exact match, try case-insensitive search
        console.log(`🔍 No exact match for ${machineId}, trying case-insensitive search`);
        const caseInsensitiveResults = await this.query(
          `SELECT Machine_Name, Date_Insert_Day, Run_Hours_Day, Down_Hours_Day, 
                  Good_QTY_Day, Rejects_QTY_Day, Speed_Day, Availability_Rate_Day,
                  Performance_Rate_Day, Quality_Rate_Day, OEE_Day, Shift,
                  Part_Number, Poid_Unitaire, Cycle_Theorique, Poid_Purge
           FROM machine_daily_table_mould
           WHERE UPPER(Machine_Name) = UPPER(?)
           ORDER BY Date_Insert_Day DESC
           LIMIT 1`,
          [machineId],
          5000 // 5 second timeout
        );

        if (caseInsensitiveResults.length > 0) {
          console.log(`✅ Found case-insensitive match for: ${machineId}`);
          this.logDataSource(machineId, 'MACHINE_DAILY', 'machine_daily_table_mould (case-insensitive)', caseInsensitiveResults.length);
          return caseInsensitiveResults[0];
        }

        // If still no match, try partial match
        console.log(`🔍 No case-insensitive match for ${machineId}, trying partial match`);
        const partialResults = await this.query(
          `SELECT Machine_Name, Date_Insert_Day, Run_Hours_Day, Down_Hours_Day,
                  Good_QTY_Day, Rejects_QTY_Day, Speed_Day, Availability_Rate_Day,
                  Performance_Rate_Day, Quality_Rate_Day, OEE_Day, Shift,
                  Part_Number, Poid_Unitaire, Cycle_Theorique, Poid_Purge
           FROM machine_daily_table_mould
           WHERE Machine_Name LIKE ?
           ORDER BY Date_Insert_Day DESC
           LIMIT 1`,
          [`%${machineId}%`],
          5000 // 5 second timeout
        );

        if (partialResults.length > 0) {
          console.log(`✅ Found partial match for: ${machineId} -> ${partialResults[0].Machine_Name}`);
          this.logDataSource(machineId, 'MACHINE_DAILY', 'machine_daily_table_mould (partial match)', partialResults.length);
          return partialResults[0];
        }

        // NO MOCK DATA - Fail if no real data found
        const error = new Error(`No machine data found for machine: ${machineId}. Cannot generate report without real data.`);
        error.code = 'NO_MACHINE_DATA';
        console.error(`❌ [CRITICAL] ${error.message}`);
        throw error;
        
      } catch (error) {
        // NO MOCK DATA - Always fail on database errors
        console.error(`❌ [CRITICAL] Database error for machine ${machineId}: ${error.message}`);
        error.code = error.message.includes('timeout') ? 'DATABASE_TIMEOUT' : 'DATABASE_ERROR';
        throw error;
      }
    });
  }

  /**
   * Get session data with aggregation optimization and timeout handling
   */
  async getSessionData(machineId, startTime, endTime) {
    const cacheKey = `sessions_${machineId}_${startTime}_${endTime}`;
    
    return this.getCachedData(cacheKey, async () => {
      try {
        console.log(`🔍 Searching for session data: ${machineId} from ${startTime} to ${endTime}`);
        
        // Use aggregation query for better performance with shorter timeout
        const aggregationResult = await this.query(`
          SELECT 
            COUNT(*) as session_count,
            SUM(COALESCE(Quantite_Bon, 0)) as total_good_qty,
            SUM(COALESCE(Quantite_Rejet, 0)) as total_reject_qty,
            SUM(COALESCE(Stop_Time, 0)) as total_stop_time,
            SUM(COALESCE(Poids_Purge, 0)) as total_purge_weight,
            AVG(CASE WHEN cycle IS NOT NULL AND cycle != '' AND cycle != '0' 
                THEN CAST(cycle AS DECIMAL(10,2)) END) as avg_cycle_time,
            AVG(CASE WHEN TRS IS NOT NULL AND TRS != '' AND TRS != '0' 
                THEN CAST(TRS AS DECIMAL(10,2)) END) as avg_trs,
            GROUP_CONCAT(DISTINCT Regleur_Prenom) as operators
          FROM machine_sessions
          WHERE Machine_Name = ?
          AND session_start BETWEEN ? AND ?
        `, [machineId, startTime, endTime], 15000); // Increased timeout for aggregation

        console.log(`📊 Found ${aggregationResult[0]?.session_count || 0} sessions for machine ${machineId}`);

        // Get detailed session data for the report with optimized query
        const detailedSessions = await this.query(`
          SELECT
            session_start, session_end, Quantite_Bon, Quantite_Rejet,
            cycle, TRS, Stop_Time, Regleur_Prenom, Article, Ordre_Fabrication
          FROM machine_sessions
          WHERE Machine_Name = ?
          AND session_start BETWEEN ? AND ?
          ORDER BY session_start DESC
          LIMIT 50
        `, [machineId, startTime, endTime], 12000); // Increased timeout for detailed sessions

        const aggregated = aggregationResult[0];

        // Log session data source
        this.logDataSource(machineId, 'SESSION_DATA', 'machine_sessions', aggregated.session_count);

        return {
          totalGoodQty: this.parseNumeric(aggregated.total_good_qty),
          totalRejectQty: this.parseNumeric(aggregated.total_reject_qty),
          totalStopTime: this.parseNumeric(aggregated.total_stop_time),
          totalPurgeWeight: this.parseNumeric(aggregated.total_purge_weight),
          avgCycleTime: this.parseNumeric(aggregated.avg_cycle_time),
          avgTRS: this.parseNumeric(aggregated.avg_trs),
          operators: aggregated.operators ? aggregated.operators.split(',').filter(Boolean) : [],
          sessionCount: aggregated.session_count,
          sessions: detailedSessions.map(session => ({
            startTime: session.session_start,
            endTime: session.session_end,
            goodQty: this.parseNumeric(session.Quantite_Bon),
            rejectQty: this.parseNumeric(session.Quantite_Rejet),
            cycle: this.parseNumeric(session.cycle),
            trs: this.parseNumeric(session.TRS),
            stopTime: this.parseNumeric(session.Stop_Time),
            operator: session.Regleur_Prenom || 'N/A',
            article: session.Article || 'N/A',
            orderNumber: session.Ordre_Fabrication || 'N/A'
          }))
        };
      } catch (error) {
        // NO MOCK DATA - Always fail on database errors
        console.error(`❌ [CRITICAL] Session data error for machine ${machineId}: ${error.message}`);
        error.code = error.message.includes('timeout') ? 'SESSION_TIMEOUT' : 'SESSION_ERROR';
        throw error;
      }
    });
  }

  /**
   * Validate data authenticity and integrity
   * Ensures all data comes from legitimate database sources
   */
  validateDataAuthenticity(machineData, sessionData, machineId) {
    console.log(`🔍 [DATA VALIDATION] Starting authenticity check for machine: ${machineId}`);

    // Validate machine data authenticity
    if (!machineData || typeof machineData !== 'object') {
      throw new Error(`Invalid machine data structure for ${machineId}`);
    }

    // Check for required machine data fields
    const requiredMachineFields = ['Machine_Name', 'Date_Insert_Day'];
    for (const field of requiredMachineFields) {
      if (!machineData[field]) {
        throw new Error(`Missing required machine field '${field}' for ${machineId}`);
      }
    }

    // Validate that machine name matches requested machine
    if (machineData.Machine_Name !== machineId) {
      console.warn(`⚠️ [DATA VALIDATION] Machine name mismatch: requested ${machineId}, got ${machineData.Machine_Name}`);
    }

    // Validate session data authenticity
    if (!sessionData || typeof sessionData !== 'object') {
      throw new Error(`Invalid session data structure for ${machineId}`);
    }

    // Check for required session data fields
    const requiredSessionFields = ['totalGoodQty', 'totalRejectQty', 'sessionCount'];
    for (const field of requiredSessionFields) {
      if (sessionData[field] === undefined || sessionData[field] === null) {
        throw new Error(`Missing required session field '${field}' for ${machineId}`);
      }
    }

    // Validate data freshness (not older than 30 days)
    const dataDate = new Date(machineData.Date_Insert_Day);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    if (dataDate < thirtyDaysAgo) {
      console.warn(`⚠️ [DATA VALIDATION] Data is older than 30 days for ${machineId}: ${machineData.Date_Insert_Day}`);
    }

    // Log successful validation
    console.log(`✅ [DATA VALIDATION] Data authenticity verified for machine: ${machineId}`);
    console.log(`✅ [DATA VALIDATION] Machine data date: ${machineData.Date_Insert_Day}`);
    console.log(`✅ [DATA VALIDATION] Session count: ${sessionData.sessionCount}`);
    console.log(`✅ [DATA VALIDATION] Total production: ${sessionData.totalGoodQty + sessionData.totalRejectQty}`);

    return true;
  }

  /**
   * Log data source and authenticity information
   */
  logDataSource(machineId, dataType, source, recordCount) {
    const timestamp = new Date().toISOString();
    console.log(`📊 [DATA SOURCE LOG] ${timestamp}`);
    console.log(`📊 [DATA SOURCE LOG] Machine: ${machineId}`);
    console.log(`📊 [DATA SOURCE LOG] Data Type: ${dataType}`);
    console.log(`📊 [DATA SOURCE LOG] Source: ${source}`);
    console.log(`📊 [DATA SOURCE LOG] Record Count: ${recordCount}`);
    console.log(`📊 [DATA SOURCE LOG] Status: AUTHENTIC_DATABASE_DATA`);
  }

  /**
   * Calculate enhanced performance metrics
   */
  calculatePerformanceMetrics(dailyData, sessionData) {
    const totalProduction = sessionData.totalGoodQty + sessionData.totalRejectQty;
    const qualityRate = totalProduction > 0 
      ? (sessionData.totalGoodQty / totalProduction) * 100 
      : 0;

    // Calculate cycle efficiency
    let cycleEfficiency = null;
    if (dailyData.Cycle_Theorique && sessionData.avgCycleTime > 0) {
      const theoreticalCycle = this.parseNumeric(dailyData.Cycle_Theorique);
      if (theoreticalCycle > 0) {
        cycleEfficiency = (theoreticalCycle / sessionData.avgCycleTime) * 100;
      }
    }

    // Calculate production rate
    const productionRate = sessionData.sessionCount > 0 
      ? sessionData.totalGoodQty / sessionData.sessionCount 
      : 0;

    return {
      totalProduction,
      qualityRate,
      cycleEfficiency,
      productionRate,
      rejectionRate: totalProduction > 0 ? (sessionData.totalRejectQty / totalProduction) * 100 : 0,
      utilization: sessionData.totalStopTime > 0 
        ? ((8 * 60 - sessionData.totalStopTime) / (8 * 60)) * 100 
        : 100
    };
  }

  /**
   * Generate complete report data
   */
  async generateReportData(machineId, date, shift, userId, username) {
    try {
      console.log(`🚀 Generating report data for machine: ${machineId}, shift: ${shift}`);
      
      // Handle 'ALL' machine case - get first available machine
      let actualMachineId = machineId;
      if (machineId === 'ALL' || !machineId) {
        // Get list of available machines with short timeout
        const availableMachines = await this.query(
          `SELECT DISTINCT Machine_Name FROM machine_daily_table_mould 
           LIMIT 5`,
          [],
          8000 // 8 second timeout
        );
        
        if (availableMachines.length === 0) {
          throw new Error('No machines found in the database');
        }
        
        actualMachineId = availableMachines[0].Machine_Name;
        console.log(`Using machine: ${actualMachineId} (selected from available machines)`);
      }
      
      // Clear cache for fresh data
      const machineDataCacheKey = `machine_daily_${actualMachineId}`;
      cache.del(machineDataCacheKey);
      console.log(`🗑️ Cleared cache for machine: ${actualMachineId}`);
      
      // Calculate time window
      const timeWindow = this.calculateShiftTimeWindow(date, shift);
      console.log(`⏰ Time window: ${timeWindow.start} to ${timeWindow.end}`);
      
      // Clear session cache as well
      const sessionCacheKey = `sessions_${actualMachineId}_${timeWindow.start}_${timeWindow.end}`;
      cache.del(sessionCacheKey);
      
      // Fetch data in parallel for better performance with overall timeout
      console.log(`📊 Fetching data for machine: ${actualMachineId}`);
      const [dailyData, sessionData] = await Promise.race([
        Promise.all([
          this.getMachineData(actualMachineId),
          this.getSessionData(actualMachineId, timeWindow.start, timeWindow.end)
        ]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Overall data fetch timeout after 35 seconds')), 35000)
        )
      ]);

      console.log(`✅ Data fetched successfully for machine: ${actualMachineId}`);

      // CRITICAL: Validate data authenticity before proceeding
      this.validateDataAuthenticity(dailyData, sessionData, actualMachineId);

      // Calculate performance metrics
      const performanceMetrics = this.calculatePerformanceMetrics(dailyData, sessionData);

      // Structure report data
      return {
        machine: {
          name: dailyData.Machine_Name,
          partNumber: dailyData.Part_Number || 'N/A',
          poidUnitaire: dailyData.Poid_Unitaire || 'N/A',
          cycleTheorique: dailyData.Cycle_Theorique || 'N/A',
          shift: shift || dailyData.Shift || "Current"
        },
        period: {
          startTime: timeWindow.start,
          endTime: timeWindow.end,
          duration: '8 hours'
        },
        daily: {
          date: dailyData.Date_Insert_Day,
          runHours: this.parseNumeric(dailyData.Run_Hours_Day),
          downHours: this.parseNumeric(dailyData.Down_Hours_Day),
          goodQty: this.parseNumeric(dailyData.Good_QTY_Day),
          rejectsQty: this.parseNumeric(dailyData.Rejects_QTY_Day),
          speed: this.parseNumeric(dailyData.Speed_Day),
          availabilityRate: this.parseNumeric(dailyData.Availability_Rate_Day),
          performanceRate: this.parseNumeric(dailyData.Performance_Rate_Day),
          qualityRate: this.parseNumeric(dailyData.Quality_Rate_Day),
          oee: this.parseNumeric(dailyData.OEE_Day),
          shift: dailyData.Shift,
          poidPurge: this.parseNumeric(dailyData.Poid_Purge)
        },
        session: sessionData,
        performance: performanceMetrics,
        generatedAt: new Date().toISOString(),
        shift: shift || dailyData.Shift || "Current",
        userId,
        username: username || "system"
      };
    } catch (error) {
      console.error('Error generating report data:', error);
      throw error;
    }
  }
}

export default ReportDataService;
