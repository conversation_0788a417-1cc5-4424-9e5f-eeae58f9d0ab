/**
 * API Configuration
 * @module apiConfig
 */
import React from "react" ;
import request from 'superagent';

/**
 * API base URL based on environment
 * @type {string}
 */
export const API_BASE_URL = (() => {
  // Check if we're in a browser environment first
  if (typeof window !== 'undefined') {
    const currentOrigin = window.location.origin;
    console.log('🔌 Current origin:', currentOrigin);

    // If running on ngrok domain, use the same origin (unified architecture)
    if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
      console.log('🔌 Detected ngrok deployment - using same origin:', currentOrigin);
      return currentOrigin;
    }

    // For local development, check environment variable first
    if (import.meta.env.VITE_API_URL) {
      console.log('🔌 Using VITE_API_URL for local development:', import.meta.env.VITE_API_URL);
      return import.meta.env.VITE_API_URL;
    }

    // Fallback to current origin for local development
    console.log('🔌 Using current origin for local development:', currentOrigin);
    return currentOrigin;
  }

  // Fallback for server-side rendering
  return "http://localhost:5000";
})();

console.log('🔗 API_BASE_URL configured as:', API_BASE_URL);
console.log('🌍 Environment variables:', {
  NODE_ENV: process.env.NODE_ENV,
  VITE_API_URL: import.meta.env.VITE_API_URL,
  VITE_WS_URL: import.meta.env.VITE_WS_URL,
  VITE_NGROK_ENABLED: import.meta.env.VITE_NGROK_ENABLED
});
console.log('🔍 Domain detection:', {
  currentOrigin: typeof window !== 'undefined' ? window.location.origin : 'N/A',
  isNgrokDomain: typeof window !== 'undefined' ? (window.location.origin.includes('ngrok-free.app') || window.location.origin.includes('ngrok.io')) : false,
  detectedEnvironment: typeof window !== 'undefined' && (window.location.origin.includes('ngrok-free.app') || window.location.origin.includes('ngrok.io')) ? 'ngrok' : 'Local'
});

/**
 * API endpoints
 * @type {Object}
 */
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register',
    VERIFY_TOKEN: '/api/auth/verify',
    RESET_PASSWORD: '/api/auth/reset-password',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    CHANGE_PASSWORD: '/api/auth/change-password',
  },

  // User endpoints
  USER: {
    PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
    ALL: '/api/users',
    BY_ID: (id) => `/api/users/${id}`,
  },

  // Production endpoints
  PRODUCTION: {
    DAILY: '/api/production/daily',
    WEEKLY: '/api/production/weekly',
    MONTHLY: '/api/production/monthly',
    BY_MACHINE: (id) => `/api/production/machine/${id}`,
    STATS: '/api/production/stats',
  },

  // Machine endpoints
  MACHINE: {
    ALL: '/api/machines',
    BY_ID: (id) => `/api/machines/${id}`,
    STOPS: '/api/machines/stops',
    PERFORMANCE: '/api/machines/performance',
  },

  // Dashboard endpoints
  DASHBOARD: {
    SUMMARY: '/api/dashboard/summary',
    PERFORMANCE: '/api/dashboard/performance',
    STOPS: '/api/dashboard/stops',
  },



  // Notification endpoints
  NOTIFICATIONS: {
    ALL: '/api/notifications',
    UNREAD: '/api/notifications/unread',
    MARK_READ: (id) => `/api/notifications/${id}/read`,
    MARK_ALL_READ: '/api/notifications/read-all',
  },

  // Report endpoints
  REPORTS: {
    GENERATE: '/api/reports/generate',
    DOWNLOAD: (id) => `/api/reports/${id}/download`,
    LIST: '/api/reports',
  },
};

/**
 * Default request headers
 * @type {Object}
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

/**
 * Request timeout in milliseconds
 * @type {number}
 */
export const REQUEST_TIMEOUT = 30000; // 30 seconds

/**
 * Get optimal WebSocket URL based on current environment
 * For unified container: uses same origin to avoid CORS issues
 * For external access: uses ngrok tunnel
 */
const getOptimalWebSocketUrl = () => {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    const currentOrigin = window.location.origin;

    // If running on ngrok domain, use the same origin (unified architecture)
    if (currentOrigin.includes('ngrok-free.app') || currentOrigin.includes('ngrok.io')) {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}`;
      console.log('🔌 WebSocket detected ngrok deployment - using same origin:', wsUrl);
      return wsUrl;
    }

    // For local development, check environment variable first
    if (import.meta.env.VITE_WS_URL) {
      console.log('🔌 WebSocket using VITE_WS_URL for local development:', import.meta.env.VITE_WS_URL);
      return import.meta.env.VITE_WS_URL;
    }

    // Fallback to current origin for local development
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}`;
    console.log('🔌 WebSocket using current origin for local development:', wsUrl);
    return wsUrl;
  }

  // Fallback for server-side rendering
  return "ws://localhost:5000";
};

/**
 * WebSocket URL
 * @type {string}
 */
export const WEBSOCKET_URL = getOptimalWebSocketUrl();

console.log('🔌 WEBSOCKET_URL configured as:', WEBSOCKET_URL);

/**
 * Global SuperAgent configuration object
 * @type {Object}
 */
export const superagentConfig = {
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  retries: 2,
  headers: DEFAULT_HEADERS,
};

/**
 * Creates a configured SuperAgent request with retry logic and global headers
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {boolean} isAuthenticated - Whether user is authenticated
 * @returns {SuperAgent} Configured SuperAgent request
 */
export const createRequest = (method, url, isAuthenticated = false) => {
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;
  
  let req = request[method.toLowerCase()](fullUrl)
    .timeout(REQUEST_TIMEOUT)
    .retry(2);
  
  // Set default headers
  Object.entries(DEFAULT_HEADERS).forEach(([key, value]) => {
    req = req.set(key, value);
  });
  
  // 🔒 SECURITY: Use HTTP-only cookies for authentication
  if (isAuthenticated) {
    req = req.withCredentials(); // ✅ FIXED: Correct SuperAgent syntax
    // Note: No Authorization header needed - backend uses HTTP-only cookies
  }
  
  return req;
};

/**
 * Configures SuperAgent with authentication headers and global settings
 * @param {boolean} isAuthenticated - Whether the user is authenticated
 */
export const configureSuperAgent = (isAuthenticated) => {
  // Store authentication state for createRequest function
  configureSuperAgent.isAuthenticated = isAuthenticated;

  console.log(`SuperAgent configured with baseURL: ${API_BASE_URL}, timeout: ${REQUEST_TIMEOUT}ms`);
};

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  DEFAULT_HEADERS,
  REQUEST_TIMEOUT,
  WEBSOCKET_URL,
  configureSuperAgent,
  createRequest,
  superagentConfig,
};