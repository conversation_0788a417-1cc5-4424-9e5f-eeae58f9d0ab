import React, { useMemo, useState, useCallback } from 'react';
import { Card, Button, Space, Tag, Alert, Tooltip, Switch } from 'antd';
import { 
  ExpandAltOutlined, 
  DownloadOutlined, 
  WarningOutlined,
  ThunderboltOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import PropTypes from 'prop-types';

/**
 * Optimized Chart Component with performance enhancements
 * - Data sampling for large datasets
 * - Conditional rendering based on data size
 * - Performance monitoring
 * - Export functionality
 * - Expansion capabilities
 */
const OptimizedChart = ({
  data = [],
  title,
  children,
  maxDataPoints = 200,
  enableSampling = true,
  enableExpansion = true,
  enableExport = true,
  performanceMode = false,
  loading = false,
  height = 300,
  onExpand,
  onExport,
  extra,
  ...cardProps
}) => {
  const [samplingEnabled, setSamplingEnabled] = useState(enableSampling);
  const [exportLoading, setExportLoading] = useState(false);

  // Data sampling logic for performance
  const optimizedData = useMemo(() => {
    if (!samplingEnabled || data.length <= maxDataPoints) {
      return data;
    }

    // Sample data points evenly across the dataset
    const step = Math.ceil(data.length / maxDataPoints);
    const sampledData = [];
    
    for (let i = 0; i < data.length; i += step) {
      sampledData.push(data[i]);
    }

    // Always include the last data point
    if (data.length > 0 && sampledData[sampledData.length - 1] !== data[data.length - 1]) {
      sampledData.push(data[data.length - 1]);
    }

    return sampledData;
  }, [data, samplingEnabled, maxDataPoints]);

  // Performance metrics
  const performanceMetrics = useMemo(() => {
    const originalCount = data.length;
    const optimizedCount = optimizedData.length;
    const reductionPercentage = originalCount > 0 ? 
      ((originalCount - optimizedCount) / originalCount * 100).toFixed(1) : 0;
    
    return {
      originalCount,
      optimizedCount,
      reductionPercentage,
      isLargeDataset: originalCount > maxDataPoints,
      estimatedRenderTime: optimizedCount * 0.5 // Rough estimate: 0.5ms per data point
    };
  }, [data.length, optimizedData.length, maxDataPoints]);

  // Handle export functionality
  const handleExport = useCallback(async () => {
    if (!onExport) return;
    
    setExportLoading(true);
    try {
      await onExport({
        originalData: data,
        optimizedData,
        title,
        performanceMetrics
      });
    } catch (error) {
      console.error('Chart export failed:', error);
    } finally {
      setExportLoading(false);
    }
  }, [onExport, data, optimizedData, title, performanceMetrics]);

  // Handle expansion
  const handleExpand = useCallback(() => {
    if (onExpand) {
      onExpand({
        data: optimizedData,
        title,
        performanceMetrics
      });
    }
  }, [onExpand, optimizedData, title, performanceMetrics]);

  // Performance warning component
  const PerformanceWarning = () => {
    if (!performanceMetrics.isLargeDataset) return null;

    return (
      <Alert
        message={`Dataset volumineux: ${performanceMetrics.originalCount} points de données`}
        description={
          samplingEnabled 
            ? `Échantillonnage activé: ${performanceMetrics.optimizedCount} points affichés (réduction de ${performanceMetrics.reductionPercentage}%)`
            : `Tous les points sont affichés. Activez l'échantillonnage pour améliorer les performances.`
        }
        type={samplingEnabled ? "info" : "warning"}
        showIcon
        style={{ marginBottom: 16 }}
        action={
          <Switch
            checked={samplingEnabled}
            onChange={setSamplingEnabled}
            checkedChildren="Échantillonnage ON"
            unCheckedChildren="Échantillonnage OFF"
            size="small"
          />
        }
      />
    );
  };

  // Card title with performance indicators
  const cardTitle = useMemo(() => {
    if (!title) return null;

    return (
      <Space>
        <span>{title}</span>
        {performanceMetrics.isLargeDataset && (
          <Tag color={samplingEnabled ? 'green' : 'orange'} icon={<ThunderboltOutlined />}>
            {performanceMetrics.optimizedCount} points
          </Tag>
        )}
        {performanceMode && (
          <Tooltip title={`Temps de rendu estimé: ${performanceMetrics.estimatedRenderTime.toFixed(1)}ms`}>
            <Tag color="blue" icon={<LineChartOutlined />}>
              Performance
            </Tag>
          </Tooltip>
        )}
      </Space>
    );
  }, [title, performanceMetrics, samplingEnabled, performanceMode]);

  // Card extra actions
  const cardExtra = useMemo(() => (
    <Space>
      {extra}
      {enableExpansion && (
        <Tooltip title="Agrandir le graphique">
          <Button
            type="text"
            icon={<ExpandAltOutlined />}
            onClick={handleExpand}
            size="small"
          />
        </Tooltip>
      )}
      {enableExport && (
        <Tooltip title="Exporter les données">
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={handleExport}
            loading={exportLoading}
            size="small"
            disabled={data.length === 0}
          />
        </Tooltip>
      )}
    </Space>
  ), [
    extra,
    enableExpansion,
    enableExport,
    handleExpand,
    handleExport,
    exportLoading,
    data.length
  ]);

  // Conditional rendering based on data size and performance
  const shouldRenderChart = useMemo(() => {
    // Don't render if no data
    if (data.length === 0) return false;
    
    // Always render if dataset is small
    if (!performanceMetrics.isLargeDataset) return true;
    
    // For large datasets, only render if sampling is enabled or user explicitly wants to see all data
    return samplingEnabled || !performanceMode;
  }, [data.length, performanceMetrics.isLargeDataset, samplingEnabled, performanceMode]);

  // Clone children with optimized data
  const optimizedChildren = useMemo(() => {
    if (!children || !shouldRenderChart) return null;

    return React.cloneElement(children, {
      data: optimizedData,
      height: height
    });
  }, [children, optimizedData, shouldRenderChart, height]);

  return (
    <Card
      title={cardTitle}
      extra={cardExtra}
      loading={loading}
      {...cardProps}
    >
      <PerformanceWarning />
      
      {shouldRenderChart ? (
        optimizedChildren
      ) : (
        <div style={{ 
          height: height, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          background: '#fafafa',
          border: '1px dashed #d9d9d9',
          borderRadius: 6
        }}>
          <Space direction="vertical" align="center">
            <WarningOutlined style={{ fontSize: 24, color: '#faad14' }} />
            <div>Dataset trop volumineux ({performanceMetrics.originalCount} points)</div>
            <Button 
              type="primary" 
              onClick={() => setSamplingEnabled(true)}
              icon={<ThunderboltOutlined />}
            >
              Activer l'échantillonnage
            </Button>
          </Space>
        </div>
      )}
    </Card>
  );
};

OptimizedChart.propTypes = {
  data: PropTypes.array,
  title: PropTypes.string,
  children: PropTypes.node,
  maxDataPoints: PropTypes.number,
  enableSampling: PropTypes.bool,
  enableExpansion: PropTypes.bool,
  enableExport: PropTypes.bool,
  performanceMode: PropTypes.bool,
  loading: PropTypes.bool,
  height: PropTypes.number,
  onExpand: PropTypes.func,
  onExport: PropTypes.func,
  extra: PropTypes.node
};

export default OptimizedChart;
