import React from 'react';
import { Card, Row, Col, Statistic, Progress, Space, Badge, Tooltip } from 'antd';
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  TrophyOutlined,
  RobotOutlined,
  <PERSON>boltOutlined,
  <PERSON><PERSON>Outlined,
  CheckCircleOutlined,
  <PERSON><PERSON>ircleOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined
} from '@ant-design/icons';

const AnalyticsStatsCards = ({ loading, filters }) => {
  // Mock data (will be replaced with real data later)
  const statsData = [
    {
      title: "AI Performance Score",
      value: 94.2,
      suffix: "%",
      precision: 1,
      trend: "up",
      change: "+5.2%",
      color: "#52c41a",
      icon: <RobotOutlined />,
      description: "Overall AI system efficiency",
      gradient: "linear-gradient(135deg, #52c41a 0%, #73d13d 100%)"
    },
    {
      title: "Production Efficiency",
      value: 87.5,
      suffix: "%",
      precision: 1,
      trend: "up",
      change: "+3.1%",
      color: "#1890ff",
      icon: <BarChartOutlined />,
      description: "Real-time production optimization",
      gradient: "linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)"
    },
    {
      title: "Quality Index",
      value: 98.7,
      suffix: "%",
      precision: 1,
      trend: "up",
      change: "+1.8%",
      color: "#722ed1",
      icon: <TrophyOutlined />,
      description: "AI-powered quality assurance",
      gradient: "linear-gradient(135deg, #722ed1 0%, #9254de 100%)"
    },
    {
      title: "Predicted Savings",
      value: 15847,
      prefix: "$",
      precision: 0,
      trend: "up",
      change: "+12.4%",
      color: "#fa8c16",
      icon: <DollarOutlined />,
      description: "Monthly cost optimization",
      gradient: "linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)"
    },
    {
      title: "Real-time Alerts",
      value: 23,
      precision: 0,
      trend: "down",
      change: "-15%",
      color: "#f5222d",
      icon: <AlertOutlined />,
      description: "Active system notifications",
      gradient: "linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)"
    },
    {
      title: "Operator Efficiency",
      value: 91.3,
      suffix: "%",
      precision: 1,
      trend: "up",
      change: "+4.7%",
      color: "#eb2f96",
      icon: <ThunderboltOutlined />,
      description: "AI-enhanced performance",
      gradient: "linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)"
    }
  ];

  const aiInsights = [
    {
      title: "ML Model Accuracy",
      value: 96.8,
      target: 95,
      color: "#52c41a"
    },
    {
      title: "Prediction Confidence",
      value: 92.1,
      target: 90,
      color: "#1890ff"
    },
    {
      title: "Data Quality Score",
      value: 89.4,
      target: 85,
      color: "#722ed1"
    },
    {
      title: "System Uptime",
      value: 99.7,
      target: 99,
      color: "#fa8c16"
    }
  ];

  return (
    <>
      {/* Main Stats Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {statsData.map((stat, index) => (
          <Col xs={24} sm={12} md={8} lg={4} key={index}>
            <Card
              loading={loading}
              style={{
                borderRadius: '16px',
                border: 'none',
                background: stat.gradient,
                color: 'white',
                boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
                position: 'relative',
                overflow: 'hidden'
              }}
              bodyStyle={{ 
                padding: '20px',
                position: 'relative',
                zIndex: 2
              }}
            >
              {/* Background Pattern */}
              <div style={{
                position: 'absolute',
                top: '-20px',
                right: '-20px',
                width: '80px',
                height: '80px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '50%',
                zIndex: 1
              }} />
              
              <div style={{
                position: 'absolute',
                bottom: '-30px',
                left: '-30px',
                width: '100px',
                height: '100px',
                background: 'rgba(255,255,255,0.05)',
                borderRadius: '50%',
                zIndex: 1
              }} />

              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div style={{
                    background: 'rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    padding: '6px',
                    fontSize: '16px'
                  }}>
                    {stat.icon}
                  </div>
                  
                  <Badge 
                    count={
                      <Space size={2}>
                        {stat.trend === 'up' ? 
                          <ArrowUpOutlined style={{ fontSize: '10px' }} /> : 
                          <ArrowDownOutlined style={{ fontSize: '10px' }} />
                        }
                        <span style={{ fontSize: '10px' }}>{stat.change}</span>
                      </Space>
                    }
                    style={{
                      background: stat.trend === 'up' ? 'rgba(82, 196, 26, 0.9)' : 'rgba(245, 34, 45, 0.9)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '10px',
                      fontSize: '10px'
                    }}
                  />
                </div>
                
                <Statistic
                  value={stat.value}
                  precision={stat.precision}
                  prefix={stat.prefix}
                  suffix={stat.suffix}
                  valueStyle={{ 
                    color: 'white', 
                    fontSize: '24px', 
                    fontWeight: '700',
                    lineHeight: '1.2'
                  }}
                />
                
                <div>
                  <div style={{ 
                    fontSize: '12px', 
                    fontWeight: '600',
                    marginBottom: '2px'
                  }}>
                    {stat.title}
                  </div>
                  <div style={{ 
                    fontSize: '10px', 
                    opacity: 0.8,
                    fontWeight: '400'
                  }}>
                    {stat.description}
                  </div>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>

      {/* AI Insights Progress Cards */}
      <Card
        title={
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <span>AI System Health Metrics</span>
            <Badge count="Live" style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        style={{
          marginBottom: '24px',
          borderRadius: '16px',
          border: 'none',
          boxShadow: '0 8px 24px rgba(0,0,0,0.06)'
        }}
        bodyStyle={{ padding: '20px' }}
      >
        <Row gutter={[24, 16]}>
          {aiInsights.map((insight, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <div style={{
                background: 'linear-gradient(135deg, #f8faff 0%, #e6f7ff 100%)',
                borderRadius: '12px',
                padding: '16px',
                border: `2px solid ${insight.color}20`
              }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  marginBottom: '12px'
                }}>
                  <span style={{ 
                    fontSize: '13px', 
                    fontWeight: '600',
                    color: '#595959'
                  }}>
                    {insight.title}
                  </span>
                  
                  <Tooltip title={`Target: ${insight.target}%`}>
                    <CheckCircleOutlined 
                      style={{ 
                        color: insight.value >= insight.target ? '#52c41a' : '#faad14',
                        fontSize: '14px'
                      }} 
                    />
                  </Tooltip>
                </div>
                
                <div style={{ marginBottom: '8px' }}>
                  <span style={{ 
                    fontSize: '20px', 
                    fontWeight: '700',
                    color: insight.color
                  }}>
                    {insight.value}%
                  </span>
                </div>
                
                <Progress
                  percent={insight.value}
                  strokeColor={{
                    '0%': insight.color,
                    '100%': insight.color + '80'
                  }}
                  trailColor="#f0f0f0"
                  strokeWidth={6}
                  showInfo={false}
                  style={{ marginBottom: '4px' }}
                />
                
                <div style={{
                  fontSize: '11px',
                  color: '#8c8c8c',
                  textAlign: 'center'
                }}>
                  Target: {insight.target}%
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      {/* Quick Action Cards */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card
            style={{
              borderRadius: '12px',
              border: 'none',
              background: 'linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)',
              boxShadow: '0 4px 16px rgba(250, 140, 22, 0.1)'
            }}
            bodyStyle={{ padding: '16px', textAlign: 'center' }}
          >
            <Space direction="vertical" size="small">
              <ClockCircleOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />
              <div style={{ fontWeight: '600', color: '#fa8c16' }}>
                Real-time Monitoring
              </div>
              <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                24/7 AI surveillance active
              </div>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} sm={8}>
          <Card
            style={{
              borderRadius: '12px',
              border: 'none',
              background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
              boxShadow: '0 4px 16px rgba(82, 196, 26, 0.1)'
            }}
            bodyStyle={{ padding: '16px', textAlign: 'center' }}
          >
            <Space direction="vertical" size="small">
              <LineChartOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
              <div style={{ fontWeight: '600', color: '#52c41a' }}>
                Predictive Analytics
              </div>
              <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                ML models optimizing performance
              </div>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} sm={8}>
          <Card
            style={{
              borderRadius: '12px',
              border: 'none',
              background: 'linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)',
              boxShadow: '0 4px 16px rgba(114, 46, 209, 0.1)'
            }}
            bodyStyle={{ padding: '16px', textAlign: 'center' }}
          >
            <Space direction="vertical" size="small">
              <PieChartOutlined style={{ fontSize: '24px', color: '#722ed1' }} />
              <div style={{ fontWeight: '600', color: '#722ed1' }}>
                Smart Insights
              </div>
              <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                AI-generated recommendations
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default AnalyticsStatsCards;
