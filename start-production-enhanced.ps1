#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Enhanced Docker Compose Launch Script for LOCQL Application

.DESCRIPTION
    This script provides comprehensive Docker Compose orchestration for the LOCQL application
    with ngrok tunnel integration, health checks, and advanced monitoring capabilities.
    
    Features:
    - Multiple deployment modes (unified, ngrok, local, hybrid)
    - Comprehensive pre-flight validation checks
    - Health check monitoring with retry logic
    - Service readiness validation
    - Enhanced logging and progress tracking
    - Automatic cleanup and rollback on failure
    - Real-time service status monitoring

.PARAMETER Mode
    Deployment mode: unified, ngrok, local, hybrid, or local-ngrok

.PARAMETER Build
    Force rebuild of containers before starting

.PARAMETER Logs
    Show container logs after successful startup

.PARAMETER HealthCheck
    Enable comprehensive health check validation

.PARAMETER Timeout
    Maximum timeout in seconds for service startup (default: 300)

.PARAMETER Force
    Force start even if validation checks fail

.PARAMETER Verbose
    Enable verbose logging output

.PARAMETER Help
    Show detailed help information

.EXAMPLE
    .\start-production-enhanced.ps1 -Mode ngrok
    Starts the application in ngrok tunnel mode
    
.EXAMPLE
    .\start-production-enhanced.ps1 -Mode unified -Build -HealthCheck
    Rebuilds and starts with comprehensive health checks

.EXAMPLE
    .\start-production-enhanced.ps1 -Mode hybrid -Logs -Verbose
    Starts in hybrid mode with logs and verbose output

.NOTES
    Author: LOCQL Development Team
    Requires: Docker, PowerShell 5.1+, ngrok (for tunnel modes)
    
#>

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Deployment mode")]
    [ValidateSet("unified", "ngrok", "local", "hybrid", "local-ngrok")]
    [string]$Mode = "unified",
    
    [Parameter(HelpMessage = "Force rebuild containers")]
    [switch]$Build,
    
    [Parameter(HelpMessage = "Show logs after startup")]
    [switch]$Logs,
    
    [Parameter(HelpMessage = "Enable health check validation")]
    [switch]$HealthCheck,
    
    [Parameter(HelpMessage = "Service startup timeout in seconds")]
    [ValidateRange(30, 600)]
    [int]$Timeout = 300,
    
    [Parameter(HelpMessage = "Force start despite validation failures")]
    [switch]$Force,
    
    [Parameter(HelpMessage = "Show detailed help information")]
    [switch]$Help
)

# =============================================================================
# CONFIGURATION & GLOBAL VARIABLES
# =============================================================================

# Script configuration
$Script:Config = @{
    NgrokTunnelUrl = "https://eternal-friendly-chigger.ngrok-free.app"
    LocalUrl = "http://localhost:5000"
    ComposeFiles = @{
        unified = "docker-compose.production.yml"
        ngrok = "docker-compose.hub-ngrok.yml"
        local = "docker-compose.local.yml"
        hybrid = "docker-compose.hybrid.yml"
        "local-ngrok" = "docker-compose.local-ngrok.yml"
    }
    HealthEndpoints = @{
        api = "/api/health/ping"
        frontend = "/"
    }
    MaxRetries = 5
    RetryDelay = 10
}

# Script state tracking
$Script:State = @{
    StartTime = Get-Date
    ServicesStarted = @()
    HealthChecksCompleted = @()
    ContainersRunning = @()
    ErrorsEncountered = @()
}

# =============================================================================
# ENHANCED LOGGING & OUTPUT FUNCTIONS
# =============================================================================

function Write-LogMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Message,
        
        [Parameter()]
        [ValidateSet('Success', 'Error', 'Warning', 'Info', 'Step', 'Debug')]
        [string]$Level = 'Info',
        
        [Parameter()]
        [switch]$NoNewline
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $prefix = switch ($Level) {
        'Success' { "✅"; $color = 'Green' }
        'Error'   { "❌"; $color = 'Red' }
        'Warning' { "⚠️ "; $color = 'Yellow' }
        'Info'    { "ℹ️ "; $color = 'Cyan' }
        'Step'    { "🔧"; $color = 'Blue' }
        'Debug'   { "🐛"; $color = 'Magenta' }
    }
    
    $formattedMessage = "[$timestamp] $prefix $Message"
    
    if ($NoNewline) {
        Write-Host $formattedMessage -ForegroundColor $color -NoNewline
    } else {
        Write-Host $formattedMessage -ForegroundColor $color
    }
    
    # Also write to verbose stream for detailed logging
    Write-Verbose $formattedMessage
}

function Write-Progress-Enhanced {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Activity,
        
        [Parameter(Mandatory)]
        [string]$Status,
        
        [Parameter()]
        [int]$PercentComplete = -1,
        
        [Parameter()]
        [int]$Id = 1
    )
    
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete -Id $Id
    Write-LogMessage -Message "$Activity - $Status" -Level 'Step'
}

# =============================================================================
# PARAMETER VALIDATION & INITIALIZATION
# =============================================================================

function Initialize-ScriptParameters {
    [CmdletBinding()]
    param()
    
    # Validate compose file exists for selected mode
    $composeFile = $Script:Config.ComposeFiles[$Mode]
    if (-not $composeFile) {
        throw "Invalid mode specified: $Mode"
    }
    
    if (-not (Test-Path $composeFile)) {
        throw "Compose file not found: $composeFile"
    }
    
    Write-LogMessage -Message "Initialized for mode: $Mode using $composeFile" -Level 'Success'
}

function Show-Help {
    $helpText = @"
🚀 LOCQL Docker Compose Launch Script - Enhanced Edition

SYNOPSIS:
    Enhanced Docker Compose orchestration for LOCQL application deployment

USAGE:
    .\start-production-enhanced.ps1 [OPTIONS]

PARAMETERS:
    -Mode <string>        Deployment mode (unified|ngrok|local|hybrid|local-ngrok)
    -Build               Force rebuild containers before starting
    -Logs                Show container logs after successful startup
    -HealthCheck         Enable comprehensive health check validation
    -Timeout <int>       Service startup timeout in seconds (30-600)
    -Force               Force start despite validation failures
    -Verbose             Enable detailed verbose logging
    -Help                Show this comprehensive help information

DEPLOYMENT MODES:
    unified      Single container with all services (recommended)
    ngrok        Unified container with ngrok tunnel for external access
    local        Development mode with separate frontend/backend containers
    hybrid       Mixed mode supporting both local and external access
    local-ngrok  Local development with ngrok tunnel (reserved domain)

EXAMPLES:
    # Start with ngrok tunnel (production)
    .\start-production-enhanced.ps1 -Mode ngrok -HealthCheck
    
    # Development with rebuild and logs
    .\start-production-enhanced.ps1 -Mode local -Build -Logs
    
    # Production with comprehensive monitoring
    .\start-production-enhanced.ps1 -Mode unified -HealthCheck -Verbose

FEATURES:
    ✅ Comprehensive pre-flight validation checks
    ✅ Service health monitoring with retry logic
    ✅ Real-time container status tracking
    ✅ Automatic cleanup on failure
    ✅ Enhanced error reporting and troubleshooting
    ✅ Progress tracking with detailed logging

REQUIREMENTS:
    - Docker Desktop installed and running
    - PowerShell 5.1+ or PowerShell Core
    - ngrok installed (for tunnel modes)
    - Required compose files present

ARCHITECTURE:
    This script supports the LOCQL application's ngrok tunnel architecture:
    - Tunnel URL: https://eternal-friendly-chigger.ngrok-free.app
    - Environment: NGROK_ENABLED=true
    - Unified container deployment model

"@
    Write-Host $helpText -ForegroundColor White
}

# =============================================================================
# ENHANCED VALIDATION FUNCTIONS
# =============================================================================

function Test-Prerequisites {
    [CmdletBinding()]
    param()

    Write-Progress-Enhanced -Activity "Pre-flight Validation" -Status "Checking prerequisites" -PercentComplete 10

    $validationResults = @()

    # Test Docker installation
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-LogMessage -Message "Docker is installed: $dockerVersion" -Level 'Success'
            $validationResults += $true
        } else {
            throw "Docker command failed"
        }
    }
    catch {
        Write-LogMessage -Message "Docker is not installed or not accessible" -Level 'Error'
        $validationResults += $false
    }

    # Test Docker daemon
    try {
        docker info > $null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-LogMessage -Message "Docker daemon is running" -Level 'Success'
            $validationResults += $true
        } else {
            throw "Docker daemon not accessible"
        }
    }
    catch {
        Write-LogMessage -Message "Docker daemon is not running" -Level 'Error'
        Write-LogMessage -Message "Please start Docker Desktop" -Level 'Info'
        $validationResults += $false
    }

    # Test ngrok availability (for tunnel modes) - Optional check
    if ($Mode -in @('ngrok', 'hybrid')) {
        try {
            $ngrokVersion = ngrok version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-LogMessage -Message "ngrok is available: $ngrokVersion" -Level 'Success'
                $validationResults += $true
            } else {
                throw "ngrok command failed"
            }
        }
        catch {
            Write-LogMessage -Message "ngrok is not installed or not accessible" -Level 'Warning'
            Write-LogMessage -Message "Download from: https://ngrok.com/download" -Level 'Info'
            Write-LogMessage -Message "Note: ngrok tunnel functionality will be limited without ngrok installed" -Level 'Info'

            # Don't fail validation for missing ngrok - just warn
            if (-not $Force) {
                $response = Read-Host "Continue without ngrok? (y/N)"
                $validationResults += ($response -eq 'y' -or $response -eq 'Y')
            } else {
                Write-LogMessage -Message "Continuing without ngrok (Force mode)" -Level 'Warning'
                $validationResults += $true
            }
        }
    }

    # Test compose file existence
    $composeFile = $Script:Config.ComposeFiles[$Mode]
    if (Test-Path $composeFile) {
        Write-LogMessage -Message "Compose file found: $composeFile" -Level 'Success'
        $validationResults += $true
    } else {
        Write-LogMessage -Message "Compose file not found: $composeFile" -Level 'Error'
        $validationResults += $false
    }

    $allPassed = $validationResults -notcontains $false

    if ($allPassed) {
        Write-LogMessage -Message "All prerequisite checks passed" -Level 'Success'
    } else {
        Write-LogMessage -Message "Some prerequisite checks failed" -Level 'Warning'
    }

    return $allPassed
}

function Stop-ExistingContainers {
    [CmdletBinding()]
    param()

    Write-Progress-Enhanced -Activity "Cleanup Process" -Status "Stopping existing containers" -PercentComplete 20

    # Define containers to stop
    $containers = @(
        "locql-app-simple", "locql-app-production", "locql-app-unified",
        "locql-backend", "locql-frontend", "locql-ngrok", "locql-app"
    )

    $stoppedCount = 0

    foreach ($container in $containers) {
        try {
            $containerExists = docker ps -a --format "{{.Names}}" | Select-String -Pattern "^$container$"

            if ($containerExists) {
                Write-LogMessage -Message "Stopping container: $container" -Level 'Info'
                docker stop $container 2>$null | Out-Null
                docker rm $container 2>$null | Out-Null
                $stoppedCount++
            }
        }
        catch {
            # Container doesn't exist or already stopped, continue
        }
    }

    # Clean up docker-compose services
    $composeFiles = $Script:Config.ComposeFiles.Values
    foreach ($composeFile in $composeFiles) {
        if (Test-Path $composeFile) {
            try {
                Write-LogMessage -Message "Cleaning up compose services from: $composeFile" -Level 'Debug'
                docker-compose -f $composeFile down 2>$null | Out-Null
            }
            catch {
                # Ignore cleanup errors
            }
        }
    }

    if ($stoppedCount -gt 0) {
        Write-LogMessage -Message "Stopped and removed $stoppedCount existing containers" -Level 'Success'
    } else {
        Write-LogMessage -Message "No existing containers found to clean up" -Level 'Info'
    }
}

function Start-DockerServices {
    [CmdletBinding()]
    param()

    $composeFile = $Script:Config.ComposeFiles[$Mode]

    Write-Progress-Enhanced -Activity "Service Startup" -Status "Starting Docker services" -PercentComplete 40

    try {
        Write-LogMessage -Message "Starting services using: $composeFile" -Level 'Step'

        # Prepare docker-compose command
        $composeArgs = @("-f", $composeFile)

        # Add profile for ngrok modes to start tunnel service
        if ($Mode -in @('ngrok', 'local-ngrok')) {
            $composeArgs += @("--profile", "ngrok")
            Write-LogMessage -Message "Using ngrok profile to start tunnel service" -Level 'Info'
        }

        if ($Build) {
            $composeArgs += @("up", "--build", "-d")
            Write-LogMessage -Message "Building and starting services (this may take several minutes)" -Level 'Info'
        } else {
            $composeArgs += @("up", "-d")
            Write-LogMessage -Message "Starting services" -Level 'Info'
        }

        # Execute docker-compose command
        Write-LogMessage -Message "Executing: docker-compose $($composeArgs -join ' ')" -Level 'Debug'

        $startProcess = Start-Process -FilePath "docker-compose" -ArgumentList $composeArgs -NoNewWindow -PassThru -Wait

        if ($startProcess.ExitCode -eq 0) {
            Write-LogMessage -Message "Docker services started successfully" -Level 'Success'

            # Get list of started containers
            $runningContainers = docker-compose -f $composeFile ps --services 2>$null
            if ($runningContainers) {
                $Script:State.ServicesStarted = $runningContainers
                Write-LogMessage -Message "Started services: $($runningContainers -join ', ')" -Level 'Info'
            }

            return $true
        } else {
            throw "docker-compose failed with exit code: $($startProcess.ExitCode)"
        }
    }
    catch {
        Write-LogMessage -Message "Failed to start Docker services: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}

# =============================================================================
# ENHANCED HEALTH CHECK & MONITORING FUNCTIONS
# =============================================================================

function Test-ServiceHealth {
    [CmdletBinding()]
    param()

    if (-not $HealthCheck) {
        Write-LogMessage -Message "Skipping health checks (not requested)" -Level 'Info'
        return $true
    }

    Write-Progress-Enhanced -Activity "Health Check Process" -Status "Validating service health" -PercentComplete 60

    $healthResults = @()
    $maxAttempts = $Script:Config.MaxRetries
    $retryDelay = $Script:Config.RetryDelay

    # Test API health endpoint
    Write-LogMessage -Message "Testing API health endpoint" -Level 'Step'

    for ($attempt = 1; $attempt -le $maxAttempts; $attempt++) {
        try {
            $apiUrl = "$($Script:Config.LocalUrl)$($Script:Config.HealthEndpoints.api)"
            Write-LogMessage -Message "Attempt $attempt/$maxAttempts - Testing: $apiUrl" -Level 'Debug'

            $response = Invoke-WebRequest -Uri $apiUrl -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop

            if ($response.StatusCode -eq 200) {
                Write-LogMessage -Message "API health check passed" -Level 'Success'
                $healthResults += $true
                $Script:State.HealthChecksCompleted += "API"
                break
            }
        }
        catch {
            Write-LogMessage -Message "API health check attempt $attempt failed: $($_.Exception.Message)" -Level 'Warning'

            if ($attempt -eq $maxAttempts) {
                Write-LogMessage -Message "API health check failed after $maxAttempts attempts" -Level 'Error'
                $healthResults += $false
            } else {
                Write-LogMessage -Message "Retrying in $retryDelay seconds..." -Level 'Info'
                Start-Sleep -Seconds $retryDelay
            }
        }
    }

    # Test frontend accessibility (for unified/ngrok modes)
    if ($Mode -in @('unified', 'ngrok', 'local-ngrok')) {
        Write-LogMessage -Message "Testing frontend accessibility" -Level 'Step'

        for ($attempt = 1; $attempt -le $maxAttempts; $attempt++) {
            try {
                $frontendUrl = if ($Mode -in @('ngrok', 'local-ngrok')) { $Script:Config.NgrokTunnelUrl } else { $Script:Config.LocalUrl }
                Write-LogMessage -Message "Attempt $attempt/$maxAttempts - Testing: $frontendUrl" -Level 'Debug'

                $response = Invoke-WebRequest -Uri $frontendUrl -TimeoutSec 15 -UseBasicParsing -ErrorAction Stop

                if ($response.StatusCode -eq 200) {
                    Write-LogMessage -Message "Frontend accessibility check passed" -Level 'Success'
                    $healthResults += $true
                    $Script:State.HealthChecksCompleted += "Frontend"
                    break
                }
            }
            catch {
                Write-LogMessage -Message "Frontend check attempt $attempt failed: $($_.Exception.Message)" -Level 'Warning'

                if ($attempt -eq $maxAttempts) {
                    Write-LogMessage -Message "Frontend accessibility check failed after $maxAttempts attempts" -Level 'Error'
                    $healthResults += $false
                } else {
                    Write-LogMessage -Message "Retrying in $retryDelay seconds..." -Level 'Info'
                    Start-Sleep -Seconds $retryDelay
                }
            }
        }
    }

    # Test ngrok tunnel (for ngrok modes)
    if ($Mode -in @('ngrok', 'local-ngrok')) {
        Write-LogMessage -Message "Testing ngrok tunnel connectivity" -Level 'Step'

        try {
            $tunnelApiUrl = "$($Script:Config.NgrokTunnelUrl)$($Script:Config.HealthEndpoints.api)"
            $response = Invoke-WebRequest -Uri $tunnelApiUrl -TimeoutSec 20 -UseBasicParsing -ErrorAction Stop

            if ($response.StatusCode -eq 200) {
                Write-LogMessage -Message "ngrok tunnel connectivity check passed" -Level 'Success'
                $healthResults += $true
                $Script:State.HealthChecksCompleted += "ngrok Tunnel"
            }
        }
        catch {
            Write-LogMessage -Message "ngrok tunnel connectivity check failed: $($_.Exception.Message)" -Level 'Warning'
            $healthResults += $false
        }
    }

    $allHealthy = $healthResults -notcontains $false

    if ($allHealthy) {
        Write-LogMessage -Message "All health checks passed successfully" -Level 'Success'
    } else {
        Write-LogMessage -Message "Some health checks failed" -Level 'Warning'
    }

    return $allHealthy
}

function Get-ContainerStatus {
    [CmdletBinding()]
    param()

    Write-Progress-Enhanced -Activity "Status Check" -Status "Checking container status" -PercentComplete 80

    try {
        $composeFile = $Script:Config.ComposeFiles[$Mode]
        $containerStatus = docker-compose -f $composeFile ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}" 2>$null

        if ($containerStatus) {
            Write-LogMessage -Message "Container status retrieved successfully" -Level 'Success'

            # Parse and store running containers
            $runningContainers = docker-compose -f $composeFile ps -q 2>$null
            if ($runningContainers) {
                $Script:State.ContainersRunning = $runningContainers
            }

            # Convert array to string with proper formatting
            if ($containerStatus -is [array]) {
                return ($containerStatus -join "`n")
            } else {
                return [string]$containerStatus
            }
        } else {
            Write-LogMessage -Message "No containers found or compose file issue" -Level 'Warning'
            return ""
        }
    }
    catch {
        Write-LogMessage -Message "Failed to get container status: $($_.Exception.Message)" -Level 'Error'
        return ""
    }
}

function Show-ServiceLogs {
    [CmdletBinding()]
    param()

    if (-not $Logs) {
        return
    }

    Write-LogMessage -Message "Displaying service logs (press Ctrl+C to exit)" -Level 'Info'

    try {
        $composeFile = $Script:Config.ComposeFiles[$Mode]
        docker-compose -f $composeFile logs -f
    }
    catch {
        Write-LogMessage -Message "Failed to display logs: $($_.Exception.Message)" -Level 'Error'
    }
}

# =============================================================================
# ENHANCED MAIN EXECUTION FUNCTION
# =============================================================================

function Invoke-EnhancedMain {
    [CmdletBinding()]
    param()

    try {
        # Initialize parameters and show banner
        Initialize-ScriptParameters

        $banner = @"
🚀 LOCQL Docker Compose Launch Script - Enhanced Edition
========================================================
Mode: $Mode
Compose File: $($Script:Config.ComposeFiles[$Mode])
Build: $Build
Health Check: $HealthCheck
Timeout: $Timeout seconds
ngrok Tunnel: $($Script:Config.NgrokTunnelUrl)
========================================================
"@
        Write-Host $banner -ForegroundColor Cyan

        # Pre-flight validation
        Write-LogMessage -Message "Starting pre-flight validation checks" -Level 'Step'

        if (-not (Test-Prerequisites)) {
            if (-not $Force) {
                Write-LogMessage -Message "Pre-flight validation failed. Use -Force to continue anyway." -Level 'Error'
                exit 1
            } else {
                Write-LogMessage -Message "Continuing despite validation failures due to -Force flag" -Level 'Warning'
            }
        }

        # Cleanup existing containers
        Stop-ExistingContainers

        # Start Docker services
        Write-LogMessage -Message "Starting Docker services in $Mode mode" -Level 'Step'

        if (-not (Start-DockerServices)) {
            throw "Failed to start Docker services"
        }

        # Wait for services to be ready
        Write-LogMessage -Message "Waiting for services to be ready..." -Level 'Step'
        Start-Sleep -Seconds 5

        # Perform health checks
        if (-not (Test-ServiceHealth)) {
            if (-not $Force) {
                throw "Health checks failed"
            } else {
                Write-LogMessage -Message "Continuing despite health check failures due to -Force flag" -Level 'Warning'
            }
        }

        # Get final container status
        $containerStatus = Get-ContainerStatus

        # Show completion summary
        Write-Progress-Enhanced -Activity "Completion" -Status "Services started successfully" -PercentComplete 100
        Start-Sleep -Seconds 1
        Write-Progress -Activity "Completion" -Completed

        Show-CompletionSummary -ContainerStatus $containerStatus

        # Show logs if requested
        Show-ServiceLogs

    }
    catch {
        Write-LogMessage -Message "Script execution failed: $($_.Exception.Message)" -Level 'Error'
        Show-ErrorSummary -ErrorMessage $_.Exception.Message
        exit 1
    }
}

function Show-CompletionSummary {
    [CmdletBinding()]
    param(
        [Parameter()]
        [string]$ContainerStatus
    )

    $duration = (Get-Date) - $Script:State.StartTime

    Write-Host ""
    Write-LogMessage -Message "🎉 LOCQL application started successfully!" -Level 'Success'
    Write-Host ""

    Write-Host "📊 DEPLOYMENT SUMMARY" -ForegroundColor Green
    Write-Host "=====================" -ForegroundColor Green
    Write-Host "  🚀 Mode: $Mode" -ForegroundColor White
    Write-Host "  📄 Compose File: $($Script:Config.ComposeFiles[$Mode])" -ForegroundColor White
    Write-Host "  ⏱️  Duration: $($duration.ToString('mm\:ss'))" -ForegroundColor White
    Write-Host "  🔧 Services Started: $($Script:State.ServicesStarted.Count)" -ForegroundColor White
    Write-Host "  ✅ Health Checks: $($Script:State.HealthChecksCompleted -join ', ')" -ForegroundColor White
    Write-Host "  🐳 Containers Running: $($Script:State.ContainersRunning.Count)" -ForegroundColor White
    Write-Host ""

    Write-Host "🌐 ACCESS URLS" -ForegroundColor Cyan
    Write-Host "==============" -ForegroundColor Cyan

    switch ($Mode) {
        'unified' {
            Write-Host "  • Application: $($Script:Config.LocalUrl)" -ForegroundColor White
            Write-Host "  • API Health: $($Script:Config.LocalUrl)$($Script:Config.HealthEndpoints.api)" -ForegroundColor White
        }
        'ngrok' {
            Write-Host "  • Application (External): $($Script:Config.NgrokTunnelUrl)" -ForegroundColor White
            Write-Host "  • Application (Local): $($Script:Config.LocalUrl)" -ForegroundColor White
            Write-Host "  • API Health: $($Script:Config.NgrokTunnelUrl)$($Script:Config.HealthEndpoints.api)" -ForegroundColor White
        }
        'local' {
            Write-Host "  • Frontend: http://localhost:5173" -ForegroundColor White
            Write-Host "  • Backend: $($Script:Config.LocalUrl)" -ForegroundColor White
            Write-Host "  • API Health: $($Script:Config.LocalUrl)$($Script:Config.HealthEndpoints.api)" -ForegroundColor White
        }
        'hybrid' {
            Write-Host "  • Application (Local): $($Script:Config.LocalUrl)" -ForegroundColor White
            Write-Host "  • Application (External): $($Script:Config.NgrokTunnelUrl)" -ForegroundColor White
            Write-Host "  • API Health: $($Script:Config.LocalUrl)$($Script:Config.HealthEndpoints.api)" -ForegroundColor White
        }
    }

    Write-Host ""

    if ($ContainerStatus -and $ContainerStatus.Trim() -ne "") {
        Write-Host "🐳 CONTAINER STATUS" -ForegroundColor Yellow
        Write-Host "==================" -ForegroundColor Yellow
        Write-Host $ContainerStatus -ForegroundColor White
        Write-Host ""
    } else {
        Write-Host "🐳 CONTAINER STATUS" -ForegroundColor Yellow
        Write-Host "==================" -ForegroundColor Yellow
        Write-Host "  Container status information not available" -ForegroundColor Gray
        Write-Host ""
    }

    Write-Host "🔧 MANAGEMENT COMMANDS" -ForegroundColor Magenta
    Write-Host "=====================" -ForegroundColor Magenta
    Write-Host "  • View logs: docker-compose -f $($Script:Config.ComposeFiles[$Mode]) logs -f" -ForegroundColor White
    Write-Host "  • Stop services: docker-compose -f $($Script:Config.ComposeFiles[$Mode]) down" -ForegroundColor White
    Write-Host "  • Restart: .\start-production-enhanced.ps1 -Mode $Mode" -ForegroundColor White
    Write-Host "  • Health check: curl $($Script:Config.LocalUrl)$($Script:Config.HealthEndpoints.api)" -ForegroundColor White
}

function Show-ErrorSummary {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ErrorMessage
    )

    $duration = (Get-Date) - $Script:State.StartTime

    Write-Host ""
    Write-Host "❌ DEPLOYMENT FAILED" -ForegroundColor Red
    Write-Host "====================" -ForegroundColor Red
    Write-Host "  Error: $ErrorMessage" -ForegroundColor White
    Write-Host "  Duration: $($duration.ToString('mm\:ss'))" -ForegroundColor White
    Write-Host "  Services Started: $($Script:State.ServicesStarted.Count)" -ForegroundColor White
    Write-Host "  Health Checks Completed: $($Script:State.HealthChecksCompleted.Count)" -ForegroundColor White
    Write-Host ""

    Write-Host "🔧 TROUBLESHOOTING TIPS" -ForegroundColor Yellow
    Write-Host "=======================" -ForegroundColor Yellow
    Write-Host "  1. Check Docker Desktop is running" -ForegroundColor White
    Write-Host "  2. Verify compose file exists: $($Script:Config.ComposeFiles[$Mode])" -ForegroundColor White
    Write-Host "  3. Check port availability (5000, 5173)" -ForegroundColor White
    Write-Host "  4. For ngrok mode: ensure ngrok is installed and authenticated" -ForegroundColor White
    Write-Host "  5. Try with -Force flag to skip validations" -ForegroundColor White
    Write-Host "  6. Use -Verbose for detailed logging" -ForegroundColor White
    Write-Host ""

    Write-Host "🔍 DIAGNOSTIC COMMANDS" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    Write-Host "  docker ps -a" -ForegroundColor White
    Write-Host "  docker-compose -f $($Script:Config.ComposeFiles[$Mode]) logs" -ForegroundColor White
    Write-Host "  docker system df" -ForegroundColor White
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Show help if requested
if ($Help) {
    Show-Help
    exit 0
}

# Execute enhanced main function
try {
    Invoke-EnhancedMain
}
catch {
    Write-LogMessage -Message "Critical script failure: $($_.Exception.Message)" -Level 'Error'
    exit 1
}
