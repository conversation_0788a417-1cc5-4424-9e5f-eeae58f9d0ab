version: '3.8'

services:
  # Simplified Application Service (Frontend + Backend on Single Port)
  # This bypasses external tunnels for testing authentication issues
  app:
    build:
      context: .
      dockerfile: Dockerfile.unified
    container_name: locql-app-simple
    ports:
      - "5000:5000"  # Single port serves both frontend and backend
    environment:
      # Database Configuration for Docker
      - DB_HOST=host.docker.internal
      - DB_USER=root
      - DB_PASS=root
      - DB_NAME=Testingarea51
      - PORT=5000
      
      # JWT Configuration
      - JWT_SECRET=dadfba2957b57678f0bdf5bb2f27b1938b9a0b472cd95a49a7f01397af5db005b9072f3d5b0a42268571b735187e2a7aedc532ae79797206fe5789718711d719
      - JWT_EXPIRE=8h
      - JWT_COOKIE_EXPIRE=1
      
      # Environment
      - NODE_ENV=production
      - DOCKER_ENV=true

      # Disable external tunnels for testing
      - NGROK_ENABLED=false
      
      # CORS Configuration
      - CORS_ORIGINS=http://localhost:5000,http://localhost:5173
      
      # Frontend URL
      - FRONTEND_URL=http://localhost:5000
      
      # Cache Configuration
      - DISABLE_CACHE=false
      
      # Email Configuration - Updated for Phase 4 SMTP Integration
      - SMTP_HOST=sicaf.com.tn
      - SMTP_PORT=25
      - SMTP_SECURE=false
      - SMTP_USE_STARTTLS=true
      - SMTP_USER=<EMAIL>
      - SMTP_PASS=5vx7*Ok48
      - SMTP_FROM_NAME=LOCQL Performance System
      - SMTP_FROM_EMAIL=<EMAIL>
      
      # Session Configuration
      - SESSION_SECRET=your-session-secret-change-this-in-production
      - TRUST_PROXY=true
      
      # Security Configuration
      - SECURE_COOKIES=false
      - SAME_SITE_COOKIES=lax
      
      # Logging
      - LOG_LEVEL=debug
      - AUTH_DEBUG=true
      - DISABLE_AUTH_CACHE=true
      
    volumes:
      # Mount source code for development hot reload
      - .:/app
      - node_modules_root:/app/node_modules
      - node_modules_frontend:/app/frontend/node_modules
      - node_modules_backend:/app/backend/node_modules
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  default:
    driver: bridge

volumes:
  node_modules_root:
  node_modules_frontend:
  node_modules_backend:
