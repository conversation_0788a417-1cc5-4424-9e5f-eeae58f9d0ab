import React, { memo, useCallback } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Select, 
  DatePicker, 
  Space, 
  Button, 
  Tag, 
  Typography,
  Tooltip,
  Divider,
  Input
} from 'antd';
import {
  FilterOutlined,
  ClearOutlined,
  CalendarOutlined,
  ToolOutlined,
  SettingOutlined,
  TeamOutlined,
  SearchOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import SOMIPEM_COLORS from '../../styles/brand-colors';

const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * Advanced Filter Component for Reports
 * Similar to ProductionDashboard and ArretsDashboard filters
 */
const ReportFilters = memo(({
  // Current filter state
  activeReportType,
  dateRange,
  selectedShift,
  selectedMachines,
  selectedModels,
  searchText,
  
  // Data options
  machines,
  models,
  shifts,
  
  // Event handlers
  onReportTypeChange,
  onDateRangeChange,
  onShiftChange,
  onMachineChange,
  onModelChange,
  onSearchChange,
  onClearFilters,
  
  // Loading states
  machinesLoading = false,
  modelsLoading = false,
  
  // Report existence checking
  existingReports = [],
  onCheckReportExists
}) => {

  // Check if any filters are active
  const hasActiveFilters = selectedShift || 
                          selectedMachines.length > 0 || 
                          selectedModels.length > 0 || 
                          searchText;

  // Get filter summary count
  const activeFilterCount = [
    selectedShift,
    selectedMachines.length > 0,
    selectedModels.length > 0,
    searchText
  ].filter(Boolean).length;

  // Check if report exists for shift reports
  const reportExists = activeReportType === 'shift' && 
                      dateRange?.[0] && 
                      selectedShift &&
                      existingReports.some(report => 
                        report.date === dateRange[0].format('YYYY-MM-DD') &&
                        report.shift === selectedShift
                      );

  // Check if all required fields are selected for shift reports
  const isShiftReportValid = activeReportType !== 'shift' || 
                            (dateRange?.[0] && selectedShift && selectedMachines.length > 0);

  // Handle machine change with default model setting for shift reports
  const handleMachineChange = (value) => {
    if (activeReportType === 'shift') {
      // For shift reports, single machine selection and auto-select IPS model
      const machineValue = Array.isArray(value) ? value[0] : value;
      onMachineChange(machineValue ? [machineValue] : []);
      
      // Auto-select IPS model if not already selected
      if (machineValue && !selectedModels.includes('IPS')) {
        onModelChange(['IPS']);
      }
    } else {
      // For other reports, multi-machine selection
      onMachineChange(value || []);
    }
  };

  return (
    <Card
      title={
        <Space>
          <FilterOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
          <Text strong>Filtres Avancés</Text>
          {activeFilterCount > 0 && (
            <Tag color={SOMIPEM_COLORS.PRIMARY_BLUE} style={{ marginLeft: 8 }}>
              {activeFilterCount} actif{activeFilterCount > 1 ? 's' : ''}
            </Tag>
          )}
        </Space>
      }
      extra={
        <Space>
          {hasActiveFilters && (
            <Tooltip title="Effacer tous les filtres">
              <Button
                type="text"
                icon={<ClearOutlined />}
                onClick={onClearFilters}
                style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}
                size="small"
              >
                Effacer
              </Button>
            </Tooltip>
          )}
        </Space>
      }
      style={{
        marginBottom: 16,
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}20`
      }}
      bodyStyle={{ paddingBottom: 16 }}
    >
      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <>
          <div style={{ 
            padding: '8px 12px', 
            backgroundColor: '#f0f8ff', 
            borderRadius: '6px',
            border: `1px solid ${SOMIPEM_COLORS.SECONDARY_BLUE}20`,
            marginBottom: '16px'
          }}>
            <Space wrap size="small">
              <Text style={{ 
                fontSize: '12px', 
                color: SOMIPEM_COLORS.SECONDARY_BLUE, 
                fontWeight: 500 
              }}>
                Filtres actifs:
              </Text>
              {selectedShift && (
                <Tag 
                  closable 
                  onClose={() => onShiftChange(null)}
                  color={SOMIPEM_COLORS.SECONDARY_BLUE}
                  size="small"
                >
                  <TeamOutlined /> Équipe: {shifts.find(s => s.key === selectedShift)?.label}
                </Tag>
              )}
              {selectedMachines.length > 0 && (
                <Tag 
                  closable 
                  onClose={() => onMachineChange([])}
                  color={SOMIPEM_COLORS.PRIMARY_BLUE}
                  size="small"
                >
                  <ToolOutlined /> Machines: {selectedMachines.length}
                </Tag>
              )}
              {selectedModels.length > 0 && (
                <Tag 
                  closable 
                  onClose={() => onModelChange([])}
                  color={SOMIPEM_COLORS.CHART_TERTIARY}
                  size="small"
                >
                  <SettingOutlined /> Modèles: {selectedModels.length}
                </Tag>
              )}
              {searchText && (
                <Tag 
                  closable 
                  onClose={() => onSearchChange("")}
                  color="orange"
                  size="small"
                >
                  Recherche: "{searchText}"
                </Tag>
              )}
            </Space>
          </div>
          <Divider style={{ margin: '16px 0' }} />
        </>
      )}

      {/* Filter Controls */}
      <Row gutter={[16, 16]}>
        {/* 1. Machine Model Filter */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
              <SettingOutlined style={{ marginRight: 4 }} />
              Modèles
              {selectedModels.length > 0 && (
                <Tag 
                  size="small" 
                  color={SOMIPEM_COLORS.CHART_TERTIARY}
                  style={{ marginLeft: 4 }}
                >
                  {selectedModels.length}
                </Tag>
              )}
            </Text>
          </div>
          <Select
            mode="multiple"
            placeholder="Tous les modèles"
            style={{ width: "100%" }}
            allowClear
            onChange={onModelChange}
            value={selectedModels}
            maxTagCount="responsive"
            showSearch
            loading={modelsLoading}
            filterOption={(input, option) =>
              option.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            notFoundContent={modelsLoading ? "Chargement..." : "Aucun modèle trouvé"}
          >
            {models.map((model) => (
              <Option key={model.id || model.name} value={model.id || model.name}>
                {model.name}
              </Option>
            ))}
          </Select>
        </Col>

        {/* 2. Machine Name Filter */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
              <ToolOutlined style={{ marginRight: 4 }} />
              Machines
              {selectedMachines.length > 0 && (
                <Tag 
                  size="small" 
                  color={SOMIPEM_COLORS.PRIMARY_BLUE}
                  style={{ marginLeft: 4 }}
                >
                  {selectedMachines.length}
                </Tag>
              )}
            </Text>
          </div>
          <Select
            mode={activeReportType === 'shift' ? "single" : "multiple"}
            placeholder={activeReportType === 'shift' ? "Sélectionner une machine" : "Toutes les machines"}
            style={{ width: "100%" }}
            allowClear
            onChange={handleMachineChange}
            value={activeReportType === 'shift' ? selectedMachines[0] : selectedMachines}
            maxTagCount="responsive"
            showSearch
            loading={machinesLoading}
            filterOption={(input, option) =>
              option.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            notFoundContent={machinesLoading ? "Chargement..." : "Aucune machine trouvée"}
          >
            {machines.map((machine) => (
              <Option key={machine.id || machine.name} value={machine.id || machine.name}>
                {machine.name}
              </Option>
            ))}
          </Select>
        </Col>

        {/* 3. Shift Filter (only for shift and production reports) */}
        {(activeReportType === "shift" || activeReportType === "production") && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <div style={{ marginBottom: 8 }}>
              <Text strong style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                <TeamOutlined style={{ marginRight: 4 }} />
                Équipe
                {activeReportType === 'shift' && (
                  <Text style={{ color: '#ff4d4f', fontSize: '12px' }}> *</Text>
                )}
              </Text>
            </div>
            <Select
              value={selectedShift}
              onChange={onShiftChange}
              placeholder={activeReportType === 'shift' ? "Sélectionner une équipe" : "Toutes les équipes"}
              allowClear={activeReportType !== 'shift'}
              style={{ width: '100%' }}
            >
              {shifts.map((shift) => (
                <Option key={shift.key} value={shift.key}>
                  <Space>
                    <div 
                      style={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        backgroundColor: shift.color 
                      }} 
                    />
                    {shift.label} ({shift.hours})
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
        )}

        {/* 4. Date Range Filter */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
              <CalendarOutlined style={{ marginRight: 4 }} />
              {activeReportType === 'shift' ? 'Date' : 'Période'}
              {activeReportType === 'shift' && (
                <Text style={{ color: '#ff4d4f', fontSize: '12px' }}> *</Text>
              )}
            </Text>
          </div>
          {activeReportType === 'shift' ? (
            <DatePicker
              value={dateRange[0]}
              onChange={(date) => onDateRangeChange([date, date])}
              format="DD/MM/YYYY"
              placeholder="Sélectionner une date"
              style={{ width: '100%' }}
              allowClear={false}
            />
          ) : (
            <RangePicker
              value={dateRange}
              onChange={onDateRangeChange}
              format="DD/MM/YYYY"
              placeholder={["Date début", "Date fin"]}
              style={{ width: '100%' }}
              allowClear={false}
            />
          )}
        </Col>

        {/* Search Filter */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
              <SearchOutlined style={{ marginRight: 4 }} />
              Recherche
            </Text>
          </div>
          <Input
            placeholder="Rechercher par type, machine, date, utilisateur..."
            prefix={<SearchOutlined />}
            allowClear
            onChange={(e) => onSearchChange(e.target.value)}
            value={searchText}
            style={{ width: '100%' }}
          />
        </Col>
      </Row>

      {/* Filter Performance Indicator */}
      {hasActiveFilters && (
        <div style={{ 
          marginTop: 16, 
          padding: '8px 12px',
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '4px'
        }}>
          <Text style={{ fontSize: '12px', color: '#52c41a' }}>
            ✓ Filtres appliqués - Les données sont filtrées selon vos critères
          </Text>
        </div>
      )}

      {/* Shift Report Validation */}
      {activeReportType === 'shift' && (
        <>
          {/* Report Exists Warning */}
          {reportExists && (
            <div style={{ 
              marginTop: 16, 
              padding: '8px 12px',
              backgroundColor: '#fff7e6',
              border: '1px solid #ffd666',
              borderRadius: '4px'
            }}>
              <Text style={{ fontSize: '12px', color: '#d48806' }}>
                ⚠️ Un rapport existe déjà pour cette date et équipe
              </Text>
            </div>
          )}
          
          {/* Required Fields Validation */}
          {!isShiftReportValid && (
            <div style={{ 
              marginTop: 16, 
              padding: '8px 12px',
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: '4px'
            }}>
              <Text style={{ fontSize: '12px', color: '#cf1322' }}>
                ❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine
              </Text>
            </div>
          )}

          {/* Success State */}
          {isShiftReportValid && !reportExists && (
            <div style={{ 
              marginTop: 16, 
              padding: '8px 12px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '4px'
            }}>
              <Text style={{ fontSize: '12px', color: '#52c41a' }}>
                ✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)
              </Text>
            </div>
          )}
        </>
      )}
    </Card>
  );
});

ReportFilters.displayName = 'ReportFilters';

// Export validation helper
export const getShiftReportValidation = (activeReportType, dateRange, selectedShift, selectedMachines, existingReports) => {
  if (activeReportType !== 'shift') {
    return { isValid: true, canCreate: true, reportExists: false };
  }

  const reportExists = dateRange?.[0] && 
                      selectedShift &&
                      existingReports.some(report => 
                        report.date === dateRange[0].format('YYYY-MM-DD') &&
                        report.shift === selectedShift
                      );

  const isValid = dateRange?.[0] && selectedShift && selectedMachines.length > 0;
  const canCreate = isValid && !reportExists;

  return { isValid, canCreate, reportExists };
};

export default ReportFilters;
