/**
 * StatusIndicator Component
 * Displays WebSocket connection status and last update time
 */
import React from 'react';
import { Tag, Button, Space, Tooltip } from 'antd';
import {
  CheckCircleOutlined,
  ReloadOutlined,
  SyncOutlined,
  WarningOutlined
} from '@ant-design/icons';
import './status-indicator.css';

/**
 * StatusIndicator component - Shows WebSocket connection status
 * @param {Object} wsStatus - WebSocket connection status object
 * @param {Function} onRefresh - Function to call when refresh button is clicked
 */
const StatusIndicator = ({ wsStatus, onRefresh }) => {
  // Determine the status tag to display based on WebSocket status
  const getStatusTag = () => {
    if (wsStatus.connected) {
      return (
        <Tooltip title="Data is being updated in real-time">
          <Tag className="ws-status-tag" color="success">
            <CheckCircleOutlined /> Real-time connected
          </Tag>
        </Tooltip>
      );
    }

    if (wsStatus.connecting) {
      return (
        <Tooltip title="Attempting to establish real-time connection">
          <Tag className="ws-status-tag" color="processing">
            <SyncOutlined spin /> Connecting...
          </Tag>
        </Tooltip>
      );
    }

    if (wsStatus.reconnecting) {
      return (
        <Tooltip title="Connection lost, attempting to reconnect">
          <Tag className="ws-status-tag" color="warning">
            <SyncOutlined spin /> Reconnecting...
          </Tag>
        </Tooltip>
      );
    }

    // Disconnected state - not connecting or reconnecting
    return (
      <Tooltip title="WebSocket connection lost - click Refresh to reconnect">
        <Tag className="ws-status-tag" color="error">
          <WarningOutlined /> Disconnected
        </Tag>
      </Tooltip>
    );
  };

  // Determine if the data is currently being updated
  const getUpdatingIndicator = () => {
    if (wsStatus.updating) {
      return (
        <Tooltip title="Receiving new data from server">
          <Tag className="ws-status-tag ws-status-updating" color="processing">
            <SyncOutlined spin /> Updating
          </Tag>
        </Tooltip>
      );
    }
    return null;
  };

  // Determine the refresh button state
  const getRefreshButton = () => {
    // Button is loading when connecting but not connected
    const isLoading = !wsStatus.connected && wsStatus.connecting;

    // Button is disabled when updating
    const isDisabled = wsStatus.updating;

    // Tooltip message based on state
    let tooltipMessage = "Refresh data from server";
    let buttonText = "Refresh Data";

    if (isLoading) {
      tooltipMessage = "Connection in progress...";
    } else if (isDisabled) {
      tooltipMessage = "Data is currently being updated";
    } else if (!wsStatus.connected && !wsStatus.reconnecting) {
      tooltipMessage = "Click to attempt WebSocket reconnection";
      buttonText = "Reconnect";
    } else if (wsStatus.reconnecting) {
      tooltipMessage = "Reconnection in progress...";
      buttonText = "Reconnecting...";
    }

    return (
      <Tooltip title={tooltipMessage}>
        <Button
          type={!wsStatus.connected && !wsStatus.connecting && !wsStatus.reconnecting ? "danger" : "primary"}
          icon={<ReloadOutlined spin={wsStatus.updating || wsStatus.reconnecting} />}
          onClick={onRefresh}
          size="small"
          loading={isLoading}
          disabled={isDisabled || wsStatus.reconnecting}
        >
          {buttonText}
        </Button>
      </Tooltip>
    );
  };

  return (
    <Space>
      {getStatusTag()}
      {getUpdatingIndicator()}
      {getRefreshButton()}
    </Space>
  );
};

export default StatusIndicator;