# New Settings System Architecture Specification

## 🎯 Core Principles

### **Immediate Effect Guarantee**
Every setting change must produce **immediate, visible, functional effects** throughout the application without requiring page refreshes or manual actions.

### **Real Application Behavior Control**
Settings drive real application behavior, not just cosmetic changes. Each setting directly impacts how the application functions and performs.

### **Extensible Architecture**
The system is designed to easily accommodate future settings additions without architectural changes.

### **Persistent & Synchronized**
Settings persist across sessions and synchronize across multiple browser tabs/windows in real-time.

---

## 🏗️ System Architecture

### **1. Settings Context Provider (`SettingsProvider`)**

**Location**: `frontend/src/contexts/SettingsContext.jsx`

**Core Responsibilities**:
- Central state management for all user settings
- Real-time synchronization with backend
- Immediate propagation to all consuming components
- Local storage caching for offline access
- Settings validation and default value management

**State Structure**:
```javascript
const settingsState = {
  // UI & Display Settings
  theme: {
    darkMode: false,
    compactMode: false,
    animationsEnabled: true,
    chartAnimations: true
  },
  
  // Table & Data Display Settings
  tables: {
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    virtualizationThreshold: 100,
    showQuickJumper: true
  },
  
  // Chart & Visualization Settings
  charts: {
    animationsEnabled: true,
    defaultType: 'bar', // 'bar', 'line', 'pie'
    showLegend: true,
    colorScheme: 'somipem', // Brand colors
    performanceMode: false
  },
  
  // Refresh & Timing Settings
  refresh: {
    dashboardInterval: 300, // seconds
    realtimeInterval: 60,   // seconds
    autoRefreshEnabled: true,
    backgroundRefresh: true
  },
  
  // Comprehensive Notification Settings
  notifications: {
    enabled: true,
    // Notification Categories (production, quality, maintenance, alerts, etc.)
    categories: {
      production: true,           // Production status changes
      quality: true,              // Quality control alerts
      maintenance: true,          // Maintenance schedules and alerts
      alerts: true,               // General system alerts
      machine_alert: true,        // Machine-specific alerts
      info: false,                // Informational notifications
      update: false,              // System updates
      shift_reports: true,        // Shift change reports
      performance: true           // Performance metrics alerts
    },
    // Priority Thresholds (low, medium, high, critical)
    priorities: {
      low: false,                 // Low priority notifications
      medium: true,               // Medium priority notifications
      high: true,                 // High priority notifications
      critical: true              // Critical priority notifications
    },
    // Delivery Methods
    delivery: {
      sse: true,                  // Server-Sent Events (real-time)
      browser: true,              // Browser notifications
      email: true,                // Email notifications
      inApp: true                 // In-application notifications
    },
    // Notification Behavior
    behavior: {
      autoClose: true,            // Auto-close notifications
      autoCloseDelay: 5000,       // Auto-close delay in milliseconds
      sound: false,               // Sound notifications
      vibration: false,           // Vibration (mobile)
      showOnLockScreen: false     // Show on lock screen (mobile)
    }
  },

  // Comprehensive Email Settings
  email: {
    enabled: true,
    // Email Notification Preferences
    notifications: {
      enabled: true,
      categories: {
        production: true,
        quality: true,
        maintenance: true,
        alerts: true,
        machine_alert: true,
        shift_reports: true,
        performance: false,
        info: false,
        update: false
      }
    },
    // Frequency Options
    frequency: 'immediate',       // 'immediate', 'hourly_batch', 'daily_digest'
    batchSettings: {
      hourlyTime: 0,              // Minute of hour for hourly batch (0-59)
      dailyTime: '08:00',         // Time for daily digest (HH:MM)
      weeklyDay: 1,               // Day of week for weekly digest (0=Sunday)
      timezone: 'Africa/Tunis'    // User timezone
    },
    // Template Preferences
    template: {
      format: 'html',             // 'html', 'text'
      style: 'detailed',          // 'minimal', 'standard', 'detailed'
      includeCharts: true,        // Include charts in emails
      includeTables: true,        // Include data tables
      language: 'fr'              // Email language
    },
    // Email Behavior
    behavior: {
      groupSimilar: true,         // Group similar notifications
      maxPerHour: 10,             // Maximum emails per hour
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '06:00'
      }
    }
  },

  // Comprehensive Report Settings
  reports: {
    // Report Generation Preferences
    generation: {
      autoGenerate: true,         // Enable auto-generation
      formats: ['pdf', 'html'],   // Preferred formats
      defaultFormat: 'pdf',       // Default format
      quality: 'high',            // 'low', 'medium', 'high'
      includeCharts: true,        // Include charts in reports
      includeTables: true,        // Include data tables
      includeRawData: false       // Include raw data export
    },
    // Subscription Settings
    subscriptions: {
      enabled: true,
      types: {
        daily: false,             // Daily reports
        weekly: true,             // Weekly reports
        monthly: true,            // Monthly reports
        quarterly: false,         // Quarterly reports
        onDemand: true            // On-demand reports
      }
    },
    // Auto-generation Schedules
    schedules: {
      daily: {
        enabled: false,
        time: '06:00',            // Generation time
        timezone: 'Africa/Tunis'
      },
      weekly: {
        enabled: true,
        day: 1,                   // Monday
        time: '07:00'
      },
      monthly: {
        enabled: true,
        day: 1,                   // First day of month
        time: '08:00'
      }
    },
    // Delivery Options
    delivery: {
      email: true,                // Email delivery
      download: true,             // Download link
      storage: true,              // Store in system
      retention: 90               // Days to retain reports
    },
    // Report Content Settings
    content: {
      sections: {
        summary: true,            // Executive summary
        production: true,         // Production metrics
        quality: true,            // Quality metrics
        maintenance: true,        // Maintenance data
        performance: true,        // Performance analysis
        trends: true,             // Trend analysis
        recommendations: false    // AI recommendations
      },
      dateRange: 'auto',          // 'auto', 'custom'
      includeComparisons: true,   // Include period comparisons
      includeForecasts: false     // Include forecasts
    }
  },
  
  // Default Views & Navigation
  defaults: {
    landingPage: 'dashboard', // 'dashboard', 'production', 'quality'
    defaultShift: 'Matin',
    dataDisplayMode: 'chart' // 'chart', 'table', 'both'
  },
  
  // Performance Settings
  performance: {
    cacheEnabled: true,
    cacheDuration: 300, // seconds
    maxConcurrentRequests: 5,
    requestTimeout: 30000 // milliseconds
  }
}
```

### **2. Settings Hook (`useSettings`)**

**Location**: `frontend/src/hooks/useSettings.js`

**API Design**:
```javascript
const {
  settings,           // Current settings object
  updateSetting,      // Update single setting
  updateSettings,     // Update multiple settings
  resetSettings,      // Reset to defaults
  isLoading,         // Loading state
  error,             // Error state
  lastUpdated        // Last update timestamp
} = useSettings();

// Usage examples:
updateSetting('theme.darkMode', true);
updateSetting('tables.defaultPageSize', 50);
updateSettings({
  'charts.animationsEnabled': false,
  'refresh.dashboardInterval': 120
});
```

### **3. Settings Service (`SettingsService`)**

**Location**: `frontend/src/services/SettingsService.js`

**Core Functions**:
- API communication with backend
- Local storage management
- Settings validation
- Default value resolution
- Change event broadcasting

### **4. Backend Settings API**

**Location**: `backend/routes/settingsRoutes.js`

**Endpoints**:
```javascript
GET    /api/settings           // Get user settings
PUT    /api/settings           // Update user settings
POST   /api/settings/reset     // Reset to defaults
GET    /api/settings/schema    // Get settings schema/validation
```

**Database Schema**:
```sql
CREATE TABLE user_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  settings_json JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_settings (user_id)
);
```

---

## 🔄 Data Flow & Integration

### **Settings Change Flow**:
1. **User Action** → Settings UI component
2. **Settings UI** → `updateSetting()` call
3. **Settings Hook** → Optimistic update + API call
4. **Settings Context** → State update + broadcast
5. **All Consumers** → Immediate re-render with new values
6. **Backend API** → Database persistence
7. **Real-time Sync** → Other browser tabs/windows

### **Component Integration Pattern**:
```javascript
// Any component that needs settings
import { useSettings } from '../hooks/useSettings';

const MyComponent = () => {
  const { settings } = useSettings();
  
  // Settings automatically available and reactive
  const pageSize = settings.tables.defaultPageSize;
  const animationsEnabled = settings.charts.animationsEnabled;
  
  return (
    <Table 
      pageSize={pageSize}
      // Component automatically updates when settings change
    />
  );
};
```

### **Critical Integration Points**:

**1. Theme Integration**:
```javascript
// ThemeProvider integration
const { settings } = useSettings();
const darkMode = settings.theme.darkMode;

// Automatic theme switching
useEffect(() => {
  updateAntdTheme(darkMode);
  updateChartTheme(darkMode);
}, [darkMode]);
```

**2. Chart Configuration Integration**:
```javascript
// Chart config automatically uses settings
export const getChartOptions = () => {
  const { settings } = useSettings();
  
  return {
    animation: settings.charts.animationsEnabled,
    responsive: true,
    // All chart options driven by settings
  };
};
```

**3. Table Configuration Integration**:
```javascript
// Table components automatically use settings
const { settings } = useSettings();

const paginationConfig = {
  pageSize: settings.tables.defaultPageSize,
  pageSizeOptions: settings.tables.pageSizeOptions,
  showQuickJumper: settings.tables.showQuickJumper
};
```

**4. Notification System Integration**:
```javascript
// SSE and notification services use settings
const { settings } = useSettings();

// Configure notification delivery
const notificationConfig = {
  categories: settings.notifications.categories,
  priorities: settings.notifications.priorities,
  delivery: settings.notifications.delivery,
  behavior: settings.notifications.behavior
};

// Email notification configuration
const emailConfig = {
  enabled: settings.email.enabled,
  frequency: settings.email.frequency,
  template: settings.email.template,
  categories: settings.email.notifications.categories
};
```

**5. Report System Integration**:
```javascript
// Report generation uses settings
const { settings } = useSettings();

const reportConfig = {
  generation: settings.reports.generation,
  schedules: settings.reports.schedules,
  delivery: settings.reports.delivery,
  content: settings.reports.content
};

// Auto-schedule reports based on user preferences
useEffect(() => {
  if (settings.reports.subscriptions.enabled) {
    scheduleReports(settings.reports.schedules);
  }
}, [settings.reports.schedules, settings.reports.subscriptions.enabled]);
```

---

## 🚀 Implementation Strategy

### **Phase 1: Core Infrastructure**
1. Create `SettingsContext` and `useSettings` hook
2. Implement backend API and database schema
3. Create basic settings service with local storage
4. Add settings to existing `RealtimeStateContext`

### **Phase 2: UI Integration**
1. Create settings page with real-time preview
2. Integrate theme settings with `ThemeProvider`
3. Connect table settings to all table components
4. Connect chart settings to all chart configurations

### **Phase 3: Advanced Features**
1. Real-time synchronization across browser tabs
2. Settings import/export functionality
3. Role-based default settings
4. Settings change history and rollback

### **Phase 4: Performance Optimization**
1. Settings memoization and caching
2. Selective re-rendering optimization
3. Settings change batching
4. Background synchronization

---

## ✅ Success Criteria

### **Immediate Effect Verification**:
- [ ] Theme change instantly updates all UI components
- [ ] Table page size change immediately affects all tables
- [ ] Chart animation toggle instantly enables/disables animations
- [ ] Refresh interval change immediately updates polling timers
- [ ] Notification settings instantly affect real-time notifications
- [ ] Email frequency change immediately updates email delivery schedules
- [ ] Report generation settings instantly affect auto-generation schedules
- [ ] Notification category changes instantly filter incoming notifications
- [ ] Priority threshold changes instantly affect notification delivery
- [ ] Email template changes instantly affect outgoing email format

### **Functional Impact Verification**:
- [ ] Settings changes affect actual application behavior
- [ ] Performance settings impact real application performance
- [ ] Display settings change how data is presented
- [ ] Timing settings affect refresh and polling intervals

### **Persistence Verification**:
- [ ] Settings persist across browser sessions
- [ ] Settings synchronize across multiple tabs
- [ ] Settings are restored after page refresh
- [ ] Settings changes are saved to backend immediately

### **Extensibility Verification**:
- [ ] New settings can be added without code changes
- [ ] Settings schema is flexible and versioned
- [ ] Default values are properly managed
- [ ] Settings validation works correctly

---

## 🔧 Technical Implementation Details

### **Settings Validation Schema**:
```javascript
const settingsSchema = {
  theme: {
    darkMode: { type: 'boolean', default: false },
    compactMode: { type: 'boolean', default: false },
    animationsEnabled: { type: 'boolean', default: true }
  },
  tables: {
    defaultPageSize: { 
      type: 'number', 
      default: 20, 
      options: [10, 20, 50, 100] 
    }
  },
  // ... complete schema definition
};
```

### **Performance Optimizations**:
- Settings memoization with `useMemo`
- Selective component updates with `React.memo`
- Debounced API calls for rapid changes
- Local storage caching for immediate access
- Background synchronization for multi-tab support

### **Error Handling**:
- Graceful fallback to default values
- Settings validation before application
- Rollback mechanism for failed updates
- User notification for settings conflicts

This architecture ensures that every setting change produces immediate, visible, functional effects throughout the application while maintaining excellent performance and user experience.
