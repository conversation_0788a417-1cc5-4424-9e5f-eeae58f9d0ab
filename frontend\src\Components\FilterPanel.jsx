import React, { useState, useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Space,
  Select,
  DatePicker,
  Button,
  Segmented,
  Tooltip,
  Alert,
  Tag,
  Divider,
  Row,
  Col,
  Skeleton,
  Badge,
  Spin,
  notification
} from "antd";
import {
  FilterOutlined,
  CalendarOutlined,
  ClearOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  LoadingOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import isoWeek from "dayjs/plugin/isoWeek";
import SOMIPEM_COLORS from "../styles/brand-colors";

// Configure dayjs plugins
dayjs.extend(isoWeek);
dayjs.locale("fr");

// Clean console logging utility for better UX (reduced noise)
const devLog = (message, data = null) => {
  if (process.env.NODE_ENV === 'development') {
    if (data) {
      console.log(message, data);
    } else {
      console.log(message);
    }
  }
};

// Enhanced date picker format utility
const getDatePickerFormat = (dateRangeType) => {
  switch (dateRangeType) {
    case 'day': return 'DD/MM/YYYY';
    case 'week': return '[Semaine] w YYYY';
    case 'month': return 'MMMM YYYY';
    default: return 'DD/MM/YYYY';
  }
};

const { Option } = Select;

/**
 * Enhanced FilterPanel component for dashboard data filtering
 * Based on ArretsDashboard filtering architecture and adapted for ProductionDashboard
 * 
 * @param {Object} props - Component props
 * @param {Array} props.machineModels - List of machine models
 * @param {Array} props.filteredMachineNames - List of filtered machine names
 * @param {string} props.selectedMachineModel - Selected machine model
 * @param {string} props.selectedMachine - Selected machine
 * @param {Object} props.dateFilter - Date filter (dayjs object)
 * @param {string} props.dateRangeType - Date range type (day/week/month)
 * @param {boolean} props.dateFilterActive - Whether date filter is active
 * @param {Function} props.handleMachineModelChange - Handler for machine model change
 * @param {Function} props.handleMachineChange - Handler for machine change
 * @param {Function} props.handleDateChange - Handler for date change
 * @param {Function} props.handleDateRangeTypeChange - Handler for date range type change
 * @param {Function} props.resetFilters - Handler for resetting filters
 * @param {Function} props.handleRefresh - Handler for refreshing data
 * @param {boolean} props.loading - Whether data is loading
 * @param {string} props.pageType - Type of page ('production' or 'arrets')
 * @param {number} props.dataSize - Current data size for performance monitoring
 * @param {number} props.estimatedLoadTime - Estimated load time for large datasets
 * @param {string} props.error - Error message if any
 * @returns {JSX.Element} - Rendered component
 */
const FilterPanel = ({
  machineModels = [],
  filteredMachineNames = [],
  selectedMachineModel = "",
  selectedMachine = "",
  dateFilter = null,
  dateRangeType = 'day',
  dateFilterActive = false,
  handleMachineModelChange,
  handleMachineChange,
  handleDateChange,
  handleDateRangeTypeChange,
  resetFilters,
  handleRefresh,
  loading = false,
  dataSize = 0,
  estimatedLoadTime = 0,
  pageType = 'production',
  error = null
}) => {
  // Enhanced debug logging with filter context information
  devLog('🔍 [FILTERPANEL] Enhanced FilterPanel props:', {
    machineModels: machineModels?.length || 0,
    filteredMachineNames: filteredMachineNames?.length || 0,
    selectedMachineModel,
    selectedMachine,
    dateFilter: dateFilter ? (typeof dateFilter === 'object' ? dateFilter.format?.('YYYY-MM-DD') : dateFilter) : null,
    dateRangeType,
    dateFilterActive,
    loading,
    dataSize,
    pageType,
    hasHandlers: {
      machineModelChange: typeof handleMachineModelChange === 'function',
      machineChange: typeof handleMachineChange === 'function',
      dateChange: typeof handleDateChange === 'function',
      dateRangeTypeChange: typeof handleDateRangeTypeChange === 'function',
      resetFilters: typeof resetFilters === 'function',
      handleRefresh: typeof handleRefresh === 'function'
    }
  });

  // Simplified data source monitoring
  const [dataSourceStatus, setDataSourceStatus] = useState({
    mysql: true,
    primary: 'mysql'
  });

  // Filter state tracking (similar to ArretFilters pattern)
  const [filterState, setFilterState] = useState({
    hasAllFilters: false,
    isComplexScenario: false,
    activeFiltersCount: 0
  });

  // UX Enhancement States for better user feedback
  const [uxState, setUxState] = useState({
    isApplyingFilters: false,
    lastFilterAction: null,
    filterProcessingStartTime: null,
    machineModelLoading: false,
    machineNamesLoading: false,
    dateFilterLoading: false,
    showSuccessAnimation: false,
    lastSuccessMessage: '',
    resultCount: null,
    hasActiveFilters: false
  });

  // Debounced filter feedback to prevent UI spam
  const [filterFeedback, setFilterFeedback] = useState({
    message: '',
    type: 'info', // 'success', 'warning', 'error', 'info'
    visible: false,
    autoHide: true
  });

  // Track filter state changes with enhanced UX feedback
  useEffect(() => {
    const hasAllFilters = selectedMachineModel && selectedMachine && dateFilterActive;
    const activeFiltersCount = [
      selectedMachineModel && 'model',
      selectedMachine && 'machine',
      dateFilterActive && 'date'
    ].filter(Boolean).length;

    const hasActiveFilters = activeFiltersCount > 0;

    const newFilterState = {
      hasAllFilters,
      isComplexScenario: hasAllFilters,
      activeFiltersCount
    };

    setFilterState(newFilterState);

    // Update UX state for active filters
    setUxState(prev => ({
      ...prev,
      hasActiveFilters
    }));

    // Reduced logging for better UX (only log in development)
    if (process.env.NODE_ENV === 'development' && hasAllFilters) {
      devLog('🎯 [FILTERPANEL] Filters applied:', {
        model: selectedMachineModel,
        machine: selectedMachine,
        date: dateFilter ? (typeof dateFilter === 'object' ? dateFilter.format?.('YYYY-MM-DD') : dateFilter) : null,
        dataSize
      });
    }
  }, [selectedMachineModel, selectedMachine, dateFilterActive, dateFilter, dataSize]);

  // Update result count when data size changes
  useEffect(() => {
    if (dataSize !== undefined && dataSize !== null) {
      setUxState(prev => ({
        ...prev,
        resultCount: dataSize
      }));
    }
  }, [dataSize]);

  // UX Enhancement Helper Functions
  const showFilterFeedback = useCallback((message, type = 'info', duration = 3000) => {
    setFilterFeedback({
      message,
      type,
      visible: true,
      autoHide: true
    });

    if (duration > 0) {
      setTimeout(() => {
        setFilterFeedback(prev => ({ ...prev, visible: false }));
      }, duration);
    }
  }, []);

  const showSuccessAnimation = useCallback((message = 'Filtres appliqués avec succès') => {
    setUxState(prev => ({
      ...prev,
      showSuccessAnimation: true,
      lastSuccessMessage: message
    }));

    // Auto-hide success animation
    setTimeout(() => {
      setUxState(prev => ({
        ...prev,
        showSuccessAnimation: false
      }));
    }, 2000);
  }, []);

  const startFilterProcessing = useCallback((action) => {
    setUxState(prev => ({
      ...prev,
      isApplyingFilters: true,
      lastFilterAction: action,
      filterProcessingStartTime: Date.now()
    }));
  }, []);

  const completeFilterProcessing = useCallback((resultCount = null) => {
    const processingTime = Date.now() - (uxState.filterProcessingStartTime || Date.now());
    
    setUxState(prev => ({
      ...prev,
      isApplyingFilters: false,
      resultCount,
      lastFilterAction: null,
      filterProcessingStartTime: null
    }));

    // Show success feedback
    if (resultCount !== null) {
      showSuccessAnimation(`Filtres appliqués - ${resultCount} résultat(s) trouvé(s)`);
    }

    // Show processing time feedback for slow operations
    if (processingTime > 1000) {
      showFilterFeedback(`Filtres appliqués en ${(processingTime / 1000).toFixed(1)}s`, 'success');
    }
  }, [uxState.filterProcessingStartTime, showSuccessAnimation, showFilterFeedback]);

  // Enhanced quick filter handlers with improved date handling and UX feedback
  const handleQuickFilter = (type) => {
    startFilterProcessing(`quick-filter-${type}`);
    
    const today = dayjs();
    let targetDate;
    let rangeType;
    let filterLabel;

    switch (type) {
      case 'today':
        targetDate = today;
        rangeType = 'day';
        filterLabel = "Aujourd'hui";
        break;
      case 'week':
        targetDate = today.startOf('isoWeek');
        rangeType = 'week';
        filterLabel = "Cette semaine";
        break;
      case 'month':
        targetDate = today.startOf('month');
        rangeType = 'month';
        filterLabel = "Ce mois";
        break;
      case 'last7days':
        targetDate = today.subtract(7, 'days');
        rangeType = 'day';
        filterLabel = "7 derniers jours";
        break;
      case 'last30days':
        targetDate = today.subtract(30, 'days');
        rangeType = 'day';
        filterLabel = "30 derniers jours";
        break;
      default:
        completeFilterProcessing();
        showFilterFeedback('Type de filtre inconnu', 'error');
        return;
    }

    // Apply the filter changes with visual feedback
    try {
      if (handleDateRangeTypeChange) {
        handleDateRangeTypeChange(rangeType);
      }
      
      if (handleDateChange) {
        handleDateChange(targetDate);
      }

      // Show immediate feedback
      showFilterFeedback(`Filtre appliqué: ${filterLabel}`, 'success', 2000);
      
      // Complete processing after a brief delay to show the change
      setTimeout(() => {
        completeFilterProcessing();
      }, 300);
      
    } catch (error) {
      completeFilterProcessing();
      showFilterFeedback('Erreur lors de l\'application du filtre', 'error');
    }
  };

  // Enhanced data size warning calculation
  const getDataSizeWarning = () => {
    if (dataSize > 1000) {
      return {
        type: 'warning',
        message: `⚠️ Volume important: ${dataSize.toLocaleString('fr-FR')} enregistrements${estimatedLoadTime ? ` (temps estimé: ${estimatedLoadTime}s)` : ''}`,
        showIcon: true
      };
    } else if (dataSize > 500) {
      return {
        type: 'info',
        message: `📊 ${dataSize.toLocaleString('fr-FR')} enregistrements à afficher`,
        showIcon: true
      };
    } else if (filterState.isComplexScenario && dataSize > 100) {
      return {
        type: 'info',
        message: `🎯 Filtrage complexe appliqué: ${dataSize.toLocaleString('fr-FR')} résultats`,
        showIcon: true
      };
    }
    return null;
  };

  // Enhanced date picker rendering based on range type (following ArretFilters pattern)
  const renderDatePicker = () => {
    const commonProps = {
      value: dateFilter,
      onChange: handleDateChange,
      allowClear: true,
      style: { width: '100%' },
      placeholder: `Sélectionner ${dateRangeType === 'day' ? 'une date' : dateRangeType === 'week' ? 'une semaine' : 'un mois'}`,
      disabled: loading
    };

    if (dateRangeType === "day") {
      return (
        <DatePicker
          {...commonProps}
          format="DD/MM/YYYY"
        />
      );
    } else if (dateRangeType === "week") {
      return (
        <DatePicker
          {...commonProps}
          picker="week"
          format="[Semaine] w YYYY"
        />
      );
    } else if (dateRangeType === "month") {
      return (
        <DatePicker
          {...commonProps}
          picker="month"
          format="MMMM YYYY"
        />
      );
    }
    return null;
  };

  const dataSizeWarning = getDataSizeWarning();

  return (
    <div style={{ width: '100%' }}>
      <Row gutter={[16, 16]}>
        {/* Enhanced Main Filters Row (following ArretFilters structure) */}
        <Col span={24}>
          <Row gutter={[16, 16]} align="middle">
            {/* Enhanced Machine Model Selection with UX Feedback */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  Modèle de Machine
                  {uxState.machineModelLoading && <LoadingOutlined style={{ fontSize: '10px' }} />}
                  {selectedMachineModel && <Badge status="processing" />}
                </div>
                <Tooltip title={
                  selectedMachineModel 
                    ? `Modèle sélectionné: ${selectedMachineModel}` 
                    : "Sélectionner un modèle de machine pour filtrer les données"
                }>
                  <Select
                    placeholder={
                      machineModels.length === 0 
                        ? "Chargement des modèles..." 
                        : "Sélectionner un modèle..."
                    }
                    style={{ width: '100%' }}
                    value={selectedMachineModel || undefined}
                    onChange={(value) => {
                      startFilterProcessing('machine-model-change');
                      setUxState(prev => ({ ...prev, machineModelLoading: true }));
                      
                      try {
                        if (handleMachineModelChange) {
                          handleMachineModelChange(value);
                          showFilterFeedback(
                            value ? `Modèle sélectionné: ${value}` : 'Modèle désélectionné', 
                            'success', 
                            1500
                          );
                        }
                      } catch (error) {
                        showFilterFeedback('Erreur lors de la sélection du modèle', 'error');
                      } finally {
                        setTimeout(() => {
                          setUxState(prev => ({ ...prev, machineModelLoading: false }));
                          completeFilterProcessing();
                        }, 200);
                      }
                    }}
                    allowClear
                    showSearch
                    disabled={loading || uxState.machineModelLoading}
                    loading={loading && !machineModels.length}
                    suffixIcon={
                      uxState.machineModelLoading ? 
                        <LoadingOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} /> :
                        selectedMachineModel ?
                          <CheckCircleOutlined style={{ color: SOMIPEM_COLORS.SUCCESS_GREEN }} /> :
                          <FilterOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />
                    }
                    filterOption={(input, option) =>
                      option.children.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {(machineModels || []).map((model) => {
                      const modelValue = typeof model === 'object' ? model.model || model.value : model;
                      return (
                        <Option key={modelValue} value={modelValue}>
                          {modelValue}
                        </Option>
                      );
                    })}
                  </Select>
                </Tooltip>
              </div>
            </Col>

            {/* Enhanced Machine Selection with UX Feedback */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  Machine Spécifique
                  {uxState.machineNamesLoading && <LoadingOutlined style={{ fontSize: '10px' }} />}
                  {selectedMachine && <Badge status="processing" />}
                </div>
                <Select
                  placeholder={
                    !selectedMachineModel 
                      ? "Sélectionner d'abord un modèle" 
                      : filteredMachineNames.length === 0 
                        ? "Aucune machine disponible"
                        : "Sélectionner une machine..."
                  }
                  style={{ width: '100%' }}
                  value={selectedMachine || undefined}
                  onChange={(value) => {
                    startFilterProcessing('machine-change');
                    setUxState(prev => ({ ...prev, machineNamesLoading: true }));
                    
                    try {
                      if (handleMachineChange) {
                        handleMachineChange(value);
                        showFilterFeedback(
                          value ? `Machine sélectionnée: ${value}` : 'Machine désélectionnée', 
                          'success', 
                          1500
                        );
                      }
                    } catch (error) {
                      showFilterFeedback('Erreur lors de la sélection de la machine', 'error');
                    } finally {
                      setTimeout(() => {
                        setUxState(prev => ({ ...prev, machineNamesLoading: false }));
                        completeFilterProcessing();
                      }, 200);
                    }
                  }}
                  disabled={!selectedMachineModel || filteredMachineNames.length === 0 || loading || uxState.machineNamesLoading}
                  allowClear
                  showSearch
                  loading={loading && selectedMachineModel}
                  suffixIcon={
                    uxState.machineNamesLoading ? 
                      <LoadingOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} /> :
                      selectedMachine ?
                        <CheckCircleOutlined style={{ color: SOMIPEM_COLORS.SUCCESS_GREEN }} /> :
                        <FilterOutlined style={{ color: !selectedMachineModel ? SOMIPEM_COLORS.LIGHT_GRAY : SOMIPEM_COLORS.PRIMARY_BLUE }} />
                  }
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {(filteredMachineNames || []).map((machine) => {
                    const machineName = typeof machine === 'object' 
                      ? machine.Machine_Name || machine.name || machine.value
                      : machine;
                    
                    return (
                      <Option key={machineName || `machine-${Math.random()}`} value={machineName}>
                        {machineName || 'Machine inconnue'}
                      </Option>
                    );
                  })}
                </Select>
              </div>
            </Col>

            {/* Date Range Type Selection */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500
                }}>
                  Type de Période
                </div>
                <Segmented
                  options={[
                    {
                      label: "Jour",
                      value: "day",
                      icon: <CalendarOutlined />,
                    },
                    {
                      label: "Semaine",
                      value: "week",
                      icon: <CalendarOutlined />,
                    },
                    {
                      label: "Mois",
                      value: "month",
                      icon: <CalendarOutlined />,
                    },
                  ]}
                  value={dateRangeType}
                  onChange={handleDateRangeTypeChange}
                  disabled={loading}
                  style={{ width: '100%' }}
                />
              </div>
            </Col>

            {/* Date Picker */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500
                }}>
                  Sélection de Date
                </div>
                {renderDatePicker()}
              </div>
            </Col>
          </Row>
        </Col>

        {/* Enhanced Action Buttons and Status Row */}
        <Col span={24}>
          <Row gutter={[16, 16]} align="middle" justify="space-between">
            <Col>
              <Space wrap>
                {/* Active Filters Display (following ArretFilters pattern) */}
                {(selectedMachineModel || selectedMachine || dateFilterActive) && (
                  <Space wrap>
                    {selectedMachineModel && (
                      <Tag 
                        color="blue" 
                        closable 
                        onClose={() => handleMachineModelChange && handleMachineModelChange('')}
                        style={{
                          backgroundColor: '#e6f7ff',
                          borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                          color: SOMIPEM_COLORS.PRIMARY_BLUE
                        }}
                      >
                        Modèle: {selectedMachineModel}
                      </Tag>
                    )}
                    {selectedMachine && (
                      <Tag 
                        color="green" 
                        closable 
                        onClose={() => handleMachineChange && handleMachineChange('')}
                        style={{
                          backgroundColor: '#f6ffed',
                          borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                          color: SOMIPEM_COLORS.SUCCESS_GREEN
                        }}
                      >
                        Machine: {selectedMachine}
                      </Tag>
                    )}
                    {dateFilterActive && dateFilter && (
                      <Tag 
                        color="orange" 
                        closable 
                        onClose={() => handleDateChange && handleDateChange(null)}
                        icon={<ClockCircleOutlined />}
                        style={{
                          backgroundColor: '#fff7e6',
                          borderColor: SOMIPEM_COLORS.WARNING_ORANGE,
                          color: SOMIPEM_COLORS.WARNING_ORANGE
                        }}
                      >
                        {dateFilter && typeof dateFilter === 'object' && dateFilter.format
                          ? dateFilter.format(
                              dateRangeType === 'day' ? 'DD/MM/YYYY' :
                              dateRangeType === 'week' ? '[Semaine] w YYYY' :
                              'MMMM YYYY'
                            )
                          : 'Date sélectionnée'
                        }
                      </Tag>
                    )}
                  </Space>
                )}

                {/* Filter Status Indicators */}
                <Tag 
                  icon={<FilterOutlined />} 
                  color="processing"
                  style={{
                    backgroundColor: '#f0f9ff',
                    borderColor: SOMIPEM_COLORS.SECONDARY_BLUE,
                    color: SOMIPEM_COLORS.SECONDARY_BLUE
                  }}
                >
                  {dataSize.toLocaleString('fr-FR')} enregistrement(s)
                  {selectedMachineModel && !selectedMachine && ` (modèle: ${selectedMachineModel})`}
                  {selectedMachine && ` (machine: ${selectedMachine})`}
                </Tag>

                {/* Active filters count */}
                {/* Enhanced Active Filters Count */}
                {filterState.activeFiltersCount > 0 && !loading && (
                  <Tag 
                    color="blue" 
                    icon={<FilterOutlined />}
                    style={{
                      backgroundColor: '#e6f7ff',
                      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                      color: SOMIPEM_COLORS.PRIMARY_BLUE
                    }}
                  >
                    {filterState.activeFiltersCount} filtre(s) actif(s)
                  </Tag>
                )}

                {/* Processing Indicator */}
                {uxState.isApplyingFilters && (
                  <Tag color="processing" icon={<LoadingOutlined />}>
                    Application des filtres...
                  </Tag>
                )}

                {/* Success Animation */}
                {uxState.showSuccessAnimation && (
                  <Tag color="success" icon={<CheckCircleOutlined />}>
                    {uxState.lastSuccessMessage}
                  </Tag>
                )}

                {/* Data Size Result */}
                {uxState.resultCount !== null && !uxState.isApplyingFilters && (
                  <Tag color="cyan" icon={<InfoCircleOutlined />}>
                    {uxState.resultCount} résultat(s)
                  </Tag>
                )}                {/* Loading indicators for different filter states */}
                {loading && selectedMachineModel && !selectedMachine && (
                  <Tag color="processing">
                    <ClockCircleOutlined spin /> Filtrage par modèle...
                  </Tag>
                )}
                
                {loading && selectedMachine && (
                  <Tag color="processing">
                    <ClockCircleOutlined spin /> Filtrage par machine...
                  </Tag>
                )}
                
                {loading && dateFilterActive && (
                  <Tag color="orange">
                    <ClockCircleOutlined spin /> Filtrage par date...
                  </Tag>
                )}

                {/* Complex filter scenario indicator */}
                {filterState.isComplexScenario && loading && (
                  <Tag color="gold">
                    <ClockCircleOutlined spin /> Traitement complexe...
                  </Tag>
                )}
              </Space>
            </Col>

            <Col>
              <Space>
                {/* Enhanced Action Buttons with UX Feedback */}
                <Tooltip title="Effacer tous les filtres et revenir à l'état initial">
                  <Button 
                    icon={<ClearOutlined />} 
                    onClick={() => {
                      startFilterProcessing('reset-filters');
                      try {
                        resetFilters();
                        showFilterFeedback('Tous les filtres ont été effacés', 'success');
                        setTimeout(() => completeFilterProcessing(null), 300);
                      } catch (error) {
                        showFilterFeedback('Erreur lors de l\'effacement des filtres', 'error');
                        completeFilterProcessing();
                      }
                    }}
                    disabled={(!selectedMachineModel && !selectedMachine && !dateFilterActive) || loading || uxState.isApplyingFilters}
                    loading={uxState.lastFilterAction === 'reset-filters' && uxState.isApplyingFilters}
                  >
                    Effacer
                  </Button>
                </Tooltip>

                <Tooltip title="Actualiser les données avec les filtres actuels">
                  <Button 
                    type="primary" 
                    icon={loading ? <LoadingOutlined /> : <ReloadOutlined />} 
                    onClick={() => {
                      startFilterProcessing('refresh-data');
                      try {
                        handleRefresh();
                        showFilterFeedback('Actualisation des données...', 'info');
                      } catch (error) {
                        showFilterFeedback('Erreur lors de l\'actualisation', 'error');
                        completeFilterProcessing();
                      }
                    }}
                    loading={loading || (uxState.lastFilterAction === 'refresh-data' && uxState.isApplyingFilters)}
                    style={{
                      backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
                    }}
                  >
                    {loading ? 'Chargement...' : 'Actualiser'}
                  </Button>
                </Tooltip>
              </Space>
            </Col>
          </Row>
        </Col>

        {/* Enhanced Quick Filters Row */}
        <Col span={24}>
          <Space split={<Divider type="vertical" />} wrap>
            <Space>
              <Tag 
                icon={<ThunderboltOutlined />} 
                color="blue"
                style={{
                  backgroundColor: '#e6f7ff',
                  borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                  color: SOMIPEM_COLORS.PRIMARY_BLUE
                }}
              >
                Filtres rapides:
              </Tag>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('today')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                Aujourd'hui
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('week')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                Cette semaine
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('month')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                Ce mois
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('last7days')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                7 derniers jours
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('last30days')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                30 derniers jours
              </Button>
            </Space>

            {/* Enhanced Performance Indicators */}
            {!selectedMachineModel && !loading && (
              <Tag 
                icon={<FilterOutlined />} 
                color="blue"
                style={{
                  backgroundColor: '#f0f9ff',
                  borderColor: SOMIPEM_COLORS.SECONDARY_BLUE,
                  color: SOMIPEM_COLORS.SECONDARY_BLUE
                }}
              >
                Affichage de tous les modèles
              </Tag>
            )}

            {!dateFilterActive && !loading && (
              <Tag 
                icon={<ClockCircleOutlined />} 
                color="green"
                style={{
                  backgroundColor: '#f6ffed',
                  borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                  color: SOMIPEM_COLORS.SUCCESS_GREEN
                }}
              >
                Filtre par défaut: 7 derniers jours
              </Tag>
            )}

            {/* Auto-refresh info */}
            <Tag 
              color="success" 
              style={{ 
                marginLeft: 'auto',
                backgroundColor: '#f6ffed',
                borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                color: SOMIPEM_COLORS.SUCCESS_GREEN
              }}
            >
              ✓ Actualisation automatique des filtres
            </Tag>
          </Space>
        </Col>

        {/* Enhanced Data Size Warning */}
        {dataSizeWarning && (
          <Col span={24}>
            <Alert
              message={dataSizeWarning.message}
              type={dataSizeWarning.type}
              showIcon={dataSizeWarning.showIcon}
              closable
              style={{ marginBottom: 0 }}
            />
          </Col>
        )}

        {/* Enhanced Error Recovery Guidance (following ArretFilters pattern) */}
        {error && (
          <Col span={24}>
            <div style={{ 
              padding: '12px', 
              backgroundColor: '#FFFFFF',
              borderRadius: '8px',
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ExclamationCircleOutlined style={{ 
                  color: '#ff4d4f',
                  fontSize: '16px', 
                  marginRight: '8px' 
                }} />
                <div>
                  <div style={{ 
                    fontWeight: 'bold',
                    color: SOMIPEM_COLORS.DARK_GRAY
                  }}>
                    Erreur de chargement des données
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    marginTop: '4px',
                    color: SOMIPEM_COLORS.LIGHT_GRAY
                  }}>
                    {error.includes && error.includes('AbortError') 
                      ? "La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page."
                      : typeof error === 'string' 
                        ? error
                        : "Une erreur est survenue lors du chargement des données. Veuillez réessayer."}
                  </div>
                  <Space style={{ marginTop: '8px' }}>
                    <Button 
                      size="small" 
                      type="primary" 
                      onClick={handleRefresh}
                      style={{
                        backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                        borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
                      }}
                    >
                      Réessayer
                    </Button>
                    <Button 
                      size="small"
                      onClick={resetFilters}
                    >
                      Réinitialiser les filtres
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          </Col>
        )}
      </Row>

      {/* Floating UX Feedback Notification */}
      {filterFeedback.visible && (
        <div style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          zIndex: 9999,
          maxWidth: '400px'
        }}>
          <Alert
            message={filterFeedback.message}
            type={filterFeedback.type}
            showIcon
            closable
            onClose={() => setFilterFeedback(prev => ({ ...prev, visible: false }))}
            style={{
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              border: `1px solid ${
                filterFeedback.type === 'success' ? SOMIPEM_COLORS.SUCCESS_GREEN :
                filterFeedback.type === 'error' ? '#ff4d4f' :
                filterFeedback.type === 'warning' ? '#faad14' :
                SOMIPEM_COLORS.PRIMARY_BLUE
              }`
            }}
          />
        </div>
      )}
    </div>
  );
};

// Enhanced PropTypes for comprehensive type checking
FilterPanel.propTypes = {
  // Machine filtering props
  machineModels: PropTypes.array.isRequired,
  filteredMachineNames: PropTypes.array.isRequired,
  selectedMachineModel: PropTypes.string,
  selectedMachine: PropTypes.string,
  
  // Date filtering props
  dateFilter: PropTypes.object, // dayjs object or Date
  dateRangeType: PropTypes.oneOf(['day', 'week', 'month']).isRequired,
  dateFilterActive: PropTypes.bool.isRequired,
  
  // Event handler props
  handleMachineModelChange: PropTypes.func.isRequired,
  handleMachineChange: PropTypes.func.isRequired,
  handleDateChange: PropTypes.func.isRequired,
  handleDateRangeTypeChange: PropTypes.func.isRequired,
  resetFilters: PropTypes.func.isRequired,
  handleRefresh: PropTypes.func.isRequired,
  
  // UI state props
  loading: PropTypes.bool,
  dataSize: PropTypes.number,
  estimatedLoadTime: PropTypes.number,
  error: PropTypes.string,
  
  // Page context props
  pageType: PropTypes.oneOf(['production', 'arrets'])
};

export default FilterPanel;
