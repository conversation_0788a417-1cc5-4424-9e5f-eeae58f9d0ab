import React, { useState, useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Space,
  Select,
  DatePicker,
  Button,
  Segmented,
  Tooltip,
  Alert,
  Tag,
  Divider,
  Row,
  Col,
  Input,
  AutoComplete,
  Switch,
  Badge,
  Skeleton
} from "antd";
import {
  FilterOutlined,
  CalendarOutlined,
  ClearOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ThunderboltOutlined,
  SearchOutlined,
  ExperimentOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import isoWeek from "dayjs/plugin/isoWeek";
import searchService from "../services/searchService";
import SOMIPEM_COLORS from "../styles/brand-colors";

// Configure dayjs plugins
dayjs.extend(isoWeek);
dayjs.locale("fr");

// Enhanced date picker format utility
const getDatePickerFormat = (dateRangeType) => {
  switch (dateRangeType) {
    case 'day': return 'DD/MM/YYYY';
    case 'week': return '[Semaine] w YYYY';
    case 'month': return 'MMMM YYYY';
    default: return 'DD/MM/YYYY';
  }
};

const { Option } = Select;
const { Search } = Input;

/**
 * Enhanced FilterPanel component with dual data source support (Redis + Elasticsearch)
 * Based on ArretsDashboard filtering architecture and adapted for ProductionDashboard
 * 
 * @param {Object} props - Component props
 * @param {Array} props.machineModels - List of machine models
 * @param {Array} props.filteredMachineNames - List of filtered machine names
 * @param {string} props.selectedMachineModel - Selected machine model
 * @param {string} props.selectedMachine - Selected machine
 * @param {Object} props.dateFilter - Date filter (dayjs object)
 * @param {string} props.dateRangeType - Date range type (day/week/month)
 * @param {boolean} props.dateFilterActive - Whether date filter is active
 * @param {Function} props.handleMachineModelChange - Handler for machine model change
 * @param {Function} props.handleMachineChange - Handler for machine change
 * @param {Function} props.handleDateChange - Handler for date change
 * @param {Function} props.handleDateRangeTypeChange - Handler for date range type change
 * @param {Function} props.resetFilters - Handler for resetting filters
 * @param {Function} props.handleRefresh - Handler for refreshing data
 * @param {boolean} props.loading - Whether data is loading
 * @param {string} props.pageType - Type of page ('production' or 'arrets')
 * @param {Function} props.onSearchResults - Handler for search results
 * @param {boolean} props.enableElasticsearch - Whether to enable Elasticsearch features
 * @param {number} props.dataSize - Current data size for performance monitoring
 * @param {number} props.estimatedLoadTime - Estimated load time for large datasets
 * @param {string} props.error - Error message if any
 * @returns {JSX.Element} - Rendered component
 */
const FilterPanel = ({
  machineModels = [],
  filteredMachineNames = [],
  selectedMachineModel = "",
  selectedMachine = "",
  dateFilter = null,
  dateRangeType = 'day',
  dateFilterActive = false,
  handleMachineModelChange,
  handleMachineChange,
  handleDateChange,
  handleDateRangeTypeChange,
  resetFilters,
  handleRefresh,
  loading = false,
  dataSize = 0,
  estimatedLoadTime = 0,
  pageType = 'production',
  onSearchResults,
  enableElasticsearch = true,
  error = null
}) => {
  // Enhanced debug logging with filter context information
  console.log('🔍 [FILTERPANEL] Enhanced FilterPanel props:', {
    machineModels: machineModels?.length || 0,
    filteredMachineNames: filteredMachineNames?.length || 0,
    selectedMachineModel,
    selectedMachine,
    dateFilter: dateFilter ? (typeof dateFilter === 'object' ? dateFilter.format?.('YYYY-MM-DD') : dateFilter) : null,
    dateRangeType,
    dateFilterActive,
    loading,
    dataSize,
    pageType,
    enableElasticsearch,
    hasHandlers: {
      machineModelChange: typeof handleMachineModelChange === 'function',
      machineChange: typeof handleMachineChange === 'function',
      dateChange: typeof handleDateChange === 'function',
      dateRangeTypeChange: typeof handleDateRangeTypeChange === 'function',
      resetFilters: typeof resetFilters === 'function',
      handleRefresh: typeof handleRefresh === 'function'
    }
  });

  // Elasticsearch search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResults, setSearchResults] = useState(null);
  const [elasticsearchEnabled, setElasticsearchEnabled] = useState(false);
  const [searchMode, setSearchMode] = useState(false);

  // Simplified data source monitoring (no more interference)
  const [dataSourceStatus, setDataSourceStatus] = useState({
    elasticsearch: false,
    mysql: true,
    primary: 'mysql'
  });

  // Filter state tracking (similar to ArretFilters pattern)
  const [filterState, setFilterState] = useState({
    hasAllFilters: false,
    isComplexScenario: false,
    activeFiltersCount: 0
  });

  // Check Elasticsearch health on component mount (simplified)
  useEffect(() => {
    if (enableElasticsearch) {
      checkElasticsearchHealth();
    }
  }, [enableElasticsearch]);

  // Track filter state changes (similar to ArretFilters)
  useEffect(() => {
    const hasAllFilters = selectedMachineModel && selectedMachine && dateFilterActive;
    const activeFiltersCount = [
      selectedMachineModel && 'model',
      selectedMachine && 'machine',
      dateFilterActive && 'date'
    ].filter(Boolean).length;

    const newFilterState = {
      hasAllFilters,
      isComplexScenario: hasAllFilters,
      activeFiltersCount
    };

    setFilterState(newFilterState);

    // Enhanced logging for complex filter scenarios
    if (hasAllFilters) {
      console.log('🎯 [FILTERPANEL] Complex filter scenario detected:', {
        model: selectedMachineModel,
        machine: selectedMachine,
        date: dateFilter ? (typeof dateFilter === 'object' ? dateFilter.format?.('YYYY-MM-DD') : dateFilter) : null,
        dataSize
      });
    }
  }, [selectedMachineModel, selectedMachine, dateFilterActive, dateFilter, dataSize]);

  const checkElasticsearchHealth = async () => {
    try {
      const health = await searchService.checkHealth();
      const isHealthy = health.elasticsearch.status === 'healthy';
      
      setElasticsearchEnabled(isHealthy);
      setDataSourceStatus(prev => ({
        ...prev,
        elasticsearch: isHealthy,
        primary: isHealthy ? 'elasticsearch' : 'mysql'
      }));

      console.log('🔍 [FILTERPANEL] Elasticsearch health check:', {
        status: health.elasticsearch.status,
        enabled: isHealthy,
        primary: isHealthy ? 'elasticsearch' : 'mysql'
      });
    } catch (error) {
      console.warn('🔍 [FILTERPANEL] Elasticsearch not available:', error);
      setElasticsearchEnabled(false);
      setDataSourceStatus(prev => ({
        ...prev,
        elasticsearch: false,
        primary: 'mysql'
      }));
    }
  };

  // Enhanced debounced search suggestions with error handling
  const debouncedGetSuggestions = useCallback(
    searchService.createDebouncedSearch(async (query) => {
      if (!query || query.length < 2) {
        setSearchSuggestions([]);
        return;
      }

      try {
        const field = pageType === 'production' ? 'machineName' : 'stopDescription';
        const suggestions = await searchService.getSuggestions(query, field, 8);
        const formattedSuggestions = suggestions.map(suggestion => ({ 
          value: suggestion,
          label: suggestion 
        }));
        setSearchSuggestions(formattedSuggestions);
      } catch (error) {
        console.error('🔍 [FILTERPANEL] Error getting suggestions:', error);
        setSearchSuggestions([]);
      }
    }, 300),
    [pageType]
  );

  // Enhanced search input change handler
  const handleSearchChange = (value) => {
    setSearchQuery(value);
    if (elasticsearchEnabled && value) {
      debouncedGetSuggestions(value);
    } else {
      setSearchSuggestions([]);
    }
  };

  // Enhanced Elasticsearch search with better error handling and data source tracking
  const performElasticsearchSearch = async (query) => {
    if (!query.trim() || !elasticsearchEnabled) return;

    setSearchLoading(true);
    console.log('🔍 [FILTERPANEL] Performing Elasticsearch search:', { query, pageType });

    try {
      const searchParams = {
        query: query.trim(),
        page: 1,
        size: 50
      };

      // Add current filter context to search
      if (dateFilter) {
        if (typeof dateFilter === 'object' && dateFilter.format) {
          // dayjs object
          searchParams.dateFrom = dateFilter.format('YYYY-MM-DD');
          searchParams.dateTo = dateFilter.format('YYYY-MM-DD');
        } else if (dateFilter instanceof Date) {
          searchParams.dateFrom = dateFilter.toISOString().split('T')[0];
          searchParams.dateTo = dateFilter.toISOString().split('T')[0];
        } else if (typeof dateFilter === 'string') {
          searchParams.dateFrom = dateFilter;
          searchParams.dateTo = dateFilter;
        }
      }

      if (selectedMachine) {
        searchParams.machineId = selectedMachine;
      }

      if (selectedMachineModel) {
        searchParams.machineModel = selectedMachineModel;
      }

      let results;
      if (pageType === 'production') {
        results = await searchService.searchProductionData(searchParams);
      } else if (pageType === 'arrets') {
        results = await searchService.searchMachineStops(searchParams);
      }

      setSearchResults(results);
      setSearchMode(true);

      console.log('🔍 [FILTERPANEL] Search completed:', {
        total: results?.total || 0,
        searchMethod: results?.searchMethod || 'unknown',
        dataSource: results?.dataSource || 'unknown'
      });

      if (onSearchResults) {
        onSearchResults(results, query);
      }
    } catch (error) {
      console.error('🔍 [FILTERPANEL] Search error:', error);
      setSearchResults(null);
      
      // Show user-friendly error message
      if (error.message) {
        console.warn('Search failed:', error.message);
      }
    } finally {
      setSearchLoading(false);
    }
  };

  // Enhanced clear search function
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
    setSearchMode(false);
    setSearchSuggestions([]);

    console.log('🔍 [FILTERPANEL] Search mode cleared, returning to filter mode');

    if (onSearchResults) {
      onSearchResults(null, '');
    }
  };

  // Enhanced quick filter handlers with improved date handling
  const handleQuickFilter = (type) => {
    console.log('🎯 [FILTERPANEL] Quick filter applied:', type);
    
    const today = dayjs();
    let targetDate;
    let rangeType;

    switch (type) {
      case 'today':
        targetDate = today;
        rangeType = 'day';
        break;
      case 'week':
        targetDate = today.startOf('isoWeek');
        rangeType = 'week';
        break;
      case 'month':
        targetDate = today.startOf('month');
        rangeType = 'month';
        break;
      case 'last7days':
        targetDate = today.subtract(7, 'days');
        rangeType = 'day';
        break;
      case 'last30days':
        targetDate = today.subtract(30, 'days');
        rangeType = 'day';
        break;
      default:
        console.warn('🎯 [FILTERPANEL] Unknown quick filter type:', type);
        return;
    }

    // Apply the filter changes
    if (handleDateRangeTypeChange) {
      handleDateRangeTypeChange(rangeType);
    }
    
    if (handleDateChange) {
      handleDateChange(targetDate);
    }
  };

  // Enhanced data size warning calculation
  const getDataSizeWarning = () => {
    if (dataSize > 1000) {
      return {
        type: 'warning',
        message: `⚠️ Volume important: ${dataSize.toLocaleString('fr-FR')} enregistrements${estimatedLoadTime ? ` (temps estimé: ${estimatedLoadTime}s)` : ''}`,
        showIcon: true
      };
    } else if (dataSize > 500) {
      return {
        type: 'info',
        message: `📊 ${dataSize.toLocaleString('fr-FR')} enregistrements à afficher`,
        showIcon: true
      };
    } else if (filterState.isComplexScenario && dataSize > 100) {
      return {
        type: 'info',
        message: `🎯 Filtrage complexe appliqué: ${dataSize.toLocaleString('fr-FR')} résultats`,
        showIcon: true
      };
    }
    return null;
  };

  // Enhanced date picker rendering based on range type (following ArretFilters pattern)
  const renderDatePicker = () => {
    const commonProps = {
      value: dateFilter,
      onChange: handleDateChange,
      allowClear: true,
      style: { width: '100%' },
      placeholder: `Sélectionner ${dateRangeType === 'day' ? 'une date' : dateRangeType === 'week' ? 'une semaine' : 'un mois'}`,
      disabled: loading
    };

    if (dateRangeType === "day") {
      return (
        <DatePicker
          {...commonProps}
          format="DD/MM/YYYY"
        />
      );
    } else if (dateRangeType === "week") {
      return (
        <DatePicker
          {...commonProps}
          picker="week"
          format="[Semaine] w YYYY"
        />
      );
    } else if (dateRangeType === "month") {
      return (
        <DatePicker
          {...commonProps}
          picker="month"
          format="MMMM YYYY"
        />
      );
    }
    return null;
  };

  const dataSizeWarning = getDataSizeWarning();

  return (
    <div style={{ width: '100%' }}>
      <Row gutter={[16, 16]}>
        {/* Enhanced Elasticsearch Search Row with data source indicators */}
        {elasticsearchEnabled && (
          <Col span={24}>
            <Space wrap style={{ width: '100%' }} size="middle">
              <Badge dot={searchMode} color="green" offset={[0, 0]}>
                <AutoComplete
                  style={{ width: 320 }}
                  options={searchSuggestions}
                  onSearch={handleSearchChange}
                  onSelect={(value) => {
                    setSearchQuery(value);
                    performElasticsearchSearch(value);
                  }}
                  value={searchQuery}
                  placeholder={`Rechercher ${pageType === 'production' ? 'dans les données de production' : 'dans les arrêts'}...`}
                  disabled={loading}
                >
                  <Search
                    loading={searchLoading}
                    onSearch={performElasticsearchSearch}
                    disabled={loading}
                    enterButton={
                      <Button 
                        type="primary" 
                        icon={<SearchOutlined />}
                        disabled={loading}
                        style={{
                          backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                          borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
                        }}
                      >
                        Rechercher
                      </Button>
                    }
                  />
                </AutoComplete>
              </Badge>

              {/* Enhanced search mode indicators */}
              {searchMode && (
                <Space>
                  <Tag 
                    color="green" 
                    icon={<ExperimentOutlined />}
                    style={{ 
                      backgroundColor: '#f6ffed',
                      borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                      color: SOMIPEM_COLORS.SUCCESS_GREEN
                    }}
                  >
                    Mode recherche actif
                  </Tag>
                  <Button 
                    size="small" 
                    onClick={clearSearch}
                    style={{
                      color: SOMIPEM_COLORS.PRIMARY_BLUE,
                      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
                    }}
                  >
                    Retour aux filtres
                  </Button>
                  {searchResults && (
                    <Tag 
                      color="blue"
                      style={{
                        backgroundColor: '#e6f7ff',
                        borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                        color: SOMIPEM_COLORS.PRIMARY_BLUE
                      }}
                    >
                      {searchResults.total?.toLocaleString('fr-FR') || 0} résultat(s)
                    </Tag>
                  )}
                </Space>
              )}

              {/* Data source toggle */}
              <Tooltip title="Basculer entre recherche Elasticsearch et filtres SQL">
                <Switch
                  checkedChildren="ES"
                  unCheckedChildren="SQL"
                  checked={searchMode}
                  onChange={(checked) => {
                    if (!checked) {
                      clearSearch();
                    }
                  }}
                  disabled={loading}
                />
              </Tooltip>

              {/* Data source status indicator */}
              <Tag 
                color={dataSourceStatus.primary === 'elasticsearch' ? 'blue' : 'orange'}
                icon={<ThunderboltOutlined />}
              >
                Source: {dataSourceStatus.primary === 'elasticsearch' ? 'Elasticsearch' : 'MySQL'}
              </Tag>
            </Space>
            <Divider style={{ margin: '12px 0' }} />
          </Col>
        )}

        {/* Enhanced Main Filters Row (following ArretFilters structure) */}
        <Col span={24}>
          <Row gutter={[16, 16]} align="middle">
            {/* Machine Model Selection */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500
                }}>
                  Modèle de Machine
                </div>
                <Tooltip title="Sélectionner un modèle de machine pour filtrer les données">
                  <Select
                    placeholder="Tous les modèles"
                    style={{ width: '100%' }}
                    value={selectedMachineModel || undefined}
                    onChange={(value) => {
                      console.log('🎯 [FILTERPANEL] Machine Model changed:', value);
                      if (handleMachineModelChange) {
                        handleMachineModelChange(value);
                      } else {
                        console.error('🎯 [FILTERPANEL] handleMachineModelChange not provided!');
                      }
                    }}
                    allowClear
                    showSearch
                    disabled={loading}
                    loading={loading && !machineModels.length}
                    suffixIcon={<FilterOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />}
                    filterOption={(input, option) =>
                      option.children.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {(machineModels || []).map((model) => {
                      const modelValue = typeof model === 'object' ? model.model || model.value : model;
                      return (
                        <Option key={modelValue} value={modelValue}>
                          {modelValue}
                        </Option>
                      );
                    })}
                  </Select>
                </Tooltip>
              </div>
            </Col>

            {/* Machine Selection */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500
                }}>
                  Machine Spécifique
                </div>
                <Select
                  placeholder="Sélectionner une machine"
                  style={{ width: '100%' }}
                  value={selectedMachine || undefined}
                  onChange={(value) => {
                    console.log('🏭 [FILTERPANEL] Machine changed:', value);
                    if (handleMachineChange) {
                      handleMachineChange(value);
                    } else {
                      console.error('🏭 [FILTERPANEL] handleMachineChange not provided!');
                    }
                  }}
                  disabled={!selectedMachineModel || filteredMachineNames.length === 0 || loading}
                  allowClear
                  showSearch
                  loading={loading && selectedMachineModel}
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {(filteredMachineNames || []).map((machine) => {
                    const machineName = typeof machine === 'object' 
                      ? machine.Machine_Name || machine.name || machine.value
                      : machine;
                    
                    return (
                      <Option key={machineName || `machine-${Math.random()}`} value={machineName}>
                        {machineName || 'Machine inconnue'}
                      </Option>
                    );
                  })}
                </Select>
              </div>
            </Col>

            {/* Date Range Type Selection */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500
                }}>
                  Type de Période
                </div>
                <Segmented
                  options={[
                    {
                      label: "Jour",
                      value: "day",
                      icon: <CalendarOutlined />,
                    },
                    {
                      label: "Semaine",
                      value: "week",
                      icon: <CalendarOutlined />,
                    },
                    {
                      label: "Mois",
                      value: "month",
                      icon: <CalendarOutlined />,
                    },
                  ]}
                  value={dateRangeType}
                  onChange={handleDateRangeTypeChange}
                  disabled={loading}
                  style={{ width: '100%' }}
                />
              </div>
            </Col>

            {/* Date Picker */}
            <Col xs={24} sm={12} lg={6}>
              <div>
                <div style={{ 
                  marginBottom: 4, 
                  fontSize: '12px', 
                  color: SOMIPEM_COLORS.LIGHT_GRAY,
                  fontWeight: 500
                }}>
                  Sélection de Date
                </div>
                {renderDatePicker()}
              </div>
            </Col>
          </Row>
        </Col>

        {/* Enhanced Action Buttons and Status Row */}
        <Col span={24}>
          <Row gutter={[16, 16]} align="middle" justify="space-between">
            <Col>
              <Space wrap>
                {/* Active Filters Display (following ArretFilters pattern) */}
                {(selectedMachineModel || selectedMachine || dateFilterActive) && (
                  <Space wrap>
                    {selectedMachineModel && (
                      <Tag 
                        color="blue" 
                        closable 
                        onClose={() => handleMachineModelChange && handleMachineModelChange('')}
                        style={{
                          backgroundColor: '#e6f7ff',
                          borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                          color: SOMIPEM_COLORS.PRIMARY_BLUE
                        }}
                      >
                        Modèle: {selectedMachineModel}
                      </Tag>
                    )}
                    {selectedMachine && (
                      <Tag 
                        color="green" 
                        closable 
                        onClose={() => handleMachineChange && handleMachineChange('')}
                        style={{
                          backgroundColor: '#f6ffed',
                          borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                          color: SOMIPEM_COLORS.SUCCESS_GREEN
                        }}
                      >
                        Machine: {selectedMachine}
                      </Tag>
                    )}
                    {dateFilterActive && dateFilter && (
                      <Tag 
                        color="orange" 
                        closable 
                        onClose={() => handleDateChange && handleDateChange(null)}
                        icon={<ClockCircleOutlined />}
                        style={{
                          backgroundColor: '#fff7e6',
                          borderColor: SOMIPEM_COLORS.WARNING_ORANGE,
                          color: SOMIPEM_COLORS.WARNING_ORANGE
                        }}
                      >
                        {dateFilter && typeof dateFilter === 'object' && dateFilter.format
                          ? dateFilter.format(
                              dateRangeType === 'day' ? 'DD/MM/YYYY' :
                              dateRangeType === 'week' ? '[Semaine] w YYYY' :
                              'MMMM YYYY'
                            )
                          : 'Date sélectionnée'
                        }
                      </Tag>
                    )}
                  </Space>
                )}

                {/* Filter Status Indicators */}
                <Tag 
                  icon={<FilterOutlined />} 
                  color="processing"
                  style={{
                    backgroundColor: '#f0f9ff',
                    borderColor: SOMIPEM_COLORS.SECONDARY_BLUE,
                    color: SOMIPEM_COLORS.SECONDARY_BLUE
                  }}
                >
                  {dataSize.toLocaleString('fr-FR')} enregistrement(s)
                  {selectedMachineModel && !selectedMachine && ` (modèle: ${selectedMachineModel})`}
                  {selectedMachine && ` (machine: ${selectedMachine})`}
                </Tag>

                {/* Active filters count */}
                {filterState.activeFiltersCount > 0 && (
                  <Tag 
                    color="blue"
                    style={{
                      backgroundColor: '#e6f7ff',
                      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                      color: SOMIPEM_COLORS.PRIMARY_BLUE
                    }}
                  >
                    {filterState.activeFiltersCount} filtre(s) actif(s)
                  </Tag>
                )}

                {/* Loading indicators for different filter states */}
                {loading && selectedMachineModel && !selectedMachine && (
                  <Tag color="processing">
                    <ClockCircleOutlined spin /> Filtrage par modèle...
                  </Tag>
                )}
                
                {loading && selectedMachine && (
                  <Tag color="processing">
                    <ClockCircleOutlined spin /> Filtrage par machine...
                  </Tag>
                )}
                
                {loading && dateFilterActive && (
                  <Tag color="orange">
                    <ClockCircleOutlined spin /> Filtrage par date...
                  </Tag>
                )}

                {/* Complex filter scenario indicator */}
                {filterState.isComplexScenario && loading && (
                  <Tag color="gold">
                    <ClockCircleOutlined spin /> Traitement complexe...
                  </Tag>
                )}
              </Space>
            </Col>

            <Col>
              <Space>
                {/* Action Buttons */}
                <Tooltip title="Effacer tous les filtres">
                  <Button 
                    icon={<ClearOutlined />} 
                    onClick={resetFilters}
                    disabled={(!selectedMachineModel && !selectedMachine && !dateFilterActive) || loading}
                  >
                    Effacer
                  </Button>
                </Tooltip>

                <Tooltip title="Actualiser les données">
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />} 
                    onClick={handleRefresh}
                    loading={loading}
                    style={{
                      backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                      borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
                    }}
                  >
                    {loading ? 'Chargement...' : 'Actualiser'}
                  </Button>
                </Tooltip>
              </Space>
            </Col>
          </Row>
        </Col>

        {/* Enhanced Quick Filters Row */}
        <Col span={24}>
          <Space split={<Divider type="vertical" />} wrap>
            <Space>
              <Tag 
                icon={<ThunderboltOutlined />} 
                color="blue"
                style={{
                  backgroundColor: '#e6f7ff',
                  borderColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                  color: SOMIPEM_COLORS.PRIMARY_BLUE
                }}
              >
                Filtres rapides:
              </Tag>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('today')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                Aujourd'hui
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('week')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                Cette semaine
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('month')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                Ce mois
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('last7days')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                7 derniers jours
              </Button>
              <Button 
                size="small" 
                type="link" 
                onClick={() => handleQuickFilter('last30days')}
                disabled={loading}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              >
                30 derniers jours
              </Button>
            </Space>

            {/* Enhanced Performance Indicators */}
            {!selectedMachineModel && !loading && (
              <Tag 
                icon={<FilterOutlined />} 
                color="blue"
                style={{
                  backgroundColor: '#f0f9ff',
                  borderColor: SOMIPEM_COLORS.SECONDARY_BLUE,
                  color: SOMIPEM_COLORS.SECONDARY_BLUE
                }}
              >
                Affichage de tous les modèles
              </Tag>
            )}

            {!dateFilterActive && !loading && (
              <Tag 
                icon={<ClockCircleOutlined />} 
                color="green"
                style={{
                  backgroundColor: '#f6ffed',
                  borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                  color: SOMIPEM_COLORS.SUCCESS_GREEN
                }}
              >
                Filtre par défaut: 7 derniers jours
              </Tag>
            )}

            {/* Auto-refresh info */}
            <Tag 
              color="success" 
              style={{ 
                marginLeft: 'auto',
                backgroundColor: '#f6ffed',
                borderColor: SOMIPEM_COLORS.SUCCESS_GREEN,
                color: SOMIPEM_COLORS.SUCCESS_GREEN
              }}
            >
              ✓ Actualisation automatique des filtres
            </Tag>
          </Space>
        </Col>

        {/* Enhanced Data Size Warning */}
        {dataSizeWarning && (
          <Col span={24}>
            <Alert
              message={dataSizeWarning.message}
              type={dataSizeWarning.type}
              showIcon={dataSizeWarning.showIcon}
              closable
              style={{ marginBottom: 0 }}
            />
          </Col>
        )}

        {/* Enhanced Error Recovery Guidance (following ArretFilters pattern) */}
        {error && (
          <Col span={24}>
            <div style={{ 
              padding: '12px', 
              backgroundColor: '#FFFFFF',
              borderRadius: '8px',
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ExclamationCircleOutlined style={{ 
                  color: '#ff4d4f',
                  fontSize: '16px', 
                  marginRight: '8px' 
                }} />
                <div>
                  <div style={{ 
                    fontWeight: 'bold',
                    color: SOMIPEM_COLORS.DARK_GRAY
                  }}>
                    Erreur de chargement des données
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    marginTop: '4px',
                    color: SOMIPEM_COLORS.LIGHT_GRAY
                  }}>
                    {error.includes && error.includes('AbortError') 
                      ? "La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page."
                      : typeof error === 'string' 
                        ? error
                        : "Une erreur est survenue lors du chargement des données. Veuillez réessayer."}
                  </div>
                  <Space style={{ marginTop: '8px' }}>
                    <Button 
                      size="small" 
                      type="primary" 
                      onClick={handleRefresh}
                      style={{
                        backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                        borderColor: SOMIPEM_COLORS.PRIMARY_BLUE
                      }}
                    >
                      Réessayer
                    </Button>
                    <Button 
                      size="small"
                      onClick={resetFilters}
                    >
                      Réinitialiser les filtres
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          </Col>
        )}
      </Row>
    </div>
  );
};

// Enhanced PropTypes for comprehensive type checking
FilterPanel.propTypes = {
  // Machine filtering props
  machineModels: PropTypes.array.isRequired,
  filteredMachineNames: PropTypes.array.isRequired,
  selectedMachineModel: PropTypes.string,
  selectedMachine: PropTypes.string,
  
  // Date filtering props
  dateFilter: PropTypes.object, // dayjs object or Date
  dateRangeType: PropTypes.oneOf(['day', 'week', 'month']).isRequired,
  dateFilterActive: PropTypes.bool.isRequired,
  
  // Event handler props
  handleMachineModelChange: PropTypes.func.isRequired,
  handleMachineChange: PropTypes.func.isRequired,
  handleDateChange: PropTypes.func.isRequired,
  handleDateRangeTypeChange: PropTypes.func.isRequired,
  resetFilters: PropTypes.func.isRequired,
  handleRefresh: PropTypes.func.isRequired,
  
  // UI state props
  loading: PropTypes.bool,
  dataSize: PropTypes.number,
  estimatedLoadTime: PropTypes.number,
  error: PropTypes.string,
  
  // Page context props
  pageType: PropTypes.oneOf(['production', 'arrets']),
  
  // Search functionality props
  onSearchResults: PropTypes.func,
  enableElasticsearch: PropTypes.bool
};

export default FilterPanel;
