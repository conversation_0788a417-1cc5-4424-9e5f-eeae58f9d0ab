import React, { useState, useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Space,
  Select,
  DatePicker,
  Button,
  Segmented,
  Tooltip,
  Alert,
  Tag,
  Divider,
  Row,
  Col,
  Input,
  AutoComplete,
  Switch,
  Badge
} from "antd";
import {
  FilterOutlined,
  CalendarOutlined,
  ClearOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ThunderboltOutlined,
  SearchOutlined,
  ExperimentOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import searchService from "../services/searchService";

// Simple date picker format utility
const getDatePickerFormat = (dateRangeType) => {
  switch (dateRangeType) {
    case 'day': return 'DD/MM/YYYY';
    case 'week': return 'DD/MM/YYYY';
    case 'month': return 'MM/YYYY';
    default: return 'DD/MM/YYYY';
  }
};

const { Option } = Select;
const { Search } = Input;

/**
 * Enhanced FilterPanel component with Elasticsearch search capabilities
 * @param {Object} props - Component props
 * @param {Array} props.machineModels - List of machine models
 * @param {Array} props.filteredMachineNames - List of filtered machine names
 * @param {string} props.selectedMachineModel - Selected machine model
 * @param {string} props.selectedMachine - Selected machine
 * @param {Object} props.dateFilter - Date filter
 * @param {string} props.dateRangeType - Date range type
 * @param {boolean} props.dateFilterActive - Whether date filter is active
 * @param {Function} props.handleMachineModelChange - Handler for machine model change
 * @param {Function} props.handleMachineChange - Handler for machine change
 * @param {Function} props.handleDateChange - Handler for date change
 * @param {Function} props.handleDateRangeTypeChange - Handler for date range type change
 * @param {Function} props.resetFilters - Handler for resetting filters
 * @param {Function} props.handleRefresh - Handler for refreshing data
 * @param {boolean} props.loading - Whether data is loading
 * @param {string} props.pageType - Type of page ('production' or 'arrets')
 * @param {Function} props.onSearchResults - Handler for search results
 * @param {boolean} props.enableElasticsearch - Whether to enable Elasticsearch features
 * @returns {JSX.Element} - Rendered component
 */
const FilterPanel = ({
  machineModels,
  filteredMachineNames,
  selectedMachineModel = "",
  selectedMachine = "",
  dateFilter = null,
  dateRangeType,
  dateFilterActive,
  handleMachineModelChange,
  handleMachineChange,
  handleDateChange,
  handleDateRangeTypeChange,
  resetFilters,
  handleRefresh,
  loading = false,
  dataSize = 0,
  estimatedLoadTime = 0,
  pageType = 'production',
  onSearchResults,
  enableElasticsearch = true
}) => {
  // Elasticsearch search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResults, setSearchResults] = useState(null);
  const [elasticsearchEnabled, setElasticsearchEnabled] = useState(false);
  const [searchMode, setSearchMode] = useState(false);

  // Check Elasticsearch health on component mount
  useEffect(() => {
    if (enableElasticsearch) {
      checkElasticsearchHealth();
    }
  }, [enableElasticsearch]);

  const checkElasticsearchHealth = async () => {
    try {
      const health = await searchService.checkHealth();
      setElasticsearchEnabled(health.elasticsearch.status === 'healthy');
    } catch (error) {
      console.warn('Elasticsearch not available:', error);
      setElasticsearchEnabled(false);
    }
  };

  // Debounced search suggestions
  const debouncedGetSuggestions = useCallback(
    searchService.createDebouncedSearch(async (query) => {
      if (!query || query.length < 2) {
        setSearchSuggestions([]);
        return;
      }

      try {
        const field = pageType === 'production' ? 'machineName' : 'stopDescription';
        const suggestions = await searchService.getSuggestions(query, field, 8);
        setSearchSuggestions(suggestions.map(suggestion => ({ value: suggestion })));
      } catch (error) {
        console.error('Error getting suggestions:', error);
        setSearchSuggestions([]);
      }
    }, 300),
    [pageType]
  );

  // Handle search input change
  const handleSearchChange = (value) => {
    setSearchQuery(value);
    if (elasticsearchEnabled) {
      debouncedGetSuggestions(value);
    }
  };

  // Perform Elasticsearch search
  const performElasticsearchSearch = async (query) => {
    if (!query.trim() || !elasticsearchEnabled) return;

    setSearchLoading(true);
    try {
      const searchParams = {
        query: query.trim(),
        dateFrom: dateFilter?.startDate,
        dateTo: dateFilter?.endDate,
        machineId: selectedMachine,
        machineModel: selectedMachineModel,
        page: 1,
        size: 50
      };

      let results;
      if (pageType === 'production') {
        results = await searchService.searchProductionData(searchParams);
      } else if (pageType === 'arrets') {
        results = await searchService.searchMachineStops(searchParams);
      }

      setSearchResults(results);
      setSearchMode(true);

      if (onSearchResults) {
        onSearchResults(results, query);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults(null);
    } finally {
      setSearchLoading(false);
    }
  };

  // Clear search and return to normal mode
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
    setSearchMode(false);
    setSearchSuggestions([]);

    if (onSearchResults) {
      onSearchResults(null, '');
    }
  };
  // Quick filter handlers
  const handleQuickFilter = (type) => {
    const today = dayjs();
    let targetDate;
    let rangeType;

    switch (type) {
      case 'today':
        targetDate = today;
        rangeType = 'day';
        break;
      case 'week':
        targetDate = today;
        rangeType = 'week';
        break;
      case 'month':
        targetDate = today;
        rangeType = 'month';
        break;
      case 'last7days':
        targetDate = today.subtract(7, 'days');
        rangeType = 'week';
        break;
      case 'last30days':
        targetDate = today.subtract(30, 'days');
        rangeType = 'month';
        break;
      default:
        return;
    }

    handleDateRangeTypeChange(rangeType);
    handleDateChange(targetDate);
  };

  // Calculate data size warning
  const getDataSizeWarning = () => {
    if (dataSize > 1000) {
      return {
        type: 'warning',
        message: `Attention: ${dataSize} enregistrements à charger (temps estimé: ${estimatedLoadTime}s)`
      };
    } else if (dataSize > 500) {
      return {
        type: 'info',
        message: `${dataSize} enregistrements à charger`
      };
    }
    return null;
  };

  const dataSizeWarning = getDataSizeWarning();

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* Elasticsearch Search Row */}
        {elasticsearchEnabled && (
          <Col span={24}>
            <Space wrap style={{ width: '100%' }}>
              <Badge dot={searchMode} color="green">
                <AutoComplete
                  style={{ width: 300 }}
                  options={searchSuggestions}
                  onSearch={handleSearchChange}
                  onSelect={(value) => {
                    setSearchQuery(value);
                    performElasticsearchSearch(value);
                  }}
                  value={searchQuery}
                  placeholder={`Rechercher ${pageType === 'production' ? 'dans les données de production' : 'dans les arrêts'}...`}
                >
                  <Search
                    loading={searchLoading}
                    onSearch={performElasticsearchSearch}
                    enterButton={
                      <Button type="primary" icon={<SearchOutlined />}>
                        Rechercher
                      </Button>
                    }
                  />
                </AutoComplete>
              </Badge>

              {searchMode && (
                <Space>
                  <Tag color="green" icon={<ExperimentOutlined />}>
                    Mode recherche actif
                  </Tag>
                  <Button size="small" onClick={clearSearch}>
                    Retour aux filtres
                  </Button>
                  {searchResults && (
                    <Tag color="blue">
                      {searchResults.total} résultat(s) trouvé(s)
                    </Tag>
                  )}
                </Space>
              )}

              <Switch
                checkedChildren="ES"
                unCheckedChildren="SQL"
                checked={searchMode}
                onChange={(checked) => {
                  if (!checked) {
                    clearSearch();
                  }
                }}
                title="Basculer entre recherche Elasticsearch et filtres SQL"
              />
            </Space>
            <Divider style={{ margin: '12px 0' }} />
          </Col>
        )}

        {/* Main Filters Row */}
        <Col span={24}>
          <Space wrap>
            {/* Machine Model Selection */}
            <Tooltip title="Filtrer par modèle de machine (optionnel - toutes les données sont affichées par défaut)">
              <Select
                placeholder="Tous les modèles"
                style={{ width: 150 }}
                value={selectedMachineModel || undefined}
                onChange={handleMachineModelChange}
                allowClear
                suffixIcon={<FilterOutlined style={{ color: '#1890ff' }} />}
              >
                {machineModels.map((model) => (
                  <Option key={model} value={model}>
                    {model}
                  </Option>
                ))}
              </Select>
            </Tooltip>

            {/* Machine Selection */}
            <Select
              placeholder="Sélectionner une machine"
              style={{ width: 150 }}
              value={selectedMachine || undefined}
              onChange={handleMachineChange}
              disabled={!selectedMachineModel || filteredMachineNames.length === 0}
              allowClear
            >
              {filteredMachineNames.map((machine) => (
                <Option key={machine.Machine_Name} value={machine.Machine_Name}>
                  {machine.Machine_Name}
                </Option>
              ))}
            </Select>

            {/* Date Range Type */}
            <Segmented
              options={[
                {
                  label: "Jour",
                  value: "day",
                  icon: <CalendarOutlined />,
                },
                {
                  label: "Semaine",
                  value: "week",
                  icon: <CalendarOutlined />,
                },
                {
                  label: "Mois",
                  value: "month",
                  icon: <CalendarOutlined />,
                },
              ]}
              value={dateRangeType}
              onChange={handleDateRangeTypeChange}
            />

            {/* Date Picker */}
            <DatePicker
              placeholder={`Sélectionner un ${dateRangeType === "day" ? "jour" : dateRangeType === "week" ? "semaine" : "mois"}`}
              format={getDatePickerFormat(dateRangeType)}
              value={dateFilter ? dayjs(dateFilter) : null}
              onChange={handleDateChange}
              picker={dateRangeType === "day" ? undefined : dateRangeType}
              allowClear
              style={{ width: 180 }}
            />

            {/* Action Buttons */}
            <Tooltip title="Réinitialiser les filtres">
              <Button
                icon={<ClearOutlined />}
                onClick={resetFilters}
                disabled={!selectedMachineModel && !selectedMachine && !dateFilterActive}
              />
            </Tooltip>

            <Tooltip title="Rafraîchir les données">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              />
            </Tooltip>
          </Space>
        </Col>

        {/* Quick Filters Row */}
        <Col span={24}>
          <Space split={<Divider type="vertical" />} wrap>
            <Space>
              <Tag icon={<ThunderboltOutlined />} color="blue">Filtres rapides:</Tag>
              <Button size="small" type="link" onClick={() => handleQuickFilter('today')}>
                Aujourd'hui
              </Button>
              <Button size="small" type="link" onClick={() => handleQuickFilter('week')}>
                Cette semaine
              </Button>
              <Button size="small" type="link" onClick={() => handleQuickFilter('month')}>
                Ce mois
              </Button>
              <Button size="small" type="link" onClick={() => handleQuickFilter('last7days')}>
                7 derniers jours
              </Button>
              <Button size="small" type="link" onClick={() => handleQuickFilter('last30days')}>
                30 derniers jours
              </Button>
            </Space>

            {/* Performance Indicators */}
            {!selectedMachineModel && (
              <Tag icon={<FilterOutlined />} color="blue">
                Affichage de tous les modèles de machines
              </Tag>
            )}

            {!dateFilterActive && (
              <Tag icon={<ClockCircleOutlined />} color="green">
                Filtre par défaut: 7 derniers jours
              </Tag>
            )}
          </Space>
        </Col>

        {/* Data Size Warning */}
        {dataSizeWarning && (
          <Col span={24}>
            <Alert
              message={dataSizeWarning.message}
              type={dataSizeWarning.type}
              showIcon
              closable
              style={{ marginBottom: 0 }}
            />
          </Col>
        )}
      </Row>
    </div>
  );
};

// PropTypes for type checking
FilterPanel.propTypes = {
  machineModels: PropTypes.array.isRequired,
  filteredMachineNames: PropTypes.array.isRequired,
  selectedMachineModel: PropTypes.string,
  selectedMachine: PropTypes.string,
  dateFilter: PropTypes.object,
  dateRangeType: PropTypes.string.isRequired,
  dateFilterActive: PropTypes.bool.isRequired,
  handleMachineModelChange: PropTypes.func.isRequired,
  handleMachineChange: PropTypes.func.isRequired,
  handleDateChange: PropTypes.func.isRequired,
  handleDateRangeTypeChange: PropTypes.func.isRequired,
  resetFilters: PropTypes.func.isRequired,
  handleRefresh: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  dataSize: PropTypes.number,
  estimatedLoadTime: PropTypes.number,
  pageType: PropTypes.oneOf(['production', 'arrets']),
  onSearchResults: PropTypes.func,
  enableElasticsearch: PropTypes.bool
};

// Default props have been moved to function parameters

export default FilterPanel;
