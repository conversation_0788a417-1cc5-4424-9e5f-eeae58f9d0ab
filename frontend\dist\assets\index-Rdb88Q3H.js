import{R as u,r,a1 as D,q as M,b5 as Se,c as ye,Q as ve,U as xe,V as l,ap as Ce,J as be,aF as Ee,ba as ze,au as Pe,aQ as Q,aH as Ne,b3 as U,b as Oe,bb as Be,m as Ie}from"./index-y9W4UQPd.js";const F=u.createContext({});F.Consumer;var Y=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)e.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(i[n[a]]=t[n[a]]);return i};const Le=t=>{var{prefixCls:e,className:i,avatar:n,title:a,description:s}=t,p=Y(t,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:y}=r.useContext(D),m=y("list",e),z=M(`${m}-item-meta`,i),x=u.createElement("div",{className:`${m}-item-meta-content`},a&&u.createElement("h4",{className:`${m}-item-meta-title`},a),s&&u.createElement("div",{className:`${m}-item-meta-description`},s));return u.createElement("div",Object.assign({},p,{className:z}),n&&u.createElement("div",{className:`${m}-item-meta-avatar`},n),(a||s)&&x)},we=u.forwardRef((t,e)=>{const{prefixCls:i,children:n,actions:a,extra:s,styles:p,className:y,classNames:m,colStyle:z}=t,x=Y(t,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:P,itemLayout:c}=r.useContext(F),{getPrefixCls:N,list:$}=r.useContext(D),C=f=>{var d,b;return M((b=(d=$==null?void 0:$.item)===null||d===void 0?void 0:d.classNames)===null||b===void 0?void 0:b[f],m==null?void 0:m[f])},O=f=>{var d,b;return Object.assign(Object.assign({},(b=(d=$==null?void 0:$.item)===null||d===void 0?void 0:d.styles)===null||b===void 0?void 0:b[f]),p==null?void 0:p[f])},j=()=>{let f=!1;return r.Children.forEach(n,d=>{typeof d=="string"&&(f=!0)}),f&&r.Children.count(n)>1},B=()=>c==="vertical"?!!s:!j(),S=N("list",i),I=a&&a.length>0&&u.createElement("ul",{className:M(`${S}-item-action`,C("actions")),key:"actions",style:O("actions")},a.map((f,d)=>u.createElement("li",{key:`${S}-item-action-${d}`},f,d!==a.length-1&&u.createElement("em",{className:`${S}-item-action-split`})))),H=P?"div":"li",L=u.createElement(H,Object.assign({},x,P?{}:{ref:e},{className:M(`${S}-item`,{[`${S}-item-no-flex`]:!B()},y)}),c==="vertical"&&s?[u.createElement("div",{className:`${S}-item-main`,key:"content"},n,I),u.createElement("div",{className:M(`${S}-item-extra`,C("extra")),key:"extra",style:O("extra")},s)]:[n,I,Se(s,{key:"extra"})]);return P?u.createElement(ye,{ref:e,flex:1,style:z},L):L}),Z=we;Z.Meta=Le;const Me=t=>{const{listBorderedCls:e,componentCls:i,paddingLG:n,margin:a,itemPaddingSM:s,itemPaddingLG:p,marginLG:y,borderRadiusLG:m}=t;return{[e]:{border:`${l(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:m,[`${i}-header,${i}-footer,${i}-item`]:{paddingInline:n},[`${i}-pagination`]:{margin:`${l(a)} ${l(y)}`}},[`${e}${i}-sm`]:{[`${i}-item,${i}-header,${i}-footer`]:{padding:s}},[`${e}${i}-lg`]:{[`${i}-item,${i}-header,${i}-footer`]:{padding:p}}}},je=t=>{const{componentCls:e,screenSM:i,screenMD:n,marginLG:a,marginSM:s,margin:p}=t;return{[`@media screen and (max-width:${n}px)`]:{[e]:{[`${e}-item`]:{[`${e}-item-action`]:{marginInlineStart:a}}},[`${e}-vertical`]:{[`${e}-item`]:{[`${e}-item-extra`]:{marginInlineStart:a}}}},[`@media screen and (max-width: ${i}px)`]:{[e]:{[`${e}-item`]:{flexWrap:"wrap",[`${e}-action`]:{marginInlineStart:s}}},[`${e}-vertical`]:{[`${e}-item`]:{flexWrap:"wrap-reverse",[`${e}-item-main`]:{minWidth:t.contentWidth},[`${e}-item-extra`]:{margin:`auto auto ${l(p)}`}}}}}},He=t=>{const{componentCls:e,antCls:i,controlHeight:n,minHeight:a,paddingSM:s,marginLG:p,padding:y,itemPadding:m,colorPrimary:z,itemPaddingSM:x,itemPaddingLG:P,paddingXS:c,margin:N,colorText:$,colorTextDescription:C,motionDurationSlow:O,lineWidth:j,headerBg:B,footerBg:S,emptyTextPadding:I,metaMarginBottom:H,avatarMarginRight:L,titleMarginBottom:f,descriptionFontSize:d}=t;return{[e]:Object.assign(Object.assign({},Ce(t)),{position:"relative","*":{outline:"none"},[`${e}-header`]:{background:B},[`${e}-footer`]:{background:S},[`${e}-header, ${e}-footer`]:{paddingBlock:s},[`${e}-pagination`]:{marginBlockStart:p,[`${i}-pagination-options`]:{textAlign:"start"}},[`${e}-spin`]:{minHeight:a,textAlign:"center"},[`${e}-items`]:{margin:0,padding:0,listStyle:"none"},[`${e}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:m,color:$,[`${e}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${e}-item-meta-avatar`]:{marginInlineEnd:L},[`${e}-item-meta-content`]:{flex:"1 0",width:0,color:$},[`${e}-item-meta-title`]:{margin:`0 0 ${l(t.marginXXS)} 0`,color:$,fontSize:t.fontSize,lineHeight:t.lineHeight,"> a":{color:$,transition:`all ${O}`,"&:hover":{color:z}}},[`${e}-item-meta-description`]:{color:C,fontSize:d,lineHeight:t.lineHeight}},[`${e}-item-action`]:{flex:"0 0 auto",marginInlineStart:t.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${l(c)}`,color:C,fontSize:t.fontSize,lineHeight:t.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${e}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:j,height:t.calc(t.fontHeight).sub(t.calc(t.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:t.colorSplit}}},[`${e}-empty`]:{padding:`${l(y)} 0`,color:C,fontSize:t.fontSizeSM,textAlign:"center"},[`${e}-empty-text`]:{padding:I,color:t.colorTextDisabled,fontSize:t.fontSize,textAlign:"center"},[`${e}-item-no-flex`]:{display:"block"}}),[`${e}-grid ${i}-col > ${e}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:N,paddingBlock:0,borderBlockEnd:"none"},[`${e}-vertical ${e}-item`]:{alignItems:"initial",[`${e}-item-main`]:{display:"block",flex:1},[`${e}-item-extra`]:{marginInlineStart:p},[`${e}-item-meta`]:{marginBlockEnd:H,[`${e}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:f,color:$,fontSize:t.fontSizeLG,lineHeight:t.lineHeightLG}},[`${e}-item-action`]:{marginBlockStart:y,marginInlineStart:"auto","> li":{padding:`0 ${l(y)}`,"&:first-child":{paddingInlineStart:0}}}},[`${e}-split ${e}-item`]:{borderBlockEnd:`${l(t.lineWidth)} ${t.lineType} ${t.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${e}-split ${e}-header`]:{borderBlockEnd:`${l(t.lineWidth)} ${t.lineType} ${t.colorSplit}`},[`${e}-split${e}-empty ${e}-footer`]:{borderTop:`${l(t.lineWidth)} ${t.lineType} ${t.colorSplit}`},[`${e}-loading ${e}-spin-nested-loading`]:{minHeight:n},[`${e}-split${e}-something-after-last-item ${i}-spin-container > ${e}-items > ${e}-item:last-child`]:{borderBlockEnd:`${l(t.lineWidth)} ${t.lineType} ${t.colorSplit}`},[`${e}-lg ${e}-item`]:{padding:P},[`${e}-sm ${e}-item`]:{padding:x},[`${e}:not(${e}-vertical)`]:{[`${e}-item-no-flex`]:{[`${e}-item-action`]:{float:"right"}}}}},Te=t=>({contentWidth:220,itemPadding:`${l(t.paddingContentVertical)} 0`,itemPaddingSM:`${l(t.paddingContentVerticalSM)} ${l(t.paddingContentHorizontal)}`,itemPaddingLG:`${l(t.paddingContentVerticalLG)} ${l(t.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:t.padding,metaMarginBottom:t.padding,avatarMarginRight:t.padding,titleMarginBottom:t.paddingSM,descriptionFontSize:t.fontSize}),We=ve("List",t=>{const e=xe(t,{listBorderedCls:`${t.componentCls}-bordered`,minHeight:t.controlHeightLG});return[He(e),Me(e),je(e)]},Te);var Ge=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)e.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(i[n[a]]=t[n[a]]);return i};function Re(t,e){const{pagination:i=!1,prefixCls:n,bordered:a=!1,split:s=!0,className:p,rootClassName:y,style:m,children:z,itemLayout:x,loadMore:P,grid:c,dataSource:N=[],size:$,header:C,footer:O,loading:j=!1,rowKey:B,renderItem:S,locale:I}=t,H=Ge(t,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),L=i&&typeof i=="object"?i:{},[f,d]=r.useState(L.defaultCurrent||1),[b,k]=r.useState(L.defaultPageSize||10),{getPrefixCls:ee,direction:te,className:ie,style:ne}=be("list"),{renderEmpty:R}=r.useContext(D),ae={current:1,total:0,position:"bottom"},J=o=>(h,E)=>{var X;d(h),k(E),i&&((X=i==null?void 0:i[o])===null||X===void 0||X.call(i,h,E))},re=J("onChange"),oe=J("onShowSizeChange"),le=(o,h)=>{if(!S)return null;let E;return typeof B=="function"?E=B(o):B?E=o[B]:E=o.key,E||(E=`list-item-${h}`),r.createElement(r.Fragment,{key:E},S(o,h))},se=!!(P||i||O),g=ee("list",n),[ce,de,me]=We(g);let w=j;typeof w=="boolean"&&(w={spinning:w});const _=!!(w!=null&&w.spinning),ge=Ee($);let T="";switch(ge){case"large":T="lg";break;case"small":T="sm";break}const pe=M(g,{[`${g}-vertical`]:x==="vertical",[`${g}-${T}`]:T,[`${g}-split`]:s,[`${g}-bordered`]:a,[`${g}-loading`]:_,[`${g}-grid`]:!!c,[`${g}-something-after-last-item`]:se,[`${g}-rtl`]:te==="rtl"},ie,p,y,de,me),v=ze(ae,{total:N.length,current:f,pageSize:b},i||{}),$e=Math.ceil(v.total/v.pageSize);v.current=Math.min(v.current,$e);const q=i&&r.createElement("div",{className:M(`${g}-pagination`)},r.createElement(Pe,Object.assign({align:"end"},v,{onChange:re,onShowSizeChange:oe})));let A=Q(N);i&&N.length>(v.current-1)*v.pageSize&&(A=Q(N).splice((v.current-1)*v.pageSize,v.pageSize));const fe=Object.keys(c||{}).some(o=>["xs","sm","md","lg","xl","xxl"].includes(o)),K=Ne(fe),W=r.useMemo(()=>{for(let o=0;o<U.length;o+=1){const h=U[o];if(K[h])return h}},[K]),ue=r.useMemo(()=>{if(!c)return;const o=W&&c[W]?c[W]:c.column;if(o)return{width:`${100/o}%`,maxWidth:`${100/o}%`}},[JSON.stringify(c),W]);let V=_&&r.createElement("div",{style:{minHeight:53}});if(A.length>0){const o=A.map(le);V=c?r.createElement(Oe,{gutter:c.gutter},r.Children.map(o,h=>r.createElement("div",{key:h==null?void 0:h.key,style:ue},h))):r.createElement("ul",{className:`${g}-items`},o)}else!z&&!_&&(V=r.createElement("div",{className:`${g}-empty-text`},(I==null?void 0:I.emptyText)||(R==null?void 0:R("List"))||r.createElement(Be,{componentName:"List"})));const G=v.position,he=r.useMemo(()=>({grid:c,itemLayout:x}),[JSON.stringify(c),x]);return ce(r.createElement(F.Provider,{value:he},r.createElement("div",Object.assign({ref:e,style:Object.assign(Object.assign({},ne),m),className:pe},H),(G==="top"||G==="both")&&q,C&&r.createElement("div",{className:`${g}-header`},C),r.createElement(Ie,Object.assign({},w),V,z),O&&r.createElement("div",{className:`${g}-footer`},O),P||(G==="bottom"||G==="both")&&q)))}const _e=r.forwardRef(Re),Ae=_e;Ae.Item=Z;export{Ae as L};
