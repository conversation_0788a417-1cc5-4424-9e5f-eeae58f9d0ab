/**
 * Authentication Configuration for All Deployment Scenarios
 */

/**
 * Get authentication configuration based on environment
 * @returns {Object} Authentication configuration object
 */
export const getAuthConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isNgrokEnabled = process.env.NGROK_ENABLED === 'true';
  const isUnifiedContainer = process.env.DOCKER_ENV === 'true';
  const isLocalDevelopment = !isProduction && !process.env.DOCKER_ENV;

  console.log('🔧 Authentication Environment Detection:', {
    NODE_ENV: process.env.NODE_ENV,
    DOCKER_ENV: process.env.DOCKER_ENV,
    NGROK_ENABLED: process.env.NGROK_ENABLED,
    isProduction,
    isNgrokEnabled,
    isUnifiedContainer,
    isLocalDevelopment
  });

  return {
    // Environment flags
    isProduction,
    isNgrokEnabled,
    isUnifiedContainer,
    isLocalDevelopment,

    // JWT Configuration
    jwt: {
      secret: process.env.JWT_SECRET,
      expire: process.env.JWT_EXPIRE || '8h',
      cookieExpire: parseInt(process.env.JWT_COOKIE_EXPIRE || '7')
    },

    // Cookie Configuration - More flexible for dual environment support
    cookie: {
      httpOnly: true,
      secure: false,        // Will be overridden per request based on context
      sameSite: 'lax',      // Default to lax, will be adjusted per request
      domain: undefined,    // Let browser handle domain automatically
      path: '/'
    },

    // CORS Configuration - Enhanced for dual environment support
    cors: {
      allowedOrigins: [
        'http://localhost:5173', // Vite dev server
        'http://localhost:5000', // Unified container
        'http://127.0.0.1:5000', // Alternative localhost
        'http://127.0.0.1:5173', // Alternative localhost dev
        'http://frontend:5173',  // Docker internal
        'http://locql-frontend:5173', // Docker compose
        'https://eternal-friendly-chigger.ngrok-free.app', // Always include ngrok
        ...(process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : [])
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'x-auth-token',
        'withcredentials',
        // Standard authentication headers
        'x-user-id',
        'x-user-email',
        'x-user-role',
        // Ngrok headers
        'x-forwarded-host',
        'x-forwarded-proto',
        'x-forwarded-for'
      ]
    },

    // Session Configuration
    session: {
      secret: process.env.SESSION_SECRET || 'default-session-secret-change-in-production',
      trustProxy: process.env.TRUST_PROXY === 'true' || isUnifiedContainer || isNgrokEnabled
    },

    // Database Configuration
    database: {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'root',
      database: process.env.DB_NAME || 'Testingarea51'
    }
  };
};

/**
 * Get cookie options for JWT token based on request context
 * @param {Object} req - Express request object (optional)
 * @returns {Object} Cookie options
 */
export const getCookieOptions = (req = null) => {
  const config = getAuthConfig();

  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + config.jwt.cookieExpire);

  // Determine if this is a localhost request
  const isLocalhostRequest = req && (
    req.get('host')?.includes('localhost') ||
    req.get('host')?.includes('127.0.0.1') ||
    req.get('origin')?.includes('localhost') ||
    req.get('origin')?.includes('127.0.0.1') ||
    req.get('referer')?.includes('localhost') ||
    req.get('referer')?.includes('127.0.0.1')
  );

  // Check if request is coming from ngrok tunnel
  const isNgrokRequest = req && (
    req.get('host')?.includes('ngrok-free.app') ||
    req.get('origin')?.includes('ngrok-free.app') ||
    req.get('referer')?.includes('ngrok-free.app') ||
    req.headers['x-forwarded-host']?.includes('ngrok-free.app')
  );

  console.log('🍪 Cookie context detection:', {
    host: req?.get('host'),
    origin: req?.get('origin'),
    referer: req?.get('referer'),
    forwardedHost: req?.headers['x-forwarded-host'],
    isLocalhostRequest,
    isNgrokRequest
  });

  // For dual environment support, always use localhost-compatible settings
  // This ensures cookies work for both localhost:5000 and ngrok tunnel
  if (process.env.DUAL_ENV_SUPPORT === 'true') {
    console.log('🍪 Using dual environment cookie settings (localhost-compatible)');
    return {
      expires: expiresAt,
      httpOnly: true,
      secure: false,        // HTTP for localhost compatibility
      sameSite: 'lax',      // Lax for cross-domain compatibility
      domain: undefined,    // No domain restriction
      path: '/'
    };
  }

  // Override cookie settings for localhost requests
  if (isLocalhostRequest) {
    console.log('🍪 Detected localhost request - using localhost cookie settings');
    return {
      expires: expiresAt,
      httpOnly: true,
      secure: false,        // HTTP is fine for localhost
      sameSite: 'lax',      // Lax for same-site requests
      domain: undefined,    // No domain restriction for localhost
      path: '/'
    };
  }

  // Override cookie settings for ngrok requests
  if (isNgrokRequest) {
    console.log('🍪 Detected ngrok request - using ngrok cookie settings');
    return {
      expires: expiresAt,
      httpOnly: true,
      secure: true,         // HTTPS required for ngrok
      sameSite: 'lax',      // Lax works better than 'none' for ngrok
      domain: undefined,    // Let browser handle domain automatically
      path: '/'
    };
  }

  // Use default configuration for ngrok/production
  return {
    expires: expiresAt,
    ...config.cookie
  };
};

/**
 * Set authentication cookie with dual environment support
 * @param {Object} res - Express response object
 * @param {string} token - JWT token to set
 * @param {Object} req - Express request object (optional)
 */
export const setAuthCookie = (res, token, req = null) => {
  const cookieOptions = getCookieOptions(req);

  console.log('🍪 Setting authentication cookie with options:', cookieOptions);

  // Set the main authentication cookie
  res.cookie('token', token, cookieOptions);

  // For dual environment support, also set a backup cookie with different settings
  if (process.env.DUAL_ENV_SUPPORT === 'true') {
    console.log('🍪 Setting backup cookie for dual environment support');

    // Set a backup cookie with more permissive settings for cross-environment access
    const backupOptions = {
      ...cookieOptions,
      secure: false,      // Always HTTP for localhost compatibility
      sameSite: 'lax',    // Lax for better cross-domain support
      domain: undefined   // No domain restriction
    };

    res.cookie('token_backup', token, backupOptions);
  }
};

/**
 * Clear authentication cookies
 * @param {Object} res - Express response object
 * @param {Object} req - Express request object (optional)
 */
export const clearAuthCookies = (res, req = null) => {
  console.log('🍪 Clearing authentication cookies');

  // Clear cookies with multiple option combinations to ensure complete removal
  // This is necessary because cookie clearing must match the exact options used when setting

  // Option 1: Clear with current context options
  const cookieOptions = getCookieOptions(req);
  res.clearCookie('token', cookieOptions);

  // Option 2: Clear with localhost-compatible options (for dual environment)
  const localhostOptions = {
    httpOnly: true,
    secure: false,
    sameSite: 'lax',
    domain: undefined,
    path: '/'
  };
  res.clearCookie('token', localhostOptions);

  // Option 3: Clear with ngrok-compatible options
  const ngrokOptions = {
    httpOnly: true,
    secure: true,
    sameSite: 'lax',
    domain: undefined,
    path: '/'
  };
  res.clearCookie('token', ngrokOptions);

  // Clear backup cookie if dual environment support is enabled
  if (process.env.DUAL_ENV_SUPPORT === 'true') {
    console.log('🍪 Clearing backup cookie for dual environment support');

    // Clear backup cookie with multiple option combinations
    res.clearCookie('token_backup', localhostOptions);
    res.clearCookie('token_backup', ngrokOptions);
    res.clearCookie('token_backup', cookieOptions);
  }

  console.log('🍪 Authentication cookies cleared with multiple option combinations');
};

/**
 * Validate authentication configuration
 * @returns {Object} Validation result
 */
export const validateAuthConfig = () => {
  const config = getAuthConfig();
  const errors = [];
  const warnings = [];

  // Check required environment variables
  if (!config.jwt.secret) {
    errors.push('JWT_SECRET is required');
  }

  if (!config.database.host) {
    errors.push('DB_HOST is required');
  }

  // Check security warnings
  if (config.isProduction && config.session.secret.includes('default')) {
    warnings.push('Using default session secret in production');
  }

  if (config.isProduction && !config.cookie.secure && config.isNgrokEnabled) {
    warnings.push('Secure cookies should be enabled in production with ngrok tunnel');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    config
  };
};

export default {
  getAuthConfig,
  getCookieOptions,
  setAuthCookie,
  clearAuthCookies,
  validateAuthConfig
};
