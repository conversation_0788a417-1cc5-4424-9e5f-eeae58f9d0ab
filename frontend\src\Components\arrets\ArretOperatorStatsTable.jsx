import React, { memo } from 'react';
import { Table, Card, Typography, Tag, Progress, Space } from 'antd';
import { UserOutlined, ToolOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';

const { Text, Title } = Typography;

const ArretOperatorStatsTable = memo(() => {
  const { operatorStats = [], loading } = useArretQueuedContext();

  const columns = [
    {
      title: 'Opérateur',
      dataIndex: 'operator',
      key: 'operator',
      render: (text) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          <Text strong>{text || 'Non assigné'}</Text>
        </Space>
      ),
    },
    {
      title: 'Interventions',
      dataIndex: 'interventions',
      key: 'interventions',
      render: (value) => (
        <Space>
          <ToolOutlined style={{ color: '#52c41a' }} />
          <Text>{value || 0}</Text>
        </Space>
      ),
      sorter: (a, b) => (a.interventions || 0) - (b.interventions || 0),
    },
    {
      title: 'Temps Total (min)',
      dataIndex: 'totalTime',
      key: 'totalTime',
      render: (value) => (
        <Space>
          <ClockCircleOutlined style={{ color: '#faad14' }} />
          <Text>{value || 0} min</Text>
        </Space>
      ),
      sorter: (a, b) => (a.totalTime || 0) - (b.totalTime || 0),
    },
    {
      title: 'Temps Moyen (min)',
      dataIndex: 'avgTime',
      key: 'avgTime',
      render: (value) => (
        <Text type="secondary">{value ? value.toFixed(1) : '0.0'} min</Text>
      ),
      sorter: (a, b) => (a.avgTime || 0) - (b.avgTime || 0),
    },
    {
      title: 'Efficacité',
      key: 'efficiency',
      render: (_, record) => {
        const maxTime = Math.max(...operatorStats.map(op => op.totalTime || 0));
        const efficiency = maxTime > 0 ? ((record.totalTime || 0) / maxTime) * 100 : 0;
        
        let color = '#52c41a'; // Green
        if (efficiency > 75) color = '#f5222d'; // Red (more time = less efficient)
        else if (efficiency > 50) color = '#faad14'; // Orange
        
        return (
          <Progress 
            percent={efficiency} 
            size="small" 
            strokeColor={color}
            format={percent => `${percent.toFixed(0)}%`}
          />
        );
      },
    },
  ];

  // Process operator stats data
  const processedData = operatorStats.map((item, index) => ({
    key: index,
    operator: item.operator || item.Regleur_Prenom || 'Non assigné',
    interventions: item.interventions || item.count || 0,
    totalTime: item.totalTime || item.total_duration || 0,
    avgTime: item.avgTime || (item.total_duration && item.count ? item.total_duration / item.count : 0),
  }));

  return (
    <Card 
      title={
        <Space>
          <UserOutlined />
          Statistiques des Opérateurs
        </Space>
      }
      bordered={false}
    >
      <Table
        columns={columns}
        dataSource={processedData}
        loading={loading}
        pagination={{
          pageSize: 8,
          showSizeChanger: false,
          showTotal: (total) => `Total ${total} opérateurs`,
        }}
        size="middle"
        bordered
      />
    </Card>
  );
});

ArretOperatorStatsTable.displayName = 'ArretOperatorStatsTable';

export default ArretOperatorStatsTable;
