/**
 * ngrokWebSocketConfig.js
 * Configuration for WebSocket connections through ngrok
 */
import { WebSocketServer } from "ws";
import { WebSocket } from "ws";
import db from "../db.js";

// Store active WebSocket connections
const ngrokClients = new Set();

/**
 * Initialize WebSocket server for ngrok connections
 * This function creates a WebSocket server that handles connections through ngrok
 * @param {Object} server - HTTP server instance
 * @param {string} ngrokUrl - The ngrok URL to use (without protocol)
 * @returns {WebSocketServer} The WebSocket server instance
 */
const initNgrokWebSocketServer = (server, ngrokUrl = "charming-hermit-intense.ngrok-free.app") => {
  // Create a new WebSocket server
  const wss = new WebSocketServer({ noServer: true });

  console.log(`Initializing ngrok WebSocket server for ${ngrokUrl}`);

  // Handle upgrade requests
  server.on("upgrade", (request, socket, head) => {
    // Check if the request is for our machine data WebSocket endpoint
    if (request.url === "/api/machine-data-ws") {
      // Extract the host from the request headers
      const host = request.headers.host;

      console.log(`WebSocket upgrade request from host: ${host}`);

      // Handle the upgrade for both local and ngrok connections
      wss.handleUpgrade(request, socket, head, (ws) => {
        wss.emit("connection", ws, request);
      });
    }
  });

  // Handle new connections
  wss.on("connection", (ws, request) => {
    const clientIp = request.socket.remoteAddress;
    console.log(`Machine data WebSocket client connected from ${clientIp}`);

    // Add the client to our set of connected clients
    ngrokClients.add(ws);

    // Send initial machine data
    sendInitialMachineData(ws);

    // Set up a ping interval to keep the connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      }
    }, 30000);

    // Handle incoming messages
    ws.on("message", (message) => {
      try {
        const data = JSON.parse(message);
        console.log("Received message from client:", data);

        // Handle client messages
        if (data.type === "requestUpdate") {
          sendInitialMachineData(ws);
        } else if (data.type === "ping") {
          // Respond to ping with pong
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: "pong" }));
          }
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    });

    // Handle connection close
    ws.on("close", (code, reason) => {
      // Remove the client when connection is closed
      ngrokClients.delete(ws);
      clearInterval(pingInterval);
      console.log(`Machine data WebSocket client disconnected. Code: ${code}, Reason: ${reason || 'No reason provided'}`);
    });

    // Handle errors
    ws.on("error", (error) => {
      console.error("WebSocket error:", error);
      ngrokClients.delete(ws);
      clearInterval(pingInterval);
    });
  });

  return wss;
};

/**
 * Send initial machine data to a client
 * @param {WebSocket} ws - The WebSocket client
 */
const sendInitialMachineData = async (ws) => {
  try {
    // Get machine data from database
    const [machineData] = await db.query(`
      SELECT
        id,
        Machine_Name,
        Ordre_Fabrication,
        Article,
        Quantite_Planifier,
        Quantite_Bon,
        Quantite_Rejet,
        Poids_Purge,
        Stop_Time,
        Regleur_Prenom,
        Etat,
        Code_arret,
        TRS,
        cycle,
        Poid_unitaire,
        cycle_theorique,
        empreint
      FROM real_time_table
    `);

    // Get active sessions
    const [activeSessions] = await db.query(`
      SELECT *
      FROM machine_sessions
      WHERE session_end IS NULL
    `);

    // Get side card data
    const [sideCardData] = await db.query(`
      SELECT *
      FROM sidecards
      LIMIT 1
    `);

    // Get daily stats
    const [dailyStats] = await db.query(`
      SELECT *
      FROM daily_stats
      ORDER BY date DESC
      LIMIT 7
    `);

    // Send the data to the client
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: "initialData",
        machineData,
        activeSessions,
        sideCardData: sideCardData[0] || {},
        dailyStats
      }));
    }
  } catch (error) {
    console.error("Error fetching initial machine data:", error);
  }
};

/**
 * Broadcast machine data updates to all connected clients
 * @param {Object} data - The data to broadcast
 */
const broadcastMachineUpdate = (data) => {
  ngrokClients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({
        type: "update",
        data
      }));
    }
  });
};

/**
 * Broadcast session updates to all connected clients
 * @param {Object} sessionData - The session data to broadcast
 * @param {string} updateType - The type of update ('created', 'updated', or 'stopped')
 */
const broadcastSessionUpdate = (sessionData, updateType) => {
  ngrokClients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({
        type: "sessionUpdate",
        updateType, // 'created', 'updated', or 'stopped'
        sessionData
      }));
    }
  });
};

/**
 * Get the number of connected clients
 * @returns {number} The number of connected clients
 */
const getConnectedClientCount = () => {
  return ngrokClients.size;
};

export {
  initNgrokWebSocketServer,
  broadcastMachineUpdate,
  broadcastSessionUpdate,
  getConnectedClientCount
};
