import{a6 as E,a5 as y,j as g,u as v,R as e,d as R,g as s,T as h,ab as z}from"./index-O2xm1U_Z.js";import{R as x}from"./index-DGvU15vp.js";import{R as T}from"./HomeOutlined-v47PeetK.js";import{R as k}from"./ArrowLeftOutlined-BfXR7gC1.js";const{Text:t,Title:C,Paragraph:l}=h,q=({title:i="Accès refusé",subTitle:m="Vous n'avez pas les permissions nécessaires pour accéder à cette page.",status:u="403"})=>{var o,c;const n=E(),d=y(),{darkMode:r}=g(),{user:a}=v(),p=((c=(o=d.state)==null?void 0:o.from)==null?void 0:c.pathname)||"/",f=(a==null?void 0:a.role)||"utilisateur";return e.createElement("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:r?"#141414":"#f0f2f5"}},e.createElement(x,{status:u,icon:e.createElement(z,{style:{fontSize:72,color:"#ff4d4f"}}),title:e.createElement(C,{level:1},i),subTitle:e.createElement("div",null,e.createElement(t,{style:{fontSize:"18px",color:r?"#d9d9d9":"#595959"}},m),e.createElement(l,{style:{marginTop:16}},e.createElement(t,{type:"secondary"},"Vous êtes connecté en tant que ",e.createElement(t,{strong:!0},f)," et vous avez tenté d'accéder à ",e.createElement(t,{code:!0},p))),e.createElement(l,null,e.createElement(t,{type:"secondary"},"Si vous pensez que c'est une erreur, veuillez contacter votre administrateur système."))),extra:e.createElement(R,{size:"middle"},e.createElement(s,{type:"primary",icon:e.createElement(T,null),onClick:()=>n("/home")},"Retour à l'accueil"),e.createElement(s,{icon:e.createElement(k,null),onClick:()=>n(-1)},"Retour"))}))};export{q as default};
