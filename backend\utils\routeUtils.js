/**
 * Utility functions for route handling and standardization
 */
import { body, query, param, validationResult } from 'express-validator';
import { sendValidationError } from './responseUtils.js';
import { buildDateFilter, buildMachineFilter } from './dateUtils.js';
import { executeQuery, buildQuery } from './dbUtils.js';

/**
 * Validates request parameters based on validation chains
 * @param {Array} validations - Array of express-validator validation chains
 * @returns {Function} Express middleware function
 */
export const validate = (validations) => {
  return async (req, res, next) => {
    // Execute all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }
    
    next();
  };
};

/**
 * Common validation chains for date parameters
 */
export const dateValidation = [
  query('date').optional().isDate().withMessage('Date must be in YYYY-MM-DD format'),
  query('dateRangeType').optional().isIn(['day', 'week', 'month']).withMessage('Date range type must be day, week, or month')
];

/**
 * Common validation chains for machine parameters
 */
export const machineValidation = [
  query('machine').optional().isString().withMessage('Machine name must be a string'),
  query('model').optional().isString().withMessage('Machine model must be a string')
];

/**
 * Common validation chains for pagination parameters
 */
export const paginationValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
];

/**
 * Builds a complete query with filters for machine data
 * @param {string} baseQuery - Base SQL query
 * @param {Object} req - Express request object
 * @param {string} dateField - Database field containing the date
 * @param {string} machineField - Database field containing the machine name
 * @param {string} groupBy - GROUP BY clause
 * @param {string} orderBy - ORDER BY clause
 * @param {number} limit - LIMIT clause
 * @returns {Object} Object containing the complete query and parameters
 */
export const buildMachineDataQuery = (baseQuery, req, dateField = 'Date_Insert_Day', machineField = 'Machine_Name', groupBy = '', orderBy = '', limit = 0) => {
  const conditions = [];
  let params = [];
  
  // Add date filter if provided
  if (req.query.date) {
    const dateFilter = buildDateFilter(req.query.date, req.query.dateRangeType, dateField);
    if (dateFilter.condition) {
      conditions.push(dateFilter.condition.replace(/^\s*AND\s*/, '')); // Remove leading AND
      params = params.concat(dateFilter.params);
    }
  }
  
  // Add machine filter if provided
  if (req.query.model || req.query.machine) {
    const machineFilter = buildMachineFilter(req.query.model, req.query.machine, machineField);
    if (machineFilter.condition) {
      conditions.push(machineFilter.condition.replace(/^\s*AND\s*/, '')); // Remove leading AND
      params = params.concat(machineFilter.params);
    }
  }
  
  // Build the complete query
  return buildQuery(baseQuery, conditions, params, groupBy, orderBy, limit);
};

/**
 * Sets appropriate cache headers based on data volatility
 * @param {Response} res - Express response object
 * @param {string} cacheType - Cache type (realtime, short, medium, long)
 * @param {string} etag - Optional ETag value
 * @returns {Response} Express response with cache headers
 */
export const setCacheHeaders = (res, cacheType = 'medium', etag = null) => {
  // Define cache durations based on data volatility
  const cacheDurations = {
    realtime: 'public, max-age=30',         // 30 seconds
    short: 'public, max-age=120',           // 2 minutes
    medium: 'public, max-age=300',          // 5 minutes
    long: 'public, max-age=900',            // 15 minutes
    veryLong: 'public, max-age=1800'        // 30 minutes
  };
  
  // Set Cache-Control header
  res.set('Cache-Control', cacheDurations[cacheType] || cacheDurations.medium);
  
  // Set ETag if provided
  if (etag) {
    res.set('ETag', etag);
  }
  
  return res;
};

/**
 * Handles pagination for database queries
 * @param {Object} req - Express request object
 * @param {string} baseQuery - Base SQL query
 * @param {Array} params - Query parameters
 * @param {string} countField - Field to count for pagination (default: *)
 * @param {string} tableName - Table name for count query
 * @returns {Promise<Object>} Pagination result with data and metadata
 */
export const paginateQuery = async (req, baseQuery, params = [], countField = '*', tableName) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;
  
  // Add pagination to the query
  const paginatedQuery = `${baseQuery} LIMIT ? OFFSET ?`;
  const paginatedParams = [...params, limit, offset];
  
  // Create count query to get total records
  const countQuery = `SELECT COUNT(${countField}) AS total FROM ${tableName}`;
  
  // Execute both queries in parallel
  const [dataResult, countResult] = await Promise.all([
    executeQuery(paginatedQuery, paginatedParams),
    executeQuery(countQuery, [], true)
  ]);
  
  // Check for errors
  if (!dataResult.success || !countResult.success) {
    return {
      success: false,
      error: dataResult.error || countResult.error,
      details: dataResult.details || countResult.details
    };
  }
  
  // Return paginated result
  return {
    success: true,
    data: dataResult.data,
    pagination: {
      total: countResult.data.total,
      page,
      limit,
      pages: Math.ceil(countResult.data.total / limit)
    }
  };
};
