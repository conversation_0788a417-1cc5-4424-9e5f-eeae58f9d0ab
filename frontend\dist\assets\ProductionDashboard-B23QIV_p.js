import{r as d,a as _t,at as X,s as wt,aj as Zt,R as e,f as k,A as Ct,d as J,al as Tt,g as we,S as T,T as ut,e as he,ad as ea,l as ta,E as ae,h as Ue,c as v,b as De,F as $t,au as aa,G as ra,C as ue,O as st,B as Yt,m as vt,i as na}from"./index-N0wOiMt6.js";import{t as oa,n as oe,R as la,S as ia}from"./dataUtils-Dps55uI1.js";import{d as A}from"./dayjs.min-BHt7dFLo.js";import"./fr-DC3HkAP8.js";import{i as sa,G as ca}from"./GlobalSearchModal-BvgW-kQU.js";import{w as da,c as ua,R as ct}from"./DownloadOutlined-C8TU0wLq.js";import{R as Ot}from"./CheckCircleOutlined-DcDkn_d7.js";import{u as ma}from"./useDailyTableGraphQL-DxxRorF1.js";import{F as fa,S as pa}from"./SearchResultsDisplay-D6-PBd55.js";import{U as me,u as ga,g as ha}from"./UnifiedChartExpansion-DA5Tm2-X.js";import{c as qe,d as Ge,a as dt,f as Se}from"./numberFormatter-CKFvf91F.js";import{R as ya,a as Ea}from"./RiseOutlined-B8NfZNNm.js";import{R as Mt}from"./DashboardOutlined-CBEyWGTp.js";import{R as Da}from"./ClockCircleOutlined-C6SZLNSK.js";import{R as xt}from"./ThunderboltOutlined-Dz7LyqJg.js";import{R as Sa}from"./CloseCircleOutlined-kKLfKks7.js";import{R as It}from"./ToolOutlined-C50QNs7D.js";import{P as be}from"./progress-CyD0QBQj.js";import{R as zt}from"./LineChartOutlined-2kYZXigx.js";import{R as ba}from"./CalendarOutlined-BxlyaoqS.js";import{u as Re,g as Ra,a as qt}from"./chartColors-CNaGTE23.js";import{R as fe,B as je,C as le,X as ie,Y as se,T as re,a as ne,g as Be,P as _a,e as Ca,f as Ta,L as He,d as We,h as Ma,i as L}from"./PieChart-BZME-zsX.js";import{R as Nt}from"./InfoCircleOutlined-DImdGCrM.js";import{R as xa}from"./WarningOutlined-5IE-NKcf.js";import{R as yt}from"./BarChartOutlined-DzqCoGDG.js";import{S as Ve}from"./index-BP6n0Cjb.js";import{R as Na}from"./TableOutlined-qwEzR1LV.js";import{R as Pa}from"./SearchOutlined-xbMlVLbw.js";import"./FilePdfOutlined-Ci40U05P.js";import"./index-C2CgWKoY.js";import"./FileTextOutlined-BAhSEapg.js";import"./ExperimentOutlined-J9m82uMz.js";import"./index-Dea_-S4D.js";import"./FilterOutlined-DjPdBmQR.js";import"./index-BCtPda2K.js";import"./ReloadOutlined-DZn6IdM2.js";import"./EyeOutlined-BNZGoZWA.js";import"./FullscreenOutlined--8214N_2.js";import"./CloseOutlined-BCYtRaiR.js";import"./ZoomOutOutlined-CdPjzMfa.js";const Aa=()=>{const[n,i]=d.useState([]),[a,t]=d.useState([]),[r,s]=d.useState([]),[g,u]=d.useState("IPS"),[o,D]=d.useState(""),[C,x]=d.useState(!1),m=d.useCallback(async()=>{try{console.log("Fetching machine models..."),x(!0);const E=await _t.get("/api/machine-models").retry(2);if(E.body){const y=X(E),f=Array.isArray(y)?y.map(S=>S.model||S):[];console.log("Machine models fetched:",f),i(f.length>0?f:["IPS","CCM24"])}else console.log("No machine models returned from API, using defaults"),i(["IPS","CCM24"])}catch(E){console.error("Error loading machine models:",E),i(["IPS","CCM24"])}finally{x(!1)}},[]),h=d.useCallback(async()=>{try{console.log("Fetching machine names..."),x(!0);const E=await _t.get("/api/machine-names").retry(2);if(E.body){const y=X(E);console.log("Machine names fetched:",y),Array.isArray(y)&&y.length>0?(t(y),y.find(S=>S.Machine_Name==="IPS01")&&g==="IPS"&&console.log("Confirmed IPS as default machine model")):(console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}]))}else console.log("No machine names returned from API, using defaults"),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(E){console.error("Error loading machine names:",E),t([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}finally{x(!1)}},[g]),c=E=>{console.log("🔧 [FILTER DEBUG] handleMachineModelChange called with:",E),console.log("🔧 [FILTER DEBUG] Previous selectedMachineModel:",g),u(E),console.log("🔧 [FILTER DEBUG] New selectedMachineModel will be:",E)},p=E=>{console.log("🚗 [FILTER DEBUG] handleMachineChange called with:",E),console.log("🚗 [FILTER DEBUG] Previous selectedMachine:",o),D(E),console.log("🚗 [FILTER DEBUG] New selectedMachine will be:",E)};return d.useEffect(()=>{if(g){const E=a.filter(y=>y.Machine_Name&&y.Machine_Name.startsWith(g));s(E),o&&!E.some(y=>y.Machine_Name===o)&&D("")}else s([]),D("")},[g,a,o]),d.useEffect(()=>{m()},[m]),d.useEffect(()=>{h()},[h]),{machineModels:n,machineNames:a,filteredMachineNames:r,selectedMachineModel:g,selectedMachine:o,loading:C,handleMachineModelChange:c,handleMachineChange:p,fetchMachineModels:m,fetchMachineNames:h,setSelectedMachineModel:u,setSelectedMachine:D}},Ia=n=>n?new Date(n).toISOString().split("T")[0]:null;A.extend(sa);A.extend(da);A.extend(ua);A.locale("fr");const ka=()=>{const[n,i]=d.useState(null),[a,t]=d.useState("day"),[r,s]=d.useState(""),[g,u]=d.useState(!1),o=d.useCallback((h,c)=>{if(!h)return{short:"",full:""};try{const p=A(h);if(!p.isValid())return console.error("Invalid date in formatDateRange:",h),{short:"Date invalide",full:"Date invalide"};if(c==="day")return{short:p.format("DD/MM/YYYY"),full:`le ${p.format("DD MMMM YYYY")}`};if(c==="week"){const E=p.startOf("isoWeek"),y=p.endOf("isoWeek"),f=p.isoWeek();return{short:`S${f} ${p.format("YYYY")}`,full:`Semaine ${f} (du ${E.format("DD MMMM")} au ${y.format("DD MMMM YYYY")})`}}else if(c==="month"){const E=p.format("MMMM"),y=E.charAt(0).toUpperCase()+E.slice(1);return{short:`${y} ${p.format("YYYY")}`,full:`${y} ${p.format("YYYY")}`}}return{short:"",full:""}}catch(p){return console.error("Error in formatDateRange:",p),{short:"Erreur de date",full:"Erreur de date"}}},[]),D=h=>{if(console.log("📅 [FILTER DEBUG] handleDateChange called with:",h),console.log("📅 [FILTER DEBUG] Current dateFilter:",n),console.log("📅 [FILTER DEBUG] Current dateRangeType:",a),!h){console.log("📅 [FILTER DEBUG] Date is null/undefined, resetting filter"),x();return}try{let c=A(h);a==="week"?c=c.startOf("isoWeek"):a==="month"&&(c=c.startOf("month")),i(c);const{full:p}=o(c,a);s(p),u(!0),console.log(`📅 [FILTER DEBUG] Date filter set: ${c.format("YYYY-MM-DD")}, Range type: ${a}`)}catch(c){console.error("Error handling date change:",c);const p=A(h);i(p);const{full:E}=o(p,a);s(E),u(!0)}},C=h=>{if(console.log("📅 [FILTER DEBUG] handleDateRangeTypeChange called with:",h),t(h),n){let c=A(n),p=c;h==="week"?p=c.startOf("isoWeek"):h==="month"&&(p=c.startOf("month")),i(p);const{full:E}=o(p,h);s(E),console.log(`📅 [FILTER DEBUG] Date range type changed to: ${h}, Adjusted date: ${p.format("YYYY-MM-DD")}`)}},x=()=>{i(null),s(""),u(!1)},m=d.useCallback(()=>{const h=new URLSearchParams;if(n)try{const c=Ia(n);c?(h.append("date",c),h.append("dateRangeType",a),console.log(`API request params: date=${c}, dateRangeType=${a}`)):console.error("Failed to format date for API request:",n)}catch(c){console.error("Error building date query params:",c)}return h},[n,a]);return{dateFilter:n,dateRangeType:a,dateRangeDescription:r,dateFilterActive:g,handleDateChange:D,handleDateRangeTypeChange:C,resetDateFilter:x,buildDateQueryParams:m,formatDateRange:o}},wa=({selectedMachineModel:n,selectedMachine:i,dateFilter:a,dateRangeType:t,buildDateQueryParams:r})=>{const s=(O,U)=>{const $=(()=>{if(typeof window<"u"){const B=window.location.origin;return B.includes("ngrok-free.app")||B.includes("ngrok.io")?B:"http://localhost:5000"}return"http://localhost:5000"})();return _t[O](`${$}${U}`).retry(2).withCredentials().timeout(3e4)},[g,u]=d.useState(!1),[o,D]=d.useState([]),[C,x]=d.useState([]),[m,h]=d.useState([]),[c,p]=d.useState([]),[E,y]=d.useState(0),[f,S]=d.useState(0),[b,M]=d.useState([]),[w,_]=d.useState([]),[q,ee]=d.useState([]),[j,W]=d.useState([]),Pe=d.useCallback(()=>{const O=new URLSearchParams;n&&!i?O.append("model",n):i&&O.append("machine",i),O.append("limit","100"),O.append("chartLimit","200"),O.append("page","1");const U=r();if(Object.entries(U).forEach(([B,Z])=>{O.append(B,Z)}),!U.date&&!U.dateRangeType){const B=A().subtract(7,"days").format("YYYY-MM-DD");O.append("date",B),O.append("dateRangeType","week"),O.append("defaultFilter","true")}const $=U.dateRangeType;return $==="month"?O.append("aggregateBy","day"):$==="year"&&O.append("aggregateBy","week"),O.toString()?`?${O.toString()}`:""},[n,i,r]),ye=d.useCallback(()=>{const O=A(),U=[];for(let $=9;$>=0;$--){const B=O.subtract($,"day").format("YYYY-MM-DD");if(!A(B).isValid()){console.error("Invalid date generated:",B);continue}U.push({date:B,good:Math.floor(Math.random()*1e3)+500,reject:Math.floor(Math.random()*100)+10,oee:Math.floor(Math.random()*30)+70,speed:Math.floor(Math.random()*5)+5,Machine_Name:i||(n?`${n}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(Math.random()*3)]})}return U},[i,n]),G=d.useCallback(async()=>{var O,U;if(!n){console.log("No machine model selected, skipping data fetch");return}u(!0);try{const $=Pe();console.log("API query string:",$);const B=await Promise.allSettled([s("get",`/api/testing-chart-production${$}`),s("get","/api/unique-dates-production").catch(()=>({body:[]})),s("get",`/api/sidecards-prod${$}`),s("get",`/api/sidecards-prod-rejet${$}`),s("get",`/api/machine-performance${$}`),s("get",`/api/hourly-trends${$}`),s("get",`/api/machine-oee-trends${$}`),s("get",`/api/speed-trends${$}`),s("get",`/api/shift-comparison${$}`),s("get",`/api/machine-daily-mould${$}`)]),[Z,pe,Ee,te,K,Q,ve,Ae,at,_e]=B;if(Z.status==="fulfilled"&&Z.value.body){const Y=X(Z.value),de=(Array.isArray(Y)?Y:[]).map(oa);D(de)}else console.log("No chart data available"),D([]);if(pe.status==="fulfilled"){const Y=X(pe.value);p(Y||[])}if(Ee.status==="fulfilled"){const Y=X(Ee.value);y(((O=Y[0])==null?void 0:O.goodqty)||0)}else y(0);if(te.status==="fulfilled"){const Y=X(te.value);S(((U=Y[0])==null?void 0:U.rejetqty)||0)}else S(0);if(K.status==="fulfilled"&&K.value.body){const Y=X(K.value);x(Y||[])}else console.log("No machine performance data available"),x([]);if(Q.status==="fulfilled"){const Y=X(Q.value);ee(Y||[])}const rt=ve.status==="fulfilled"&&ve.value.body?X(ve.value).reduce((Y,z)=>(Y[z.date]=parseFloat(z.oee)||0,Y),{}):{},Ke=Ae.status==="fulfilled"&&Ae.value.body?X(Ae.value).reduce((Y,z)=>{const de=parseFloat(z.speed);return!isNaN(de)&&de>0&&(Y[z.date]=de),Y},{}):{},Ie=[...[...new Set([...Object.keys(rt),...Object.keys(Ke)])]].sort((Y,z)=>A(Y).diff(A(z)));let ft=Ie;if(Ie.length>0){const Y=A(Ie[Ie.length-1]);ft=Ie.filter(z=>Y.diff(A(z),"day")<=60)}const ke=ft.map(Y=>({date:Y,oee:rt[Y]||0,speed:Ke[Y]||null})).sort((Y,z)=>A(Y.date).diff(A(z.date)));if(_e&&_e.status==="fulfilled"&&_e.value.body){const Y=X(_e.value);if(Y.length>0)try{const z=Y.map(F=>{const Fe=parseFloat(F.Good_QTY_Day||F.good||0),Je=parseFloat(F.Rejects_QTY_Day||F.reject||0),nt=parseFloat(F.OEE_Day||F.oee||0),ge=parseFloat(F.Speed_Day||F.speed||0),Xe=parseFloat(F.Availability_Rate_Day||F.availability||0),Ze=parseFloat(F.Performance_Rate_Day||F.performance||0),pt=parseFloat(F.Quality_Rate_Day||F.quality||0);let Ce=null;try{const Te=F.Date_Insert_Day||F.date;if(Te)if(A(Te).isValid())Ce=A(Te).format("YYYY-MM-DD");else{const l=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const R of l){const P=A(Te,R);if(P.isValid()){Ce=P.format("YYYY-MM-DD");break}}}Ce||(console.warn(`Invalid date found: ${F.Date_Insert_Day||F.date}, using today's date instead`),Ce=A().format("YYYY-MM-DD"))}catch(Te){console.error("Error parsing date:",Te),Ce=A().format("YYYY-MM-DD")}const Qe=oe(nt),gt=oe(Xe),ht=oe(Ze),ot=oe(pt);return{date:Ce,oee:Qe,speed:isNaN(ge)?0:ge,good:isNaN(Fe)?0:Fe,reject:isNaN(Je)?0:Je,Machine_Name:F.Machine_Name||"N/A",Shift:F.Shift||"N/A",availability:gt,performance:ht,quality:ot}}).sort((F,Fe)=>A(F.date).diff(A(Fe.date)));if(z.some(F=>F.good>0||F.reject>0||F.oee>0||F.speed>0))h(z);else{console.warn("No valid data points found in processed mould data");const F=ye();h(F)}}catch(z){console.error("Error processing mould data:",z),h(ke)}else if(console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback"),ke.length>0)h(ke);else{const z=ye();h(z)}}else if(console.log("Machine daily mould API request failed or returned invalid data"),_e&&_e.status==="rejected"&&console.error("API error:",_e.reason),ke.length>0)h(ke);else{const Y=ye();h(Y),console.log("Using sample data for default dashboard state (IPS model)")}ve.status==="fulfilled"&&M(X(ve.value)||[]),Ae.status==="fulfilled"&&_(X(Ae.value)||[]),at.status==="fulfilled"&&W(X(at.value)||[])}catch($){console.error("Error loading data:",$),y(0),S(0),D([]),x([])}finally{u(!1)}},[n,i,a,t,Pe,ye]),ce=d.useCallback(async()=>{var O,U;try{u(!0);const $=await Promise.allSettled([s("get","/api/sidecards-prod"),s("get","/api/sidecards-prod-rejet")]),[B,Z]=$;if(B.status==="fulfilled"){const pe=X(B.value);y(((O=pe[0])==null?void 0:O.goodqty)||15e3)}else console.error("Failed to fetch good quantity:",B.reason),y(15e3);if(Z.status==="fulfilled"){const pe=X(Z.value);S(((U=pe[0])==null?void 0:U.rejetqty)||750)}else console.error("Failed to fetch rejected quantity:",Z.reason),S(750)}catch($){console.error("Error loading general data:",$),y(15e3),S(750)}finally{u(!1)}},[]),tt=d.useCallback(()=>{let O=0;o.length>0&&(O=o.reduce((te,K)=>{let Q=parseFloat(K.oee||0);return Q=oe(Q),te+Q},0)/o.length);const U=E+f>0?f/(E+f)*100:0,$=E+f>0?E/(E+f)*100:0;let B=0;o.length>0&&(B=o.reduce((te,K)=>{let Q=parseFloat(K.availability||0);return Q=oe(Q),te+Q},0)/o.length);let Z=0;o.length>0&&(Z=o.reduce((te,K)=>{let Q=parseFloat(K.performance||0);return Q=oe(Q),te+Q},0)/o.length);let pe=0;return o.length>0&&(pe=o.reduce((te,K)=>{let Q=parseFloat(K.quality||0);return Q=oe(Q),te+Q},0)/o.length),{avgTRS:O,rejectRate:U,qualityRate:$,avgAvailability:B,avgPerformance:Z,avgQuality:pe}},[o,E,f]);return d.useEffect(()=>{console.log("🔄 Data fetch effect triggered:",{selectedMachineModel:n,selectedMachine:i,dateFilter:a,dateRangeType:t}),n?(console.log("📊 Fetching production data for model:",n),G()):(console.log("📊 Fetching general data (no machine model selected)"),ce())},[n,i,a,t,G,ce]),{loading:g,chartData:o,machinePerformance:C,mergedData:m,uniqueDates:c,goodQty:E,rejetQty:f,oeeTrends:b,speedTrends:w,hourlyTrends:q,shiftComparison:j,fetchData:G,fetchGeneralData:ce,calculateStatistics:tt}},Ya=n=>{const[i,a]=d.useState({isCoordinated:!1,lastSync:null,pendingFilters:0,dataSourceHealth:"unknown",filterQueue:[]}),[t,r]=d.useState(!1),s=d.useRef(null),g=d.useRef(null),{selectedMachineModel:u,selectedMachine:o,dateFilter:D,dateRangeType:C,loading:x}=n||{},m=d.useCallback(async()=>{var p,E,y,f,S,b,M,w;if(t){console.log("🔄 [FILTER COORDINATOR] Filter processing already in progress, queuing request");return}r(!0),console.log("🔄 [FILTER COORDINATOR] Starting filter coordination:",{model:u,machine:o,date:D?typeof D=="object"?(p=D.format)==null?void 0:p.call(D,"YYYY-MM-DD"):D:null,dateRangeType:C});try{const q=await(await fetch("/api/graphql",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:`
            query TestFilterCoordination($filters: ProductionFilters) {
              enhancedGetProductionSidecards(filters: $filters) {
                dataSource
                goodqty
                rejetqty
              }
            }
          `,variables:{filters:{dateRangeType:C,model:u||void 0,machine:o||void 0,date:D?typeof D=="object"?(E=D.format)==null?void 0:E.call(D,"YYYY-MM-DD"):D:void 0}}})})).json();if(q.errors)return console.warn("🔄 [FILTER COORDINATOR] GraphQL errors during coordination test:",q.errors),a(W=>({...W,isCoordinated:!1,lastSync:new Date().toISOString(),dataSourceHealth:"error"})),!1;const ee=((f=(y=q.data)==null?void 0:y.enhancedGetProductionSidecards)==null?void 0:f.dataSource)||"unknown",j=(((b=(S=q.data)==null?void 0:S.enhancedGetProductionSidecards)==null?void 0:b.goodqty)||0)>0||(((w=(M=q.data)==null?void 0:M.enhancedGetProductionSidecards)==null?void 0:w.rejetqty)||0)>0;return a(W=>({...W,isCoordinated:!0,lastSync:new Date().toISOString(),pendingFilters:0,dataSourceHealth:j?"healthy":"no-data",filterQueue:[]})),console.log("✅ [FILTER COORDINATOR] Coordination successful:",{dataSource:ee,hasData:j,timestamp:new Date().toISOString()}),(u||o||D)&&wt.success({message:"Filters Applied",description:`Data updated using ${ee} source`,icon:React.createElement(Ot,{style:{color:"#52c41a"}}),duration:2,placement:"bottomRight"}),!0}catch(_){return console.error("❌ [FILTER COORDINATOR] Coordination failed:",_),a(q=>({...q,isCoordinated:!1,lastSync:new Date().toISOString(),dataSourceHealth:"error"})),wt.error({message:"Filter Coordination Failed",description:"Unable to sync filters with backend. Data may be stale.",icon:React.createElement(Zt,{style:{color:"#ff4d4f"}}),duration:4,placement:"bottomRight"}),!1}finally{r(!1)}},[u,o,D,C,t]);d.useEffect(()=>{if(x){console.log("🔄 [FILTER COORDINATOR] Skipping coordination - data is loading");return}return s.current&&clearTimeout(s.current),a(p=>({...p,pendingFilters:p.pendingFilters+1})),s.current=setTimeout(()=>{m()},500),()=>{s.current&&clearTimeout(s.current)}},[u,o,D,C,x,m]),d.useEffect(()=>{const p=async()=>{var E,y;if(t){console.log("🏥 [FILTER COORDINATOR] Skipping health check - filters are processing");return}try{const b=((y=(E=(await(await fetch("/api/graphql",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:`
              query HealthCheck {
                enhancedGetProductionSidecards(filters: { dateRangeType: "day" }) {
                  dataSource
                }
              }
            `})})).json()).data)==null?void 0:E.enhancedGetProductionSidecards)==null?void 0:y.dataSource)||"unknown";a(M=>({...M,dataSourceHealth:b==="elasticsearch"?"elasticsearch":"mysql"})),console.log("🏥 [FILTER COORDINATOR] Health check completed:",{dataSource:b})}catch(f){console.warn("🏥 [FILTER COORDINATOR] Health check failed:",f),a(S=>({...S,dataSourceHealth:"error"}))}};return p(),g.current=setInterval(p,6e4),()=>{g.current&&clearInterval(g.current)}},[t]);const h=d.useCallback(async()=>(console.log("🔄 [FILTER COORDINATOR] Manual coordination triggered"),await m()),[m]),c=d.useCallback(()=>{const{isCoordinated:p,lastSync:E,pendingFilters:y,dataSourceHealth:f}=i;let S="unknown",b="Filter coordination status unknown",M="default";return t?(S="processing",b=`Processing ${y} filter(s)...`,M="processing"):p&&f!=="error"?(S="coordinated",b=`Filters synchronized (${f})`,M="success"):y>0?(S="pending",b=`${y} filter(s) pending coordination`,M="warning"):f==="error"&&(S="error",b="Coordination error - check connection",M="error"),{status:S,message:b,color:M,lastSync:E,dataSourceHealth:f,isProcessing:t}},[i,t]);return d.useEffect(()=>()=>{s.current&&clearTimeout(s.current),g.current&&clearInterval(g.current)},[]),{coordinationStatus:c(),triggerCoordination:h,isProcessingFilters:t}},jt=d.createContext(),va=({children:n})=>{const i=Aa(),a=ka(),t=wa({selectedMachineModel:i.selectedMachineModel,selectedMachine:i.selectedMachine,dateFilter:a.dateFilter,dateRangeType:a.dateRangeType,buildDateQueryParams:a.buildDateQueryParams}),r=Ya({selectedMachineModel:i.selectedMachineModel,selectedMachine:i.selectedMachine,dateFilter:a.dateFilter,dateRangeType:a.dateRangeType,loading:i.loading||t.loading}),s=t.calculateStatistics(),g=async()=>{console.log("🔄 [PRODUCTION CONTEXT] Resetting all filters"),a.resetDateFilter(),a.setDateRangeType("day"),i.setSelectedMachineModel(""),i.setSelectedMachine(""),setTimeout(()=>{r.triggerCoordination()},100)},u=async()=>{console.log("🔄 [PRODUCTION CONTEXT] Refreshing data with coordination"),t.fetchData(),setTimeout(()=>{r.triggerCoordination()},200)},o={...i,...a,...t,...s,coordinationStatus:r.coordinationStatus,isProcessingFilters:r.isProcessingFilters,resetFilters:g,handleRefresh:u,triggerCoordination:r.triggerCoordination};return e.createElement(jt.Provider,{value:o},n)},Bt=()=>{const n=d.useContext(jt);if(n===void 0)throw new Error("useProduction must be used within a ProductionProvider");return n},{Option:Ft}=Tt,Fa=()=>{const{selectedMachineModel:n,selectedMachine:i,machineModels:a,filteredMachineNames:t,handleMachineModelChange:r,handleMachineChange:s,resetFilters:g,loading:u}=Bt(),[o,D]=d.useState("WAITING"),[C,x]=d.useState(null);d.useEffect(()=>{const p=new Date().toLocaleTimeString();x(p),n||i?(D("ACTIVE"),console.log("🧪 [TEST COORDINATION] Context updated:",{model:n,machine:i,timestamp:p})):(D("IDLE"),console.log("🧪 [TEST COORDINATION] Context cleared:",{timestamp:p}))},[n,i]);const m=p=>{console.log("🧪 [TEST] Model change requested:",p),console.log("🧪 [TEST] Calling context handleMachineModelChange"),D("COORDINATING"),r(p)},h=p=>{console.log("🧪 [TEST] Machine change requested:",p),console.log("🧪 [TEST] Calling context handleMachineChange"),D("COORDINATING"),s(p)},c=()=>{console.log("🧪 [TEST] Reset requested"),console.log("🧪 [TEST] Calling context resetFilters"),D("RESETTING"),g()};return e.createElement("div",{style:{padding:"16px",border:"2px solid #1890ff",borderRadius:"8px",backgroundColor:"#f8f9fa",margin:"16px 0"}},e.createElement("div",{style:{marginBottom:"12px"}},e.createElement("strong",{style:{color:"#1890ff"}},"🧪 Filter Coordination Test"),e.createElement(k,{color:o==="ACTIVE"?"green":o==="COORDINATING"?"orange":o==="RESETTING"?"red":"default",style:{marginLeft:"8px"}},o),C&&e.createElement("span",{style:{fontSize:"12px",marginLeft:"8px",color:"#666"}},"Last update: ",C)),e.createElement("div",{style:{marginBottom:"12px"}},e.createElement("strong",null,"Context State:"),e.createElement("br",null),e.createElement("span",{style:{fontSize:"12px"}},"Model: ",e.createElement("code",null,n||"None"),e.createElement("br",null),"Machine: ",e.createElement("code",null,i||"None"),e.createElement("br",null),"Available Models: ",e.createElement("code",null,(a==null?void 0:a.length)||0),e.createElement("br",null),"Filtered Machines: ",e.createElement("code",null,(t==null?void 0:t.length)||0),e.createElement("br",null),"Loading: ",e.createElement("code",null,u?"Yes":"No"))),o==="WAITING"&&e.createElement(Ct,{message:"Waiting for filter interaction...",type:"info",size:"small",style:{marginBottom:"12px"}}),e.createElement(J,{wrap:!0},e.createElement(Tt,{placeholder:"Select Model",style:{width:150},value:n||void 0,onChange:m,allowClear:!0,loading:u},(a||[]).map(p=>e.createElement(Ft,{key:p,value:p},p))),e.createElement(Tt,{placeholder:"Select Machine",style:{width:150},value:i||void 0,onChange:h,disabled:!n||!(t!=null&&t.length),allowClear:!0,loading:u},(t||[]).map(p=>e.createElement(Ft,{key:p.Machine_Name,value:p.Machine_Name},p.Machine_Name))),e.createElement(we,{onClick:c,disabled:u},"Reset Test")),o==="ACTIVE"&&e.createElement(Ct,{message:"✅ Coordination working - filters are connected to context!",type:"success",size:"small",style:{marginTop:"12px"}}))},La=(n,i,a,t,r,s,g)=>[{title:"Production Totale",value:qe(n,"Pcs"),rawValue:n,suffix:"Pcs",icon:e.createElement(ya,null),color:T.PRIMARY_BLUE,description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:qe(i,"Kg"),rawValue:i,suffix:"Kg",icon:e.createElement(Ea,null),color:T.PRIMARY_BLUE,description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:qe(a,"%"),rawValue:a,suffix:"%",icon:e.createElement(Mt,null),color:T.PRIMARY_BLUE,description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:qe(t,"%"),rawValue:t,suffix:"%",icon:e.createElement(Da,null),color:T.PRIMARY_BLUE,description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:qe(r,"%"),rawValue:r,suffix:"%",icon:e.createElement(xt,null),color:T.PRIMARY_BLUE,description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:qe(s,"%"),rawValue:s,suffix:"%",icon:e.createElement(Sa,null),color:T.PRIMARY_BLUE,description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:qe(g,"%"),rawValue:g,suffix:"%",icon:e.createElement(Ot,null),color:T.PRIMARY_BLUE,description:"Pourcentage de pièces bonnes sur la production totale"}],{Text:Et}=ut,$a=(n,i)=>[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>e.createElement(J,null,e.createElement(It,{style:{color:n[0]}}),e.createElement(Et,{strong:!0},a||"N/A")),sorter:(a,t)=>(a.Machine_Name||"").localeCompare(t.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:a=>{if(!a)return e.createElement(Et,null,"N/A");const t=new Date(a);return e.createElement(Et,null,t.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}))},sorter:(a,t)=>{const r=new Date(a.Date_Insert_Day||0),s=new Date(t.Date_Insert_Day||0);return r-s}},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:a=>e.createElement(k,{color:"green"},Ge(parseFloat(a)||0)," h"),sorter:(a,t)=>(parseFloat(a.Run_Hours_Day)||0)-(parseFloat(t.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:a=>e.createElement(k,{color:"orange"},Ge(parseFloat(a)||0)," h"),sorter:(a,t)=>(parseFloat(a.Down_Hours_Day)||0)-(parseFloat(t.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:a=>e.createElement(k,{color:"green"},dt(parseInt(a)||0)," pcs"),sorter:(a,t)=>(parseInt(a.Good_QTY_Day)||0)-(parseInt(t.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:a=>e.createElement(k,{color:"red"},dt(parseInt(a)||0)," pcs"),sorter:(a,t)=>(parseInt(a.Rejects_QTY_Day)||0)-(parseInt(t.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:a=>e.createElement(k,{color:"blue"},Ge(parseFloat(a)||0)),sorter:(a,t)=>(parseFloat(a.Speed_Day)||0)-(parseFloat(t.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:a=>{const t=i(a);return e.createElement(he,{title:`${Se(t,1)}% de disponibilité`},e.createElement(be,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${Se(r,1)}%`:"0,0%"}))},sorter:(a,t)=>i(a.Availability_Rate_Day)-i(t.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:a=>{const t=i(a);return e.createElement(he,{title:`${t.toFixed(1)}% de performance`},e.createElement(be,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>i(a.Performance_Rate_Day)-i(t.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:a=>{const t=i(a);return e.createElement(he,{title:`${t.toFixed(1)}% de qualité`},e.createElement(be,{percent:t,size:"small",status:t>90?"success":t>80?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>i(a.Quality_Rate_Day)-i(t.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:a=>{const t=i(a);return e.createElement(he,{title:`${t.toFixed(1)}% de TRS`},e.createElement(be,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:r=>typeof r=="number"&&!isNaN(r)?`${r.toFixed(1)}%`:"0.0%"}))},sorter:(a,t)=>i(a.OEE_Day)-i(t.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:a=>e.createElement(k,{color:"blue"},a||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(a,t)=>t.Shift===a},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:a=>e.createElement(k,{color:"purple"},a||"N/A")},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:a=>e.createElement(k,{color:"cyan"},a||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:a=>e.createElement(k,{color:"magenta"},a||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:a=>e.createElement(k,{color:"gold"},a||"N/A")},{title:"Actions",key:"actions",fixed:"right",width:80,render:()=>e.createElement(ea,{menu:{items:[{key:"1",icon:e.createElement(zt,null),label:"Voir tendances"},{key:"2",icon:e.createElement(ta,null),label:"Paramètres"},{key:"3",icon:e.createElement(ct,null),label:"Exporter données"}]},trigger:["click"]},e.createElement(we,{type:"text",icon:e.createElement(la,null)}))}],{Text:Dt}=ut,Oa=(n,i=[])=>{const a=t=>{if(t==null||t==="")return 0;if(typeof t=="number"&&!isNaN(t))return t<=1&&t>0?t*100:t;if(typeof t=="string"){const r=parseFloat(t.replace(",","."));if(!isNaN(r))return r<=1&&r>0?r*100:r}return 0};return[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:t=>e.createElement(J,null,e.createElement(It,{style:{color:n[0]}}),e.createElement(Dt,{strong:!0},t||"N/A")),filters:Array.from(new Set(i.map(t=>t.Machine_Name||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,r)=>r.Machine_Name===t||t==="N/A"&&!r.Machine_Name,sorter:(t,r)=>(t.Machine_Name||"").localeCompare(r.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:t=>{if(!t)return e.createElement(Dt,null,"N/A");const r=new Date(t);return e.createElement(J,null,e.createElement(ba,{style:{color:n[1]}}),e.createElement(Dt,null,r.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})))},sorter:(t,r)=>{const s=new Date(t.Date_Insert_Day||0),g=new Date(r.Date_Insert_Day||0);return s-g},defaultSortOrder:"descend"},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:t=>e.createElement(k,{color:"green"},Ge(parseFloat(t)||0)," h"),sorter:(t,r)=>(parseFloat(t.Run_Hours_Day)||0)-(parseFloat(r.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:t=>e.createElement(k,{color:"orange"},Ge(parseFloat(t)||0)," h"),sorter:(t,r)=>(parseFloat(t.Down_Hours_Day)||0)-(parseFloat(r.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:t=>e.createElement(k,{color:"green"},dt(parseInt(t)||0)," pcs"),sorter:(t,r)=>(parseInt(t.Good_QTY_Day)||0)-(parseInt(r.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:t=>e.createElement(k,{color:"red"},dt(parseInt(t)||0)," pcs"),sorter:(t,r)=>(parseInt(t.Rejects_QTY_Day)||0)-(parseInt(r.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:t=>e.createElement(k,{color:"blue"},Ge(parseFloat(t)||0)),sorter:(t,r)=>(parseFloat(t.Speed_Day)||0)-(parseFloat(r.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de disponibilité`},e.createElement(be,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${Se(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Availability_Rate_Day)-a(r.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de performance`},e.createElement(be,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${Se(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Performance_Rate_Day)-a(r.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de qualité`},e.createElement(be,{percent:r,size:"small",status:r>90?"success":r>80?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${Se(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.Quality_Rate_Day)-a(r.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:t=>{const r=a(t);return e.createElement(he,{title:`${Se(r,1)}% de TRS`},e.createElement(be,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:s=>typeof s=="number"&&!isNaN(s)?`${Se(s,1)}%`:"0,0%"}))},sorter:(t,r)=>a(t.OEE_Day)-a(r.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:t=>e.createElement(k,{color:"blue"},t||"N/A"),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(t,r)=>r.Shift===t},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:t=>e.createElement(k,{color:"purple"},t||"N/A"),filters:Array.from(new Set(i.map(t=>t.Part_Number||"N/A"))).map(t=>({text:t,value:t})),onFilter:(t,r)=>r.Part_Number===t||t==="N/A"&&!r.Part_Number},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:t=>e.createElement(k,{color:"cyan"},t||"N/A")},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:t=>e.createElement(k,{color:"magenta"},t||"N/A")},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:t=>e.createElement(k,{color:"gold"},t||"N/A")}]},kt=(n,i=!1,a=0)=>{if(!n)return"N/A";try{const t=A(n);return t.isValid()?i?a>100?t.format("MM/DD"):a>50?t.format("MM/DD/YY"):t.format("DD/MM/YYYY"):a>30?t.format("MM/DD"):t.format("DD/MM"):"N/A"}catch(t){return console.error("Error formatting date:",t),"N/A"}},Ye=[T.PRIMARY_BLUE,T.SECONDARY_BLUE,T.CHART_TERTIARY,T.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],Pt=d.memo(({data:n,title:i,dataKey:a,color:t,label:r="Quantité",tooltipLabel:s="Quantité",isKg:g=!1,height:u=300,enhanced:o=!1,expanded:D=!1,zoom:C=1,selectedDataPoints:x=[],chartConfig:m={},dimensions:h={},isModal:c=!1})=>{const{charts:p,theme:E}=Ue(),y=Re({charts:p,theme:E});if(!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée disponible"}));const f=y.responsiveContainerProps,S=y.gridConfig,b=y.axisConfig,M=y.tooltipConfig,w=y.legendConfig,_=y.displayConfig,q=y.height||u,ee=y.margins,j=h.margin||ee,W=h.fontSize||(o?14:12),Pe=h.labelAngle||(o?-45:0),ye=h.labelHeight||(o?100:60);return e.createElement(fe,{...f,height:q},e.createElement(je,{data:n,margin:j},e.createElement(le,{...S}),e.createElement(ie,{dataKey:"date",...b,tick:{fill:"#666",fontSize:W},tickFormatter:G=>kt(G,D||o,n.length),interval:m.labelInterval!==void 0?m.labelInterval:o?0:"preserveStartEnd",angle:Pe,textAnchor:Pe!==0?"end":"middle",height:ye,minTickGap:D?5:10}),e.createElement(se,{...b,tick:{fontSize:W},tickFormatter:G=>G.toLocaleString(),label:{value:g?`${r} (kg)`:r,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:W}}}),e.createElement(re,{...M,formatter:G=>{const ce=parseFloat(G);return[isNaN(ce)?"N/A":ce.toLocaleString(),g?`${s} (kg)`:s]},labelFormatter:G=>{try{return G&&A(G).isValid()?`Date: ${A(G).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),(o||p.showLegend)&&e.createElement(ne,{...w}),e.createElement(Be,{dataKey:a,name:s,fill:t,maxBarSize:m.maxBarSize||(o?60:40),radius:o||D?[4,4,0,0]:[0,0,0,0],...y.getBarElementConfig(t)},_.showDataLabels&&e.createElement(Ma,{dataKey:a,position:"top",formatter:G=>{const ce=parseFloat(G);return isNaN(ce)?"":ce.toLocaleString()},style:{fill:"#666",fontSize:"10px"}}))))}),za=d.memo(({data:n,title:i,dataKey:a,color:t,label:r="Quantité",tooltipLabel:s="Quantité",isKg:g=!1,height:u=300,enhanced:o=!1,expanded:D=!1,zoom:C=1,selectedDataPoints:x=[],chartConfig:m={},dimensions:h={},isModal:c=!1})=>{var S;if(i&&i.includes("Temps d'arrêt")&&(n==null?void 0:n.length)>0&&console.log("EnhancedShiftBarChart - Downtime data received:",n.map(b=>({Shift:b.Shift,[a]:b[a]}))),!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée disponible"}));const p=h.margin||(o?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),E=h.fontSize||(o?14:12),y=h.labelAngle||(o?-45:0),f=h.labelHeight||(o?100:60);return e.createElement(fe,{width:"100%",height:u},e.createElement(je,{data:n,margin:p},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Shift",tick:{fill:"#666",fontSize:E},tickFormatter:b=>b||"N/A",interval:m.labelInterval!==void 0?m.labelInterval:o?0:"preserveStartEnd",angle:y,textAnchor:y!==0?"end":"middle",height:f,minTickGap:D?5:10}),e.createElement(se,{tick:{fontSize:E},tickFormatter:b=>b.toLocaleString(),domain:i&&i.includes("Temps d'arrêt")?[0,"dataMax"]:["auto","auto"],label:{value:g?`${r} (kg)`:r,angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:E}}}),e.createElement(re,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:o?14:12},formatter:b=>{const M=parseFloat(b);return[isNaN(M)?"N/A":M.toLocaleString(),g?`${s} (kg)`:s]},labelFormatter:b=>`Équipe: ${b}`}),o&&e.createElement(ne,null),e.createElement(Be,{dataKey:a,name:s,fill:t,maxBarSize:m.maxBarSize||(o?60:40),radius:o||D?[4,4,0,0]:[0,0,0,0],label:(S=enhancedChartConfig.displayConfig)!=null&&S.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Qt=d.memo(({data:n,color:i=Ye[0],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:g=[],chartConfig:u={},dimensions:o={},isModal:D=!1})=>{var y;const{charts:C,theme:x}=Ue(),m=Re({charts:C,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée TRS disponible"}));const h=o.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),c=o.fontSize||(t?14:12),p=o.labelAngle||(t?-45:0),E=o.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(He,{data:n,margin:h},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"date",tick:{fill:"#666",fontSize:c},tickFormatter:f=>kt(f,r||t,n.length),interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:E,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:c},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:c}}}),e.createElement(re,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{const S=parseFloat(f);return[!isNaN(S)?`${S.toFixed(2)}%`:`${f}%`,"TRS"]},labelFormatter:f=>{try{return f&&A(f).isValid()?`Date: ${A(f).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(ne,null),e.createElement(We,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:i},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(y=m.displayConfig)!=null&&y.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Vt=d.memo(({data:n,color:i=Ye[0],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:g=[],chartConfig:u={},dimensions:o={},isModal:D=!1})=>{var y;const{charts:C,theme:x}=Ue(),m=Re({charts:C,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée TRS disponible"}));const h=o.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),c=o.fontSize||(t?14:12),p=o.labelAngle||(t?-45:0),E=o.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(He,{data:n,margin:h},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Shift",tick:{fill:"#666",fontSize:c},tickFormatter:f=>f||"N/A",interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:E,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:c},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:c}}}),e.createElement(re,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let S=parseFloat(f);const b=!isNaN(S);return b&&S<=1&&S>0&&(S=S*100),[b?`${S.toFixed(2)}%`:`${f}%`,"TRS"]},labelFormatter:f=>`Équipe: ${f}`}),t&&e.createElement(ne,null),e.createElement(We,{type:"monotone",dataKey:"oee",name:"TRS",stroke:i,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:i},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(y=m.displayConfig)!=null&&y.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Gt=d.memo(({data:n,color:i=Ye[5],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:g=[],chartConfig:u={},dimensions:o={},isModal:D=!1})=>{var y;const{charts:C,theme:x}=Ue(),m=Re({charts:C,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée de performance disponible"}));const h=o.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),c=o.fontSize||(t?14:12),p=o.labelAngle||(t?-45:0),E=o.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(He,{data:n,margin:h},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Shift",tick:{fill:"#666",fontSize:c},tickFormatter:f=>f||"N/A",interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:E,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:c},tickFormatter:f=>`${f}%`,domain:[0,100],label:{value:"Performance (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:c}}}),e.createElement(re,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>{let S=parseFloat(f);const b=!isNaN(S);return b&&S<=1&&S>0&&(S=S*100),[b?`${S.toFixed(2)}%`:`${f}%`,"Performance"]},labelFormatter:f=>`Équipe: ${f}`}),t&&e.createElement(ne,null),e.createElement(We,{type:"monotone",dataKey:"performance",name:"Performance",stroke:i,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:i},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(y=m.displayConfig)!=null&&y.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Ut=d.memo(({data:n,color:i=Ye[1],height:a=300,enhanced:t=!1,expanded:r=!1,zoom:s=1,selectedDataPoints:g=[],chartConfig:u={},dimensions:o={},isModal:D=!1})=>{var y;const{charts:C,theme:x}=Ue(),m=Re({charts:C,theme:x});if(!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée de cycle disponible"}));const h=o.margin||(t?{top:30,right:50,left:50,bottom:100}:{top:16,right:24,left:24,bottom:60}),c=o.fontSize||(t?14:12),p=o.labelAngle||(t?-45:0),E=o.labelHeight||(t?100:60);return e.createElement(fe,{width:"100%",height:a},e.createElement(He,{data:n,margin:h},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"date",tick:{fill:"#666",fontSize:c},tickFormatter:f=>kt(f,r||t,n.length),interval:u.labelInterval!==void 0?u.labelInterval:t?0:"preserveStartEnd",angle:p,textAnchor:p!==0?"end":"middle",height:E,minTickGap:r?5:10}),e.createElement(se,{tick:{fontSize:c},tickFormatter:f=>`${f}s`,label:{value:"Cycle De Temps (s)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:c}}}),e.createElement(re,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:f=>[typeof f=="number"&&!isNaN(f)?`${f.toFixed(2)}s`:`${f}s`,"Cycle De Temps"],labelFormatter:f=>{try{return f&&A(f).isValid()?`Date: ${A(f).format("DD/MM/YYYY")}`:"Date: N/A"}catch{return"Date: N/A"}}}),t&&e.createElement(ne,null),e.createElement(We,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:i,strokeWidth:u.strokeWidth||(t||r?3:2),dot:{r:u.dotSize||(t||r?6:4),fill:i},activeDot:{r:(u.dotSize||(t||r?6:4))+2,fill:"#fff",stroke:i,strokeWidth:2},label:(y=m.displayConfig)!=null&&y.showDataLabels?{position:"top",fontSize:10,fill:"#666"}:!1})))}),Ht=d.memo(({data:n,dataKey:i="value",nameKey:a="name",colors:t=Ye,height:r,enhanced:s=!1,zoom:g=1,selectedDataPoints:u=[]})=>{var C;const o=Re({chartType:"pie",allowedTypes:["pie"]});if(!n||n.length===0)return e.createElement("div",{style:{height:r||o.height,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée disponible"}));const D=r||o.height;return e.createElement(fe,{width:"100%",height:D},e.createElement(_a,{margin:o.margins},e.createElement(Ca,{data:n,dataKey:i,nameKey:a,cx:"50%",cy:"50%",innerRadius:s?80:60,outerRadius:s?120:80,paddingAngle:s?8:5,label:s?({name:x,percent:m})=>`${x}: ${(m*100).toFixed(1)}%`:!1,labelLine:s},n.map((x,m)=>e.createElement(Ta,{key:`cell-${m}`,fill:t[m%t.length]}))),e.createElement(re,{...o.tooltipConfig,formatter:(x,m)=>[typeof x=="number"?x.toLocaleString():x,m]}),((C=o.charts)==null?void 0:C.showLegend)&&e.createElement(ne,{...o.legendConfig,layout:s?"horizontal":"vertical",verticalAlign:s?"bottom":"middle",align:s?"center":"right",wrapperStyle:{paddingLeft:s?0:24,paddingTop:s?20:0,fontSize:s?14:12}})))}),qa=d.memo(({data:n,color:i=Ye[2],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:s=[]})=>{var x;const g=Re({chartType:"bar",allowedTypes:["bar"]}),u=a||g.height;if(!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée de production disponible"}));const o=n.reduce((m,h)=>{const c=h.Machine_Name;return m[c]||(m[c]={Machine_Name:c,production:0}),m[c].production+=Number(h.production)||0,m},{}),D=Object.values(o),C=t?14:12;return e.createElement(fe,{width:"100%",height:u},e.createElement(je,{data:D,margin:g.margins},e.createElement(le,{...g.gridConfig}),e.createElement(ie,{dataKey:"Machine_Name",...g.axisConfig,tick:{fontSize:C},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(se,{...g.axisConfig,tick:{fontSize:C},tickFormatter:m=>m.toLocaleString(),label:{value:"Production (pcs)",angle:-90,position:"insideLeft",style:{fontSize:C}}}),e.createElement(re,{...g.tooltipConfig,formatter:m=>[typeof m=="number"&&!isNaN(m)?Number.isInteger(m)?m.toLocaleString():m.toFixed(2):m,"Production"]}),((x=g.charts)==null?void 0:x.showLegend)&&e.createElement(ne,{...g.legendConfig}),e.createElement(Be,{dataKey:"production",name:"Production",fill:i,radius:t?[4,4,0,0]:[0,0,0,0]})))}),Wt=d.memo(({data:n,color:i=Ye[4],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:s=[]})=>{var x;const g=Re({chartType:"bar",allowedTypes:["bar"]}),u=a||g.height;if(!n||n.length===0)return e.createElement("div",{style:{height:u,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée de rejets disponible"}));const o=n.reduce((m,h)=>{const c=h.Machine_Name;return m[c]||(m[c]={Machine_Name:c,rejects:0}),m[c].rejects+=Number(h.rejects)||0,m},{}),D=Object.values(o),C=t?14:12;return e.createElement(fe,{width:"100%",height:u},e.createElement(je,{data:D,margin:g.margins},e.createElement(le,{...g.gridConfig}),e.createElement(ie,{dataKey:"Machine_Name",...g.axisConfig,tick:{fontSize:C},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(se,{...g.axisConfig,tick:{fontSize:C},tickFormatter:m=>m.toLocaleString(),label:{value:"Rejets (kg)",angle:-90,position:"insideLeft",style:{fontSize:C}}}),e.createElement(re,{...g.tooltipConfig,formatter:m=>[typeof m=="number"&&!isNaN(m)?Number.isInteger(m)?m.toLocaleString():m.toFixed(2):m,"Rejets"]}),((x=g.charts)==null?void 0:x.showLegend)&&e.createElement(ne,{...g.legendConfig}),e.createElement(Be,{dataKey:"rejects",name:"Rejets",fill:i,radius:t?[4,4,0,0]:[0,0,0,0]})))}),Kt=d.memo(({data:n,color:i=Ye[5],height:a,enhanced:t=!1,zoom:r=1,selectedDataPoints:s=[]})=>{if(Re({chartType:"bar",allowedTypes:["bar"]}),!n||n.length===0)return e.createElement("div",{style:{height:a,display:"flex",alignItems:"center",justifyContent:"center"}},e.createElement(ae,{description:"Aucune donnée TRS disponible"}));const g=n.reduce((C,x)=>{const m=x.Machine_Name;C[m]||(C[m]={Machine_Name:m,trs:0,count:0});let h=Number(x.oee)||0;return h>0&&h<=1&&(h=h*100),C[m].trs+=h,C[m].count+=1,C},{}),u=Object.values(g).map(C=>({Machine_Name:C.Machine_Name,trs:C.count>0?C.trs/C.count:0})),o=t?{top:20,right:30,left:30,bottom:80}:{top:16,right:24,left:24,bottom:60},D=t?14:12;return e.createElement(fe,{width:"100%",height:a},e.createElement(je,{data:u,margin:o},e.createElement(le,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),e.createElement(ie,{dataKey:"Machine_Name",tick:{fill:"#666",fontSize:D},interval:0,angle:-45,textAnchor:"end",height:t?100:80}),e.createElement(se,{tick:{fontSize:D},tickFormatter:C=>`${C.toFixed(1)}%`,label:{value:"TRS (%)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:D}},domain:[0,100]}),e.createElement(re,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:t?14:12},formatter:C=>{let x=parseFloat(C);return isNaN(x)?["N/A","TRS"]:[`${x.toFixed(1)}%`,"TRS"]}}),t&&e.createElement(ne,null),e.createElement(Be,{dataKey:"trs",name:"TRS",fill:i,radius:t?[4,4,0,0]:[0,0,0,0]})))});Pt.displayName="EnhancedQuantityBarChart";za.displayName="EnhancedShiftBarChart";Qt.displayName="EnhancedTRSLineChart";Vt.displayName="EnhancedShiftTRSLineChart";Gt.displayName="EnhancedPerformanceLineChart";Ut.displayName="EnhancedCycleTimeLineChart";Ht.displayName="EnhancedPieChart";qa.displayName="EnhancedMachineProductionChart";Wt.displayName="EnhancedMachineRejectsChart";Kt.displayName="EnhancedMachineTRSChart";const{Text:wr}=ut,ja=({data:n,colors:i,dateRangeType:a,dateFilter:t,formatDateRange:r})=>{const{settings:s}=Ue();Re({chartType:"bar",allowedTypes:["bar","line"]});const g=d.useMemo(()=>{const D=Ra(s);return D===null?i||["#1890ff","#52c41a","#faad14","#f5222d","#722ed1"]:D},[s,i]),u=D=>{console.log(`Chart expanded: ${D}`)},o=D=>{console.log(`Chart collapsed: ${D}`)};return e.createElement(e.Fragment,null,e.createElement(v,{span:24},e.createElement(De,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(me,{title:`Quantité Bonne - ${a==="day"?"Journalière":a==="week"?"Hebdomadaire":"Mensuelle"}`,data:n,chartType:"bar",expandMode:"modal",onExpand:()=>u("quantity-good"),onCollapse:()=>o("quantity-good"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:t?"blue":"green"},r(t,a))}},e.createElement(Pt,{data:n,title:"Quantité Bonne",dataKey:"good",color:g[2],tooltipLabel:"Quantité bonne"}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:`Quantité Rejetée - ${a==="day"?"Journalière":a==="week"?"Hebdomadaire":"Mensuelle"}`,data:n,chartType:"bar",expandMode:"modal",onExpand:()=>u("quantity-reject"),onCollapse:()=>o("quantity-reject"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:t?"blue":"green"},r(t,a))}},e.createElement(Pt,{data:n,title:"Quantité Rejetée",dataKey:"reject",color:g[4],label:"Quantité",tooltipLabel:"Quantité rejetée",isKg:!0}))))),e.createElement(v,{xs:24,md:24},e.createElement(De,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Tendances TRS (Taux de Rendement Synthétique)",data:n,chartType:"line",expandMode:"modal",onExpand:()=>u("trs-trends"),onCollapse:()=>o("trs-trends"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:"cyan"},"Évolution TRS")}},e.createElement(Qt,{data:n,color:g[0]}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Tendances Cycle De Temps",data:n,chartType:"line",expandMode:"modal",onExpand:()=>u("cycle-time-trends"),onCollapse:()=>o("cycle-time-trends"),exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:"orange"},"Évolution Cycle")}},e.createElement(Ut,{data:n,color:g[1]}))))))},At=d.memo(({data:n,title:i,dataKey:a,color:t,label:r="Valeur",tooltipLabel:s,isKg:g=!1,chartType:u,allowedTypes:o=["bar","line"],enhanced:D=!1,expanded:C=!1,isModal:x=!1,height:m=300,...h})=>{const c=qt({fallbackType:"bar",allowedTypes:o,propChartType:u}),p=c.chartType,E=d.useMemo(()=>{if(!Array.isArray(n)||n.length===0)return[];const b=n.reduce((M,w)=>{const _=w.Shift||w.shift||w.name||w.label||"N/A",q=parseFloat(w[a])||0;return _==="N/A"||(M[_]||(M[_]={shift:_,[a]:0,count:0,originalData:[]}),["production","rejects","downtime"].includes(a),M[_][a]+=q,M[_].count+=1,M[_].originalData.push(w)),M},{});return Object.values(b).map(M=>(["production","rejects","downtime"].includes(a)||(M[a]=M[a]/M.count),{...M,[a]:Number(M[a])||0})).filter(M=>M[a]!==void 0&&M[a]!==null)},[n,a]),y=({active:b,payload:M,label:w})=>{if(b&&M&&M.length){const q=M[0].value,ee=g?`${q.toLocaleString()} kg`:q.toLocaleString(),j=c.tooltipConfig;return e.createElement("div",{style:j.contentStyle},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:c.getTextColor()}},`Équipe: ${w}`),e.createElement("p",{style:{margin:0,color:t||c.getPrimaryColor()}},`${s||w}: ${ee}`))}return null},f={data:E,margin:c.margins,...h},S=()=>{const b=t||c.getPrimaryColor();switch(p){case"line":return e.createElement(He,{...f},e.createElement(le,{...c.gridConfig}),e.createElement(ie,{dataKey:"shift",...c.axisConfig,tick:{fontSize:11}}),e.createElement(se,{...c.axisConfig,tick:{fontSize:11},label:{value:g?"Quantité (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(re,{content:e.createElement(y,null)}),c.displayConfig.showLegend&&e.createElement(ne,{...c.legendConfig}),e.createElement(We,{type:"monotone",dataKey:a,name:s||i,...c.getLineElementConfig(b)}));case"bar":default:return e.createElement(je,{...f},e.createElement(le,{...c.gridConfig}),e.createElement(ie,{dataKey:"shift",...c.axisConfig,tick:{fontSize:11}}),e.createElement(se,{...c.axisConfig,tick:{fontSize:11},label:{value:g?"Quantité (kg)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(re,{content:e.createElement(y,null)}),c.displayConfig.showLegend&&e.createElement(ne,{...c.legendConfig}),e.createElement(Be,{dataKey:a,name:s||i,...c.getBarElementConfig(b),maxBarSize:D||C?60:40}))}};return!E||E.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:m,width:"100%"}},e.createElement(ae,{description:"Aucune donnée disponible pour les équipes",image:ae.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{width:"100%",height:m}},e.createElement(fe,{...c.responsiveContainerProps},S()))});At.displayName="EnhancedShiftChart";const Jt=d.memo(({data:n,title:i,dataKey:a="production",color:t,label:r="Production",tooltipLabel:s,isKg:g=!0,chartType:u,allowedTypes:o=["bar","line"],enhanced:D=!1,expanded:C=!1,isModal:x=!1,height:m=300,...h})=>{const c=qt({fallbackType:"bar",allowedTypes:o,propChartType:u}),p=c.chartType,E=d.useMemo(()=>!Array.isArray(n)||n.length===0?[]:n.map(b=>{const M=b.Machine_Name||b.Machine||b.machine||b.name||b.label||"N/A",w=b[a]||0;return{...b,machine:M,[a]:Number(w)||0,originalData:b}}).filter(b=>b[a]!==void 0&&b[a]!==null),[n,a]),y=({active:b,payload:M,label:w})=>{const _=c.theme||{};if(b&&M&&M.length){const ee=M[0].value,j=g?`${ee.toLocaleString()} Pcs`:ee.toLocaleString();return e.createElement("div",{style:{backgroundColor:_.darkMode?"#1f1f1f":"#ffffff",border:`1px solid ${t||T.PRIMARY_BLUE}`,borderRadius:"8px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px"}},e.createElement("p",{style:{margin:0,fontWeight:"bold",color:_.darkMode?"#ffffff":"#000000"}},`Machine: ${w}`),e.createElement("p",{style:{margin:0,color:t||T.PRIMARY_BLUE}},`${s||w}: ${j}`))}return null},f={data:E,margin:c.margins,...h},S=()=>{var M,w;const b=t||T.PRIMARY_BLUE;switch(p){case"line":return e.createElement(He,{...f},e.createElement(le,{...c.gridConfig}),e.createElement(ie,{dataKey:"machine",...c.axisConfig,tick:{fontSize:11},angle:-45,textAnchor:"end",height:80}),e.createElement(se,{...c.axisConfig,tick:{fontSize:11},label:{value:g?"Production (Pcs)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(re,{content:e.createElement(y,null)}),((M=c.charts)==null?void 0:M.showLegend)&&e.createElement(ne,{...c.legendConfig}),e.createElement(We,{type:"monotone",dataKey:a,name:s||i,...c.getLineElementConfig(b)}));case"bar":default:return e.createElement(je,{...f},e.createElement(le,{...c.gridConfig}),e.createElement(ie,{dataKey:"machine",...c.axisConfig,tick:{fontSize:11},angle:-45,textAnchor:"end",height:80}),e.createElement(se,{...c.axisConfig,tick:{fontSize:11},label:{value:g?"Production (Pcs)":"Valeur",angle:-90,position:"insideLeft",style:{textAnchor:"middle"}}}),e.createElement(re,{content:e.createElement(y,null)}),((w=c.charts)==null?void 0:w.showLegend)&&e.createElement(ne,{...c.legendConfig}),e.createElement(Be,{dataKey:a,name:s||i,...c.getBarElementConfig(b),maxBarSize:D||C?60:40}))}};return!E||E.length===0?e.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:m,width:"100%"}},e.createElement(ae,{description:"Aucune donnée disponible pour les machines",image:ae.PRESENTED_IMAGE_SIMPLE})):e.createElement("div",{style:{width:"100%",height:m}},e.createElement(fe,{...c.responsiveContainerProps},S()))});Jt.displayName="EnhancedMachineChart";const Xt=({dataSource:n=[],columns:i=[],loading:a=!1,title:t,totalRecords:r=0,pageSize:s=100,currentPage:g=1,onPageChange:u,onPageSizeChange:o,exportEnabled:D=!1,onExport:C,maxRecordsWarning:x=1e3,performanceMode:m=!1,rowKey:h="id",scroll:c={x:1300},expandable:p,...E})=>{const[y,f]=d.useState(!1),S=d.useMemo(()=>{const j=n.length,W=j*.1,Pe=j>x;return{recordCount:j,estimatedRenderTime:W,isLargeDataset:Pe,performanceLevel:j>2e3?"poor":j>1e3?"warning":"good"}},[n.length,x]),b=d.useCallback(async()=>{if(C){f(!0);try{await C({data:n,totalRecords:r,currentPage:g,pageSize:s})}catch(j){console.error("Export failed:",j)}finally{f(!1)}}},[C,n,r,g,s]),M=d.useMemo(()=>({current:g,pageSize:s,total:r,showSizeChanger:!0,showQuickJumper:r>1e3,pageSizeOptions:["50","100","200","500"],showTotal:(j,W)=>`${W[0]}-${W[1]} sur ${j} enregistrements`,onChange:u,onShowSizeChange:o,size:"default"}),[g,s,r,u,o]),w=()=>S.isLargeDataset?e.createElement(Ct,{message:`Attention: ${S.recordCount} enregistrements`,description:`Le chargement peut prendre ${Math.ceil(S.estimatedRenderTime/1e3)}s. Considérez l'utilisation de filtres pour améliorer les performances.`,type:"warning",showIcon:!0,icon:e.createElement(xa,null),style:{marginBottom:16},action:e.createElement(J,null,e.createElement(we,{size:"small",type:"link"},"Optimiser les filtres"))}):null,_=d.useMemo(()=>t?e.createElement(J,null,e.createElement("span",null,t),e.createElement(k,{color:S.performanceLevel==="good"?"green":S.performanceLevel==="warning"?"orange":"red"},S.recordCount," enregistrements"),m&&e.createElement(he,{title:`Temps de rendu estimé: ${S.estimatedRenderTime.toFixed(1)}ms`},e.createElement(Nt,{style:{color:"#1890ff"}}))):null,[t,S,m]),q=d.useMemo(()=>e.createElement(J,null,D&&e.createElement(we,{icon:e.createElement(ct,null),onClick:b,loading:y,disabled:n.length===0},"Exporter"),m&&e.createElement(k,{color:"blue"},"Mode Performance")),[D,b,y,n.length,m]),ee=d.useMemo(()=>({...E,dataSource:n,columns:i,loading:a,rowKey:h,scroll:S.isLargeDataset?{...c,y:400}:c,pagination:r>s?M:!1,size:S.isLargeDataset?"small":"middle",expandable:p,title:_?()=>_:void 0,extra:q,virtual:S.isLargeDataset,sticky:!0,showSorterTooltip:!1,rowSelection:E.rowSelection?{...E.rowSelection,preserveSelectedRowKeys:!0}:void 0}),[E,n,i,a,h,c,S.isLargeDataset,r,s,M,p,_,q]);return e.createElement("div",null,e.createElement(w,null),e.createElement($t,{...ee}),r>1e3&&e.createElement("div",{style:{marginTop:16,textAlign:"center"}},e.createElement(aa,{...M,simple:!1,showLessItems:!1})))};Xt.propTypes={dataSource:L.array,columns:L.array,loading:L.bool,title:L.string,totalRecords:L.number,pageSize:L.number,currentPage:L.number,onPageChange:L.func,onPageSizeChange:L.func,exportEnabled:L.bool,onExport:L.func,maxRecordsWarning:L.number,performanceMode:L.bool,rowKey:L.oneOfType([L.string,L.func]),scroll:L.object,expandable:L.object};L.array,L.string,L.node,L.number,L.bool,L.bool,L.bool,L.bool,L.bool,L.number,L.func,L.func,L.node;const Ba=n=>n?new Date(n).toISOString().split("T")[0]:null,Lt=(n,i)=>{if(!(n!=null&&n.start))return"Toutes les dates";const a=A(n.start),t=n.end?A(n.end):a;return i==="day"?a.format("DD/MM/YYYY"):i==="week"?`${a.format("DD/MM")} - ${t.format("DD/MM/YYYY")}`:i==="month"?a.format("MM/YYYY"):`${a.format("DD/MM/YYYY")} - ${t.format("DD/MM/YYYY")}`};A.locale("fr");const{Title:St,Text:bt,Paragraph:Rt}=ut,{useBreakpoint:Qa}=ra,Va=()=>{var ot,Te;const n=ga(),{getChartColor:i,getChartColors:a,charts:t}=n,{dateFilter:r,dateRangeType:s,dateRangeDescription:g,selectedMachineModel:u,selectedMachine:o,machineModels:D,filteredMachineNames:C,handleMachineModelChange:x,handleMachineChange:m,handleDateChange:h,handleDateRangeTypeChange:c,resetFilters:p,handleRefresh:E,coordinationStatus:y,isProcessingFilters:f,triggerCoordination:S}=Bt();d.useCallback(()=>{const l={};return u&&(l.model=u),o&&(l.machine=o),r!=null&&r.start&&(r!=null&&r.end)&&(l.startDate=r.start.format("YYYY-MM-DD"),l.endDate=r.end.format("YYYY-MM-DD")),l.dateRangeType=s,l},[u,o,r,s]);const{getDashboardData:b,getAllDailyProduction:M,loading:w}=ma(),[_,q]=d.useState({allDailyProduction:[],productionChart:{data:[],dataSource:"unknown"},sidecards:{goodqty:0,rejetqty:0,dataSource:"unknown"},machinePerformance:{data:[],dataSource:"unknown"},shiftPerformance:{data:[],dataSource:"unknown"},availabilityTrend:[]}),[ee,j]=d.useState({elasticsearch:!1,mysql:!0,primary:"mysql"}),W=d.useCallback(async()=>{var l,R,P,V,Me,et,xe,Ne,lt,it,Le,$e;try{const H={dateRangeType:s,model:u||void 0,machine:o||void 0};r&&(typeof r.format=="function"?H.date=r.format("YYYY-MM-DD"):r instanceof Date?H.date=r.toISOString().split("T")[0]:typeof r=="string"&&(H.date=r)),Object.keys(H).forEach(N=>{H[N]===void 0&&delete H[N]});const[Oe,I]=await Promise.all([M(H),b(H)]);if(I){const N=((l=I.productionChart)==null?void 0:l.dataSource)||((R=I.sidecards)==null?void 0:R.dataSource)||((P=I.machinePerformance)==null?void 0:P.dataSource)||"mysql";j({elasticsearch:N==="elasticsearch",mysql:N==="mysql"||N==="unknown",primary:N})}q({allDailyProduction:(Oe==null?void 0:Oe.getAllDailyProduction)||[],productionChart:{data:(((V=I==null?void 0:I.productionChart)==null?void 0:V.data)||[]).map(N=>({date:N.Date_Insert_Day,good:parseInt(N.Total_Good_Qty_Day)||0,reject:parseInt(N.Total_Rejects_Qty_Day)||0,oee:parseFloat(N.OEE_Day)||0,speed:parseFloat(N.Speed_Day)||0,availability:parseFloat(N.Availability_Rate_Day)||0,performance:parseFloat(N.Performance_Rate_Day)||0,quality:parseFloat(N.Quality_Rate_Day)||0,OEE_Day:parseFloat(N.OEE_Day)||0,Availability_Rate_Day:parseFloat(N.Availability_Rate_Day)||0,Performance_Rate_Day:parseFloat(N.Performance_Rate_Day)||0,Quality_Rate_Day:parseFloat(N.Quality_Rate_Day)||0})),dataSource:((Me=I==null?void 0:I.productionChart)==null?void 0:Me.dataSource)||"unknown"},sidecards:{goodqty:parseInt((et=I==null?void 0:I.sidecards)==null?void 0:et.goodqty)||0,rejetqty:parseInt((xe=I==null?void 0:I.sidecards)==null?void 0:xe.rejetqty)||0,dataSource:((Ne=I==null?void 0:I.sidecards)==null?void 0:Ne.dataSource)||"unknown"},machinePerformance:{data:(((lt=I==null?void 0:I.machinePerformance)==null?void 0:lt.data)||[]).map(N=>({...N,availability:parseFloat(N.availability)||0,performance:parseFloat(N.performance)||0,oee:parseFloat(N.oee)||0,quality:parseFloat(N.quality)||0,disponibilite:parseFloat(N.disponibilite)||0,downtime:parseFloat(N.downtime)||0})),dataSource:((it=I==null?void 0:I.machinePerformance)==null?void 0:it.dataSource)||"unknown"},shiftPerformance:{data:(((Le=I==null?void 0:I.shiftPerformance)==null?void 0:Le.data)||[]).map(N=>({...N,name:N.Shift||N.name,availability:parseFloat(N.availability)||0,performance:parseFloat(N.performance)||0,oee:parseFloat(N.oee)||0,quality:parseFloat(N.quality)||0,disponibilite:parseFloat(N.disponibilite)||0,downtime:parseFloat(N.downtime)||0})),dataSource:(($e=I==null?void 0:I.shiftPerformance)==null?void 0:$e.dataSource)||"unknown"},availabilityTrend:(I==null?void 0:I.availabilityTrend)||[]})}catch(H){console.error("Error fetching GraphQL data:",H)}},[s,u,o,r]);d.useEffect(()=>{W()},[W]);const[Pe,ye]=d.useState("1"),[G,ce]=d.useState(0),[tt,O]=d.useState(null),[U,$]=d.useState(""),[B,Z]=d.useState(!1),[pe,Ee]=d.useState(!1),te=Qa(),[K,Q]=d.useState(!1),ve=d.useCallback(l=>{h(l),Q(!!l)},[]);d.useEffect(()=>{var R,P;const l=_.allDailyProduction.length+(((R=_.productionChart.data)==null?void 0:R.length)||0)+(((P=_.machinePerformance.data)==null?void 0:P.length)||0);ce(l)},[_]);const Ae=d.useCallback(async l=>{},[]),at=d.useCallback((l,R)=>{O(l),$(R),Z(!!l),l&&ye("3")},[]),_e=d.useCallback(l=>{Ee(!1),l.type==="production-data"&&ye("3")},[]);d.useCallback(()=>{O(null),$(""),Z(!1)},[]);const rt=d.useMemo(()=>{let l=0,R=0,P=0,V=0;const Me=_.productionChart.data||[],et=_.sidecards;if(Me.length>0){const Le=Me.reduce(($e,H)=>{let Oe=parseFloat(H.oee||H.OEE_Day||0),I=parseFloat(H.availability||H.Availability_Rate_Day||0),N=parseFloat(H.performance||H.Performance_Rate_Day||0),ze=parseFloat(H.quality||H.Quality_Rate_Day||0);return Oe=oe(Oe),I=oe(I),N=oe(N),ze=oe(ze),{oee:$e.oee+Oe,availability:$e.availability+I,performance:$e.performance+N,quality:$e.quality+ze}},{oee:0,availability:0,performance:0,quality:0});l=Le.oee/Me.length,R=Le.availability/Me.length,P=Le.performance/Me.length,V=Le.quality/Me.length}const xe=parseInt(et.goodqty)||0,Ne=parseInt(et.rejetqty)||0,lt=xe+Ne>0?Ne/(xe+Ne)*100:0,it=xe+Ne>0?xe/(xe+Ne)*100:0;return{avgTRS:l,avgAvailability:R,avgPerformance:P,avgQuality:V,rejectRate:lt,qualityRate:it,totalGood:xe,totalRejects:Ne}},[_]),{avgTRS:Ke,avgAvailability:mt,avgPerformance:Ie,avgQuality:ft,rejectRate:ke,qualityRate:Y,totalGood:z,totalRejects:de}=rt,F=d.useCallback(()=>{const R=new Date().getHours();return R>=6&&R<14?"Matin":R>=14&&R<22?"Après-midi":"Nuit"},[]),Fe=d.useMemo(()=>La(z,de,Ke,mt,Ie,ke,Y),[z,de,Ke,mt,Ie,ke,Y]),Je=d.useMemo(()=>$a(a([T.PRIMARY_BLUE,T.SECONDARY_BLUE,T.CHART_TERTIARY,T.SUCCESS_GREEN,T.WARNING_ORANGE]),oe),[a]),nt=d.useMemo(()=>Oa(a([T.PRIMARY_BLUE,T.SECONDARY_BLUE,T.CHART_TERTIARY,T.SUCCESS_GREEN,T.WARNING_ORANGE]),_.allDailyProduction),[a,_.allDailyProduction]),ge=d.useCallback((l,R=0)=>{if(l==null||l==="")return R;if(typeof l=="number"&&!isNaN(l))return l;const P=String(l).trim().replace(",","."),V=parseFloat(P);return isNaN(V)?R:V},[]),Xe=d.useCallback((l,R=0)=>{if(l==null||l==="")return R;if(typeof l=="number"&&!isNaN(l))return Math.round(l);const P=String(l).trim(),V=parseInt(P,10);return isNaN(V)?R:V},[]),Ze=d.useMemo(()=>_.allDailyProduction.map(l=>({...l,date:(()=>{try{const R=l.Date_Insert_Day||l.date;if(R){if(R.includes("/")){let V=A(R,"DD/MM/YYYY HH:mm:ss");if(V.isValid()||(V=A(R,"DD/MM/YYYY")),V.isValid())return V.format("YYYY-MM-DD")}const P=A(R);if(P.isValid())return P.format("YYYY-MM-DD")}return console.warn(`Invalid date found in table data: ${R}, using today's date`),A().format("YYYY-MM-DD")}catch(R){return console.error("Error parsing date for table:",R),A().format("YYYY-MM-DD")}})(),Machine_Name:l.Machine_Name||"N/A",Shift:l.Shift||"N/A",good:Xe(l.Good_QTY_Day),reject:Xe(l.Rejects_QTY_Day),oee:(()=>{const R=ge(l.OEE_Day);return R>0&&R<=1?R*100:R})(),speed:ge(l.Speed_Day,null),mould_number:l.Part_Number||"N/A",poid_unitaire:l.Poid_Unitaire||"N/A",cycle_theorique:l.Cycle_Theorique||"N/A",poid_purge:l.Poid_Purge||"N/A",availability:(()=>{const R=ge(l.Availability_Rate_Day);return R>0&&R<=1?R*100:R})(),performance:(()=>{const R=ge(l.Performance_Rate_Day);return R>0&&R<=1?R*100:R})(),quality:(()=>{const R=ge(l.Quality_Rate_Day);return R>0&&R<=1?R*100:R})(),run_hours:ge(l.Run_Hours_Day),down_hours:ge(l.Down_Hours_Day)})),[_.allDailyProduction,ge,Xe]),pt=d.useMemo(()=>{var l,R;return[{key:"1",label:e.createElement("span",null,e.createElement(zt,null),"Tendances"),children:e.createElement(De,{gutter:[24,24]},w?e.createElement(v,{span:24},e.createElement(ue,null,e.createElement(st,{active:!0,paragraph:{rows:8}}))):e.createElement(ja,{data:_.productionChart.data,colors:a([T.PRIMARY_BLUE,T.SECONDARY_BLUE,T.CHART_TERTIARY,T.SUCCESS_GREEN,T.WARNING_ORANGE]),dateRangeType:s,dateFilter:r,formatDateRange:Lt}))},{key:"2",label:e.createElement("span",null,e.createElement(yt,null),"Performance"),children:e.createElement(De,{gutter:[24,24]},w?e.createElement(v,{span:24},e.createElement(ue,null,e.createElement(st,{active:!0,paragraph:{rows:8}}))):e.createElement(e.Fragment,null,e.createElement(v,{span:24},e.createElement(ue,{title:e.createElement(J,null,e.createElement(yt,{style:{fontSize:20,color:i(T.SECONDARY_BLUE,1)}}),e.createElement(bt,{strong:!0},"Performance des Machines")),variant:"borderless",extra:e.createElement(Yt,{count:((l=_.machinePerformance.data)==null?void 0:l.length)||0,style:{backgroundColor:i(T.SECONDARY_BLUE,1)}})},e.createElement(De,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Production par Machine",data:_.machinePerformance.data,chartType:"bar",expandMode:"modal"},e.createElement(Jt,{data:_.machinePerformance.data,title:"Production par Machine",dataKey:"production",tooltipLabel:"Production",isKg:!0,color:i(T.CHART_TERTIARY,2)}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Rejets par Machine",data:_.machinePerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(Wt,{data:_.machinePerformance.data,color:i(T.WARNING_ORANGE,4)})))))),e.createElement(v,{xs:24,md:12},e.createElement(me,{...ha("TRS par Machine",_.machinePerformance.data,"bar")},e.createElement(Kt,{data:_.machinePerformance.data,color:i("#60A5FA",5)}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Répartition Production - Qualité",data:[{name:"Bonnes Pièces",value:Number(z)||0},{name:"Rejets",value:Number(de)||0}].filter(P=>P.value>0),chartType:"pie",expandMode:"modal",exportEnabled:!0,cardProps:{extra:e.createElement(k,{color:"red"},"Qualité"),loading:w}},e.createElement(Ht,{data:[{name:"Bonnes Pièces",value:Number(z)||0},{name:"Rejets",value:Number(de)||0}].filter(P=>P.value>0),colors:a([T.CHART_TERTIARY,T.WARNING_ORANGE]),height:((R=t==null?void 0:t.layout)==null?void 0:R.defaultHeight)||300}))),e.createElement(v,{xs:24,md:24},e.createElement(ue,{title:e.createElement(J,null,e.createElement(yt,{style:{fontSize:20,color:i(T.CHART_QUATERNARY,3)}}),e.createElement(bt,{strong:!0},"Comparaison des Équipes")),variant:"borderless",extra:e.createElement(k,{color:"orange"},"Par équipe")},e.createElement(De,{gutter:[24,24]},e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Production par Équipe",data:_.shiftPerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(At,{data:_.shiftPerformance.data,title:"Production par Équipe",dataKey:"production",color:i(T.CHART_TERTIARY,2),label:"Production",tooltipLabel:"Production",isKg:!1}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Temps d'arrêt par Équipe",data:_.shiftPerformance.data,chartType:"bar",expandMode:"modal",exportEnabled:!0},e.createElement(At,{data:_.shiftPerformance.data,title:"Temps d'arrêt par Équipe",dataKey:"downtime",color:i(T.WARNING_ORANGE,4),label:"Temps d'arrêt (heures)",tooltipLabel:"Temps d'arrêt (heures)",isKg:!1}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"TRS par Équipe",data:_.shiftPerformance.data,chartType:"line",expandMode:"modal",exportEnabled:!0},e.createElement(Vt,{data:_.shiftPerformance.data,color:i(T.PRIMARY_BLUE,0)}))),e.createElement(v,{xs:24,md:12},e.createElement(me,{title:"Performance par Équipe",data:_.shiftPerformance.data,chartType:"line",expandMode:"modal",exportEnabled:!0},e.createElement(Gt,{data:_.shiftPerformance.data,color:i("#60A5FA",5)}))))))))},{key:"3",label:e.createElement("span",null,e.createElement(Na,null),"Détails"),children:e.createElement(De,{gutter:[24,24]},e.createElement(v,{span:24},e.createElement(ue,{title:e.createElement(J,null,e.createElement(It,{style:{fontSize:20,color:i(T.SECONDARY_BLUE,1)}}),e.createElement(bt,{strong:!0},"Données Journalières par Machine")),variant:"borderless",extra:e.createElement(J,null,e.createElement(Yt,{count:_.allDailyProduction.length,style:{backgroundColor:i(T.SECONDARY_BLUE,1)}}),e.createElement(we,{type:"link",icon:e.createElement(ct,null),disabled:!0},"Exporter"))},e.createElement($t,{dataSource:_.allDailyProduction,columns:Je,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:P=>`Total ${P} enregistrements`},scroll:{x:1800},rowKey:(P,V)=>`${P.Machine_Name}-${P.Date_Insert_Day}-${V}`}))),e.createElement(v,{span:24},e.createElement(Xt,{title:"Données Détaillées de Production",dataSource:Ze,columns:nt,totalRecords:Ze.length,pageSize:50,currentPage:1,onExport:Ae,maxRecordsWarning:500,loading:w,scroll:{x:2200},rowKey:(P,V)=>`${P.Date_Insert_Day}-${P.Machine_Name||"unknown"}-${P.Part_Number||"unknown"}-${V}`,expandable:{expandedRowRender:P=>e.createElement(ue,{size:"small",title:"Informations du moule"},e.createElement(De,{gutter:[16,16]},e.createElement(v,{span:6},e.createElement(Ve,{title:"Numéro de Pièce",value:P.Part_Number||"N/A",valueStyle:{fontSize:16}})),e.createElement(v,{span:6},e.createElement(Ve,{title:"Poids Unitaire",value:P.Poid_Unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})),e.createElement(v,{span:6},e.createElement(Ve,{title:"Cycle Théorique",value:P.Cycle_Theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})),e.createElement(v,{span:6},e.createElement(Ve,{title:"Poids Purge",value:P.Poid_Purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})))),expandRowByClick:!0,rowExpandable:P=>P.Part_Number&&P.Part_Number!=="N/A"}})))}]},[_.allDailyProduction,_.machinePerformance.data,_.sidecards,s,r,Lt,Ze,nt,Ae,w,Je]),Ce=w,Qe=w,gt=(((ot=_.productionChart.data)==null?void 0:ot.length)||0)>0||_.sidecards.goodqty>0,ht=(((Te=_.productionChart.data)==null?void 0:Te.length)||0)>0||_.sidecards.goodqty>0;return e.createElement("div",{style:{padding:te.md?24:16}},e.createElement(vt,{spinning:Ce,tip:"Chargement des données...",size:"large"},e.createElement(De,{gutter:[24,24]},e.createElement(v,{span:24},e.createElement(ue,{variant:"borderless",styles:{body:{padding:te.md?24:16}}},e.createElement(De,{gutter:[24,24],align:"middle"},e.createElement(v,{xs:24,md:12},e.createElement(St,{level:3,style:{marginBottom:8}},e.createElement(Mt,{style:{marginRight:12,color:i(T.PRIMARY_BLUE,0)}}),"Tableau de Bord de Production")),e.createElement(v,{xs:24,md:12,style:{textAlign:te.md?"right":"left"}},e.createElement(J,{direction:"vertical",style:{width:"100%"}},e.createElement("div",null,e.createElement(Fa,null),y&&e.createElement(k,{color:y.color,style:{marginTop:"8px"}},"🔄 ",y.message)),e.createElement(fa,{selectedMachineModel:u,selectedMachine:o,machineModels:D,filteredMachineNames:C,dateRangeType:s,dateFilter:r,dateFilterActive:K,handleMachineModelChange:l=>{console.log("📊 [DASHBOARD DEBUG] handleMachineModelChange called with:",l),x(l)},handleMachineChange:l=>{console.log("📊 [DASHBOARD DEBUG] handleMachineChange called with:",l),m(l)},handleDateRangeTypeChange:c,handleDateChange:ve,resetFilters:p,handleRefresh:E,loading:w||f,dataSize:G,pageType:"production",onSearchResults:at,enableElasticsearch:!0}),G>500&&e.createElement(k,{color:"blue",icon:e.createElement(xt,null)},G," enregistrements"),(u||K)&&e.createElement(J,{wrap:!0,style:{marginTop:8}},u&&e.createElement(k,{color:"blue",closable:!0,onClose:()=>x("")},"Modèle: ",u),o&&e.createElement(k,{color:"green",closable:!0,onClose:()=>m("")},"Machine: ",o),K&&e.createElement(k,{color:"purple",closable:!0,onClose:()=>h(null)},"Période: ",g),(u||o||K)&&e.createElement(we,{size:"small",type:"link",onClick:S,loading:f},"🔄 Sync")),!(u||K)&&ht&&e.createElement(J,{wrap:!0,style:{marginTop:8}},e.createElement(k,{color:"green",icon:e.createElement(xt,null)},"Powered by GraphQL"),y&&e.createElement(k,{color:y.dataSourceHealth==="elasticsearch"?"blue":"orange"},"Source: ",y.dataSourceHealth==="elasticsearch"?"Elasticsearch":"MySQL"),f&&e.createElement(k,{color:"processing"},"Processing..."))))))),Fe.slice(0,4).map(l=>e.createElement(v,{key:l.title,xs:24,sm:12,md:6},e.createElement(ue,{hoverable:!0,loading:Qe,style:{backgroundColor:"#FFFFFF",border:`1px solid ${T.PRIMARY_BLUE}`,borderTop:`3px solid ${T.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},Qe?e.createElement(st,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ve,{title:e.createElement(he,{title:l.description},e.createElement(J,null,e.cloneElement(l.icon,{style:{color:T.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:T.DARK_GRAY,fontWeight:600}},l.title),e.createElement(Nt,{style:{color:T.LIGHT_GRAY,fontSize:14}}))),value:l.rawValue||l.value,precision:l.title.includes("TRS")||l.title.includes("Taux")||l.title.includes("Disponibilité")||l.title.includes("Performance")||l.title.includes("Qualité")?1:0,suffix:l.suffix,valueStyle:{fontSize:24,color:T.PRIMARY_BLUE,fontWeight:700},formatter:R=>l.suffix==="%"?R.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):l.suffix==="Pcs"||l.suffix==="Kg"?R.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):R.toLocaleString("fr-FR")}),(l.title.includes("TRS")||l.title.includes("Taux")||l.title.includes("Disponibilité")||l.title.includes("Performance")||l.title.includes("Qualité"))&&e.createElement(be,{percent:l.rawValue||l.value,strokeColor:T.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),Fe.slice(4).map(l=>e.createElement(v,{key:l.title,xs:24,sm:12,md:6},e.createElement(ue,{hoverable:!0,loading:Qe,style:{backgroundColor:"#FFFFFF",border:`1px solid ${T.PRIMARY_BLUE}`,borderTop:`3px solid ${T.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"}},Qe?e.createElement(st,{active:!0,paragraph:{rows:1}}):e.createElement(e.Fragment,null,e.createElement(Ve,{title:e.createElement(he,{title:l.description},e.createElement(J,null,e.cloneElement(l.icon,{style:{color:T.PRIMARY_BLUE,fontSize:20}}),e.createElement("span",{style:{color:T.DARK_GRAY,fontWeight:600}},l.title),e.createElement(Nt,{style:{color:T.LIGHT_GRAY,fontSize:14}}))),value:l.rawValue||l.value,precision:l.title.includes("TRS")||l.title.includes("Taux")||l.title.includes("Disponibilité")||l.title.includes("Performance")||l.title.includes("Qualité")?1:0,suffix:l.suffix,valueStyle:{fontSize:24,color:T.PRIMARY_BLUE,fontWeight:700},formatter:R=>l.suffix==="%"?R.toLocaleString("fr-FR",{minimumFractionDigits:1,maximumFractionDigits:1}):l.suffix==="Pcs"||l.suffix==="Kg"?R.toLocaleString("fr-FR",{minimumFractionDigits:0,maximumFractionDigits:0}):R.toLocaleString("fr-FR")}),(l.title.includes("TRS")||l.title.includes("Taux")||l.title.includes("Disponibilité")||l.title.includes("Performance")||l.title.includes("Qualité"))&&e.createElement(be,{percent:l.rawValue||l.value,strokeColor:T.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6}))))),Ce?e.createElement(v,{span:24},e.createElement(ue,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(vt,{size:"large",style:{marginBottom:24}}),e.createElement(St,{level:3},"Chargement des données..."),e.createElement(Rt,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},u?`Chargement des données pour ${u}...`:"Chargement des données de production pour tous les modèles de machines...")))):gt?e.createElement(e.Fragment,null,e.createElement(v,{span:24},e.createElement(ue,{variant:"borderless"},e.createElement(na,{defaultActiveKey:"1",onChange:ye,items:pt,tabBarExtraContent:e.createElement(J,null,e.createElement(we,{type:"link",icon:e.createElement(Pa,null),onClick:()=>Ee(!0)},"Recherche globale"),e.createElement(we,{type:"link",icon:e.createElement(ct,null),disabled:!0},"Exporter"),o&&e.createElement(ia,{machineId:o,machineName:o,shift:F()}))})))):e.createElement(v,{span:24},e.createElement(ue,null,e.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"}},e.createElement(Mt,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),e.createElement(St,{level:3},"Aucune donnée disponible"),e.createElement(Rt,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600}},u||r?"Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.":"Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."),(u||r)&&e.createElement(Rt,{style:{fontSize:14,color:"#999",textAlign:"center",marginTop:16}},"Filtres actifs:",u&&` Modèle: ${u}`,o&&` Machine: ${o}`,r&&` Période: ${Ba(r)}`)))))),B&&tt&&e.createElement("div",{style:{marginTop:24}},e.createElement(pa,{results:tt,searchQuery:U,pageType:"production",loading:w,onResultSelect:l=>{},onPageChange:l=>{}})),e.createElement(ca,{visible:pe,onClose:()=>Ee(!1),onResultSelect:_e}))},Ga=d.memo(Va),Yr=d.memo(()=>e.createElement(va,null,e.createElement(Ga,null)));export{Yr as default};
