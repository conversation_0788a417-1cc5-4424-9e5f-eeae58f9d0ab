import db from '../db.js';
import NotificationService from"./notificationService.js"
import nodemailer from "nodemailer"

/**
 * Service for generating and sending shift reports
 */
class ShiftReportService {
  /**
   * Get the current shift based on time
   * @returns {Object} Current shift information
   */
  static getCurrentShift() {
    const now = new Date()
    const hour = now.getHours()

    if (hour >= 6 && hour < 14) {
      return { id: 1, name: "Mat<PERSON>", hours: "06:00 - 14:00" }
    } else if (hour >= 14 && hour < 22) {
      return { id: 2, name: "Après-midi", hours: "14:00 - 22:00" }
    } else {
      return { id: 3, name: "Nuit", hours: "22:00 - 06:00" }
    }
  }

  /**
   * Generate a shift report
   * @param {number} shiftId - Shift ID (1, 2, or 3)
   * @param {Date} date - Date for the report
   * @returns {Promise<Object>} Shift report data
   */
  static async generateShiftReport(shiftId, date = new Date()) {
    return new Promise((resolve, reject) => {
      // Format date for SQL query
      const formattedDate = date.toISOString().split("T")[0]

      // Determine shift times
      let startTime, endTime
      let shiftName

      switch (shiftId) {
        case 1:
          startTime = `${formattedDate} 06:00:00`
          endTime = `${formattedDate} 14:00:00`
          shiftName = "Matin"
          break
        case 2:
          startTime = `${formattedDate} 14:00:00`
          endTime = `${formattedDate} 22:00:00`
          shiftName = "Après-midi"
          break
        case 3:
          // For night shift, if the date is today, we can't have complete data yet
          // So we use yesterday's night shift
          const yesterday = new Date(date)
          yesterday.setDate(yesterday.getDate() - 1)
          const yesterdayFormatted = yesterday.toISOString().split("T")[0]

          startTime = `${yesterdayFormatted} 22:00:00`
          endTime = `${formattedDate} 06:00:00`
          shiftName = "Nuit"
          break
        default:
          return reject(new Error("Invalid shift ID"))
      }

      // Query production data for the shift
      db.query(
        `SELECT 
          SUM(production_count) as total_production,
          AVG(production_rate) as avg_production_rate,
          COUNT(DISTINCT machine_id) as machines_active
        FROM production_data
        WHERE timestamp BETWEEN ? AND ?`,
        [startTime, endTime],
        (err, productionResults) => {
          if (err) {
            console.error("Database error:", err)
            return reject(err)
          }

          // Query machine alerts for the shift
          db.query(
            `SELECT 
              COUNT(*) as total_alerts,
              COUNT(DISTINCT machine_id) as machines_with_alerts
            FROM machine_alerts
            WHERE timestamp BETWEEN ? AND ?`,
            [startTime, endTime],
            (err, alertResults) => {
              if (err) {
                console.error("Database error:", err)
                return reject(err)
              }

              // Query maintenance events for the shift
              db.query(
                `SELECT 
                  COUNT(*) as total_maintenance,
                  SUM(duration_minutes) as total_maintenance_minutes
                FROM maintenance_events
                WHERE start_time BETWEEN ? AND ?`,
                [startTime, endTime],
                (err, maintenanceResults) => {
                  if (err) {
                    console.error("Database error:", err)
                    return reject(err)
                  }

                  // Compile the report
                  const report = {
                    shiftId,
                    shiftName,
                    date: formattedDate,
                    startTime,
                    endTime,
                    production: productionResults[0] || {
                      total_production: 0,
                      avg_production_rate: 0,
                      machines_active: 0,
                    },
                    alerts: alertResults[0] || { total_alerts: 0, machines_with_alerts: 0 },
                    maintenance: maintenanceResults[0] || { total_maintenance: 0, total_maintenance_minutes: 0 },
                    generatedAt: new Date().toISOString(),
                  }

                  resolve(report)
                },
              )
            },
          )
        },
      )
    })
  }

  /**
   * Send shift report notification to a user
   * @param {number} userId - User ID
   * @param {Object} report - Shift report data
   * @returns {Promise<boolean>} Success status
   */
  static async sendShiftReportNotification(userId, report) {
    try {
      // Check if user wants shift report notifications
      const userSettings = await this.getUserShiftSettings(userId)

      if (!userSettings.shiftReportNotifications) {
        return false
      }

      // Check if user wants notifications for this specific shift
      const shiftKey = `shift${report.shiftId}Notifications`
      if (!userSettings[shiftKey]) {
        return false
      }

      // Create notification
      await NotificationService.createNotification({
        title: `Rapport de fin de quart: ${report.shiftName}`,
        message: `Le rapport du quart ${report.shiftName} du ${report.date} est disponible.`,
        category: "info",
        userId,
      })

      return true
    } catch (error) {
      console.error("Error sending shift report notification:", error)
      return false
    }
  }

  /**
   * Send shift report email to a user
   * @param {number} userId - User ID
   * @param {Object} report - Shift report data
   * @returns {Promise<boolean>} Success status
   */
  static async sendShiftReportEmail(userId, report) {
    try {
      // Check if user wants shift report emails
      const userSettings = await this.getUserShiftSettings(userId)

      if (!userSettings.shiftReportEmails) {
        return false
      }

      // Check if user wants emails for this specific shift
      const shiftKey = `shift${report.shiftId}Emails`
      if (!userSettings[shiftKey]) {
        return false
      }

      // Get user email
      const user = await this.getUserById(userId)
      if (!user || !user.email) {
        return false
      }

      // Create email content
      const emailContent = this.generateShiftReportEmail(report, userSettings.emailFormat === "html")

      // Send email with updated SMTP configuration
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'sicaf.com.tn',
        port: parseInt(process.env.SMTP_PORT) || 25,
        secure: process.env.SMTP_SECURE === 'true', // false for port 25 with STARTTLS
        requireTLS: process.env.SMTP_USE_STARTTLS === 'true', // Enable STARTTLS
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || '5vx7*Ok48'
        },
        // TLS configuration for self-signed certificates
        tls: {
          rejectUnauthorized: false, // Accept self-signed certificates
          ciphers: 'SSLv3'
        }
      })

      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: user.email,
        subject: `Rapport de fin de quart: ${report.shiftName} - ${report.date}`,
        ...(userSettings.emailFormat === "html" ? { html: emailContent } : { text: emailContent }),
      })

      return true
    } catch (error) {
      console.error("Error sending shift report email:", error)
      return false
    }
  }

  /**
   * Generate HTML or text email content for shift report
   * @param {Object} report - Shift report data
   * @param {boolean} isHtml - Whether to generate HTML or text
   * @returns {string} Email content
   */
  static generateShiftReportEmail(report, isHtml = true) {
    if (isHtml) {
      return `
        <html>
          <head>
            <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              h1 { color: #1890ff; }
              h2 { color: #096dd9; border-bottom: 1px solid #eee; padding-bottom: 10px; }
              .stat { margin-bottom: 20px; }
              .stat-label { font-weight: bold; }
              .stat-value { font-size: 18px; }
              .footer { margin-top: 30px; font-size: 12px; color: #888; }
            </style>
          </head>
          <body>
            <div class="container">
              <h1>Rapport de fin de quart: ${report.shiftName}</h1>
              <p>Date: ${report.date}</p>
              <p>Période: ${report.startTime.split(" ")[1].substring(0, 5)} - ${report.endTime.split(" ")[1].substring(0, 5)}</p>
              
              <h2>Production</h2>
              <div class="stat">
                <div class="stat-label">Production totale:</div>
                <div class="stat-value">${report.production.total_production} unités</div>
              </div>
              <div class="stat">
                <div class="stat-label">Taux de production moyen:</div>
                <div class="stat-value">${Math.round(report.production.avg_production_rate * 100) / 100} unités/heure</div>
              </div>
              <div class="stat">
                <div class="stat-label">Machines actives:</div>
                <div class="stat-value">${report.production.machines_active}</div>
              </div>
              
              <h2>Alertes</h2>
              <div class="stat">
                <div class="stat-label">Nombre total d'alertes:</div>
                <div class="stat-value">${report.alerts.total_alerts}</div>
              </div>
              <div class="stat">
                <div class="stat-label">Machines avec alertes:</div>
                <div class="stat-value">${report.alerts.machines_with_alerts}</div>
              </div>
              
              <h2>Maintenance</h2>
              <div class="stat">
                <div class="stat-label">Événements de maintenance:</div>
                <div class="stat-value">${report.maintenance.total_maintenance}</div>
              </div>
              <div class="stat">
                <div class="stat-label">Durée totale de maintenance:</div>
                <div class="stat-value">${report.maintenance.total_maintenance_minutes} minutes</div>
              </div>
              
              <div class="footer">
                <p>Ce rapport a été généré automatiquement le ${new Date(report.generatedAt).toLocaleString("fr-FR")}.</p>
                <p>Pour modifier vos préférences de notification, veuillez accéder à la page des paramètres de votre compte.</p>
              </div>
            </div>
          </body>
        </html>
      `
    } else {
      return `
RAPPORT DE FIN DE QUART: ${report.shiftName}
Date: ${report.date}
Période: ${report.startTime.split(" ")[1].substring(0, 5)} - ${report.endTime.split(" ")[1].substring(0, 5)}

PRODUCTION
----------
Production totale: ${report.production.total_production} unités
Taux de production moyen: ${Math.round(report.production.avg_production_rate * 100) / 100} unités/heure
Machines actives: ${report.production.machines_active}

ALERTES
-------
Nombre total d'alertes: ${report.alerts.total_alerts}
Machines avec alertes: ${report.alerts.machines_with_alerts}

MAINTENANCE
-----------
Événements de maintenance: ${report.maintenance.total_maintenance}
Durée totale de maintenance: ${report.maintenance.total_maintenance_minutes} minutes

Ce rapport a été généré automatiquement le ${new Date(report.generatedAt).toLocaleString("fr-FR")}.
Pour modifier vos préférences de notification, veuillez accéder à la page des paramètres de votre compte.
      `
    }
  }

  /**
   * Get user shift settings
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User shift settings
   */
  static async getUserShiftSettings(userId) {
    return new Promise((resolve, reject) => {
      db.query("SELECT * FROM user_settings WHERE user_id = ?", [userId], (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return reject(err)
        }

        if (results.length === 0) {
          // Return default settings if none exist
          return resolve({
            shiftReportNotifications: true,
            shiftReportEmails: true,
            shift1Notifications: true,
            shift2Notifications: true,
            shift3Notifications: true,
            shift1Emails: true,
            shift2Emails: true,
            shift3Emails: true,
            emailFormat: "html",
          })
        }

        resolve(results[0])
      })
    })
  }

  /**
   * Get user by ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User data
   */
  static async getUserById(userId) {
    return new Promise((resolve, reject) => {
      db.query("SELECT * FROM users WHERE id = ?", [userId], (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return reject(err)
        }

        if (results.length === 0) {
          return resolve(null)
        }

        resolve(results[0])
      })
    })
  }

  /**
   * Process end of shift reports for all users
   * @param {number} shiftId - Shift ID (1, 2, or 3)
   * @returns {Promise<void>}
   */
  static async processEndOfShiftReports(shiftId) {
    try {
      // Generate the shift report
      const report = await this.generateShiftReport(shiftId)

      // Get all users
      const users = await this.getAllUsers()

      // Send notifications and emails to each user
      for (const user of users) {
        await this.sendShiftReportNotification(user.id, report)
        await this.sendShiftReportEmail(user.id, report)
      }

      console.log(`End of shift reports processed for shift ${shiftId}`)
    } catch (error) {
      console.error("Error processing end of shift reports:", error)
    }
  }

  /**
   * Get all users
   * @returns {Promise<Array>} Array of users
   */
  static async getAllUsers() {
    return new Promise((resolve, reject) => {
      db.query("SELECT * FROM users", (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return reject(err)
        }

        resolve(results)
      })
    })
  }
}

export default ShiftReportService

