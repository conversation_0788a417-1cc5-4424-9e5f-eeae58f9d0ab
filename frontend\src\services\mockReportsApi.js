// Mock API service for reports - Use this in development
// Replace with real API calls in production

const mockReports = [
  {
    id: 1,
    type: "shift",
    date: "2024-12-08",
    endDate: "2024-12-08",
    generatedAt: "2024-12-08T14:30:00Z",
    generatedBy: "Chef d'équipe",
    status: "completed",
    size: 256000,
    shiftData: {
      team: "Équipe A",
      production: 15500,
      performance: 94.2,
      incidents: 2
    }
  },
  {
    id: 2,
    type: "daily",
    date: "2024-12-07",
    endDate: "2024-12-07",
    generatedAt: "2024-12-07T18:45:00Z",
    generatedBy: "Superviseur",
    status: "completed",
    size: 320000,
    dailyData: {
      totalProduction: 125000,
      performance: 92.5,
      quality: 96.2,
      availability: 94.8
    }
  },
  {
    id: 3,
    type: "weekly",
    date: "2024-12-02",
    endDate: "2024-12-08",
    generatedAt: "2024-12-08T09:15:00Z",
    generatedBy: "Manager Production",
    status: "completed",
    size: 480000,
    weeklyData: {
      totalProduction: 875000,
      averagePerformance: 91.8,
      trends: "Amélioration continue"
    }
  },
  {
    id: 4,
    type: "monthly",
    date: "2024-11-01",
    endDate: "2024-11-30",
    generatedAt: "2024-12-01T10:00:00Z",
    generatedBy: "Directeur Usine",
    status: "completed",
    size: 1200000,
    monthlyData: {
      totalProduction: 3500000,
      growth: 5.2,
      efficiency: 89.5
    }
  },
  {
    id: 5,
    type: "machine",
    date: "2024-12-08",
    endDate: "2024-12-08",
    generatedAt: "2024-12-08T16:20:00Z",
    generatedBy: "Technicien",
    status: "completed",
    size: 180000,
    machineData: {
      machineId: "M001",
      production: 15000,
      performance: 94.5,
      availability: 98.2,
      maintenance: "OK"
    }
  },
  {
    id: 6,
    type: "production",
    date: "2024-12-08",
    endDate: "2024-12-08",
    generatedAt: "2024-12-08T17:30:00Z",
    generatedBy: "Responsable Production",
    status: "completed",
    size: 380000,
    production: {
      total: 125000,
      performance: 92.5,
      activeMachines: 8,
      quality: 96.2
    }
  },
  {
    id: 7,
    type: "maintenance",
    date: "2024-12-07",
    endDate: "2024-12-07",
    generatedAt: "2024-12-07T20:45:00Z",
    generatedBy: "Chef Maintenance",
    status: "completed",
    size: 240000,
    maintenanceData: {
      preventive: 5,
      corrective: 3,
      totalCost: 15000,
      mttr: 2.5
    }
  },
  {
    id: 8,
    type: "quality",
    date: "2024-12-08",
    endDate: "2024-12-08",
    generatedAt: "2024-12-08T15:15:00Z",
    generatedBy: "Responsable Qualité",
    status: "completed",
    size: 200000,
    qualityData: {
      rate: 96.2,
      rejects: 4750,
      nonConformities: 12,
      corrections: 8
    }
  },
  {
    id: 9,
    type: "financial",
    date: "2024-11-01",
    endDate: "2024-11-30",
    generatedAt: "2024-12-01T11:30:00Z",
    generatedBy: "Contrôleur Financier",
    status: "completed",
    size: 650000,
    financialData: {
      revenue: 1250000,
      costs: 980000,
      profit: 270000,
      roi: 21.6
    }
  },
  {
    id: 10,
    type: "custom",
    date: "2024-12-08",
    endDate: "2024-12-08",
    generatedAt: "2024-12-08T13:45:00Z",
    generatedBy: "Analyste",
    status: "pending",
    size: 0,
    customData: {
      parameters: "Performance par équipe",
      template: "Custom_Template_01",
      filters: ["Équipe A", "Équipe B"]
    }
  }
]

const mockMachines = [
  { id: "m001", name: "Machine de Production 001" },
  { id: "m002", name: "Machine de Production 002" },
  { id: "m003", name: "Machine de Production 003" },
  { id: "m004", name: "Machine de Production 004" },
  { id: "m005", name: "Machine de Production 005" },
  { id: "m006", name: "Machine de Production 006" },
  { id: "m007", name: "Machine de Production 007" },
  { id: "m008", name: "Machine de Production 008" },
]

// Mock API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

export const mockApiService = {
  async getMachines() {
    await delay(500) // Simulate network delay
    return { machines: mockMachines }
  },

  async getReports(params) {
    await delay(800) // Simulate network delay
    
    let filteredReports = [...mockReports]
    
    // Filter by type
    if (params.type && params.type !== 'all') {
      filteredReports = filteredReports.filter(r => r.type === params.type)
    }
    
    // Filter by search
    if (params.search) {
      const searchLower = params.search.toLowerCase()
      filteredReports = filteredReports.filter(r => 
        r.type.toLowerCase().includes(searchLower) ||
        r.generatedBy.toLowerCase().includes(searchLower) ||
        r.id.toString().includes(searchLower)
      )
    }
    
    // Filter by status
    if (params.status) {
      filteredReports = filteredReports.filter(r => r.status === params.status)
    }
    
    // Pagination
    const page = parseInt(params.page) || 1
    const pageSize = parseInt(params.pageSize) || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      reports: filteredReports.slice(start, end),
      total: filteredReports.length,
      page,
      pageSize
    }
  },

  async generateReport(config) {
    await delay(2000) // Simulate report generation time
    
    const newReport = {
      id: mockReports.length + 1,
      type: config.type,
      date: config.dateRange.start,
      endDate: config.dateRange.end,
      generatedAt: new Date().toISOString(),
      generatedBy: "Current User",
      status: "completed",
      size: Math.floor(Math.random() * 500000) + 100000
    }
    
    mockReports.unshift(newReport)
    return newReport
  },

  async exportReport(reportId, format) {
    await delay(1000) // Simulate export processing
    
    const report = mockReports.find(r => r.id === parseInt(reportId))
    if (!report) {
      throw new Error('Rapport introuvable')
    }
    
    // Create mock file content
    let content = ''
    let mimeType = 'text/plain'
    
    switch (format) {
      case 'csv':
        content = `ID,Type,Date,Statut,Généré par\n${report.id},${report.type},${report.date},${report.status},${report.generatedBy}`
        mimeType = 'text/csv'
        break
      case 'pdf':
        content = `Rapport ${report.type} #${report.id}\nDate: ${report.date}\nStatut: ${report.status}`
        mimeType = 'application/pdf'
        break
      case 'excel':
        content = `Rapport Excel - ${report.type} #${report.id}`
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        break
      default:
        content = JSON.stringify(report, null, 2)
    }
    
    const blob = new Blob([content], { type: mimeType })
    return new Response(blob)
  },

  async deleteReport(reportId) {
    await delay(500)
    const index = mockReports.findIndex(r => r.id === parseInt(reportId))
    if (index !== -1) {
      mockReports.splice(index, 1)
      return { success: true }
    }
    throw new Error('Rapport introuvable')
  }
}

// Development mode check
export const isDevelopment = process.env.NODE_ENV === 'development'

// Use this function to determine whether to use mock or real API
export const getApiService = (realApiService) => {
  return isDevelopment ? mockApiService : realApiService
}
