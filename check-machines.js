// Check database content for machines
const query = `
  query {
    getAllDailyProduction {
      Machine_Name
    }
  }
`;

fetch('http://localhost:5000/api/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ query })
}).then(res => res.json()).then(data => {
  const machines = data.data?.getAllDailyProduction || [];
  const uniqueMachines = [...new Set(machines.map(m => m.Machine_Name))].filter(Boolean);
  console.log('🏭 Unique machines found in database:', uniqueMachines.length > 0 ? uniqueMachines : 'No machines found');
  
  // Extract models from machine names
  const models = [...new Set(uniqueMachines.map(name => {
    const match = name.match(/^([A-Za-z]+)/);
    return match ? match[1] : null;
  }))].filter(Boolean);
  
  console.log('🔧 Models extracted:', models.length > 0 ? models : 'No models found');
}).catch(console.error);
