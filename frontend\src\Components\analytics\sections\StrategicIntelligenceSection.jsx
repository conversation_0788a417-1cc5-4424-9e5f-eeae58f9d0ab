import React from 'react';
import { Card, Space } from 'antd';
import { ExperimentOutlined } from '@ant-design/icons';

const StrategicIntelligenceSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <ExperimentOutlined style={{ fontSize: '64px', color: '#096dd9' }} />
          <h2 style={{ color: '#096dd9', margin: 0 }}>Strategic Intelligence</h2>
          <p style={{ color: '#8c8c8c' }}>Long-term strategic planning and future insights coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default StrategicIntelligenceSection;
