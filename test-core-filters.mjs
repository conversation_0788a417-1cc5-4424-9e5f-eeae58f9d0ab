#!/usr/bin/env node

/**
 * Core Filter System Test - Final Validation
 * Quick test of essential filter functionality
 */

import superagent from 'superagent';

const baseUrl = 'http://localhost:4000';
const graphqlUrl = `${baseUrl}/api/graphql`;

console.log('🎯 CORE FILTER SYSTEM VALIDATION\n');

try {
  console.log('1. Testing GraphQL baseline...');
  
  const response = await superagent
    .post(graphqlUrl)
    .send({
      query: `
        query {
          getAllDailyProduction {
            Machine_Name
            Date_Insert_Day
            Good_QTY_Day
            Rejects_QTY_Day
          }
        }
      `
    })
    .timeout(10000);

  const records = response.body.data.getAllDailyProduction;
  console.log(`✅ GraphQL working: ${records.length} records found`);
  
  if (records.length > 0) {
    console.log(`📋 Sample: ${records[0].Machine_Name} - ${records[0].Date_Insert_Day}`);
  }

  console.log('\n2. Testing filtered query...');
  
  const filteredResponse = await superagent
    .post(graphqlUrl)
    .send({
      query: `
        query GetFilteredData($filters: FilterInput) {
          getAllDailyProduction(filters: $filters) {
            Machine_Name
            Date_Insert_Day
            Good_QTY_Day
          }
        }
      `,
      variables: {
        filters: { date: "2024-01-08" }
      }
    })
    .timeout(10000);

  const filteredRecords = filteredResponse.body.data.getAllDailyProduction;
  console.log(`✅ Date filter working: ${filteredRecords.length} records found`);
  
  const reduction = records.length > 0 ? ((records.length - filteredRecords.length) / records.length * 100).toFixed(1) : 0;
  console.log(`📊 Filter effectiveness: ${reduction}% reduction (${records.length} → ${filteredRecords.length})`);

  console.log('\n🎉 FILTER SYSTEM VALIDATION COMPLETE! 🎉');
  console.log('\n✅ Key Components Status:');
  console.log('   ✅ GraphQL endpoint: FUNCTIONAL');
  console.log('   ✅ Filter system: FUNCTIONAL');
  console.log('   ✅ Data reduction: WORKING');
  console.log('\n🚀 Ready for browser testing!');

} catch (error) {
  console.error('❌ Test failed:', error.message);
  if (error.response?.body) {
    console.error('Response:', error.response.body);
  }
}
