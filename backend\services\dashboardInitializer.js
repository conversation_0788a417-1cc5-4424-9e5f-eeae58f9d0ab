/**
 * Dashboard Data Initialization Script
 * 
 * Sets up Elasticsearch indexing for dashboard data and starts periodic updates
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

import dashboardDataIndexer from './dashboardDataIndexer.js';
import { checkElasticsearchHealth } from '../config/elasticsearch.js';
const elasticsearchService = require('./elasticsearchService');

class DashboardInitializer {
  constructor() {
    this.isInitialized = false;
    this.indexingInProgress = false;
  }

  /**
   * Initialize the dashboard data system
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('Dashboard system already initialized');
      return { success: true, message: 'Already initialized' };
    }

    try {
      console.log('🚀 Initializing dashboard data system...');

      // Check Elasticsearch connection
      const health = await checkElasticsearchHealth();
      if (!health) {
        console.warn('⚠️ Elasticsearch not available, dashboard will use MySQL fallback');
        this.isInitialized = true;
        return { 
          success: true, 
          message: 'Initialized with MySQL fallback - Elasticsearch unavailable',
          elasticsearchAvailable: false
        };
      }

      console.log('✅ Elasticsearch is healthy');

      // Create index mapping
      await dashboardDataIndexer.createIndexMapping();
      console.log('✅ Index mapping created');

      // Check if index already has data
      const indexHealth = await dashboardDataIndexer.checkIndexHealth();
      console.log('📊 Index status:', indexHealth);

      if (indexHealth.documentCount === 0) {
        console.log('📥 Index is empty, starting initial data indexing...');
        this.indexingInProgress = true;
        
        // Index data in background to avoid blocking startup
        this.performInitialIndexing().finally(() => {
          this.indexingInProgress = false;
        });
      } else {
        console.log(`✅ Index already contains ${indexHealth.documentCount} documents`);
      }

      // Start periodic indexing
      dashboardDataIndexer.schedulePeriodicIndexing();
      console.log('⏰ Periodic indexing scheduled');

      this.isInitialized = true;
      return { 
        success: true, 
        message: 'Dashboard system initialized successfully',
        elasticsearchAvailable: true,
        documentCount: indexHealth.documentCount
      };

    } catch (error) {
      console.error('❌ Error initializing dashboard system:', error);
      
      // Set as initialized anyway to allow MySQL fallback
      this.isInitialized = true;
      return { 
        success: false, 
        message: `Initialization failed: ${error.message}`,
        elasticsearchAvailable: false,
        error: error.message
      };
    }
  }

  /**
   * Perform initial indexing in background
   */
  async performInitialIndexing() {
    try {
      console.log('🔄 Starting background indexing...');
      const result = await dashboardDataIndexer.indexAllStopData();
      console.log(`✅ Background indexing complete: ${result.indexed} records indexed, ${result.errors} errors`);
    } catch (error) {
      console.error('❌ Background indexing failed:', error);
    }
  }

  /**
   * Get initialization status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      indexingInProgress: this.indexingInProgress
    };
  }

  /**
   * Force re-initialization
   */
  async reinitialize() {
    this.isInitialized = false;
    this.indexingInProgress = false;
    return await this.initialize();
  }

  /**
   * Manual indexing trigger
   */
  async triggerManualIndexing() {
    if (this.indexingInProgress) {
      return { 
        success: false, 
        message: 'Indexing already in progress' 
      };
    }

    try {
      this.indexingInProgress = true;
      console.log('🔄 Manual indexing triggered...');
      
      const result = await dashboardDataIndexer.indexAllStopData();
      
      return { 
        success: true, 
        message: `Manual indexing complete: ${result.indexed} records indexed, ${result.errors} errors`,
        indexed: result.indexed,
        errors: result.errors
      };
    } catch (error) {
      console.error('❌ Manual indexing failed:', error);
      return { 
        success: false, 
        message: `Manual indexing failed: ${error.message}`,
        error: error.message
      };
    } finally {
      this.indexingInProgress = false;
    }
  }

  /**
   * Health check for dashboard system
   */
  async healthCheck() {
    try {
      const status = this.getStatus();
      const indexHealth = await dashboardDataIndexer.checkIndexHealth();
      const esHealth = await checkElasticsearchHealth();
      
      return {
        system: {
          initialized: status.isInitialized,
          indexingInProgress: status.indexingInProgress
        },
        elasticsearch: {
          available: esHealth.isHealthy,
          cluster_status: esHealth.cluster_status,
          number_of_nodes: esHealth.number_of_nodes
        },
        index: {
          exists: indexHealth.status === 'healthy',
          documentCount: indexHealth.documentCount,
          indexSize: indexHealth.indexSize,
          status: indexHealth.status
        }
      };
    } catch (error) {
      console.error('Error in health check:', error);
      return {
        system: {
          initialized: this.isInitialized,
          indexingInProgress: this.indexingInProgress,
          error: error.message
        },
        elasticsearch: {
          available: false,
          error: error.message
        },
        index: {
          exists: false,
          error: error.message
        }
      };
    }
  }
}

// Export singleton instance
export default new DashboardInitializer();
