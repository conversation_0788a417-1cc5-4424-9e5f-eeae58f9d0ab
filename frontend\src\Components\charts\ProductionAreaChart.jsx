import React, { memo } from "react";
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  CartesianGrid,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  Tooltip as RechartsTooltip
} from "recharts";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// SOMIPEM Brand Color Palette for Charts (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Area chart component for production data
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {string} props.dataKey - Data key for area (default: "average_speed")
 * @param {string} props.color - Color for area (default: COLORS[2])
 * @returns {JSX.Element} - Rendered chart component
 */
const ProductionAreaChart = memo(({ data, dataKey = "average_speed", color = COLORS[2] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <AreaChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis
        dataKey="hour"
        tick={{ fill: "#666" }}
        tickFormatter={(hour) => {
          if (!hour) return ""
          const parts = hour.split(" ")
          if (parts.length >= 2) {
            return `${parts[1]}h`
          }
          return hour
        }}
      />
      <YAxis tickFormatter={(value) => `${value} u/h`} />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)
          return [
            isValidNumber ? `${value.toFixed(2)} unités/heure` : `${value} unités/heure`,
            "Cycle De Temps Moyenne",
          ]
        }}
        labelFormatter={(hour) => {
          if (!hour) return "Heure inconnue"
          const parts = hour.split(" ")
          if (parts.length >= 2) {
            return `Date: ${parts[0]}, Heure: ${parts[1]}h`
          }
          return hour
        }}
      />

      <Area
        type="monotone"
        dataKey={dataKey}
        name="Cycle De Temps Moyenne"
        stroke={color}
        fill={color}
        fillOpacity={0.3}
      />
    </AreaChart>
  </ResponsiveContainer>
));

ProductionAreaChart.displayName = 'ProductionAreaChart';

export default ProductionAreaChart;
