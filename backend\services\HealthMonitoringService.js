import superAgentService from './SuperAgentService.js';
import { checkElasticsearchHealth } from '../config/elasticsearch.js';
import dbPool from '../db.js';

/**
 * Comprehensive health monitoring service using SuperAgent for external checks
 * Monitors database, Elasticsearch, and external dependencies
 */
class HealthMonitoringService {
  constructor() {
    this.monitoringInterval = null;
    this.isMonitoring = false;
    this.healthHistory = [];
    this.maxHistoryEntries = 100;
    
    // Configuration for external services to monitor
    this.externalServices = [
      {
        name: 'frontend-app',
        url: process.env.FRONTEND_URL || 'http://localhost:3000',
        critical: false,
        timeout: 5000
      },
      {
        name: 'elasticsearch-http',
        url: (process.env.ELASTICSEARCH_URL || 'http://localhost:9200') + '/_cluster/health',
        critical: true,
        timeout: 10000,
        headers: {
          'Accept': 'application/json'
        }
      }
    ];

    // Add environment-specific services
    if (process.env.NODE_ENV === 'production') {
      this.externalServices.push({
        name: 'nginx-reverse-proxy',
        url: process.env.NGINX_HEALTH_URL || 'http://localhost/health',
        critical: true,
        timeout: 5000
      });
    }
  }

  /**
   * Start comprehensive health monitoring
   * @param {number} intervalMs - Monitoring interval in milliseconds
   */
  startMonitoring(intervalMs = 300000) { // 5 minutes default
    if (this.isMonitoring) {
      console.log('Health monitoring is already running');
      return;
    }

    console.log(`Starting comprehensive health monitoring with ${intervalMs}ms interval`);
    this.isMonitoring = true;

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('Error in health monitoring:', error);
      }
    }, intervalMs);

    // Perform initial health check
    this.performHealthCheck();
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.isMonitoring = false;
      console.log('Health monitoring stopped');
    }
  }

  /**
   * Perform comprehensive health check
   * @returns {Promise<Object>} Complete health status
   */
  async performHealthCheck() {
    const healthCheck = {
      timestamp: new Date().toISOString(),
      overall: 'unknown',
      services: {}
    };

    try {
      // Check database connectivity
      healthCheck.services.database = await this.checkDatabaseHealth();
      
      // Check Elasticsearch
      healthCheck.services.elasticsearch = await this.checkElasticsearchStatus();
      
      // Check external services using SuperAgent
      healthCheck.services.external = await this.checkExternalServices();
      
      // Determine overall health
      healthCheck.overall = this.calculateOverallHealth(healthCheck.services);
      
      // Store in history
      this.addToHistory(healthCheck);
      
      // Log significant health changes
      this.logHealthChanges(healthCheck);
      
      return healthCheck;
    } catch (error) {
      console.error('Health check failed:', error);
      healthCheck.overall = 'error';
      healthCheck.error = error.message;
      return healthCheck;
    }
  }

  /**
   * Check database health
   * @returns {Promise<Object>} Database health status
   */
  async checkDatabaseHealth() {
    const startTime = Date.now();
    
    try {
      // Test database connection with a simple query
      const [rows] = await dbPool.execute('SELECT 1 as health_check');
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        responseTime,
        timestamp: new Date().toISOString(),
        details: {
          connected: true,
          testQuery: 'SELECT 1',
          result: rows[0]?.health_check === 1
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
        error: error.message,
        details: {
          connected: false,
          code: error.code,
          sqlState: error.sqlState
        }
      };
    }
  }

  /**
   * Check Elasticsearch health
   * @returns {Promise<Object>} Elasticsearch health status
   */
  async checkElasticsearchStatus() {
    const startTime = Date.now();
    
    try {
      const isHealthy = await checkElasticsearchHealth();
      const responseTime = Date.now() - startTime;
      
      // Also check via HTTP using SuperAgent for additional verification
      const httpHealthCheck = await superAgentService.healthCheck(
        (process.env.ELASTICSEARCH_URL || 'http://localhost:9200') + '/_cluster/health',
        { timeout: 10000 }
      );
      
      return {
        status: isHealthy ? 'healthy' : 'degraded',
        responseTime,
        timestamp: new Date().toISOString(),
        details: {
          clientCheck: isHealthy,
          httpCheck: httpHealthCheck.healthy,
          httpStatus: httpHealthCheck.status,
          httpResponseTime: httpHealthCheck.responseTime
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
        error: error.message,
        details: {
          clientCheck: false,
          httpCheck: false
        }
      };
    }
  }

  /**
   * Check external services using SuperAgent
   * @returns {Promise<Object>} External services health status
   */
  async checkExternalServices() {
    try {
      const healthResults = await superAgentService.batchHealthCheck(
        this.externalServices.map(service => ({
          name: service.name,
          url: service.url,
          options: {
            timeout: service.timeout,
            headers: service.headers || {}
          }
        }))
      );

      return {
        status: healthResults.overall.healthy ? 'healthy' : 'degraded',
        timestamp: healthResults.overall.timestamp,
        totalServices: healthResults.overall.checkedServices,
        details: healthResults.services,
        summary: {
          healthy: healthResults.services.filter(s => s.healthy).length,
          unhealthy: healthResults.services.filter(s => !s.healthy).length,
          critical_issues: healthResults.services.filter(s => 
            !s.healthy && this.externalServices.find(ext => ext.name === s.name)?.critical
          ).length
        }
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
        details: []
      };
    }
  }

  /**
   * Calculate overall system health
   * @param {Object} services - Service health statuses
   * @returns {string} Overall health status
   */
  calculateOverallHealth(services) {
    const statuses = Object.values(services).map(service => service.status);
    
    if (statuses.some(status => status === 'unhealthy')) {
      // Check if critical services are down
      const criticalDown = services.external?.details?.some(service => 
        !service.healthy && 
        this.externalServices.find(ext => ext.name === service.name)?.critical
      );
      
      if (criticalDown || services.database?.status === 'unhealthy') {
        return 'critical';
      }
      return 'degraded';
    }
    
    if (statuses.some(status => status === 'degraded')) {
      return 'degraded';
    }
    
    if (statuses.every(status => status === 'healthy')) {
      return 'healthy';
    }
    
    return 'unknown';
  }

  /**
   * Add health check to history
   * @param {Object} healthCheck - Health check result
   */
  addToHistory(healthCheck) {
    this.healthHistory.unshift(healthCheck);
    
    // Limit history size
    if (this.healthHistory.length > this.maxHistoryEntries) {
      this.healthHistory = this.healthHistory.slice(0, this.maxHistoryEntries);
    }
  }

  /**
   * Log significant health changes
   * @param {Object} currentHealth - Current health status
   */
  logHealthChanges(currentHealth) {
    const previousHealth = this.healthHistory[1]; // Get previous entry
    
    if (!previousHealth) {
      console.log(`Initial health check: ${currentHealth.overall}`);
      return;
    }
    
    if (currentHealth.overall !== previousHealth.overall) {
      console.log(`Health status changed: ${previousHealth.overall} → ${currentHealth.overall}`);
      
      // Log specific service changes
      Object.keys(currentHealth.services).forEach(serviceName => {
        const current = currentHealth.services[serviceName];
        const previous = previousHealth.services[serviceName];
        
        if (current?.status !== previous?.status) {
          console.log(`Service ${serviceName}: ${previous?.status || 'unknown'} → ${current?.status || 'unknown'}`);
        }
      });
    }
  }

  /**
   * Get current health status
   * @returns {Object} Current health status
   */
  getCurrentHealth() {
    return this.healthHistory[0] || { status: 'unknown', message: 'No health data available' };
  }

  /**
   * Get health history
   * @param {number} limit - Number of entries to return
   * @returns {Array} Health history entries
   */
  getHealthHistory(limit = 10) {
    return this.healthHistory.slice(0, limit);
  }

  /**
   * Get health statistics
   * @returns {Object} Health statistics
   */
  getHealthStatistics() {
    if (this.healthHistory.length === 0) {
      return { message: 'No health data available' };
    }

    const recent = this.healthHistory.slice(0, 20); // Last 20 checks
    const healthyCounts = recent.filter(h => h.overall === 'healthy').length;
    const degradedCounts = recent.filter(h => h.overall === 'degraded').length;
    const unhealthyCounts = recent.filter(h => h.overall === 'critical').length;

    return {
      total_checks: recent.length,
      healthy_percentage: (healthyCounts / recent.length) * 100,
      degraded_percentage: (degradedCounts / recent.length) * 100,
      critical_percentage: (unhealthyCounts / recent.length) * 100,
      current_status: this.healthHistory[0].overall,
      last_check: this.healthHistory[0].timestamp,
      uptime_score: healthyCounts / recent.length
    };
  }

  /**
   * Test external connectivity for specific service
   * @param {string} serviceName - Name of the service to test
   * @returns {Promise<Object>} Connectivity test result
   */
  async testServiceConnectivity(serviceName) {
    const service = this.externalServices.find(s => s.name === serviceName);
    
    if (!service) {
      return {
        error: `Service '${serviceName}' not found in configuration`,
        available_services: this.externalServices.map(s => s.name)
      };
    }

    return await superAgentService.testConnectivity(service.url, {
      timeout: service.timeout
    });
  }
}

// Export singleton instance
const healthMonitoringService = new HealthMonitoringService();
export default healthMonitoringService;
