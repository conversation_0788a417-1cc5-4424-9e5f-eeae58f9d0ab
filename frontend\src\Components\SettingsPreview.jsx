import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Progress,
  Statistic,
  Row,
  Col,
  Alert,
  Spin
} from 'antd';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Line<PERSON>hart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { useSettings } from '../hooks/useSettings';
import { useEnhancedTheme } from '../contexts/EnhancedThemeContext';
import { useUnifiedChartConfig } from '../hooks/useUnifiedChartConfig';
import { useTheme } from '../theme-context';
import SOMIPEM_COLORS from '../styles/brand-colors';

const { Title, Text } = Typography;

/**
 * Settings Preview Component
 * Demonstrates immediate effects of settings changes
 */
function SettingsPreview() {
  // Use MainLayout theme system for consistency
  const { darkMode, toggleDarkMode: mainToggleDarkMode } = useTheme();

  const {
    theme: settingsTheme,
    tables,
    charts,
    refresh,
    notifications,
    email,
    reports,
    performance,
    loading,
    toggleCompactMode,
    toggleAnimations
  } = useSettings();

  const {
    themeClasses,
    chartTheme,
    tableConfig,
    chartConfig,
    refreshConfig
  } = useEnhancedTheme();

  // Unified chart configuration
  const enhancedChartConfig = useUnifiedChartConfig({
    charts,
    theme: settingsTheme
  });

  // Debug logging
  useEffect(() => {
    console.log('SettingsPreview - Charts settings:', charts);
    console.log('SettingsPreview - Settings theme:', settingsTheme);
    console.log('SettingsPreview - MainLayout dark mode:', darkMode);
    console.log('SettingsPreview - Chart config:', chartConfig);
  }, [charts, settingsTheme, darkMode, chartConfig]);

  const [refreshCount, setRefreshCount] = useState(0);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Sample data for demonstrations
  const sampleTableData = [
    { key: '1', name: 'Machine A', status: 'Running', efficiency: 95, production: 1250 },
    { key: '2', name: 'Machine B', status: 'Maintenance', efficiency: 0, production: 0 },
    { key: '3', name: 'Machine C', status: 'Running', efficiency: 87, production: 1100 },
    { key: '4', name: 'Machine D', status: 'Running', efficiency: 92, production: 1180 },
    { key: '5', name: 'Machine E', status: 'Stopped', efficiency: 0, production: 0 },
  ];

  const sampleChartData = [
    { name: 'Jan', production: 4000, efficiency: 85 },
    { name: 'Feb', production: 3000, efficiency: 78 },
    { name: 'Mar', production: 2000, efficiency: 92 },
    { name: 'Apr', production: 2780, efficiency: 88 },
    { name: 'May', production: 1890, efficiency: 95 },
    { name: 'Jun', production: 2390, efficiency: 82 },
  ];

  const pieData = [
    { name: 'Running', value: 60, color: '#52c41a' },
    { name: 'Maintenance', value: 25, color: '#faad14' },
    { name: 'Stopped', value: 15, color: '#ff4d4f' },
  ];

  // Table columns configuration
  const tableColumns = [
    {
      title: 'Machine',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const color = status === 'Running' ? 'green' : status === 'Maintenance' ? 'orange' : 'red';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: 'Efficiency (%)',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (efficiency) => (
        <Progress 
          percent={efficiency} 
          size="small" 
          status={efficiency > 90 ? 'success' : efficiency > 70 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: 'Production',
      dataIndex: 'production',
      key: 'production',
      render: (production) => production.toLocaleString(),
    },
  ];

  // Auto-refresh simulation
  useEffect(() => {
    if (!refreshConfig.autoRefreshEnabled) return;

    const interval = setInterval(() => {
      setRefreshCount(prev => prev + 1);
      setLastRefresh(new Date());
    }, refreshConfig.dashboardInterval * 1000);

    return () => clearInterval(interval);
  }, [refreshConfig.autoRefreshEnabled, refreshConfig.dashboardInterval]);

  // Render chart based on settings
  const renderChart = () => {
    // Get enhanced configuration
    const responsiveProps = enhancedChartConfig.responsiveContainerProps;
    const gridConfig = enhancedChartConfig.getGridConfig;
    const axisConfig = enhancedChartConfig.getAxisConfig;
    const tooltipConfig = enhancedChartConfig.getTooltipConfig;
    const legendConfig = enhancedChartConfig.getLegendConfig;
    const animationConfig = enhancedChartConfig.getAnimationConfig;
    const colors = enhancedChartConfig.getColorScheme;

    // Handle null colors (default mode) by providing fallback colors
    const safeColors = colors || [
      SOMIPEM_COLORS.PRIMARY_BLUE,
      SOMIPEM_COLORS.SECONDARY_BLUE,
      SOMIPEM_COLORS.CHART_TERTIARY,
      SOMIPEM_COLORS.SUCCESS_GREEN,
      SOMIPEM_COLORS.WARNING_ORANGE
    ];

    // Apply settings to data (using 'bar' as default since defaultType was removed)
    const enhancedChartData = enhancedChartConfig.applySettingsToData(sampleChartData, 'bar');
    const enhancedPieData = enhancedChartConfig.applySettingsToData(pieData, 'pie');

    // Use 'bar' as default chart type since defaultType setting was removed
    const chartType = 'bar';
    switch (chartType) {
      case 'line':
        const lineConfig = enhancedChartConfig.getLineChartConfig();
        return (
          <ResponsiveContainer {...responsiveProps}>
            <LineChart data={enhancedChartData} margin={enhancedChartConfig.getChartMargins()}>
              <CartesianGrid {...gridConfig} />
              <XAxis dataKey="name" {...axisConfig} />
              <YAxis {...axisConfig} />
              <Tooltip {...tooltipConfig} />
              {chartConfig.showLegend && <Legend {...legendConfig} />}
              <Line
                type="monotone"
                dataKey="production"
                stroke={safeColors[0]}
                strokeWidth={2}
                dot={lineConfig.lineProps.dot}
                activeDot={lineConfig.lineProps.activeDot}
                {...animationConfig}
              />
              <Line
                type="monotone"
                dataKey="efficiency"
                stroke={safeColors[1]}
                strokeWidth={2}
                dot={lineConfig.lineProps.dot}
                activeDot={lineConfig.lineProps.activeDot}
                {...animationConfig}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'pie':
        const pieConfig = enhancedChartConfig.getPieChartConfig();
        return (
          <ResponsiveContainer {...responsiveProps}>
            <PieChart margin={enhancedChartConfig.getChartMargins()}>
              <Pie
                data={enhancedPieData}
                {...pieConfig.pieProps}
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                {...animationConfig}
              >
                {enhancedPieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill || colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip {...tooltipConfig} />
              {chartConfig.showLegend && <Legend {...legendConfig} />}
            </PieChart>
          </ResponsiveContainer>
        );

      default: // bar chart
        const barConfig = enhancedChartConfig.getBarChartConfig;
        return (
          <ResponsiveContainer {...responsiveProps}>
            <BarChart data={enhancedChartData} margin={enhancedChartConfig.getChartMargins}>
              <CartesianGrid {...gridConfig} />
              <XAxis dataKey="name" {...axisConfig} />
              <YAxis {...axisConfig} />
              <Tooltip {...tooltipConfig} />
              {chartConfig.showLegend && <Legend {...legendConfig} />}
              <Bar
                dataKey="production"
                fill={safeColors[0]}
                radius={barConfig.barProps.radius}
                {...animationConfig}
              />
              <Bar
                dataKey="efficiency"
                fill={safeColors[1]}
                radius={barConfig.barProps.radius}
                {...animationConfig}
              />
            </BarChart>
          </ResponsiveContainer>
        );
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="Loading settings preview..." />
      </div>
    );
  }

  return (
    <div className={themeClasses} style={{ padding: '24px' }}>
      <Title level={3}>Settings Preview - Live Demo</Title>
      <Text type="secondary">
        This preview updates immediately when you change settings. All changes demonstrate the "immediate effect guarantee".
      </Text>

      {/* Quick Theme Controls */}
      <Card style={{ marginTop: '16px', marginBottom: '16px' }}>
        <Title level={4}>Quick Theme Controls</Title>
        <Space>
          <Button
            type={darkMode ? 'primary' : 'default'}
            onClick={mainToggleDarkMode}
            icon={darkMode ? '🌙' : '☀️'}
          >
            {darkMode ? 'Dark Mode' : 'Light Mode'}
          </Button>
          <Button
            type={settingsTheme.compactMode ? 'primary' : 'default'}
            onClick={toggleCompactMode}
          >
            {settingsTheme.compactMode ? 'Compact' : 'Normal'} Mode
          </Button>
          <Button
            type={settingsTheme.animationsEnabled ? 'primary' : 'default'}
            onClick={toggleAnimations}
          >
            Animations {settingsTheme.animationsEnabled ? 'ON' : 'OFF'}
          </Button>
        </Space>
      </Card>

      {/* Current Settings Status */}
      <Card style={{ marginBottom: '16px' }}>
        <Title level={4}>Current Settings Status</Title>
        <Row gutter={[16, 8]}>
          <Col span={6}>
            <Text strong>Theme:</Text> {darkMode ? 'Dark' : 'Light'}
          </Col>
          <Col span={6}>
            <Text strong>Layout:</Text> {settingsTheme.compactMode ? 'Compact' : 'Normal'}
          </Col>
          <Col span={6}>
            <Text strong>Animations:</Text> {settingsTheme.animationsEnabled ? 'Enabled' : 'Disabled'}
          </Col>
          <Col span={6}>
            <Text strong>Chart Animations:</Text> {settingsTheme.chartAnimations ? 'Enabled' : 'Disabled'}
          </Col>
          <Col span={6}>
            <Text strong>Page Size:</Text> {tables.defaultPageSize} rows
          </Col>
          <Col span={6}>
            <Text strong>Color Scheme:</Text> {charts.colorScheme ? charts.colorScheme.toUpperCase() : 'N/A'}
          </Col>
          <Col span={6}>
            <Text strong>Auto Refresh:</Text> {refresh.autoRefreshEnabled ? 'ON' : 'OFF'}
          </Col>
          <Col span={6}>
            <Text strong>Email:</Text> {email.enabled ? 'Enabled' : 'Disabled'}
          </Col>
        </Row>
      </Card>

      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        {/* Statistics Cards */}
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Production"
              value={16060}
              suffix="units"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Efficiency"
              value={87.5}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Machines"
              value={3}
              suffix="/ 5"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Refresh Count"
              value={refreshCount}
              suffix={refreshConfig.autoRefreshEnabled ? '(Auto)' : '(Manual)'}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Last: {lastRefresh.toLocaleTimeString()}
            </Text>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        {/* Chart Preview */}
        <Col span={16}>
          <Card title={`Sample ${chartConfig.defaultType} Chart`}>
            <div key={`chart-${chartConfig.defaultType}-${chartConfig.showLegend}-${JSON.stringify(charts.layout)}-${JSON.stringify(charts.dataDisplay)}`}>
              {renderChart()}
            </div>
          </Card>
        </Col>

        {/* Settings Effects Demonstration */}
        <Col span={8}>
          <Card title="Settings Effects Demo">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Chart Type:</Text> {chartConfig.defaultType}
                <br />
                <Text type="secondary">Changes chart visualization immediately</Text>
              </div>
              <div>
                <Text strong>Show Legend:</Text> {chartConfig.showLegend ? 'Yes' : 'No'}
                <br />
                <Text type="secondary">Toggles chart legend visibility</Text>
              </div>
              <div>
                <Text strong>Animations:</Text> {chartConfig.animations ? 'Enabled' : 'Disabled'}
                <br />
                <Text type="secondary">Controls chart transition effects</Text>
              </div>
              <div>
                <Text strong>Page Size:</Text> {tableConfig.pageSize} rows
                <br />
                <Text type="secondary">Changes table pagination immediately</Text>
              </div>
              <div>
                <Text strong>Refresh Interval:</Text> {refreshConfig.dashboardInterval}s
                <br />
                <Text type="secondary">Updates auto-refresh timing</Text>
              </div>
              <div>
                <Text strong>Auto Refresh:</Text> {refreshConfig.autoRefreshEnabled ? 'On' : 'Off'}
                <br />
                <Text type="secondary">Enables/disables automatic data updates</Text>
              </div>
            </Space>
          </Card>

          {/* Notification Settings Demo */}
          <Card title="Notification Settings" style={{ marginTop: '16px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Categories Enabled:</Text>
                <br />
                {Object.entries(notifications.categories).filter(([_, enabled]) => enabled).map(([category]) => (
                  <Tag key={category} color="blue" style={{ margin: '2px' }}>
                    {category.replace('_', ' ').toUpperCase()}
                  </Tag>
                ))}
              </div>
              <div>
                <Text strong>Sound:</Text> {notifications.behavior.sound ? 'Enabled' : 'Disabled'}
              </div>
              <div>
                <Text strong>Max Visible:</Text> {notifications.behavior.maxVisible}
              </div>
            </Space>
          </Card>

          {/* Email Settings Demo */}
          <Card title="Email Settings" style={{ marginTop: '16px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Email Notifications:</Text> {email.enabled ? 'Enabled' : 'Disabled'}
              </div>
              {email.enabled && (
                <>
                  <div>
                    <Text strong>Frequency:</Text> {email.frequency}
                  </div>
                  <div>
                    <Text strong>Template:</Text> {email.template}
                  </div>
                  {email.quietHours?.enabled && (
                    <div>
                      <Text strong>Quiet Hours:</Text> {email.quietHours.start} - {email.quietHours.end}
                    </div>
                  )}
                </>
              )}
            </Space>
          </Card>

          {refreshConfig.autoRefreshEnabled && (
            <Alert
              message="Auto Refresh Active"
              description={`Data refreshes every ${refreshConfig.dashboardInterval} seconds`}
              type="info"
              showIcon
              style={{ marginTop: '16px' }}
            />
          )}
        </Col>
      </Row>

      {/* Table Preview */}
      <Row style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card title="Sample Data Table">
            <Table
              columns={tableColumns}
              dataSource={sampleTableData}
              pagination={{
                pageSize: tableConfig.pageSize,
                showQuickJumper: tableConfig.showQuickJumper,
                showSizeChanger: true,
                pageSizeOptions: tableConfig.pageSizeOptions?.map(String) || ['10', '20', '50', '100']
              }}
              size={settingsTheme.compactMode ? 'small' : 'middle'}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default SettingsPreview;
