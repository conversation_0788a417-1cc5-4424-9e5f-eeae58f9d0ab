#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Environment Test Script for Multi-Client State Management
    
.DESCRIPTION
    Tests the basic Node.js environment and dependencies before running the full test suite.
    This helps diagnose issues with the Node.js test script execution.
    
.EXAMPLE
    .\test-environment.ps1
#>

param()

# Script configuration
$ErrorActionPreference = "Stop"

# Colors for output
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-TestHeader {
    param([string]$Title)
    
    Write-ColorOutput "`n$('=' * 60)" -Color $Colors.Header
    Write-ColorOutput "🧪 $Title" -Color $Colors.Header
    Write-ColorOutput "$('=' * 60)" -Color $Colors.Header
}

Write-TestHeader "ENVIRONMENT TEST SUITE"

# Test 1: Check Node.js installation
Write-ColorOutput "`n🔍 Test 1: Checking Node.js installation..." -Color $Colors.Info

try {
    $nodeVersion = node --version
    Write-ColorOutput "✅ Node.js is installed: $nodeVersion" -Color $Colors.Success
} catch {
    Write-ColorOutput "❌ Node.js is not installed or not in PATH" -Color $Colors.Error
    Write-ColorOutput "Please install Node.js from https://nodejs.org/" -Color $Colors.Warning
    exit 1
}

# Test 2: Check npm installation
Write-ColorOutput "`n🔍 Test 2: Checking npm installation..." -Color $Colors.Info

try {
    $npmVersion = npm --version
    Write-ColorOutput "✅ npm is installed: $npmVersion" -Color $Colors.Success
} catch {
    Write-ColorOutput "❌ npm is not installed or not in PATH" -Color $Colors.Error
    exit 1
}

# Test 3: Check if we're in the correct directory
Write-ColorOutput "`n🔍 Test 3: Checking project structure..." -Color $Colors.Info

$currentDir = Get-Location
Write-ColorOutput "Current directory: $currentDir" -Color $Colors.Info

if (Test-Path "backend\package.json") {
    Write-ColorOutput "✅ Backend package.json found" -Color $Colors.Success
} else {
    Write-ColorOutput "❌ Backend package.json not found" -Color $Colors.Error
    Write-ColorOutput "Please run this script from the project root directory" -Color $Colors.Warning
    exit 1
}

if (Test-Path "backend\scripts\testEnvironment.js") {
    Write-ColorOutput "✅ Environment test script found" -Color $Colors.Success
} else {
    Write-ColorOutput "❌ Environment test script not found" -Color $Colors.Error
    exit 1
}

# Test 4: Check backend dependencies
Write-ColorOutput "`n🔍 Test 4: Checking backend dependencies..." -Color $Colors.Info

try {
    Set-Location "backend"
    
    # Check if node_modules exists
    if (Test-Path "node_modules") {
        Write-ColorOutput "✅ node_modules directory exists" -Color $Colors.Success
    } else {
        Write-ColorOutput "⚠️ node_modules not found, running npm install..." -Color $Colors.Warning
        npm install
        Write-ColorOutput "✅ Dependencies installed" -Color $Colors.Success
    }
    
    # Check specific dependencies
    $requiredDeps = @("ws", "superagent", "jsonwebtoken")
    
    foreach ($dep in $requiredDeps) {
        if (Test-Path "node_modules\$dep") {
            Write-ColorOutput "✅ Dependency '$dep' is installed" -Color $Colors.Success
        } else {
            Write-ColorOutput "❌ Dependency '$dep' is missing" -Color $Colors.Error
            Write-ColorOutput "Running npm install to fix dependencies..." -Color $Colors.Warning
            npm install
            break
        }
    }
    
} catch {
    Write-ColorOutput "❌ Error checking dependencies: $($_.Exception.Message)" -Color $Colors.Error
    exit 1
} finally {
    Set-Location ".."
}

# Test 5: Run the Node.js environment test
Write-ColorOutput "`n🔍 Test 5: Running Node.js environment test..." -Color $Colors.Info

try {
    Set-Location "backend"
    
    Write-ColorOutput "Executing: node scripts/testEnvironment.js" -Color $Colors.Info
    $output = node scripts/testEnvironment.js 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Node.js environment test passed" -Color $Colors.Success
        Write-ColorOutput "`nEnvironment test output:" -Color $Colors.Info
        $output | ForEach-Object { Write-ColorOutput "  $_" -Color $Colors.Info }
    } else {
        Write-ColorOutput "❌ Node.js environment test failed" -Color $Colors.Error
        Write-ColorOutput "`nError output:" -Color $Colors.Error
        $output | ForEach-Object { Write-ColorOutput "  $_" -Color $Colors.Error }
        exit 1
    }
    
} catch {
    Write-ColorOutput "❌ Error running Node.js environment test: $($_.Exception.Message)" -Color $Colors.Error
    exit 1
} finally {
    Set-Location ".."
}

# Test 6: Check server availability (optional)
Write-ColorOutput "`n🔍 Test 6: Checking server availability..." -Color $Colors.Info

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -Method GET -TimeoutSec 5
    Write-ColorOutput "✅ Server is running and responding" -Color $Colors.Success
} catch {
    Write-ColorOutput "⚠️ Server is not running or not responding" -Color $Colors.Warning
    Write-ColorOutput "This is expected if you haven't started the server yet" -Color $Colors.Info
    Write-ColorOutput "To start the server, run: npm run start" -Color $Colors.Info
}

# Summary
Write-TestHeader "ENVIRONMENT TEST SUMMARY"

Write-ColorOutput "🎉 Environment tests completed successfully!" -Color $Colors.Success
Write-ColorOutput "`nNext steps:" -Color $Colors.Info
Write-ColorOutput "1. Start the server if not already running: npm run start" -Color $Colors.Info
Write-ColorOutput "2. Run the full test suite:" -Color $Colors.Info
Write-ColorOutput "   PowerShell: .\test-multi-client-state-management.ps1 -TestMode full -VerboseOutput" -Color $Colors.Info
Write-ColorOutput "   Node.js: cd backend && node scripts/testMultiClientStateManagement.js" -Color $Colors.Info

Write-ColorOutput "`n✅ Environment is ready for multi-client state management testing!" -Color $Colors.Success
