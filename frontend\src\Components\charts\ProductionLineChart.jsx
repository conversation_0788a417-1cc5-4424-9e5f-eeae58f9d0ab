import React, { memo } from "react";
import {
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  CartesianGrid,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON> as RechartsTooltip
} from "recharts";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import SOMIPEM_COLORS from "../../styles/brand-colors";

// SOMIPEM Brand Color Palette for Charts (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
];

/**
 * Line chart component for production data
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {Array} props.dataKeys - Data keys for lines (default: ["oee", "speed"])
 * @param {Array} props.colors - Array of colors (default: [COLORS[0], COLORS[1]])
 * @returns {JSX.Element} - Rendered chart component
 */
const ProductionLineChart = memo(({ data, dataKeys = ["oee", "speed"], colors = [COLORS[0], COLORS[1]] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="date" tick={{ fill: "#666" }} tickFormatter={(date) => dayjs(date).format("DD/MM")} />
      <YAxis tickFormatter={(value) => `${value}%`} domain={[0, 100]} />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)
          const formattedValue = isValidNumber
            ? Number.isInteger(value)
              ? value.toLocaleString()
              : value.toFixed(2)
            : value

          const labels = {
            oee: "TRS",
            speed: "Cycle De Temps",
          }

          return [isValidNumber ? formattedValue + "%" : value, labels[name] || name]
        }}
        labelFormatter={(label) => `Date: ${dayjs(label).format("DD/MM/YYYY")}`}
      />

      {dataKeys.map((key, index) => (
        <Line
          key={key}
          type="monotone"
          dataKey={key}
          name={key}
          stroke={colors[index]}
          strokeWidth={2}
          dot={{ r: 4, fill: colors[index] }}
          activeDot={{ r: 6, fill: "#fff", stroke: colors[index], strokeWidth: 2 }}
        />
      ))}
    </LineChart>
  </ResponsiveContainer>
));

ProductionLineChart.displayName = 'ProductionLineChart';

export default ProductionLineChart;
