{"name": "triple-filter-freeze-test", "version": "1.0.0", "description": "Test suite for triple filter freeze fix", "type": "module", "main": "test-backend-node.js", "scripts": {"test": "node test-backend-freeze-fix.js", "test-backend": "node test-backend-freeze-fix.js", "test-old": "node test-backend-node.js", "test-simple": "node test-simple.cjs", "install-deps": "npm install node-fetch"}, "dependencies": {"node-fetch": "^3.3.2"}, "keywords": ["test", "graphql", "performance", "dashboard"], "author": "Dashboard Team", "license": "MIT"}