import React from "react" ;
import { useState, useCallback } from "react";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import isoWeek from "dayjs/plugin/isoWeek";
import weekOfYear from "dayjs/plugin/weekOfYear";
import customParseFormat from "dayjs/plugin/customParseFormat";

// Simple date formatting function
const formatApiDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split('T')[0];
};

// Add plugins
dayjs.extend(isoWeek);
dayjs.extend(weekOfYear);
dayjs.extend(customParseFormat);

// Configure dayjs to use French locale
dayjs.locale("fr");

/**
 * Custom hook for managing date filters
 * @returns {Object} Date filter state and functions
 */
const useDateFilter = () => {
  const [dateFilter, setDateFilter] = useState(null);
  const [dateRangeType, setDateRangeType] = useState("day");
  const [dateRangeDescription, setDateRangeDescription] = useState("");
  const [dateFilterActive, setDateFilterActive] = useState(false);

  // Helper function to format date range for display
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" };

    try {
      const formattedDate = dayjs(date);
      if (!formattedDate.isValid()) {
        console.error("Invalid date in formatDateRange:", date);
        return { short: "Date invalide", full: "Date invalide" };
      }

      if (rangeType === "day") {
        return {
          short: formattedDate.format("DD/MM/YYYY"),
          full: `le ${formattedDate.format("DD MMMM YYYY")}`,
        };
      } else if (rangeType === "week") {
        // Ensure we're using ISO week (Monday to Sunday)
        const startOfWeek = formattedDate.startOf("isoWeek");
        const endOfWeek = formattedDate.endOf("isoWeek");
        const weekNumber = formattedDate.isoWeek();

        return {
          short: `S${weekNumber} ${formattedDate.format("YYYY")}`,
          full: `Semaine ${weekNumber} (du ${startOfWeek.format("DD MMMM")} au ${endOfWeek.format("DD MMMM YYYY")})`,
        };
      } else if (rangeType === "month") {
        // Capitalize the month name
        const monthName = formattedDate.format("MMMM");
        const capitalizedMonth = monthName.charAt(0).toUpperCase() + monthName.slice(1);

        return {
          short: `${capitalizedMonth} ${formattedDate.format("YYYY")}`,
          full: `${capitalizedMonth} ${formattedDate.format("YYYY")}`,
        };
      }

      return { short: "", full: "" };
    } catch (e) {
      console.error("Error in formatDateRange:", e);
      return { short: "Erreur de date", full: "Erreur de date" };
    }
  }, []);

  // Handle date change
  const handleDateChange = (date) => {
    console.log('📅 [FILTER DEBUG] handleDateChange called with:', date);
    console.log('📅 [FILTER DEBUG] Current dateFilter:', dateFilter);
    console.log('📅 [FILTER DEBUG] Current dateRangeType:', dateRangeType);
    
    if (!date) {
      console.log('📅 [FILTER DEBUG] Date is null/undefined, resetting filter');
      resetDateFilter();
      return;
    }

    try {
      // Ensure we're working with a dayjs object
      let adjustedDate = dayjs(date);

      // Adjust the date based on the current date range type
      if (dateRangeType === "week") {
        // For week view, use the start of the ISO week (Monday)
        adjustedDate = adjustedDate.startOf("isoWeek");
      } else if (dateRangeType === "month") {
        // For month view, use the start of the month
        adjustedDate = adjustedDate.startOf("month");
      }
      // For day view, use the selected date as is

      // Store the dayjs object (not converted to Date) for better compatibility
      setDateFilter(adjustedDate);
      const { full } = formatDateRange(adjustedDate, dateRangeType);
      setDateRangeDescription(full);
      setDateFilterActive(true);

      console.log(`📅 [FILTER DEBUG] Date filter set: ${adjustedDate.format('YYYY-MM-DD')}, Range type: ${dateRangeType}`);
    } catch (e) {
      console.error("Error handling date change:", e);
      // Use the original date as fallback
      const fallbackDate = dayjs(date);
      setDateFilter(fallbackDate);
      const { full } = formatDateRange(fallbackDate, dateRangeType);
      setDateRangeDescription(full);
      setDateFilterActive(true);
    }
  };

  // Handle date range type change
  const handleDateRangeTypeChange = (type) => {
    console.log('📅 [FILTER DEBUG] handleDateRangeTypeChange called with:', type);
    setDateRangeType(type);

    // If a date is already selected, update the description and adjust the date
    if (dateFilter) {
      let date = dayjs(dateFilter);
      let adjustedDate = date;

      // Adjust date based on the new range type
      if (type === "week") {
        adjustedDate = date.startOf("isoWeek");
      } else if (type === "month") {
        adjustedDate = date.startOf("month");
      }
      // For day, keep the current date

      // Update the date filter with the adjusted date (keep as dayjs object)
      setDateFilter(adjustedDate);

      // Update the description
      const { full } = formatDateRange(adjustedDate, type);
      setDateRangeDescription(full);

      console.log(`📅 [FILTER DEBUG] Date range type changed to: ${type}, Adjusted date: ${adjustedDate.format('YYYY-MM-DD')}`);
    }
  };

  // Reset date filter
  const resetDateFilter = () => {
    setDateFilter(null);
    setDateRangeDescription("");
    setDateFilterActive(false);
  };

  // Build query parameters for API requests
  const buildDateQueryParams = useCallback(() => {
    const queryParams = new URLSearchParams();

    // Add date range type and date if a date is selected
    if (dateFilter) {
      try {
        // The date should already be adjusted based on the date range type
        // when it was selected in the date picker or when the date range type was changed
        const formattedDate = formatApiDate(dateFilter);

        if (formattedDate) {
          // Add the date and date range type to the query parameters
          queryParams.append("date", formattedDate);
          queryParams.append("dateRangeType", dateRangeType);

          console.log(`API request params: date=${formattedDate}, dateRangeType=${dateRangeType}`);
        } else {
          console.error("Failed to format date for API request:", dateFilter);
        }
      } catch (e) {
        console.error("Error building date query params:", e);
      }
    }

    return queryParams;
  }, [dateFilter, dateRangeType]);

  return {
    dateFilter,
    dateRangeType,
    dateRangeDescription,
    dateFilterActive,
    handleDateChange,
    handleDateRangeTypeChange,
    resetDateFilter,
    buildDateQueryParams,
    formatDateRange
  };
};

export default useDateFilter;
