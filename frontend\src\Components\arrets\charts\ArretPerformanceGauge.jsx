import React, { memo } from 'react';
import { Card, Progress, Row, Col, Typography, Statistic } from 'antd';
import { TrophyOutlined, ClockCircleOutlined, ToolOutlined } from '@ant-design/icons';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const { Text, Title } = Typography;

const ArretPerformanceGauge = memo(({ mttr = 0, mtbf = 0, doper = 0, loading = false }) => {
  if (loading) {
    return (
      <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div>Chargement des métriques de performance...</div>
      </div>
    );
  }

  // Calculate performance scores (0-100)
  const mttrScore = Math.max(0, Math.min(100, 100 - (mttr / 2))); // Lower MTTR is better
  const mtbfScore = Math.max(0, Math.min(100, mtbf / 10)); // Higher MTBF is better
  const doperScore = Math.max(0, Math.min(100, doper)); // DOPER is already a percentage

  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a'; // Green
    if (score >= 60) return '#faad14'; // Yellow
    if (score >= 40) return '#fa8c16'; // Orange
    return '#f5222d'; // Red
  };

  const getScoreStatus = (score) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Bon';
    if (score >= 40) return 'Moyen';
    return 'À améliorer';
  };

  return (
    <div style={{ height: 300, padding: 16 }}>
      <Row gutter={[24, 24]} style={{ height: '100%' }}>
        {/* MTTR Gauge */}
        <Col xs={24} md={8}>
          <Card 
            size="small" 
            style={{ 
              height: '100%',
              background: '#FFFFFF', // White background
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // SOMIPEM Primary Blue border
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <ClockCircleOutlined style={{ fontSize: 24, color: SOMIPEM_COLORS.PRIMARY_BLUE, marginBottom: 8 }} />
              <Title level={5} style={{ margin: 0, color: SOMIPEM_COLORS.PRIMARY_BLUE }}>MTTR</Title>
              <Progress
                type="circle"
                percent={mttrScore}
                strokeColor={getScoreColor(mttrScore)}
                size={80}
                format={() => `${mttr.toFixed(0)}min`}
                style={{ margin: '12px 0' }}
              />
              <Text style={{ display: 'block', fontSize: 12, color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                {getScoreStatus(mttrScore)}
              </Text>
              <Text style={{ display: 'block', fontSize: 11, color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Temps Moyen de Réparation
              </Text>
            </div>
          </Card>
        </Col>

        {/* MTBF Gauge */}
        <Col xs={24} md={8}>
          <Card 
            size="small" 
            style={{ 
              height: '100%',
              background: '#FFFFFF', // White background
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // SOMIPEM Primary Blue border
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <ToolOutlined style={{ fontSize: 24, color: SOMIPEM_COLORS.PRIMARY_BLUE, marginBottom: 8 }} />
              <Title level={5} style={{ margin: 0, color: SOMIPEM_COLORS.PRIMARY_BLUE }}>MTBF</Title>
              <Progress
                type="circle"
                percent={mtbfScore}
                strokeColor={getScoreColor(mtbfScore)}
                size={80}
                format={() => `${mtbf.toFixed(0)}h`}
                style={{ margin: '12px 0' }}
              />
              <Text style={{ display: 'block', fontSize: 12, color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                {getScoreStatus(mtbfScore)}
              </Text>
              <Text style={{ display: 'block', fontSize: 11, color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Temps Moyen Entre Pannes
              </Text>
            </div>
          </Card>
        </Col>

        {/* DOPER Gauge */}
        <Col xs={24} md={8}>
          <Card 
            size="small" 
            style={{ 
              height: '100%',
              background: '#FFFFFF', // White background
              border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // SOMIPEM Primary Blue border
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <TrophyOutlined style={{ fontSize: 24, color: SOMIPEM_COLORS.PRIMARY_BLUE, marginBottom: 8 }} />
              <Title level={5} style={{ margin: 0, color: SOMIPEM_COLORS.PRIMARY_BLUE }}>Disponibilité</Title>
              <Progress
                type="circle"
                percent={doperScore}
                strokeColor={getScoreColor(doperScore)}
                size={80}
                format={() => `${doper.toFixed(1)}%`}
                style={{ margin: '12px 0' }}
              />
              <Text style={{ display: 'block', fontSize: 12, color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                {getScoreStatus(doperScore)}
              </Text>
              <Text style={{ display: 'block', fontSize: 11, color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Disponibilité Opérationnelle
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Overall Performance Summary */}
      <Card 
        size="small" 
        style={{ 
          marginTop: 16,
          background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE})`, // SOMIPEM gradient
          border: 'none',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <Title level={5} style={{ margin: 0, color: 'white' }}>
            Performance Globale: {((mttrScore + mtbfScore + doperScore) / 3).toFixed(0)}/100
          </Title>
        </div>
      </Card>
    </div>
  );
});

ArretPerformanceGauge.displayName = 'ArretPerformanceGauge';

export default ArretPerformanceGauge;
