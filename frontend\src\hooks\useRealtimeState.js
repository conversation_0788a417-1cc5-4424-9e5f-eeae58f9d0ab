/**
 * Real-time State Management Hook
 * Provides WebSocket-based state synchronization for React components
 * 
 * Features:
 * - Real-time state synchronization across devices
 * - Optimistic updates with rollback
 * - Automatic reconnection handling
 * - Conflict resolution
 * - Cross-device consistency
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

// WebSocket connection states
const CONNECTION_STATES = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
};

// State sync strategies
const SYNC_STRATEGIES = {
  IMMEDIATE: 'immediate',
  DEBOUNCED: 'debounced',
  OPTIMISTIC: 'optimistic',
  COLLABORATIVE: 'collaborative'
};

export const useRealtimeState = (stateType, stateKey, initialValue = null, options = {}) => {
  const { user, token } = useAuth();
  const [state, setState] = useState(initialValue);
  const [connectionState, setConnectionState] = useState(CONNECTION_STATES.DISCONNECTED);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [conflicts, setConflicts] = useState([]);
  const [isOptimistic, setIsOptimistic] = useState(false);

  // Configuration options
  const config = {
    autoReconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    syncStrategy: SYNC_STRATEGIES.IMMEDIATE,
    debounceMs: 300,
    enableOptimisticUpdates: true,
    ...options
  };

  // Refs for WebSocket and internal state
  const wsRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef(null);
  const debounceTimeoutRef = useRef(null);
  const optimisticStateRef = useRef(null);
  const pendingUpdatesRef = useRef(new Map());

  /**
   * Initialize WebSocket connection
   */
  const initializeWebSocket = useCallback(() => {
    if (!user || !token) {
      console.warn('⚠️ Cannot initialize WebSocket: user not authenticated');
      return;
    }

    try {
      setConnectionState(CONNECTION_STATES.CONNECTING);
      
      // Construct WebSocket URL with authentication
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${wsProtocol}//${window.location.host}/api/state-sync-ws?token=${encodeURIComponent(token)}`;
      
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('✅ WebSocket connected for real-time state sync');
        setConnectionState(CONNECTION_STATES.CONNECTED);
        reconnectAttemptsRef.current = 0;

        // Subscribe to specific state updates
        ws.send(JSON.stringify({
          type: 'SUBSCRIBE_STATE',
          stateType,
          stateKey
        }));

        // Request current state
        ws.send(JSON.stringify({
          type: 'REQUEST_STATE',
          stateType,
          stateKey
        }));
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        setConnectionState(CONNECTION_STATES.DISCONNECTED);
        wsRef.current = null;

        // Attempt reconnection if enabled
        if (config.autoReconnect && reconnectAttemptsRef.current < config.maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionState(CONNECTION_STATES.ERROR);
      };

    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
      setConnectionState(CONNECTION_STATES.ERROR);
    }
  }, [user, token, stateType, stateKey, config.autoReconnect, config.maxReconnectAttempts]);

  /**
   * Handle incoming WebSocket messages
   */
  const handleWebSocketMessage = useCallback((message) => {
    switch (message.type) {
      case 'WELCOME':
        console.log('👋 WebSocket welcome message received');
        break;

      case 'INITIAL_STATE':
        if (message.states && message.states[stateType]) {
          const initialState = message.states[stateType];
          setState(initialState);
          setLastUpdate(message.timestamp);
        }
        break;

      case 'STATE_RESPONSE':
        if (message.stateType === stateType && message.stateKey === stateKey) {
          setState(message.data);
          setLastUpdate(message.timestamp);
        }
        break;

      case 'STATE_UPDATED':
        if (message.stateType === stateType && message.stateKey === stateKey) {
          // Check if this is our own optimistic update
          const updateId = `${message.stateType}:${message.stateKey}:${message.timestamp}`;
          
          if (pendingUpdatesRef.current.has(updateId)) {
            // Our optimistic update was confirmed
            pendingUpdatesRef.current.delete(updateId);
            setIsOptimistic(false);
          } else {
            // External update - apply immediately
            setState(message.data);
            setLastUpdate(message.timestamp);
          }
        }
        break;

      case 'STATE_UPDATE_CONFIRMED':
        if (message.stateType === stateType && message.stateKey === stateKey) {
          // Our update was confirmed
          setIsOptimistic(false);
          setLastUpdate(message.timestamp);
        }
        break;

      case 'CONFLICT_DETECTED':
        console.warn('⚠️ State conflict detected:', message.conflict);
        setConflicts(prev => [...prev, message.conflict]);
        break;

      case 'ERROR':
        console.error('❌ WebSocket error message:', message.message);
        break;

      default:
        console.log('📨 Unknown WebSocket message type:', message.type);
    }
  }, [stateType, stateKey]);

  /**
   * Schedule reconnection attempt
   */
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    setConnectionState(CONNECTION_STATES.RECONNECTING);
    reconnectAttemptsRef.current++;

    const delay = config.reconnectInterval * Math.pow(2, reconnectAttemptsRef.current - 1);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      console.log(`🔄 Attempting WebSocket reconnection (${reconnectAttemptsRef.current}/${config.maxReconnectAttempts})`);
      initializeWebSocket();
    }, delay);
  }, [config.reconnectInterval, config.maxReconnectAttempts, initializeWebSocket]);

  /**
   * Update state with real-time synchronization
   */
  const updateState = useCallback((newValue, strategy = config.syncStrategy) => {
    const timestamp = Date.now();
    const updateId = `${stateType}:${stateKey}:${timestamp}`;

    // Apply optimistic update if enabled
    if (config.enableOptimisticUpdates && strategy === SYNC_STRATEGIES.OPTIMISTIC) {
      optimisticStateRef.current = state;
      setState(newValue);
      setIsOptimistic(true);
    }

    // Send update to server
    const sendUpdate = () => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        pendingUpdatesRef.current.set(updateId, {
          originalValue: state,
          newValue,
          timestamp
        });

        wsRef.current.send(JSON.stringify({
          type: 'STATE_UPDATE',
          stateType,
          stateKey,
          data: newValue,
          strategy,
          timestamp
        }));
      } else {
        console.warn('⚠️ WebSocket not connected - state update queued');
        // Queue update for when connection is restored
        // This could be enhanced with local storage persistence
      }
    };

    // Apply debouncing if configured
    if (strategy === SYNC_STRATEGIES.DEBOUNCED) {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      debounceTimeoutRef.current = setTimeout(sendUpdate, config.debounceMs);
    } else {
      sendUpdate();
    }

    // Update local state if not using optimistic updates
    if (!config.enableOptimisticUpdates || strategy !== SYNC_STRATEGIES.OPTIMISTIC) {
      setState(newValue);
    }

    setLastUpdate(timestamp);
  }, [state, stateType, stateKey, config.syncStrategy, config.enableOptimisticUpdates, config.debounceMs]);

  /**
   * Rollback optimistic update
   */
  const rollbackOptimisticUpdate = useCallback(() => {
    if (optimisticStateRef.current !== null) {
      setState(optimisticStateRef.current);
      setIsOptimistic(false);
      optimisticStateRef.current = null;
    }
  }, []);

  /**
   * Resolve conflict
   */
  const resolveConflict = useCallback((conflictId, resolution) => {
    setConflicts(prev => prev.filter(c => c.id !== conflictId));
    
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'RESOLVE_CONFLICT',
        conflictId,
        resolution
      }));
    }
  }, []);

  /**
   * Manually reconnect WebSocket
   */
  const reconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    reconnectAttemptsRef.current = 0;
    initializeWebSocket();
  }, [initializeWebSocket]);

  // Initialize WebSocket on mount
  useEffect(() => {
    initializeWebSocket();

    // Cleanup on unmount
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [initializeWebSocket]);

  // Return state and control functions
  return {
    // State data
    state,
    setState: updateState,
    
    // Connection status
    connectionState,
    isConnected: connectionState === CONNECTION_STATES.CONNECTED,
    
    // Metadata
    lastUpdate,
    isOptimistic,
    conflicts,
    
    // Control functions
    rollbackOptimisticUpdate,
    resolveConflict,
    reconnect,
    
    // Sync strategies
    SYNC_STRATEGIES
  };
};
