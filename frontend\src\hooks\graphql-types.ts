/**
 * TypeScript type definitions for GraphQL hooks
 * Provides type safety for GraphQL queries and responses
 */

// Filter types
export interface FilterInput {
  date?: string;
  dateRangeType?: 'day' | 'week' | 'month';
  model?: string;
  machine?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface StopFilterInput {
  date?: string;
  dateRangeType?: 'day' | 'week' | 'month';
  model?: string;
  machine?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// Daily Production Types
export interface DailyProduction {
  Machine_Name: string;
  Date_Insert_Day: string;
  Run_Hours_Day: string;
  Down_Hours_Day: string;
  Good_QTY_Day: string;
  Rejects_QTY_Day: string;
  Speed_Day: number;
  Availability_Rate_Day: number;
  Performance_Rate_Day: number;
  Quality_Rate_Day: number;
  OEE_Day: number;
  Shift: string;
}

export interface ProductionChart {
  Date_Insert_Day: string;
  Total_Good_Qty_Day: number;
  Total_Rejects_Qty_Day: number;
  OEE_Day: number;
  Speed_Day: number;
  Availability_Rate_Day: number;
  Performance_Rate_Day: number;
  Quality_Rate_Day: number;
}

export interface ProductionSidecards {
  goodqty: number;
  rejetqty: number;
}

export interface MachineModel {
  model: string;
}

export interface MachineName {
  Machine_Name: string;
}

export interface MachinePerformance {
  Machine_Name: string;
  Shift: string;
  production: number;
  rejects: number;
  availability: number;
  performance: number;
  oee: number;
  quality: number;
  disponibilite: number;
}

export interface AvailabilityTrend {
  date: string;
  machine: string;
  disponibilite: number;
}

export interface PerformanceMetrics {
  machine: string;
  model: string;
  disponibilite: number;
  stops: number;
  mttr: number;
  mtbf: number;
}

// Stop Table Types
export interface MachineStop {
  Machine_Name: string;
  Date_Insert: string;
  Debut_Stop: string;
  Fin_Stop_Time: string;
  Code_Stop: string;
  Libelle_Arret: string;
  Regleur_Prenom: string;
  Regleur_Nom: string;
  duration_minutes: number;
}

export interface TopStop {
  stopName: string;
  count: number;
}

export interface StopStats {
  Stop_Date: string;
  Total_Stops: number;
}

export interface StopSidecards {
  Arret_Totale: number;
  Arret_Totale_nondeclare: number;
}

export interface StopDurationTrend {
  date: string;
  avgDuration: number;
  totalDuration: number;
  stopCount: number;
}

export interface MachineStopComparison {
  Machine_Name: string;
  totalStops: number;
  avgDuration: number;
  totalDowntime: number;
}

export interface OperatorStopStats {
  operatorName: string;
  stopCount: number;
  avgResolutionTime: number;
  totalDowntime: number;
}

export interface StopReasonsAnalysis {
  stopReason: string;
  count: number;
  percentage: number;
  avgDuration: number;
}

// Composite response types
export interface DashboardData {
  productionChart: ProductionChart[];
  sidecards: ProductionSidecards;
  machinePerformance: MachinePerformance[];
  availabilityTrend: AvailabilityTrend[];
}

export interface StopDashboardData {
  allStops: MachineStop[];
  topStops: TopStop[];
  sidecards: StopSidecards;
  stopComparison: MachineStopComparison[];
  reasonsAnalysis: StopReasonsAnalysis[];
}

export interface StopsAnalysisData {
  durationTrend: StopDurationTrend[];
  operatorStats: OperatorStopStats[];
  reasonsAnalysis: StopReasonsAnalysis[];
  stopStats: StopStats[];
}

// Hook return types
export interface DailyTableGraphQLHook {
  loading: boolean;
  error: string | null;
  getAllDailyProduction: () => Promise<{ getAllDailyProduction: DailyProduction[] }>;
  getProductionChart: (filters?: FilterInput) => Promise<{ getProductionChart: ProductionChart[] }>;
  getProductionSidecards: (filters?: FilterInput) => Promise<{ getProductionSidecards: ProductionSidecards }>;
  getUniqueDates: () => Promise<{ getUniqueDates: string[] }>;
  getMachineModels: () => Promise<{ getMachineModels: MachineModel[] }>;
  getMachineNames: () => Promise<{ getMachineNames: MachineName[] }>;
  getMachinePerformance: (filters?: FilterInput) => Promise<{ getMachinePerformance: MachinePerformance[] }>;
  getAvailabilityTrend: (filters?: FilterInput) => Promise<{ getAvailabilityTrend: AvailabilityTrend[] }>;
  getPerformanceMetrics: (filters?: FilterInput) => Promise<{ getPerformanceMetrics: PerformanceMetrics[] }>;
  getDashboardData: (filters?: FilterInput) => Promise<DashboardData>;
  executeQuery: (query: string, variables?: any) => Promise<any>;
}

export interface StopTableGraphQLHook {
  loading: boolean;
  error: string | null;
  getAllMachineStops: (filters?: StopFilterInput) => Promise<{ getAllMachineStops: MachineStop[] }>;
  getTop5Stops: (filters?: StopFilterInput) => Promise<{ getTop5Stops: TopStop[] }>;
  getStopStats: (filters?: StopFilterInput) => Promise<{ getStopStats: StopStats[] }>;
  getUniqueStopDates: (filters?: StopFilterInput) => Promise<{ getUniqueStopDates: string[] }>;
  getStopDetailsByDate: (filters?: StopFilterInput) => Promise<{ getStopDetailsByDate: MachineStop[] }>;
  getStopDurationTrend: (filters?: StopFilterInput) => Promise<{ getStopDurationTrend: StopDurationTrend[] }>;
  getMachineStopComparison: (filters?: StopFilterInput) => Promise<{ getMachineStopComparison: MachineStopComparison[] }>;
  getOperatorStopStats: (filters?: StopFilterInput) => Promise<{ getOperatorStopStats: OperatorStopStats[] }>;
  getStopSidecards: (filters?: StopFilterInput) => Promise<{ getStopSidecards: StopSidecards }>;
  getStopMachineModels: () => Promise<{ getStopMachineModels: MachineModel[] }>;
  getStopMachineNames: () => Promise<{ getStopMachineNames: MachineName[] }>;
  getStopReasonsAnalysis: (filters?: StopFilterInput) => Promise<{ getStopReasonsAnalysis: StopReasonsAnalysis[] }>;
  getStopDashboardData: (filters?: StopFilterInput) => Promise<StopDashboardData>;
  getStopsAnalysisData: (filters?: StopFilterInput) => Promise<StopsAnalysisData>;
  executeQuery: (query: string, variables?: any) => Promise<any>;
}
