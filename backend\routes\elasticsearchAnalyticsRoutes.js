/**
 * 📊 Elasticsearch Analytics Routes
 * 
 * Phase 3: Elasticsearch Optimization Routes
 * High-performance analytics endpoints for Production and Arrets dashboards
 * Implements sub-second response times with advanced aggregations
 */

import express from 'express';
import elasticsearchProductionService from '../services/ElasticsearchProductionService.js';
import elasticsearchStopsService from '../services/ElasticsearchStopsService.js';
import elasticsearchDataIndexer from '../services/ElasticsearchDataIndexer.js';
import auth from '../middleware/auth.js';
import rateLimiter from '../middleware/rateLimiter.js';

const router = express.Router();

// Apply authentication and rate limiting to all routes except health endpoint
router.use((req, res, next) => {
  // Skip authentication for health endpoint
  if (req.path === '/health') {
    return next();
  }
  // Apply authentication for all other routes
  return auth(req, res, next);
});

router.use(rateLimiter.analyticsLimiter());

/**
 * 🏭 PRODUCTION ANALYTICS ENDPOINTS
 */

/**
 * GET /api/elasticsearch/production/dashboard
 * Real-time production dashboard data with sub-second response
 */
router.get('/production/dashboard', async (req, res) => {
  try {
    const { machine_ids, start_date, end_date } = req.query;
    
    const machineIds = machine_ids ? machine_ids.split(',').map(id => parseInt(id)) : [];
    const dateRange = {};
    
    if (start_date) dateRange.start = start_date;
    if (end_date) dateRange.end = end_date;
    
    const startTime = Date.now();
    const dashboardData = await elasticsearchProductionService.getDashboardData(machineIds, dateRange);
    const responseTime = Date.now() - startTime;
    
    res.json({
      success: true,
      data: dashboardData,
      meta: {
        response_time_ms: responseTime,
        elasticsearch_query_time_ms: dashboardData.query_time_ms,
        cache_status: responseTime < 50 ? 'hit' : 'miss',
        machine_count: machineIds.length || 'all',
        date_range: dateRange
      }
    });
    
  } catch (error) {
    console.error('❌ Production dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch production dashboard data',
      error: error.message
    });
  }
});

/**
 * GET /api/elasticsearch/production/analytics
 * Advanced production analytics with complex aggregations
 */
router.get('/production/analytics', async (req, res) => {
  try {
    const {
      machine_ids,
      start_date,
      end_date,
      shift,
      operator,
      min_oee,
      page = 1,
      limit = 100,
      sort_by = 'date',
      sort_order = 'desc'
    } = req.query;
    
    const filters = {};
    
    if (machine_ids) {
      filters.machineIds = machine_ids.split(',').map(id => parseInt(id));
    }
    
    if (start_date || end_date) {
      filters.dateRange = {};
      if (start_date) filters.dateRange.start = start_date;
      if (end_date) filters.dateRange.end = end_date;
    }
    
    if (shift) filters.shift = shift;
    if (operator) filters.operator = operator;
    if (min_oee) filters.minOEE = parseFloat(min_oee);
    
    const options = {
      size: Math.min(parseInt(limit), 1000), // Max 1000 records
      from: (parseInt(page) - 1) * parseInt(limit),
      sort: [{ [sort_by]: { order: sort_order } }]
    };
    
    const startTime = Date.now();
    const analytics = await elasticsearchProductionService.getProductionAnalytics(filters, options);
    const responseTime = Date.now() - startTime;
    
    res.json({
      success: true,
      data: {
        records: analytics.records,
        aggregations: analytics.aggregations,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: analytics.total,
          pages: Math.ceil(analytics.total / parseInt(limit))
        }
      },
      meta: {
        response_time_ms: responseTime,
        elasticsearch_query_time_ms: analytics.query_time_ms,
        filters_applied: Object.keys(filters).length,
        cache_status: responseTime < 100 ? 'hit' : 'miss'
      }
    });
    
  } catch (error) {
    console.error('❌ Production analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch production analytics',
      error: error.message
    });
  }
});

/**
 * POST /api/elasticsearch/production/index
 * Index production data for analytics (admin only)
 */
router.post('/production/index', async (req, res) => {
  try {
    // Check if user has admin privileges
    if (req.user.role !== 'admin' && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient privileges for data indexing'
      });
    }
    
    const { production_data } = req.body;
    
    if (!Array.isArray(production_data) || production_data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid production data format'
      });
    }
    
    const startTime = Date.now();
    const result = await elasticsearchProductionService.indexProductionData(production_data);
    const responseTime = Date.now() - startTime;
    
    if (result.success) {
      res.json({
        success: true,
        message: `Successfully indexed ${result.indexed} production records`,
        data: {
          indexed_count: result.indexed,
          processing_time_ms: responseTime
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        errors: result.errors
      });
    }
    
  } catch (error) {
    console.error('❌ Production indexing error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to index production data',
      error: error.message
    });
  }
});

/**
 * 🛑 MACHINE STOPS ANALYTICS ENDPOINTS
 */

/**
 * GET /api/elasticsearch/stops/dashboard
 * Real-time downtime dashboard for Arrets page
 */
router.get('/stops/dashboard', async (req, res) => {
  try {
    const { machine_ids, start_date, end_date } = req.query;
    
    const machineIds = machine_ids ? machine_ids.split(',').map(id => parseInt(id)) : [];
    const dateRange = {};
    
    if (start_date) dateRange.start = start_date;
    if (end_date) dateRange.end = end_date;
    
    const startTime = Date.now();
    const dashboardData = await elasticsearchStopsService.getDowntimeDashboard(machineIds, dateRange);
    const responseTime = Date.now() - startTime;
    
    res.json({
      success: true,
      data: dashboardData,
      meta: {
        response_time_ms: responseTime,
        elasticsearch_query_time_ms: dashboardData.query_time_ms,
        cache_status: responseTime < 50 ? 'hit' : 'miss',
        machine_count: machineIds.length || 'all',
        date_range: dateRange
      }
    });
    
  } catch (error) {
    console.error('❌ Stops dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch stops dashboard data',
      error: error.message
    });
  }
});

/**
 * GET /api/elasticsearch/stops/analytics
 * Advanced stops analytics with downtime analysis
 */
router.get('/stops/analytics', async (req, res) => {
  try {
    const {
      machine_ids,
      start_date,
      end_date,
      stop_category,
      severity,
      resolved,
      page = 1,
      limit = 100,
      sort_by = 'dateInsert',
      sort_order = 'desc'
    } = req.query;
    
    const filters = {};
    
    if (machine_ids) {
      filters.machineIds = machine_ids.split(',').map(id => parseInt(id));
    }
    
    if (start_date || end_date) {
      filters.dateRange = {};
      if (start_date) filters.dateRange.start = start_date;
      if (end_date) filters.dateRange.end = end_date;
    }
    
    if (stop_category) filters.stopCategory = stop_category;
    if (severity) filters.severity = severity;
    if (resolved !== undefined) filters.resolved = resolved === 'true';
    
    const options = {
      size: Math.min(parseInt(limit), 1000),
      from: (parseInt(page) - 1) * parseInt(limit),
      sort: [{ [sort_by]: { order: sort_order } }]
    };
    
    const startTime = Date.now();
    const analytics = await elasticsearchStopsService.getStopsAnalytics(filters, options);
    const responseTime = Date.now() - startTime;
    
    res.json({
      success: true,
      data: {
        stops: analytics.stops,
        analytics: analytics.analytics,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: analytics.total,
          pages: Math.ceil(analytics.total / parseInt(limit))
        }
      },
      meta: {
        response_time_ms: responseTime,
        elasticsearch_query_time_ms: analytics.query_time_ms,
        filters_applied: Object.keys(filters).length,
        cache_status: responseTime < 100 ? 'hit' : 'miss'
      }
    });
    
  } catch (error) {
    console.error('❌ Stops analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch stops analytics',
      error: error.message
    });
  }
});

/**
 * POST /api/elasticsearch/stops/index
 * Index stops data for analytics (admin only)
 */
router.post('/stops/index', async (req, res) => {
  try {
    // Check if user has admin privileges
    if (req.user.role !== 'admin' && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient privileges for data indexing'
      });
    }
    
    const { stops_data } = req.body;
    
    if (!Array.isArray(stops_data) || stops_data.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid stops data format'
      });
    }
    
    const startTime = Date.now();
    const result = await elasticsearchStopsService.indexStopsData(stops_data);
    const responseTime = Date.now() - startTime;
    
    if (result.success) {
      res.json({
        success: true,
        message: `Successfully indexed ${result.indexed} stop records`,
        data: {
          indexed_count: result.indexed,
          processing_time_ms: responseTime
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        errors: result.errors
      });
    }
    
  } catch (error) {
    console.error('❌ Stops indexing error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to index stops data',
      error: error.message
    });
  }
});

/**
 * 🔧 UTILITY ENDPOINTS
 */

/**
 * GET /api/elasticsearch/health
 * Elasticsearch cluster health status
 */
router.get('/health', async (req, res) => {
  try {
    const { checkElasticsearchHealth } = await import('../config/elasticsearch.js');
    const isHealthy = await checkElasticsearchHealth();

    res.json({
      success: true,
      elasticsearch: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to check Elasticsearch health',
      error: error.message
    });
  }
});

/**
 * POST /api/elasticsearch/index/full
 * Perform full data indexing from MySQL to Elasticsearch (admin only)
 */
router.post('/index/full', async (req, res) => {
  try {
    // Check if user has admin privileges
    if (req.user.role !== 'admin' && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient privileges for full data indexing'
      });
    }

    // Check if indexing is already in progress
    if (elasticsearchDataIndexer.isIndexingInProgress()) {
      return res.status(409).json({
        success: false,
        message: 'Data indexing is already in progress'
      });
    }

    // Start full indexing (async)
    elasticsearchDataIndexer.performFullIndexing()
      .then(result => {
        console.log('✅ Full indexing completed:', result);
      })
      .catch(error => {
        console.error('❌ Full indexing failed:', error);
      });

    res.json({
      success: true,
      message: 'Full data indexing started in background',
      status: 'started'
    });

  } catch (error) {
    console.error('❌ Full indexing error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start full data indexing',
      error: error.message
    });
  }
});

/**
 * POST /api/elasticsearch/index/incremental
 * Perform incremental data indexing (recent data only)
 */
router.post('/index/incremental', async (req, res) => {
  try {
    // Check if user has admin privileges
    if (req.user.role !== 'admin' && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient privileges for data indexing'
      });
    }

    const { hours = 24 } = req.body;

    // Check if indexing is already in progress
    if (elasticsearchDataIndexer.isIndexingInProgress()) {
      return res.status(409).json({
        success: false,
        message: 'Data indexing is already in progress'
      });
    }

    const result = await elasticsearchDataIndexer.performIncrementalIndexing(parseInt(hours));

    if (result.success) {
      res.json({
        success: true,
        message: `Incremental indexing completed for last ${hours} hours`,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message
      });
    }

  } catch (error) {
    console.error('❌ Incremental indexing error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform incremental indexing',
      error: error.message
    });
  }
});

/**
 * GET /api/elasticsearch/index/status
 * Get indexing status
 */
router.get('/index/status', async (req, res) => {
  try {
    const status = elasticsearchDataIndexer.getStatus();

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get indexing status',
      error: error.message
    });
  }
});

export default router;
