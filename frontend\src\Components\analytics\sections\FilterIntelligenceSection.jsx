import React from 'react';
import { Card, Row, Col, Space, Badge } from 'antd';
import { ThunderboltOutlined } from '@ant-design/icons';

const FilterIntelligenceSection = ({ loading, filters }) => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Card style={{ textAlign: 'center', border: 'none', background: 'transparent' }}>
        <Space direction="vertical" align="center" size="large">
          <ThunderboltOutlined style={{ fontSize: '64px', color: '#13c2c2' }} />
          <h2 style={{ color: '#13c2c2', margin: 0 }}>Filter Intelligence</h2>
          <p style={{ color: '#8c8c8c' }}>Smart filtering and data intelligence coming soon</p>
        </Space>
      </Card>
    </div>
  );
};

export default FilterIntelligenceSection;
