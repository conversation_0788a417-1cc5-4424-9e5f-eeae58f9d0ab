/**
 * Simplified Optimized GraphQL resolver for testing
 * Basic version that just gets the main data without complex aggregations
 */

import { GraphQLObjectType, GraphQLString, GraphQLInt, GraphQLFloat, GraphQLList, GraphQLInputObjectType } from 'graphql';
import { executeQuery } from '../../utils/dbUtils.js';

// Simplified comprehensive data type
const SimpleComprehensiveStopDataType = new GraphQLObjectType({
  name: 'SimpleComprehensiveStopData',
  fields: {
    allStops: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'SimpleStopData',
        fields: {
          Machine_Name: { type: GraphQLString },
          Date_Insert: { type: GraphQLString },
          Code_Stop: { type: GraphQLString },
          Debut_Stop: { type: GraphQLString },
          Fin_Stop_Time: { type: GraphQLString },
          Regleur_Prenom: { type: GraphQLString },
          duration_minutes: { type: GraphQLInt }
        }
      }))
    },
    
    totalRecords: { type: GraphQLInt },
    queryExecutionTime: { type: GraphQLFloat }
  }
});

// Simple filter input
const SimpleStopFilterInputType = new GraphQLInputObjectType({
  name: 'SimpleStopFilterInput',
  fields: {
    model: { type: GraphQLString },
    machine: { type: GraphQLString },
    startDate: { type: GraphQLString },
    endDate: { type: GraphQLString },
    limit: { type: GraphQLInt }
  }
});

// Helper functions
const addMachineFilters = (conditions, queryParams, filters) => {
  const { model, machine } = filters;

  if (model && !machine) {
    conditions.push(`Machine_Name LIKE ?`);
    queryParams.push(`${model}%`);
  } else if (machine) {
    conditions.push(`Machine_Name = ?`);
    queryParams.push(machine);
  }
};

const addDateRangeFilter = (conditions, queryParams, filters) => {
  const { startDate, endDate } = filters;

  const parseDateColumn = `
    COALESCE(
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i'), '%Y-%m-%d')
    )
  `;

  if (startDate && endDate) {
    conditions.push(`${parseDateColumn} BETWEEN ? AND ?`);
    queryParams.push(startDate, endDate);
  }
};

// Simplified resolver
const simpleOptimizedStopResolvers = {
  getSimpleComprehensiveStopData: {
    type: SimpleComprehensiveStopDataType,
    args: {
      filters: { type: SimpleStopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      console.log('🚀 SIMPLE: getSimpleComprehensiveStopData called with filters:', JSON.stringify(filters, null, 2));
      
      const startTime = Date.now();
      const limit = filters.limit || 100;
      
      try {
        // Build simple query conditions
        const conditions = [];
        const queryParams = [];
        
        // Apply machine filtering
        addMachineFilters(conditions, queryParams, filters);
        
        // Apply date filtering
        if (filters.startDate && filters.endDate) {
          addDateRangeFilter(conditions, queryParams, filters);
        }
        
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        
        // Simple single query to get basic stop data
        const query = `
          SELECT 
            Machine_Name,
            Date_Insert,
            Part_NO,
            Code_Stop,
            Debut_Stop,
            Fin_Stop_Time,
            Regleur_Prenom,
            COALESCE(
              TIMESTAMPDIFF(MINUTE,
                STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
              ), 15
            ) AS duration_minutes
          FROM machine_stop_table_mould
          ${whereClause}
          ORDER BY Date_Insert DESC
          LIMIT ${limit}
        `;
        
        console.log('📊 SIMPLE: Executing query with params:', queryParams);
        
        const { success, data, error } = await executeQuery(query, queryParams);
        
        if (!success) {
          console.error('❌ SIMPLE: Database query failed:', error);
          throw new Error(`Database query failed: ${error}`);
        }
        
        const executionTime = Date.now() - startTime;
        
        console.log(`✅ SIMPLE: Query completed in ${executionTime}ms`);
        console.log(`📊 SIMPLE: Retrieved ${data.length} records`);
        
        return {
          allStops: data || [],
          totalRecords: data ? data.length : 0,
          queryExecutionTime: executionTime
        };
        
      } catch (error) {
        console.error('❌ SIMPLE: getSimpleComprehensiveStopData error:', error);
        throw new Error(`Failed to fetch simple comprehensive stop data: ${error.message}`);
      }
    }
  }
};

// Export for schema integration
export const simpleOptimizedStopQueries = simpleOptimizedStopResolvers;
export const simpleOptimizedStopTypes = {
  SimpleComprehensiveStopDataType,
  SimpleStopFilterInputType
};

export default simpleOptimizedStopResolvers;
