import{u as a}from"./index-N0wOiMt6.js";function u(){const{user:s,isAuthenticated:e}=a();return{hasPermission:r=>{if(console.log("🔍 [usePermission] Checking permissions:",{requiredPermissions:r,isAuthenticated:e,user:s?{role:s.role,permissions:s.all_permissions}:null}),!e||!s)return console.log("🔍 [usePermission] User not authenticated"),!1;if(s.role==="admin")return console.log("🔍 [usePermission] User is admin - granting access"),!0;const i=Array.isArray(r)?r:[r],n=s.all_permissions?s.all_permissions:[...Array.isArray(s.permissions)?s.permissions:[],...Array.isArray(s.role_permissions)?s.role_permissions:[],...Array.isArray(s.hierarchy_permissions)?s.hierarchy_permissions:[]];return i.some(o=>n.includes(o))},hasDepartmentAccess:r=>!e||!s?!1:s.role==="admin"||s.permissions&&s.permissions.includes("view_all_departments")?!0:(Array.isArray(r)?r:[r]).includes(s.department_id),hasRole:r=>!e||!s?!1:(Array.isArray(r)?r:[r]).includes(s.role)}}export{u};
