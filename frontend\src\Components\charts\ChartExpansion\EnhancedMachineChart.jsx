import React, { memo, useMemo } from "react";
import {
  <PERSON>sponsive<PERSON><PERSON>r,
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
} from "recharts";
import { Empty } from "antd";
import { useDynamicChartConfig } from "../../../hooks/useUnifiedChartConfig";
import SOMIPEM_COLORS from "../../../styles/brand-colors";

/**
 * Enhanced Dynamic Machine Chart - Can render as Bar or Line based on settings
 * Replaces EnhancedMachineProductionChart with dynamic chart type support
 */
const EnhancedMachineChart = memo(({
  data,
  title,
  dataKey = "production",
  color,
  label = "Production",
  tooltipLabel,
  isKg = true,
  chartType: propChartType, // Override setting if provided
  allowedTypes = ['bar', 'line'], // Restrict which types are allowed
  enhanced = false,
  expanded = false,
  isModal = false,
  height = 300,
  ...otherProps
}) => {
  // Get unified chart configuration with dynamic type support
  const chartConfig = useDynamicChartConfig({
    fallbackType: 'bar',
    allowedTypes,
    propChartType
  });

  // Extract chart type from unified config
  const chartType = chartConfig.chartType;

  // Process and validate data
  const processedData = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) {
      return [];
    }

    return data.map(item => {
      // Handle different data structures for machine data - FIXED: Added Machine_Name
      const machineValue = item.Machine_Name || item.Machine || item.machine || item.name || item.label || 'N/A';
      const dataValue = item[dataKey] || 0;
      
      return {
        ...item,
        machine: machineValue,
        [dataKey]: Number(dataValue) || 0,
        // Preserve original data structure
        originalData: item
      };
    }).filter(item => item[dataKey] !== undefined && item[dataKey] !== null);
  }, [data, dataKey]);

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }) => {
    const theme = chartConfig.theme || {};
    if (active && payload && payload.length) {
      const data = payload[0];
      const value = data.value;
      const formattedValue = isKg ? `${value.toLocaleString()} Pcs` : value.toLocaleString();
      
      return (
        <div style={{
          backgroundColor: theme.darkMode ? '#1f1f1f' : '#ffffff',
          border: `1px solid ${color || SOMIPEM_COLORS.PRIMARY_BLUE}`,
          borderRadius: '8px',
          padding: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          fontSize: '12px'
        }}>
          <p style={{ 
            margin: 0, 
            fontWeight: 'bold',
            color: theme.darkMode ? '#ffffff' : '#000000'
          }}>
            {`Machine: ${label}`}
          </p>
          <p style={{ 
            margin: 0, 
            color: color || SOMIPEM_COLORS.PRIMARY_BLUE 
          }}>
            {`${tooltipLabel || label}: ${formattedValue}`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Prepare chart props
  const chartProps = {
    data: processedData,
    margin: chartConfig.margins,
    ...otherProps
  };

  // Render appropriate chart type
  const renderChart = () => {
    const finalColor = color || SOMIPEM_COLORS.PRIMARY_BLUE;
    
    switch (chartType) {
      case 'line':
        return (
          <LineChart {...chartProps}>
            <CartesianGrid {...chartConfig.gridConfig} />
            <XAxis 
              dataKey="machine" 
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
              label={{ 
                value: isKg ? 'Production (Pcs)' : 'Valeur', 
                angle: -90, 
                position: 'insideLeft',
                style: { textAnchor: 'middle' }
              }}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            {chartConfig.charts?.showLegend && <Legend {...chartConfig.legendConfig} />}
            <Line
              type="monotone"
              dataKey={dataKey}
              name={tooltipLabel || title}
              {...chartConfig.getLineElementConfig(finalColor)}
            />
          </LineChart>
        );

      case 'bar':
      default:
        return (
          <BarChart {...chartProps}>
            <CartesianGrid {...chartConfig.gridConfig} />
            <XAxis 
              dataKey="machine" 
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              {...chartConfig.axisConfig}
              tick={{ fontSize: 11 }}
              label={{ 
                value: isKg ? 'Production (Pcs)' : 'Valeur', 
                angle: -90, 
                position: 'insideLeft',
                style: { textAnchor: 'middle' }
              }}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            {chartConfig.charts?.showLegend && <Legend {...chartConfig.legendConfig} />}
            <Bar
              dataKey={dataKey}
              name={tooltipLabel || title}
              {...chartConfig.getBarElementConfig(finalColor)}
              maxBarSize={enhanced || expanded ? 60 : 40}
            />
          </BarChart>
        );
    }
  };

  // Handle loading state
  if (!processedData || processedData.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: height,
        width: '100%'
      }}>
        <Empty 
          description="Aucune donnée disponible pour les machines"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div style={{ width: '100%', height: height }}>
      <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
        {renderChart()}
      </ResponsiveContainer>
    </div>
  );
});

EnhancedMachineChart.displayName = 'EnhancedMachineChart';

export default EnhancedMachineChart;
