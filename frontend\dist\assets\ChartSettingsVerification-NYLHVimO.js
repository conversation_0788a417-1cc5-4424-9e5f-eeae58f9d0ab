import{c2 as fe,S as h,h as pe,r as ne,R as e,T as ue,l as Ce,A as ae,C as d,b as ie,c as b,d as Ee,f as oe,D as be}from"./index-N0wOiMt6.js";import{R as ye}from"./CheckCircleOutlined-DcDkn_d7.js";import{R as xe}from"./CloseCircleOutlined-kKLfKks7.js";import{R as ve}from"./BarChartOutlined-DzqCoGDG.js";import{R as v,B as we,C as se,X as re,Y as le,T as w,a as D,g as De,L as ke,d as Ae,P as Me,e as Se,f as Re}from"./PieChart-BZME-zsX.js";import{R as Pe}from"./LineChartOutlined-2kYZXigx.js";import{R as Le}from"./PieChartOutlined-BTCQURIB.js";class Te{constructor(n={}){this.settings=n,this.charts=n.charts||{},this.theme=n.theme||{},this._unifiedManager=new fe(n),console.warn("EnhancedRechartsConfig is deprecated. Please use useUnifiedChartConfig hook instead.")}updateSettings(n){this.settings=n,this.charts=n.charts||{},this.theme=n.theme||{},this._unifiedManager.updateSettings(n)}getChartHeight(){return this._unifiedManager.getChartHeight()}getChartMargins(){return this._unifiedManager.getChartMargins()}getColorScheme(){return this._unifiedManager.getColorScheme()}getAnimationConfig(){return{animationBegin:0,animationDuration:this.theme.animationsEnabled&&this.theme.chartAnimations?750:0,animationEasing:"ease-in-out"}}getGridConfig(){const a=(this.charts.dataDisplay||{}).showGridLines!==!1;return{stroke:this.theme.darkMode?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)",strokeDasharray:a?"3 3":"none",opacity:a?1:0}}getAxisConfig(){return this._unifiedManager.getAxisConfig()}getTooltipConfig(){return this._unifiedManager.getTooltipConfig()}getLegendConfig(){return this._unifiedManager.getLegendConfig()}getDataLabelConfig(){return this._unifiedManager.getDisplayConfig().showDataLabels?{fill:this._unifiedManager.getTextColor(),fontSize:11,fontWeight:"bold"}:{dataKey:null}}getHoverEffectsConfig(){var a;return((a=this.charts.interactions)==null?void 0:a.hoverEffects)!==!1?{cursor:"pointer",style:{filter:"brightness(1.1)",transition:"all 0.2s ease"}}:{onMouseEnter:null,onMouseLeave:null,cursor:"default"}}getZoomConfig(){var a;return((a=this.charts.interactions)==null?void 0:a.enableZoom)!==!1?{enabled:!0,mode:"x",wheelSensitivity:.1,minZoom:.1,maxZoom:10,onWheel:i=>{(i.ctrlKey||i.metaKey)&&(i.preventDefault(),console.log("Zoom gesture detected:",i.deltaY))},onDoubleClick:()=>{console.log("Reset zoom on double click")}}:{enabled:!1,onWheel:null,onMouseDown:null,onMouseMove:null,onMouseUp:null}}getClickToExpandConfig(){var a;const n=((a=this.charts.interactions)==null?void 0:a.clickToExpand)!==!1;return{enabled:n,cursor:n?"pointer":"default"}}getAnimationConfig(){var t,r;const n=((t=this.settings.theme)==null?void 0:t.chartAnimations)!==!1,a=((r=this.settings.theme)==null?void 0:r.animationsEnabled)!==!1;return n&&a?{isAnimationActive:!0,animationBegin:0,animationDuration:800,animationEasing:"ease-out"}:{isAnimationActive:!1,animationBegin:0,animationDuration:0}}getChartProps(n="bar"){const a=this.getChartMargins(),i=this.getAnimationConfig(),t=this.getHoverEffectsConfig(),r=this.getClickToExpandConfig();return{width:"100%",height:this.getChartHeight(),margin:a,...i,style:{cursor:r.enabled?"pointer":"default",...t.style}}}getBarChartConfig(){const n=this.getColorScheme(),a=this.getHoverEffectsConfig(),i=this.getAnimationConfig(),t=n&&n.length>0?n[0]:h.PRIMARY_BLUE;return{...this.getChartProps("bar"),barProps:{fill:t,radius:[4,4,0,0],...a,...i}}}getBarElementConfig(n,a=0){const i=this.getHoverEffectsConfig(),t=this.getAnimationConfig(),r=this.getColorScheme();let g;if(n)g=n;else if(r&&r.length>0)g=r[a%r.length];else{const c=[h.PRIMARY_BLUE,h.SECONDARY_BLUE,h.CHART_TERTIARY,h.SUCCESS_GREEN,h.WARNING_ORANGE];g=c[a%c.length]}return{fill:g,radius:[4,4,0,0],...t,...i.cursor==="pointer"&&{onMouseEnter:(c,p)=>{},onMouseLeave:(c,p)=>{},style:{...i.style,cursor:i.cursor}}}}getLineElementConfig(n,a=0){var c;const i=this.getHoverEffectsConfig(),t=this.getAnimationConfig(),r=this.getColorScheme(),g=((c=this.charts.dataDisplay)==null?void 0:c.showDataPoints)!==!1;return{stroke:n||r[a%r.length],strokeWidth:2,dot:g?{fill:n||r[a%r.length],strokeWidth:2,r:4}:!1,activeDot:i.cursor==="pointer"?{r:6,stroke:n||r[a%r.length],strokeWidth:2,fill:"#fff"}:!1,...t}}getLineChartConfig(){var t;const n=this.getColorScheme(),a=((t=this.charts.dataDisplay)==null?void 0:t.showDataPoints)!==!1,i=n&&n.length>0?n[0]:h.PRIMARY_BLUE;return{...this.getChartProps("line"),lineProps:{stroke:i,strokeWidth:2,dot:a?{fill:i,r:4}:!1,activeDot:{r:6,fill:i}}}}getPieChartConfig(){const n=this.getColorScheme();return{...this.getChartProps("pie"),pieProps:{cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",colors:n}}}applySettingsToData(n,a="bar"){if(!n)return n;const i=this.getColorScheme();return a==="pie"?n.map((t,r)=>({...t,fill:i[r%i.length]})):n}getPerformanceModeConfig(){return this.charts.performanceMode||!1?{optimizeForSpeed:!0,maxDataPoints:200,enableSampling:!0,animationDuration:0}:{optimizeForSpeed:!1,maxDataPoints:1/0,enableSampling:!1}}getResponsiveContainerProps(){var t;const n=((t=this.charts.layout)==null?void 0:t.aspectRatio)||"auto",a=this.getPerformanceModeConfig();let i;switch(n){case"16:9":i=16/9;break;case"4:3":i=4/3;break;case"1:1":i=1;break;default:i=void 0}return{width:"100%",height:this.getChartHeight(),aspect:i,debounceMs:a.optimizeForSpeed?100:0}}}const He=s=>(console.warn("useEnhancedRechartsConfig is deprecated. Please use useUnifiedChartConfig hook instead."),new Te(s)),{Title:Be,Text:ce,Paragraph:ze}=ue,Ie=[{name:"Machine A",value:120,color:"#1E3A8A"},{name:"Machine B",value:98,color:"#3B82F6"},{name:"Machine C",value:86,color:"#93C5FD"},{name:"Machine D",value:75,color:"#DBEAFE"}],_e=[{name:"Jan",value:65},{name:"Feb",value:78},{name:"Mar",value:90},{name:"Apr",value:81},{name:"May",value:95}],ge=[{name:"Production",value:400},{name:"Maintenance",value:300},{name:"Arrêts",value:200}];function Ue(){var p;const{charts:s,theme:n}=pe(),[a,i]=ne.useState([]),t=He({charts:s,theme:n});ne.useEffect(()=>{var U,Z,N,O,X,j,J,q,Q,ee,te;const o=[],l=t.getChartHeight(),m=((U=s.layout)==null?void 0:U.defaultHeight)||300;o.push({setting:"Default Chart Height",expected:`${m}px`,actual:`${l}px`,working:l===m,description:"Default chart height should match settings"});const u=t.getLegendConfig(),k=s.showLegend!==!1,A=u.content&&typeof u.content=="function";o.push({setting:"Show Legend",expected:k?"Visible":"Hidden",actual:A?"Hidden":"Visible",working:k===!A,description:"Chart legends should show/hide based on settings"});const y=t.getColorScheme(),f=s.colorScheme||"somipem",M=f==="somipem"?y[0]==="#1E3A8A":f==="blue"?y[0]==="#1890ff":f==="green"?y[0]==="#52c41a":!1;o.push({setting:"Color Scheme",expected:f,actual:M?f:"Unknown",working:M,description:"Chart colors should match selected color scheme"});const S=((Z=s.dataDisplay)==null?void 0:Z.showDataLabels)||!1;o.push({setting:"Show Data Labels",expected:S?"Enabled":"Disabled",actual:S?"Enabled":"Disabled",working:!0,description:"Data labels should show/hide based on settings"});const R=t.getGridConfig(),P=((N=s.dataDisplay)==null?void 0:N.showGridLines)!==!1;o.push({setting:"Show Grid Lines",expected:P?"Visible":"Hidden",actual:R.strokeDasharray?"Visible":"Hidden",working:P===!!R.strokeDasharray,description:"Grid lines should show/hide based on settings"});const L=((O=s.dataDisplay)==null?void 0:O.showDataPoints)!==!1;o.push({setting:"Show Data Points",expected:L?"Visible":"Hidden",actual:L?"Visible":"Hidden",working:!0,description:"Line chart data points should show/hide based on settings"});const C=t.getAxisConfig(),T=((X=s.dataDisplay)==null?void 0:X.zeroBasedAxis)===!0;o.push({setting:"Zero-Based Y-Axis",expected:T?"Enabled":"Disabled",actual:C.domain&&C.domain[0]===0?"Enabled":"Disabled",working:T===(C.domain&&C.domain[0]===0),description:"Y-axis should start from zero only when explicitly enabled (improves chart readability)"});const H=t.getAnimationConfig(),B=n.chartAnimations!==!1;o.push({setting:"Chart Animations",expected:B?"Enabled":"Disabled",actual:H.isAnimationActive?"Enabled":"Disabled",working:B===H.isAnimationActive,description:"Chart animations should enable/disable based on settings"});const z=t.getHoverEffectsConfig(),I=((j=s.interactions)==null?void 0:j.hoverEffects)!==!1;o.push({setting:"Hover Effects",expected:I?"Enabled":"Disabled",actual:z.cursor==="pointer"?"Enabled":"Disabled",working:I===(z.cursor==="pointer"),description:"Hover effects should enable/disable based on settings"});const _=t.getClickToExpandConfig(),G=((J=s.interactions)==null?void 0:J.clickToExpand)!==!1;o.push({setting:"Click to Expand",expected:G?"Enabled":"Disabled",actual:_.enabled?"Enabled":"Disabled",working:G===_.enabled,description:"Click to expand should enable/disable based on settings"});const he=t.getTooltipConfig(),de=((q=s.interactions)==null?void 0:q.tooltipStyle)||"standard";o.push({setting:"Tooltip Style",expected:de,actual:he.formatter?"detailed":"standard",working:!0,description:"Tooltip appearance should match selected style"});const F=t.getPerformanceModeConfig(),W=s.performanceMode||!1;o.push({setting:"Performance Mode",expected:W?"Enabled":"Disabled",actual:F.optimizeForSpeed?"Enabled":"Disabled",working:W===F.optimizeForSpeed,description:"Performance optimizations should enable/disable based on settings"});const x=((Q=s.layout)==null?void 0:Q.compactMode)||!1,$=x?Math.max(200,l*.8):l;o.push({setting:"Compact Chart Mode",expected:x?"Enabled":"Disabled",actual:$<l?"Enabled":"Disabled",working:x===$<l,description:"Charts should be smaller in compact mode"});const K=t.getResponsiveContainerProps(),me=((ee=s.layout)==null?void 0:ee.aspectRatio)||"auto";o.push({setting:"Chart Aspect Ratio",expected:me,actual:K.aspect?`${K.aspect}:1`:"auto",working:!0,description:"Chart proportions should match selected aspect ratio"});const V=t.getChartMargins(),E=((te=s.layout)==null?void 0:te.marginSize)||"standard",Y=E==="compact"?10:E==="spacious"?30:20;o.push({setting:"Chart Margins",expected:E,actual:V.top===Y?E:"unknown",working:V.top===Y,description:"Chart spacing should match selected margin size"}),i(o)},[s,n,t]);const r=a.filter(o=>o.working).length,g=a.length,c=r===g;return e.createElement("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"}},e.createElement(Be,{level:2},e.createElement(Ce,null)," Chart Settings Integration Verification"),e.createElement(ze,null,"This tool verifies that all chart settings from the Settings page are properly applied to dashboard charts."),e.createElement(ae,{type:c?"success":"warning",message:`Chart Settings Integration: ${r}/${g} Working`,description:c?"All chart settings are properly integrated and working!":"Some chart settings may not be fully integrated. Check the details below.",style:{marginBottom:"24px"},showIcon:!0}),e.createElement(d,{title:"Settings Verification Results",style:{marginBottom:"24px"}},e.createElement(ie,{gutter:[16,16]},a.map((o,l)=>e.createElement(b,{xs:24,md:12,lg:8,key:l},e.createElement(d,{size:"small",style:{height:"100%"}},e.createElement(Ee,{direction:"vertical",style:{width:"100%"}},e.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"}},e.createElement(ce,{strong:!0},o.setting),o.working?e.createElement(ye,{style:{color:"#52c41a",fontSize:"16px"}}):e.createElement(xe,{style:{color:"#f5222d",fontSize:"16px"}})),e.createElement("div",null,e.createElement(ce,{type:"secondary",style:{fontSize:"12px"}},o.description)),e.createElement("div",null,e.createElement(oe,{color:o.working?"success":"error"},"Expected: ",o.expected),e.createElement(oe,{color:"processing"},"Actual: ",o.actual)))))))),e.createElement(d,{title:"Live Chart Examples with Current Settings",style:{marginBottom:"24px"}},e.createElement(ie,{gutter:[24,24]},e.createElement(b,{xs:24,md:8},e.createElement(d,{size:"small",title:e.createElement(e.Fragment,null,e.createElement(ve,null)," Bar Chart")},e.createElement("div",{style:{height:t.getChartHeight()}},e.createElement(v,{...t.getResponsiveContainerProps()},e.createElement(we,{data:Ie,margin:t.getChartMargins()},e.createElement(se,{...t.getGridConfig()}),e.createElement(re,{...t.getAxisConfig(),dataKey:"name"}),e.createElement(le,{...t.getAxisConfig()}),e.createElement(w,{...t.getTooltipConfig()}),s.showLegend&&e.createElement(D,{...t.getLegendConfig()}),e.createElement(De,{dataKey:"value",...t.getBarElementConfig()})))))),e.createElement(b,{xs:24,md:8},e.createElement(d,{size:"small",title:e.createElement(e.Fragment,null,e.createElement(Pe,null)," Line Chart")},e.createElement("div",{style:{height:t.getChartHeight()}},e.createElement(v,{...t.getResponsiveContainerProps()},e.createElement(ke,{data:_e,margin:t.getChartMargins()},e.createElement(se,{...t.getGridConfig()}),e.createElement(re,{...t.getAxisConfig(),dataKey:"name"}),e.createElement(le,{...t.getAxisConfig()}),e.createElement(w,{...t.getTooltipConfig()}),s.showLegend&&e.createElement(D,{...t.getLegendConfig()}),e.createElement(Ae,{dataKey:"value",...t.getLineElementConfig()})))))),e.createElement(b,{xs:24,md:8},e.createElement(d,{size:"small",title:e.createElement(e.Fragment,null,e.createElement(Le,null)," Pie Chart")},e.createElement("div",{style:{height:t.getChartHeight()}},e.createElement(v,{...t.getResponsiveContainerProps()},e.createElement(Me,null,e.createElement(Se,{data:ge,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:80,label:(p=s.dataDisplay)==null?void 0:p.showDataLabels,...t.getAnimationConfig()},ge.map((o,l)=>{const m=t.getColorScheme(),u=t.getHoverEffectsConfig();return e.createElement(Re,{key:`cell-${l}`,fill:m[l%m.length],style:{cursor:u.cursor}})})),e.createElement(w,{...t.getTooltipConfig()}),s.showLegend&&e.createElement(D,{...t.getLegendConfig()})))))))),e.createElement(be,null),e.createElement(d,{title:"Testing Instructions"},e.createElement("ol",null,e.createElement("li",null,"Go to Settings page → Charts tab"),e.createElement("li",null,"Change any chart setting (height, animations, hover effects, etc.)"),e.createElement("li",null,"Return to this page and verify the changes are reflected"),e.createElement("li",null,'Check that the verification results show "Working" status'),e.createElement("li",null,"Test the live chart examples to see settings in action")),e.createElement(ae,{type:"info",message:"Settings should apply immediately without page refresh",style:{marginTop:"16px"}})))}export{Ue as default};
