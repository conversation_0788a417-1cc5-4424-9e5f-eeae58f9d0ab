import{r as c,Q as M,U as F,ap as R,J as H,q as I,t as _,O as L,bp as U,aN as b,b5 as B}from"./index-O2xm1U_Z.js";const V=t=>{const{value:n,formatter:o,precision:e,decimalSeparator:a,groupSeparator:u="",prefixCls:i}=t;let s;if(typeof o=="function")s=o(n);else{const r=String(n),f=r.match(/^(-?)(\d*)(\.(\d+))?$/);if(!f||r==="-")s=r;else{const d=f[1];let p=f[2]||"0",m=f[4]||"";p=p.replace(/\B(?=(\d{3})+(?!\d))/g,u),typeof e=="number"&&(m=m.padEnd(e,"0").slice(0,e>0?e:0)),m&&(m=`${a}${m}`),s=[c.createElement("span",{key:"int",className:`${i}-content-value-int`},d,p),m&&c.createElement("span",{key:"decimal",className:`${i}-content-value-decimal`},m)]}}return c.createElement("span",{className:`${i}-content-value`},s)},X=t=>{const{componentCls:n,marginXXS:o,padding:e,colorTextDescription:a,titleFontSize:u,colorTextHeading:i,contentFontSize:s,fontFamily:r}=t;return{[n]:Object.assign(Object.assign({},R(t)),{[`${n}-title`]:{marginBottom:o,color:a,fontSize:u},[`${n}-skeleton`]:{paddingTop:e},[`${n}-content`]:{color:i,fontSize:s,fontFamily:r,[`${n}-content-value`]:{display:"inline-block",direction:"ltr"},[`${n}-content-prefix, ${n}-content-suffix`]:{display:"inline-block"},[`${n}-content-prefix`]:{marginInlineEnd:o},[`${n}-content-suffix`]:{marginInlineStart:o}}})}},q=t=>{const{fontSizeHeading3:n,fontSize:o}=t;return{titleFontSize:o,contentFontSize:n}},A=M("Statistic",t=>{const n=F(t,{});return[X(n)]},q);var J=function(t,n){var o={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(o[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)n.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(o[e[a]]=t[e[a]]);return o};const y=t=>{const{prefixCls:n,className:o,rootClassName:e,style:a,valueStyle:u,value:i=0,title:s,valueRender:r,prefix:f,suffix:d,loading:p=!1,formatter:m,precision:l,decimalSeparator:g=".",groupSeparator:v=",",onMouseEnter:E,onMouseLeave:$}=t,w=J(t,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:C,direction:h,className:N,style:j}=H("statistic"),S=C("statistic",n),[T,k,z]=A(S),x=c.createElement(V,{decimalSeparator:g,groupSeparator:v,prefixCls:S,formatter:m,precision:l,value:i}),P=I(S,{[`${S}-rtl`]:h==="rtl"},N,o,e,k,z),D=_(w,{aria:!0,data:!0});return T(c.createElement("div",Object.assign({},D,{className:P,style:Object.assign(Object.assign({},j),a),onMouseEnter:E,onMouseLeave:$}),s&&c.createElement("div",{className:`${S}-title`},s),c.createElement(L,{paragraph:!1,loading:p,className:`${S}-skeleton`},c.createElement("div",{style:u,className:`${S}-content`},f&&c.createElement("span",{className:`${S}-content-prefix`},f),r?r(x):x,d&&c.createElement("span",{className:`${S}-content-suffix`},d)))))},Q=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function Y(t,n){let o=t;const e=/\[[^\]]*]/g,a=(n.match(e)||[]).map(r=>r.slice(1,-1)),u=n.replace(e,"[]"),i=Q.reduce((r,[f,d])=>{if(r.includes(f)){const p=Math.floor(o/d);return o-=p*d,r.replace(new RegExp(`${f}+`,"g"),m=>{const l=m.length;return p.toString().padStart(l,"0")})}return r},u);let s=0;return i.replace(e,()=>{const r=a[s];return s+=1,r})}function G(t,n,o){const{format:e=""}=n,a=new Date(t).getTime(),u=Date.now(),i=Math.max(o?a-u:u-a,0);return Y(i,e)}var K=function(t,n){var o={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(o[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)n.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(o[e[a]]=t[e[a]]);return o};function W(t){return new Date(t).getTime()}const O=t=>{const{value:n,format:o="HH:mm:ss",onChange:e,onFinish:a,type:u}=t,i=K(t,["value","format","onChange","onFinish","type"]),s=u==="countdown",[r,f]=c.useState(null),d=U(()=>{const l=Date.now(),g=W(n);f({});const v=s?g-l:l-g;return e==null||e(v),s&&g<l?(a==null||a(),!1):!0});c.useEffect(()=>{let l;const g=()=>b.cancel(l),v=()=>{l=b(()=>{d()&&v()})};return v(),g},[n,s]),c.useEffect(()=>{f({})},[]);const p=(l,g)=>r?G(l,Object.assign(Object.assign({},g),{format:o}),s):"-",m=l=>B(l,{title:void 0});return c.createElement(y,Object.assign({},i,{value:n,valueRender:m,formatter:p}))},Z=t=>c.createElement(O,Object.assign({},t,{type:"countdown"})),ee=c.memo(Z);y.Timer=O;y.Countdown=ee;export{y as S};
