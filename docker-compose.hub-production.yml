# Docker Compose for LOCQL Unified Container - Production Deployment
# Pulls image from Docker Hub for production deployment

services:
  locql:
    image: mayahinasr/locql-unified:1.0.0
    container_name: locql-production
    ports:
      - "5000:5000"
    environment:
      # Application Environment
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=5000

      # Database Configuration (External Production Database)
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_NAME:-locql_db}

      # Authentication
      - JWT_SECRET=${JWT_SECRET:-CHANGE_THIS_IN_PRODUCTION}
      
      # CORS Configuration
      - CORS_ORIGINS=${CORS_ORIGINS}
      
      # API Configuration
      - VITE_API_URL=${VITE_API_URL}
      
      # WebSocket Configuration
      - WS_EXTERNAL_URL=${WS_EXTERNAL_URL}
      
      
      # Production Settings
      - DEBUG=false
      - LOG_LEVEL=info
      
      # Security Settings
      - SECURE_COOKIES=true
      - TRUST_PROXY=true
      
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # Volume mounts for production logs and uploads
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/backend/uploads
      - ./reports:/app/backend/reports
    
    # Network configuration
    networks:
      - locql-production-network
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # User configuration (non-root)
    user: "1001:1001"

networks:
  locql-production-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Optional: Add reverse proxy (nginx) for production
# Uncomment if you want to add SSL termination and reverse proxy
#
# services:
#   nginx:
#     image: nginx:alpine
#     container_name: locql-nginx
#     ports:
#       - "80:80"
#       - "443:443"
#     volumes:
#       - ./nginx.conf:/etc/nginx/nginx.conf
#       - ./ssl:/etc/nginx/ssl
#     depends_on:
#       - locql
#     networks:
#       - locql-production-network
#     restart: unless-stopped
