import NotificationService from "../utils/notificationService.js"

/**
 * Create a notification for machine alerts
 * @param {string} machineName - Name of the machine
 * @param {string} message - Alert message
 * @param {number} machineId - Machine ID (optional)
 */
const createMachineAlert = async (machineName, message, machineId = null) => {
  try {
    await NotificationService.createNotification({
      title: `Alerte: ${machineName}`,
      message: message,
      category: 'machine_alert',
      priority: 'high',
      severity: 'warning',
      source: 'machine_monitoring',
      machine_id: machineId,
      userId: null // Broadcast to all users
    })
  } catch (error) {
    console.error("Error creating machine alert notification:", error)
  }
}

/**
 * Create a notification for maintenance events
 * @param {string} title - Maintenance title
 * @param {string} message - Maintenance details
 * @param {number} machineId - Machine ID (optional)
 */
const createMaintenanceNotification = async (title, message, machineId = null) => {
  try {
    await NotificationService.createNotification({
      title: title,
      message: message,
      category: 'maintenance',
      priority: 'medium',
      severity: 'info',
      source: 'maintenance_system',
      machine_id: machineId,
      userId: null // Broadcast to all users
    })
  } catch (error) {
    console.error("Error creating maintenance notification:", error)
  }
}

/**
 * Create a notification for system updates
 * @param {string} title - Update title
 * @param {string} message - Update details
 */
const createUpdateNotification = async (title, message) => {
  try {
    await NotificationService.createNotification({
      title: title,
      message: message,
      category: 'update',
      priority: 'low',
      severity: 'info',
      source: 'system',
      machine_id: null,
      userId: null // Broadcast to all users
    })
  } catch (error) {
    console.error("Error creating update notification:", error)
  }
}

export {
  createMachineAlert,
  createMaintenanceNotification,
  createUpdateNotification,
}

