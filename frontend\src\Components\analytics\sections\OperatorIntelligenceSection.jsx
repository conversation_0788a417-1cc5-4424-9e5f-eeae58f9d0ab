import React from 'react';
import { Card, Row, Col, Space, Badge } from 'antd';
import { UserOutlined, TrophyOutlined, TeamOutlined, BarChartOutlined } from '@ant-design/icons';

const OperatorIntelligenceSection = ({ loading, filters }) => {
  const operatorFeatures = [
    {
      title: "Operator Performance 360°",
      description: "Comprehensive operator performance analytics",
      icon: <TrophyOutlined />,
      color: "#1890ff"
    },
    {
      title: "Skills Assessment AI",
      description: "AI-powered skills gap analysis",
      icon: <UserOutlined />,
      color: "#52c41a"
    },
    {
      title: "Training Optimization",
      description: "Personalized training recommendations",
      icon: <TeamOutlined />,
      color: "#722ed1"
    },
    {
      title: "Productivity Insights",
      description: "Real-time productivity monitoring",
      icon: <BarChartOutlined />,
      color: "#fa8c16"
    }
  ];

  return (
    <div style={{
      background: 'linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)',
      borderRadius: '16px',
      padding: '24px',
      minHeight: '600px'
    }}>
      {/* Section Header */}
      <div style={{
        background: 'linear-gradient(135deg, #722ed1 0%, #9254de 100%)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '24px',
        color: 'white'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space align="center" size="large">
              <div style={{
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '10px',
                padding: '10px'
              }}>
                <UserOutlined style={{ fontSize: '24px' }} />
              </div>
              <div>
                <h2 style={{ color: 'white', margin: 0, fontSize: '24px' }}>
                  Operator Intelligence 360°
                </h2>
                <p style={{ color: 'rgba(255,255,255,0.9)', margin: 0, fontSize: '14px' }}>
                  AI-driven operator performance and optimization analytics
                </p>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Badge count="12 AI Models" style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
              <Badge count="360° View" style={{ backgroundColor: '#eb2f96' }} />
            </Space>
          </Col>
        </Row>
      </div>

      {/* Feature Cards */}
      <Row gutter={[24, 24]}>
        {operatorFeatures.map((feature, index) => (
          <Col xs={24} md={12} key={index}>
            <Card
              style={{
                height: '200px',
                borderRadius: '16px',
                border: 'none',
                background: `linear-gradient(135deg, ${feature.color}10 0%, ${feature.color}05 100%)`,
                boxShadow: '0 8px 24px rgba(0,0,0,0.06)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              bodyStyle={{ 
                height: '100%', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                textAlign: 'center'
              }}
            >
              <Space direction="vertical" align="center" size="large">
                <div style={{
                  background: feature.color,
                  borderRadius: '50%',
                  width: '60px',
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '24px'
                }}>
                  {feature.icon}
                </div>
                <div>
                  <h3 style={{ margin: 0, color: feature.color }}>
                    {feature.title}
                  </h3>
                  <p style={{ margin: '8px 0 0 0', color: '#8c8c8c' }}>
                    {feature.description}
                  </p>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default OperatorIntelligenceSection;
