import{R as t,r as s,av as de,aG as te,q as w,Q as ue,U as pe,ap as be,V as $,aD as fe,J as ge,aH as Ee,aF as ve,u as ye,aI as y,b as _,c as z,C as J,aa as T,T as he,D as K,d as xe,e as Z,g as M,aJ as Q,i as ae,ay as A,aK as Oe,f as $e,B,ab as L,a9 as je,n as Y,al as Ce}from"./index-N0wOiMt6.js";import{R as we,a as Se,U as Ie}from"./user-management-CfIaNDZW.js";import{A as Ne}from"./index-DIJFODWS.js";import{R as Pe}from"./SaveOutlined-BzKiXS91.js";import{S as k}from"./index-Dea_-S4D.js";import"./SearchOutlined-xbMlVLbw.js";import"./ReloadOutlined-DZn6IdM2.js";import"./CloseCircleOutlined-kKLfKks7.js";import"./CheckCircleOutlined-DcDkn_d7.js";import"./EyeOutlined-BNZGoZWA.js";const Me={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},q=t.createContext({});var Ae=function(e,a){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&a.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)a.indexOf(l[n])<0&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(r[l[n]]=e[l[n]]);return r};const ze=e=>de(e).map(a=>Object.assign(Object.assign({},a==null?void 0:a.props),{key:a.key}));function Re(e,a,r){const l=s.useMemo(()=>a||ze(r),[a,r]);return s.useMemo(()=>l.map(i=>{var{span:o}=i,E=Ae(i,["span"]);return o==="filled"?Object.assign(Object.assign({},E),{filled:!0}):Object.assign(Object.assign({},E),{span:typeof o=="number"?o:te(e,o)})}),[l,e])}var Te=function(e,a){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&a.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)a.indexOf(l[n])<0&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(r[l[n]]=e[l[n]]);return r};function Be(e,a){let r=[],l=[],n=!1,i=0;return e.filter(o=>o).forEach(o=>{const{filled:E}=o,p=Te(o,["filled"]);if(E){l.push(p),r.push(l),l=[],i=0;return}const d=a-i;i+=o.span||1,i>=a?(i>a?(n=!0,l.push(Object.assign(Object.assign({},p),{span:d}))):l.push(p),r.push(l),l=[],i=0):l.push(p)}),l.length>0&&r.push(l),r=r.map(o=>{const E=o.reduce((p,d)=>p+(d.span||1),0);if(E<a){const p=o[o.length-1];return p.span=a-(E-(p.span||1)),o}return o}),[r,n]}const Le=(e,a)=>{const[r,l]=s.useMemo(()=>Be(a,e),[a,e]);return r},De=({children:e})=>e;function ee(e){return e!=null}const W=e=>{const{itemPrefixCls:a,component:r,span:l,className:n,style:i,labelStyle:o,contentStyle:E,bordered:p,label:d,content:u,colon:S,type:h,styles:b}=e,x=r,m=s.useContext(q),{classNames:g}=m;return p?s.createElement(x,{className:w({[`${a}-item-label`]:h==="label",[`${a}-item-content`]:h==="content",[`${g==null?void 0:g.label}`]:h==="label",[`${g==null?void 0:g.content}`]:h==="content"},n),style:i,colSpan:l},ee(d)&&s.createElement("span",{style:Object.assign(Object.assign({},o),b==null?void 0:b.label)},d),ee(u)&&s.createElement("span",{style:Object.assign(Object.assign({},o),b==null?void 0:b.content)},u)):s.createElement(x,{className:w(`${a}-item`,n),style:i,colSpan:l},s.createElement("div",{className:`${a}-item-container`},(d||d===0)&&s.createElement("span",{className:w(`${a}-item-label`,g==null?void 0:g.label,{[`${a}-item-no-colon`]:!S}),style:Object.assign(Object.assign({},o),b==null?void 0:b.label)},d),(u||u===0)&&s.createElement("span",{className:w(`${a}-item-content`,g==null?void 0:g.content),style:Object.assign(Object.assign({},E),b==null?void 0:b.content)},u)))};function G(e,{colon:a,prefixCls:r,bordered:l},{component:n,type:i,showLabel:o,showContent:E,labelStyle:p,contentStyle:d,styles:u}){return e.map(({label:S,children:h,prefixCls:b=r,className:x,style:m,labelStyle:g,contentStyle:c,span:O=1,key:j,styles:v},I)=>typeof n=="string"?s.createElement(W,{key:`${i}-${j||I}`,className:x,style:m,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},p),u==null?void 0:u.label),g),v==null?void 0:v.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},d),u==null?void 0:u.content),c),v==null?void 0:v.content)},span:O,colon:a,component:n,itemPrefixCls:b,bordered:l,label:o?S:null,content:E?h:null,type:i}):[s.createElement(W,{key:`label-${j||I}`,className:x,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},p),u==null?void 0:u.label),m),g),v==null?void 0:v.label),span:1,colon:a,component:n[0],itemPrefixCls:b,bordered:l,label:S,type:"label"}),s.createElement(W,{key:`content-${j||I}`,className:x,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},d),u==null?void 0:u.content),m),c),v==null?void 0:v.content),span:O*2-1,component:n[1],itemPrefixCls:b,bordered:l,content:h,type:"content"})])}const Fe=e=>{const a=s.useContext(q),{prefixCls:r,vertical:l,row:n,index:i,bordered:o}=e;return l?s.createElement(s.Fragment,null,s.createElement("tr",{key:`label-${i}`,className:`${r}-row`},G(n,e,Object.assign({component:"th",type:"label",showLabel:!0},a))),s.createElement("tr",{key:`content-${i}`,className:`${r}-row`},G(n,e,Object.assign({component:"td",type:"content",showContent:!0},a)))):s.createElement("tr",{key:i,className:`${r}-row`},G(n,e,Object.assign({component:o?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},a)))},Ve=e=>{const{componentCls:a,labelBg:r}=e;return{[`&${a}-bordered`]:{[`> ${a}-view`]:{border:`${$(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${a}-row`]:{borderBottom:`${$(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${a}-item-label, > ${a}-item-content`]:{padding:`${$(e.padding)} ${$(e.paddingLG)}`,borderInlineEnd:`${$(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${a}-item-label`]:{color:e.colorTextSecondary,backgroundColor:r,"&::after":{display:"none"}}}},[`&${a}-middle`]:{[`${a}-row`]:{[`> ${a}-item-label, > ${a}-item-content`]:{padding:`${$(e.paddingSM)} ${$(e.paddingLG)}`}}},[`&${a}-small`]:{[`${a}-row`]:{[`> ${a}-item-label, > ${a}-item-content`]:{padding:`${$(e.paddingXS)} ${$(e.padding)}`}}}}}},Ue=e=>{const{componentCls:a,extraColor:r,itemPaddingBottom:l,itemPaddingEnd:n,colonMarginRight:i,colonMarginLeft:o,titleMarginBottom:E}=e;return{[a]:Object.assign(Object.assign(Object.assign({},be(e)),Ve(e)),{"&-rtl":{direction:"rtl"},[`${a}-header`]:{display:"flex",alignItems:"center",marginBottom:E},[`${a}-title`]:Object.assign(Object.assign({},fe),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${a}-extra`]:{marginInlineStart:"auto",color:r,fontSize:e.fontSize},[`${a}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${a}-row`]:{"> th, > td":{paddingBottom:l,paddingInlineEnd:n},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${a}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${$(o)} ${$(i)}`},[`&${a}-item-no-colon::after`]:{content:'""'}},[`${a}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${a}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${a}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${a}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${a}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${a}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${a}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},_e=e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}),We=ue("Descriptions",e=>{const a=pe(e,{});return Ue(a)},_e);var Ge=function(e,a){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&a.indexOf(l)<0&&(r[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,l=Object.getOwnPropertySymbols(e);n<l.length;n++)a.indexOf(l[n])<0&&Object.prototype.propertyIsEnumerable.call(e,l[n])&&(r[l[n]]=e[l[n]]);return r};const f=e=>{const{prefixCls:a,title:r,extra:l,column:n,colon:i=!0,bordered:o,layout:E,children:p,className:d,rootClassName:u,style:S,size:h,labelStyle:b,contentStyle:x,styles:m,items:g,classNames:c}=e,O=Ge(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:j,direction:v,className:I,style:le,classNames:N,styles:P}=ge("descriptions"),C=j("descriptions",a),V=Ee(),ne=s.useMemo(()=>{var R;return typeof n=="number"?n:(R=te(V,Object.assign(Object.assign({},Me),n)))!==null&&R!==void 0?R:3},[V,n]),re=Re(V,g,p),U=ve(h),se=Le(ne,re),[oe,ie,ce]=We(C),me=s.useMemo(()=>({labelStyle:b,contentStyle:x,styles:{content:Object.assign(Object.assign({},P.content),m==null?void 0:m.content),label:Object.assign(Object.assign({},P.label),m==null?void 0:m.label)},classNames:{label:w(N.label,c==null?void 0:c.label),content:w(N.content,c==null?void 0:c.content)}}),[b,x,m,c,N,P]);return oe(s.createElement(q.Provider,{value:me},s.createElement("div",Object.assign({className:w(C,I,N.root,c==null?void 0:c.root,{[`${C}-${U}`]:U&&U!=="default",[`${C}-bordered`]:!!o,[`${C}-rtl`]:v==="rtl"},d,u,ie,ce),style:Object.assign(Object.assign(Object.assign(Object.assign({},le),P.root),m==null?void 0:m.root),S)},O),(r||l)&&s.createElement("div",{className:w(`${C}-header`,N.header,c==null?void 0:c.header),style:Object.assign(Object.assign({},P.header),m==null?void 0:m.header)},r&&s.createElement("div",{className:w(`${C}-title`,N.title,c==null?void 0:c.title),style:Object.assign(Object.assign({},P.title),m==null?void 0:m.title)},r),l&&s.createElement("div",{className:w(`${C}-extra`,N.extra,c==null?void 0:c.extra),style:Object.assign(Object.assign({},P.extra),m==null?void 0:m.extra)},l)),s.createElement("div",{className:`${C}-view`},s.createElement("table",null,s.createElement("tbody",null,se.map((R,X)=>s.createElement(Fe,{key:X,index:X,colon:i,prefixCls:C,vertical:E==="vertical",bordered:o,row:R}))))))))};f.Item=De;const{Title:D,Text:F,Paragraph:He}=he,{TabPane:H}=ae,{Option:at}=Ce,lt=()=>{const{user:e,updateProfile:a,changePassword:r}=ye(),[l]=y.useForm(),[n]=y.useForm(),[i,o]=s.useState(!1),[E,p]=s.useState(!1),[d,u]=s.useState(!1);s.useEffect(()=>{const c=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark")||localStorage.getItem("theme")==="dark";u(c);const O=new MutationObserver(j=>{j.forEach(v=>{if(v.attributeName==="class"){const I=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark");u(I)}})});return O.observe(document.documentElement,{attributes:!0}),O.observe(document.body,{attributes:!0}),()=>O.disconnect()},[]);const S={backgroundColor:d?"#1f1f1f":"#ffffff",boxShadow:d?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.05)",borderRadius:"12px",border:"none",transition:"all 0.3s ease"},h={backgroundColor:"#1890ff",boxShadow:d?"0 4px 8px rgba(24, 144, 255, 0.5)":"0 4px 8px rgba(24, 144, 255, 0.2)",padding:"4px",border:"4px solid",borderColor:d?"#141414":"#ffffff",transition:"all 0.3s ease"},b=async c=>{p(!0);try{(await a(c)).success&&(o(!1),Y.success("Profil mis à jour avec succès!"))}finally{p(!1)}},x=async c=>{p(!0);try{(await r(c)).success&&(n.resetFields(),Y.success("Mot de passe changé avec succès!"))}finally{p(!1)}},m=()=>{i||l.setFieldsValue({username:e==null?void 0:e.username,email:e==null?void 0:e.email,fullName:(e==null?void 0:e.fullName)||"",phone:(e==null?void 0:e.phone)||""}),o(!i)},g=()=>e?e.role==="admin"?t.createElement(B,{status:"success",text:t.createElement(F,{strong:!0,style:{color:"#52c41a"}},"Administrateur")}):e.active?t.createElement(B,{status:"processing",text:t.createElement(F,null,"Utilisateur actif")}):t.createElement(B,{status:"default",text:t.createElement(F,{type:"secondary"},"Utilisateur inactif")}):null;return t.createElement("div",{style:{padding:24}},t.createElement(_,{gutter:[24,24]},t.createElement(z,{xs:24,md:8},t.createElement(J,{bordered:!1,style:S,className:"profile-card"},t.createElement("div",{style:{textAlign:"center",marginBottom:24}},t.createElement(Ne,{size:120,icon:t.createElement(T,null),style:h,className:"profile-avatar"}),t.createElement(D,{level:3,style:{marginTop:16,marginBottom:4}},(e==null?void 0:e.fullName)||(e==null?void 0:e.username)),t.createElement("div",{style:{marginBottom:8}},g()),t.createElement(He,{type:"secondary",style:{fontSize:"14px"}},"Membre depuis ",e!=null&&e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A")),t.createElement(K,{style:{margin:"12px 0 24px"}}),t.createElement(f,{title:t.createElement(F,{strong:!0},"Informations"),column:1,bordered:!1,size:"small",labelStyle:{fontWeight:"500",color:d?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"},contentStyle:{color:d?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"}},t.createElement(f.Item,{label:"Nom d'utilisateur"},e==null?void 0:e.username),t.createElement(f.Item,{label:"Email"},e==null?void 0:e.email),t.createElement(f.Item,{label:"Téléphone"},(e==null?void 0:e.phone)||"Non renseigné"),t.createElement(f.Item,{label:"Rôle"},(e==null?void 0:e.role)==="admin"?"Administrateur":"Utilisateur"),t.createElement(f.Item,{label:"Statut"},e!=null&&e.active?"Actif":"Inactif")),t.createElement("div",{style:{marginTop:24,textAlign:"center"}},t.createElement(xe,null,t.createElement(Z,{title:"Modifier le profil"},t.createElement(M,{type:"primary",icon:t.createElement(Q,null),onClick:m,shape:"round"},"Modifier")),t.createElement(Z,{title:"Changer le mot de passe"},t.createElement(M,{icon:t.createElement(we,null),onClick:()=>document.getElementById("security-tab").click(),shape:"round"},"Mot de passe")))))),t.createElement(z,{xs:24,md:16},t.createElement(J,{bordered:!1,style:S,className:"profile-tabs-card"},t.createElement(ae,{defaultActiveKey:"profile"},t.createElement(H,{tab:t.createElement("span",null,t.createElement(T,null),"Profil"),key:"profile"},t.createElement("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16}},t.createElement(D,{level:4},"Informations du profil"),t.createElement(M,{type:i?"primary":"default",icon:i?t.createElement(Pe,null):t.createElement(Q,null),onClick:m},i?"Enregistrer":"Modifier")),i?t.createElement(y,{form:l,layout:"vertical",onFinish:b,initialValues:{username:e==null?void 0:e.username,email:e==null?void 0:e.email,fullName:(e==null?void 0:e.fullName)||"",phone:(e==null?void 0:e.phone)||""}},t.createElement(_,{gutter:16},t.createElement(z,{span:12},t.createElement(y.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer votre nom complet"}]},t.createElement(A,{prefix:t.createElement(T,null),placeholder:"Nom complet"}))),t.createElement(z,{span:12},t.createElement(y.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer votre nom d'utilisateur"}]},t.createElement(A,{prefix:t.createElement(T,null),placeholder:"Nom d'utilisateur"})))),t.createElement(_,{gutter:16},t.createElement(z,{span:12},t.createElement(y.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}]},t.createElement(A,{prefix:t.createElement(Oe,null),placeholder:"Email"}))),t.createElement(z,{span:12},t.createElement(y.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}]},t.createElement(A,{prefix:t.createElement(Se,null),placeholder:"Téléphone"})))),t.createElement(y.Item,null,t.createElement(M,{type:"primary",htmlType:"submit",loading:E},"Mettre à jour le profil"))):t.createElement(f,{bordered:!0,column:{xxl:2,xl:2,lg:2,md:1,sm:1,xs:1},labelStyle:{fontWeight:"500",color:d?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"}},t.createElement(f.Item,{label:"Nom complet"},(e==null?void 0:e.fullName)||"Non renseigné"),t.createElement(f.Item,{label:"Nom d'utilisateur"},e==null?void 0:e.username),t.createElement(f.Item,{label:"Email"},e==null?void 0:e.email),t.createElement(f.Item,{label:"Téléphone"},(e==null?void 0:e.phone)||"Non renseigné"),t.createElement(f.Item,{label:"Rôle",span:2},t.createElement($e,{color:(e==null?void 0:e.role)==="admin"?"green":"blue"},(e==null?void 0:e.role)==="admin"?"Administrateur":"Utilisateur")),t.createElement(f.Item,{label:"Statut",span:2},t.createElement(B,{status:e!=null&&e.active?"success":"default",text:e!=null&&e.active?"Actif":"Inactif"})),t.createElement(f.Item,{label:"Compte créé",span:2},e!=null&&e.createdAt?new Date(e.createdAt).toLocaleDateString():"N/A"),t.createElement(f.Item,{label:"Dernière connexion",span:2},e!=null&&e.lastLogin?new Date(e.lastLogin).toLocaleString():"N/A"))),t.createElement(H,{tab:t.createElement("span",{id:"security-tab"},t.createElement(L,null),"Sécurité"),key:"security"},t.createElement(D,{level:4},"Changer le mot de passe"),t.createElement(y,{form:n,layout:"vertical",onFinish:x},t.createElement(y.Item,{name:"currentPassword",label:"Mot de passe actuel",rules:[{required:!0,message:"Veuillez entrer votre mot de passe actuel"}]},t.createElement(A.Password,{prefix:t.createElement(L,null),placeholder:"Mot de passe actuel"})),t.createElement(y.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}]},t.createElement(A.Password,{prefix:t.createElement(L,null),placeholder:"Nouveau mot de passe",autoComplete:"new-password"})),t.createElement(y.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:c})=>({validator(O,j){return!j||c("newPassword")===j?Promise.resolve():Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))}})]},t.createElement(A.Password,{prefix:t.createElement(L,null),placeholder:"Confirmer le mot de passe",autoComplete:"new-password"})),t.createElement(y.Item,null,t.createElement(M,{type:"primary",htmlType:"submit",loading:E},"Changer le mot de passe"))),t.createElement(K,null),t.createElement(D,{level:4},"Paramètres de sécurité"),t.createElement(f,{bordered:!0,column:1},t.createElement(f.Item,{label:"Authentification à deux facteurs"},t.createElement(k,{checkedChildren:"Activée",unCheckedChildren:"Désactivée",defaultChecked:e==null?void 0:e.twoFactorEnabled,disabled:!0}),t.createElement(M,{type:"link",disabled:!0},"Configurer")),t.createElement(f.Item,{label:"Notifications de connexion"},t.createElement(k,{checkedChildren:"Activées",unCheckedChildren:"Désactivées",defaultChecked:e==null?void 0:e.loginNotifications,disabled:!0})),t.createElement(f.Item,{label:"Sessions actives"},t.createElement(M,{type:"link",disabled:!0},"Voir les sessions (1 active)")))),e&&(e==null?void 0:e.role)==="admin"&&t.createElement(H,{tab:t.createElement("span",null,t.createElement(je,null),"Gestion des utilisateurs"),key:"users"},t.createElement(Ie,{darkMode:d})))))))};export{lt as default};
