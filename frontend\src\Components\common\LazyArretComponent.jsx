/**
 * Enhanced Lazy Component Wrapper with Priority Loading
 * Integrates with useLazyLoading for optimal performance
 */

import React from 'react';
import { Skeleton, Spin } from 'antd';
import useLazyLoading from '../../hooks/useLazyLoading';

const LazyArretComponent = ({ 
  children, 
  priority = 1, 
  delay = 0, 
  loadingType = 'skeleton',
  height = 200,
  className = '',
  title = 'Loading...'
}) => {
  const { shouldRender, isVisible, elementRef } = useLazyLoading(priority, delay);

  if (!shouldRender) {
    return (
      <div 
        ref={elementRef} 
        className={`lazy-component-placeholder ${className}`}
        style={{ height, minHeight: height }}
      >
        {loadingType === 'skeleton' ? (
          <Skeleton active paragraph={{ rows: 4 }} />
        ) : (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%' 
          }}>
            <Spin size="large" tip={title} />
          </div>
        )}
      </div>
    );
  }

  if (!isVisible) {
    return (
      <div 
        ref={elementRef} 
        className={`lazy-component-loading ${className}`}
        style={{ height, minHeight: height }}
      >
        {loadingType === 'skeleton' ? (
          <Skeleton active paragraph={{ rows: 3 }} />
        ) : (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%' 
          }}>
            <Spin tip="Rendering..." />
          </div>
        )}
      </div>
    );
  }

  return (
    <div ref={elementRef} className={`lazy-component-rendered ${className}`}>
      {children}
    </div>
  );
};

export default LazyArretComponent;
