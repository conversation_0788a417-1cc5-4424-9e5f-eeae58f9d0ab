/**
 * Rate limiting middleware for API endpoints
 */
class RateLimiter {
  constructor() {
    this.requests = new Map(); // Store request counts per IP/user
    this.cleanupInterval = 60000; // Cleanup every minute
    this.startCleanup();
  }

  /**
   * Create rate limiting middleware
   * @param {Object} options - Rate limiting options
   * @param {number} options.windowMs - Time window in milliseconds
   * @param {number} options.max - Maximum number of requests per window
   * @param {string} options.message - Error message when limit exceeded
   * @param {Function} options.keyGenerator - Function to generate unique key for requests
   * @returns {Function} - Express middleware function
   */
  createLimiter(options = {}) {
    const {
      windowMs = 15 * 60 * 1000, // 15 minutes default
      max = 100, // 100 requests per window default
      message = 'Too many requests, please try again later.',
      keyGenerator = (req) => req.ip || 'unknown'
    } = options;

    return (req, res, next) => {
      const key = keyGenerator(req);
      const now = Date.now();
      const windowStart = now - windowMs;

      // Get or create request history for this key
      if (!this.requests.has(key)) {
        this.requests.set(key, []);
      }

      const requestHistory = this.requests.get(key);

      // Remove old requests outside the window
      const validRequests = requestHistory.filter(timestamp => timestamp > windowStart);
      this.requests.set(key, validRequests);

      // Check if limit exceeded
      if (validRequests.length >= max) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message,
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }

      // Add current request
      validRequests.push(now);

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': max,
        'X-RateLimit-Remaining': Math.max(0, max - validRequests.length),
        'X-RateLimit-Reset': new Date(now + windowMs).toISOString()
      });

      next();
    };
  }

  /**
   * Rate limiter for notification creation (stricter limits)
   */
  notificationCreationLimiter() {
    return this.createLimiter({
      windowMs: 5 * 60 * 1000, // 5 minutes
      max: 10, // 10 notifications per 5 minutes
      message: 'Too many notifications created. Please wait before creating more.',
      keyGenerator: (req) => {
        // Use user ID if authenticated, otherwise IP
        return req.user?.id ? `user_${req.user.id}` : `ip_${req.ip}`;
      }
    });
  }

  /**
   * Rate limiter for notification reading (more permissive)
   */
  notificationReadLimiter() {
    return this.createLimiter({
      windowMs: 1 * 60 * 1000, // 1 minute
      max: 60, // 60 requests per minute
      message: 'Too many notification requests. Please slow down.',
      keyGenerator: (req) => {
        return req.user?.id ? `user_${req.user.id}` : `ip_${req.ip}`;
      }
    });
  }

  /**
   * Rate limiter for WebSocket connections
   */
  websocketConnectionLimiter() {
    return this.createLimiter({
      windowMs: 1 * 60 * 1000, // 1 minute
      max: 5, // 5 connection attempts per minute
      message: 'Too many WebSocket connection attempts. Please wait.',
      keyGenerator: (req) => req.ip || 'unknown'
    });
  }

  /**
   * General API rate limiter
   */
  generalApiLimiter() {
    return this.createLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // 1000 requests per 15 minutes
      message: 'API rate limit exceeded. Please try again later.',
      keyGenerator: (req) => {
        return req.user?.id ? `user_${req.user.id}` : `ip_${req.ip}`;
      }
    });
  }

  /**
   * Rate limiter for analytics endpoints (more permissive for data processing)
   */
  analyticsLimiter() {
    return this.createLimiter({
      windowMs: 5 * 60 * 1000, // 5 minutes
      max: 500, // 500 requests per 5 minutes (higher limit for analytics)
      message: 'Analytics rate limit exceeded. Please reduce request frequency.',
      keyGenerator: (req) => {
        return req.user?.id ? `user_${req.user.id}` : `ip_${req.ip}`;
      }
    });
  }

  /**
   * Rate limiter for settings updates (very permissive for real-time interactions)
   */
  settingsUpdateLimiter() {
    return this.createLimiter({
      windowMs: 1 * 60 * 1000, // 1 minute window
      max: 50, // 50 settings updates per minute (allows rapid interactions)
      message: 'Settings update rate limit exceeded. Please slow down your interactions.',
      keyGenerator: (req) => {
        // Use user ID for per-user limits
        return req.user?.id ? `settings_user_${req.user.id}` : `settings_ip_${req.ip || 'unknown'}`;
      }
    });
  }

  /**
   * Start cleanup process to remove old request data
   */
  startCleanup() {
    setInterval(() => {
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      for (const [key, requests] of this.requests.entries()) {
        // Remove requests older than maxAge
        const validRequests = requests.filter(timestamp => (now - timestamp) < maxAge);
        
        if (validRequests.length === 0) {
          this.requests.delete(key);
        } else {
          this.requests.set(key, validRequests);
        }
      }
    }, this.cleanupInterval);
  }

  /**
   * Get current rate limit statistics
   * @returns {Object} - Rate limit statistics
   */
  getStats() {
    const totalKeys = this.requests.size;
    let totalRequests = 0;

    for (const requests of this.requests.values()) {
      totalRequests += requests.length;
    }

    return {
      totalKeys,
      totalRequests,
      averageRequestsPerKey: totalKeys > 0 ? totalRequests / totalKeys : 0
    };
  }

  /**
   * Clear all rate limit data
   */
  clear() {
    this.requests.clear();
  }

  /**
   * Clear rate limit data for specific key
   * @param {string} key - Key to clear
   */
  clearKey(key) {
    this.requests.delete(key);
  }
}

// Create singleton instance
const rateLimiter = new RateLimiter();

export default rateLimiter;
