import React from 'react';
import { <PERSON>, Col, Card, Statistic, Spin } from 'antd';
import { 
  ClockCircleOutlined, 
  StopOutlined, 
  AlertOutlined, 
  CheckCircleOutlined 
} from '@ant-design/icons';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';

const ArretStatsCards = () => {
  const context = useArretQueuedContext();
  
  // Extra defensive programming
  if (!context) {
    console.error('🚨 ArretStatsCards - No context available!');
    return <div>Context not available</div>;
  }
  
  const { stats = {}, loading = true } = context;

  // Ensure stats object exists and has safe defaults
  const safeStats = {
    totalArrets: stats?.totalArrets ?? 0,
    dureeTotale: stats?.dureeTotale ?? 0,
    arretsCritiques: stats?.arretsCritiques ?? 0,
    machinesActives: stats?.machinesActives ?? 0
  };
  const statsCards = [
    {
      title: "Total des Arrêts",
      value: safeStats.totalArrets,
      icon: <StopOutlined style={{ color: '#ff4d4f' }} />,
      color: '#ff4d4f'
    },
    {
      title: "Durée Totale",
      value: `${safeStats.dureeTotale} min`,
      icon: <ClockCircleOutlined style={{ color: '#faad14' }} />,
      color: '#faad14'
    },
    {
      title: "Arrêts Critiques",
      value: safeStats.arretsCritiques,
      icon: <AlertOutlined style={{ color: '#ff7a45' }} />,
      color: '#ff7a45'
    },
    {
      title: "Machines Actives",
      value: safeStats.machinesActives,
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      color: '#52c41a'
    }
  ];

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      {statsCards.map((stat, index) => (
        <Col xs={24} sm={12} lg={6} key={index}>
          <Card>
            <Spin spinning={loading}>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.color }}
              />
            </Spin>
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default ArretStatsCards;
