import React, { createContext, useContext, useMemo, useRef, useCallback } from 'react';
import { useSettings } from '../hooks/useSettings';
import ChartConfigurationManager from '../config/ChartConfigurationManager';

/**
 * Chart Configuration Context
 * Provides global chart configuration state and ensures consistency across all charts
 */
const ChartConfigurationContext = createContext(null);

/**
 * Chart Configuration Provider
 * Wraps the application and provides unified chart configuration to all components
 */
export const ChartConfigurationProvider = ({ children }) => {
  const { settings, updateSetting } = useSettings();
  const managerRef = useRef(null);
  const configCacheRef = useRef(new Map());
  
  // Create or update configuration manager
  const configManager = useMemo(() => {
    if (!managerRef.current) {
      managerRef.current = new ChartConfigurationManager(settings);
    } else {
      managerRef.current.updateSettings(settings);
    }
    return managerRef.current;
  }, [settings]);

  // Global configuration methods
  const getGlobalChartConfig = useCallback((chartType = 'bar', options = {}) => {
    const cacheKey = `${chartType}_${JSON.stringify(options)}`;
    
    if (configCacheRef.current.has(cacheKey)) {
      return configCacheRef.current.get(cacheKey);
    }
    
    const config = configManager.getChartConfig(chartType, options);
    configCacheRef.current.set(cacheKey, config);
    return config;
  }, [configManager]);

  // Clear cache when settings change
  const clearConfigCache = useCallback(() => {
    configCacheRef.current.clear();
    configManager.clearCache();
  }, [configManager]);

  // Update chart setting and propagate to all charts
  const updateChartSetting = useCallback((path, value) => {
    updateSetting(`charts.${path}`, value);
    clearConfigCache();
  }, [updateSetting, clearConfigCache]);

  // Batch update multiple chart settings
  const updateChartSettings = useCallback((updates) => {
    Object.entries(updates).forEach(([path, value]) => {
      updateSetting(`charts.${path}`, value);
    });
    clearConfigCache();
  }, [updateSetting, clearConfigCache]);

  // Get configuration for specific chart types
  const getBarChartConfig = useCallback((options = {}) => {
    return getGlobalChartConfig('bar', options);
  }, [getGlobalChartConfig]);

  const getLineChartConfig = useCallback((options = {}) => {
    return getGlobalChartConfig('line', options);
  }, [getGlobalChartConfig]);

  const getPieChartConfig = useCallback((options = {}) => {
    return getGlobalChartConfig('pie', options);
  }, [getGlobalChartConfig]);

  // Dynamic chart type resolution
  const resolveChartType = useCallback((requestedType, allowedTypes = ['bar', 'line', 'pie']) => {
    return configManager.getChartType(requestedType, allowedTypes);
  }, [configManager]);

  // Performance monitoring
  const getPerformanceStats = useCallback(() => {
    return {
      cacheSize: configCacheRef.current.size,
      managerCacheStats: configManager.getCacheStats(),
      settingsHash: JSON.stringify(settings).length
    };
  }, [configManager, settings]);

  // Context value
  const contextValue = useMemo(() => ({
    // Core manager
    manager: configManager,
    
    // Configuration methods
    getGlobalChartConfig,
    getBarChartConfig,
    getLineChartConfig,
    getPieChartConfig,
    
    // Settings management
    updateChartSetting,
    updateChartSettings,
    clearConfigCache,
    
    // Utilities
    resolveChartType,
    getPerformanceStats,
    
    // Quick access to common configurations
    colors: configManager.getColorScheme(),
    height: configManager.getChartHeight(),
    margins: configManager.getChartMargins(),
    animations: configManager.getAnimationConfig(),
    interactions: configManager.getInteractionConfig(),
    display: configManager.getDisplayConfig(),
    performance: configManager.getPerformanceConfig(),
    
    // Raw settings (for backward compatibility)
    settings: settings,
    charts: settings && typeof settings === 'object' && settings.charts ? settings.charts : {},
    theme: settings && typeof settings === 'object' && settings.theme ? settings.theme : {}
  }), [
    configManager,
    getGlobalChartConfig,
    getBarChartConfig,
    getLineChartConfig,
    getPieChartConfig,
    updateChartSetting,
    updateChartSettings,
    clearConfigCache,
    resolveChartType,
    getPerformanceStats,
    settings
  ]);

  return (
    <ChartConfigurationContext.Provider value={contextValue}>
      {children}
    </ChartConfigurationContext.Provider>
  );
};

/**
 * Hook to use chart configuration context
 */
export const useChartConfigurationContext = () => {
  const context = useContext(ChartConfigurationContext);
  
  if (!context) {
    throw new Error('useChartConfigurationContext must be used within a ChartConfigurationProvider');
  }
  
  return context;
};

/**
 * Higher-order component to provide chart configuration
 */
export const withChartConfiguration = (Component) => {
  return function ChartConfigurationWrapper(props) {
    const chartConfig = useChartConfigurationContext();
    
    return (
      <Component
        {...props}
        chartConfig={chartConfig}
      />
    );
  };
};

/**
 * Hook for components that need to listen to specific chart settings
 */
export const useChartSettingListener = (settingPath, defaultValue = null) => {
  const { charts, updateChartSetting } = useChartConfigurationContext();
  
  const getValue = useCallback(() => {
    const keys = settingPath.split('.');
    let value = charts;
    
    for (const key of keys) {
      value = value?.[key];
      if (value === undefined) {
        return defaultValue;
      }
    }
    
    return value;
  }, [charts, settingPath, defaultValue]);

  const setValue = useCallback((newValue) => {
    updateChartSetting(settingPath, newValue);
  }, [settingPath, updateChartSetting]);

  return [getValue(), setValue];
};

/**
 * Hook for chart components that need real-time configuration updates
 */
export const useRealtimeChartConfig = (chartType = 'bar', options = {}) => {
  const context = useChartConfigurationContext();
  
  return useMemo(() => {
    const config = context.getGlobalChartConfig(chartType, options);
    
    return {
      ...config,
      // Add real-time update capabilities
      updateSetting: context.updateChartSetting,
      updateSettings: context.updateChartSettings,
      clearCache: context.clearConfigCache,
      
      // Recharts-specific helpers
      axisConfig: context.manager.getAxisConfig(),
      gridConfig: context.manager.getGridConfig(),
      tooltipConfig: context.manager.getTooltipConfig(),
      legendConfig: context.manager.getLegendConfig(),
      responsiveProps: context.manager.getResponsiveContainerProps(),
      
      // Element configurations
      getBarElement: (color, index) => context.manager.getBarElementConfig(color, index),
      getLineElement: (color, index) => context.manager.getLineElementConfig(color, index),
      
      // Data processing
      applyToData: (data, type) => context.manager.applySettingsToData(data, type || chartType)
    };
  }, [context, chartType, options]);
};

export default ChartConfigurationContext;
