#!/usr/bin/env node

/**
 * Test the specific GraphQL queries used by the failing dashboard routes
 */

import http from 'http';

function testGraphQLQuery(query, description) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ query });
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/graphql',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 10000
    };

    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function runDashboardTests() {
  console.log('🔍 Testing Dashboard GraphQL Queries...\n');

  // Test 1: ArretsDashboard - getStopSidecards
  try {
    console.log('1. Testing ArretsDashboard - getStopSidecards...');
    const result1 = await testGraphQLQuery(
      'query { getStopSidecards { Arret_Totale Arret_Totale_nondeclare } }',
      'ArretsDashboard sidecards'
    );
    
    if (result1.data.errors) {
      console.log('❌ Errors:', result1.data.errors);
    } else {
      console.log('✅ Success:', JSON.stringify(result1.data.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Failed:', error.message);
  }

  // Test 2: ProductionDashboard - getDashboardData
  try {
    console.log('\n2. Testing ProductionDashboard - getDashboardData...');
    const result2 = await testGraphQLQuery(
      'query { getDashboardData { productionChart { Date_Insert_Day Total_Good_Qty_Day } sidecards { goodqty rejetqty } } }',
      'ProductionDashboard data'
    );
    
    if (result2.data.errors) {
      console.log('❌ Errors:', result2.data.errors);
    } else {
      console.log('✅ Success:', JSON.stringify(result2.data.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Failed:', error.message);
  }

  // Test 3: ArretsDashboard - getAllMachineStops
  try {
    console.log('\n3. Testing ArretsDashboard - getAllMachineStops...');
    const result3 = await testGraphQLQuery(
      'query { getAllMachineStops(limit: 10) { Machine_Name Date_Insert Code_Stop } }',
      'ArretsDashboard stops'
    );
    
    if (result3.data.errors) {
      console.log('❌ Errors:', result3.data.errors);
    } else {
      console.log('✅ Success:', JSON.stringify(result3.data.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Failed:', error.message);
  }

  // Test 4: ProductionDashboard - getAllDailyProduction
  try {
    console.log('\n4. Testing ProductionDashboard - getAllDailyProduction...');
    const result4 = await testGraphQLQuery(
      'query { getAllDailyProduction(limit: 5) { Date_Insert_Day Machine_Name Total_Good_Qty_Day } }',
      'ProductionDashboard production'
    );
    
    if (result4.data.errors) {
      console.log('❌ Errors:', result4.data.errors);
    } else {
      console.log('✅ Success:', JSON.stringify(result4.data.data, null, 2));
    }
  } catch (error) {
    console.log('❌ Failed:', error.message);
  }

  console.log('\n🏁 Dashboard query testing completed!');
}

runDashboardTests().catch(console.error);
