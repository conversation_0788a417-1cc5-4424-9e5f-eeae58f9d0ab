import React from 'react';
import { usePermission } from '../hooks/usePermission';

/**
 * Component that conditionally renders children based on user permissions
 * 
 * @param {Object} props - Component props
 * @param {string|string[]} [props.permissions] - Required permission(s)
 * @param {string|string[]} [props.roles] - Required role(s)
 * @param {number|number[]} [props.departments] - Required department(s)
 * @param {React.ReactNode} props.children - Child components to render if authorized
 * @param {React.ReactNode} [props.fallback] - Component to render if not authorized
 * @returns {React.ReactNode} Children if authorized, fallback or null if not
 */
const PermissionGuard = ({ 
  permissions, 
  roles, 
  departments, 
  children, 
  fallback = null 
}) => {
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  
  // Check if user meets all specified conditions
  const isAuthorized = (
    // If permissions specified, user must have them
    (!permissions || hasPermission(permissions)) &&
    // If roles specified, user must have one
    (!roles || hasRole(roles)) &&
    // If departments specified, user must have access
    (!departments || hasDepartmentAccess(departments))
  );
  
  // Render children if authorized, fallback otherwise
  return isAuthorized ? children : fallback;
};

export default PermissionGuard;
