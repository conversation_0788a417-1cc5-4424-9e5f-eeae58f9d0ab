import React from 'react';
import { useSettings } from '../hooks/useSettings';
import logoLight from '../assets/logo.jpg';
import logoDark from '../assets/logo_for_DarkMode.jpg';

/**
 * Theme-Aware Logo Component
 * Automatically switches between light and dark mode logos based on current theme
 */
function ThemeAwareLogo({ 
  style = {}, 
  className = '', 
  alt = 'SOMIPEM Logo',
  width,
  height,
  ...props 
}) {
  const { theme } = useSettings();

  // Select appropriate logo based on theme
  const logoSrc = theme?.darkMode ? logoDark : logoLight;

  // Default styling for logo
  const defaultStyle = {
    maxHeight: '40px',
    width: 'auto',
    transition: 'all 0.3s ease',
    ...style
  };

  // Apply width and height if provided
  if (width) defaultStyle.width = width;
  if (height) defaultStyle.height = height;

  return (
    <img
      src={logoSrc}
      alt={alt}
      style={defaultStyle}
      className={`theme-aware-logo ${className}`}
      {...props}
    />
  );
}

export default ThemeAwareLogo;
