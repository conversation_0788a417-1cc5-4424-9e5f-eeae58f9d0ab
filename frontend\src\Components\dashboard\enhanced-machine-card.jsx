
import React from "react" ;
import { Card, Row, Col, Progress, <PERSON>atistic, Badge, Divider, Typography } from "antd"
import {
  DashboardOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons"

import SOMIPEM_COLORS from "../../styles/brand-colors"

const { Title, Text } = Typography

// Use SOMIPEM brand colors
const COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  primaryLight: SOMIPEM_COLORS.SELECTED_BG,
  success: SOMIPEM_COLORS.SUCCESS,
  warning: SOMIPEM_COLORS.WARNING,
  error: SOMIPEM_COLORS.ERROR,
  gray: SOMIPEM_COLORS.LIGHT_GRAY,
  textSecondary: SOMIPEM_COLORS.LIGHT_GRAY,
  bgLight: SOMIPEM_COLORS.LIGHT_BLUE_BG,
  buttonBlue: SOMIPEM_COLORS.SECONDARY_BLUE,
}

// Helper functions to safely parse numeric values from strings
const safeParseFloat = (value) => {
  if (value === undefined || value === null || value === "") return 0
  // Handle both comma and period as decimal separators
  const sanitized = String(value).replace(/,/g, ".")
  const parsed = Number.parseFloat(sanitized)
  return isNaN(parsed) ? 0 : parsed
}

// Format number with comma for decimal separator (e.g., 0.5 -> 0,5)
const formatDecimal = (value, decimals = 1) => {
  if (value === undefined || value === null || value === "") return "0"
  const num = safeParseFloat(value)
  return num.toFixed(decimals).replace(/\./g, ",")
}

// Format number with decimal point for thousands separator (e.g., 813,888 -> 813.888)
const formatThousands = (value) => {
  if (value === undefined || value === null || value === "") return "0"
  const num = safeParseFloat(value)
  return num.toLocaleString('en-US').replace(/,/g, ".")
}



// Update the MetricCard component to handle the new field names
const MetricCard = ({ title, value, suffix = "", color = "inherit", style = {}, useDecimalComma = false, useThousandComma = false }) => {
  // Format the value based on the formatting options
  let displayValue = value;

  if (useDecimalComma) {
    // For values like 0.5 kg -> 0,5 kg
    displayValue = formatDecimal(value);
  } else if (useThousandComma) {
    // For values like 813888 -> 813,888
    displayValue = formatThousands(value);
  }

  return (
    <Card
      size="small"
      style={{
        background: COLORS.bgLight,
        borderRadius: "8px",
        height: "100%",
        padding: "12px",
        ...style,
      }}
    >
      <div style={{ fontSize: "12px", marginBottom: "4px", color: COLORS.textSecondary }}>{title}</div>
      <div style={{ fontSize: "14px", fontWeight: "bold", color: color }}>
        {displayValue} {suffix}
      </div>
    </Card>
  );
}

// Update the EnhancedMachineCard component to use the correct field names from real_time_table
const EnhancedMachineCard = ({ machine, handleMachineClick, getStatusColor }) => {
  // Helper function to get progress color
  const getProgressColor = (progress) => {
    if (progress > 90) return COLORS.success
    if (progress > 75) return COLORS.warning
    return COLORS.error
  }
  const getTrsColor = (TrsValue) => {
    if (TrsValue > 90) return COLORS.success
    if (TrsValue > 75) return COLORS.warning
    return COLORS.error
  }

  // Format TRS value - safely parse from string
  const trsValue = safeParseFloat(machine.TRS || "0").toFixed(1).replace(".", ",")
  // Format progress value - safely parse from string
  const progressValue =
    ((safeParseFloat(machine.Quantite_Bon) || 0) / (safeParseFloat(machine.Quantite_Planifier) || 1)) * 100

  return (
    <Card
      hoverable
      onClick={() => handleMachineClick(machine)}
      className="machine-card"
      style={{
        borderRadius: "12px",
        overflow: "hidden",
        height: "100%",
        transition: "all 0.3s",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08)",
        position: "relative",
      }}
    >
      {/* Status indicator */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "6px",
          background: machine.id ? getStatusColor(machine.status, machine) : COLORS.gray,
        }}
      />

      {/* Header Section */}
      <div style={{ padding: "16px 16px 0", marginTop: "6px" }}>
        <div style={{ display: "flex", alignItems: "flex-start", marginBottom: "16px" }}>
          <div
            style={{
              width: "48px",
              height: "48px",
              borderRadius: "12px",
              background: COLORS.primaryLight,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginRight: "12px",
            }}
          >
            <DashboardOutlined style={{ fontSize: "24px", color: COLORS.primary }} />
          </div>
          <div style={{ flex: 1 }}>
            <Title level={4} style={{ margin: 0, fontSize: "18px" }}>
              {machine.Machine_Name || "IPS01"}
            </Title>
            <Text type="secondary" style={{ fontSize: "14px" }}>
              Chef de poste: {machine.Regleur_Prenom || "Non assigné"}
            </Text>
            {machine.Etat === "off" && machine.Code_arret && (
              <Text type="danger" style={{ fontSize: "16px", display: "block", marginTop: "4px" }}>
                {machine.Code_arret}
              </Text>
            )}
          </div>

          {/* TRS and Session badge in a column layout */}
          <div style={{ textAlign: "right", display: "flex", flexDirection: "column", alignItems: "flex-end" }}>
            {machine.Etat === "on" && (
              <Badge
                color="#1890ff"
                text={<span style={{ color: "#1890ff", fontWeight: 500 }}>Session active</span>}
                style={{
                  fontSize: "12px",
                  marginBottom: "8px",
                }}
              />
            )}
            <div style={{ marginTop: machine.Etat === "on" ? "0" : "12px" }}>
              <div
                style={{
                  width: "80px",
                  height: "80px",
                  position: "relative",
                }}
              >
                <Progress
                  type="circle"
                  percent={Number.parseFloat(trsValue)}
                  width={80}
                  strokeColor={getProgressColor(Number.parseFloat(trsValue))}
                  strokeWidth={8}
                  format={() => (
                    <div>
                      <div style={{ fontSize: "18px", fontWeight: "bold" }}>{trsValue}%</div>
                      <div style={{ fontSize: "12px", marginTop: "-2px" }}>TRS</div>
                    </div>
                  )}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Info fields row - Order, Article, Cycle Time */}
        <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
          <Col span={8}>
            <MetricCard
              title="Ordre de fabrication"
              value={machine.Ordre_Fabrication || "N/A"}
              style={{ textAlign: "center" }}
            />
          </Col>
          <Col span={8}>
            <MetricCard title="Article" value={machine.Article || "N/A"} style={{ textAlign: "center" }} />
          </Col>
          <Col span={8}>
            <MetricCard
              title="Empreintes"
              value={machine.empreint +"/"+machine.empreint || "N/A"}

              style={{ textAlign: "center" }}
            />
          </Col>
        </Row>

        {/* Metrics Section 1 - Material and Time */}
        <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
          <Col span={8}>
            <MetricCard
              title="Rejet"
              value={machine.Quantite_Rejet || "0"}
              suffix="kg"
              style={{ textAlign: "center" }}
              useDecimalComma={true}
            />
          </Col>
          <Col span={8}>
            <MetricCard
              title="Purge"
              value={machine.Poids_Purge || "0"}
              suffix="Kg"
              style={{ textAlign: "center" }}
              useDecimalComma={true}
            />
          </Col>
          <Col span={8}>
            <MetricCard
              title="Temps de cycle"
              value={safeParseFloat(machine.cycle || "0").toFixed(2).replace(".", ",")+"/"+safeParseFloat(machine.cycle_theorique || "0").toFixed(2).replace(".", ",")}
              suffix="sec"
              style={{ textAlign: "center" }}
            />
          </Col>
        </Row>

        {/* Progress Section */}
        <div style={{ marginBottom: "16px" }}>
          <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "4px" }}>
            <Text strong>Progression</Text>
            <Text type="secondary">{progressValue.toFixed(1).replace(".", ",")}% d'objectif</Text>
          </div>
          <Progress
            percent={progressValue}
            strokeColor={getProgressColor(progressValue)}
            showInfo={false}
            strokeWidth={8}
            trailColor="rgba(0,0,0,0.04)"
          />
        </div>

        {/* Statistics Section */}
        <div style={{ marginTop: "16px" }}>
          <Divider style={{ margin: "16px 0" }} />
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title={
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <ClockCircleOutlined style={{ marginRight: "4px", color: COLORS.textSecondary }} />
                    <span style={{ fontSize: "13px", color: COLORS.textSecondary }}>Planifié</span>
                  </div>
                }
                value={safeParseFloat(machine.Quantite_Planifier) || 0}
                formatter={(value) => value.toLocaleString().replace(/,/g, ".")}
                suffix="pcs"
                valueStyle={{ fontSize: "16px", fontWeight: "bold" }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <CheckCircleOutlined
                      style={{
                        marginRight: "4px",
                        color: machine.id ? COLORS.success : COLORS.gray,
                      }}
                    />
                    <span style={{ fontSize: "13px", color: COLORS.textSecondary }}>Bon</span>
                  </div>
                }
                value={safeParseFloat(machine.Quantite_Bon) || 0}
                formatter={(value) => value.toLocaleString().replace(/,/g, ".")}
                suffix="pcs"
                valueStyle={{ fontSize: "16px", fontWeight: "bold", color: COLORS.success }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <CloseCircleOutlined
                      style={{
                        marginRight: "4px",
                        color: machine.id ? COLORS.error : COLORS.gray,
                      }}
                    />
                    <span style={{ fontSize: "13px", color: COLORS.textSecondary }}>Rejet</span>
                  </div>
                }
                value={Math.trunc((machine.Quantite_Rejet * 1000) / machine.Poid_unitaire || "0") || 0}
                formatter={(value) => value.toLocaleString().replace(/,/g, ".")}
                suffix="pcs"
                valueStyle={{ fontSize: "16px", fontWeight: "bold", color: COLORS.error }}
              />
            </Col>
          </Row>
          {/* Added spacing below Statistics Section */}
          <div style={{ marginBottom: "24px" }}></div>
        </div>
      </div>

      {/* Details button */}
      <div
        style={{
          position: "absolute",
          bottom: "12px",
          right: "12px",
          background: COLORS.primaryLight,
          borderRadius: "50%",
          width: "32px",
          height: "32px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
        }}
        onClick={(e) => {
          e.stopPropagation()
          handleMachineClick(machine)
        }}
      >
        <InfoCircleOutlined style={{ color: COLORS.primary }} />
      </div>
    </Card>
  )
}

export default EnhancedMachineCard
