import{r,_ as lt,o as it,p as me,q as N,t as xe,w as ct,v as Ce,x as Ne,y as D,z as je,K as ge,H as ze,I as dt,J as Ge,L as mt,N as Pe,O as ut,Q as ft,U as pt,V as ae,W as gt,X as vt,Y as ht,Z as bt,$ as yt,a0 as De,a1 as xt,a2 as Z,a3 as Ct,R as a,a4 as ue,j as Et,a5 as wt,a6 as Ot,u as St,a7 as kt,a8 as w,a9 as $t,l as Ae,aa as ve,ab as Rt,g as _,ac as Le,D as He,d as de,T as It,e as Mt,ad as Nt,ae as jt}from"./index-y9W4UQPd.js";import{I as _e,l as Be,a as Te}from"./logo_for_DarkMode-Dp87Vf_v.js";import{u as Ye}from"./usePermission-WvgDJJ2d.js";import{S as zt}from"./SSENotificationBell-B63B8UAg.js";import{R as Pt}from"./HomeOutlined-DexCJo75.js";import{R as Dt}from"./DashboardOutlined-DzkF0_SV.js";import{R as At}from"./AlertOutlined-ByJiI9cI.js";import{R as Lt}from"./LineChartOutlined-Bc1BVguD.js";import{R as Ht}from"./BarChartOutlined-B49EAf86.js";import{R as _t}from"./ToolOutlined-bXQU0KMP.js";import{R as Bt}from"./BellOutlined-CN3CDS2V.js";import{L as Ee}from"./index-B6VLygmr.js";import{R as Tt}from"./CloseOutlined-BMC5_DtO.js";import{S as Wt}from"./index-pzHVCrlC.js";import{R as Kt}from"./CalendarOutlined-BvTWpIAf.js";import{A as Vt}from"./index-BCUNiCcZ.js";import"./ZoomOutOutlined-CdPjzMfa.js";import"./dayjs.min-D8Lc9v5x.js";import"./relativeTime-D5nVX57i.js";import"./fr-DGSiljjZ.js";import"./ReloadOutlined-Cyu5KuEL.js";import"./index-Rdb88Q3H.js";import"./InfoCircleOutlined-DzeHyv3U.js";import"./AppstoreOutlined-CD3bDheK.js";var We=r.createContext(null),Xe=r.createContext({}),Ft=["prefixCls","className","containerRef"],Ut=function(t){var o=t.prefixCls,n=t.className,l=t.containerRef,c=lt(t,Ft),d=r.useContext(Xe),m=d.panel,p=it(m,l);return r.createElement("div",me({className:N("".concat(o,"-content"),n),role:"dialog",ref:p},xe(t,{aria:!0}),{"aria-modal":"true"},c))};function Ke(e){return typeof e=="string"&&String(Number(e))===e?(ct(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var Ve={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function Gt(e,t){var o,n,l,c=e.prefixCls,d=e.open,m=e.placement,p=e.inline,u=e.push,b=e.forceRender,x=e.autoFocus,j=e.keyboard,i=e.classNames,g=e.rootClassName,h=e.rootStyle,S=e.zIndex,k=e.className,R=e.id,$=e.style,O=e.motion,f=e.width,C=e.height,P=e.children,A=e.mask,L=e.maskClosable,z=e.maskMotion,B=e.maskClassName,s=e.maskStyle,I=e.afterOpenChange,W=e.onClose,K=e.onMouseEnter,oe=e.onMouseOver,re=e.onMouseLeave,Q=e.onClick,se=e.onKeyDown,le=e.onKeyUp,E=e.styles,V=e.drawerRender,H=r.useRef(),F=r.useRef(),U=r.useRef();r.useImperativeHandle(t,function(){return H.current});var G=function(M){var ee=M.keyCode,te=M.shiftKey;switch(ee){case ge.TAB:{if(ee===ge.TAB){if(!te&&document.activeElement===U.current){var ne;(ne=F.current)===null||ne===void 0||ne.focus({preventScroll:!0})}else if(te&&document.activeElement===F.current){var pe;(pe=U.current)===null||pe===void 0||pe.focus({preventScroll:!0})}}break}case ge.ESC:{W&&j&&(M.stopPropagation(),W(M));break}}};r.useEffect(function(){if(d&&x){var v;(v=H.current)===null||v===void 0||v.focus({preventScroll:!0})}},[d]);var Y=r.useState(!1),ie=Ce(Y,2),ce=ie[0],J=ie[1],y=r.useContext(We),fe;typeof u=="boolean"?fe=u?{}:{distance:0}:fe=u||{};var X=(o=(n=(l=fe)===null||l===void 0?void 0:l.distance)!==null&&n!==void 0?n:y==null?void 0:y.pushDistance)!==null&&o!==void 0?o:180,nt=r.useMemo(function(){return{pushDistance:X,push:function(){J(!0)},pull:function(){J(!1)}}},[X]);r.useEffect(function(){if(d){var v;y==null||(v=y.push)===null||v===void 0||v.call(y)}else{var M;y==null||(M=y.pull)===null||M===void 0||M.call(y)}},[d]),r.useEffect(function(){return function(){var v;y==null||(v=y.pull)===null||v===void 0||v.call(y)}},[]);var at=A&&r.createElement(Ne,me({key:"mask"},z,{visible:d}),function(v,M){var ee=v.className,te=v.style;return r.createElement("div",{className:N("".concat(c,"-mask"),ee,i==null?void 0:i.mask,B),style:D(D(D({},te),s),E==null?void 0:E.mask),onClick:L&&d?W:void 0,ref:M})}),ot=typeof O=="function"?O(m):O,q={};if(ce&&X)switch(m){case"top":q.transform="translateY(".concat(X,"px)");break;case"bottom":q.transform="translateY(".concat(-X,"px)");break;case"left":q.transform="translateX(".concat(X,"px)");break;default:q.transform="translateX(".concat(-X,"px)");break}m==="left"||m==="right"?q.width=Ke(f):q.height=Ke(C);var rt={onMouseEnter:K,onMouseOver:oe,onMouseLeave:re,onClick:Q,onKeyDown:se,onKeyUp:le},st=r.createElement(Ne,me({key:"panel"},ot,{visible:d,forceRender:b,onVisibleChanged:function(M){I==null||I(M)},removeOnLeave:!1,leavedClassName:"".concat(c,"-content-wrapper-hidden")}),function(v,M){var ee=v.className,te=v.style,ne=r.createElement(Ut,me({id:R,containerRef:M,prefixCls:c,className:N(k,i==null?void 0:i.content),style:D(D({},$),E==null?void 0:E.content)},xe(e,{aria:!0}),rt),P);return r.createElement("div",me({className:N("".concat(c,"-content-wrapper"),i==null?void 0:i.wrapper,ee),style:D(D(D({},q),te),E==null?void 0:E.wrapper)},xe(e,{data:!0})),V?V(ne):ne)}),Me=D({},h);return S&&(Me.zIndex=S),r.createElement(We.Provider,{value:nt},r.createElement("div",{className:N(c,"".concat(c,"-").concat(m),g,je(je({},"".concat(c,"-open"),d),"".concat(c,"-inline"),p)),style:Me,tabIndex:-1,ref:H,onKeyDown:G},at,r.createElement("div",{tabIndex:0,ref:F,style:Ve,"aria-hidden":"true","data-sentinel":"start"}),st,r.createElement("div",{tabIndex:0,ref:U,style:Ve,"aria-hidden":"true","data-sentinel":"end"})))}var Yt=r.forwardRef(Gt),Xt=function(t){var o=t.open,n=o===void 0?!1:o,l=t.prefixCls,c=l===void 0?"rc-drawer":l,d=t.placement,m=d===void 0?"right":d,p=t.autoFocus,u=p===void 0?!0:p,b=t.keyboard,x=b===void 0?!0:b,j=t.width,i=j===void 0?378:j,g=t.mask,h=g===void 0?!0:g,S=t.maskClosable,k=S===void 0?!0:S,R=t.getContainer,$=t.forceRender,O=t.afterOpenChange,f=t.destroyOnClose,C=t.onMouseEnter,P=t.onMouseOver,A=t.onMouseLeave,L=t.onClick,z=t.onKeyDown,B=t.onKeyUp,s=t.panelRef,I=r.useState(!1),W=Ce(I,2),K=W[0],oe=W[1],re=r.useState(!1),Q=Ce(re,2),se=Q[0],le=Q[1];ze(function(){le(!0)},[]);var E=se?n:!1,V=r.useRef(),H=r.useRef();ze(function(){E&&(H.current=document.activeElement)},[E]);var F=function(ce){var J;if(oe(ce),O==null||O(ce),!ce&&H.current&&!((J=V.current)!==null&&J!==void 0&&J.contains(H.current))){var y;(y=H.current)===null||y===void 0||y.focus({preventScroll:!0})}},U=r.useMemo(function(){return{panel:s}},[s]);if(!$&&!K&&!E&&f)return null;var G={onMouseEnter:C,onMouseOver:P,onMouseLeave:A,onClick:L,onKeyDown:z,onKeyUp:B},Y=D(D({},t),{},{open:E,prefixCls:c,placement:m,autoFocus:u,keyboard:x,width:i,mask:h,maskClosable:k,inline:R===!1,afterOpenChange:F,ref:V},G);return r.createElement(Xe.Provider,{value:U},r.createElement(dt,{open:E||$||K,autoDestroy:!1,getContainer:R,autoLock:h&&(E||K)},r.createElement(Yt,Y)))};const qe=e=>{var t,o;const{prefixCls:n,title:l,footer:c,extra:d,loading:m,onClose:p,headerStyle:u,bodyStyle:b,footerStyle:x,children:j,classNames:i,styles:g}=e,h=Ge("drawer"),S=r.useCallback(f=>r.createElement("button",{type:"button",onClick:p,className:`${n}-close`},f),[p]),[k,R]=mt(Pe(e),Pe(h),{closable:!0,closeIconRender:S}),$=r.useMemo(()=>{var f,C;return!l&&!k?null:r.createElement("div",{style:Object.assign(Object.assign(Object.assign({},(f=h.styles)===null||f===void 0?void 0:f.header),u),g==null?void 0:g.header),className:N(`${n}-header`,{[`${n}-header-close-only`]:k&&!l&&!d},(C=h.classNames)===null||C===void 0?void 0:C.header,i==null?void 0:i.header)},r.createElement("div",{className:`${n}-header-title`},R,l&&r.createElement("div",{className:`${n}-title`},l)),d&&r.createElement("div",{className:`${n}-extra`},d))},[k,R,d,u,n,l]),O=r.useMemo(()=>{var f,C;if(!c)return null;const P=`${n}-footer`;return r.createElement("div",{className:N(P,(f=h.classNames)===null||f===void 0?void 0:f.footer,i==null?void 0:i.footer),style:Object.assign(Object.assign(Object.assign({},(C=h.styles)===null||C===void 0?void 0:C.footer),x),g==null?void 0:g.footer)},c)},[c,x,n]);return r.createElement(r.Fragment,null,$,r.createElement("div",{className:N(`${n}-body`,i==null?void 0:i.body,(t=h.classNames)===null||t===void 0?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},(o=h.styles)===null||o===void 0?void 0:o.body),b),g==null?void 0:g.body)},m?r.createElement(ut,{active:!0,title:!1,paragraph:{rows:5},className:`${n}-body-skeleton`}):j),O)},qt=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},Ze=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),Qe=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},Ze({opacity:e},{opacity:1})),Zt=(e,t)=>[Qe(.7,t),Ze({transform:qt(e)},{transform:"none"})],Qt=e=>{const{componentCls:t,motionDurationSlow:o}=e;return{[t]:{[`${t}-mask-motion`]:Qe(0,o),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce((n,l)=>Object.assign(Object.assign({},n),{[`&-${l}`]:Zt(l,o)}),{})}}},Jt=e=>{const{borderRadiusSM:t,componentCls:o,zIndexPopup:n,colorBgMask:l,colorBgElevated:c,motionDurationSlow:d,motionDurationMid:m,paddingXS:p,padding:u,paddingLG:b,fontSizeLG:x,lineHeightLG:j,lineWidth:i,lineType:g,colorSplit:h,marginXS:S,colorIcon:k,colorIconHover:R,colorBgTextHover:$,colorBgTextActive:O,colorText:f,fontWeightStrong:C,footerPaddingBlock:P,footerPaddingInline:A,calc:L}=e,z=`${o}-content-wrapper`;return{[o]:{position:"fixed",inset:0,zIndex:n,pointerEvents:"none",color:f,"&-pure":{position:"relative",background:c,display:"flex",flexDirection:"column",[`&${o}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${o}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${o}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${o}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${o}-mask`]:{position:"absolute",inset:0,zIndex:n,background:l,pointerEvents:"auto"},[z]:{position:"absolute",zIndex:n,maxWidth:"100vw",transition:`all ${d}`,"&-hidden":{display:"none"}},[`&-left > ${z}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${z}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${z}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${z}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${o}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:c,pointerEvents:"auto"},[`${o}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${ae(u)} ${ae(b)}`,fontSize:x,lineHeight:j,borderBottom:`${ae(i)} ${g} ${h}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${o}-extra`]:{flex:"none"},[`${o}-close`]:Object.assign({display:"inline-flex",width:L(x).add(p).equal(),height:L(x).add(p).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:S,color:k,fontWeight:C,fontSize:x,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${m}`,textRendering:"auto","&:hover":{color:R,backgroundColor:$,textDecoration:"none"},"&:active":{backgroundColor:O}},gt(e)),[`${o}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:x,lineHeight:j},[`${o}-body`]:{flex:1,minWidth:0,minHeight:0,padding:b,overflow:"auto",[`${o}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${o}-footer`]:{flexShrink:0,padding:`${ae(P)} ${ae(A)}`,borderTop:`${ae(i)} ${g} ${h}`},"&-rtl":{direction:"rtl"}}}},en=e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding}),Je=ft("Drawer",e=>{const t=pt(e,{});return[Jt(t),Qt(t)]},en);var et=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(o[n[l]]=e[n[l]]);return o};const tn={distance:180},tt=e=>{const{rootClassName:t,width:o,height:n,size:l="default",mask:c=!0,push:d=tn,open:m,afterOpenChange:p,onClose:u,prefixCls:b,getContainer:x,style:j,className:i,visible:g,afterVisibleChange:h,maskStyle:S,drawerStyle:k,contentWrapperStyle:R,destroyOnClose:$,destroyOnHidden:O}=e,f=et(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle","destroyOnClose","destroyOnHidden"]),{getPopupContainer:C,getPrefixCls:P,direction:A,className:L,style:z,classNames:B,styles:s}=Ge("drawer"),I=P("drawer",b),[W,K,oe]=Je(I),re=x===void 0&&C?()=>C(document.body):x,Q=N({"no-mask":!c,[`${I}-rtl`]:A==="rtl"},t,K,oe),se=r.useMemo(()=>o??(l==="large"?736:378),[o,l]),le=r.useMemo(()=>n??(l==="large"?736:378),[n,l]),E={motionName:De(I,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},V=ie=>({motionName:De(I,`panel-motion-${ie}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500}),H=vt(),[F,U]=ht("Drawer",f.zIndex),{classNames:G={},styles:Y={}}=f;return W(r.createElement(bt,{form:!0,space:!0},r.createElement(yt.Provider,{value:U},r.createElement(Xt,Object.assign({prefixCls:I,onClose:u,maskMotion:E,motion:V},f,{classNames:{mask:N(G.mask,B.mask),content:N(G.content,B.content),wrapper:N(G.wrapper,B.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},Y.mask),S),s.mask),content:Object.assign(Object.assign(Object.assign({},Y.content),k),s.content),wrapper:Object.assign(Object.assign(Object.assign({},Y.wrapper),R),s.wrapper)},open:m??g,mask:c,push:d,width:se,height:le,style:Object.assign(Object.assign({},z),j),className:N(L,i),rootClassName:Q,getContainer:re,afterOpenChange:p??h,panelRef:H,zIndex:F,destroyOnClose:O??$}),r.createElement(qe,Object.assign({prefixCls:I},f,{onClose:u}))))))},nn=e=>{const{prefixCls:t,style:o,className:n,placement:l="right"}=e,c=et(e,["prefixCls","style","className","placement"]),{getPrefixCls:d}=r.useContext(xt),m=d("drawer",t),[p,u,b]=Je(m),x=N(m,`${m}-pure`,`${m}-${l}`,u,b,n);return p(r.createElement("div",{className:x,style:o},r.createElement(qe,Object.assign({prefixCls:m},c))))};tt._InternalPanelDoNotUseOrYouWillBeFired=nn;var an={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.4 800.9c.2-.3.5-.6.7-.9C920.6 722.1 960 621.7 960 512s-39.4-210.1-104.8-288c-.2-.3-.5-.5-.7-.8-1.1-1.3-2.1-2.5-3.2-3.7-.4-.5-.8-.9-1.2-1.4l-4.1-4.7-.1-.1c-1.5-1.7-3.1-3.4-4.6-5.1l-.1-.1c-3.2-3.4-6.4-6.8-9.7-10.1l-.1-.1-4.8-4.8-.3-.3c-1.5-1.5-3-2.9-4.5-4.3-.5-.5-1-1-1.6-1.5-1-1-2-1.9-3-2.8-.3-.3-.7-.6-1-1C736.4 109.2 629.5 64 512 64s-224.4 45.2-304.3 119.2c-.3.3-.7.6-1 1-1 .9-2 1.9-3 2.9-.5.5-1 1-1.6 1.5-1.5 1.4-3 2.9-4.5 4.3l-.3.3-4.8 4.8-.1.1c-3.3 3.3-6.5 6.7-9.7 10.1l-.1.1c-1.6 1.7-3.1 3.4-4.6 5.1l-.1.1c-1.4 1.5-2.8 3.1-4.1 4.7-.4.5-.8.9-1.2 1.4-1.1 1.2-2.1 2.5-3.2 3.7-.2.3-.5.5-.7.8C103.4 301.9 64 402.3 64 512s39.4 210.1 104.8 288c.2.3.5.6.7.9l3.1 3.7c.4.5.8.9 1.2 1.4l4.1 4.7c0 .1.1.1.1.2 1.5 1.7 3 3.4 4.6 5l.1.1c3.2 3.4 6.4 6.8 9.6 10.1l.1.1c1.6 1.6 3.1 3.2 4.7 4.7l.3.3c3.3 3.3 6.7 6.5 10.1 9.6 80.1 74 187 119.2 304.5 119.2s224.4-45.2 304.3-119.2a300 300 0 0010-9.6l.3-.3c1.6-1.6 3.2-3.1 4.7-4.7l.1-.1c3.3-3.3 6.5-6.7 9.6-10.1l.1-.1c1.5-1.7 3.1-3.3 4.6-5 0-.1.1-.1.1-.2 1.4-1.5 2.8-3.1 4.1-4.7.4-.5.8-.9 1.2-1.4a99 99 0 003.3-3.7zm4.1-142.6c-13.8 32.6-32 62.8-54.2 90.2a444.07 444.07 0 00-81.5-55.9c11.6-46.9 18.8-98.4 20.7-152.6H887c-3 40.9-12.6 80.6-28.5 118.3zM887 484H743.5c-1.9-54.2-9.1-105.7-20.7-152.6 29.3-15.6 56.6-34.4 81.5-55.9A373.86 373.86 0 01887 484zM658.3 165.5c39.7 16.8 75.8 40 107.6 69.2a394.72 394.72 0 01-59.4 41.8c-15.7-45-35.8-84.1-59.2-115.4 3.7 1.4 7.4 2.9 11 4.4zm-90.6 700.6c-9.2 7.2-18.4 12.7-27.7 16.4V697a389.1 389.1 0 01115.7 26.2c-8.3 24.6-17.9 47.3-29 67.8-17.4 32.4-37.8 58.3-59 75.1zm59-633.1c11 20.6 20.7 43.3 29 67.8A389.1 389.1 0 01540 327V141.6c9.2 3.7 18.5 9.1 27.7 16.4 21.2 16.7 41.6 42.6 59 75zM540 640.9V540h147.5c-1.6 44.2-7.1 87.1-16.3 127.8l-.3 1.2A445.02 445.02 0 00540 640.9zm0-156.9V383.1c45.8-2.8 89.8-12.5 130.9-28.1l.3 1.2c9.2 40.7 14.7 83.5 16.3 127.8H540zm-56 56v100.9c-45.8 2.8-89.8 12.5-130.9 28.1l-.3-1.2c-9.2-40.7-14.7-83.5-16.3-127.8H484zm-147.5-56c1.6-44.2 7.1-87.1 16.3-127.8l.3-1.2c41.1 15.6 85 25.3 130.9 28.1V484H336.5zM484 697v185.4c-9.2-3.7-18.5-9.1-27.7-16.4-21.2-16.7-41.7-42.7-59.1-75.1-11-20.6-20.7-43.3-29-67.8 37.2-14.6 75.9-23.3 115.8-26.1zm0-370a389.1 389.1 0 01-115.7-26.2c8.3-24.6 17.9-47.3 29-67.8 17.4-32.4 37.8-58.4 59.1-75.1 9.2-7.2 18.4-12.7 27.7-16.4V327zM365.7 165.5c3.7-1.5 7.3-3 11-4.4-23.4 31.3-43.5 70.4-59.2 115.4-21-12-40.9-26-59.4-41.8 31.8-29.2 67.9-52.4 107.6-69.2zM165.5 365.7c13.8-32.6 32-62.8 54.2-90.2 24.9 21.5 52.2 40.3 81.5 55.9-11.6 46.9-18.8 98.4-20.7 152.6H137c3-40.9 12.6-80.6 28.5-118.3zM137 540h143.5c1.9 54.2 9.1 105.7 20.7 152.6a444.07 444.07 0 00-81.5 55.9A373.86 373.86 0 01137 540zm228.7 318.5c-39.7-16.8-75.8-40-107.6-69.2 18.5-15.8 38.4-29.7 59.4-41.8 15.7 45 35.8 84.1 59.2 115.4-3.7-1.4-7.4-2.9-11-4.4zm292.6 0c-3.7 1.5-7.3 3-11 4.4 23.4-31.3 43.5-70.4 59.2-115.4 21 12 40.9 26 59.4 41.8a373.81 373.81 0 01-107.6 69.2z"}}]},name:"global",theme:"outlined"},on={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"},rn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},sn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},ln={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M489.5 111.66c30.65-1.8 45.98 36.44 22.58 56.33A243.35 243.35 0 00426 354c0 134.76 109.24 244 244 244 72.58 0 139.9-31.83 186.01-86.08 19.87-23.38 58.07-8.1 56.34 22.53C900.4 745.82 725.15 912 512.5 912 291.31 912 112 732.69 112 511.5c0-211.39 164.29-386.02 374.2-399.65l.2-.01zm-81.15 79.75l-4.11 1.36C271.1 237.94 176 364.09 176 511.5 176 697.34 326.66 848 512.5 848c148.28 0 274.94-96.2 319.45-230.41l.63-1.93-.11.07a307.06 307.06 0 01-159.73 46.26L670 662c-170.1 0-308-137.9-308-308 0-58.6 16.48-114.54 46.27-162.47z"}}]},name:"moon",theme:"outlined"},cn={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M548 818v126a16 16 0 01-16 16h-40a16 16 0 01-16-16V818c15.85 1.64 27.84 2.46 36 2.46 8.15 0 20.16-.82 36-2.46m205.25-115.66l89.1 89.1a16 16 0 010 22.62l-28.29 28.29a16 16 0 01-22.62 0l-89.1-89.1c12.37-10.04 21.43-17.95 27.2-23.71 5.76-5.77 13.67-14.84 23.71-27.2m-482.5 0c10.04 12.36 17.95 21.43 23.71 27.2 5.77 5.76 14.84 13.67 27.2 23.71l-89.1 89.1a16 16 0 01-22.62 0l-28.29-28.29a16 16 0 010-22.63zM512 278c129.24 0 234 104.77 234 234S641.24 746 512 746 278 641.24 278 512s104.77-234 234-234m0 72c-89.47 0-162 72.53-162 162s72.53 162 162 162 162-72.53 162-162-72.53-162-162-162M206 476c-1.64 15.85-2.46 27.84-2.46 36 0 8.15.82 20.16 2.46 36H80a16 16 0 01-16-16v-40a16 16 0 0116-16zm738 0a16 16 0 0116 16v40a16 16 0 01-16 16H818c1.64-15.85 2.46-27.84 2.46-36 0-8.15-.82-20.16-2.46-36zM814.06 180.65l28.29 28.29a16 16 0 010 22.63l-89.1 89.09c-10.04-12.37-17.95-21.43-23.71-27.2-5.77-5.76-14.84-13.67-27.2-23.71l89.1-89.1a16 16 0 0122.62 0m-581.5 0l89.1 89.1c-12.37 10.04-21.43 17.95-27.2 23.71-5.76 5.77-13.67 14.84-23.71 27.2l-89.1-89.1a16 16 0 010-22.62l28.29-28.29a16 16 0 0122.62 0M532 64a16 16 0 0116 16v126c-15.85-1.64-27.84-2.46-36-2.46-8.15 0-20.16.82-36 2.46V80a16 16 0 0116-16z"}}]},name:"sun",theme:"outlined"};function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},we.apply(this,arguments)}const dn=(e,t)=>r.createElement(Z,we({},e,{ref:t,icon:an})),Fe=r.forwardRef(dn);function Oe(){return Oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},Oe.apply(this,arguments)}const mn=(e,t)=>r.createElement(Z,Oe({},e,{ref:t,icon:on})),un=r.forwardRef(mn);function Se(){return Se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},Se.apply(this,arguments)}const fn=(e,t)=>r.createElement(Z,Se({},e,{ref:t,icon:rn})),pn=r.forwardRef(fn);function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},ke.apply(this,arguments)}const gn=(e,t)=>r.createElement(Z,ke({},e,{ref:t,icon:sn})),vn=r.forwardRef(gn);function $e(){return $e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},$e.apply(this,arguments)}const hn=(e,t)=>r.createElement(Z,$e({},e,{ref:t,icon:ln})),he=r.forwardRef(hn);function Re(){return Re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},Re.apply(this,arguments)}const bn=(e,t)=>r.createElement(Z,Re({},e,{ref:t,icon:Ct})),be=r.forwardRef(bn);function Ie(){return Ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},Ie.apply(this,arguments)}const yn=(e,t)=>r.createElement(Z,Ie({},e,{ref:t,icon:cn})),ye=r.forwardRef(yn),T=({to:e,permissions:t,roles:o,departments:n,children:l})=>{const{hasPermission:c,hasRole:d,hasDepartmentAccess:m}=Ye();return(!t||c(t))&&(!o||d(o))&&(!n||m(n))?a.createElement(ue,{to:e},l):null},{Header:xn,Sider:Cn,Content:En,Footer:wn}=Ee,{Text:Ue,Title:On}=It,Xn=({currentDate:e=new Date().toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})=>{const[t,o]=r.useState(3),{darkMode:n,toggleDarkMode:l}=Et(),c=wt(),d=Ot(),{user:m,logout:p}=St(),{collapsed:u,setCollapsed:b,broken:x,setBroken:j,isMobile:i,screens:g,LAYOUT_CONSTANTS:h,getZIndex:S}=kt(),k=h.SIDEBAR_WIDTH,R=h.COLLAPSED_WIDTH,$=()=>{const s=c.pathname;return s.includes("/home")?"1":s.includes("/production")?"2":s==="/arrets"?"3-1":s==="/arrets-dashboard"?"3-2":s.includes("/arrets")?"3":s.includes("/admin/users")?"admin":s.includes("/profile")?"/profile":"1"},O=async({key:s})=>{s==="1"?d("/profile"):s==="3"||s==="4"&&(await p(),d("/login"))},{hasPermission:f,hasRole:C}=Ye(),P=s=>s?!s.permissions&&!s.roles?!0:(!s.permissions||f(s.permissions))&&(!s.roles||C(s.roles)):!1,A=[{key:"1",icon:a.createElement(Pt,null),label:a.createElement(T,{to:"/home",permissions:w.dashboard.permissions},"Accueil"),permissions:w.dashboard.permissions},{key:"2",icon:a.createElement(Dt,null),label:a.createElement(T,{to:"/production",permissions:w.production.permissions},"Production"),permissions:w.production.permissions},{key:"3",icon:a.createElement(At,null),label:"Arrêts",permissions:w.stops.permissions,children:[{key:"3-1",label:a.createElement(T,{to:"/arrets",permissions:w.stops.permissions},"Arrêts (Classique)"),permissions:w.stops.permissions},{key:"3-2",label:a.createElement(T,{to:"/arrets-dashboard",permissions:w.stops.permissions},"Tableau de Bord Modulaire"),permissions:w.stops.permissions}]},{type:"divider"},{key:"group-1",type:"group",label:"Analyses",children:[{key:"4",icon:a.createElement(Lt,null),label:a.createElement(T,{to:"/analytics",permissions:w.analytics.permissions},"Analyses"),permissions:w.analytics.permissions},{key:"5",icon:a.createElement(Ht,null),label:a.createElement(T,{to:"/reports",permissions:w.reports.permissions},"Rapports"),permissions:w.reports.permissions}]},{type:"divider"},{key:"group-2",type:"group",label:"Configuration",children:[{key:"7",icon:a.createElement(_t,null),label:a.createElement(T,{to:"/maintenance",permissions:w.maintenance.permissions},"Maintenance"),permissions:w.maintenance.permissions},{key:"notifications",icon:a.createElement(Bt,null),label:a.createElement(T,{to:"/notifications",permissions:w.notifications.permissions},"Notifications"),permissions:w.notifications.permissions}]},{key:"admin",icon:a.createElement(Ae,null),label:"Administration",roles:w.admin.roles,children:[{key:"/admin/users",icon:a.createElement($t,null),label:a.createElement(T,{to:"/admin/users",permissions:["manage_users"],roles:["admin"]},"Gestion des utilisateurs"),permissions:["manage_users"],roles:["admin"]}]},{key:"/profile",icon:a.createElement(ve,null),label:a.createElement(ue,{to:"/profile"},"Mon profil")},{key:"/settings",icon:a.createElement(Ae,null),label:a.createElement(ue,{to:"/settings"},"Paramètres")},{key:"/permission-test",icon:a.createElement(Rt,null),label:a.createElement(ue,{to:"/permission-test"},"Test des permissions")}].filter(s=>s.type==="divider"||s.type==="group"?s.type==="group"&&s.children?(s.children=s.children.filter(I=>P(I)),s.children.length>0):!0:P(s)),L={items:[{key:"1",label:"Mon profil",icon:a.createElement(ve,null)},{type:"divider"},{key:"3",label:"Aide",icon:a.createElement(be,null)},{key:"4",label:"Déconnexion",icon:a.createElement(un,null),danger:!0}],onClick:O},z={borderRight:0,padding:i?"8px 0":"16px 0",fontSize:i?"14px":"15px"},B=()=>{const s=c.pathname;return s.includes("/home")?"Tableau de Bord":s.includes("/production")?"Production":s==="/arrets-dashboard"?"Tableau de Bord des Arrêts (Modulaire)":s.includes("/arrets")?"Gestion des Arrêts":s.includes("/analytics")?"Analyses":s.includes("/reports")?"Rapports":s.includes("/maintenance")?"Maintenance":s.includes("/admin/users")?"Gestion des Utilisateurs":s.includes("/profile")?"Mon Profil":"Tableau de Bord"};return a.createElement(Ee,{style:{minHeight:"100vh"}},i&&a.createElement(tt,{placement:"left",closable:!1,onClose:()=>b(!0),open:!u,bodyStyle:{padding:0},width:k,style:{zIndex:S("SIDEBAR"),position:"fixed"}},a.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:16,borderBottom:`1px solid ${n?"#303030":"#f0f0f0"}`,height:"120px"}},a.createElement("div",{style:{display:"flex",alignItems:"center",width:"100%",justifyContent:"center"}},a.createElement(_e,{src:n?Be:Te,alt:"SOMIPEM Logo",preview:!1,style:{height:100,maxWidth:"90%",objectFit:"contain"}})),a.createElement(_,{icon:a.createElement(Tt,null),onClick:()=>b(!0),type:"text",style:{position:"absolute",right:10,top:10}})),a.createElement(Le,{theme:n?"dark":"light",mode:"inline",items:A,style:{padding:"8px 0"},defaultSelectedKeys:[$()],selectedKeys:[$()]}),a.createElement(He,{style:{margin:"8px 0"}}),a.createElement("div",{style:{padding:"0 16px 16px"}},a.createElement(de,{direction:"vertical",style:{width:"100%"}},a.createElement(_,{icon:a.createElement(Fe,null),block:!0},"Changer de langue"),a.createElement(_,{icon:a.createElement(be,null),block:!0},"Aide et support"),a.createElement(_,{icon:n?a.createElement(ye,null):a.createElement(he,null),block:!0,onClick:l},n?"Mode clair":"Mode sombre")))),!i&&a.createElement(Cn,{collapsible:!0,collapsed:u,trigger:null,breakpoint:"lg",theme:n?"dark":"light",onBreakpoint:s=>{j(s),s&&!g.md&&b(!0)},width:k,collapsedWidth:R,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0,zIndex:S("SIDEBAR"),boxShadow:n?"2px 0 8px rgba(0,0,0,0.2)":"2px 0 8px rgba(0,0,0,0.06)"}},a.createElement("div",{className:"logo",style:{padding:u?"16px 8px":"24px 16px",transition:"all 0.3s",borderBottom:`1px solid ${n?"#303030":"#f0f0f0"}`,display:"flex",alignItems:"center",justifyContent:"center",height:u?"120px":"180px"}},a.createElement(_e,{src:n?Be:Te,alt:"SOMIPEM Logo",preview:!1,style:{height:u?100:160,maxWidth:"100%",objectFit:"contain",transition:"all 0.3s"}})),a.createElement(Le,{theme:n?"dark":"light",mode:"inline",defaultSelectedKeys:[$()],selectedKeys:[$()],items:A,inlineCollapsed:u,style:z}),!u&&a.createElement(a.Fragment,null,a.createElement(He,{style:{margin:"8px 0"}}),a.createElement("div",{style:{padding:"0 16px 16px"}},a.createElement(de,{direction:"vertical",style:{width:"100%"}},a.createElement(_,{icon:a.createElement(Fe,null),block:!0},"Changer de langue"),a.createElement(_,{icon:a.createElement(be,null),block:!0},"Aide et support"),a.createElement(_,{icon:n?a.createElement(ye,null):a.createElement(he,null),block:!0,onClick:l},n?"Mode clair":"Mode sombre"))))),a.createElement(Ee,{style:{marginLeft:i?0:u?R:k,transition:"margin 0.2s, padding 0.2s"}},a.createElement(xn,{style:{padding:"0 24px",background:n?"#1f1f1f":"#fff",position:"sticky",top:0,zIndex:S("HEADER"),boxShadow:n?"0 2px 8px rgba(0,0,0,0.2)":"0 2px 8px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"space-between",height:64}},a.createElement("div",{style:{display:"flex",alignItems:"center"}},a.createElement(_,{icon:u?a.createElement(vn,null):a.createElement(pn,null),onClick:()=>b(!u),type:"text",style:{fontSize:16,width:48,height:48,display:"flex",alignItems:"center",justifyContent:"center"}}),!i&&a.createElement(On,{level:4,style:{margin:0,marginLeft:16}},B())),a.createElement(de,{size:16},a.createElement(Wt,{className:"header-date",value:e,valueStyle:{fontSize:i?12:14,fontWeight:500,color:n?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"},prefix:a.createElement(Kt,{style:{marginRight:8}})}),a.createElement(de,{size:16},a.createElement(Mt,{title:n?"Passer en mode clair":"Passer en mode sombre"},a.createElement(_,{type:"text",icon:n?a.createElement(ye,null):a.createElement(he,null),onClick:l,style:{width:40,height:40,display:"flex",alignItems:"center",justifyContent:"center"}})),a.createElement(zt,null),a.createElement(Nt,{menu:L,trigger:["click"],placement:"bottomRight"},a.createElement(_,{type:"text",style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"0 8px"}},a.createElement(de,null,a.createElement(Vt,{icon:a.createElement(ve,null),style:{backgroundColor:"#1890ff"}}),!i&&a.createElement(Ue,null,(m==null?void 0:m.username)||"Utilisateur"))))))),a.createElement(En,{style:{margin:i?"16px 8px":"24px 16px",padding:i?16:24,minHeight:280,background:n?"#141414":"#fff",borderRadius:8,position:"relative",boxShadow:n?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"}},i&&!u&&a.createElement("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0,0,0,0.3)",zIndex:S("MOBILE_OVERLAY"),cursor:"pointer"},onClick:()=>b(!0)}),a.createElement(jt,null)),a.createElement(wn,{style:{textAlign:"center",padding:i?"12px 8px":"16px 24px",background:"transparent"}},a.createElement(Ue,{type:"secondary"},"Performance 4.0 ©",new Date().getFullYear()," Caps and Preforms"))))};export{Xn as default};
