/**
 * Background Data Synchronization Service
 * Handles data sync between Elasticsearch and MySQL
 * Implements conflict resolution and performance monitoring
 */

import { executeQuery } from '../utils/dbUtils.js';
import elasticsearchProductionService from './ElasticsearchProductionService.js';
import redisClient from './redisService.js';

class DataSyncService {
  constructor() {
    this.isRunning = false;
    this.syncInterval = null;
    this.lastSyncTime = null;
    this.syncStats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      conflictsResolved: 0,
      recordsSynced: 0
    };
  }

  // Start background synchronization
  async startSync(intervalMinutes = 15) {
    if (this.isRunning) {
      console.log('🔄 Data sync already running');
      return;
    }

    this.isRunning = true;
    console.log(`🔄 Starting data synchronization service (interval: ${intervalMinutes}m)`);
    
    // Initial sync
    await this.performSync();
    
    // Schedule periodic syncs
    this.syncInterval = setInterval(async () => {
      await this.performSync();
    }, intervalMinutes * 60 * 1000);
  }

  // Stop synchronization
  stopSync() {
    if (!this.isRunning) return;

    this.isRunning = false;
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    console.log('🔄 Data synchronization service stopped');
  }

  // Perform a single sync operation
  async performSync() {
    if (!this.isRunning) return;

    const startTime = Date.now();
    console.log('🔄 Starting data synchronization...');
    
    try {
      this.syncStats.totalSyncs++;
      
      // Check if Elasticsearch is available
      const isElasticsearchAvailable = await this.testElasticsearchConnection();
      
      if (!isElasticsearchAvailable) {
        console.log('⚠️ Elasticsearch unavailable, skipping sync');
        return;
      }

      // Sync production data
      const productionResults = await this.syncProductionData();
      
      // Sync machine performance data
      const performanceResults = await this.syncMachinePerformanceData();
      
      // Update statistics
      this.syncStats.successfulSyncs++;
      this.syncStats.recordsSynced += productionResults.synced + performanceResults.synced;
      this.syncStats.conflictsResolved += productionResults.conflicts + performanceResults.conflicts;
      
      this.lastSyncTime = new Date().toISOString();
      
      // Cache sync results
      await this.cacheSyncStats();
      
      const duration = Date.now() - startTime;
      console.log(`✅ Data sync completed in ${duration}ms`, {
        production: productionResults,
        performance: performanceResults,
        totalRecords: productionResults.synced + performanceResults.synced
      });
      
    } catch (error) {
      this.syncStats.failedSyncs++;
      console.error('❌ Data sync failed:', error);
      
      // Cache error state
      await redisClient.setex('data_sync_error', 300, JSON.stringify({
        error: error.message,
        timestamp: new Date().toISOString()
      }));
    }
  }

  // Test Elasticsearch connection
  async testElasticsearchConnection() {
    try {
      await elasticsearchProductionService.ping();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Sync production chart data
  async syncProductionData() {
    const results = { synced: 0, conflicts: 0, errors: 0 };
    
    try {
      // Get recent MySQL data (last 7 days)
      const mysqlQuery = `
        SELECT 
          Date_Insert_Day,
          SUM(Good_QTY_Day) as Total_Good_Qty_Day,
          SUM(Rejects_QTY_Day) as Total_Rejects_Qty_Day,
          AVG(OEE_Day) as OEE_Day,
          AVG(Speed_Day) as Speed_Day,
          AVG(Availability_Rate_Day) as Availability_Rate_Day,
          AVG(Performance_Rate_Day) as Performance_Rate_Day,
          AVG(Quality_Rate_Day) as Quality_Rate_Day
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY Date_Insert_Day
        ORDER BY Date_Insert_Day DESC
      `;
      
      const { success, data: mysqlData } = await executeQuery(mysqlQuery);
      
      if (!success || !mysqlData?.length) {
        console.log('⚠️ No MySQL production data to sync');
        return results;
      }

      // Get corresponding Elasticsearch data
      const esData = await elasticsearchProductionService.getProductionChart({
        dateRangeType: 'day',
        startDate: mysqlData[mysqlData.length - 1].Date_Insert_Day,
        endDate: mysqlData[0].Date_Insert_Day
      });

      // Compare and sync differences
      for (const mysqlRecord of mysqlData) {
        const esRecord = esData.find(es => es.Date_Insert_Day === mysqlRecord.Date_Insert_Day);
        
        if (!esRecord) {
          // Record missing in Elasticsearch, add it
          await this.addToElasticsearch('production', mysqlRecord);
          results.synced++;
        } else if (this.hasDataConflict(mysqlRecord, esRecord)) {
          // Resolve conflict (MySQL wins as source of truth)
          await this.resolveProductionConflict(mysqlRecord, esRecord);
          results.conflicts++;
        }
      }
      
    } catch (error) {
      console.error('Error syncing production data:', error);
      results.errors++;
    }
    
    return results;
  }

  // Sync machine performance data
  async syncMachinePerformanceData() {
    const results = { synced: 0, conflicts: 0, errors: 0 };
    
    try {
      // Get recent MySQL machine performance data
      const mysqlQuery = `
        SELECT 
          Machine_Name,
          Shift,
          Good_QTY_Day as production,
          Rejects_QTY_Day as rejects,
          COALESCE(Availability_Rate_Day * 100, 0) as availability,
          COALESCE(Performance_Rate_Day * 100, 0) as performance,
          COALESCE(OEE_Day * 100, 0) as oee,
          COALESCE(Quality_Rate_Day * 100, 0) as quality,
          COALESCE(Down_Hours_Day, 0) as downtime,
          Date_Insert_Day
        FROM machine_daily_table_mould 
        WHERE Date_Insert_Day >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        ORDER BY Date_Insert_Day DESC, Machine_Name
      `;
      
      const { success, data: mysqlData } = await executeQuery(mysqlQuery);
      
      if (!success || !mysqlData?.length) {
        console.log('⚠️ No MySQL machine performance data to sync');
        return results;
      }

      // Process data by machine and date
      const groupedData = this.groupByMachineAndDate(mysqlData);
      
      for (const [key, records] of Object.entries(groupedData)) {
        const [machine, date] = key.split('_');
        
        // Check if exists in Elasticsearch
        const esRecord = await elasticsearchProductionService.getMachinePerformance({
          machine,
          date
        });

        if (!esRecord || esRecord.length === 0) {
          // Add to Elasticsearch
          await this.addToElasticsearch('machine_performance', records[0]);
          results.synced++;
        } else if (this.hasPerformanceConflict(records[0], esRecord[0])) {
          // Resolve conflict
          await this.resolvePerformanceConflict(records[0], esRecord[0]);
          results.conflicts++;
        }
      }
      
    } catch (error) {
      console.error('Error syncing machine performance data:', error);
      results.errors++;
    }
    
    return results;
  }

  // Group machine data by machine name and date
  groupByMachineAndDate(data) {
    return data.reduce((groups, record) => {
      const key = `${record.Machine_Name}_${record.Date_Insert_Day}`;
      if (!groups[key]) groups[key] = [];
      groups[key].push(record);
      return groups;
    }, {});
  }

  // Check for data conflicts
  hasDataConflict(mysqlRecord, esRecord) {
    const threshold = 0.01; // 1% tolerance
    
    const fields = ['Total_Good_Qty_Day', 'Total_Rejects_Qty_Day', 'OEE_Day'];
    
    return fields.some(field => {
      const mysqlVal = parseFloat(mysqlRecord[field]) || 0;
      const esVal = parseFloat(esRecord[field]) || 0;
      const diff = Math.abs(mysqlVal - esVal);
      return diff > (Math.max(mysqlVal, esVal) * threshold);
    });
  }

  // Check for performance data conflicts
  hasPerformanceConflict(mysqlRecord, esRecord) {
    const threshold = 0.05; // 5% tolerance for performance metrics
    
    const fields = ['production', 'oee', 'availability'];
    
    return fields.some(field => {
      const mysqlVal = parseFloat(mysqlRecord[field]) || 0;
      const esVal = parseFloat(esRecord[field]) || 0;
      const diff = Math.abs(mysqlVal - esVal);
      return diff > (Math.max(mysqlVal, esVal) * threshold);
    });
  }

  // Add record to Elasticsearch
  async addToElasticsearch(type, record) {
    try {
      if (type === 'production') {
        await elasticsearchProductionService.indexProductionData(record);
      } else if (type === 'machine_performance') {
        await elasticsearchProductionService.indexMachinePerformance(record);
      }
      console.log(`✅ Added ${type} record to Elasticsearch:`, record.Date_Insert_Day || record.Machine_Name);
    } catch (error) {
      console.error(`❌ Failed to add ${type} record to Elasticsearch:`, error);
    }
  }

  // Resolve production data conflict (MySQL wins)
  async resolveProductionConflict(mysqlRecord, esRecord) {
    try {
      await elasticsearchProductionService.updateProductionData(mysqlRecord);
      console.log(`🔧 Resolved production conflict for ${mysqlRecord.Date_Insert_Day}`);
    } catch (error) {
      console.error('❌ Failed to resolve production conflict:', error);
    }
  }

  // Resolve performance data conflict (MySQL wins)
  async resolvePerformanceConflict(mysqlRecord, esRecord) {
    try {
      await elasticsearchProductionService.updateMachinePerformance(mysqlRecord);
      console.log(`🔧 Resolved performance conflict for ${mysqlRecord.Machine_Name}`);
    } catch (error) {
      console.error('❌ Failed to resolve performance conflict:', error);
    }
  }

  // Cache synchronization statistics
  async cacheSyncStats() {
    try {
      const stats = {
        ...this.syncStats,
        lastSync: this.lastSyncTime,
        isRunning: this.isRunning
      };
      
      await redisClient.setex('data_sync_stats', 3600, JSON.stringify(stats));
    } catch (error) {
      console.error('Error caching sync stats:', error);
    }
  }

  // Get sync statistics
  async getSyncStats() {
    try {
      const cached = await redisClient.get('data_sync_stats');
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('Error getting sync stats:', error);
    }
    
    return {
      ...this.syncStats,
      lastSync: this.lastSyncTime,
      isRunning: this.isRunning
    };
  }

  // Manual sync trigger
  async triggerManualSync() {
    if (!this.isRunning) {
      console.log('🔄 Triggering manual sync...');
      await this.performSync();
      return this.getSyncStats();
    } else {
      console.log('⚠️ Sync already in progress');
      return null;
    }
  }
}

// Create singleton instance
const dataSyncService = new DataSyncService();

export default dataSyncService;
