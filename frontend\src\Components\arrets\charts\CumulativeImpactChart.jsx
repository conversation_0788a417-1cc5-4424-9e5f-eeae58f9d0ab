import React, { memo } from 'react';
import { Responsive<PERSON><PERSON><PERSON>, Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ReferenceLine } from 'recharts';
import { Empty, Spin } from 'antd';
import { useUnifiedChartConfig } from '../../../hooks/useUnifiedChartConfig';

const CumulativeImpactChart = memo(({ data = [], loading = false }) => {
  // Get unified chart configuration
  const chartConfig = useUnifiedChartConfig({
    chartType: 'line'
  });

  console.log('🔧 CumulativeImpactChart received data:', {
    dataType: typeof data,
    isArray: Array.isArray(data),
    dataLength: data?.length || 0,
    sampleData: data?.slice(0, 3) || []
  });

  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée d'impact cumulé disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  // Process data to focus on cumulative impact
  const sortedData = safeData
    .map(item => ({
      reason: item.reason || item.Code_Stop || item.stopName || 'N/A',
      value: parseFloat(item.value || item.duration || item.count || 0),
    }))
    .sort((a, b) => b.value - a.value);

  // Calculate cumulative percentage
  const totalValue = sortedData.reduce((sum, item) => sum + item.value, 0);
  let cumulativeSum = 0;
  
  const cumulativeData = sortedData.map((item, index) => {
    cumulativeSum += item.value;
    const cumulativePercentage = (cumulativeSum / totalValue) * 100;
    
    return {
      index: index + 1,
      reason: item.reason.length > 12 ? item.reason.substring(0, 10) + '...' : item.reason,
      fullReason: item.reason,
      value: item.value,
      cumulativePercentage: cumulativePercentage,
    };
  }).slice(0, 10); // Show only top 10 for better visualization
  return (
    <ResponsiveContainer {...chartConfig.responsiveContainerProps}>
      <LineChart data={cumulativeData} margin={chartConfig.margins}>
        <CartesianGrid {...chartConfig.gridConfig} />
        <XAxis
          dataKey="reason"
          {...chartConfig.axisConfig}
          angle={-25}
          textAnchor="end"
          height={50}
          interval={0}
        />
        <YAxis
          {...chartConfig.axisConfig}
          width={25}
          domain={[0, 100]}
          tickCount={5}
          tickFormatter={(value) => `${value}%`}
        />
        
        {/* Reference lines for 80-20 rule */}        <ReferenceLine 
          y={80} 
          stroke="#f5222d" 
          strokeDasharray="5 3" 
          strokeWidth={1.5}
          label={{ value: "80%", position: "right", style: { fill: "#f5222d", fontSize: "10px", fontWeight: 'bold' } }}
        />
        <Tooltip {...chartConfig.tooltipConfig} />
        {chartConfig.displayConfig.showLegend && <Legend {...chartConfig.legendConfig} />}
        <Line
          type="monotone"
          dataKey="cumulativePercentage"
          {...chartConfig.getLineElementConfig(chartConfig.getPrimaryColor())}
          name="Impact Cumulé"
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

CumulativeImpactChart.displayName = 'CumulativeImpactChart';

export default CumulativeImpactChart;
