import { executeQuery } from '../utils/dbUtils.js';

/**
 * Create the settings table and populate it with default data
 */
async function createSettingsTable() {
  try {
    console.log('🚀 Creating settings table...');
    
    // Step 1: Drop existing tables for clean slate
    console.log('🧹 Cleaning up existing settings tables...');
    
    const dropTables = [
      'DROP TABLE IF EXISTS user_notification_preferences',
      'DROP TABLE IF EXISTS user_report_subscriptions', 
      'DROP TABLE IF EXISTS system_settings',
      'DROP TABLE IF EXISTS user_settings'
    ];
    
    for (const dropSQL of dropTables) {
      const result = await executeQuery(dropSQL);
      if (result.success) {
        console.log(`✅ ${dropSQL.split(' ')[4]} dropped successfully`);
      } else {
        console.log(`⚠️  ${dropSQL.split(' ')[4]} drop failed (might not exist):`, result.error);
      }
    }
    
    // Step 2: Create new user_settings table
    console.log('🏗️  Creating new user_settings table...');
    
    const createTableSQL = `
      CREATE TABLE user_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        settings_json JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_settings (user_id),
        INDEX idx_user_settings_user_id (user_id),
        INDEX idx_user_settings_updated (updated_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    const createResult = await executeQuery(createTableSQL);
    if (createResult.success) {
      console.log('✅ user_settings table created successfully');
    } else {
      throw new Error('Failed to create user_settings table: ' + createResult.error);
    }
    
    // Step 3: Get existing users
    console.log('👥 Getting existing users...');
    const usersResult = await executeQuery('SELECT id FROM users');
    
    if (!usersResult.success) {
      throw new Error('Failed to get users: ' + usersResult.error);
    }
    
    console.log(`📊 Found ${usersResult.data.length} users`);
    
    // Step 4: Insert default settings for each user
    if (usersResult.data.length > 0) {
      console.log('⚙️  Creating default settings for users...');
      
      const defaultSettings = {
        theme: {
          darkMode: false,
          compactMode: false,
          animationsEnabled: true,
          chartAnimations: true
        },
        tables: {
          defaultPageSize: 20,
          pageSizeOptions: [10, 20, 50, 100],
          virtualizationThreshold: 100,
          showQuickJumper: true
        },
        charts: {
          animationsEnabled: true,
          defaultType: 'bar',
          showLegend: true,
          colorScheme: 'somipem',
          performanceMode: false
        },
        refresh: {
          dashboardInterval: 300,
          realtimeInterval: 60,
          autoRefreshEnabled: true,
          backgroundRefresh: true
        },
        notifications: {
          categories: {
            machine_alert: true,
            production: true,
            quality: true,
            maintenance: true,
            alert: true,
            info: true,
            updates: true
          },
          priorities: {
            critical: true,
            high: true,
            medium: true,
            low: true
          },
          delivery: {
            sse: true,
            email: false,
            browser: true
          },
          behavior: {
            sound: true,
            autoClose: false,
            autoCloseDelay: 5000,
            maxVisible: 5
          }
        },
        email: {
          enabled: false,
          frequency: 'immediate',
          template: 'standard',
          notifications: {
            categories: {
              machine_alert: true,
              production: false,
              quality: true,
              maintenance: false,
              alert: true,
              info: false,
              updates: false
            },
            priorities: {
              critical: true,
              high: true,
              medium: false,
              low: false
            }
          },
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '06:00',
            timezone: 'Africa/Tunis'
          },
          batchSettings: {
            hourlyBatch: {
              enabled: false,
              maxNotifications: 10
            },
            dailyDigest: {
              enabled: false,
              time: '08:00',
              includeCharts: true,
              includeTables: false
            }
          }
        },
        reports: {
          generation: {
            autoGenerate: false,
            format: 'pdf',
            quality: 'standard',
            includeCharts: true,
            includeTables: true
          },
          schedules: {
            daily: {
              enabled: false,
              time: '07:00',
              machines: []
            },
            weekly: {
              enabled: false,
              day: 'monday',
              time: '08:00',
              machines: []
            },
            monthly: {
              enabled: false,
              day: 1,
              time: '09:00',
              machines: []
            }
          },
          subscriptions: {
            enabled: false,
            types: ['shift', 'daily', 'weekly'],
            delivery: 'email'
          },
          content: {
            sections: {
              summary: true,
              production: true,
              quality: true,
              maintenance: false,
              charts: true,
              tables: false
            },
            template: 'standard',
            branding: true
          },
          delivery: {
            email: true,
            download: false,
            storage: true,
            retention: 30
          }
        },
        performance: {
          caching: {
            enabled: true,
            duration: 300,
            strategy: 'smart'
          },
          optimization: {
            lazyLoading: true,
            virtualization: true,
            compression: false
          }
        }
      };
      
      // Insert settings for each user
      for (const user of usersResult.data) {
        const insertSQL = 'INSERT INTO user_settings (user_id, settings_json) VALUES (?, ?)';
        const insertResult = await executeQuery(insertSQL, [user.id, JSON.stringify(defaultSettings)]);
        
        if (insertResult.success) {
          console.log(`✅ Default settings created for user ${user.id}`);
        } else {
          console.error(`❌ Failed to create settings for user ${user.id}:`, insertResult.error);
        }
      }
    }
    
    // Step 5: Verify the setup
    console.log('🔍 Verifying setup...');
    const countResult = await executeQuery('SELECT COUNT(*) as count FROM user_settings');
    
    if (countResult.success) {
      console.log(`✅ Settings table created with ${countResult.data[0].count} records`);
      console.log('🎉 Settings system database setup completed successfully!');
    } else {
      throw new Error('Failed to verify setup: ' + countResult.error);
    }
    
  } catch (error) {
    console.error('❌ Settings table creation failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the setup
createSettingsTable();
